package com.adins.exceptions;

import java.util.Collections;

import com.adins.framework.exception.AdInsException;
import com.adins.framework.exception.StatusCodes;

public class EntityNotUniqueException extends AdInsException {
	private static final long serialVersionUID = 1L;
	private static final String ENTITY_CODE = "ENTITY_CODE";	

	public EntityNotUniqueException(String message, Throwable cause, String duplicateCode) {
		super(message, cause, Collections.singletonMap(ENTITY_CODE, duplicateCode));
	}

	public EntityNotUniqueException(String message, String duplicateCode) {
		super(message, Collections.singletonMap(ENTITY_CODE, duplicateCode));
	}

	public EntityNotUniqueException(Throwable cause, String duplicateCode) {
		super(cause, Collections.singletonMap(ENTITY_CODE, duplicateCode));
	}

	@Override
	public int getErrorCode() {
		return StatusCodes.ENTITY_NOT_UNIQUE;
	}

}
