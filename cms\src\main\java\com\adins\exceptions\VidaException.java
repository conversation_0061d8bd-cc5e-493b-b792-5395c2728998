package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class VidaException extends AdInsException {
	private static final long serialVersionUID = 1L;
	
	public VidaException(String message) {
		super(message);
	}
	
	public VidaException(String message, Throwable ex) {
		super(message, ex);
	}

	@Override
	public int getErrorCode() {
		return StatusCode.VIDA_ERROR;
	}

}
