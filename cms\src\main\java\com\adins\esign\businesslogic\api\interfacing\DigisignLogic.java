package com.adins.esign.businesslogic.api.interfacing;

import java.io.IOException;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.custom.ActivationDigisignResponseBean;
import com.adins.esign.model.custom.BalanceDigiResponseBean;
import com.adins.esign.model.custom.BulkSignDocumentDigisignResponseBean;
import com.adins.esign.model.custom.ChangeEmailPhoneDigiRequest;
import com.adins.esign.model.custom.ChangeEmailPhoneDigiResponse;
import com.adins.esign.model.custom.CheckDigiCertExpDateResponse;
import com.adins.esign.model.custom.RegisterDigisignResponseBean;
import com.adins.esign.model.custom.RegisterDigisignResponseDataBean;
import com.adins.esign.model.custom.RegisterVerificationStatusBean;
import com.adins.esign.model.custom.SendDocumentResponseBean;
import com.adins.esign.model.custom.SignDocumentDigisignResponseBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.webservices.model.BulkSignDocumentRequest;
import com.adins.esign.webservices.model.DocumentConfinsRequestBean;
import com.adins.esign.webservices.model.InsertDocumentManualSignRequest;
import com.adins.esign.webservices.model.RegisterResponse;
import com.adins.esign.webservices.model.RegistrationRequest;
import com.adins.esign.webservices.model.SignDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.digisign.DigisignRegisterRequest;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.exceptions.DigisignException;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface DigisignLogic {
	
	// Register related
	RegisterDigisignResponseBean sendDataRegister(RegistrationRequest regisRequest, AuditContext audit) throws IOException;
	RegisterDigisignResponseBean sendDataRegisterV2(DigisignRegisterRequest regisRequest, AuditContext audit);
	RegisterDigisignResponseBean registerForExternalRequest(RegisterExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit);
	String buildRegisterVerifMessage(RegisterDigisignResponseDataBean bean);
	RegisterResponse buildRegisterResponse(RegisterDigisignResponseBean response, DigisignRegisterRequest request, AuditContext audit);
	AmMsuser insertRegisteredUserExternal(RegisterExternalRequest request, boolean isActivated, MsTenant tenant, MsVendor vendor, AuditContext audit);
	RegisterVerificationStatusBean buildVerificationStatusBean(RegisterDigisignResponseBean registerResponse, AuditContext audit);
	boolean cutVerifAndTextVerif(RegisterDigisignResponseBean registerResponse, AuditContext audit);
	String getRegisterErrorMessage(RegisterDigisignResponseBean registerResponse, AuditContext audit);
	
	// Activation related
	ActivationDigisignResponseBean getActivationLink(UserBean userBean, AuditContext audit);
	
	// Send document related
	SendDocumentResponseBean sendDocumentDigisign(DocumentConfinsRequestBean docRequest, InsertDocumentManualSignRequest manualSignReq, String documentId, MsTenant tenant, MsVendor vendor, AuditContext audit) throws IOException;
	
	// Sign document related
	BulkSignDocumentDigisignResponseBean bulkSignDocument(BulkSignDocumentRequest request, AuditContext audit) throws IOException;
	SignDocumentDigisignResponseBean signDocument(SignDocumentRequest signDocReq, TrDocumentD docD, AuditContext audit) throws IOException;
	
	// Download document related
	ViewDocumentResponse getDocumentFile(TrDocumentD document, AuditContext audit);
	ViewDocumentResponse getDocumentFileDigisign(TrDocumentD document, String token, AuditContext audit);
	
	// Others
	public String decryptMessage(String msg, String tenantCode) throws DigisignException;	
	BalanceDigiResponseBean getBalanceDigi(MsTenant tenant, MsVendor vendor, AuditContext audit) throws IOException;
	CheckDigiCertExpDateResponse checkCertExpDate(String emailUser, MsVendoroftenant vot, AuditContext audit) throws IOException;
	ChangeEmailPhoneDigiResponse changeEmailPhoneDigi(ChangeEmailPhoneDigiRequest request, MsTenant tenant, AuditContext audit) throws IOException;
	
}
