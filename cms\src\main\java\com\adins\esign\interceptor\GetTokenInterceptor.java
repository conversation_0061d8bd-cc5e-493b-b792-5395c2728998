package com.adins.esign.interceptor;

import java.util.Base64;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.apache.cxf.common.logging.LogUtils;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.AbstractPhaseInterceptor;
import org.apache.cxf.phase.Phase;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.boot.autoconfigure.cache.CacheProperties.Redis;

//import com.adins.esign.businesslogic.api.RedisConnectionLogic;
//import com.adins.esign.businesslogic.impl.GenericRedisConnectionLogic;


public class GetTokenInterceptor extends AbstractPhaseInterceptor<Message>{
	
//	int TOKEN_EXPIRE_SECONDS = 60 * 15;
//
//	RedisConnectionLogic redisConnLogic = new GenericRedisConnectionLogic();
//	
	private static final Logger LOG = LogUtils.getLogger(GetTokenInterceptor.class);
	
	public GetTokenInterceptor() {
		super(Phase.RECEIVE);
	}
	@SuppressWarnings("static-access")
	@Override
	public void handleMessage(Message message) throws Fault {
//		String pathInfo = (String) message.get(message.PATH_INFO);
//		if(!pathInfo.contains("login")) {
//			String token = validating(message);
//			if(null == token) {
//				LOG.log(Level.SEVERE, "Token Invalid");
//			}else {
//				LOG.log(Level.INFO, "Token Valid");
//			}
//		}
	}
//	
//	private String validating(Message message) {
//		HttpServletRequest request = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
//		String header = request.getHeaderNames().toString();
//		
//		String token = null;
//		
//		if(header.isEmpty() || null == header) {
//			LOG.log(Level.SEVERE, "No Request.");
//			return null;
//		}
//		
//		token = request.getHeader("token");
//		
//		if(token == null || token.isEmpty()) {
//			LOG.log(Level.SEVERE, "No Token Available.");
//			return null;
//		}
//		
//		if(!isBase64String(token)) {
//			LOG.log(Level.SEVERE, "Invalid Token Format.");
//			return null;
//		}
//		
//		byte[] encodedToken = Base64.getDecoder().decode(token);
//		String decodedToken = new String(encodedToken);
//		
//		if(!redisConnLogic.exists(decodedToken)) {
//			LOG.log(Level.SEVERE, "Invalid Token.");
//			return null;
//		}
//		
//		String sortedSetTokenKey = redisConnLogic.get(decodedToken);
//		
//		if(sortedSetTokenKey.isEmpty() || null == sortedSetTokenKey) {
//			LOG.log(Level.SEVERE, "Invalid Token.");
//			return null;
//		}else {
//			String[] data = sortedSetTokenKey.split("|", 2);
//		}
//		
//		redisConnLogic.expire(decodedToken, TOKEN_EXPIRE_SECONDS);
//		
//		return token;
//	}
//
//	
//	private Boolean isBase64String(String token) {
//		token = token.trim();
//		return (token.length() % 4 == 0) && Pattern.matches("^[a-zA-Z0-9\\+/]*={0,3}$", token);
//	}

}
