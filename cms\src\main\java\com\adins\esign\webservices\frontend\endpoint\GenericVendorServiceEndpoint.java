package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.model.custom.UpdatePSrERequest;
import com.adins.esign.webservices.frontend.api.VendorService;
import com.adins.esign.webservices.model.UpdatePsrePriorityRequest;
import com.adins.esign.webservices.model.UpdatePsrePriorityResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/vendor")
@Api(value = "VendorService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericVendorServiceEndpoint implements VendorService{
	@Autowired VendorLogic vendorLogic;
	
	@Override
	@POST
	@Path("/s/updateVendorPSrE")
	public MssResponseType updateVendorPSrE(UpdatePSrERequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return vendorLogic.updateVendorPSrE(request, audit);
	}


	@Override
	@POST
	@Path("/s/updatePriorityPSrE")
	public UpdatePsrePriorityResponse updatePSrEPriority(UpdatePsrePriorityRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return vendorLogic.updatePSrEPriority(request, audit);
	}


	
}
