package com.adins.esign.dataaccess.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.PaymentSignTypeDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsPaymentsigntypeoftenant;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;

@Transactional
@Component
public class PaymentSignTypeDaoHbn extends BaseDaoHbn implements PaymentSignTypeDao{

	@Override
	public List<Map<String, Object>> getListPaymentSignTypeByTenantCode(String tenantCode) {
		StringBuilder query = new StringBuilder();
		Object[][] param = new Object[][] {{ MsTenant.TENANT_CODE_HBM, tenantCode }};
		
		query
			.append("select distinct(lov.code), lov.description ")
			.append("from ms_paymentsigntypeoftenant pst ")
			.append("join ms_lov lov on pst.lov_payment_sign_type = lov.id_lov ")
			.append("join ms_tenant mt on pst.id_ms_tenant = mt.id_ms_tenant ")
			.append("where mt.tenant_code = :tenantCode");
		
		return this.managerDAO.selectAllNativeString(query.toString(), param);
	}

	@Override
	public List<Map<String, Object>> getListPaymentSignTypeByTenantCodeAndVendorCode(String tenantCode, String vendorCode) {
		StringBuilder query = new StringBuilder();
		Object[][] param = new Object[][] {{ MsTenant.TENANT_CODE_HBM, tenantCode }, { MsVendor.VENDOR_CODE_HBM , vendorCode }};
		
		query
			.append("select lov.code, lov.description ")
			.append("from ms_paymentsigntypeoftenant pst ")
			.append("join ms_lov lov on pst.lov_payment_sign_type = lov.id_lov ")
			.append("join ms_tenant mt on pst.id_ms_tenant = mt.id_ms_tenant ")
			.append("join ms_vendor mv on pst.id_ms_vendor = mv.id_ms_vendor ")
			.append("where mt.tenant_code = :tenantCode ")
		    .append("and mv.vendor_code = :vendorCode ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), param);
	}

	@Override
	public MsPaymentsigntypeoftenant getPaymentSignTypeByPaymentSignTypeVendorCodeAndTenantCode(String lovCode,
			String vendorCode, String tenantCode) {
		StringBuilder query = new StringBuilder();
		Object[][] param = new Object[][] {{ MsTenant.TENANT_CODE_HBM, tenantCode }, {MsVendor.VENDOR_CODE_HBM, vendorCode}, {MsLov.CODE_HBM, lovCode}};
		query.append("from MsPaymentsigntypeoftenant pst ")
			 .append("join fetch pst.msVendor v ")
			 .append("join fetch pst.msTenant t ")
			 .append("join fetch pst.msLov l ")
			 .append("where v.vendorCode = :vendorCode and t.tenantCode = :tenantCode and l.code = :code ");
		
		return (MsPaymentsigntypeoftenant) this.managerDAO.selectOne(query.toString(), param);
	}
}