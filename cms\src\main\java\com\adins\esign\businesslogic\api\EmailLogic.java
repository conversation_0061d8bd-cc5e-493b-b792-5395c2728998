package com.adins.esign.businesslogic.api;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

import com.adins.esign.model.MsEmailPattern;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.DeleteEmailParam;
import com.adins.esign.model.custom.ExpungeEmailResultBean;
import com.adins.esign.model.custom.ReadEmailBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.webservices.model.CreateSingleEmailRequest;
import com.adins.esign.webservices.model.CreateSingleEmailResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface EmailLogic {
	String createEmail(SignerBean bean, MsTenant tenant) throws ParseException, IOException;
	CreateSingleEmailResponse createEmail(CreateSingleEmailRequest request, AuditContext audit);
	
	List<ReadEmailBean> readEmail(String emailAddress, String password);
	ExpungeEmailResultBean deleteEmail(String emailAddress, String password, DeleteEmailParam param) throws IOException;
	String parseEmailContent(ReadEmailBean emailBean, MsEmailPattern emailPattern);
}
