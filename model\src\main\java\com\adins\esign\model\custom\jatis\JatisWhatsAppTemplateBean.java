package com.adins.esign.model.custom.jatis;

import java.util.List;

public class JatisWhatsAppTemplateBean {
	private String name;
	private JatisWhatsAppTemplateLanguageBean language;
	private List<JatisWhatsAppTemplateComponentBean> components;
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public JatisWhatsAppTemplateLanguageBean getLanguage() {
		return language;
	}
	public void setLanguage(JatisWhatsAppTemplateLanguageBean language) {
		this.language = language;
	}
	public List<JatisWhatsAppTemplateComponentBean> getComponents() {
		return components;
	}
	public void setComponents(List<JatisWhatsAppTemplateComponentBean> components) {
		this.components = components;
	}
	
}
