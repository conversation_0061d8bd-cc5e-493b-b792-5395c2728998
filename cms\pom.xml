<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.2.13.RELEASE</version>
		<relativePath />
	</parent>
	<artifactId>com.adins.esign.cms</artifactId>
	<version>4.15.1</version>
	<packaging>war</packaging>
	<description>Admin ESIGN and APIs</description>

	<properties>
		<adins-framework.version>2.0.5-SNAPSHOT</adins-framework.version>
		<commons-lang3.version>3.9</commons-lang3.version>
		<commons-io.version>2.8.0</commons-io.version>
		<commons-net.version>3.0.1</commons-net.version>
		<commons-text.version>1.8</commons-text.version>
		<cxf.version>3.3.4</cxf.version>
		<friendly-id.version>1.1.0</friendly-id.version>
		<hamcrest-all.version>1.3</hamcrest-all.version>
		<hashids.version>1.0.3</hashids.version>
		<jsoup.version>1.14.3</jsoup.version>
		<java.version>1.8</java.version>
		<javamelody.version>1.81.0</javamelody.version>
		<jasypt.version>3.0.3.a</jasypt.version>
		<json.version>20190722</json.version>
		<logback.version>1.2.8</logback.version>
		<poi.version>4.1.2</poi.version>
		<poi-ooxml.version>4.1.2</poi-ooxml.version>
		<snakeyaml.version>1.26</snakeyaml.version>
		<spring-framework.version>5.2.19.RELEASE</spring-framework.version>
		<spring-security.version>5.2.10.RELEASE</spring-security.version>
		<stringtemplate.version>4.3</stringtemplate.version>
		<swagger-ui.version>2.2.10-1</swagger-ui.version>
		<tomcat.version>9.0.58</tomcat.version>
		<pojo.version>0.7.6</pojo.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-quartz</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
		</dependency>
		<dependency>
		    <groupId>org.springframework.security.oauth.boot</groupId>
		    <artifactId>spring-security-oauth2-autoconfigure</artifactId>
		     <version>2.3.8.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-core</artifactId>
		</dependency>		
		<dependency>
		    <groupId>commons-net</groupId>
		    <artifactId>commons-net</artifactId>
		    <version>${commons-net.version}</version>
		</dependency>
		<dependency>
			<groupId>org.antlr</groupId>
			<artifactId>ST4</artifactId>
			<version>${stringtemplate.version}</version>
		</dependency>
		<dependency>
	        <groupId>com.zaxxer</groupId>
	        <artifactId>HikariCP</artifactId>
	    </dependency>		
		<dependency>
		    <groupId>org.postgresql</groupId>
		    <artifactId>postgresql</artifactId>
		    <scope>runtime</scope>
		    <version>42.7.1</version>
		</dependency>
		<dependency>
			<groupId>net.bull.javamelody</groupId>
			<artifactId>javamelody-spring-boot-starter</artifactId>
			<version>${javamelody.version}</version>
		</dependency>
        <dependency>
            <!-- jsoup HTML parser library @ https://jsoup.org/ -->
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>
		<dependency>
	        <groupId>org.springframework.boot</groupId>
	        <artifactId>spring-boot-starter-actuator</artifactId>
	    </dependency>
	  	<dependency>
	       <groupId>org.springframework</groupId>
	       <artifactId>spring-jms</artifactId>
	   </dependency>	   
	   <dependency>
		    <groupId>javax.jms</groupId>
		    <artifactId>javax.jms-api</artifactId>
		</dependency>
		<dependency>
		    <groupId>com.fasterxml.jackson.jaxrs</groupId>
		    <artifactId>jackson-jaxrs-json-provider</artifactId>
		</dependency>		
		<dependency>
		    <groupId>org.apache.cxf</groupId>
		    <artifactId>cxf-spring-boot-starter-jaxrs</artifactId>
		    <version>${cxf.version}</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.cxf</groupId>
		    <artifactId>cxf-rt-rs-service-description-swagger</artifactId>
		    <version>${cxf.version}</version>
		</dependency>
		<dependency>
		    <groupId>org.webjars</groupId>
		    <artifactId>swagger-ui</artifactId>
		    <version>${swagger-ui.version}</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.cxf</groupId>
		    <artifactId>cxf-rt-features-logging</artifactId>
		    <version>3.4.0</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.cxf</groupId>
		    <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
		    <version>${cxf.version}</version>
		    <exclusions>
		    	<exclusion>
		    		<groupId>org.ow2.asm</groupId>
		    		<artifactId>asm</artifactId>
		    	</exclusion>
		    </exclusions>
		</dependency>
	  	<dependency>
	  		<groupId>com.adins.framework</groupId>
	  		<artifactId>com.adins.framework.tool.ldap</artifactId>
	  		<version>${adins-framework.version}</version>
	  	</dependency>		
	  	<dependency>
	  		<groupId>com.adins.framework</groupId>
	  		<artifactId>com.adins.framework.tool.password</artifactId>
	  		<version>${adins-framework.version}</version>
	  	</dependency>
	  	<dependency>
  			<groupId>com.adins.framework</groupId>
  			<artifactId>com.adins.framework.tool.properties</artifactId>
  			<version>${adins-framework.version}</version>
  		</dependency>
	  	<dependency>
	  		<groupId>com.adins.framework</groupId>
	  		<artifactId>com.adins.framework.service.base</artifactId>
	  		<version>${adins-framework.version}</version>
	  	</dependency>	
		<dependency>
  			<groupId>com.adins.framework</groupId>
  			<artifactId>com.adins.framework.persistence.dao-hibernate</artifactId>
  			<version>${adins-framework.version}</version>
  		</dependency>
		<dependency>
		    <groupId>org.apache.commons</groupId>
		    <artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
  			<groupId>commons-io</groupId>
  			<artifactId>commons-io</artifactId>
  			<version>${commons-io.version}</version>
  		</dependency>
		<dependency>
		    <groupId>org.apache.poi</groupId>
		    <artifactId>poi</artifactId>
		    <version>${poi.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>${poi-ooxml.version}</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.commons</groupId>
		    <artifactId>commons-text</artifactId>
		    <version>${commons-text.version}</version>
		</dependency>
		<dependency>
		    <groupId>org.json</groupId>
		    <artifactId>json</artifactId>
		    <version>${json.version}</version>
		</dependency>
		<dependency>
		  	<groupId>org.hashids</groupId>
		  	<artifactId>hashids</artifactId>
		  	<version>${hashids.version}</version>
		</dependency>
		<dependency>
		    <groupId>com.devskiller.friendly-id</groupId>
		    <artifactId>friendly-id</artifactId>
		    <version>${friendly-id.version}</version>
		</dependency>
		<dependency>
		    <groupId>com.aliyun.oss</groupId>
		    <artifactId>aliyun-sdk-oss</artifactId>
		    <version>3.10.2</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-fc</artifactId>
			<version>1.4.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/redis.clients/jedis -->
		<dependency>
		    <groupId>redis.clients</groupId>
		    <artifactId>jedis</artifactId>
		</dependency>
		<dependency>
		    <groupId>org.apache.httpcomponents</groupId>
		    <artifactId>httpclient</artifactId>
		</dependency>
<!-- #####TEST##### -->		
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.ow2.asm</groupId>
					<artifactId>asm</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.vaadin.external.google</groupId>
					<artifactId>android-json</artifactId>
				</exclusion>
			</exclusions>
		</dependency>		
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
        	<groupId>org.springframework.boot</groupId>
        	<artifactId>com.adins.esign.model</artifactId>
        	<version>${project.version}</version>
        	<exclusions>
        		<exclusion>
        			<groupId>org.hibernate.javax.persistence</groupId>
        			<artifactId>hibernate-jpa-2.0-api</artifactId>
        		</exclusion>
        	</exclusions>
        </dependency>
        <dependency>
		    <groupId>com.squareup.okhttp3</groupId>
		    <artifactId>okhttp</artifactId>
		</dependency>
		<dependency>
		    <groupId>com.squareup.okhttp3</groupId>
		    <artifactId>mockwebserver</artifactId>
		</dependency>
		
		<!-- logservice 1. Adding the Dependencies in pom.xml -->
		<dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log-logback-appender</artifactId>
            <version>0.1.19</version>
        </dependency>
        <dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>${jasypt.version}</version>
		</dependency>
	</dependencies>
    
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>buildnumber-maven-plugin</artifactId>
				<version>1.4</version>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>create</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<doCheck>false</doCheck>
					<doUpdate>false</doUpdate>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<configuration>
					<archive>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
						</manifest>
						<manifestEntries>
							<Implementation-Build>r${buildNumber}</Implementation-Build>
						</manifestEntries>
					</archive>
					<failOnMissingWebXml>false</failOnMissingWebXml>
				</configuration>
			</plugin>
			<plugin>
			    <groupId>org.jacoco</groupId>
			    <artifactId>jacoco-maven-plugin</artifactId>
			    <version>0.8.5</version>
			    <configuration>
			        <destFile>${sonar.coverage.jacoco.xmlReportPaths}</destFile>
			        <append>true</append>
			    </configuration>
			    <executions>
			        <execution>
			            <id>agent</id>
			            <goals>
			                <goal>prepare-agent</goal>
			            </goals>
			        </execution>
					<execution>
						<id>report</id>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
			    </executions>
			</plugin>
			<plugin>
				<groupId>com.github.ulisesbocchio</groupId>
				<artifactId>jasypt-maven-plugin</artifactId>
				<version>${jasypt.version}</version>
			</plugin>
		</plugins>
		<finalName>${project.artifactId}-${project.version}</finalName>
	</build>

	<scm>
		<connection>scm:svn:https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent/cms</connection>
		<developerConnection>scm:svn:https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent/cms</developerConnection>
		<url>https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent/cms</url>
	</scm>
	
	<dependencyManagement>
		<dependencies>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<version>1.21</version>
		</dependency>
		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>nimbus-jose-jwt</artifactId>
			<version>7.9</version>
		</dependency>
		<dependency>
			<groupId>net.minidev</groupId>
			<artifactId>json-smart</artifactId>
			<version>2.4.7</version>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcpkix-jdk15on</artifactId>
			<version>1.70</version>
		</dependency>
		</dependencies>
	</dependencyManagement>
</project>