package com.adins.esign.dataaccess.impl;

import org.junit.Assert;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.dataaccess.api.CommonDao;

//@Disabled
@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class CommonDaoHbnTest {
	@Autowired CommonDao commonDao;
	
	@Test
	void nextSequenceTrBalanceMutationTrxNoTest() {
		long nextVal = commonDao.nextSequenceTrBalanceMutationTrxNo();
		Assert.assertTrue(nextVal > 0);
	}
}
