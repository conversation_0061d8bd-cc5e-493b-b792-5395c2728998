package com.adins.util;

import java.awt.Color;
import java.awt.Font;
import java.awt.FontFormatException;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.GraphicsEnvironment;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import javax.imageio.ImageIO;

import com.adins.exceptions.RegisterUserException;
import com.adins.exceptions.RegisterUserException.Reason;

public class GenerateSignPicture {
	public byte[] generateSignatureSpecimen(String name) {
		try {
			String processed = getString(name);
			return generatePicture(processed);
		}
		catch (FontFormatException | IOException ex) {
			throw new RegisterUserException(ex, Reason.GEN_SIGN_SPECIMEN_FAILED);
		}
	}
	
	private String getString(String name) {
		String[] nameArr = name.split(" ");
		
		if (nameArr.length > 2) {
			double divider = 2.0;
			int thresh = 8;
			while (nameArr.length > thresh) {
				divider += 1.0;
				thresh += 4;
			}
			
			int wordsPerLine = (int) Math.ceil(nameArr.length/divider);
			
			StringBuilder nameBuilder = new StringBuilder();
			
			for (int i = 0; i < nameArr.length; i++) {
				nameBuilder.append(nameArr[i]);
				if ((i+1) % wordsPerLine == 0) {
					nameBuilder.append("\n");
				} else {
					nameBuilder.append(" ");
				}
			}
			
			return nameBuilder.toString();
		}
		
		return name;
	}
	
	private byte[] generatePicture(String name) throws FontFormatException, IOException {
		BufferedImage image = new BufferedImage(1, 1, BufferedImage.TYPE_4BYTE_ABGR);
		Graphics2D graph2d = image.createGraphics();
		Font f = Font.createFont(Font.TRUETYPE_FONT, getClass().getClassLoader().getResourceAsStream("Thesignature.ttf"));
		
		GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
		ge.registerFont(f);
		
		int size = 65;
		
		Font font = new Font("Thesignature", Font.PLAIN, size);
		graph2d.setFont(font);
		FontMetrics fm = graph2d.getFontMetrics();
		int textWidth = 0;
		
		String[] nameArr = name.split("\n");
		int counter = 0;
		int longestIndex = 0;
		for (String test : nameArr) {
			if (fm.stringWidth(test) > textWidth) {
				textWidth = fm.stringWidth(test);
				longestIndex = counter;
			}
			
			counter++;
		}
		
		int textHeight = fm.getHeight() * nameArr.length;
		while (textWidth > 400 || textHeight > 200) {
			graph2d.dispose();
			size = size - 3;
			font = new Font("Thesignature", Font.PLAIN, size);
			graph2d.setFont(font);
			fm = graph2d.getFontMetrics();
			textWidth = fm.stringWidth(nameArr[longestIndex]);
			textHeight = fm.getHeight() * nameArr.length;
		}
		
		graph2d.setFont(font);
		int width = 400;
		int height = 200;
		graph2d.dispose();
		
		image = new BufferedImage(width, height, BufferedImage.TYPE_4BYTE_ABGR);
		graph2d = image.createGraphics();
        graph2d.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
        graph2d.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
        graph2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        graph2d.setRenderingHint(RenderingHints.KEY_DITHERING, RenderingHints.VALUE_DITHER_ENABLE);
        graph2d.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
        graph2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        graph2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        graph2d.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
        graph2d.setFont(font);
        FontMetrics fMetrics = graph2d.getFontMetrics();
        graph2d.setColor(Color.BLUE);
        int lineHeight = fMetrics.getHeight();
        int x = 0;
        int y = 0;
        if (!name.contains("\n")) {
        	y = (height - fMetrics.getHeight()) / 2 + fMetrics.getAscent();
        	x = (width - fMetrics.stringWidth(name)) / 2;
        	graph2d.drawString(name, x, y);
        } else {
        	int j = 0;
        	for (String write : nameArr) {
        		y = (height - fMetrics.getHeight() * nameArr.length) / 2 + fMetrics.getAscent() + j * lineHeight;
        		x = (width - fMetrics.stringWidth(write)) / 2;
        		graph2d.drawString(write, x, y);
        		j++;
        	}
        }
        graph2d.dispose();
		
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        
		return baos.toByteArray();
	}
}
