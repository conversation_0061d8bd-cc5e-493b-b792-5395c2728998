package com.adins.esign.webservices.frontend.api;


import java.text.ParseException;

import com.adins.esign.webservices.model.CancelJobRequest;
import com.adins.esign.webservices.model.CancelJobResponse;
import com.adins.esign.webservices.model.GetListJobRekonResultRequest;
import com.adins.esign.webservices.model.GetListJobRekonResultResponse;
import com.adins.esign.webservices.model.SubmitRekonJobResultRequest;
import com.adins.esign.webservices.model.SubmitRekonJobResultResponse;
import com.adins.esign.webservices.model.ViewRequestParamJobResultRequest;
import com.adins.esign.webservices.model.ViewRequestParamJobResultResponse;

public interface JobResultService {
	
	SubmitRekonJobResultResponse submitRekonJobResult (SubmitRekonJobResultRequest request);
	CancelJobResponse cancelJob(CancelJobRequest request);
	GetListJobRekonResultResponse getListJobRekonResultResponse(GetListJobRekonResultRequest request) throws ParseException;
	ViewRequestParamJobResultResponse getRequestParamJobResult(ViewRequestParamJobResultRequest request);
}