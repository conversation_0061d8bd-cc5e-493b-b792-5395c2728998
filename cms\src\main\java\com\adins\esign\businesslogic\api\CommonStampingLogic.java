package com.adins.esign.businesslogic.api;

import java.io.IOException;
import java.util.List;

import com.adins.esign.constants.enums.StampingDocumentType;
import com.adins.esign.constants.enums.StampingErrorDetail;
import com.adins.esign.constants.enums.StampingErrorLocation;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDStampduty;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.custom.IncrementAgreementStampingErrorCountBean;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface CommonStampingLogic {
	// General parameter
	int getStampingMaxErrorCount(AuditContext audit);
	int getStampingRetryAttempts(AuditContext audit);
	long getStampingConnectionTimeout(StampingDocumentType documentType, AuditContext audit);
	long getStampingReadTimeout(StampingDocumentType documentType, AuditContext audit);
	String getAccountUsername(TrDocumentH documentH, AuditContext audit);
	String getAccountPassword(TrDocumentH documentH, AuditContext audit);
	String[] getErrorEmailRecipients(MsTenant tenant, AuditContext audit);
	
	// Get document to do UPL DOC
	String getDocumentFileToUpload(TrDocumentD document, AuditContext audit) throws IOException;
	String getPaymentReceiptToUpload(TrDocumentD document, AuditContext audit);
	
	// GEN_SDT parameter
	String getStampDutyFee(AuditContext audit);
	String getNamaDocForGenerate(TrDocumentD document, AuditContext audit);
	String getNoDocForGenerate(TrDocumentD document, AuditContext audit);
	String getTglDocForGenerate(TrDocumentD document, AuditContext audit);
	String getBalanceMutationNotesForGenerate(TrDocumentDStampduty docSdt, AuditContext audit);
	
	// STM_SDT parameter
	String getReasonForStamp(TrDocumentD document, AuditContext audit);
	String getOnPremStampDestination(TrDocumentD document, AuditContext audit);
	String getOnPremSpecimenPath(TrDocumentDStampduty docSdt, AuditContext audit);
	String getOnPremSource(TrDocumentD document, AuditContext audit);
	
	// UPL_OSS parameter
	int getFileCheckAttempts(AuditContext audit);
	long getFileCheckDelay(AuditContext audit);
	
	// UPL_CON parameter
	String getIntegrationValue(TrDocumentD document, AuditContext audit);

	// Table update for stamping
	void updateDocumentHMeteraiProcess(TrDocumentH documentH, String prosesMeterai, AuditContext audit);
	void updateDocumentDMeteraiProcess(TrDocumentD document, String sdtProcess, AuditContext audit);
	void incrementAgreementStampingErrorCount(IncrementAgreementStampingErrorCountBean bean, String processMeterai, StampingDocumentType documentType, StampingErrorLocation errorLocation, StampingErrorDetail errorDetail, boolean throwException, AuditContext audit);
	
	// Document access to OSS
	String storeStampedDocumentToOss(TrDocumentD document, String peruriLoginToken, AuditContext audit);
	String getStampedDocumentFromOss(TrDocumentD document, AuditContext audit);
	
	// Others
	boolean enoughSdtBalance(TrDocumentD document, int sdtNeeded, AuditContext audit);
	boolean allDocumentsProcessed(List<TrDocumentD> documents);
}
