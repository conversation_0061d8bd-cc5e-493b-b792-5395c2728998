package com.adins.esign.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.VendorRegisteredUserHistoryDao;
import com.adins.esign.model.MsVendorRegisteredUserHistory;

@Transactional
@Component
public class VendorRegisteredUserHistoryDaoHbn  extends BaseDaoHbn implements VendorRegisteredUserHistoryDao{

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertVendorRegisteredUserHistoryNewTran(MsVendorRegisteredUserHistory msVendorRegisteredUserHistory) {
		
		
		managerDAO.insert(msVendorRegisteredUserHistory);
	}
}
