package com.adins.esign.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.RegionLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.RegionBean;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.RegionListEmbedRequest;
import com.adins.esign.webservices.model.RegionListRequest;
import com.adins.esign.webservices.model.RegionListResponse;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericRegionLogic extends BaseLogic implements RegionLogic {

	@Autowired CommonLogic commonLogic;
	@Autowired UserValidatorLogic userValidatorLogic;
	
	@Override
	public RegionListResponse getRegionList(RegionListRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(messageSource.getMessage("businesslogic.paymentsigntype.emptytenantcode", null
					, this.retrieveLocaleAudit(audit))
					, ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		RegionListResponse response = new RegionListResponse();
		List<RegionBean> beans = new ArrayList<>();
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), false, audit);
		MsUseroftenant uot = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCode(user.getIdMsUser(), request.getTenantCode());
		
		if (null != uot) {
			List<MsRegion> regions = daoFactory.getRegionDao().getRegionListByTenant(request.getTenantCode());
			for (MsRegion region : regions) {
				RegionBean bean = new RegionBean();
				bean.setRegionCode(region.getRegionCode());
				bean.setRegionName(region.getRegionName());
				beans.add(bean);
			}
		}
		
		response.setRegionList(beans);
		return response;
	}

	@Override
	public RegionListResponse getRegionListEmbed(RegionListEmbedRequest request, AuditContext audit) {
		EmbedMsgBean msgBean =  commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		audit.setCallerId(msgBean.getEmail());
		
		if (StringUtils.isBlank(msgBean.getTenantCode())) {
			throw new TenantException(getMessage("businesslogic.paymentsigntype.emptytenantcode", null, audit), ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		if (null == tenant) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {"Tenant code", msgBean.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		RegionListResponse response = new RegionListResponse();
		List<RegionBean> beans = new ArrayList<>();
		
		List<MsRegion> regions = daoFactory.getRegionDao().getRegionListByTenant(tenant.getTenantCode());
		for (MsRegion region : regions) {
			RegionBean bean = new RegionBean();
			bean.setRegionCode(commonLogic.encryptMessageToString(region.getRegionCode(), audit));
			bean.setRegionName(region.getRegionName());
			beans.add(bean);
		}
		
		response.setRegionList(beans);
		return response;
	}

	@Override
	public MsRegion insertUnregisteredRegion(String regionCode, String regionName, MsTenant tenant, AuditContext audit) {
		if (StringUtils.isBlank(regionCode)) {
			return null;
		}
		
		MsRegion region = daoFactory.getRegionDao().getRegionByCodeAndTenant(regionCode, tenant.getTenantCode());
		if (null == region) {
			region = new MsRegion();
			region.setUsrCrt(audit.getCallerId());
			region.setDtmCrt(new Date());
			region.setRegionCode(StringUtils.upperCase(regionCode));
			region.setRegionName(regionName);
			region.setMsTenant(tenant);
			daoFactory.getRegionDao().insertRegion(region);
		}
		return region;
	}

}
