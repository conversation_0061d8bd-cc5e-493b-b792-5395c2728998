package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.esign.model.MsOffice;

public interface OfficeDao {
	
	void insertOffice(MsOffice newOffice);
	void insertOfficeNewTrx(MsOffice office);
	void updateOffice(MsOffice office);
	
	MsOffice getActiveOfficeByOfficeCodeAndTenantCode(String officeCode, String tenantCode);
	MsOffice getFirstOfficeByTenantCode(String tenantCode);
	
	List<MsOffice> getOfficeList(String tenantCode);
	List<MsOffice> getOfficeListByTenantCodeAndRegionCode(String tenantCode,String regionCode);
	
}
