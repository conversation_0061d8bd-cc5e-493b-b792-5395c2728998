package com.adins.esign.businesslogic.impl;

import static org.junit.Assert.assertNotNull;

import java.io.IOException;
import java.text.ParseException;

import javax.transaction.Transactional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.BalanceBean;
import com.adins.esign.model.custom.BalanceThresholdBean;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.google.gson.Gson;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class GenericSchedulerLogicTest extends BaseLogic{
	@Autowired private SchedulerLogic schedLogic;
	@Autowired private TenantLogic tenantLogic;
	private MsTenant tenant;
	private AuditContext auditContext = new AuditContext();
	private String tenantCodeJunit ="WOM";
	@BeforeEach
	void setup() {
		auditContext.setCallerId("JUNIT");
		tenant =  tenantLogic.getTenantByCode(tenantCodeJunit, auditContext);
	}

	@Test
	@Rollback(true)
	void emailReminderTopUpTest() {
			try {
				schedLogic.emailReminderTopUp(auditContext);
			} catch (ParseException e) {
				e.printStackTrace();
			}
	}	
	
	@Test
	@Rollback(true)
	void digiBalanceSyncTest() {
			try {
				schedLogic.digiBalanceSync(auditContext);
			} catch (IOException e) {
				e.printStackTrace();
			}
	}	

}
