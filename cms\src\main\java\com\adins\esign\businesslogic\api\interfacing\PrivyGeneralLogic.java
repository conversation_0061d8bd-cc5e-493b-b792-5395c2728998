package com.adins.esign.businesslogic.api.interfacing;

import java.io.IOException;
import java.util.List;

import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrPsreSigningConfirmation;
import com.adins.esign.model.custom.RegisterVerificationStatusBean;
import com.adins.esign.model.custom.SendDocFullApiRequestBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.privygeneral.PrivyGeneralRegisterResponseContainer;
import com.adins.esign.webservices.model.DocumentConfinsRequestBean;
import com.adins.esign.webservices.model.InsertDocumentManualSignRequest;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpRequestSigningResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpValidationResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralRegisterResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralRegisterStatusResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralUploadDocumentResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface PrivyGeneralLogic {
	
	// Register related
	PrivyGeneralRegisterResponseContainer registerExternal(RegisterExternalRequest request, String trxNo, MsTenant tenant, MsVendor vendor, AuditContext audit);
	PrivyGeneralRegisterResponseContainer registerWithInvitationRequest(UserBean userData, String trxNo, MsTenant tenant, MsVendor vendor, AuditContext audit);
	String buildRegisterErrorMessage(PrivyGeneralRegisterResponse response, AuditContext audit);
	PrivyGeneralRegisterStatusResponse checkRegisterStatus(MsVendoroftenant vendoroftenant, TrBalanceMutation mutation, AuditContext audit) throws IOException;
	String getRegisterStatusMessage(PrivyGeneralRegisterStatusResponse response, AuditContext audit);
	RegisterVerificationStatusBean getCheckRegisterVerificationResults(PrivyGeneralRegisterStatusResponse response, AuditContext audit);
	
	// Send document related
	PrivyGeneralUploadDocumentResponse uploadDoc(DocumentConfinsRequestBean normal, SendDocFullApiRequestBean external, InsertDocumentManualSignRequest manual, TrDocumentD docD, MsTenant tenant, MsVendor vendor, AuditContext audit) throws IOException;
	String buildUploadDocErrorMessage(PrivyGeneralUploadDocumentResponse response, AuditContext audit);
	
	// Sign related
	PrivyGeneralOtpRequestSigningResponse otpRequestSigning(String signerUserId, MsTenant tenant, MsVendor vendor, List<String> documentId, AuditContext audit);

	String buildOtpRequestSigningErrorMessage(PrivyGeneralOtpRequestSigningResponse response, AuditContext audit);
	PrivyGeneralOtpValidationResponse otpValidation(String otpCode,TrPsreSigningConfirmation psreSigningCOnfirmation, MsTenant tenant, MsVendor vendor, AuditContext audit);
	PrivyGeneralOtpValidationResponse otpValidation(String otpCode,TrPsreSigningConfirmation psreSigningCOnfirmation, MsTenant tenant, MsVendor vendor, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit);
	String buildOtpValidationErrorMessage(PrivyGeneralOtpValidationResponse response, AuditContext audit);
}
