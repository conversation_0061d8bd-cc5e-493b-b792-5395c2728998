package com.adins.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.sql.Clob;
import java.sql.SQLException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.security.SecureRandom;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.adins.esign.constants.GlobalVal;

public class Tool {
	public static final long[] EMPTY_LONG_ARRAY = new long[0];
	public static final Long[] EMPTY_LONG_OBJECT_ARRAY = new Long[0];
	private static final Logger LOG = LoggerFactory.getLogger(Tool.class);

	Tool() {

	}

	public static String clobToString(Clob data) {
		if (null == data) {
			return null;
		}

		StringBuilder sb = new StringBuilder();
		try {
			Reader reader = data.getCharacterStream();
			BufferedReader br = new BufferedReader(reader);

			String line;
			while (null != (line = br.readLine())) {
				sb.append(line);
			}
			br.close();
		} catch (SQLException | IOException e) {
			LOG.error(GlobalVal.STACK_TRACE, (Object[]) e.getStackTrace());
		}
		return sb.toString();
	}

	public static Object[][] toSqlParams(Deque<Object[]> stack) {
		if (stack == null)
			return new Object[0][0];

		Object[][] sqlParams = new Object[stack.size()][2];
		Iterator<Object[]> iterator = stack.iterator();
		int i = 0;
		while (iterator.hasNext()) {
			sqlParams[i] = iterator.next();
			i++;
		}

		return sqlParams;
	}

	public static long[] strArrayToLongArray(String[] arr) {
		if (arr == null || arr.length == 0) {
			return EMPTY_LONG_ARRAY;
		}

		long[] resultArr = new long[arr.length];
		for (int i = 0; i < arr.length; i++) {
			resultArr[i] = Long.parseLong(arr[i]);
		}

		return resultArr;
	}

	public static Long[] strArrayToLongWrapperArray(String[] arr) {
		if (arr == null || arr.length == 0) {
			return EMPTY_LONG_OBJECT_ARRAY;
		}

		List<Long> resultList = new ArrayList<>(arr.length);
		for (String s : arr) {
			if (!StringUtils.isEmpty(s)) {
				resultList.add(Long.parseLong(s));
			}
		}

		Long[] result = new Long[resultList.size()];
		return resultList.toArray(result);
	}

	public static String generateRandomNumber(int charLength) {
		SecureRandom rand = new SecureRandom();
		StringBuilder resultStr = new StringBuilder();
		
		if (resultStr.length() != charLength) {
			for (int i = 0; i < charLength; i++) {
				resultStr.append(rand.nextInt(10));
			}
		}

		return resultStr.toString();
	}

	public static Date addMinuteFromNow(int addMinuteTime) {
		Date targetTime = new Date(); // now
		targetTime = DateUtils.addMinutes(targetTime, addMinuteTime);
		return targetTime;
	}

	public static String generateRandomAlphaNumericString(int length) {
		String capsAlphaString = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
		String alphaString = "abcdefghijklmnopqrstuvxyz";
		String numericString = "1234567890";
		StringBuilder sb = new StringBuilder(length);
		SecureRandom rand = new SecureRandom();

		for (int i = 0; i < length - (length - 2); i++) {
			int index = (int) (capsAlphaString.length() * rand.nextDouble());
			sb.append(capsAlphaString.charAt(index));
		}
		for (int i = 0; i < length - 4; i++) {
			int index = (int) (alphaString.length() * rand.nextDouble());
			sb.append(alphaString.charAt(index));
		}
		for (int i = 0; i < length - (length - 2); i++) {
			int index = (int) (numericString.length() * rand.nextDouble());
			sb.append(numericString.charAt(index));
		}

		return sb.toString();
	}

	public static Date truncateDate(Date date, ChronoUnit chronoUnit) {
		Instant instant = date.toInstant();
		ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
		ZonedDateTime truncatedZonedDateTime = zonedDateTime.truncatedTo(chronoUnit);
		Instant truncatedInstant = truncatedZonedDateTime.toInstant();
		return Date.from(truncatedInstant);
	}
	
	public static boolean compareShort(Short a, Short b) {
		if (a == null && b == null) {
			return true;
		}
		
		if (a == null || b == null) {
			return false;
		}
		
		int compareResult = a.compareTo(b);
		return compareResult == 0;
	}
}
