package com.adins.esign.webservices.external.api;

import java.io.IOException;
import java.text.ParseException;

import com.adins.esign.webservices.model.CheckStampingStatusRequest;
import com.adins.esign.webservices.model.CheckStampingStatusResponse;
import com.adins.esign.webservices.model.CheckStatusSigningRequest;
import com.adins.esign.webservices.model.CheckStatusSigningResponse;
import com.adins.esign.webservices.model.DownloadDocumentFullApiRequest;
import com.adins.esign.webservices.model.GetTotalUnsignedDocumentRequest;
import com.adins.esign.webservices.model.GetTotalUnsignedDocumentResponse;
import com.adins.esign.webservices.model.RequestStampingRequest;
import com.adins.esign.webservices.model.RequestStampingResponse;
import com.adins.esign.webservices.model.SendDocFullApiRequest;
import com.adins.esign.webservices.model.SendDocFullApiResponse;
import com.adins.esign.webservices.model.SignDocumentFullApiRequest;
import com.adins.esign.webservices.model.SignDocumentFullApiResponse;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptResponse;
import com.adins.esign.webservices.model.external.CancelDocumentExternalRequest;
import com.adins.esign.webservices.model.external.GetSignLinkExternalRequest;
import com.adins.esign.webservices.model.external.GetSignLinkExternalResponse;
import com.adins.esign.webservices.model.external.GetTemplateSignLocationRequest;
import com.adins.esign.webservices.model.external.GetTemplateSignLocationResponse;
import com.adins.esign.webservices.model.external.InsertStampingMateraiExternalRequest;
import com.adins.esign.webservices.model.external.SigningHashFileRequest;
import com.adins.esign.webservices.model.external.SigningHashFileResponse;
import com.adins.framework.service.base.model.MssResponseType;

public interface DocumentExternalService {
	SendDocFullApiResponse sendDocument(SendDocFullApiRequest request) throws Exception;
	CheckStampingStatusResponse checkStampingStatus(CheckStampingStatusRequest request);
	RequestStampingResponse requestStamping(RequestStampingRequest request);
	CheckStatusSigningResponse checkStatusSigning(CheckStatusSigningRequest request);
	SignDocumentFullApiResponse signDocumentFullApi(SignDocumentFullApiRequest signDocRequest) throws IOException, ParseException;
	ViewDocumentResponse downloadDocumentFullApi(DownloadDocumentFullApiRequest request);
	GetTotalUnsignedDocumentResponse getTotalUnsignedDocuments(GetTotalUnsignedDocumentRequest request);
	GetSignLinkExternalResponse getSignLink (GetSignLinkExternalRequest request);
	GetTemplateSignLocationResponse getTemplateSignLocation (GetTemplateSignLocationRequest request);
	SigningHashFileResponse signingHashFile(SigningHashFileRequest request);
	InsertStampingPaymentReceiptResponse insertStampingMaterai(InsertStampingMateraiExternalRequest request) throws ParseException;
	MssResponseType cancelDocument(CancelDocumentExternalRequest request);
}
