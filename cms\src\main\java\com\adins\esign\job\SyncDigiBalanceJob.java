package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class SyncDigiBalanceJob {
	private static final Logger LOG = LoggerFactory.getLogger(SyncDigiBalanceJob.class);
	private static final String SCHEDULER = "SCHEDULER";
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void syncDigiBalance() {
		try {
			LOG.info("Job Sync Digi Balance Started");
			AuditContext auditContext = new AuditContext(SCHEDULER);
			schedulerLogic.digiBalanceSync(auditContext);
			LOG.info("Job Sync Digi Balance Finished");
		} catch (Exception e) {
			LOG.error("Error on running Sync Digi Balance job", e);
		}
	}
}
