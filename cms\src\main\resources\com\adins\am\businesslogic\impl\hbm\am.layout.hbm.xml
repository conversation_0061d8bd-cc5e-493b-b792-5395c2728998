<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="am.layout.selectMenuByMember">
		<query-param name="uuidMsuser" type="long" />
		WITH VMENURIGHTS AS (
			SELECT msm.UUID_MS_MENU, msm.PARENT_MENU_ID
		    FROM AM_MEMBEROFGROUP amog with (nolock)
		        INNER JOIN AM_MSGROUP amg with (nolock) 
		        	ON amog.UUID_MS_GROUP=amg.UUID_MS_GROUP
		        INNER JOIN AM_MENUOFGROUP amnog with (nolock) 
		        	ON (amg.UUID_MS_GROUP=amnog.UUID_MS_GROUP AND amog.UUID_MS_GROUP=amnog.UUID_MS_GROUP)
		        INNER JOIN AM_MSMENU msm with (nolock) 
		        	on amnog.UUID_MS_MENU=msm.UUID_MS_MENU
		    WHERE amg.IS_ACTIVE = '1'
		        AND amog.UUID_MS_USER = :uuidMsuser
		 	GROUP BY msm.UUID_MS_MENU, msm.PARENT_MENU_ID
		),
		N AS (
		    SELECT lm.UUID_MS_MENU, lm.PARENT_MENU_ID, lm.MENU_TYPE, lm.MENU_LEVEL,
		           lm.MENU_ORDER, lm.MENU_PROMPT, lm.MENU_REFF, lm.MENU_ORDER AS HIRARKI,
		           CAST(lm.MENU_ORDER AS VARCHAR(MAX)) AS HIRARKI2
		   	FROM (
				SELECT amm.UUID_MS_MENU, amm.PARENT_MENU_ID, amm.MENU_TYPE, amm.MENU_LEVEL,
		               amm.MENU_ORDER, amm.MENU_PROMPT, amm.MENU_REFF
		        FROM AM_MSMENU amm with (nolock)
		        	INNER JOIN AM_MSUSER usr with (nolock) 
		        	ON (amm.UUID_MS_SUBSYSTEM = usr.UUID_MS_SUBSYSTEM AND usr.UUID_MS_USER = :uuidMsuser)
		        WHERE amm.IS_ACTIVE = '1'
		        	AND amm.UUID_MS_MENU IN (
		        		SELECT PARENT_MENU_ID FROM VMENURIGHTS
		            )
			) lm
		    WHERE lm.PARENT_MENU_ID IS NULL
		
		    UNION ALL
		
		    SELECT lm2.UUID_MS_MENU, lm2.PARENT_MENU_ID, lm2.MENU_TYPE, lm2.MENU_LEVEL,
		           lm2.MENU_ORDER, lm2.MENU_PROMPT, lm2.MENU_REFF, N.HIRARKI AS HIRARKI,
		           N.HIRARKI2+'/'+CAST(lm2.MENU_ORDER AS VARCHAR(MAX)) AS HIRARKI2
		    FROM (
				SELECT amm.UUID_MS_MENU, amm.PARENT_MENU_ID, amm.MENU_TYPE, amm.MENU_LEVEL,
		               amm.MENU_ORDER, amm.MENU_PROMPT, amm.MENU_REFF
		        FROM AM_MSMENU amm with (nolock)
		        	INNER JOIN VMENURIGHTS vm with (nolock) ON amm.UUID_MS_MENU = vm.UUID_MS_MENU
		        WHERE amm.IS_ACTIVE='1'
		    ) lm2, N
		    WHERE N.UUID_MS_MENU = lm2.PARENT_MENU_ID
		)
		SELECT UUID_MS_MENU, PARENT_MENU_ID, MENU_TYPE, MENU_LEVEL, MENU_PROMPT, MENU_REFF
		FROM N
		ORDER BY HIRARKI, HIRARKI2
	</sql-query>
</hibernate-mapping>