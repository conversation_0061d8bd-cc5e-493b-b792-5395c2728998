package com.adins.esign.businesslogic.impl;

import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Base64;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.ExcelLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SigningProcessAuditTrailLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.custom.InquiryAuditTrailSignProcessBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailDetailBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.DownloadAuditTrailExcelRequest;
import com.adins.esign.webservices.model.DownloadAuditTrailExcelResponse;
import com.adins.esign.webservices.model.InquiryAuditTrailSignProcessRequest;
import com.adins.esign.webservices.model.InquiryAuditTrailSignProcessResponse;
import com.adins.esign.webservices.model.SigningProcessAuditTrailRelatedDocumentRequest;
import com.adins.esign.webservices.model.SigningProcessAuditTrailRelatedDocumentResponse;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.StatusCode;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.util.ZipCompressionUtils;

@Component
public class GenericSigningProcessAuditTrailLogic extends BaseLogic implements SigningProcessAuditTrailLogic {
	
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired ExcelLogic excelLogic;

	@Override
	public void logProcessRequestResponse(TrSigningProcessAuditTrail trail, String subfolderName, String requestBody, String responseBody, boolean appendExistingLog, AuditContext audit) {
		
		StringBuilder builder = new StringBuilder();
		
		if (appendExistingLog) {
			byte[] zippedTextFile = cloudStorageLogic.getZippedApiLogAuditTrail(subfolderName, trail);
			if (ArrayUtils.isNotEmpty(zippedTextFile)) {
				String unzippedText = ZipCompressionUtils.unzipText(zippedTextFile);
				builder.append(unzippedText);
			}
		}
		
		if (builder.length() > 0) {
			builder.append(System.lineSeparator());
		}
		
		if (StringUtils.isNotBlank(requestBody)) {
			builder.append("REQ: ");
			builder.append(requestBody);
		}
		
		if (builder.length() > 0) {
			builder.append(System.lineSeparator());
		}
		
		if (StringUtils.isNotBlank(responseBody)) {
			builder.append("RES: ");
			builder.append(responseBody);
		}
		
		String appendedLog = builder.toString();
		String textFilename = String.valueOf(trail.getIdSigningProcessAuditTrail()) + ".txt";
		
		byte[] zippedAppendedLog = ZipCompressionUtils.zipText(textFilename, appendedLog);
		cloudStorageLogic.storeZippedApiLogAuditTrail(subfolderName, trail, zippedAppendedLog);
	}
	
	private void isDateValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
	
		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return;
		}
		if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
			try {
				sdf.parse(startDate);
			} catch (ParseException e) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
			}
			return;
		}
		if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
			try {
				sdf.parse(endDate);
			} catch (ParseException e) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
			}
			return ;
		}
		
		
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			if(start.after(end)) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_EXCEED_LIMIT,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
			}
			
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		
		
	}
	@Override
	public InquiryAuditTrailSignProcessResponse getInquiryAuditTrail(InquiryAuditTrailSignProcessRequest request, AuditContext audit) {
		InquiryAuditTrailSignProcessResponse response = new InquiryAuditTrailSignProcessResponse();
		String messageValidation ="";
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,  new Object[] {"NIK/Email/Phone"}, audit);
		commonValidatorLogic.validateNotNull(request.getNik(), messageValidation, StatusCode.MANDATORY_PARAMETER);
		
		MsVendorRegisteredUser vendorUser = new MsVendorRegisteredUser();
		
		if (StringUtils.isNumeric(request.getNik())) {
			if (request.getNik().length() == 16) {
				vendorUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserByidNo(request.getNik());
				messageValidation = getMessage("businesslogic.user.usernotfoundwiththatid",  new Object[] {request.getNik()}, audit);
				commonValidatorLogic.validateNotNull(vendorUser, messageValidation, StatusCode.USER_NOT_FOUND_WITH_THAT_ID_NO);
			} else {
				vendorUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserByPhoneNumber(request.getNik());	
				messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_PHONE_NOT_FOUND,  new Object[] {request.getNik()}, audit);
				commonValidatorLogic.validateNotNull(vendorUser, messageValidation, StatusCode.USER_NOT_FOUND_WITH_THAT_PHONE_NO);
			}
			
		} else {
			vendorUser = daoFactory.getVendorRegisteredUserDao().getLatestVendorRegisteredUserBySignerRegisteredEmail(request.getNik());
			messageValidation = getMessage("businesslogic.usermanagement.datausermanagementnotfound",  new Object[] {request.getNik()}, audit);
			commonValidatorLogic.validateNotNull(vendorUser, messageValidation, StatusCode.USER_NOT_FOUND_WITH_THAT_PHONE_NO);
		}
		
		this.isDateValid(request.getInquiryStartDate(), request.getInquiryEndDate(), audit);
		
		String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_MAX_ROW, audit);
		Integer rowPerPage = Integer.parseInt(gsValue);

		int start = ((request.getPage() - 1) * rowPerPage) + 1;
		int end = request.getPage() * rowPerPage;
		
		List<InquiryAuditTrailSignProcessBean> listInquiryBeans = new ArrayList<>();
		
		BigInteger totalResult = daoFactory.getSigningProcessAuditTrailDao().getCountListInquiryAuditTrail(request, vendorUser);
		long total = totalResult.longValue();
		long totalPage = (total % rowPerPage == 0) ? total / rowPerPage : (total / rowPerPage) + 1;
		List<Map<String, Object>> inquiryAuditTrails = daoFactory.getSigningProcessAuditTrailDao().getListInquiryAuditTrail(request, vendorUser, start, end);
		
		Iterator<Map<String, Object>> itr = inquiryAuditTrails.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			InquiryAuditTrailSignProcessBean bean = new InquiryAuditTrailSignProcessBean();
			bean.setEmail((String) map.get("d1"));
			bean.setTenantCode((String) map.get("d2"));
			bean.setPsre((String) map.get("d3"));
			bean.setDate((String) map.get("d4"));
			bean.setProcessType((String) map.get("d5").toString());
			bean.setNoHP(MssTool.maskData(personalDataEncLogic.decryptToString((byte[]) map.get("d6"))) );
			bean.setNik(MssTool.maskData(personalDataEncLogic.decryptToString((byte[]) map.get("d7"))));
			bean.setResultStatus((String) map.get("d8"));
			bean.setNotificationType((String) map.get("d9"));
			bean.setNotificationVendor((String) map.get("d10"));
			bean.setSendingPoint((String) map.get("d11"));
			bean.setOtpCode((String) map.get("d12"));
			bean.setNotes((String) map.get("d13"));
			bean.setIdSigningProcessAuditTrail(map.get("d14").toString());
			bean.setDocumentDetailStatus((String) map.get("d15"));
			listInquiryBeans.add(bean);
		}
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult((int) total);
		response.setListinquiryAuditTrailSignProcess(listInquiryBeans);
		return response;
	}

	@Override
	public SigningProcessAuditTrailRelatedDocumentResponse getSigningProcessAuditTrailRelatedDocument(SigningProcessAuditTrailRelatedDocumentRequest request, AuditContext audit) {
		SigningProcessAuditTrailRelatedDocumentResponse response = new SigningProcessAuditTrailRelatedDocumentResponse();
		String messageValidation ="";
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,  new Object[] {"Id Signing Process Audi Trail"}, audit);
		commonValidatorLogic.validateNotNull(request.getIdSigningProcessAuditTrail(), messageValidation, StatusCode.MANDATORY_PARAMETER);
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,  new Object[] {"Email"}, audit);
		commonValidatorLogic.validateNotNull(request.getEmail(), messageValidation, StatusCode.MANDATORY_PARAMETER);
		
		
		userValidatorLogic.validateGetUserByEmailv2(request.getEmail(), true, audit);
	
		TrSigningProcessAuditTrail signingProcessAuditTrail = daoFactory.getSigningProcessAuditTrailDao().getSigningProcessAuditTrailByIdAndEmail(Long.parseLong(request.getIdSigningProcessAuditTrail()),StringUtils.upperCase(request.getEmail()) );
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,  new Object[] {"Signing Process Audit Trail", request.getIdSigningProcessAuditTrail()}, audit);
		commonValidatorLogic.validateNotNull(signingProcessAuditTrail, messageValidation, StatusCode.ERROR_EXIST);
		List<SigningProcessAuditTrailDetailBean> listSigningProcessAuditTrailDetailBean = new ArrayList<>();;
		
		List<Map<String, Object>> inquiryAuditTrailDetails = daoFactory.getSigningProcessAuditTrailDao().getInquiryAuditTrailDetail(Long.parseLong(request.getIdSigningProcessAuditTrail()));
		
		Iterator<Map<String, Object>> itr = inquiryAuditTrailDetails.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			SigningProcessAuditTrailDetailBean bean = new SigningProcessAuditTrailDetailBean();
			bean.setRefNumber(map.get("d0").toString());
			bean.setDocumentId(map.get("d1").toString());
			
			listSigningProcessAuditTrailDetailBean.add(bean);
		}
		response.setListSigningProcessAuditTrailDetailBean(listSigningProcessAuditTrailDetailBean);
		return response;
		
	}
	
	@Override
	public DownloadAuditTrailExcelResponse downloadAuditTrailSignProcess(DownloadAuditTrailExcelRequest request,
			AuditContext audit) {
		String messageValidation ="";
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,  new Object[] {"NIK/Email/Phone"}, audit);
		commonValidatorLogic.validateNotNull(request.getNik(), messageValidation, StatusCode.MANDATORY_PARAMETER);

		AmMsuser user = new AmMsuser();
		
		if (StringUtils.isNumeric(request.getNik())) {
			if(request.getNik().length() == 16) {
				user = userValidatorLogic.validateGetUserByNik(request.getNik(), true, audit);
			} else {
				user = userValidatorLogic.validateGetUserByPhone(request.getNik(), true, audit);
			}
		} else {
			user = userValidatorLogic.validateGetUserByEmailv2(request.getNik(), true, audit);
		}
				
		List<Map<String, Object>> inquiryAuditTrails = daoFactory.getSigningProcessAuditTrailDao().downloadListInquiryAuditTrail(request, user);

		List<InquiryAuditTrailSignProcessBean> listInquiryBeans = new ArrayList<>();
		Iterator<Map<String, Object>> itr = inquiryAuditTrails.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			InquiryAuditTrailSignProcessBean bean = new InquiryAuditTrailSignProcessBean();
			bean.setEmail(map.get("d1").toString());
			bean.setTenantCode(map.get("d2").toString());
			bean.setPsre(map.get("d3").toString());
			bean.setDate(map.get("d4").toString());
			bean.setProcessType(map.get("d5").toString());
			bean.setNoHP(personalDataEncLogic.decryptToString( (byte[]) map.get("d6")));
			bean.setNik(personalDataEncLogic.decryptToString( (byte[])  map.get("d7")));
			bean.setResultStatus(map.get("d8").toString());
			bean.setNotificationType( map.get("d9").toString());
			bean.setNotificationVendor(map.get("d10").toString());
			bean.setSendingPoint(map.get("d11").toString());
			bean.setOtpCode(map.get("d12").toString());
			bean.setNotes(map.get("d13").toString());
			
			listInquiryBeans.add(bean);
		}

		String base64Excel = this.generateDocumentExcelFile(listInquiryBeans, audit);

		String fileName = this.generateDocumentExcelFilename(request);

		DownloadAuditTrailExcelResponse response = new DownloadAuditTrailExcelResponse();
		response.setBase64ExcelReport(base64Excel);
		response.setFilename(fileName);

		return response;
	}

	private String generateDocumentExcelFilename(DownloadAuditTrailExcelRequest request) {
		StringBuilder filename = new StringBuilder();
		filename.append("DOCUMENT_REPORT");
		if (StringUtils.isNotBlank(request.getInquiryStartDate())) {
			filename.append("_").append(request.getInquiryStartDate());
		}
		if (StringUtils.isNotBlank(request.getInquiryEndDate())) {
			filename.append("_").append(request.getInquiryEndDate());
		}
		if (StringUtils.isNotBlank(request.getNik())) {
			filename.append("_").append(request.getNik());
		}
		if (StringUtils.isNotBlank(request.getProcessType())) {
			filename.append("_").append(request.getProcessType());
		}
		if (StringUtils.isNotBlank(request.getRefNumber())) {
			filename.append("_").append(request.getRefNumber());
		}
		if (StringUtils.isNotBlank(request.getTenantCode())) {
			filename.append("_").append(request.getTenantCode());
		}
		filename.append("_").append(MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_FORMAT_SDT_REPORT));
		filename.append(".xlsx");
		return filename.toString();
	}
	
	private String generateDocumentExcelFile(List<InquiryAuditTrailSignProcessBean> listDocument,
			AuditContext audit) {
		byte[] excelByteArray = null;
		try {
			excelByteArray = excelLogic.generate((workbook, styleBoldText) -> this
					.createDocumentExcelSheet(workbook, styleBoldText, listDocument));
		} catch (Exception e) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.document.generateexcelerror", null,
					this.retrieveLocaleAudit(audit)), ReasonDocument.DOCUMENT_EXCEL_ERROR);
		}
		return Base64.getEncoder().encodeToString(excelByteArray);
	}
	
	private void createDocumentExcelSheet(XSSFWorkbook workbook, XSSFCellStyle styleBoldText,
			List<InquiryAuditTrailSignProcessBean> listDocument) {
		XSSFSheet mainSheet = workbook.createSheet("Laporan Dokumen");

		// Untuk Monitoring HO
		String[] monitoringHODocumentColumn = new String[] {"Email", "Tenant", "PSrE",
				"Signing Process Completion Date", "Process Type", "Phone No", "NIK", "Result Status", "Notification Type", "Notification Vendor", "Sending Point", "Otp Code", "Notes" };

		for (int i = 0; i < monitoringHODocumentColumn.length; i++) {
			if (i == 2) {
				mainSheet.setColumnWidth(i, 11000);
			} else {
				mainSheet.setColumnWidth(i, 6000);
			}
		}

		XSSFRow rowOne = mainSheet.createRow(0);
		for (int i = 0; i < monitoringHODocumentColumn.length; i++) {
			XSSFCell cell = rowOne.createCell(i);
			cell.setCellValue(monitoringHODocumentColumn[i]);
			cell.setCellStyle(styleBoldText);
		}

		// 11 columnms
		for (int i = 0; i < listDocument.size(); i++) {
			XSSFRow row = mainSheet.createRow(i + 1);
			XSSFCell cellOne = row.createCell(0);
			XSSFCell cellTwo = row.createCell(1);
			XSSFCell cellThree = row.createCell(2);
			XSSFCell cellFour = row.createCell(3);
			XSSFCell cellFive = row.createCell(4);
			XSSFCell cellSix = row.createCell(5);
			XSSFCell cellSeven = row.createCell(6);
			XSSFCell cellEight = row.createCell(7);
			XSSFCell cellNine = row.createCell(8);
			XSSFCell cellTen = row.createCell(9);
			XSSFCell cellEleven = row.createCell(10);
			XSSFCell cellTwelve = row.createCell(11);
			XSSFCell cellThirteen = row.createCell(12);

			InquiryAuditTrailSignProcessBean bean = listDocument.get(i);
			cellOne.setCellValue(bean.getEmail());
			cellTwo.setCellValue(bean.getTenantCode());
			cellThree.setCellValue(bean.getPsre());
			cellFour.setCellValue(bean.getDate());
			cellFive.setCellValue(bean.getProcessType());
			cellSix.setCellValue(bean.getNoHP());
			cellSeven.setCellValue(bean.getNik());
			cellEight.setCellValue(bean.getResultStatus());
			cellNine.setCellValue(bean.getNotificationType());
			cellTen.setCellValue(bean.getNotificationVendor());
			cellEleven.setCellValue(bean.getSendingPoint());
			cellTwelve.setCellValue(bean.getOtpCode());
			cellThirteen.setCellValue(bean.getNotes());

		}
	}
}
