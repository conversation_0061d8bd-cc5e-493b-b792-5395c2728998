package com.adins.esign.businesslogic.api.interfacing;

import java.io.IOException;
import java.text.ParseException;

import com.adins.esign.model.custom.PaymentReceiptConfinsSyncResponse;
import com.adins.esign.model.custom.UploadPaymentReceiptToDmsResponse;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptRequest;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface PaymentReceiptStampingLogic {
//	@RolesAllowed("ROLE_CORESYSTEM")
	InsertStampingPaymentReceiptResponse insertStampingPaymentReceipt(InsertStampingPaymentReceiptRequest request, AuditContext audit) throws ParseException;
	
	// Logic untuk job stamping payment receipt
	void retryStampingAllPaymentReceipt(AuditContext audit);
	PaymentReceiptConfinsSyncResponse synConfins(AuditContext audit) throws IOException;
	UploadPaymentReceiptToDmsResponse uploadDms(AuditContext audit) throws IOException;
	void deleteStampingBaseFromOss(AuditContext audit);
}
