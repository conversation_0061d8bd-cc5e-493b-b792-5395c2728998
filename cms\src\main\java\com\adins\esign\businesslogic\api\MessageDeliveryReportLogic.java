package com.adins.esign.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.webservices.model.GetListDeliveryReportForMessageCheckingRequest;
import com.adins.esign.webservices.model.GetListDeliveryReportForMessageCheckingResponse;
import com.adins.esign.webservices.model.GetListMessageDeliveryReportRequest;
import com.adins.esign.webservices.model.GetListMessageDeliveryReportResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface MessageDeliveryReportLogic {

	@RolesAllowed({"ROLE_MESSAGE_DELIVERY"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	GetListMessageDeliveryReportResponse getListMessageDeliveryReport (GetListMessageDeliveryReportRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_CHECK_SENDING_NOTIF"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	GetListDeliveryReportForMessageCheckingResponse getListDeliveryReportForMessageChecking (GetListDeliveryReportForMessageCheckingRequest request, AuditContext audit ) ;

	void insertMessageDeliveryReport(MsTenant tenant, MsVendor vendor, String trxNo, String vendorTrxNo, String recipientDetail, NotificationType type, MsLov messageGateway, MsLov sendingPoint, AuditContext audit);
}
