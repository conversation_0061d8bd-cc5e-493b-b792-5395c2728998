package com.adins.esign.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.FaceVerifyDao;
import com.adins.esign.model.TrFaceVerify;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class FaceVerifyDaoHbn extends BaseDaoHbn implements FaceVerifyDao {

	@Override
	public void insertFaceVerify(TrFaceVerify faceVerify) {
		faceVerify.setUsrCrt(MssTool.maskData(faceVerify.getUsrCrt()));
		this.managerDAO.insert(faceVerify);
	}

}
