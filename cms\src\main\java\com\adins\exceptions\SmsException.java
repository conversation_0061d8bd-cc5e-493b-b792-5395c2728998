package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class SmsException extends AdInsException{
	private static final long serialVersionUID = 1L;

	public enum ReasonSms {
		FAIL_GENERATE_TOKEN,
		FAIL_SEND_SMS,
		UNKNOWN
	}
	
	private final ReasonSms reason;

	public SmsException(ReasonSms reason) {
		this.reason = reason;
	}

	public SmsException(String message, ReasonSms reason) {
		super(message);
		this.reason = reason;
	}

	public SmsException(Throwable ex, ReasonSms reason) {
		super(ex);
		this.reason = reason;
	}

	public SmsException(String message, Throwable ex, ReasonSms reason) {
		super(message, ex);
		this.reason = reason;
	}

	public ReasonSms getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
			case FAIL_GENERATE_TOKEN:
				return StatusCode.SMS_ERROR;
			case FAIL_SEND_SMS:
				return StatusCode.SEND_SMS_ERROR;
			default:
				return StatusCode.UNKNOWN;
			}
			
		}
		return StatusCode.UNKNOWN;
	}

}
