package com.adins.esign.validatorlogic.api;

import com.adins.am.model.AmMsrole;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface RoleValidatorLogic {
	
	AmMsrole validateGetRole(String tenantCode, String roleCode, boolean checkRoleExistence, boolean checkAllRole, AuditContext audit  );
	void validateRoleByNameForAddRole(String tenantCode, String roleName, AuditContext audit);
	void validateRoleByNameForEditRole(String tenantCode, String roleName, AmMsrole existingRole, AuditContext audit);
}
