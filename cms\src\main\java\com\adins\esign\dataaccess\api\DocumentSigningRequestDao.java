package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.esign.model.TrDocumentSigningRequest;
import com.adins.esign.model.TrDocumentSigningRequestDetail;

public interface DocumentSigningRequestDao {
	
	void updateDocumentSigningRequestNewTran(TrDocumentSigningRequest documentSigningRequest);
	
	void insertDocumentSigningRequestnewTran(TrDocumentSigningRequest documentSigningRequest);
	
	void updateDocumentSigningRequest(TrDocumentSigningRequest documentSigningRequest);
	
	void insertDocumentSigningRequest(TrDocumentSigningRequest documentSigningRequest);
	
	List<TrDocumentSigningRequest> getDocumentSigningRequestsByRequestStatusNewTran(Short requestStatus);
	
	List<TrDocumentSigningRequest> getDocumentSigningRequestsByRequestStatusVendorNewTran(Short requestStatus, String vendorCode);
	
	List<TrDocumentSigningRequest> getDocumentSigningRequestByDocIdandUserId (String docId, long idMsUser);

	void insertDocumentSigningRequestDetail(TrDocumentSigningRequestDetail documentSigningRequestDetail);

}
