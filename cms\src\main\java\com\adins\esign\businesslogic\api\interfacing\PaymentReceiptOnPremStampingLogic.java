package com.adins.esign.businesslogic.api.interfacing;

import java.io.IOException;

import com.adins.esign.model.custom.PaymentReceiptConfinsSyncResponse;
import com.adins.esign.model.custom.UploadPaymentReceiptToDmsResponse;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptRequest;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface PaymentReceiptOnPremStampingLogic {
	// API Stamping Payment Receipt
	InsertStampingPaymentReceiptResponse insertStampingPaymentReceipt(InsertStampingPaymentReceiptRequest request, AuditContext audit);
	
	// Scheduler Stamping Payment Receipt
	void retryStampingAllPaymentReceipt(AuditContext audit);
	PaymentReceiptConfinsSyncResponse syncEmeteraiLogsToConfins(AuditContext audit) throws IOException;
	UploadPaymentReceiptToDmsResponse uploadPaymentReceiptsToDms(AuditContext audit);
	void deleteStampingBaseFromOss(AuditContext audit);
}
