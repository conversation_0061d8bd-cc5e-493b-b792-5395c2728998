package com.adins.esign.webservices.frontend.api;

import java.io.IOException;

import com.adins.esign.model.custom.FaceMatchLivenessRequest;
import com.adins.esign.webservices.model.FaceVerifyRequest;
import com.adins.esign.webservices.model.FaceVerifyResponse;
import com.adins.esign.webservices.model.LivenessEmbedRequest;
import com.adins.esign.webservices.model.LivenessRequest;
import com.adins.esign.webservices.model.LivenessResponse;

public interface LivenessService {
	LivenessResponse getTenantLiveness(LivenessRequest request);
	LivenessResponse getTenantLivenessEmbed(LivenessEmbedRequest request);
	FaceVerifyResponse getLivenessCheck(FaceVerifyRequest request) throws IOException;
	FaceVerifyResponse getLivenessCheckNodeflux(FaceMatchLivenessRequest request);
}