package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.dataaccess.api.DocumentSigningRequestDao;
import com.adins.esign.model.TrDocumentSigningRequest;
import com.adins.esign.model.TrDocumentSigningRequestDetail;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class DocumentSigningRequestDaoHbn extends BaseDaoHbn implements DocumentSigningRequestDao {

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentSigningRequestNewTran(TrDocumentSigningRequest documentSigningRequest) {
		documentSigningRequest.setUsrUpd(MssTool.maskData(documentSigningRequest.getUsrUpd()));
		managerDAO.update(documentSigningRequest);
	}
	
	@Override
	public void updateDocumentSigningRequest(TrDocumentSigningRequest documentSigningRequest) {
		documentSigningRequest.setUsrUpd(MssTool.maskData(documentSigningRequest.getUsrUpd()));
		managerDAO.update(documentSigningRequest);
	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentSigningRequest> getDocumentSigningRequestsByRequestStatusNewTran(Short requestStatus) {
		Map<String, Object> params = new HashMap<>();
		params.put("requestStatus", requestStatus);
		
		return (List<TrDocumentSigningRequest>) managerDAO.list(
				"from TrDocumentSigningRequest dsr "
				+ "join fetch dsr.trDocumentD dd "
				+ "join fetch dd.msTenant mt "
				+ "join fetch dd.msVendor mv "
				+ "join fetch dsr.trDocumentH dh "
				+ "join fetch dsr.amMsuser mu "
				+ "where dsr.requestStatus = :requestStatus "
				+ "order by dsr.idDocumentSigningRequest asc ", params).get(GlobalKey.MAP_RESULT_LIST);
	}
	

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertDocumentSigningRequestnewTran(TrDocumentSigningRequest documentSigningRequest) {
		documentSigningRequest.setUsrCrt(MssTool.maskData(documentSigningRequest.getUsrCrt()));
		managerDAO.insert(documentSigningRequest);
	}
	
	@Override
	public void insertDocumentSigningRequest(TrDocumentSigningRequest documentSigningRequest) {
		documentSigningRequest.setUsrCrt(MssTool.maskData(documentSigningRequest.getUsrCrt()));
		managerDAO.insert(documentSigningRequest);
		
	}

	@Override
	public void insertDocumentSigningRequestDetail(TrDocumentSigningRequestDetail documentSigningRequestDetail) {
		documentSigningRequestDetail.setUsrCrt(MssTool.maskData(documentSigningRequestDetail.getUsrCrt()));
		managerDAO.insert(documentSigningRequestDetail);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentSigningRequest> getDocumentSigningRequestByDocIdandUserId(String docId, long idMsUser)
	{
		Map<String, Object> params = new HashMap<>();
		params.put("documentId", docId);
		params.put("idMsUser", idMsUser);
		
		
		return (List<TrDocumentSigningRequest>) managerDAO.list(
				"from TrDocumentSigningRequest dsr "
						+ "join fetch dsr.trDocumentD dd "
						+ "join fetch dd.msTenant mt "
						+ "join fetch dd.msVendor mv "
						+ "join fetch dsr.trDocumentH dh "
						+ "join fetch dsr.amMsuser mu "
						+ "where dd.documentId = :documentId "
						+ "and mu.idMsUser = :idMsUser "
						+ "order by dsr.idDocumentSigningRequest desc ",  params).get(GlobalKey.MAP_RESULT_LIST);
		
	}
				
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@SuppressWarnings("unchecked")
	@Override
	public List<TrDocumentSigningRequest> getDocumentSigningRequestsByRequestStatusVendorNewTran(Short requestStatus, String vendorCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("requestStatus", requestStatus);
		params.put("vendorCode", vendorCode);
		
		return (List<TrDocumentSigningRequest>) managerDAO.list(
				"from TrDocumentSigningRequest dsr "
				+ "left join fetch dsr.trDocumentD dd "
				+ "left join fetch dd.msTenant mt "
				+ "join fetch dsr.msVendor mv "
				+ "join fetch dsr.trDocumentH dh "
				+ "join fetch dsr.amMsuser mu "
				+ "where dsr.requestStatus = :requestStatus and mv.vendorCode = :vendorCode "
				+ "order by dsr.idDocumentSigningRequest asc ", params).get(GlobalKey.MAP_RESULT_LIST);
	}

}
