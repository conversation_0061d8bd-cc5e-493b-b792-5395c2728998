package com.adins.esign.webservices.embed.api;

import com.adins.esign.webservices.model.TenantSettingsEmbedRequest;
import com.adins.esign.webservices.model.TenantSettingsResponse;
import com.adins.esign.webservices.model.embed.CheckLivenessFaceCompareServiceEmbedRequest;
import com.adins.esign.webservices.model.embed.CheckLivenessFaceCompareServiceEmbedResponse;
import com.adins.esign.webservices.model.embed.GetAvailableSendingPointEmbedRequest;
import com.adins.esign.webservices.model.embed.GetAvailableSendingPointEmbedResponse;

public interface TenantEmbedService {
	TenantSettingsResponse getTenantSettingsEmbed(TenantSettingsEmbedRequest request);
	CheckLivenessFaceCompareServiceEmbedResponse checkLivenessFaceCompareServiceEmbed(CheckLivenessFaceCompareServiceEmbedRequest request);
	GetAvailableSendingPointEmbedResponse getAvailableSendingPointEmbed(GetAvailableSendingPointEmbedRequest request);
}
