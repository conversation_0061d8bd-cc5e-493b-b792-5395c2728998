package com.adins.esign.businesslogic.api;

import java.text.ParseException;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.webservices.model.ErrorHistoryActivationStatusRequest;
import com.adins.esign.webservices.model.ErrorHistoryActivationStatusResponse;
import com.adins.esign.webservices.model.ErrorHistoryRequest;
import com.adins.esign.webservices.model.ErrorHistoryResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface ErrorReportLogic {
	
	@RolesAllowed("ROLE_ERROR_REPORT")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ErrorHistoryResponse getErrorHistory(ErrorHistoryRequest request, AuditContext audit) throws ParseException;
	
	@RolesAllowed("ROLE_ERROR_REPORT")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ErrorHistoryActivationStatusResponse getErrorHistActivationStatus(ErrorHistoryActivationStatusRequest request, AuditContext audit);
	
	void updateErrorHistoryRerunProcess(String errorHistoryNik, Long idVendor, AuditContext audit);
}
