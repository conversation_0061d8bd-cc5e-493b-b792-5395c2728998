package com.adins.esign.businesslogic.impl;

import static org.junit.Assert.assertNotNull;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.model.custom.EmailAttachmentBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.google.common.io.BaseEncoding;

@Disabled
//default spring context read from /resources/com/adins/esign/businesslogic/impl/SpringEmailSenderLogicTest-context.xml
@ExtendWith(SpringExtension.class)
class SpringEmailSenderLogicTest {

	@Autowired private EmailSenderLogic emailSenderLogic;
	@Value("${spring.mail.username}") private String fromEmailAddr;

	private EmailInformationBean emailInfo;

	@BeforeEach
	public void setUp() {
		this.emailInfo = new EmailInformationBean();
		this.emailInfo.setSubject("Test Email with JUnit");
		this.emailInfo.setBodyMessage("Lorem ipsum, atau ringkasnya lipsum, adalah teks standar yang ditempatkan untuk " +
				"mendemostrasikan elemen grafis atau presentasi visual seperti font, tipografi, dan tata letak.");
	}

	@Test
	void sendEmailWithoutAttachmentTest() {
		this.emailInfo.setTo(new String[]{"<EMAIL>"});
		this.emailInfo.setCc(new String[]{"<EMAIL>"});
		this.emailInfo.setBcc(new String[]{"<EMAIL>"});
		this.emailInfo.setReplyTo("<EMAIL>");
		this.emailInfo.setFrom(fromEmailAddr);

		try {
			this.emailSenderLogic.sendEmail(emailInfo, null);
		} catch (Exception ignored) {
			ignored.printStackTrace();
		}

		assertNotNull(this.emailInfo);
	}

	@Test
	void sendEmailWithNullAttachmentTest() {
		this.emailInfo.setTo(new String[]{"<EMAIL>"});
		this.emailInfo.setCc(new String[]{"<EMAIL>"});
		this.emailInfo.setReplyTo("<EMAIL>");
		this.emailInfo.setFrom(fromEmailAddr);

		// Test attachment with null binary
		EmailAttachmentBean attachment = new EmailAttachmentBean(null, "adins.gif");
		EmailAttachmentBean[] attachments = {attachment};

		try {
			this.emailSenderLogic.sendEmail(emailInfo, attachments);
		} catch (Exception ignored) {
			ignored.printStackTrace();
		}

		assertNotNull(this.emailInfo);
	}

	@Test
	void failedSendEmailToDestinationTest() {
		EmailInformationBean emailInfoBean = new EmailInformationBean();
		emailInfoBean.setSubject("subject");
		emailInfoBean.setBodyMessage("text message");

		Assertions.assertThrows(IllegalArgumentException.class, () -> {
			emailSenderLogic.sendEmail(emailInfoBean, null);
		});
	}

	@Test
	void sendEmailWithAttachmentTest() {
		String base64 = "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";
		byte[] file = BaseEncoding.base64().decode(base64);
		EmailAttachmentBean attachment = new EmailAttachmentBean(file, "adins.gif");
		EmailAttachmentBean[] attachments = {attachment};
		String[] to = {"<EMAIL>", "<EMAIL>"};
		
		EmailInformationBean emailInfoBean = new EmailInformationBean();
		emailInfoBean.setFrom(fromEmailAddr);
		emailInfoBean.setTo(to);
		emailInfoBean.setSubject("JUnit Send Email");
		emailInfoBean.setBodyMessage("<h1>hello world</h1>");
		
		try {
			emailSenderLogic.sendEmail(emailInfoBean, attachments);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		assertNotNull(to);
	}
}
