multitenant.error.cannotempty = Login arguments cannot be empty!
multitenant.error.metadata = Tenants metadata must be configured!
multitenant.error.notenant = User ID does not contain tenant code!
multitenant.error.notcontain = User ID does not contain tenant token!
multitenant.error.contain = User ID contains > 1 tenant token!

service.global.emptyaudit = Audit cannot be null
service.global.emptyparam = {0} cannot be null
service.global.existed = {0} already exist in same application
service.global.notvalid = {0} not valid
service.global.notvalidnumeric = {0} must numeric
service.global.notvaliddate = {0} date format must {1}
service.global.notvalidyear = {0} minimum year is {1}
service.global.maxlength = {0} max length must < {1}
service.global.minlength = {0} min length must > {1}
service.global.lovnotvalid = LOV {0} not valid

msg.success.insert = Data inserted successfully
msg.success.update = Data updated successfully
msg.success.delete = Data deleted successfully

services.msg.success.regemail = We have sent an Activation Code email to your email.
services.msg.success.regsms = We have sent an Activation Code sms to your number.

businesslogic.notfound = Nothing found

businesslogic.global.action = {0} must be filled {1}.
businesslogic.global.activeDel = {0} must be filled 0 or 1.
businesslogic.global.androididnotvalid = Your device ID is not match. Please contact your administrator.
businesslogic.global.dataisexist = Data {0} is exist.
businesslogic.global.datanotexist =  Data {0} : {1} not exist.
businesslogic.global.datanotfound = {0} {1} not found
businesslogic.global.datanotfound1 = {0} not found
businesslogic.global.datachanged = Data changed, please re-run transaction
businesslogic.global.datahasachild = Delete cannot be done. Data has a child.
businesslogic.global.datetimeexpired = {0} required minimum today.
businesslogic.global.dateformat = {0} must use this {1} date format
businesslogic.global.entitynotfound = {0} does not exists
businesslogic.global.error = {0} Error.
businesslogic.global.errorduplicate = Error Duplicate {0}.
businesslogic.global.errorgenxld = Failed to generate XLS
businesslogic.global.noresponse = No response. Please retry.
businesslogic.global.invaliddatevalue = Renewal date must be later than expired date.

businesslogic.global.mandatory = {0} must be filled
businesslogic.global.mandatoryphoto = {0} is empty. Please check and re-take the picture.
businesslogic.global.cannotbechanged = {0} cannot be changed
businesslogic.global.minlength = {0} character length must be >= {1}
businesslogic.global.maxlength = {0} character length must be <= {1}
businesslogic.global.mustdecimal = {0} must decimal
businesslogic.global.mustinterval = {0} must be filled between {1} and {2}
businesslogic.global.mustunique = {0} must unique
businesslogic.global.numeric = {0} must number
businesslogic.global.dateformat = {0} must use {1} date format
businesslogic.global.paraminexist = Please provide required parameter : {0}
businesslogic.global.subsystem =  {0} must be filled MO, MS, or MC.
businesslogic.global.successrequestdownload = Request report has been process. Please check Report Result List menu
businesslogic.global.valuenotmatch = This value doesn`t match the data validation restrictions defined for this cell
businesslogic.global.invalidrole = You cannot access other user's data
businesslogic.global.noresponse = No response received. Please try again.
businesslogic.global.daterangefiltermustbefilled = Both start and end date filter must be filled
businesslogic.global.datecompareinvalid = {0} must not more than {1}.
businesslogic.global.stringmaxlength = {0} cannot exceeds {1} character(s)
businesslogic.global.stringminlength = {0} must be at least {1} character(s)
businesslogic.global.status = Status is not defined
businesslogic.global.otpalreadysend = OTP has been sent

businesslogic.global.decimal.digit = {0} cannot exceed {1} total digits (including before and after the decimal point)
businesslogic.global.decimal.point = {0} cannot have more than {1} digits after the decimal point
businesslogic.global.decimal.maxvalue = {0} cannot be equals or bigger than {1}

businesslogic.error.changeexception = Status of this data has been changed, please repeat transactions
businesslogic.error.haschild = Delete cannot be done. Data has a child.
businesslogic.error.imagelocationnotdefined = Download image detail task location has not been defined.
businesslogic.error.pdflocationnotdefined = Download pdf detail task location has not been defined.
businesslogic.error.errorparsedate = Parse date error
businesslogic.error.parsedateformat = Date format does not match, please use date format 'yyyy-MM-dd'
businesslogic.error.invaliddatevalue = the start date exceeds the end date

businesslogic.context.jobalreadydeleted = User's job already deactivated
businesslogic.context.subsystemalreadydeleted = User's subsystem already deactivated
businesslogic.context.unauthorizedaccess = Unauthorized Access.
businesslogic.context.useralreadydeleted = User already deactivated/deleted
businesslogic.context.useralreadylocked = User already locked, please press forgot password to create a new password
businesslogic.context.userinactive = Login failed. Your account is inactive.

businesslogic.usermanagement.branchnotfound = No branch found with code '{0}'
businesslogic.usermanagement.initialexist = Initial {0} already exist.
businesslogic.usermanagement.initialnamelength = Initial name character must < 7
businesslogic.usermanagement.jobnotfound = No job found with code '{0}'
businesslogic.usermanagement.loginexist = LoginID {0} already exist.
businesslogic.usermanagement.loginidexist = User with loginId {0} already exists
businesslogic.usermanagement.unauthorizedoperation = Unauthorized Operation.
businesslogic.usermanagement.usernotfound = No user found with LoginId '{0}'
businesslogic.usermanagement.phonealreadyexist = Phone number is already registered
businesslogic.usermanagement.rolenotexist = Tenant doesn't have a user management role.
businesslogic.usermanagement.rolenotisusermanagement = Role is not a user management.
businesslogic.usermanagement.nophoneisnotvalid = No phone is not valid.
businesslogic.usermanagement.idNoisnotvalid = Wrong id no digit.
businesslogic.usermanagement.emailisnotvalid = Email is not valid.
businesslogic.usermanagement.isusermanagementinvalid =  Role {0} not UserManagement.
businesslogic.usermanagement.usermanagementnotfound = User with Email / NIK {0} not found.
businesslogic.usermanagement.rolenotfound = Role Not Found.
businesslogic.usermanagement.rolecodenotfound = No role code found with role code {0}
businesslogic.usermanagement.datausermanagementnotfound = User with email {0} not found.
businesslogic.usermanagement.datauserempty = Email cannot be empty.
businesslogic.usermanagement.userisnotactive = User is not active user
businesslogic.usermanagement.calleridnotatenantofloginid = {0} is not tenant of {1}.
businesslogic.usermanagement.samenoktp = Id number entered is the same as the id number in the system.
businesslogic.usermanagement.rolecodenotavailableintenant = Role code {0} not available in tenant {1}
businesslogic.usermanagement.invalidtenantandcaller = callerId {0} not available in tenant {1}
businesslogic.usermanagement.invalidroleanduserandtenant = {0} does not have a role {1} with {2} tenant.
businesslogic.usermanagement.officeisnotexist = Office is not exist
businesslogic.usermanagement.loginidisnotexist = User is not exist.
businesslogic.usermanagement.idloginexist = Login Id already exist.

businesslogic.job.entitynotuniquejob = Job ID {0} already exist.
businesslogic.job.jobresultnotfound = Job Result not found.

businesslogic.branch.entitynotexistbranchcode = Branch Code {0} is not exist.
businesslogic.branch.entitynotuniquebranchcode = Branch Code {0} already exist.
businesslogic.error.generatexls = Error generating XLS

# ESG-957: Ubah istilah password -> access code
businesslogic.login.dormantstatus = Dormant status for username {0}. Please contact your Administrator.
businesslogic.login.dormantstatus1 = Your account is in dormant status. Please contact the company where you will make the transaction.
businesslogic.login.failed = Login failed. Username not found
businesslogic.login.idinexist = Login failed. Please check your input
businesslogic.login.idpassword = Login failed. Please check your Username and Access Code
businesslogic.login.inactive = Login failed. Username {0} is not active
businesslogic.login.inactiveadmin = Username {0} is inactive. Please contact your Administrator.
businesslogic.login.inactivejob = Username {0} is registered in inactive job. Please contact your Administrator.
businesslogic.login.inactivesubsystem = Login failed. Username {0} is registered in inactive subsystem. Please contact your Administrator.
businesslogic.login.invalididpassword = Invalid Username or Access Code
businesslogic.login.locked = Login failed. Username {0} is locked
businesslogic.login.lockedadmin = Username {0} is locked. Please contact your Administrator.
businesslogic.login.loggedin = Username {0} is already logged in.
businesslogic.login.notfieldperson = Login failed. Username {0} is not field person
businesslogic.login.relogin = Username {0} require re-login
businesslogic.login.takingdayoff = Cannot log in. User is taking day off.
businesslogic.login.userpasswordrequired = Username or Access Code is required

businesslogic.generalsetting.gsinexist = General Setting does not exist
businessLogic.generalsetting.gscodeempty = Gs Code cannot be empty.
businesslogic.generalsetting.gsinexistwithcode = General Setting with code {0} does not exist

# ESG-957: Ubah istilah password -> access code
businesslogic.changepassword.emptyoldnew = Old access code and new access code must not be empty
businesslogic.changepassword.history = The access code must has not been used in the previous {0} access codes
businesslogic.changepassword.incorrectold = Old access code is incorrect
businesslogic.changepassword.minchars = Please enter at least 8 characters
businesslogic.changepassword.success = Your access code has successfully changed
businesslogic.changepassword.nodoc = user has not received the document

businesslogic.document.errorsend = Error in sending document to {0} : {1}
businesslogic.document.stampdutynotenough = Stamp duty not enough
businesslogic.document.errorreadwritepdf = Error during reading or writing pdf
businesslogic.document.samefile = Sent file must be different
businesslogic.document.errorsign = Error in sign document : {0}, {1}
businesslogic.document.errorsignpagenumpage = Sign Page ({0}) cannot be greater than Number of Page ({1})
businesslogic.document.missingsignertype = Signer Type for Sign Type {0} Must be filled
businesslogic.document.usercannotaccessdoc = User with {0} role cannot access the document
businesslogic.document.usercannotaccessdoc1 = User cannot access the document
businesslogic.document.custdocumentinaccessible = Customer cannot access the document
businesslogic.document.bmdocumentinaccessible = BM cannot access the document
businesslogic.document.customernotallowed = Customer cannot do autosign
businesslogic.document.usermustbeactive = User must be activated before doing autosign
businesslogic.document.invaliddaterange = Maximum {0} date range is 30 days
businesslogic.document.invaliddaterangegeneralsetting = Maximum {0} date range is {1} days
businesslogic.document.cannotaccessothertenant = User cannot access other tenant's document
businesslogic.document.alreadycanceled = Document already canceled
businesslogic.document.illegalissignloc = isSignLoc must only contain 1 or 0
businesslogic.document.invalidinquirytype = Invalid inquiry type
businesslogic.document.invalidtenant = Cannot check for other tenant's document template
businesslogic.document.invalidrole = Only admin client can access this
businesslogic.document.invalidrolelegal = Only admin legal can access this
businesslogic.document.emptyemail = Email cannot be empty
businesslogic.document.signernikemailalreadyexist = This ID number {0} and email {1} has already been used by two different users. Please check request data.
businesslogic.document.signerphonenoemailalreadyexist = This phone number {0}  and email {1} has already been used by two different users. Please check request data
businesslogic.document.signerphonenonikalreadyexist = This phone number {0} and ID number {1} has already been used. Use a different phone number and ID number.
businesslogic.document.signerphonenoalreadyexist = This phone number {0} has already been used. Use a different phone number.
businesslogic.document.signernikalreadyexist = This ID number {0} has already been used. Use a different ID number.
businesslogic.document.mismatchemailandid = This ID number {0} does not belong to user with this email {1}.
businesslogic.document.mismatchemailandphone = This phone number {0} does not belong to user with this email {1}.
businesslogic.document.mismatchemailandphone1 = Email {0} is not registered with phone number {1}
businesslogic.document.documentnotsignedyet = Some documents have not been signed
businesslogic.document.alreadysignedall = All documents has been affixed with e-meterai
businesslogic.document.generateexcelerror = Error during generating excel
businesslogic.document.agreementnotfoundintenant = Agreement {0} is not found in tenant {1}
businesslogic.document.docnotfoundintenant = Document {0} is not found in tenant {1}
businesslogic.document.docnotfound = Document not Found for your email
businesslogic.document.sameemailinrequest = This email {0} is used by multiple signers in this request
businesslogic.document.samenikinrequest = This NIK {0} is used by multiple signers in this request
businesslogic.document.samephoneinrequest = This phone no. {0} is used by multiple signers in this request
businesslogic.document.unsigneddocumentempty = The document has been signed for the user {0}, please check document monitoring
businesslogic.document.inactiveagreement = Agreement {0} is inactive
businesslogic.document.inactiveagreement1 = {0} {1} is inactive
businesslogic.document.usednik = NIK {0} is using email {1}. Email {2} cannot be used.
businesslogic.document.emailmandatory = Email must be filled for NIK {0}
businesslogic.document.nikdoesnothaveemail = Please empty the email parameter for NIK {0} because it cannot use personal email
businesslogic.document.nikdoesnothaveemailext = Signer with NIK {0} is registered with a different email. Re-send request with the correct email or empty the email parameter because they're registered with system generated email.
businesslogic.document.certexpired = Your Electronic Certificate has expired. Please contact Admin for to send re-registration link.
businesslogic.document.alreadysigned = User has already signed the document
businesslogic.document.needonlineverif = Please verify your account on Vendor's website.
businesslogic.document.invalidperuridoctype = Invalid Peruri Document Type.
businesslogic.document.mustbase64 = Dokumen must be in Base64 format
businesslogic.document.templatedoesnothavesdt = Document template {0} does not have stamp location
businesslogic.document.agreementnotfound= Agreement {0} is not found
businesslogic.document.mandatorycannotbeempty = {0} cannot be empty
businesslogic.document.tenanttrxidexist = Document with Transaction ID {0} existed
businesslogic.document.templatemusthavesdt = Document Template doesn't have Stamp Duty
businesslogic.document.invaliddaterangerecon = Range maximum date {0} is 14 days
businesslogic.document.invalidphonenik = Telephone number registered with NIK {0}, different from the NIK requested {1}
businesslogic.document.invalidnikphone = NIK registered with telephone number {0}, different from the telephone number requested {1}
businesslogic.document.invalidnikemail = NIK has been registered with the email {0}, different from the email requested {1}
businesslogic.document.invalidemailphone = Email registered with telephone number {0}, different from the telephone number requested {1}
businesslogic.document.invalidemailnik = Email registered with NIK {0}, different from the NIK requested, namely {1}
businesslogic.document.emptydocumentid = Document Id cannot be empty
businesslogic.document.documentnotfound = Document with document Id {0} not found
businesslogic.document.documentidnotfound = Document with ID {0} not found
businesslogic.document.documentnotfoundintenant = Document with document ID {0} is not found in tenant {1}
businesslogic.document.usernotsignerofdocument = {0} is not the signer of this document
businesslogic.document.tenantcannotaccessdoc = Document cannot be accessed
businesslogic.document.unhandleddocumentvendor = Vendor {0} is not mapped to view document
businesslogic.document.autosignfailed = Autosign failed, please re-send your document
businesslogic.document.ipaddressempty = IP Address Can not be empty or null
businesslogic.document.browserempty = Browser can not be empty or null
businesslogic.document.invalidvendorsign = Only Document With Vendor  {0} can be requested
businesslogic.document.requestsignconfirmationduplicate = Your request with document ID {0} is already requested or in progress
businesslogic.document.documentinsignprogress = Document with ID {0} is in the signature process
businesslogic.document.signerinvalid = User {0} not signer from document {1}
businesslogic.document.requestsignconfirmationdone = your request with document ID  {0}  is already done.
businesslogic.document.documentfinishedsign = Document with Id {0} has been signed.
businesslogic.document.usercannotdoautosign = User {0} cannot do autosign. Please resend you document accordingly.
businesslogic.document.documentisbeingsigned = Document with ID {0} is in the process of being signed
businesslogic.document.documentwithidnotfound = Document with ID {0} not found
businesslogic.document.contractisinstampingprocess = Contract {0} is in the stamping proccess
businesslogic.document.invalidrefno = Ref number {0} not found
businesslogic.document.verificationcannotbeempty = Sign document Verification cannot be empty ! Please input either OTP or self photo.
businesslogic.document.referencenoempty = Ref Number cannot be empty
businesslogic.document.contractalreadysignedandstamped = Contract {0} has been signed and stamped
businesslogic.document.invalidrefno = Ref number {0} not found
businesslogic.document.verificationcannotbeempty = Sign document Verification cannot be empty ! Please input either OTP or self photo.
businesslogic.document.contractalreadysigned = Contract {0} is already signed
businesslogic.document.contractisinsigningprocess = Contract {0} is in the signing proccess
businesslogic.document.userisnotthesignerofthecontract = User is not the signer of contract {0}
businesslogic.document.docnotfounds = Document not found
businesslogic.document.alldocumentalreadysigned = All documents have been signed by user
businesslogic.document.mismatchedrefno = All document must have the same Ref Number
businesslogic.document.docfileinvalid = Base64 String in docFile is invalid
businesslogic.document.doctempcodeorsignlocmustbefilled = Document Template Code or Sign Location must be filled
businesslogic.document.stampsignincomplete = Please make sure sign locations and stamp locations have page and coordinates information
businesslogic.document.agreementnotfoundintenant2 = Document with {0}{1} is not found in tenant {2}
businesslogic.document.contractalreadysigned2 = Document with {0}{1} is already signed
businesslogic.document.contractisinsigningprocess2 = Document with {0}{1} is in the signing proccess
businesslogic.document.userisnotthesignerofthecontract2 = User is not the signer of document with {0}{1}
businesslogic.document.contractalreadysignedandstamped2 = Document with {0}{1} has been signed and stamped
businesslogic.document.contractisinstampingprocess2 = Document with {0}{1} is in the stamping proccess
businesslogic.document.invalidsignaction = Sign Action must be 'at' or 'mt'
businesslogic.document.invalidsignertype = Signer Type {0} is invalid
businesslogic.document.mustsendsigner = Signer Type {0} data must be sent for document template {1}
businesslogic.document.checkautosigndata = Please make sure all data required for autosign are correct
businesslogic.document.documentnostamp = Document with {0} {1} do not require stamp duty
businesslogic.document.signernotactivated = You have not registered to {0}
businesslogic.document.mustfirstlysignbyother = Document must firstly be signed by {0}
businesslogic.document.cannotbesequential = Document template with code {0} have not yet been set up for sequential signing
businesslogic.document.cannotautosignwithvendor = Autosign is not available with {0}
businesslogic.document.cannotautosignwhensequential =  Autosign is not allowed with sequential document
businesslogic.document.cannotautosignwithouttemplate = Autosign must be done with document template
businesslogic.document.maxsignexceeded = Each signer can only sign 1 time per document with {0}
businesslogic.document.mustotp = Vendor {0} must use OTP verification
businesslogic.document.seqnomustbeunique = Signing Sequence Number must be unique
businesslogic.document.seqmustbefilled = Sequence Number must be filled for Sequential Document
businesslogic.document.documentnameempty = Document Name cannot be empty
businesslogic.document.docfileempty = Document file cannot be empty
businesslogic.document.stampinglocationempty = Documents must have at least 1 stamp
businesslogic.document.peruridoctypeinvalid = Invalid peruri document type
businesslogic.document.documentvendornotmatch = Unable to check documents with different vendors
businesslogic.document.verificationotpcannotbeempty = Sign document Verification cannot be empty ! Please input OTP.
businesslogic.document.cannotcancelalreadysigned = {0} already fully signed. Sign(s) cannot be cancelled.
businesslogic.document.doctekenordigi = Please take the signature link to continue signing.
businesslogic.document.psrenootp = PSrE for Contract {0} does not need to request OTP through this method.
businesslogic.document.documentdoesnotbelongtorefnumber =  Document does not belong to the given reference number
businesslogic.document.cannotsignwithvendor = Documents with vendor {0} cannot yet be signed.
businesslogic.document.documentsignvendornotmatch = Cannot sign with different vendors.
businesslogic.document.documentotpnotsame = There is no OTP request for the document to be signed. Please request an OTP first.
businesslogic.document.documentsignlinkwithvendornotready = Document with vendor {0} cannot yet to be request for sign link, you can directly request with Sign Document
businesslogic.document.documentisnotvalidatetostamp = Document are not qualified to stamp
businesslogic.document.certnotfound = Electronic certificate not found.
businesslogic.document.highestpriortyunsigned = There is a more urgent document that needs to be signed first.
businesslogic.document.branchnotfound = Branch not exist.
businesslogic.document.customsignnotexist = Sign image for NIK {0} not exist. Please upload the sign image first.
businesslogic.document.currentlyprocessed = Document is currently being processed
businesslogic.document.vendorregistrationidempty = Vendor registration id for signer {0} is empty.
businesslogic.document.notactive = Signer {0} is not active
businesslogic.document.certdate.notfound = Certificate expiry date for signer {0} not found.
businesslogic.document.poacert.incomplete = POA certificate for signer {0} is incomplete or missing.
businesslogic.document.poacert.expired = POA certificate for signer {0} has expired.

//RefNo
businesslogic.document.invalidrefnumberlength = Reference Number length maximum 50.
businesslogic.vendor.vendornotsupportdownloadcert = Unable to download certificate for PSrE {0}.
businesslogic.vendor.emptypsrelist = Empty PsrE list;

businesslogic.insertstamping.var.stampduty 		= Stamp Duty
businesslogic.insertstamping.var.docno			= Document Number
businesslogic.insertstamping.var.docname 		= Document Name
businesslogic.insertstamping.var.docdate 		= Document Date
businesslogic.insertstamping.var.doctype 		= Document Type
businesslogic.insertstamping.var.doctypeperuri	= Peruri Document Type
businesslogic.insertstamping.var.doctemplate	= Document Template
businesslogic.insertstamping.var.file			= Document File
businesslogic.insertstamping.var.docnominal		= Document Amount
businesslogic.insertstamping.var.regionCode		= Region Code
businesslogic.insertstamping.var.regionName		= Region Name
businesslogic.insertstamping.var.officeCode		= Office Code
businesslogic.insertstamping.var.officeName		= Office Name
businesslogic.insertstamping.var.userEmail		= User Email
businesslogic.insertstamping.var.stampLoc		= Stamping Location and/or Elements of Stamping location
businesslogic.insertstamping.var.docnumber		= Document Number
businesslogic.insertstamping.var.trxid			= Document Transaction ID
businesslogic.insertstamping.var.idno			= Tax Owed's ID Number
businesslogic.insertstamping.var.idtype			= Tax Owed's ID Type
businesslogic.insertstamping.var.name			= Tax Owed's Name
businesslogic.insertstamping.var.taxtype		= Tax Type
businesslogic.insertstamping.var.returnresult	= Return Stamped Result Flag
businesslogic.insertstamping.invaliddocnominal	= Document Nominal can only consist of numbers
businesslogic.insertstamping.invalididtype		= ID Type {0} does not exist
businesslogic.insertstamping.doctemplateorloc	= Please provide Stamping Location or a Valid Document Template Code
businesslogic.insertstamping.pemungutnotavailable	= Tax Type Pemungut not available, please change Tax Type to Non Pemungut

businesslogic.insertmanualsign.var.refno	 				= Reference Number
businesslogic.insertmanualsign.var.paymenttype 				= Payment Type
businesslogic.insertmanualsign.var.automaticstamp 			= Automatic Stamp Duty Stamping
businesslogic.insertmanualsign.var.signername 				= Signer Name with Phone {0}
businesslogic.insertmanualsign.var.signerphone 				= Signer Phone with Name {0}
businesslogic.insertmanualsign.var.signerphonenamewithemail	= Signer Name and Phone with Email {0}
businesslogic.insertmanualsign.var.signerphonename			= Signer Name and Phone
businesslogic.insertmanualsign.var.signerempty				= Document must have at least 1 signer
businesslogic.insertmanualsign.signerberegistered			= Signer {0} is unregistered
businesslogic.insertmanualsign.signermustsign				= Signer {0} Must Sign at least once
businesslogic.insertmanualsign.signermustberegisteredtopsre	= Signer {0} is unregistered in PSRE {1}
businesslogic.insertmanualsign.signeremailmismatch			= Signer {0} must provide Esignhub and PSRE registered email address
businesslogic.insertmanualsign.signerphonemismatch			= Signer {0} must provide Esignhub and PSRE registered phone number
businesslogic.insertmanualsign.mustuseqrwithvendor			= QR must be used for {0}

businesslogic.email.reademailerror = Error occurred when reading email
businesslogic.email.deleteemailerror = Error occurred when deleting email

businesslogic.emailsender.sendemailerror = Error occurred when sending email

businesslogic.paymentsigntype.emptytenantcode = Tenant code cannot be empty
businesslogic.paymentsigntype.tenantnotfound = Tenant not found
businesslogic.paymentsigntype.invalidrole = Only admin client {0} can access this

businesslogic.report.errordata = Error while get data
businesslogic.report.excelresult = Excel result location has not been defined
businesslogic.report.nocsv = No CSV file found

businesslogic.stampduty.errorparsingfee = Error while parsing stamp duty fee
businesslogic.stampduty.errorparsingqty = Error while parsing stamp duty qty
businesslogic.stampduty.usercannotcreate = Current user cannot create stamp duty
businesslogic.stampduty.invalidvendortenant = Tenant {0} does not have {1} as a vendor
businesslogic.stampduty.vendorcannotcreate = Vendor {0} cannot create stamp duty
businesslogic.stampduty.emptyinvoiceno = Invoice no cannot be empty
businesslogic.stampduty.noinvoiceno = Invoice no not found
businesslogic.stampduty.vendorcannotdelete = Vendor {0} cannot delete stamp duty

businesslogic.stampduty.flaggedforautomaticstamp = Document will automatically be stamped when it has been fully signed
businesslogic.stampduty.needsignbeforestamp = Document need to be fully signed before stamp
businesslogic.stampduty.onstampingprocess = Document is currently being processed for stamping
businesslogic.stampduty.doesnotneedstamp = Document does not need stamp
businesslogic.stampduty.cannotretrystamp = This document cannot be attempted stamping

businesslogic.emeterai.emptylogincredential = Empty login credential
businesslogic.emeterai.failedtogetossdocument = Failed to get document {0} from OSS
businesslogic.emeterai.cannotretry = Document stamping cannot be retried. Document stamping status: {0}
businesslogic.emeterai.cannotretryfromupload = Document stamping cannot be retried from upload
businesslogic.emeterai.startretryfromupload = Retry stamping from upload started
businesslogic.emeterai.stampfailed = Stamping failed. Please retry later

businesslogic.document.archived.restoring = Document {0} is archived and is being restored. Please try again in a few minutes.
businesslogic.document.restoring = Document {0} is being restored. Please try again in a few minutes.
businesslogic.document.archived.restore.error = An error occurred while restoring document {0}: {1}. Please try again later.
businesslogic.document.archived.check.error = An error occurred while checking the restore status of document {0}: {1}. Please try again later.
businesslogic.document.archived.not.signed.or.stamped = Document {0} must be signed or stamped before it can be restored.

businesslogic.emeterai.onprem.filecheckfailed = No stamp result document yet

# ESG-957: Ubah istilah password -> access code
AbstractUserDetailsAuthenticationProvider.badCredentials=Login Failed. Please Check Your Username and Access Code

# ESG-957: Ubah istilah password -> access code
businesslogic.user.updateavatar = Update avatar user success
businesslogic.user.usernotfound = User not found
businesslogic.user.invalidparam = Invalid parameter : {0}
businesslogic.user.erroractivation = Error in user activation :{0}
businesslogic.user.invalidloginid = callerId and loginId must be equals
businesslogic.user.inactiveuser = User is inactive or does not exist
businesslogic.user.invalidnewpassword = New access code must contain uppercase letter, lowercase letter, number and special characters
businesslogic.user.minlengthpassword = Access code must be atleast 8 characters
businesslogic.user.passwordhasbeenused = New access code has been used
businesslogic.user.incorrectresetcode = Incorrect reset code
businesslogic.user.invalidtenant = Can't access other tenant's users
businesslogic.user.invalidrole = Users can only by accessed by Admin
businesslogic.user.validrole = Only {0} role(s) can access this
businesslogic.user.alreadyregistered = User is already registered
businesslogic.user.invalidotpcode = Wrong OTP Code
businesslogic.user.expiredotpcode = Your OTP code expired
businesslogic.user.personaldatanotfound = User personal data not found
businesslogic.user.nodoctosign = There are no document to sign
businesslogic.user.errorinsertuser = Error during inserting new user
businesslogic.user.incorrectresetpasswordlinkcode = Incorrect access code OTP
businesslogic.user.invalidniklength = NIK length must be 16
businesslogic.user.invalidnpwplength = NPWP length must be 15-16
businesslogic.user.maxresetpasswordreached = Cannot reset access code again for today
businesslogic.user.phonealreadyregistered = Phone {0} already registered
businesslogic.user.phonealreadyregistered1 = Phone {0} already registered with email {1}
businesslogic.user.emailalreadyregistered = Email {0} already registered
businesslogic.user.cannotsentemail = Must Send OTP by SMS to user {0}
businesslogic.user.verifynotmatch = Does not match the initial verification
businesslogic.user.invalidphone = Invalid Phone No. : {0}
businesslogic.user.invalidemail = Invalid Email : {0}
businesslogic.user.nikalreadyregistered = NIK {0} is already registered with email {1}
businesslogic.user.nikalreadyregistered1 = NIK {0} is already registered
businesslogic.user.pleaseregister = You already received a signature request. Please register via the sent signature request link.
businesslogic.user.continuesign = User is already registered and activated. Please continue to sign via the sent signature request link.
businesslogic.user.doubleemail = This email {0} is requested for multiple users
businesslogic.user.doublephone = This phone no. {0} is requested for multiple users
businesslogic.user.doublenik = Id no {0} is requested for multiple users
businesslogic.user.invalidgender = Invalid gender code
businesslogic.user.usertenantnotfound = User {0} is not found in tenant {1}
businesslogic.user.invalidupdatecode = Invalid update code
businesslogic.user.userhaspendingdocument = You have unsigned document. Please login.
businesslogic.user.alreadyactivated = User is already registered and activated. Please wait for signature request link.
businesslogic.user.useremailhasdocument = User with email {0} already has document to sign
businesslogic.user.userphonehasdocument = User with phone number {0} already has document to sign
businesslogic.user.noupdate = There is no data to update on user.
businesslogic.user.cannotupdate = Cannot update User Data because User has been registered and activated.
businesslogic.user.cannotupdateemail = Cannot update user's email because current email was generated by system.
businesslogic.user.usednikphone = NIK and phone number is already used in email {0}
businesslogic.user.usednik = NIK is already used in email {0}
businesslogic.user.userdataused = Data {0} sudah ada, silahkan menunggu / menggunakan link tanda tangan
businesslogic.user.phonenotregistered = Signer with Signer Type {0} and Phone number {1} is not registered on eSignHub
businesslogic.user.emailnotregistered = Signer with Signer Type {0} and Email {1} is not registered on eSignHub
businesslogic.user.niknotregistered = Signer with Signer Type {0} and NIK {1} is not registered on eSignHub
businesslogic.user.phonenotactivated = Signer with Signer Type {0} and Phone number {1} is not activated on eSignHub
businesslogic.user.emailnotactivated = Signer with Signer Type {0} and Email {1} is not activated on eSignHub
businesslogic.user.niknotactivated = Signer with Signer Type {0} and NIK {1} is not activated on eSignHub
businesslogic.user.emailnotfound = Email {0} is not found on eSignHub
businesslogic.user.notregistered=User Not Registered
businesslogic.user.nikusedinemail = Email {0} cannot be used for NIK {1} because the NIK has been registered with other email
businesslogic.user.nikusedinphone = Phone {0} cannot be used for NIK {1} because the NIK has been registered with other phone
businesslogic.user.phoneusedbyother = Phone {0} has been used by other user
businesslogic.user.emailuserbyother = Email {0} has been used by other user
businesslogic.user.vendorcannotbeempty = Vendor Code cannot be empty.
businesslogic.user.invlinknotexist = Invitation Link Not Exist.
businesslogic.user.fillatleastone = Email or Phone Number must be filled.
businesslogic.user.errorchangedigidata = Error Change Data Digisign: request forbidden by administrative rules.
businesslogic.user.canreregister = You can re-register for {0}
businesslogic.user.signernotallowed = Signer with Signer Type {0} and Phone number {1} is not allowed
businesslogic.user.tenantwithcalleridnotfound = {0} is not ADINS tenant {1}
businesslogic.user.calleridisnotadmclient = {0} is not a admin client
businesslogic.user.idnoalreadyregistered = No Id {0} is already registered to another user
businesslogic.user.invalidpasswordcomplexity = Access code must contain uppercase letter, lowercase letter, number and special characters
businesslogic.user.usernotregisteredinvendor = User is not registered in that vendor
businesslogic.user.usernotfoundwiththatemail = User with email {0} is not found
businesslogic.user.signeremailempty = Signer email cannot be empty
businesslogic.user.vendorcodeempty = Vendor code cannot be empty
businesslogic.user.vendornameempty = Vendor Name cannot be empty
businesslogic.user.passwordnotmatch = Password do not match
businesslogic.user.passwordcannotbeemtpy = Password cannot be empty
businesslogic.user.maxotpactivationuserreached = Cannot send activation user OTP again for today
businesslogic.user.userhasnotregistered = User has not registered
businesslogic.user.useralreadyactivated = User is already activated
businesslogic.user.phonenoisempty = Phone number is empty
businesslogic.user.usernotfoundwiththatphoneno = User with phone number : {0} is not found
businesslogic.user.phonenotmatchwithinvitation = Phone number does not match the invitation data
businesslogic.user.maxotpsigningverificationreached = Cannot send signing verification OTP again for today
businesslogic.user.userhasnotactivated = User has not activated
businesslogic.user.usernotregisteredintenant = User is not registered in that tenant
businesslogic.user.usernotsigner = user {0} is not a signer
businesslogic.user.userdoesnothaveanydocument = User does not have any documents yet
businesslogic.user.maxlivenessfacecomparereached = Liveness Face Compare attempt has exceeded the daily limit
businesslogic.user.nikisnotnumber = NIK must be a number
businesslogic.user.alreadyregisteredinesignhub = You have already registered in esignhub
businesslogic.user.usernotfoundwiththatid = User with id number {0} is not found
businesslogic.user.invalidemailformat = Email format <NAME_EMAIL>
businesslogic.user.invalidphonenoformat = Invalid phone number format
businesslogic.user.invaliddateformat = Date must use format {0}
businesslogic.user.usernamenotmatch = User name not match
businesslogic.user.phonenocannotbeempty = Phone number cannot be empty
businesslogic.user.emailcannotbeempty = Email cannot be empty
businesslogic.user.userwithphoneandemailnotfound = User with phone number {0} and email {1} not found in the system
businesslogic.user.nikregistered = NIK already exist.
businesslogic.user.userwithemailnotregisteredwithphone = User with email {0} not registered on this phone number {1}
businesslogic.user.userwithphonenotregisteredwithemail = User with phone number {0} not registered on this email {1}
businesslogic.user.userwithemailhasnotactivated = User with email {0} has not activated
businesslogic.user.updatesuccess = User data updated successfully
businesslogic.user.invalidusertype = User Type {0} invalid
businesslogic.user.facenotdetected = Face not detected. Make sure your face is clearly visible
businesslogic.user.morethanonefacedetected = More than one face is detected. Make sure only one face is visible
businesslogic.user.faceoutofposition = The detected face is outside the frame. Make sure your face is properly in the frame
businesslogic.user.facetooclose = The face is detected too close to the camera. Keep some distance between your face and the camera
businesslogic.user.facetoofar = The face is detected too far with the camera. Bring your face closer to the camera
businesslogic.user.eyeglassesdetected = Glasses detected. Please take off your glasses and make sure your face is visible without them
businesslogic.user.facetoobright = Face is too bright. Ensure adequate lighting conditions and avoid excessive light on your face
businesslogic.user.backlightdetected = Backlight detected. Retake the photo with no lights behind.
businesslogic.user.facetoodark = Face is too dark. Make sure the lighting conditions are bright enough so that your face can be seen clearly
businesslogic.user.maskingfailed = The detected face is outside the frame. Make sure your face is properly in the frame
businesslogic.user.dontneedactivate = User do not need to activate again for vendor {0}.
businesslogic.user.requestotp = User must request an OTP first.
businesslogic.user.notactivated = User {0} needs to activate their account.
businesslogic.user.urlforwardercodeinvalid = Invalid link. Please ensure that the link you open is complete according to the data received.
businesslogic.user.urlforwarderexpired = Link has expired.
businesslogic.user.invitationnotsentviawa = Re-sending invitation by WhatsApp failed.
businesslogic.user.resetcodenotrequested = Please ask user to request for Reset Code.
businesslogic.user.otpsendingmedianotvalid = Invalid OTP Delivery Media.
businesslogic.user.userregisterwithoutemail = Users register without email, please send OTP via SMS or Whatsapp.
businesslogic.user.usercertificateactivestatusexpired = Users certificate electronic for PSrE {0} has expired, please re-register.
businesslogic.user.notifsendingmedianotvalid = Invalid notification Delivery Media.
businesslogic.user.notifsendingmediaandnotifgatewaynotvalid = Invalid notification Delivery Media dan notification gateway.


businesslogic.userval.emailusedbymany = Email {0} have been used by {1} different users
businesslogic.userval.phoneusedbymany = Phone {0} have been used by {1} different users
businesslogic.userval.emailnotfound = Email {0} is not found in the system
businesslogic.userval.phonenotfound = Phone {0} is not found in the system
businesslogic.userval.niknotfound = NIK {0} is not found in the system
businesslogic.userval.phoneandemailusedbymany = Phone number {0} and email {1} have been used by {2} differend users
businesslogic.userval.phoneandemailnotfound = Phone number {0} and email {1} not found in the system

businesslogic.inquiry.downloadPdfZip = Request download detail task has been process. Please check Report Result List menu
businesslogic.inquiry.noData = There is no data to download!

businesslogic.feedback.invalidvalue = feedback value must be between 1 and 5
businesslogic.feedback.invaliddocid = Document with Document ID {0} does not exist

businesslogic.saldo.invalidtenant = Cannot request other tenant's balance
businesslogic.saldo.invalidrole = You have to be an Admin.
businesslogic.saldo.tenantnotexist = Tenant with code {0} does not exist
businesslogic.saldo.vendornotexist = Vendor with code {0} does not exist
businesslogic.saldo.topupdatenotexist = Top Up date with date {0} does not exist
businesslogic.saldo.cannottopup = Cannot top up for balance type SDT
businesslogic.saldo.vendortypeinvalid = Vendor with Vendor Type E-Materai cannot top up
businesslogic.saldo.pleasechoosebalancetype = Please choose a balance type first
businesslogic.saldo.balancetypealreadyexist = Balance Type with code {0} already exist
businesslogic.saldo.balancetypenotfound = Balance Type with code {0} not found
businesslogic.saldo.balancemutationnotfound = Balance Mutation with id {0} not found
businesslogic.saldo.idbalancemutationinvalid = Id Balance Mutation invalid
businesslogic.saldo.balancenotenough = Balance {0} not enough
businesslogic.saldo.balancesarenotenough = Balance {0} dan Balance {1} not enough
businesslogic.saldo.balancenotconfigured = {0} balance is not configured for {1} yet
businesslogic.saldo.successupdate = Balance is updated successfully

businesslogic.tenant.invalidtenant = Cannot request other tenant's
businesslogic.tenant.faceverifyserviceinactive = Face Verify for this tenant is inactive
businesslogic.tenant.faceverifyserviceerrorselfiespoof = We haven't been able to detect your face yet, please try again with better lighting
businesslogic.tenant.invalidthresholdbalance = Threshold balance must be > 0
businesslogic.tenant.apikeyvalidationerror = Error validating tenant x-api-key
businesslogic.tenant.apikeyinvalidforagreement = Agreement's tenant is different than x-api-key's tenant
businesslogic.tenant.apikeyinvalidfordocument = Document's tenant is different than x-api-key's tenant
businesslogic.tenant.tenantnotfound = Tenant {0} is not found on eSignHub
businesslogic.tenant.tenantcodeempty = Tenant code cannot be empty
businesslogic.tenant.tenantlivenessfacecompareservicesnotactive = Liveness face compare service for tenant {0} is not active
businesslogic.tenant.tenantwithtenantcodenotregistered = Tenant with tenant code {0} is not registered on eSignHub
businesslogic.tenant.incorrectapikey = Incorrect API Key
businesslogic.tenant.invalidcallbackurl = Invalid Callback URL. Must starts with https:// or http://
businesslogic.tenant.invaliduploadurl = Invalid Upload URL. Must starts with https:// or http://
businesslogic.tenant.invalidusewamessage = Value Use WA Message must be 0 or 1
businesslogic.tenant.invalidredirectactivationurl = Invalid Activation Redirect URL. Must starts with https:// or http://
businesslogic.tenant.invalidredirectsigningurl = Invalid Signing Redirect URL. Must starts with https:// or http://
businesslogic.tenant.msgtemplatewithotpactiveduration = Message Template not found for OTP active duration : {0}
businessLogic.tenant.invalidregion = Region with code {0} does not exist
businessLogic.tenant.invalidbusinessline = Business Line with code {0} does not exists

businesslogic.vendor.emptyvendortypecode = Vendor type code cannot be empty
businesslogic.vendor.invalidnotificationgateway = Invalid Notification Gateway code
businesslogic.vendor.emptytenantcode = Tenant code cannot be empty
businesslogic.vendor.invalidtenantcode = Invalid tenant code
businesslogic.vendor.usertenantnotfound = User {0} cannot be found in tenant {1}
businesslogic.vendor.ematerainotallowed	= Cannot request for Vendors with Vendor Type 'E-Materai'
businesslogic.vendor.invalidtenant = Cannot request for other tenant's Vendor Status
businesslogic.vendor.invalidvendortype = Vendor type {0} is invalid
businesslogic.vendor.defaultvendornotfound = Default vendor for tenant {0} is not found
businesslogic.vendor.registeredusernotfound=Vendor Registered User Not Found
businesslogic.vendor.vendorcodeinvalid = Vendor code {0} is invalid
businesslogic.vendor.vendorcannotresendactlink = Vendor code {0} cannot resend activation link
businesslogic.vendor.vendornotexistoractiveintenant = Psre {0} not exist or active psre in tenant {1}.
businesslogic.vendor.vendornotactive = Psre {0} is not active.
businesslogic.vendor.vendornotoperating = Psre {0} is not operating.
businesslogic.vendor.vendorcodeinvlink  = Vendor {0} is different with vendor invitation link
businesslogic.vendor.unhandledvendorcode = Unhandled vendor code: {0}
businesslogic.vendor.cannotupdatevendor = Data user vendor cannot be update
businesslogic.vendor.mvrunull = user data with vendor {0} cannot be found
businesslogic.vendor.cannotsendphone = vendor {0} cannot send OTP to phone number
businesslogic.vendor.balancvendoroftenantnotfound = Balance {0} from {1} for Tenant {2} not found
businesslogic.vendor.registereduserv2 = User {0} is registered in PSrE {1}
businesslogic.vendor.vendorregisteredusernotfound = User not found in Vendor Registered User
businesslogic.vendor.vendorcodepayment  = Vendor {0} is different from vendor DocumentId
businesslogic.vendor.unhandledregistrationvendor = Unable to register to {0}
businesslogic.vendor.defaultvendornull = Vendor {0} is not default vendor in tenant {1}
businesslogic.vendor.defaultvendormustunique = Vendor default order must be unique
businesslogic.vendor.vendornotsupporthashsign = Hash Sign is not available for PSrE {0}

businesslogic.email.couldnotconnecttostore = Could not connect to the message store

businesslogic.sms.sendsmserror = Error during sending SMS

businesslogic.liveness.emptyphotobase64 = PhotoBase64 cannot be empty
businesslogic.liveness.emptyselfiebase64 = Self Photo cannot be empty
businesslogic.liveness.emptyidphotobase64 = Id Photo cannot be empty
businesslogic.liveness.emptyselfiedbbase64 = Self Photo from Database is empty

businesslogic.embedmsg.invalidmsg = Invalid message
businesslogic.embedmsg.emptymsg = Empty message
businesslogic.embedmsg.invalidencryptedobject = Invalid encrypted data
businesslogic.embedmsg.datanotfoundinencryptedobject = {0} not found in encrypted data
businesslogic.embedmsg.invalidencryptedocument = Invalid encrypted document id
businesslogic.embedmsg.sessionexpired = The page you are viewing has expired. To continue using our services, please reopen the menu.

businesslogic.invitationlink.phonenull = Phone number must be filled
businesslogic.invitationlink.processonlyforinvitationbyemail = {0} cancelled, only for invitation by Email
businesslogic.invitationlink.invitationlinknotexist = Invitation Link with code {0} does not exist
businesslogic.invitationlink.cannotsendotptoemail = Cannot send OTP to {0}
businesslogic.invitationlink.cannotverifyotptoemail = Cannot verify OTP to {0}
businesslogic.invitationlink.invalidphoneno = Phone Number {0} invalid. make sure to put a valid phone number.
businesslogic.invitationlink.invalidemail = Email {0} invalid. make sure to put a valid email.
businesslogic.invitationlink.inactivelink = Inactive invitation link.
businesslogic.invitationlink.invalidlink = Invalid registration link.
businesslogic.invitationlink.useralreadyregistered = User {0} already registered. Please wait for the signature request link.
businesslogic.invitationlink.invalidinvitationby = Invalid invitation method
businesslogic.invitationlink.invitationsent = Invitation sent to {0}
businesslogic.invitationlink.invitationdatainvalid = Invalid recipient data
businesslogic.invitationlink.invalidreceiverdetail = You have to fill receiver detail with {0}
businesslogic.invitationlink.invalidexcel = You have no excel report data
businesslogic.invitationlink.invitationlinknotexistwithdata = No Invitation Link matches provided data.
businesslogic.invitationlink.existingphone = This phone number {0} has already been used by an invitation link with a different Id No.
businesslogic.invitationlink.existingemail = This email {0} has already been used by an invitation link a different Id No.
businesslogic.invitationlink.existingidno = This ID number {0} has already been registered
businesslogic.invitationlink.mismatchreceiverdetail = {0} must contain the {0} on Receiver Detail
businesslogic.invitationlink.phoneusedinemail = Phone number {0} has been used for email {1}
businesslogic.invitationlink.emailusedinnik = Email {0} has been used for NIK {1}
businesslogic.invitationlink.emailnikusedinphone = Email {0} and NIK {1} have been used for phone number {2}
businesslogic.invitationlink.emailphoneusedinnik = Email {0} and phone number {1} have been used for NIK {2}
businesslogic.invitationlink.decrypterror = Failed to validate data. Please re-open the invitation link from SMS / Email.
businesslogic.invitationlink.cannotEditUser = User data registered to vendor {0} cannot be updated.
businesslogic.invitationlink.existingphoneotherlink = phone number {0} has already been used by other invitation link
businesslogic.invitationlink.existingemailotherlink = email {0} has already bean used by other invitation link
businesslogic.invitationlink.existingidnootherlink = ID number {0} has already been used by other invitation link
businesslogic.invitationlink.registeredtomainvendor = User {0} has been registered to main vendor. Please use registered account.
businesslogic.invitationlink.invitationexpired = Your registration invitation has expired, please re-create the registration invitation to continue the registration process
businesslogic.invitationlink.cannotregenerate = Cannot regenerate invitation. User is already registered.
businesslogic.invitationlink.cannotregeneratelinkexisted = Cannot regenerate invitation. Registration link with {0} is already existed.
businesslogic.invitationlink.cannotregenerateforvendor = Cannot regenerate invitation with {0}.
businesslogic.invitationlink.cannotuseemailforinvbysms = Cannot use email for Invitations by SMS
businesslogic.invitationlink.failedtoresend = Invitation link failed to re-send
businesslogic.invitationlink.phoneoremailmustbefilled = Phone Number or Email must be filled
businesslogic.invitationlink.genonlyforvendor = Invitation Link can only be generated for {0} (PSrE)
businesslogic.invitationlink.activeecert = {0}'s Electronic Ceritificate is still active
businesslogic.invitationlink.errorhappened = An Error has happened within the application. Please check on your request data.
businesslogic.invitationlink.currentlyprocessed = Invitation is currently being processed.

businesslogic.register.alreadyregistered = You are already registered at eSignHub
businesslogic.register.nikusedbyotherphone = NIK has been used by a different phone number than the data sent
businesslogic.register.nikusedbyotheremail = NIK has been used by a different email than the data sent
businesslogic.register.nikusedbyotherphoneemail = NIK has been used by a different phone number and email than the data sent
businesslogic.register.phoneusedbyothernik = Phone number has been used by a different NIK than the data sent
businesslogic.register.phoneusedbyothernikemail = Phone number has been used by a different NIK and email than the data sent
businesslogic.register.emailusedbyothernik = Email has been used by a different NIK than the data sent
businesslogic.register.emailusedbyothernikphone = Email has been used by a different NIK and phone number than the data sent
businesslogic.register.phoneemailusedbyothernik = Phone number and Email has been used by a different NIK than the data sent
businesslogic.register.phonenikusedbyotheremail = Phone number and NIK has been used by a different Email than the data sent
businesslogic.register.emailnikusedbyotherphone = Email and NIK has been used by a different Phone number than the data sent
businesslogic.register.phonenotbelongtonik = Phone does not belong to NIK {0}
businesslogic.register.emailnotbelongtonik = Email does not belong to NIK {0}
businesslogic.register.phoneemailnotbelongtonik = Phone and email do not belong to NIK {0}
businesslogic.register.verificationinprogress = Your registration is in the process of verification
businesslogic.register.maxattemptsreached = You've hit the maximum daily registration retries ({0} times). Please try again tomorrow.

businesslogic.digisign.registered = You are already registered to Digisign. Please wait for the signature request link.
businesslogic.digisign.registerednotactivated = You are already registered. Please continue to the activation process.
businesslogic.digisign.errorprocessingselfie = Error when processing selfie photo
businesslogic.digisign.errorprocessingktp = Error when processing KTP photo
businesslogic.digisign.connectiontimeout = There is a server problem, please contact the service provider
businesslogic.digisign.readtimeout		= There is a server problem, please contact the service provider

businesslogic.tekenaja.errorprocessingselfie = Error when processing selfie photo
businesslogic.tekenaja.errorprocessingktp = Error when processing KTP photo
businesslogic.tekenaja.waitforsign = You are already registered in TekenAja. Please wait for the signature request to be sent.
businesslogic.tekenaja.pleaseverifyemail = You are already registered in TekenAja. Please verify via the email that has been sent.
businesslogic.tekenaja.errordownloadcert = Error {0}
businesslogic.tekenaja.certificaterenewed = Certificate renewed

businesslogic.tekenaja.hashsign.sentotpfailed = Sent OTP Failed
businesslogic.tekenaja.hashsign.userhasnotregistered  = User has not registered
businesslogic.tekenaja.hashsign.failedsign = Hash Sign Failed
businesslogic.tekenaja.hashsign.invalidotp = Invalid OTP
businesslogic.tekenaja.hashsign.otpexpired = OTP was expired
businesslogic.tekenaja.hashsign.systemfailure = Sorry for the inconvenience, please try again in a moment

businesslogic.vida.livenessfailed = Please take a selfie directly. Do not take picture from other media.
businesslogic.vida.selfphotonotfound = Self photo is not recorded in system
businesslogic.vida.selfphotonotfound = Self photo is not recorded in system

businesslogic.privy.prifyidnotfound = Privy ID not found in system
businesslogic.privy.tokennotfound = Token not found in system
businesslogic.privy.failedtogetlivenessurl = Failed to get Liveness URL
businesslogic.privy.livenessnotconfigured = Tenant Setting {0} not yet configured for tenant {1}.

businesslogic.privy.general.failedtogettoken = Failed to get token
businesslogic.privy.general.failedtogeneratesignature = Failed to generate Privy signature

businesslogic.job.emptyjobtype = Job type cannot be empty
businesslogic.job.emptyidjobresult = Id job result cannot be empty

businesslogic.errorhistory.notfound = Error history not found
businesslogic.errorhistory.customer = Customer
businesslogic.errorhistory.spouse = Spouse
businesslogic.errorhistory.guarantor = Guarantor

businesslogic.province.notfound = Province with ID {0} is not recorded in the system
businesslogic.province.provincewithnamenotfound = Province with the name {0} not exist in the system

businesslogic.district.notfound = District with ID {0} is not recorded in the system
businesslogic.district.invalidprovince = District {0} is not located in {1} Province
businesslogic.district.districtwithnamenotfound = District with the name {0} not exist in the system

businesslogic.subdistrict.notfound = Subdistrict with ID {0} is not recorded in the system
businesslogic.subdistrict.invaliddistrict = Subdistrict {0} is not located in {1}

businesslogic.external.emailnotfound = User with email {0} not found
businesslogic.external.emailempty = Email cannot be empty
businesslogic.external.idnoinvalid = NIK must be 16 digits
businesslogic.external.idnumber = NIK must be a number
businesslogic.external.idempty = NIK cannot be empty
businesslogic.external.phonenumber = Phone number must be a number
businesslogic.external.phoneempty = Phone number cannot be empty
businesslogic.external.emailinvalid = The email format <NAME_EMAIL>
businesslogic.external.datatypeinvalid = Data type Invalid
businesslogic.external.idnotfound = User with NIK {0} not found
businesslogic.external.phonenotfound = User with phonenumber {0} not found
businesslogic.external.tknajaemailservicezero = The activation link has been sent to the user's email, please check the registered email to continue the activation process.
businesslogic.external.tknajaemailserviceone = The activation link has been sent to the user's phone number, please check the SMS received to continue the activation process.
businesslogic.external.pemungutaccnotavailable = Account for Tax Type Pemungut not available. Please re-send request with Tax Type Non-Pemungut.

businesslogic.embedwebview.decrypterror = The opened link is incomlete. Please use the complete link.
businesslogic.embedwebview.signlinkrequestnotfound = Sign link request not found

businesslogic.message.datestartgreaterthandateend = {0} greater than {1}

businesslogic.autosign.base64notvalid = File base64 not valid
businesslogic.autosign.executiondatenotvalid = Execution date not valid
businesslogic.autosign.emptyfilename = Filename cannot be empty
businesslogic.autosign.emptybase64 = base64 cannot be empty
businesslogic.autosign.emptyexecutiontime = Execution time cannot be empty
businesslogic.autosign.invaliddaterange = Maximum {0} date range is 30 days
bussinesslogic.autosign.headernotfound = Import data header not found
businesslogic.autosign.invalidstatusimport = Invalid Status Import
businesslogic.autosign.emptytemplatefile = File template import autosign bm cannot be found

businesslogic.tenantsettings.tenantsettingsnotfound = Tenant Setting Not Found
businesslogic.tenantsettings.tenantsettingsnotfoundfortenant = Setting Type {0} not found for tenant {0}
businesslogic.tenantsettings.settingvalueempty = Setting Value for Type {0} is empty

businesslogic.role.successinput = Role is saved successfully
businesslogic.role.successedit = Role is edited successfully
businesslogic.role.rolewithnamealreadyexist = Role with name {0} already exist
businesslogic.role.rolename = Role Name
businesslogic.role.admesignnotallowed = Role with name Admin Esign or code ADMESIGN is not allowed

businesslogic.menu.menunotactive = Menu {0} is not active
businesslogic.menu.menunotmanageable = Menu {0} is not manageable
businesslogic.menu.pathempty = Menu {0} has an empty path
businesslogic.menu.menunotfound = Menu with code {0} not found

scheduler.failed.initialization = Failed upon initializing scheduler job

endpoint.deprecated = This endpoint is deprecated

vfirst.52992 = Username / Password incorrect
vfirst.57089 = Contract Expired
vfirst.57090 = User Credit Expired
vfirst.57091 = User Disabled
vfirst.65280 = Service is temporarily unavailable
vfirst.65535 = The specified message does not conform to DTD
vfirst.28673 = Destination number not numeric
vfirst.28674 = Destination number empty
vfirst.28675 = Sender address empty
vfirst.28676 = Template mismatch
vfirst.28677 = UDH is invalid/ SPAM message
vfirst.28678 = Coding is invalid
vfirst.28679 = SMS text is empty
vfirst.28680 = Invalid sender ID
vfirst.28681 = The exact same SMS has been sent. Please wait for a moment before sending another one.
vfirst.28682 = Invalid Reciever ID
vfirst.408   = Request Timed Out

businesslogic.notification.dailylimitnotset = Notification sending point daily limit is not set. Please contact your administrator.
businesslogic.notification.dailylimitreached = You have reached the daily notification sending point limit. Please try again tomorrow.
businesslogic.global.limitnotification = You have reached notification limit, please try again in {0} minutes.