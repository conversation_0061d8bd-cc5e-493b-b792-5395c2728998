package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class RegisterException extends AdInsException {
	
	private static final long serialVersionUID = 1L;
	
	public enum ReasonRegister {
		ALREADY_REGISTERED,
		NIK_PHONE_EMAIL_USED,
		MAX_ATTEMPTS_REACHED
	}
	
	private final ReasonRegister reason;
	
	public RegisterException(String message, ReasonRegister reason) {
		super(message);
		this.reason = reason;
	}
	
	public RegisterException(String message, Throwable ex, ReasonRegister reason) {
		super(message, ex);
		this.reason = reason;
	}

	@Override
	public int getErrorCode() {
		switch (reason) {
			case ALREADY_REGISTERED:
				return StatusCode.USER_ALREADY_REGISTERED;
			case NIK_PHONE_EMAIL_USED:
				return StatusCode.ERROR_EXIST;
			case MAX_ATTEMPTS_REACHED:
				return StatusCode.MAX_REGISTER_ATTEMPTS_REACHED;
			default:
				return StatusCode.UNKNOWN;
		}
	}
	
}
