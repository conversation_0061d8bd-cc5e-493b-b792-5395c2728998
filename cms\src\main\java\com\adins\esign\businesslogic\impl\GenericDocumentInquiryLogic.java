package com.adins.esign.businesslogic.impl;

import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.DocumentInquiryLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.custom.InquiryDocumentBean;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.DocumentValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.ListInquiryDocumentRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentResponse;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.UserException;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericDocumentInquiryLogic extends BaseLogic implements DocumentInquiryLogic {

    private static final Logger LOG = LoggerFactory.getLogger(GenericDocumentInquiryLogic.class);

    @Autowired private CommonLogic commonLogic;

    @Autowired private DocumentValidatorLogic documentValidatorLogic;
    @Autowired private UserValidatorLogic userValidatorLogic;
    @Autowired private TenantValidatorLogic tenantValidatorLogic;
    @Autowired private CommonValidatorLogic commonValidatorLogic;

    private static final String CONST_BELUM_TTD = "Belum TTD";

    @Override
    public ListInquiryDocumentResponse getListInquiryDocument(ListInquiryDocumentRequest request, AuditContext audit) {
        MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		AmMemberofrole memberOfRole = this.checkingUserAndMemberRole(tenant, request.getRoleCode(), audit);
		LOG.info("Start inquiry document");
		String queryType = null;
		if (GlobalVal.INQUIRY_TYPE_INBOX.equalsIgnoreCase(request.getInquiryType())) {
			queryType = GlobalVal.INQUIRY_TYPE_INBOX;
		} else if (GlobalVal.ROLE_BM_MF.equals(memberOfRole.getAmMsrole().getRoleCode())) {
			queryType = GlobalVal.INQUIRY_TYPE_LIST_BM_MF;
		} else {
			queryType = GlobalVal.INQUIRY_TYPE_LIST_CUST;
		}

		MsLov lovDocType = new MsLov();
		if (StringUtils.isNotBlank(request.getDocType())) {
			lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE, request.getDocType());
			String errorMessage = getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST, new String[] { GlobalVal.DOCUMENT_TYPE, request.getDocType() }, audit);
			commonValidatorLogic.validateNotNull(lovDocType, errorMessage, StatusCode.DOC_TYPE_NOT_EXIST);
		}
		logProcessDuration(lovDocType.getCode(), new Date(), new Date());
		LOG.info("End Get lovDocType");
		MsLov lovSignStatus = new MsLov();
		if (StringUtils.isNotBlank(request.getTransactionStatus())) {
			lovSignStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS, request.getTransactionStatus());
			String errorMessage = getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST, new String[] { GlobalVal.TRANSACTION_STATUS, request.getTransactionStatus() }, audit);
			commonValidatorLogic.validateNotNull(lovSignStatus, errorMessage, StatusCode.SIGN_STATUS_NOT_EXISTS);
		}
		logProcessDuration(lovSignStatus.getCode(), new Date(), new Date());
		LOG.info("END Get lovSignStatus");
		if (!isDateRangeValid(request.getRequestedDateStart(), request.getRequestedDateEnd(), audit)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
					new Object[] { GlobalVal.CONST_REQUEST }, audit), ReasonDocument.INVALID_DATE_RANGE);
		}
		
		if (!isDateRangeValid(request.getCompletedDateStart(), request.getCompletedDateEnd(), audit)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
					new Object[] { GlobalVal.CONST_COMPLETED }, audit), ReasonDocument.INVALID_DATE_RANGE);
		}

		String rowPerPage = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_MAX_ROW, audit);

		Integer rowNum = Integer.parseInt(rowPerPage);
		int start = ((request.getPage() - 1) * rowNum) + 1;
		int end = request.getPage() * rowNum;
		String[] paramsInquiry = new String[13];
		LOG.info("Start params inquiry");
		this.populateParamsInquiry(paramsInquiry, request, lovSignStatus, lovDocType, start, end);
		LOG.info("End Populate Params inquiry");

		Date startProcess = new Date();
		List<Map<String, Object>> docList = daoFactory.getDocumentInquiryDao().getListInquiryDocument(paramsInquiry, audit.getCallerId(), queryType);
		logProcessDuration("Query List Inquiry", startProcess, new Date());
		
		LOG.info("Got Inquiry Doc Data");
		Iterator<Map<String, Object>> itr = docList.iterator();
		LOG.info("Document Bean Listing");
		List<InquiryDocumentBean> documentBeanList = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			documentBeanList.add(prepareInquiryBean(map, memberOfRole));
		}
		LOG.info("Document Bean Listed");
		
		Date startProcess2 = new Date();
		long totalData = daoFactory.getDocumentInquiryDao().countListInquiryDocument(paramsInquiry, audit.getCallerId(), queryType);
		logProcessDuration("Query List Count", startProcess2, new Date());

		long totalPage = (totalData % rowNum == 0) ? totalData / rowNum : (totalData / rowNum) + 1;
		LOG.info("Counted total document");
		ListInquiryDocumentResponse response = new ListInquiryDocumentResponse();
		response.setListDocument(documentBeanList);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalData);
		LOG.info("Done method inquiry");
		return response;
    }

    private AmMemberofrole checkingUserAndMemberRole(MsTenant tenant, String roleCode, AuditContext audit) {

		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), true, audit);
		AmMemberofrole memberOfRole = daoFactory.getRoleDao().getMemberofrole(tenant, user, roleCode);
		if (null == memberOfRole) {
			// Plan B in case cache FE menyebabkan tidak kirim role code
			memberOfRole = daoFactory.getRoleDao().getMemberofroleByAmMsuserAndMsTenant(user, tenant);
		}
		
		if (null == memberOfRole) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_CANNOT_ACCESS_OTHER_TENANT, null, audit), ReasonDocument.CANNOT_ACCESS_OTHER_TENANT);
		}

		if (!GlobalVal.ROLE_BM_MF.equals(memberOfRole.getAmMsrole().getRoleCode()) && !GlobalVal.ROLE_CUSTOMER.equals(memberOfRole.getAmMsrole().getRoleCode())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_USER_CANNOT_ACCESS_DOC_1, null, audit), ReasonUser.INVALID_USER_ROLE);
		}

		return memberOfRole;
	}

    private boolean isDateRangeValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);

		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return true;
		}
		if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
			try {
				sdf.parse(startDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
			try {
				sdf.parse(endDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}

		long dayCount = 0;
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			dayCount = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		LOG.info("Date range: {} hari", dayCount);
		return dayCount <= Long.valueOf(maxRangeDate);
	}
    
    private void populateParamsInquiry(String[] paramsInquiry, ListInquiryDocumentRequest request, MsLov lovSignStatus, MsLov lovDocType, int start, int end) {
		
		/**
		 * Index	Filter
		 * ========================
		 * 0		tenant code
		 * 1		office code
		 * 2		customer name
		 * 3		sign status LOV
		 * 4		reference number
		 * 5		request date START
		 * 6		request date END
		 * 7		complete date START
		 * 8		complete date END
		 * 9		paging START
		 * 10		paging END
		 * 11		document type LOV
		 * 12		region code
		 * 13		proses meterai
		 * 14		is active (Per 4.4.0, filter ini hanya untuk /s/inquiryNormal)
		 */
		
		paramsInquiry[0] = request.getTenantCode();
		paramsInquiry[1] = request.getOfficeCode();
		paramsInquiry[2] = StringUtils.upperCase(request.getCustomerName());
		
		// jika lov null maka idLov terisi 0
		paramsInquiry[3] = 0 == lovSignStatus.getIdLov() ? "" : String.valueOf(lovSignStatus.getIdLov());
		paramsInquiry[4] = request.getRefNumber();
		paramsInquiry[5] = request.getRequestedDateStart();
		paramsInquiry[6] = request.getRequestedDateEnd();
		paramsInquiry[7] = request.getCompletedDateStart();
		paramsInquiry[8] = request.getCompletedDateEnd();
		paramsInquiry[9] = String.valueOf(start);
		paramsInquiry[10] = String.valueOf(end);
		paramsInquiry[11] = 0 == lovDocType.getIdLov() ? "" : String.valueOf(lovDocType.getIdLov());
		paramsInquiry[12] = request.getRegionCode();
		
		if (paramsInquiry.length > 13) {
			paramsInquiry[13] = request.getProsesMaterai();
		}
		
		/**
		 * Rework 4.4.0 - Tambahan filter isActive
		 * Per 4.4.0, hanya /s/inquiryNormal yang paramsInquiry sizenya 15
		 */
		if (paramsInquiry.length >= 15) {
			paramsInquiry[14] = request.getIsActive();
		}
	}

    private InquiryDocumentBean prepareInquiryBean(Map<String, Object> map, AmMemberofrole memberOfRole) {
		InquiryDocumentBean bean = new InquiryDocumentBean();
		bean.setRefNumber((String) map.get("d1"));
		bean.setDocTypeName((String) map.get("d2"));
		bean.setDocTemplateName((String) map.get("d3"));
		bean.setCustomerName((String) map.get("d4"));
		bean.setRequestDate((String) map.get("d5"));
		bean.setCompleteDate((String) map.get("d6"));
		bean.setDocumentId((String) map.get("d7"));
		bean.setTotalSigned(((String) map.get("d8")));
		bean.setSignStatus((String) map.get("d11"));
		bean.setIdDocumentD(((BigInteger) map.get("d9")).longValue());
		bean.setTotalStamped((String) map.get("d12"));
		bean.setStatusOtomatisStamping((String) map.get("d13"));
		bean.setVendorCode((String) map.get("d15"));
		if ("1".equals(map.get("d16"))) {
			bean.setSigningProcess(GlobalVal.CONST_PROSES_TTD);
		} else {
			bean.setSigningProcess(CONST_BELUM_TTD);
		}

		// Set document archive status
		String archiveStatus = (String) map.get("d19");
		if ("1".equals(archiveStatus)) {
			bean.setDocumentArchiveStatus(GlobalVal.CONST_ARCHIVE);
		} else if ("2".equals(archiveStatus)) {
			bean.setDocumentArchiveStatus(GlobalVal.CONST_RESTORE);
		} else {
			bean.setDocumentArchiveStatus(GlobalVal.CONST_ACTIVE);
		}

		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(bean.getDocumentId());
		bean.setIsCurrentTopPriority(documentValidatorLogic.isDocumentTopPriorityForSigner(docD, memberOfRole.getAmMsuser()) ? "1" : "0");
		
		return bean;
	}
}
