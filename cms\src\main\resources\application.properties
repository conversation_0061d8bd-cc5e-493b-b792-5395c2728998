management.server.port=9093
management.server.address=0.0.0.0
management.endpoints.web.exposure.include=env,logfile

#disabled karena pakai token dari struts2 
security.enable-csrf=false

server.port=8095
server.servlet.context-path=/
server.servlet.session.cookie.name=idjs
server.tomcat.additional-tld-skip-patterns=serializer*.jar,xml-apis*.jar,xerces*.jar
server.tomcat.max-threads=200
server.tomcat.max-http-form-post-size=10MB
server.tomcat.max-swallow-size=10MB
server.jetty.threads.max=200
server.jetty.max-http-form-post-size=10MB
server.compression.enabled=true
server.compression.min-response-size=1024
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/xml,application/xhtml+xml,application/x-javascript

server.servlet.context-parameters.initializerClassLocation=com.adins.am.controller.SystemLogout

logging.config=classpath:logback-spring.xml
logging.level.root=info
logging.level.org.hibernate=warn
logging.level.org.apache.struts2.config.AbstractBeanSelectionProvider=warn
logging.level.org.apache.struts2.util.TextProviderHelper=error
logging.level.org.quartz.core=info
logging.level.com.adins=trace
logging.level.com.adins.framework.mvc.struts2.interceptor.PushPullParamsInterceptor=debug
logging.level.com.adins.framework.persistence.dao.hibernate.interceptor.audit.GenericAuditLog=debug
logging.level.com.adins.framework.service.base.aspect.AuditContextAspect=debug
logging.level.com.adins.struts2.interceptors=debug
logging.level.com.adins.cxf=debug
logging.file.path=/esign/logs
logging.file.name=${logging.file.path}/esign-cms.log
logging.file.max-history=1000
logging.file.max-size=100MB
logging.file.total-size-cap=5GB
logging.pattern.rolling-file-name=${logging.file.path}/%d{yyyy-MM-dd, aux}/esign-cms.%d{yyyy-MM-dd-HH}.%i.log.gz
logging.pattern.console=%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(%X{loginId}%X{callerId}:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}
logging.pattern.file=%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} %X{loginId}%X{callerId}: %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}

# List parameter yang akan dimask di tracedata. 
# untuk tambah parameter yang akan dimask, pisahkan tiap param dengan ; 
logging.sensitive.parameter=password;newPassword;oldPassword;content;pdfFile;file;idPhoto;selfPhoto;npwpPhoto;documentFile;signerSelfPhoto;base64ExcelReport;excelBase64;xlBase64;docFile;photoBase64;image;pdfBase64;stampedDocument

oauth.token.validity.hour=5

spring.aop.proxy-target-class=false
spring.freemarker.checkTemplateLocation=false

spring.flyway.enabled=false
spring.flyway.placeholder-prefix=$${
spring.flyway.locations=classpath:db/migration/mssqlserver
spring.flyway.url=*****************************************************************
spring.flyway.user=sa
spring.flyway.password=AdIns2021

spring.datasource.driver-class-name=org.postgresql.Driver

#creator_dbsvr
spring.datasource.url=**************************************************************
spring.datasource.username=ENC(kc3iWR2ZcV3uU6qLNgd0+qSpskbYdQxUX6WVX9fAJeDSbTWbuCzob8WUmSyBQmGP)
spring.datasource.password=ENC(1XsyemWeFT/qsu7etMkZP3jSLToPXXf86fJb/l6S6jyeP/4hPdwSMILnO1eIWmPb)

#RDS
#spring.datasource.url=*************************************************************************************************************
#spring.datasource.username=sa
#spring.datasource.password=AdIns2021!

spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=200

spring.mail.host=smtp.office365.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=ENC(odeebnipLVpNarx7/iBqLunj+ksWuicXk3F4o8GLkY6l5O4jUUgXdUkDtnWxpYJM)
#spring.mail.properties.mail.smtp.ssl.enable=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.timeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000
spring.mail.properties.mail.smtp.socketFactory.class= javax.net.ssl.SSLSocketFactory

spring.mail.backup.host=mail.esignhub.my.id
spring.mail.backup.port=465
spring.mail.backup.username=<EMAIL>
spring.mail.backup.password=ENC(zZqE/vHNKUeL5EkfyIoU05X5TBZg6SCkP/QODZ3j/gGkLTVi7IZo/ZIfL9tAFT3Q)
spring.mail.backup.properties.mail.smtp.starttls.enable=true
spring.mail.backup.properties.mail.smtp.auth=true
spring.mail.backup.properties.mail.smtp.connectiontimeout=30000
spring.mail.backup.properties.mail.smtp.timeout=30000
spring.mail.backup.properties.mail.smtp.writetimeout=30000
spring.mail.backup.properties.mail.smtp.socketFactory.class= javax.net.ssl.SSLSocketFactory

javamelody.init-parameters.authorized-users=ENC(GY3GFuN2D1WP3xIw/jJaSh7Yq9yf50LEgoCFB2nZ9mIOJ3sciKnRCNFneq2ajQKhjMH9cHal1Uwbg7iO/e2v6g==)
javamelody.init-parameters.storage-directory=/esign/javamelody
javamelody.init-parameters.url-exclude-pattern=(/css/.*|/doc/.*|/dwr/.*|/img/.*|/js/.*)
javamelody.init-parameters.quartz-default-listener-disabled=true

spring.quartz.properties.org.quartz.scheduler.skipUpdateCheck=true
spring.quartz.properties.org.quartz.threadPool.threadCount=15
quartz.scheduler-factory.expose-scheduler-in-repository=true

cxf.feature.swagger2.enabled=true
cxf.feature.logging.enabled=true
cxf.logging.limit=20971520

google.api.firebase=AAAA5rWyeDE:APA91bHI9dYV7hm7Q0_YWFlF-w7U31zXjHOofYWAP-B4AqWaMuVwt_yutG2G8hAny_duJAeFAxxE6HuBnYMkQ6pouL2Xz3vhzPDtftDiv7eME1BoYMyuww_0xf6EZN1N3pMTbzpJRsGO
google.fcm.url = https://fcm.googleapis.com/fcm/send

##---APPLICATION FUNCTION---
login.bypass=0

ldap.enabled=true
ldap.host=judges
ldap.port=389
ldap.domain=AD-INS

#keystore.url if file in classpath "classpath:esign-dev.jks", absolute "file:///F:/esign.jks"
keystore.url=classpath:esign-dev.jks
keystore.password=ENC(nXn03u6EvT/TCoWGIUx5FoVpuc2GOfkegWbsmXNEHm2oMNk7DsiUlEIxJHqD5TgC)
keystore.keypair.alias=ENC(gt8fpZUN4Xb6g0agbkiQTZ2QMBZR0rO+jMpHviEJzuLVImfbFe0E6CJmz7il/cZL)
keystore.keypair.password=ENC(4X0Sb4q0kxvLqagvkOSZL9rqyANCkYUeak0xC7ZbFlCdlmuEbW2rw7Zkcx7wp+L2)

#0 = HTTP | 1 = HTTPS
is.secure = 0

#nc-webserver
nc.uri=http://***********/CORE/CONFINS/api

#staging
staging.uri=http://localhost:8080/com.adins.mss-staging.webservices/services/staging

#esign
esign.activation.uri = http://ppd-webdev-svr:8086/adimobile/esigns/activation?uri=
esign.sign.uri = http://gdkwebsvr:8080/signature?id=
esign.login.uri = http://gdkwebsvr:8080/login

#digisign
digisign.uri=https://api.tandatanganku.com
digisign.preregister.uri=/REG-MITRA.html
digisign.activation.uri=/gen/genACTPage.html
digisign.senddocument.uri=/SendDocMitraAT.html
digisign.signdocument.uri=/gen/genSignPage.html
digisign.bulksigndocument.uri=/gen/genBulkSign.html
digisign.downloaddocument.uri=/DWMITRA64.html
digisign.checkbalance.uri=/status/balance
digisign.checkcertexp.uri=https://api.tandatanganku.com/status/certificate
digisign.changephoneemail.uri=/UPDATE-EP.html

# Alibaba cloud properties
spring.cloud.alicloud.region=ap-southeast-5
spring.cloud.alicloud.uid=5236635838005115
spring.cloud.alicloud.access-key=ENC(d0m75RGAu7E6LN6jOiPSKhYPxP42bBdj4W+m4nmt8g2aUaOUm8qJ6/IJ1sbTku5/nV4w9+UXN60bG/4Myqehsg==)
spring.cloud.alicloud.secret-key=ENC(oAxhXmgHlR6lydioxnQa03weeEOw5uIJmRIKDKMNIInDTLxOJw2RXL1FKZSYcBFg+t+2mKUc7gxnwr4sRrhpSQ==)
spring.cloud.alicloud.oss.endpoint=oss-ap-southeast-5.aliyuncs.com
spring.cloud.alicloud.mns.endpoint=http://5236635838005115.mns.ap-southeast-5.aliyuncs.com
spring.cloud.alicloud.fc.service=esignhub_dev
esignhub.cloudstorage.bucket=esignhub-bucket2
esignhub.cloudstorage.archive.bucket=esignhub-bucket2-archive

#e-meterai MCP
emeterai.clientid=MCP30
emeterai.clientemail=<EMAIL>
emeterai.clientpassword=8af69b82
emeterai.auth.xapikey = c02a26b3-6cb9-4a39-97cc-0cb3277a80c1
emeterai.uri=https://sandbox.pastisah.id/api/v1
emeterai.generatetoken.uri=/client/get-token
emeterai.uploaddocument.uri=/emeterai/upload-document-onpremis
emeterai.generateemeterai.uri=/emeterai/generate-meterai-onpremis-single
emeterai.stampingemeterai.uri=/emeterai/stamping-meterai-onpremis
emeterai.downloaddocument.uri=/emeterai/download-document-onpremis

#e-meterai RKG
emeterai.rkg.auth.token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJ1c2Vycy5kaWdpdGFsbWF0ZXJhaS5jb20iLCJjb3Jwb3JhdGVDb2RlIjoiOTAwNTUiLCJleHAiOjE2NDM4NTg0OTcsImlhdCI6MTY0Mzc3MjA5NywiaXNzIjoiZGlnaXRhbG1hdGVyYWkuY29tIiwianRpIjoiMjAyMjAyMDIxMDIxMzcyMWYzNzAxYS1hOTkyLTQ1NDItODVlNS0zMTU3ODc5OTE5NWIiLCJuYmYiOiIyMDIyLTAyLTE2VDEwOjIxOjM3LjIyNDk2OTk2KzA3OjAwIiwicmlnaHRzIjoiYmFubmluZ191c2VyLGludml0ZV9vdGhlcixyZXZva2VfdXNlcixjaGFuZ2VfdXNlcl9yb2xlLHVwbG9hZF9kb2Msc3RhbXBpbmdfbWF0ZXJhaSxzaWduaW5nLHN0YW1waW5nX3RlcmEsd29ya2luZ191bml0LHVzZXJfYWN0aXZpdHlfbG9nLHF1b3RhX3VzYWdlIiwic3ViIjoiZmZkb3NLV25UT0JlNnhyU2lvWXloVHVsaUtrNlFURkNZOWNOcTlYWnk2eXBaMlcycE5Ranl6UkxpNXhwWU5nTiIsInVzZXJUeXBlIjoiUEVSU09OQUwifQ.0oTfuQzNTnQQsxOQLUA5iTsV_aFNnoxsYqS_9sWW65A
emeterai.rkg.uri=http://117.53.44.44:8080/api
emeterai.rkg.uploaddocument.uri=/secured/documents
emeterai.rkg.createorder.uri=/secured/stamping-order/create
emeterai.rkg.stampingorder.uri = /secured/stamping-order
emeterai.rkg.download.uri = /result
emeterai.rkg.params.uri = /params
emeterai.rkg.submit.uri = /submit
emeterai.rkg.generate.uri = /generate-sn
emeterai.rkg.complete.uri = /complete

#e-meterai Pajakku
e-meterai.pajakku.processnumber = 2
e-meterai.pajakku.user=ENC(tZkj4PVLe9z96Ro+G7Z0LEhx/9VAOLyl4nsdvBTUPzk7hcgk2kPm0gFFh8ApLpLKtOyP0FdnPIbSI3ecCfsnsw==)
e-meterai.pajakku.password=ENC(dsvjq6J9gWXqCRszBpbdVOn8J6xm5uZC+rCQ9aukxftxW1aUYzZfq3fD3j8xIxBR)
e-meterai.pajakku.login=https://backendservicestg.e-meterai.co.id/api/users/login
e-meterai.pajakku.upload=https://fileuploadstg.e-meterai.co.id/uploaddoc2
emeterai.pajakku.generate.uri = https://stampv2stg.e-meterai.co.id/chanel/stampv2
emeterai.pajakku.stamping.uri = https://stampservicestg.e-meterai.co.id/keystamp/adapter/docSigningZ
emeterai.pajakku.download = https://fileuploadstg.e-meterai.co.id/getfile3/

emeterai.pajakku.namadoc = Surat Perjanjian
emeterai.pajakku.certificatelevel = NOT_CERTIFIED
emeterai.pajakku.profilename = emeteraicertificateSigner
emeterai.pajakku.reason = Persetujuan Perjanjian Kontrak
emeterai.pajakku.reason2 = Stamping Dokumen Transaksi
emeterai.pajakku.folder = /sharefolder/

emeterai.url.documenttype = https://stampv2stg.e-meterai.co.id/jenisdoc
emeterai.uplcon.integrationvalue = dXNlci5lc2lnbg==

# On-Premise Peruri propoerties
emeterai.onprem.processnumber = 52
emeterai.onprem.paymentreceipt.processnumber = 522
emeterai.onprem.dir.unsigned = V:\\UNSIGNED\\
emeterai.onprem.dir.signed = V:\\SIGNED\\
emeterai.onprem.dir.stamp = V:\\STAMP\\
emeterai.onprem.stamp.url = http://billingserver:7777/adapter/pdfsigning/rest/docSigningZ
emeterai.onprem.stamp.src = /sharefolder/UNSIGNED
emeterai.onprem.stamp.dest = /sharefolder/SIGNED
emeterai.onprem.stamp.specimen = /sharefolder/STAMP

#tekenaja location
tekenaja.url.province=https://apix.sandbox-111094.com/v2/data/province
tekenaja.url.district=https://apix.sandbox-111094.com/v2/data/district
tekenaja.url.subdistrict=https://apix.sandbox-111094.com/v2/data/subdistrict

#tekenaja hash sign
tekenaja.hashsign.uri = https://apix.sandbox-111094.com/v3
tekenaja.hashsign.sendotp = /sign/otp
tekenaja.hashsign.downloadcert = /sign/certificate-download
tekenaja.hashsign.sign = /sign/hash

#tekenaja
tekenaja.uri = https://apix.sandbox-111094.com/
tekenaja.register.uri = v2/register
tekenaja.registercek.uri = v2/register-check
tekenaja.uploaddoc.uri = v3/document/upload
tekenaja.downloaddoc.uri = v2/document/download
tekenaja.sign.url = v2/url/sign
tekenaja.sign.bulk.url = v2/sign/bulk

#vida
vida.register.uri = https://services-sandbox.vida.id/main/v3/services/kyc
vida.generatetoken.uri = https://qa-sso.vida.id/auth/realms/vida/protocol/openid-connect/token
vida.poa.uri = https://services-sandbox.vida.id/signer/v2/services/esign/poa?docType=nontemplate
vida.poa.checkstatus.uri = https://services-sandbox.vida.id/signer/v1/services/esign/
vida.generatetokenpoa.uri = https://qa-sso.vida.id/auth/realms/vida/protocol/openid-connect/token

# Privy (Core)
privy.core.uri = https://stg-core.privy.id
privy.register.uri = /v1/merchant/registration
privy.registerstatus.uri = /v1/merchant/registration/status
privy.useraccesstoken.uri = /v1/merchant/user-token/token
privy.requestotp.uri = /v1/merchant/user-token/otp-request
privy.uploaddoc.uri = /v3/merchant/document/upload
privy.confirmotp.uri = /v1/merchant/user-token/otp-confirm

# Privy (General)
privy.general.base.uri = https://api-b2b.dcistg.id
privy.general.token.uri = /oauth2/api/v1/token
privy.general.register.uri = /web/api/v2/register
privy.general.registerstatus.uri = /web/api/v2/register/status
privy.general.uploaddoc.uri = /web/api/v2/doc-signing
privy.general.otprequest.uri = /web/api/v2/doc-signing/otp/request
privy.general.otpvalidation.uri = /web/api/v2/doc-signing/otp/validation

# Privy Liveness
privy.liveness.base.uri = https://stg-core.privypass.id
privy.liveness.uri = /manage/v1/liveness/signed/url
privy.liveness.v3.uri = /transaction/v1/liveness/signed/url

# Privy Verification V2
privy.v2.verification.url = https://b2b-api-av.privy.id/dev/api/v2/userverification

#sms http://www.myvaluefirst.com/smpp/sendsms
sms.url=https://api.wassenger.com/v1/messages
sms.username=
sms.password=92e352ceecda8b05d3d2149d92c701d82724b4c2fa893871e78c46bc1dec3067e4bafa4927e2a02b
sms.from=
sms.vFirstVersion = 1.2
sms.vFirstFrom = eSignHub
sms.vFirstTag = SMS eSignHub

# Jatis WhatsApp
jatis.sendwa.baseurl = https://m2m.coster.id/v1/{x}/messages
jatis.sendwa.accountid = 785e4680715b11eebf31fff7a41ee0e0
jatis.sendwa.token = L1IifiIkcGV5PjJnX0t6e0o0Z2ZaRC0xVXVNcVVM
jatis.sendwa.template.suffix = _dev

# Jatis SMS
jatis.sms.url = http://api.jatismobile.com/
jatis.sms.id = signhubdev
jatis.sms.password = Singhubdev123
jatis.sms.sender = eSignHub
jatis.sms.division = eSignhubDev

# Halosis WhatsApp
halosis.base.url = https://api.halosis.id
halosis.login.url = /v1/login
halosis.accesstoken.url = /v1/access-token
halosis.sendwa.url = /v1/messages
halosis.cred.email = <EMAIL>
halosis.cred.password = 1TrmQkYNwwVeg3Ue

#job
job.daily-recap.cron = 1 0 0 * * ?
job.daily-recap.enabled = true
job.delete-email.cron = 0 0 1 1/1 * ? *
job.delete-email.enabled = false
job.delete-unused-complementary-stamping-document.cron = 0 * * ? * *
job.delete-unused-complementary-stamping-document.enabled = false
job.deleteresultstamping-onprem.cron = */10 * * * * ?
job.deleteresultstamping-onprem.enabled = true
job.digi-balancesync.cron = 0 1 0 1/1 * ? *
job.digi-balancesync.enabled = false
job.digi-certexpsync.cron = 0 1 0 1/1 * ? *
job.digi-certexpsync.enabled = false
job.document-type.cron = 0 0 2 * * ?
job.document-type.enabled = false
job.email-reminder.cron = 10 30 0 * * ?
job.email-reminder.enabled = false
job.errhist-rerunsenddoc.cron = */10 * * * * ?
job.errhist-rerunsenddoc.enabled = false
job.location.cron = 0 0 3 1/1 * ? *
job.location.enabled = false
job.on-prem-payment-receipt.cron = */15 * * * * ?
job.on-prem-payment-receipt.enabled = false
job.pajakku-attachmeterai.cron = */10 * * * * ?
job.pajakku-attachmeterai.enabled = false
job.process-automatic-stamp.cron = 45 * * ? * *
job.process-automatic-stamp.enabled = true
job.queue-balancecheck.cron = 0 * * * * ?
job.queue-balancecheck.enabled = true
job.queue-deletefromoss.enabled = false
job.queue-deletefromoss.enabled.cron = */10 * * * * ?
job.queue-savesignresult.cron = */10 * * * * ?
job.queue-savesignresult.enabled = true
job.queue-savesignresulttekenaja.cron = */10 * * * * ?
job.queue-savesignresulttekenaja.enabled = false
job.queue-upderrhistrerun.cron = */10 * * * * ?
job.queue-upderrhistrerun.enabled = false
job.read-email.cron = */10 * * * * ?
job.read-email.enabled = true
job.resumeworkflow.cron = 0 * * ? * *
job.resumeworkflow.enabled = false
job.sign-vida.cron = */5 * * * * ?
job.sign-vida.enabled = true
job.sign-privy.cron = */5 * * * * ?
job.sign-privy.enabled = true
job.stamping-on-prem.cron = 0 0/1 * 1/1 * ? *
#*/10 * * * * ?
job.stamping-on-prem.enabled = true
job.stamping-payment-receipt.cron = */10 * * * * ?
job.stamping-payment-receipt.enabled = true
job.sync-payment-receipt.cron = */10 * * * * ?
job.sync-payment-receipt.enabled = true
job.uploaddmswf.cron = 30 * * ? * *
job.uploaddmswf.enabled = false
job.checkregisstat.cron = 0 0/1 * 1/1 * ? *
job.checkregisstat.enabled = true
job.attachmeterai-privy.cron = */15 * * * * ?
job.attachmeterai-privy.enabled = true
job.attachmeterai-vida.cron = */15 * * * * ?
job.attachmeterai-vida.enabled = true

# eSignHub URL
base.esg.url = https://gdkwebsvr:8080/
regist.inv.url = https://gdkwebsvr:8080/i/reg?code=
link.forwarder.url = http://gdkwebsvr:8080/url?cd=
link.embed.dashboard = http://gdkwebsvr:8080/embed/V2/dashboard?msg=


email.host = mail.andyresearch.my.id
email.port = 993

nodeflux.authkey = NODEFLUX-HMAC-SHA256 Credential=A7FTRZZ1MCZY4L8VXDT3IQ33Q/20211214/nodeflux.api.v1beta1.ImageAnalytic/StreamImageAnalytic, SignedHeaders=x-nodeflux-timestamp, Signature=abeb9f7a56c0004085faa5cee94dc61d7e9a994efdbe1652e938b86abbf2ce98
nodeflux.xtimestamp = 20211214T101551Z
nodeflux.facematchliveness.url = https://api.cloud.nodeflux.io/syncv2/analytics/face-match-liveness

#path result file
#Deprecated karena hanya dipakai untuk lokal dev environment untuk pengambilan file foto dari DB byte array
user.photo.path = E://esign/image

#vfirst
vfirst.generatetoken.url = https://api.myvfirst.com/psms/api/messages/token?action=generate
vfirst.deletetoken.url = https://api.myvfirst.com/psms/api/messages/token?action=delete&token=
vfirst.sendsms.url = https://api.myvaluefirst.com/psms/servlet/psms.JsonEservice
vfirst.username.json = ENC(qa/0YQn938Xmk2W8BR/KCz94Ac598r/zwzJmH1b0PWU+PECIMJ5GdGRWl3QXc9fb)
vfirst.pass.json = ENC(0jet07Cytu6/1xe2zfMui1JMZpkPySibwrnCt6uHy8ClFATnOL3tftZTy20eY+y4)

vfirst.otp.username.json = ENC(9gJ3zNFZa+fHCYVtdrq+S8vvhdxPzKnGLCbMfMgbPVPzXiMwxfJW6UqLpIFwxqPC)
vfirst.otp.pass.json = ENC(KI0ktBTjaGpe8M/fbVH/X1it+dfYPDxaQMxZjp9oMtSSoF8v+K5ROERwBJooZg70)

#regex
esign.regex.phone = ^08[1-9][0-9]{6,10}$
esign.regex.phone.custom = ^(08|628|\\+628)[1-9][0-9]{6,10}$
esign.regex.email = ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,6}$

#verification
verification.url = https://liveness-go3voyqswq-et.a.run.app
verification.liveness.facecompare.url = /liveness-facecompare

jasypt.encryptor.algorithm = PBEWITHHMACSHA512ANDAES_256
jasypt.encryptor.key-obtention-iterations = 1000
jasypt.encryptor.pool-size = 1
jasypt.encryptor.salt-generator-classname = org.jasypt.salt.RandomSaltGenerator
jasypt.encryptor.iv-generator-classname = org.jasypt.iv.RandomIvGenerator