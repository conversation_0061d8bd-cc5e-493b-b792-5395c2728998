package com.adins.esign.constants.enums;

public enum FilterType {
	FILTER_BY_DAILY("Daily"), 
	FILTER_BY_RANGE("Range"), 
	FILTER_BY_MONTHLY("Monthly"),
	FILTER_BY_ABSENSI("Attendance"),
	FILTER_BY_BRANCH("Branch"),
	FILTER_BY_USER_MEMBER("User"),
	FILTER_BY_LKP_COLLECTION("Billing"),
	FILTER_BY_SLA_COLLECTION("SLA Collection"),
	FILTER_BY_ORDER_MONITORING("Monitoring"),
	FILTER_BY_ORDER_PERFORMANCE("Performance"),
	FILTER_BY_DOWNLOAD_PDF("Download PDF"),
	FILTER_BY_DOWNLOAD_IMG("Download IMAGE");
	
	private FilterType(String filter) {
		this.filter = filter;
	}
	
	private String filter;
	
	public String toString() {
		return this.filter;
	}
}
