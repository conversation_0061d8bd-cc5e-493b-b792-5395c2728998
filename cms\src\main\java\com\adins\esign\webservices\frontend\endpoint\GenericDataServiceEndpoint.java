package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.api.GeneralSettingLogic;
import com.adins.esign.businesslogic.api.BusinessLineLogic;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.DistrictLogic;
import com.adins.esign.businesslogic.api.OfficeLogic;
import com.adins.esign.businesslogic.api.PaymentSignTypeLogic;
import com.adins.esign.businesslogic.api.ProvinceLogic;
import com.adins.esign.businesslogic.api.RegionLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SubDistrictLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.webservices.frontend.api.DataService;
import com.adins.esign.webservices.model.AddBalanceTypeRequest;
import com.adins.esign.webservices.model.AddBalanceTypeResponse;
import com.adins.esign.webservices.model.BusinessLineListRequest;
import com.adins.esign.webservices.model.BusinessLineListResponse;
import com.adins.esign.webservices.model.GetListTenantRequest;
import com.adins.esign.webservices.model.GetListTenantResponse;
import com.adins.esign.webservices.model.GetPeruriDocumentTypeRequest;
import com.adins.esign.webservices.model.GetProvinceByInvitationRequest;
import com.adins.esign.webservices.model.GetProvinceEmbedRequest;
import com.adins.esign.webservices.model.GetProvinceRequest;
import com.adins.esign.webservices.model.GetProvinceResponse;
import com.adins.esign.webservices.model.GetPsrePriorityRequest;
import com.adins.esign.webservices.model.GetPsrePriorityResponse;
import com.adins.esign.webservices.model.GetStatusStampingOtomatisTenantRequest;
import com.adins.esign.webservices.model.GetStatusStampingOtomatisTenantResponse;
import com.adins.esign.webservices.model.GetSubDistrictByInvitationRequest;
import com.adins.esign.webservices.model.GetSubDistrictEmbedRequest;
import com.adins.esign.webservices.model.GetSubDistrictRequest;
import com.adins.esign.webservices.model.GetSubDistrictResponse;
import com.adins.esign.webservices.model.GetDistrictByInvitationRequest;
import com.adins.esign.webservices.model.GetDistrictEmbedRequest;
import com.adins.esign.webservices.model.GetDistrictRequest;
import com.adins.esign.webservices.model.GetDistrictResponse;
import com.adins.esign.webservices.model.GetDocumentEMateraiTypeRequest;
import com.adins.esign.webservices.model.GetDocumentEMateraiTypeResponse;
import com.adins.esign.webservices.model.GetGeneralSettingRequest;
import com.adins.esign.webservices.model.GetGeneralSettingResponse;
import com.adins.esign.webservices.model.GetListPSrESettingRequest;
import com.adins.esign.webservices.model.GetListPSrESettingResponse;
import com.adins.esign.webservices.model.GetListPaymentTypeRequest;
import com.adins.esign.webservices.model.LovListRequest;
import com.adins.esign.webservices.model.LovListResponse;
import com.adins.esign.webservices.model.OfficeListEmbedRequest;
import com.adins.esign.webservices.model.OfficeListRequest;
import com.adins.esign.webservices.model.OfficeListResponse;
import com.adins.esign.webservices.model.PaymentSignTypeListRequest;
import com.adins.esign.webservices.model.PaymentSignTypeListResponse;
import com.adins.esign.webservices.model.RegionListEmbedRequest;
import com.adins.esign.webservices.model.RegionListRequest;
import com.adins.esign.webservices.model.RegionListResponse;
import com.adins.esign.webservices.model.VendorListEmbedRequest;
import com.adins.esign.webservices.model.VendorListInvitationRegisterRequest;
import com.adins.esign.webservices.model.VendorListRequest;
import com.adins.esign.webservices.model.VendorListResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssRequestType;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;


@Component
@Path("/data")
@Api(value = "DataService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericDataServiceEndpoint implements DataService {
	
	@Autowired private BusinessLineLogic businessLineLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private OfficeLogic officeLogic;
	@Autowired private PaymentSignTypeLogic paymentSignTypeLogic;
	@Autowired private VendorLogic vendorLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private RegionLogic regionLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private ProvinceLogic provinceLogic;
	@Autowired private SubDistrictLogic subDistrictLogic;
	@Autowired private DistrictLogic districtLogic;
	@Autowired private GeneralSettingLogic generalSettingLogic;

	
	@Override
	@POST
	@Path("/lov")
	public LovListResponse getLovList(LovListRequest request) {
		return this.commonLogic.getLovByGroupAndConstraint(request);
	}
	
	@Override
	@POST
	@Path("/lovembed")
	public LovListResponse getLovListEmbed(LovListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return this.commonLogic.getLovEmbedByGroupAndConstraint(request, audit);
	}

	@Override
	@POST
	@Path("/s/paymentSignType")
	public PaymentSignTypeListResponse getPaymentSignTypeList(PaymentSignTypeListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return paymentSignTypeLogic.getPaymentSignTypeList(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/ListPaymentType")
	public PaymentSignTypeListResponse getListPaymentType(GetListPaymentTypeRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return paymentSignTypeLogic.getListPaymentType(request, audit);
	}

	@Override
	@POST
	@Path("/s/vendor")
	public VendorListResponse getVendorList(VendorListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return vendorLogic.getVendorList(request, "", audit);
	}
	
	@Override
	@POST
	@Path("/vendor")
	public VendorListResponse getVendorUnsecureList(VendorListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return vendorLogic.getVendorList(request, "", audit);
	}
	
	@Override
	@POST
	@Path("/vendorEmbed")
	public VendorListResponse getVendorListEmbed(VendorListEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return vendorLogic.getVendorListEmbed(request, "",audit);
	}

	@Override
	@POST
	@Path("/s/tenant")
	public GetListTenantResponse getTenantList(GetListTenantRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getListTenantCodeAndName(request, audit);
	}

	@Override
	@POST
	@Path("/s/businessLineList")
	public BusinessLineListResponse getBusinessLineList(BusinessLineListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return businessLineLogic.getBusinessLineList(request, audit);
	}

	@Override
	@POST
	@Path("/s/officeList")
	public OfficeListResponse getOfficeList(OfficeListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return officeLogic.getOfficeList(request, audit);
	}

	@Override
	@POST
	@Path("/s/regionList")
	public RegionListResponse getRegionList(RegionListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return regionLogic.getRegionList(request, audit);
	}

	@Override
	@POST
	@Path("/officeListEmbed")
	public OfficeListResponse getOfficeListEmbed(OfficeListEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return officeLogic.getOfficeListEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/regionListEmbed")
	public RegionListResponse getRegionListEmbed(RegionListEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return regionLogic.getRegionListEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/vendorListInvReg")
	public VendorListResponse getVendorListInvReg(VendorListInvitationRegisterRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return vendorLogic.getVendorListInvReg(request, audit);
	}

	@Override
	@POST
	@Path("/s/allVendor")
	public VendorListResponse getAllVendorList(MssRequestType request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return vendorLogic.getAllVendorList(audit);
	}

	@Override
	@POST
	@Path("/addBalanceType")
	public AddBalanceTypeResponse addBalanceType(AddBalanceTypeRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return saldoLogic.addBalanceType(request, audit);
	}

	@Override
	@POST
	@Path("/editBalanceType")
	public AddBalanceTypeResponse editBalanceType(AddBalanceTypeRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return saldoLogic.editBalanceType(request, audit);
	}

	@Override
	@POST
	@Path("/s/getSubDistrict")
	public GetSubDistrictResponse getSubDistrictList(GetSubDistrictRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return subDistrictLogic.getSubDistrictList(request, audit);
	}
	
	@Override
	@POST
	@Path("/getSubDistrictEmbed")
	public GetSubDistrictResponse getSubDistrictListEmbed(GetSubDistrictEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return subDistrictLogic.getSubDistrictListEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/getSubDistrictByInvitation")
	public GetSubDistrictResponse getSubDistrictListByInvitation(GetSubDistrictByInvitationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return subDistrictLogic.getSubDistrictListByInvitation(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/getDistrict")
	public GetDistrictResponse getDistrictList(GetDistrictRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return districtLogic.getDistrictList(request, audit);
	}
	
	@Override
	@POST
	@Path("/getDistrictEmbed")
	public GetDistrictResponse getDistrictListEmbed(GetDistrictEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return districtLogic.getDistrictListEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/getDistrictByInvitation")
	public GetDistrictResponse getDistrictListByInvitation(GetDistrictByInvitationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return districtLogic.getDistrictListByInvitation(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/getProvince")
	public GetProvinceResponse getProvinceList(GetProvinceRequest request){
		AuditContext audit = request.getAudit().toAuditContext();
		return provinceLogic.getProvinceList(request, audit);
	}
	
	@Override
	@POST
	@Path("/getProvinceEmbed")
	public GetProvinceResponse getProvinceListEmbed(GetProvinceEmbedRequest request){
		AuditContext audit = request.getAudit().toAuditContext();
		return provinceLogic.getProvinceListEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/getProvinceByInvitation")
	public GetProvinceResponse getProvinceListInvReg(GetProvinceByInvitationRequest request){
		AuditContext audit = request.getAudit().toAuditContext();
		return provinceLogic.getProvinceListInvReg(request, audit);
	}

	@Override
	@POST
	@Path("/getListDocumentEMateraiType")
	public GetDocumentEMateraiTypeResponse getListDocumentEMateraiTypeEmbed(GetDocumentEMateraiTypeRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return commonLogic.getListPeruriDocTypeEmbed(request, audit);
	}

	
	
	@Override
	@POST
	@Path("/s/getListPeruriDocumentType")
	public GetDocumentEMateraiTypeResponse getListPeruriDocumentType(GetPeruriDocumentTypeRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return commonLogic.getListPeruriDocType(request, audit);
	}

	

	@Override
	@POST
	@Path("/s/getStatusStampingOtomatisTenant")
	public GetStatusStampingOtomatisTenantResponse getStatusStampingOtomatisTenant(GetStatusStampingOtomatisTenantRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getStatusStampingOtomatisTenant(request, audit);
	}
	
	
	@Override
	@POST
	@Path("/s/vendorV2")
	public VendorListResponse getVendorListV2(VendorListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return vendorLogic.getVendorListV2(request, "", audit);
	}

	@Override
	@POST
	@Path("/s/getListPSrESetting")
	public GetListPSrESettingResponse getListPSrESetting(GetListPSrESettingRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return vendorLogic.getListPSrESetting(request, audit);
	}

	@Override
	@POST
	@Path("/s/getPsrePriority")
	public GetPsrePriorityResponse getPsrePriority(GetPsrePriorityRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return vendorLogic.getPsrePriority(request, audit);
	}

	@Override
	@POST
	@Path("/s/getGeneralSetting")
	public GetGeneralSettingResponse getGeneralSetting(GetGeneralSettingRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return generalSettingLogic.getGeneralSetting(request, audit);
	}
}
