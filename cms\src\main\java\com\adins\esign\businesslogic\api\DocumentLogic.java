package com.adins.esign.businesslogic.api;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.am.model.AmMsuser;
import com.adins.esign.confins.model.UploadToCoreBean;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.<PERSON>Lov;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.custom.QueueDeleteBean;
import com.adins.esign.model.custom.SaveSignCallbackCompleteSIgnTekenAjaBean;
import com.adins.esign.model.custom.SaveSigningResultDecryptedBean;
import com.adins.esign.webservices.model.ActivationStatusByDocumentIdRequest;
import com.adins.esign.webservices.model.ActivationStatusByDocumentIdResponse;
import com.adins.esign.webservices.model.BulkSignDocumentEmbedRequest;
import com.adins.esign.webservices.model.BulkSignDocumentRequest;
import com.adins.esign.webservices.model.BulkSignDocumentResponse;
import com.adins.esign.webservices.model.CancelAgreementRequest;
import com.adins.esign.webservices.model.CancelAgreementResponse;
import com.adins.esign.webservices.model.CancelDigitalSignRequest;
import com.adins.esign.webservices.model.CancelDigitalSignResponse;
import com.adins.esign.webservices.model.CancelSignNormalRequest;
import com.adins.esign.webservices.model.CheckDocTemplateRequest;
import com.adins.esign.webservices.model.CheckDocTemplateResponse;
import com.adins.esign.webservices.model.CheckDocumentBeforeSigningRequest;
import com.adins.esign.webservices.model.CheckDocumentBeforeSigningResponse;
import com.adins.esign.webservices.model.CheckDocumentSendStatusEmbedRequest;
import com.adins.esign.webservices.model.CheckDocumentSendStatusRequest;
import com.adins.esign.webservices.model.CheckDocumentSendStatusResponse;
import com.adins.esign.webservices.model.CheckStampingStatusRequest;
import com.adins.esign.webservices.model.CheckStampingStatusResponse;
import com.adins.esign.webservices.model.CheckStatusSigningRequest;
import com.adins.esign.webservices.model.CheckStatusSigningResponse;
import com.adins.esign.webservices.model.DocumentExcelReportResponse;
import com.adins.esign.webservices.model.DocumentSignDetailsRequest;
import com.adins.esign.webservices.model.DocumentSignDetailsResponse;
import com.adins.esign.webservices.model.DocumentTemplateAddUpdateRequest;
import com.adins.esign.webservices.model.DocumentTemplateGetOneRequest;
import com.adins.esign.webservices.model.DocumentTemplateGetOneResponse;
import com.adins.esign.webservices.model.DocumentTemplateListEmbedRequest;
import com.adins.esign.webservices.model.DocumentTemplateListEmbedResponse;
import com.adins.esign.webservices.model.DocumentTemplateListRequest;
import com.adins.esign.webservices.model.DocumentTemplateListResponse;
import com.adins.esign.webservices.model.DocumentTemplateSignLocListEmbedRequest;
import com.adins.esign.webservices.model.DocumentTemplateSignLocListEmbedResponse;
import com.adins.esign.webservices.model.DownloadDocumentFullApiRequest;
import com.adins.esign.webservices.model.DownloadManualReportRequest;
import com.adins.esign.webservices.model.DownloadManualReportResponse;
import com.adins.esign.webservices.model.DownloadStampedFileFromDmsRequest;
import com.adins.esign.webservices.model.DownloadStampedFileFromDmsResponse;
import com.adins.esign.webservices.model.DummyClientURLUploadRequest;
import com.adins.esign.webservices.model.DummyClientURLUploadResponse;
import com.adins.esign.webservices.model.GetDocumentIdRequest;
import com.adins.esign.webservices.model.GetDocumentIdResponse;
import com.adins.esign.webservices.model.GetListReportRequest;
import com.adins.esign.webservices.model.GetListReportResponse;
import com.adins.esign.webservices.model.GetTotalUnsignedDocumentRequest;
import com.adins.esign.webservices.model.GetTotalUnsignedDocumentResponse;
import com.adins.esign.webservices.model.InsertDocumentManualSignRequest;
import com.adins.esign.webservices.model.InsertDocumentManualSignResponse;
import com.adins.esign.webservices.model.InsertDocumentStampingRequest;
import com.adins.esign.webservices.model.InsertDocumentStampingResponse;
import com.adins.esign.webservices.model.ListInquiryDocumentEmbedRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentNormalRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentResponse;
import com.adins.esign.webservices.model.RequestStampingRequest;
import com.adins.esign.webservices.model.RequestStampingResponse;
import com.adins.esign.webservices.model.ResendNotifSignRequest;
import com.adins.esign.webservices.model.ResendNotifSignResponse;
import com.adins.esign.webservices.model.ResendSignNotificationRequest;
import com.adins.esign.webservices.model.ResendSignNotificationResponse;
import com.adins.esign.webservices.model.ResumeSendDocumentRequest;
import com.adins.esign.webservices.model.ResumeSendDocumentResponse;
import com.adins.esign.webservices.model.RetryLatestStampFromUploadRequest;
import com.adins.esign.webservices.model.RetryLatestStampFromUploadResponse;
import com.adins.esign.webservices.model.RetryStampingEmbedRequest;
import com.adins.esign.webservices.model.RetryStampingEmbedResponse;
import com.adins.esign.webservices.model.RetryStampingMeteraiDocumentResponse;
import com.adins.esign.webservices.model.RetryStampingNormalRequest;
import com.adins.esign.webservices.model.RetryStampingNormalResponse;
import com.adins.esign.webservices.model.RetryStampingRequest;
import com.adins.esign.webservices.model.RetryStampingResponse;
import com.adins.esign.webservices.model.SaveManualStampRequest;
import com.adins.esign.webservices.model.SaveSignResultResponse;
import com.adins.esign.webservices.model.SignDocumentEmbedRequest;
import com.adins.esign.webservices.model.SignDocumentFullApiRequest;
import com.adins.esign.webservices.model.SignDocumentFullApiResponse;
import com.adins.esign.webservices.model.SignDocumentRequest;
import com.adins.esign.webservices.model.SignDocumentResponse;
import com.adins.esign.webservices.model.SignLinkRequest;
import com.adins.esign.webservices.model.SignLinkResponse;
import com.adins.esign.webservices.model.SignLocationRequest;
import com.adins.esign.webservices.model.SignerListEmbedRequest;
import com.adins.esign.webservices.model.SignerListRequest;
import com.adins.esign.webservices.model.SignerListResponse;
import com.adins.esign.webservices.model.StampDocumentRequest;
import com.adins.esign.webservices.model.StampDocumentResponse;
import com.adins.esign.webservices.model.StartStampingMeteraiRequest;
import com.adins.esign.webservices.model.StartStampingMeteraiResponse;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptResponse;
import com.adins.esign.webservices.model.embed.RetryLatestStampFromUploadEmbedRequest;
import com.adins.esign.webservices.model.external.CancelDocumentExternalRequest;
import com.adins.esign.webservices.model.external.GetSignLinkExternalRequest;
import com.adins.esign.webservices.model.external.GetSignLinkExternalResponse;
import com.adins.esign.webservices.model.external.GetTemplateSignLocationRequest;
import com.adins.esign.webservices.model.external.GetTemplateSignLocationResponse;
import com.adins.esign.webservices.model.external.InsertStampingMateraiExternalRequest;
import com.adins.esign.webservices.model.external.SigningHashFileRequest;
import com.adins.esign.webservices.model.external.SigningHashFileResponse;
import com.adins.exceptions.EntityNotFoundException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;

public interface DocumentLogic {
	// Document Template
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isValidTenant(#tenantCode, authentication) and @esignSecurityServices.isRoleTenant('ROLE_DOC_TEMPLATE', #tenantCode, authentication)")
	DocumentTemplateListResponse listDocumentTemplate(String docTempCode, String docTempName, String searchIsActive, int page, int pageSize, String tenantCode, AuditContext audit);
	
	DocumentTemplateListEmbedResponse listDocumentTemplateEmbed(DocumentTemplateListEmbedRequest request, AuditContext audit);
	
	DocumentTemplateSignLocListEmbedResponse listDocumentTemplateSignLocEmbed(DocumentTemplateSignLocListEmbedRequest request, AuditContext audit);
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isValidTenant(#docRequest.tenantCode, authentication) and @esignSecurityServices.isRoleTenant('ROLE_DOC_TEMPLATE', #docRequest.tenantCode, authentication)")
	void insertDocumentTemplate(DocumentTemplateAddUpdateRequest docRequest, AuditContext audit) throws EntityNotFoundException;
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isValidTenant(#docRequest.tenantCode, authentication) and @esignSecurityServices.isRoleTenant('ROLE_DOC_TEMPLATE', #docRequest.tenantCode, authentication)")
	DocumentTemplateGetOneResponse getDocumentTemplate(DocumentTemplateGetOneRequest docRequest, AuditContext audit);
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isValidTenant(#docRequest.tenantCode, authentication) and @esignSecurityServices.isRoleTenant('ROLE_DOC_TEMPLATE', #docRequest.tenantCode, authentication)")
	void updateDocumentTemplate(DocumentTemplateAddUpdateRequest docRequest, AuditContext audit);
	
	@RolesAllowed("ROLE_DOC_TEMPLATE")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	MsDocTemplate getDocumentTemplateByCodeAndTenantCode(String documentTemplateCode, String tenantCode, AuditContext auditContext);
	
	@RolesAllowed("ROLE_DOC_TEMPLATE")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	void insertSignLocation(SignLocationRequest signLocReq, short seqNo, AuditContext auditContext);
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isValidTenant(#docRequest.tenantCode, authentication) and @esignSecurityServices.isRoleTenant('ROLE_DOC_TEMPLATE', #docRequest.tenantCode, authentication)")
	void updateSignLocation(DocumentTemplateAddUpdateRequest docRequest, AuditContext audit);
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isValidTenant(#request.tenantCode, authentication) and @esignSecurityServices.isRoleTenant('ROLE_DOC_TEMPLATE', #request.tenantCode, authentication)")
	CheckDocTemplateResponse checkDocTemplateExist(CheckDocTemplateRequest request, AuditContext audit);

	// DocumentH
	@RolesAllowed("ROLE_CORESYSTEM")
	TrDocumentH insertDocumentH(String referenceNo, AmMsuser user, MsOffice office, MsTenant tenant, int totalDocument, int totalSigned, String docType, String urlSuccess, String urlUpload, MsBusinessLine businessLine, AuditContext callerId);

	// DocumentD
	@RolesAllowed({"ROLE_INQUIRY","ROLE_DASHBOARD","ROLE_INBOX"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	TrDocumentD getDocumentDetailById(long idDocInserted);
	
	@RolesAllowed({"ROLE_INQUIRY","ROLE_DASHBOARD","ROLE_FEEDBACK","ROLE_INBOX"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	TrDocumentD getDocumentDetailByDocumentId(String docId);
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	TrDocumentD getDocumentDetailByDocumentIdEmbed(String docId);
	
	@RolesAllowed({"ROLE_INQUIRY","ROLE_DASHBOARD","ROLE_INBOX"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	List<Map<String, Object>> getOtherDocDetailNeedSign(String email, String documentId, AuditContext audit);
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	List<Map<String, Object>> getOtherDocDetailNeedSignEmbed(String email, String documentId, AuditContext audit);
	
	@RolesAllowed({"ROLE_INQUIRY"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ListInquiryDocumentResponse getListInquiryDocument(ListInquiryDocumentRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	ListInquiryDocumentResponse getListInquiryDocumentEmbed(ListInquiryDocumentEmbedRequest request, AuditContext audit);
	
	@RolesAllowed({"DOCUMENT_MONITORING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ListInquiryDocumentResponse getListInquiryDocumentNormal(ListInquiryDocumentNormalRequest request, AuditContext auditContext);
	
	@RolesAllowed({"ROLE_INQUIRY","ROLE_DASHBOARD","ROLE_INBOX", "ROLE_DOCUMENT_MONITORING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	SignerListResponse getDocumentSigners(SignerListRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	SignerListResponse getDocumentSignersEmbed(SignerListEmbedRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_INQUIRY","ROLE_DASHBOARD","ROLE_INBOX"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	boolean isUserSignedDocument(String documentId, Long idUser);
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	boolean isUserSignedDocumentEmbed(String documentId, Long idUser);
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	ResendSignNotificationResponse resendSignNotification(ResendSignNotificationRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_DOCUMENT_MONITORING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ResendNotifSignResponse resendNotifSignResponse(ResendNotifSignRequest request, AuditContext audit);
	
	// getdocvida
	@RolesAllowed({"ROLE_INQUIRY","ROLE_DASHBOARD","ROLE_INBOX"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	List<Map<String, Object>> getOtherDocDetailNeedSignVida(AmMsuser user, String documentId,AuditContext audit);
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and hasAnyRole('ROLE_INQUIRY','ROLE_DASHBOARD','ROLE_INBOX','ROLE_DOCUMENT_MONITORING')")
	ViewDocumentResponse viewDocument(ViewDocumentRequest request, AuditContext audit);
	ViewDocumentResponse viewDocumentEmbed(ViewDocumentRequest request, AuditContext audit);
	ViewDocumentResponse viewDocumentWithoutSecurity(ViewDocumentRequest request, AuditContext audit);
	
	DocumentExcelReportResponse exportDocumentReportEmbed(ListInquiryDocumentEmbedRequest request, AuditContext audit);
	CheckDocumentSendStatusResponse checkDocSendStatus(CheckDocumentSendStatusRequest request, AuditContext audit);
	CheckDocumentSendStatusResponse checkDocSendStatusEmbed(CheckDocumentSendStatusEmbedRequest request, AuditContext audit);
	ResumeSendDocumentResponse resumeSendDocument(ResumeSendDocumentRequest request, AuditContext audit) throws IOException, ParseException;
	
	// Sign Document
	@RolesAllowed({"ROLE_INQUIRY","ROLE_DASHBOARD","ROLE_INBOX"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	SignDocumentResponse signDocument(SignDocumentRequest request, AuditContext audit) throws IOException, ParseException;
	
	SignDocumentResponse signDocumentWithoutSecurity(SignDocumentRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	SignDocumentResponse signDocumentEmbed(SignDocumentEmbedRequest request, AuditContext audit) throws IOException, ParseException;
	
	@RolesAllowed({"ROLE_INQUIRY","ROLE_DASHBOARD","ROLE_INBOX"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#request.loginId, authentication)")
	BulkSignDocumentResponse bulkSignDocument(BulkSignDocumentRequest request, AuditContext audit) throws IOException, ParseException;
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	BulkSignDocumentResponse bulkSignDocumentEmbed(BulkSignDocumentEmbedRequest request, AuditContext audit) throws IOException, ParseException;
	
	@RolesAllowed({"ROLE_INQUIRY","ROLE_DASHBOARD","ROLE_INBOX"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	boolean needSignOtherDocInAgreement(String email, String documentId);
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	boolean needSignOtherDocInAgreementEmbed(String email, String documentId);
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isValidTenant(#request.tenantCode, authentication) and @esignSecurityServices.isRoleTenant('ROLE_INQ_USER', #request.tenantCode, authentication)")
	SignLinkResponse getUserSignLink(SignLinkRequest request, AuditContext audit);
	
	// bulksignDocument Without Security
	BulkSignDocumentResponse bulkSignDocumentWithoutSecurity(BulkSignDocumentRequest request, AuditContext audit) throws IOException, ParseException;

	// Full Api
	SignDocumentFullApiResponse signDocumentFullApi(SignDocumentFullApiRequest request, String xApiKey, AuditContext audit) throws IOException, ParseException;
	ViewDocumentResponse downloadDocumentFullApi(DownloadDocumentFullApiRequest request,  String xApiKey, AuditContext audit);
	
	// Cancel Sign Document
	@RolesAllowed({"ROLE_ANONYMOUS"})
	CancelDigitalSignResponse cancelDigitalSign(CancelDigitalSignRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DOCUMENT_MONITORING")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	MssResponseType cancelSignNormalRequest(CancelSignNormalRequest request, AuditContext audit);
	
	// CONFINS
	@RolesAllowed("ROLE_CORESYSTEM")
	SaveSignResultResponse saveDocumentSignResult(SaveSigningResultDecryptedBean bean, AuditContext audit) throws IOException;
	SaveSignResultResponse saveDocumentSignResultWithoutRolesAllowed(SaveSigningResultDecryptedBean bean, AuditContext audit) throws IOException;
	@RolesAllowed({"ROLE_CORESYSTEM"})
	CancelAgreementResponse cancelDocument(CancelAgreementRequest request, AuditContext audit);
	MssResponseType cancelAgreementExternal(CancelDocumentExternalRequest request, String xApiKey, AuditContext audit);
	Status callUrlUpload(String url, UploadToCoreBean bean);
	Status callUrlSuccess(TrDocumentH documentH, AuditContext audit);
	void updateCallbackProcess(TrDocumentH docH, Short callbackProcess, AuditContext audit);
	void updateCallbackProcessNewTran(TrDocumentH docH, Short callbackProcess, AuditContext audit);
	void updateCallbackProcessNewTranNative(TrDocumentH docH, Short callbackProcess, AuditContext audit);
	void updateCallbackProcessAndRetryResumeAttemptNumNewTranNative(TrDocumentH docH, Short callbackProcess, Short retryResumeAttemptNumNewTranNative, AuditContext audit);
	UploadToCoreBean prepareUploadToCoreData(TrDocumentH docH, AuditContext audit) throws IOException;
	Status callUrlRerunSendDocument(String rerunSendDocUrl, String refNumber);
	
	// TekenAja Document
	@RolesAllowed("ROLE_CORESYSTEM")
	SaveSignResultResponse saveDocumentSignTekenAjaResult(SaveSignCallbackCompleteSIgnTekenAjaBean bean, AuditContext audit) throws IOException;
	SaveSignResultResponse saveDocumentSignResultTekenAjaWithoutRolesAllowed(SaveSignCallbackCompleteSIgnTekenAjaBean bean, AuditContext audit) throws IOException;
	void deleteFromOssLogic(QueueDeleteBean fileName);

	GetDocumentIdResponse getDocumentId(GetDocumentIdRequest request, AuditContext audit);
	String getRefNumberByPsreId(String psreDocument);
	MsLov getLovSignStatusByPsreId(String psreDocument);
	String getDocumentIdPsreId(String psreDocument);
	
	ActivationStatusByDocumentIdResponse activationStatusByDocumentId(ActivationStatusByDocumentIdRequest request, AuditContext audit) throws IOException, ParseException;
	ActivationStatusByDocumentIdResponse activationStatusByDocumentIdEmbed(ActivationStatusByDocumentIdRequest request, AuditContext audit) throws IOException, ParseException;

	public void resumeSendDocument(String documentId, AuditContext audit) throws IOException, ParseException;
	
	// Document Stamping
	@RolesAllowed({"ROLE_CORESYSTEM"})
	RetryStampingResponse retryStamping(RetryStampingRequest request, AuditContext audit) throws IOException, ParseException;
	RetryStampingEmbedResponse retryStampingEmbed(RetryStampingEmbedRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_EMETERAI_MONITORING")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	RetryStampingNormalResponse retryStampingNormal(RetryStampingNormalRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_EMETERAI_MONITORING")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	RetryLatestStampFromUploadResponse retryLatestStampFromUpload(RetryLatestStampFromUploadRequest request, AuditContext audit);
	RetryLatestStampFromUploadResponse retryLatestStampFromUploadEmbed(RetryLatestStampFromUploadEmbedRequest request, AuditContext audit);
	
	// API insert document stamping emeterai untuk menu embed
	InsertDocumentStampingResponse insertDocumentStamping(InsertDocumentStampingRequest request, AuditContext audit);
//	@RolesAllowed("ROLE_MANUAL_SIGN")
	InsertDocumentManualSignResponse insertDocumentManualSign(InsertDocumentManualSignRequest request, AuditContext audit) throws ParseException, IOException;
	// API insert document stamping emeterai untuk eksternal
	StampDocumentResponse stampDocument(StampDocumentRequest request, String xApiKey, AuditContext audit);
	
	@RolesAllowed({"ROLE_DOCUMENT_MONITORING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	StartStampingMeteraiResponse startStampingMeterai(StartStampingMeteraiRequest request, AuditContext audit);
	
	//dummy url client
	DummyClientURLUploadResponse dummyClientUrlUpload(DummyClientURLUploadRequest request, String token, AuditContext audit);
	DocumentTemplateListEmbedResponse getFilterListDocumentTemplate(DocumentTemplateListRequest request, AuditContext audit);
//	@RolesAllowed({"ROLE_EMETERAI_MONITORING"})
	DownloadStampedFileFromDmsResponse downloadStampedDocumentFromDms(DownloadStampedFileFromDmsRequest request, AuditContext audit) throws IOException;
	
	CheckDocumentBeforeSigningResponse checkDocumentBeforeSigning(CheckDocumentBeforeSigningRequest request, AuditContext audit);
	
	RequestStampingResponse requestStamping(RequestStampingRequest request, String xApiKey, AuditContext audit);
	
	CheckStatusSigningResponse checkStatusSigning(CheckStatusSigningRequest request, String xApiKey, AuditContext audit);
	

	CheckStampingStatusResponse checkStampingStatus (CheckStampingStatusRequest request,String apiKey, AuditContext audit);
	
	DocumentSignDetailsResponse getDocumentSignDetails(DocumentSignDetailsRequest request, AuditContext audit);
	
	//v2 tafs
	@RolesAllowed({"ROLE_ANONYMOUS"})
	ListInquiryDocumentResponse getListInquiryDocumentEmbedV2(ListInquiryDocumentEmbedRequest request, AuditContext audit);
	
//	GetTotalunsignedDocument
	GetTotalUnsignedDocumentResponse getTotalUnsignedDoc (GetTotalUnsignedDocumentRequest request, String apiKey ,AuditContext audit);
	
	
	@RolesAllowed({"ROLE_DOCUMENT_MONITORING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	RetryStampingMeteraiDocumentResponse retryStampingMeteraiDocument(RetryStampingRequest request, AuditContext audit);
	
	InsertDocumentStampingResponse saveManualStamp(SaveManualStampRequest request, AuditContext audit);
	
	GetSignLinkExternalResponse getSignLinkExternal (GetSignLinkExternalRequest request, String xApiKey, AuditContext audit);
	GetTemplateSignLocationResponse getTemplateSignLocation (GetTemplateSignLocationRequest request, String xApiKey, AuditContext audit);
	
	SigningHashFileResponse signingHashFile(SigningHashFileRequest request, String xApiKey, AuditContext audit);
	
	InsertStampingPaymentReceiptResponse insertStampMateraiExternal(InsertStampingMateraiExternalRequest request, String xApiKey, AuditContext audit) throws ParseException;

	void restoreDocument(String filename, String days, TrDocumentD document, MsLov processRestore, AuditContext audit);
}
