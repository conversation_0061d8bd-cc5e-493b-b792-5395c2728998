package com.adins.esign.validatorlogic.api;

import com.adins.am.model.AmMsuser;
import com.adins.esign.constants.enums.RegistrationType;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterByInvitationRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

public interface UserValidatorLogic {
	// Later will be replaced by validateGetUserByEmailv2
	AmMsuser validateGetUserByEmail(String email, boolean checkUserExistence, AuditContext audit);
	
	/**
	 * Get am_msuser from ms_vendor_registered_user by email.
	 * If there are 2 id_ms_user, will throw UserException
	 * @param checkUserExistence If true and id_ms_user is not found, will throw UserException. If false, will return null instead
	 */
	// Later will replace validateGetUserByEmail
	AmMsuser validateGetUserByEmailv2(String email, boolean checkUserExistence, AuditContext audit);
	AmMsuser validateGetUserByEmailAndVendorCode(String email, boolean checkUserExistence, String vendorCode, AuditContext audit);
	AmMsuser validateGetUserByEmailv2NewTrx(String email, boolean checkUserExistence, AuditContext audit);
	
	/**
	 * Get am_msuser from ms_vendor_registered_user by phone number.
	 * If there are 2 id_ms_user, will throw UserException
	 * @param checkUserExistence If true and id_ms_user is not found, will throw UserException. If false, will return null instead
	 */
	AmMsuser validateGetUserByPhone(String phone, boolean checkUserExistence, AuditContext audit);
	AmMsuser validateGetUserByPhoneAndVendorCode(String phone, boolean checkUserExistence, String vendorCode, AuditContext audit);
	AmMsuser validateGetUserByPhoneAndEmail(String phone, String email, boolean checkUserExistence, AuditContext audit);
	
	AmMsuser validateGetUserByNik(String nik, boolean checkUserExistence, AuditContext audit);
	
	void validateTekenAjaRegisterRequest(TekenAjaRegisterRequest request, RegistrationType registerType, AuditContext audit);
	void validateTekenAjaRegisterByInvRequest(TekenAjaRegisterByInvitationRequest request, RegistrationType registrationType, AuditContext audit);
	void validateGetUserDataCaller(String loginId, String tenantCode, AuditContext audit);
	void validateRegisterUserBean(UserBean userData, AuditContext audit);
	void validateUserByPhoneWithEmail(long idMsUserByPhone, String phoneNo, String email, boolean checkUserExistence, AuditContext audit);
	
	// Register param validation
	void validateExternalRegisterRequestParam(RegisterExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit);
	void validateNikPhoneEmailForRegister(String nik, String phone, String email, MsVendor vendor, AuditContext audit);
	Status getValidateNikPhoneEmailForRegisterMessage(String nik, String phone, String email, MsVendor vendor, AuditContext audit);
	
	void validateNikPhoneEmailForRegisterDefaultVendor(String nik, String phone, String email, MsVendor vendor, AuditContext audit);
	Status validateNikPhoneEmailForRegisterDefaultVendorStatus(String nik, String phone, String email, MsVendor vendor, AuditContext audit);
	void validateRegistrationAttemptAmount(TrInvitationLink invLink, AuditContext audit);
	
	MsVendorRegisteredUser validateGetVrUserByNikForActivationLinkExternal(String idKtp, AuditContext audit);
	
	MsVendorRegisteredUser getLatestVendorRegisteredUserByEmail(String email, boolean checkUserExistence, AuditContext audit);
	MsVendorRegisteredUser getLatestVendorRegisteredUserByPhone(String phone, boolean checkUserExistence, AuditContext audit);
	
	boolean usePsreMandatoryParamOnly(MsTenant tenant);
	void removeUnnecessaryRegisterExternalParam(RegisterExternalRequest request, MsTenant tenant);
	void removeUnnecessaryRegisterParam(UserBean userData, MsTenant tenant);
	
	boolean isCertifExpiredForSign(MsVendorRegisteredUser vendorUser, AuditContext audit);
	boolean isCertifExpiredForRegister(MsVendorRegisteredUser vendorUser, AuditContext audit);
	boolean isCertifExpiredForInquiry(MsVendorRegisteredUser vendorUser);
	
	boolean isCertifPoaExpiredForInquiry(MsVendorRegisteredUser vendorUser);
	
	void validateVendorRegisteredUserDataState(MsVendorRegisteredUser vru, String nik, String email, String phone, AuditContext audit);
}
