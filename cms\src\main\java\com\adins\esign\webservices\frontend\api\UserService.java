package com.adins.esign.webservices.frontend.api;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;

import javax.mail.MessagingException;
import javax.security.auth.login.LoginException;

import com.adins.esign.model.custom.GenerateInvLinkRequest;
import com.adins.esign.model.custom.GenerateInvLinkResponse;
import com.adins.esign.webservices.model.*;
import com.adins.esign.webservices.model.digisign.DigisignRegisterEmbedRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterEmbedRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterResponse;

public interface UserService {
	LoginUserResponse getUserProfiles(LoginUserRequest request) throws LoginException;

	UserAutoFillDataResponse getUserAutoFillData(UserAutoFillDataRequest request) throws IOException, ParseException;
	
	PreRegisterResponse preRegisterEsignUser(PreRegisterRequest request) throws NoSuchAlgorithmException;
	
	ActivationLinkResponse getActivationLink(ActivationLinkRequest activationLinkRequest);
	ActivationLinkResponse getActivationLinkEmbed(ActivationLinkEmbedRequest request);
	ActivationLinkResponse getActivationLinkInvReg(ActivationLinkEmbedRequest request);
	ResendActLinkResponse resendActivationLinkForUser(ResendActLinkForUserRequest request);
	ResendActLinkResponse resendActivationLinkForInvi(ResendActLinkForInviRequest request);
	
	MenuResponse getUserMenu(MenuRequest menuRequest);
	SaveUserActivationResponse updateUserActivation(SaveUserActivationRequest request) throws IOException;
	SaveUserActivationResponse updateUserActivationV2(SaveUserActivationRequest request) throws IOException;

	ListInquiryUserResponse getListUser(ListInquiryUserRequest request) throws ParseException;
	
	ChangePasswordResponse changePassword(ChangePasswordRequest request);
	ForgotPasswordResponse forgotPassword(ForgotPasswordRequest request);
	ResetPasswordResponse resetPassword(ResetPasswordRequest request);
	
	SendOtpEmailResponse sendOtpEmail(SendOtpEmailRequest request);
	SendOtpEmailResponse sendOtpEmailEmbed(SendOtpEmailEmbedRequest request);
	SendOtpEmailResponse sendOtpEmailInvitation(SendOtpEmailEmbedRequest request);
	
	OtpVerificationResponse verifyOtp(OtpVerificationRequest request);
	OtpVerificationResponse verifyOtpEmbed(OtpVerificationEmbedRequest request);
	OtpVerificationResponse verifyOtpInvitation(OtpVerificationEmbedRequest request);
	OtpVerifChangeProfileResponse verifyOtpChangeProfile(OtpVerifChangeProfileRequest request);
	ResetCodeVerificationResponse verifyResetCode(ResetCodeVerificationRequest request);
	
	ActivationStatusResponse getUserActivationStatus(ActivationStatusRequest request);
	EmailServiceStatusResponse checkUserEmailService(EmailServiceStatusRequest request);
	EmailServiceStatusResponse checkUserEmailServiceEmbed(EmailServiceStatusEmbedRequest request);
	
	LinkResetPasswordResponse getResetPasswordLink(LinkResetPasswordRequest request);
	CheckRegisterStatusResponse checkRegisterStatus(CheckRegisterStatusRequest request);
	CheckRegisterAutoFillResponse checkRegisterAutoFill(CheckRegisterAutoFillRequest request);
	
	// Registration Invitation
	GenerateInvLinkResponse generateAndSendInvitationLink(GenerateInvLinkRequest request) throws Exception;
	GenerateInvLinkResponse generateAndSendInvitationLinkV2(GenerateInvLinkRequest request) throws Exception;
	GenerateInvLinkResponse generateAndSendInvitationLinkSecured(GenerateInvLinkRequest request) throws Exception;
	GenerateInvLinkResponse generateAndSendInvitationLinkSecuredV2(GenerateInvLinkRequest request) throws Exception;
	InvitationRegisterDataResponse checkInvitationCode(InvitationRegisterDataRequest request);
	InvitationRegisterStatusResponse checkInvitationRegisterStatus(InvitationRegisterStatusRequest request);
	ResendRegisterInvitationResponse resendRegisterInvitation(ResendRegisterInvitationRequest request) throws UnsupportedEncodingException;
	ListInvitationResponse getListInvitation(ListInvitationRequest request) throws ParseException;
	InvitationLinkResponse getInvitationLink(InvitationLinkRequest request);
	ListInvitationExcelReportResponse exportInvitationListReport(ListInvitationExcelReportRequest request) throws IOException;
	GenerateInvitationLinkForExpiredCertResponse generateInvLinkECertExpired(GenerateInvitationLinkForExpiredCertRequest request) throws UnsupportedEncodingException;
	
	// Digisign Registration v1
	RegistrationResponse doRegisterUser(RegistrationRequest regisRequest) throws IOException;
	RegistrationResponse doRegisterUserEmbed(RegistrationEmbedRequest regisRequest) throws IOException;
	RegistrationResponse registerBM(RegistrationRequest request) throws IOException;
	
	// Digisign Registration v2
	RegisterResponse registerByInvitation(RegistrationByInvitationRequest request);
	RegisterResponse registerEmbedV2(DigisignRegisterEmbedRequest request);
	
	// TekenAja Registration, later will be deleted
	TekenAjaRegisterResponse registerTekenAja(TekenAjaRegisterRequest request);
	RegisterResponse registerEmbedTekenAja(TekenAjaRegisterEmbedRequest request);
	
	GetUserPhotoResponse getUserPhoto(GetUserPhotoRequest request);	
	SendChangeProfileOTPResponse sendChangeProfileOtpResponse(SendChangeProfileOTPRequest request) throws IOException;
	
	ChangeProfileDataResponse changeUserProfileData(ChangeProfileDataRequest request);
	
	UpdateUserDataResponse updateUserData(UpdateUserDataRequest request);
	DataInvRegisResponse getDataInvRegis(DataInvRegisRequest request);
	UpdateInvDataResponse updateDataInvRegis(UpdateInvDataRequest request) throws IOException;
	
	MyProfileResponse getMyProfile(MyProfileRequest request);
	
	// Inquiry Edit User Data
	InquiryEditUserResponse getInquiryEditUser(InquiryEditUserRequest request);
	
	UpdateUserResponse updateUser(UpdateUserRequest request) throws ParseException, IOException;
	CreateEmailResponse createEmail(CreateEmailRequest request) throws IOException;
	
	//return link activation TekenAja
	ReturnlinkAktivasiTekenAjaResponse returnLinkAktivasi(ReturnlinkAktivasiTekenAjaRequest request);

	ResetOtpCodeResponse resetOtpCode(ResetOtpCodeRequest request);
	
	//Reregistration User
	UpdateReregisResponse updateReregistrationUser(UpdateReregisRequest request);
	UpdateReregisResponse updateReregistrationUserEmbed(UpdateReregisRequest request);

	ActivationStatusResetPasswordResponse getUserActivationStatusResetPassword(ActivationStatusResetPasswordRequest request);
	
	UpdateDataUserResponse updateDataUser(UpdateDataUserRequest request);

	GetAllUserResponse getAllUserData();
	
	EditActivationStatusResponse editActivationStatus(EditActivationStatusRequest request);
	
	CheckPasswordComplexityResponse checkPasswordComplexity(CheckPasswordComplexityRequest request);
	
	GetSignerDetailResponse getSignerDetail(GetSignerDetailRequest request);
	
	GetUserActDataResponse getUserActData(GetUserActDataRequest request);
	
	SignerDataVerificationResponse signerDataVerification(SignerDataVerificationRequest request);
	
	SentOtpActivationUserResponse sentOtpActivationUser(SentOtpActivationUserRequest request);
	
	VerifyOtpActivationUserResponse verifyOtpActivationUser(VerifyOtpActivationUserRequest request);
	
	SentOtpSigningVerificationResponse sentOtpSigningVerification(SentOtpSigningVerificationRequest request);
	
	VerifyOtpSigningVerificationResponse verifyOtpSigningVerification(VerifyOtpSigningVerificationRequest request);
	
	UpdateActivationUserResponse updateActivationUser(UpdateActivationUserRequest request);
	
	VerifyLivenessFaceCompareResponse verifyLivenessFaceCompare(VerifyLivenessFaceCompareRequest request);
	
	GetSignerDetailWebviewResponse getSignerDetailWebview(GetSignerDetailWebviewRequest request);
	
	RegenerateInvitationResponse regenerateInvitation(RegenerateInvitationRequest request) throws MessagingException, IOException;
	GetUrlForwarderResponse getUrlForwarder(GetUrlForwarderRequest request);
	
	// view otp
	ListUserViewOtpResponse getListUserViewOtp(ListUserViewOtpRequest request);
	ViewResetCodeResponse viewResetCode(ViewResetCodeRequest request);
	ViewOtpResponse viewOtp ( ViewOtpRequest request);
	decryptUserDataResponse decryptUserData(decryptUserDataRequest request);
}
