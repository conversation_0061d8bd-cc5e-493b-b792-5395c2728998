package com.adins.esign.businesslogic.api;

import java.io.File;
import java.nio.file.Path;
import java.util.Map;

import com.adins.esign.constants.enums.ImageStorageLocation;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface ImageStorageLogic {
    
    public ImageStorageLocation retrieveGsImageLocation(AuditContext auditContext);
    
    public Path retrieveGsImageFileSystemPath(AuditContext auditContext);
        
    public byte[] retrieveImageBlob(long uuidTaskD, boolean isFinal);
    
    /**
     * 
     * @param file
     * @return
     *  <code>null</code> if file not found instead of FileNotFoundException
     */
    public byte[] retrieveImageFileSystemByFile(File file);
    
    /**
     * 
     * @param path
     * @return
     *  <code>null</code> if file not found instead of FileNotFoundException
     */
    public byte[] retrieveImageFileSystemByPath(Path path);
    
    public byte[] retrieveImageDms(Map<String, Object> dmsParameters);
    
    /**
     * Store <b>image</b> to <b>path</b>
     * Subfolder will be created if <b>subFolder</b> argument found. 
     * 
     * @param image     binary of the image
     * @param path      path for storing image
     * @param fileName  image file name complete with extension
     * @return
     *      absolutePath to image
     */
    public String storeImageFileSystem(byte[] image, Path path, String fileName);
    
    public Map<String, Object> storeImageDms(byte[] image, Map<String, Object> dmsParameters);

}
