package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class CallbackException extends AdInsException {
	private static final long serialVersionUID = 1L;
	
	public enum ReasonCallback {
		TOKEN_NOT_MATCH	
	}
	
	private final ReasonCallback reason;
	
	public CallbackException(ReasonCallback reason) {
		this.reason = reason;
	}

	public CallbackException(String message, ReasonCallback reason) {
		super(message);
		this.reason = reason;
	}

	public CallbackException(Throwable ex, ReasonCallback reason) {
		super(ex);
		this.reason = reason;
	}

	public CallbackException(String message, Throwable ex, ReasonCallback reason) {
		super(message, ex);
		this.reason = reason;
	}

	public ReasonCallback getReason() {
		return reason;
	}


	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
			case TOKEN_NOT_MATCH:
				return StatusCode.TOKEN_NOT_MATCH;
			}
		}
		return StatusCode.UNKNOWN;
	}

}
