package com.adins.esign.dataaccess.impl;

import static org.junit.Assert.assertEquals;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.model.AmGeneralsetting;
import com.adins.esign.businesslogic.api.TestSetUpLogic;
import com.adins.esign.dataaccess.api.GeneralSettingDao;

@Disabled
@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class GeneralSettingDaoHbnTest{
	AmGeneralsetting gensetJunit;
	
	@Autowired
	GeneralSettingDao gensetDao;
	public GeneralSettingDao getGensetDao() {
		return gensetDao;
	}

	public void setGensetDao(GeneralSettingDao gensetDao) {
		this.gensetDao = gensetDao;
	}
	
	@Autowired
	TestSetUpLogic setupLogic;
	
	@BeforeEach
	public void setUp() {
//		gensetJunit = setupLogic.setUpGenSet("GSCODEJUNIT");
	}
	
	@Order(1)
	@Test
	void getGsValueByCodeTest() {
		String gsValue = gensetDao.getGsValueByCode(gensetJunit.getGsCode());
		assertEquals(gensetJunit.getGsValue(), gsValue);
		
	}
	
	@Order(2)
	@Test
	void getGsValueByCodeNullTest() {
		String gsValue = gensetDao.getGsValueByCode("INVALIDCODE");
		assertEquals(StringUtils.EMPTY, gsValue);
		
	}


}
