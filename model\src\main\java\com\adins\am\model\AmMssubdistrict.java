package com.adins.am.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "am_mssubdistrict")
public class AmMssubdistrict extends UpdateableEntity {

	public static final String ID_MSSUBDISTRICT_HBM = "idMssubdistrict";
	public static final String SUBDISTRICT_ID_HBM = "subdistrictId";
	public static final String SUBDISTRICT_NAME_HBM = "subdistrictName";
	
	private long idMssubdistrict;
	private String subdistrictName;
	private long subdistrictId;
	private AmMsdistrict amMsdistrict;

	
	public AmMssubdistrict() {
		
	}
	
	public AmMssubdistrict(long idMssubdistrict, String subdistrictName, long subdistrictId, long idMsdistrict,
			String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd, AmMsdistrict amMsdistrict) {
		super();
		this.idMssubdistrict = idMssubdistrict;
		this.subdistrictName = subdistrictName;
		this.subdistrictId = subdistrictId;
		this.amMsdistrict = amMsdistrict;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
	}
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_mssubdistrict", unique = true, nullable = false)
	public long getIdMssubdistrict() {
		return idMssubdistrict;
	}

	public void setIdMssubdistrict(long idMssubdistrict) {
		this.idMssubdistrict = idMssubdistrict;
	}
	
	@Column(name = "subdistrict_name", length = 70)
	public String getSubdistrictName() {
		return subdistrictName;
	}

	public void setSubdistrictName(String subdistrictName) {
		this.subdistrictName = subdistrictName;
	}
	
	@Column(name = "subdistrict_id")
	public long getSubdistrictId() {
		return subdistrictId;
	}

	public void setSubdistrictId(long subdistrictId) {
		this.subdistrictId = subdistrictId;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_msdistrict", nullable = false)
	public AmMsdistrict getAmMsdistrict() {
		return amMsdistrict;
	}

	public void setAmMsdistrict(AmMsdistrict amMsdistrict) {
		this.amMsdistrict = amMsdistrict;
	}
	


}
