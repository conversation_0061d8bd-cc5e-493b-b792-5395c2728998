package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.dataaccess.api.JobCheckRegisterStatusDao;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrJobCheckRegisterStatus;
import com.adins.esign.util.MssTool;

@Transactional
@Component
@SuppressWarnings("unchecked")
public class JobCheckRegisterStatusDaoHbn extends BaseDaoHbn implements JobCheckRegisterStatusDao {

	@Override
	public List<TrJobCheckRegisterStatus> getUnprocessedJobCheckRegStatus() {
		Object[][] params = {{Restrictions.eq("requestStatus", (short) 0)}};
		return (List<TrJobCheckRegisterStatus>) this.managerDAO.list(TrJobCheckRegisterStatus.class, params, null)
				.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrJobCheckRegisterStatus> getUnprocessedJobCheckRegStatusNewTrx() {
		Object[][] params = {{"requestStatus", (short) 0}};
		return (List<TrJobCheckRegisterStatus>) managerDAO.list(
				"from TrJobCheckRegisterStatus jcrs "
				+ "join fetch jcrs.trBalanceMutation "
				+ "left join fetch jcrs.lovUserType "
				+ "where jcrs.requestStatus = :requestStatus ", params).get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateJobCheckRegStatusNewTrx(TrJobCheckRegisterStatus jobCheckRegisStatus) {
		jobCheckRegisStatus.setUsrUpd(MssTool.maskData(jobCheckRegisStatus.getUsrUpd()));
		this.managerDAO.update(jobCheckRegisStatus);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertJobCheckRegStatusNewTrx(TrJobCheckRegisterStatus jobCheckRegisStatus) {
		jobCheckRegisStatus.setUsrCrt(MssTool.maskData(jobCheckRegisStatus.getUsrCrt()));
		managerDAO.insert(jobCheckRegisStatus);
	}

	@Override
	public TrJobCheckRegisterStatus getLatestJobCheckRegStatusByIdNo(String idNo, MsVendor vendor) {
		Map<String, Object> params = new HashMap<>();
		params.put("idNo", MssTool.getHashedString(idNo));
		params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select crs.id_job_check_register_status ")
			.append("from tr_job_check_register_status crs ")
			.append("join tr_balance_mutation bm on crs.id_balance_mutation = bm.id_balance_mutation ")
			.append("where crs.hashed_id_no = :idNo ")
			.append("and bm.id_ms_vendor = :idMsVendor ")
			.append("order by crs.dtm_crt desc limit 1");
		
		BigInteger idJob = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idJob) {
			return null;
		}
		
		return managerDAO.selectOne(
				"from TrJobCheckRegisterStatus crs "
				+ "join fetch crs.trBalanceMutation bm "
				+ "where crs.idJobCheckRegisterStatus = :idJob ", new Object[][] {{"idJob", idJob.longValue()}});
		
	}

}
