package com.adins.esign.businesslogic.api;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.impl.provider.EsignAuthenticationToken;
import com.adins.esign.constants.enums.RegistrationType;
import com.adins.esign.model.<PERSON>Lov;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.DataUserOptionalBean;
import com.adins.esign.model.custom.SaveActivationDecryptedResultBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.webservices.model.*;
import com.adins.esign.webservices.model.digisign.DigisignRegisterEmbedRequest;
import com.adins.esign.webservices.model.digisign.DigisignRegisterRequest;
import com.adins.esign.webservices.model.external.DownloadUserCertificateRequest;
import com.adins.esign.webservices.model.external.DownloadUserCertificateResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterEmbedRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface UserLogic {
	
	UserAutoFillDataResponse getUserDataRegistrationByLoginId(UserAutoFillDataRequest request, AuditContext audit) throws IOException;
	
	// Register Digisign
	RegistrationResponse registerUser(RegistrationRequest regisRequest, AuditContext audit) throws IOException;
	RegistrationResponse registerUserEmbed(RegistrationEmbedRequest regisRequest, AuditContext audit) throws IOException;
	RegistrationResponse registerBM(RegistrationRequest request, AuditContext audit) throws IOException;
	
	// Register Digisign v2
	RegisterResponse registerEmbedDigisign(DigisignRegisterEmbedRequest request, AuditContext audit);
	RegisterResponse registerDigisign(DigisignRegisterRequest request, RegistrationType registrationType, MsLov lovUserType, AuditContext audit);
	
	// Register TekenAja
	TekenAjaRegisterResponse registerNormalTekenAja(TekenAjaRegisterRequest request, AuditContext audit);
	RegisterResponse registerEmbedTekenAja(TekenAjaRegisterEmbedRequest request, AuditContext audit);
	
	public UserBean prepareDataActivationLink(ActivationLinkRequest activationLinkRequest, AuditContext audit);
	
	public String generateRandomPassword();
	
	public AmMsuser getUserByLoginId(String loginId);
	
	void insertBalanceMutationVerif(String vendorCode, String tenantCode, String loginId, AuditContext audit );

	void insertPasswordHist(AmMsuser newUser, AuditContext audit);

	void insertRoleNewUser(AmMsuser newUser, AmMsrole role, AuditContext audit);

	void insertNewUserPersonalData(AmMsuser newUser, SignerBean signerBean, AuditContext audit);
	
	void saveUserPhoto(String loginId, String idPhoto, String selfPhoto, AuditContext audit);

	void insertNewUser(AmMsuser newUser, SignerBean signerBean, String randomPassword, MsOffice office, AuditContext audit) throws NoSuchAlgorithmException;
	
	void insertUserofTenant(AmMsuser user, MsTenant tenant, AuditContext audit);
	
	SaveUserActivationResponse updateActivationStatus(SaveActivationDecryptedResultBean decryptedBean, AuditContext audit) throws IOException;
	
	SaveUserActivationResponse updateActivationStatusV2(SaveActivationDecryptedResultBean decryptedBean,String tenantCode, AuditContext audit) throws IOException;
	
	@RolesAllowed({"ROLE_INQUIRY_USER"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ListInquiryUserResponse getListInquiryUser(ListInquiryUserRequest request, AuditContext audit) throws ParseException;
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#request.loginId, authentication)")
	ChangePasswordResponse changePassword(ChangePasswordRequest request, AuditContext audit);
	
	ForgotPasswordResponse forgotPassword(ForgotPasswordRequest request, AuditContext audit);
	
	ResetPasswordResponse resetPassword(ResetPasswordRequest request, AuditContext audit);
	
	SendOtpEmailResponse sendOtp(SendOtpEmailRequest request, AuditContext audit);
	
	SendOtpEmailResponse sendOtpEmbed(SendOtpEmailEmbedRequest request, AuditContext audit);
	
	SendOtpEmailResponse sendOtpInvitation(SendOtpEmailEmbedRequest request, AuditContext audit);
	
	OtpVerificationResponse verifyOtp(OtpVerificationRequest request, AuditContext audit);
	
	OtpVerificationResponse verifyOtpEmbed(OtpVerificationEmbedRequest request, AuditContext audit);
	
	OtpVerificationResponse verifyOtpInvitation(OtpVerificationEmbedRequest request, AuditContext audit);
	
	ResetCodeVerificationResponse verifyResetCode(ResetCodeVerificationRequest request, AuditContext audit);
	
	ActivationStatusResponse getUserActivationStatus(ActivationStatusRequest request, AuditContext audit);
	
	EmailServiceStatusResponse checkUserEmailService(EmailServiceStatusRequest request, AuditContext audit);
	
	EmailServiceStatusResponse checkUserEmailServiceEmbed(EmailServiceStatusEmbedRequest request, AuditContext audit);
	
	PreRegisterResponse preRegisterEsignUser(PreRegisterRequest request, AuditContext audit);
	
	void updateUserPersonalData(UserBean userBean, AuditContext audit);
	
	AmMsuser getUserByPhone(String phone, AuditContext audit);
	
	AmMsuser getUserByOtpCode(String otpCode, AuditContext audit);
	
	LinkResetPasswordResponse getResetPasswordLink(LinkResetPasswordRequest request, AuditContext audit);		
	
	// Get activation link Digisign
	ActivationLinkResponse getActivationLink(ActivationLinkRequest request, AuditContext audit);
	ActivationLinkResponse getActivationLinkEmbed(ActivationLinkEmbedRequest request, AuditContext audit);
	ActivationLinkResponse getActivationLinkRegInv(ActivationLinkEmbedRequest request, AuditContext audit);
	
	//resend activation link
	ResendActLinkResponse resendActivationLinkForInvi(ResendActLinkForInviRequest request, AuditContext audit);
	ResendActLinkResponse resendActivationLinkForUser(ResendActLinkForUserRequest request, AuditContext audit);
	
	InvitationRegisterDataResponse getInvitationRegisterData(InvitationRegisterDataRequest request, AuditContext audit);
	
	InvitationRegisterStatusResponse checkInvitationRegisterStatus(InvitationRegisterStatusRequest request, AuditContext audit);

	GetUserPhotoResponse getPhoto(GetUserPhotoRequest request, AuditContext audit);
	
	ChangeProfileDataResponse changeUserProfileData(ChangeProfileDataRequest request, AuditContext audit);
	
	SendChangeProfileOTPResponse sendChangeProfileOtp(SendChangeProfileOTPRequest request, AuditContext audit) throws IOException;

	OtpVerifChangeProfileResponse otpVerifChangeProfile(OtpVerifChangeProfileRequest request, AuditContext audit);
	
	UpdateUserDataResponse updateUserData(UpdateUserDataRequest request, String apiKey, AuditContext audit);
	
	// Invitation Register
	@RolesAllowed("ROLE_INQUIRY_INV")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ResendRegisterInvitationResponse resendRegisterInvitation(ResendRegisterInvitationRequest request, AuditContext audit) throws UnsupportedEncodingException;
	
	@RolesAllowed("ROLE_INQUIRY_INV")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	InvitationLinkResponse getInvitationLink(InvitationLinkRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_REPORT_LINK_INV")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ListInvitationResponse getListInvitation(ListInvitationRequest request, AuditContext audit) throws ParseException;
	
	@RolesAllowed("ROLE_INQUIRY_INV")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	DataInvRegisResponse getDataInvRegis(DataInvRegisRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_REPORT_LINK_INV")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ListInvitationExcelReportResponse exportListInvitationReport(ListInvitationExcelReportRequest request, AuditContext audit) throws IOException;
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#request.email, authentication)")
	MyProfileResponse getMyProfile(MyProfileRequest request, AuditContext audit);
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isValidTenant(#request.tenantCode, authentication) and @esignSecurityServices.isRoleTenant('ROLE_INQ_USER', #request.tenantCode, authentication)")
	InquiryEditUserResponse getInquiryEditUser(InquiryEditUserRequest request, AuditContext audit);
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isValidTenant(#request.tenantCode, authentication) and @esignSecurityServices.isRoleTenant('ROLE_INQ_USER', #request.tenantCode, authentication)")
	UpdateUserResponse updateUser(UpdateUserRequest request, AuditContext audit) throws ParseException, IOException;
	
	CreateEmailResponse createEmail(CreateEmailRequest request, AuditContext audit) throws IOException;
	
	//return link Activation
	ReturnlinkAktivasiTekenAjaResponse checkKodeEncryptValid(ReturnlinkAktivasiTekenAjaRequest request, AuditContext audit);
	//callback Register
	TekenAjaCallbackResponse callbackRegister(TekenAjaCallbackRequest request, AuditContext audit);

	boolean isNikUserActivated(String nik, String vendorCode, AuditContext audit);
	String getNikUserActivationStatus(String nik, String vendorCode, AuditContext audit);

	// Reset Percobaan Request OTP
	@RolesAllowed({"ROLE_EMPLOYEE", "ROLE_CUSTOMER", "ROLE_INQ_USER"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ResetOtpCodeResponse resetOtpCode(ResetOtpCodeRequest request, AuditContext audit);
	
	UpdateReregisResponse updateReregister(UpdateReregisRequest request, AuditContext audit);
	UpdateReregisResponse updateReregisterEmbed(UpdateReregisRequest request, AuditContext audit);

	ActivationStatusResetPasswordResponse getUserActivationStatusForgetPassword(ActivationStatusResetPasswordRequest request, AuditContext audit);
	CheckRegisterStatusResponse checkRegisterStatus(CheckRegisterStatusRequest request, AuditContext audit);
	CheckRegisterAutoFillResponse checkRegisterAutoFill(CheckRegisterAutoFillRequest request, AuditContext audit);
	
	DataUserOptionalBean getDataUserOpsionalDataEncrypt(long idMsUser, long idMsVendorRegisteredUser, boolean getNIK, boolean getPhone, boolean getAddress, boolean getPhotoKTP, boolean getPhotoSelfie , AuditContext audit);
	
	GetAllUserResponse getAllUserData();
	
	@RolesAllowed("ROLE_USER_MANAGEMENT")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	UpdateDataUserResponse updateDataUser(UpdateDataUserRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_UPDATE_USER")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	EditActivationStatusResponse editActivationStatus(EditActivationStatusRequest request, AuditContext audit);
	
	CheckPasswordComplexityResponse checkPasswordComplexity(CheckPasswordComplexityRequest request, AuditContext audit);
	
	GetSignerDetailResponse getSignerDetail(GetSignerDetailRequest request, AuditContext audit);
	
	GetUserActDataResponse getUserActData(GetUserActDataRequest request, AuditContext audit);
	
	SignerDataVerificationResponse signerDataVerification(SignerDataVerificationRequest request, AuditContext audit);
	
	SentOtpActivationUserResponse sentOtpActivationUser(SentOtpActivationUserRequest request, AuditContext audit);
	
	VerifyOtpActivationUserResponse verifyOtpActivationUser(VerifyOtpActivationUserRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@esignSecurityServices.isValidUser(#request.phoneNo, authentication)")
	SentOtpSigningVerificationResponse sentOtpSigningVerification(SentOtpSigningVerificationRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	VerifyOtpSigningVerificationResponse verifyOtpSigningVerification(VerifyOtpSigningVerificationRequest request, AuditContext audit);
	
	UpdateActivationUserResponse updateActivationUser(UpdateActivationUserRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_DASHBOARD")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	VerifyLivenessFaceCompareResponse verifyLivenessFaceCompare(VerifyLivenessFaceCompareRequest request, AuditContext audit);
	
	//full api
	VerifyLivenessFaceCompareFullApiResponse verifyLivenessFaceCompareFullApi(VerifyLivenessFaceCompareFullApiRequest request, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit);
	
	CheckRegistrationResponse checkRegistration(CheckRegistrationRequest request,String apiKey, AuditContext audit);
	
	GetSignerDetailWebviewResponse getSignerDetailWebview(GetSignerDetailWebviewRequest request, AuditContext audit);
	
	DownloadUserCertificateResponse downloadUserCertificate(DownloadUserCertificateRequest request, String xApiKey, AuditContext audit);
	
	GetUrlForwarderResponse getUrlForwarder(GetUrlForwarderRequest request,AuditContext audit);

	//view user otp
	@RolesAllowed("ROLE_VIEW_OTP")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ListUserViewOtpResponse getListUserViewOtp(ListUserViewOtpRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_VIEW_OTP")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ViewOtpResponse viewOtp (ViewOtpRequest request,AuditContext audit);
	
	@RolesAllowed("ROLE_VIEW_OTP")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ViewResetCodeResponse viewResetCode(ViewResetCodeRequest request, AuditContext audit);

	@RolesAllowed("ROLE_TOPUP")
    @PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	decryptUserDataResponse decryptKTP(decryptUserDataRequest request, AuditContext audit);

	void insertUserActivityLog(AmMsuser user, MsTenant tenant, AmMsrole role, String loginId, MsLov activityType, AuditContext audit);

	void insertUserLoginActivityLog(String tenantCode, String roleCode, AuditContext audit);

	void insertUserLogoutActivityLog(EsignAuthenticationToken esignToken, AuditContext audit);
}
