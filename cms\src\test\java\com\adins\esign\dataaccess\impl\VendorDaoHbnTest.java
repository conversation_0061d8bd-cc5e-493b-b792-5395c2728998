package com.adins.esign.dataaccess.impl;

import static org.junit.Assert.assertNotNull;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.VendorDao;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.AuditDataType;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class VendorDaoHbnTest {
	@Autowired private VendorDao vendorDao;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private VendorLogic vendorLogic;
	
	private AuditDataType auditData;
	private AuditContext auditContext;

	private MsTenant tenantJunit;
	private MsVendor vendorJunit;

	@BeforeEach
	public void setUp() {
		auditData = new AuditDataType();
		auditData.setCallerId("INITIAL");
		
		auditContext = new AuditContext(auditData.getCallerId());
		
		tenantJunit = tenantLogic.getTenantById((long) 1, auditContext);
		vendorJunit = vendorLogic.getVendorByCode(GlobalVal.VENDOR_CODE_DIRJENPAJAK, auditContext);
		
	}
	
	@Order(1)
	@Test
	void getVendoroftenantTest() {
		MsVendoroftenant vendorOfTenant = vendorDao.getVendoroftenant(tenantJunit, vendorJunit);
		assertNotNull(vendorOfTenant);
	}
	
	@Order(1)
	@Test
	void getVendoroftenantByCodeTest() {
		MsVendoroftenant vendorOfTenant = vendorDao.getVendorTenantByCode(tenantJunit.getTenantCode(), vendorJunit.getVendorCode());
		assertNotNull(vendorOfTenant);
	}
}
