package com.adins.esign.businesslogic.impl;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.ImportResource;
import org.springframework.stereotype.Component;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.TestSetUpLogic;
import com.adins.esign.constants.GlobalVal;
//import com.adins.esign.model.MsBranch;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.MsDocTemplateSignLoc;
//import com.adins.esign.model.MsDocumentTemplate;
//import com.adins.esign.model.MsJob;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsOffice;
//import com.adins.esign.model.MsSignLocation;
import com.adins.esign.model.MsTenant;
//import com.adins.esign.model.TrAgreement;
//import com.adins.esign.model.TrDocument;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.webservices.model.DocumentConfinsRequest;
import com.adins.esign.webservices.model.DocumentTemplateGetOneRequest;
import com.adins.esign.webservices.model.DocumentTemplateGetOneResponse;
import com.adins.esign.webservices.model.SignLocationRequest;
import com.adins.exceptions.DigisignException;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.EntityNotFoundException;
import com.adins.exceptions.EntityNotUniqueException;
import com.adins.framework.persistence.dao.model.AuditContext;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
@TestPropertySource(locations = "classpath:application.properties")
@Component
class GenericDocumentLogicTest extends BaseLogic{
//    @Value("${digisign.senddocument.uri}") private String urlSendDocument;
//	
//	@Autowired private TestSetUpLogic testSetUpLogic;
//	@Autowired private DocumentLogic documentLogic;
//    
//	private AuditContext auditContext = new AuditContext();
//	private MsDocTemplate docTemp;
//	private DocumentTemplateRequest docReq;
//	private SignLocationRequest signLocReq;
//	private MsLov signerType;
//	private MsLov signType;
//	private MsDocTemplateSignLoc signLoc;
//	private DocumentConfinsRequest docConfinsReq;
//	private SignerBean[] signer;
//	private String encodedDocumentFile;
//	private TrDocumentH agreement;
//	private TrDocumentD doc;
//	private MsTenant tenant;
//	private AmMsuser user;
//	private MsOffice branch;
//	private MockWebServer server;
//    private AmMsrole job;
//	private String emailLogin = "<EMAIL>";
	
//    @BeforeEach
//	@Rollback(true)
//	public void setUp() {
//		auditContext.setCallerId("JUNIT");
//    	tenant = testSetUpLogic.setUpTenant("JTENANT", "TENANT JUNIT");
//    	branch = testSetUpLogic.setUpBranch("JBRANCH", "BRANCH JUNIT", null, tenant);
//		job = daoFactory.getJobDao().getJobByCode(GlobalVal.JOB_ADMIN);
//    	user = testSetUpLogic.setUpUser(emailLogin, "USER JUNIT", job, branch, null, subsystem);
//    	agreement = testSetUpLogic.setUpTrAgreement("REF_JUNIT", user, branch, tenant, 5, 5);
//
//    	docTemp = testSetUpLogic.setUpDocumentTemplate("CODE_JUNIT", "JUNIT TEMPLATE", "1",tenant);
//    	docReq = testSetUpLogic.setUpDocumentTemplateRequest("CODE_JUNIT", "JUNIT TEMPLATE", tenant.getTenantCode());
//    	signLocReq = testSetUpLogic.setUpSignLocationReqest("CODE_JUNIT", GlobalVal.CODE_LOV_SIGNER_CUSTOMER, "ttd");
//    	signType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_TYPE, signLocReq.getSignTypeCode());
//    	signerType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNER_TYPE, GlobalVal.CODE_LOV_SIGNER_CUSTOMER);
//
//    	signLoc = testSetUpLogic.setUpSignLocation(docTemp, signerType, signType);
//    	docConfinsReq = testSetUpLogic.setUpDocumentConfinsRequest(docTemp.getDocumentTemplateCode(), emailLogin, branch.getBranchCode());
//    	signer = docConfinsReq.getSigner();
//    	encodedDocumentFile = new String(Base64.getEncoder().encode("DOCUMENT_FILE_JUNIT".getBytes(StandardCharsets.UTF_8)));
//
//    	doc = testSetUpLogic.setUpTrDocument(docTemp, agreement, tenant);
//		server = new MockWebServer();
//	}
//    
//    @Test
//	@Rollback(true)
//    void getListDocumentTemplateTestWithPageMinus() {
//    	DocumentTemplateResponse result = documentLogic.listDocumentTemplate(docTemp.getDocumentTemplateCode(), 
//    			docTemp.getDocumentTemplateName(), docTemp.getIsActive(), -1, GlobalVal.ROW_PER_PAGE, auditContext);
//    	assertFalse(result.getListDocumentTemplate().isEmpty());
//    }
//    
//    @Test
//	@Rollback(true)
//    void getListDocumentTemplateTestWithPageFive() {
//    	DocumentTemplateResponse result = documentLogic.listDocumentTemplate(docTemp.getDocumentTemplateCode(), 
//    			docTemp.getDocumentTemplateName(), docTemp.getIsActive(), 5, GlobalVal.ROW_PER_PAGE, auditContext);
//    	assertFalse(result.getListDocumentTemplate().isEmpty());
//    }
//    
//    @Test
//	@Rollback(true)
//    void getListDocumentTemplateTestWithPageOne() {
//    	DocumentTemplateResponse result = documentLogic.listDocumentTemplate(docTemp.getDocumentTemplateCode(), 
//    			docTemp.getDocumentTemplateName(), docTemp.getIsActive(), 1, GlobalVal.ROW_PER_PAGE, auditContext);
//    	assertFalse(result.getListDocumentTemplate().isEmpty());
//    }
//    
//    @Test
//    void getListDocumentTemplateTestWithoutList() {
//    	DocumentTemplateResponse result = documentLogic.listDocumentTemplate("CODE JUNIT NULL", 
//    			"JUNIT TEMPLATE NULL", "1", 1, GlobalVal.ROW_PER_PAGE, auditContext);
//    	assertTrue(result.getListDocumentTemplate().isEmpty());
//    }
//    
//    @Test
//    @Rollback(true)
//    void insertDocumentTemplateWithIsActive() {
//    	docReq.setDocumentTemplateCode("CODE JUNIT 2");
//    	docReq.setIsActive("1");
//    	documentLogic.insertDocumentTemplate(docReq, auditContext);
//    	MsDocTemplate docTemp = daoFactory.getDocumentDao().getDocumentTemplateByCode(docReq.getDocumentTemplateCode());
//    	assertNotNull(docTemp);
//    }
//    
//    @Test
//    @Rollback(true)
//    void insertDocumentTemplateWithoutIsActive() {
//    	docReq.setDocumentTemplateCode("CODE JUNIT 2");
//    	documentLogic.insertDocumentTemplate(docReq, auditContext);
//    	MsDocTemplate docTemp = daoFactory.getDocumentDao().getDocumentTemplateByCode(docReq.getDocumentTemplateCode());
//    	assertNotNull(docTemp);
//    }
//    
//    @Test
//    @Rollback(true)
//    void insertDocumentTemplateWithExistingData() {
//    	docReq.setIsActive("1");
//    	assertThrows(EntityNotUniqueException.class, () -> documentLogic.insertDocumentTemplate(docReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void getDocumentTemplateByCodeWithExistingData() {
//    	MsDocTemplate docTemp = documentLogic.getDocumentTemplateByCode(this.docTemp.getDocumentTemplateCode(), auditContext);
//    	assertNotNull(docTemp);
//    }
//    
//    @Test
//    @Rollback(true)
//    void getDocumentTemplateByCodeWithoutExistingData() {
//    	MsDocTemplate docTemp = documentLogic.getDocumentTemplateByCode("CODE JUNIT NULL", auditContext);
//    	assertNull(docTemp);
//    }
//    
//    @Test
//    @Rollback(true)
//    void insertSignLocationWithoutDocumentTemplate() {
//    	signLocReq.setDocumentTemplateCode("CODE JUNIT NULL");
//    	assertThrows(EntityNotFoundException.class, () -> documentLogic.insertSignLocation(signLocReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void insertSignLocationWithoutSignerType() {
//    	signLocReq.setSignerTypeCode("CODE JUNIT NULL");
//    	assertThrows(EntityNotFoundException.class, () -> documentLogic.insertSignLocation(signLocReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void insertSignLocationWithoutSignType() {
//    	signLocReq.setSignTypeCode("CODE JUNIT NULL");
//    	assertThrows(EntityNotFoundException.class, () -> documentLogic.insertSignLocation(signLocReq, auditContext));
//    }
//
//    @Test
//    @Rollback(true)
//    void insertSignLocationWithCorrectData() {
//    	documentLogic.insertSignLocation(signLocReq, auditContext);
//    	signLoc = daoFactory.getDocumentDao().getListSignLocationByTemplateCode(signLocReq.getDocumentTemplateCode()).get(0);
//    	assertNotNull(signLoc);
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithoutRequestAndAudit() {
//    	docConfinsReq = null;
//    	auditContext = null;
//    	assertThrows(DocumentException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithoutReferencoNo() {
//    	docConfinsReq.setReferenceNo("");
//    	assertThrows(DocumentException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithoutDocumentTemplateCode() {
//    	docConfinsReq.setDocumentTemplateCode("");
//    	assertThrows(DocumentException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithoutDocumentFile() {
//    	docConfinsReq.setDocumentFile("");
//    	assertThrows(DocumentException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithoutSigner() {
//    	signer = null;
//    	docConfinsReq.setSigner(signer);
//    	assertThrows(DocumentException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithSignerNoData() {
//    	signer = new SignerBean[] {};
//    	docConfinsReq.setSigner(signer);
//    	assertThrows(DocumentException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithoutSignAction() {
//    	signer[0].setSignAction("");
//    	docConfinsReq.setSigner(signer);
//    	assertThrows(DocumentException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithoutSignerType() {
//    	signer[0].setSignerType("");
//    	docConfinsReq.setSigner(signer);
//    	assertThrows(DocumentException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithoutUsername() {
//    	signer[0].setUserName("");
//    	docConfinsReq.setSigner(signer);
//    	assertThrows(DocumentException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithoutEmail() {
//    	signer[0].setEmail("");
//    	docConfinsReq.setSigner(signer);
//    	assertThrows(DocumentException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithFormatPDF() throws IOException {
//    	server.start(8095);
//		server.enqueue(new MockResponse().setBody("{\"JSONFile\":{\"notif\":\"Kirim dokumen berhasil.\",\"result\":\"00\"}}"));
//		
//		OkHttpClient client = new OkHttpClient().newBuilder().build();
//		
//		client.newCall(new Request.Builder().url(server.url(urlSendDocument)).build());
//    	
//    	String id = documentLogic.sendDocument(docConfinsReq, auditContext);
//    	
//    	server.shutdown();
//    	
//    	assertTrue(StringUtils.isNotBlank(id));
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithoutFormatPDF() throws IOException {
//    	server.start(8095);
//		server.enqueue(new MockResponse().setBody("{\"JSONFile\":{\"result\":\"FE\",\"notif\":\"Format dokumen harus pdf\"}}"));
//		
//		OkHttpClient client = new OkHttpClient().newBuilder().build();
//		
//		client.newCall(new Request.Builder().url(server.url(urlSendDocument)).build());
//    	
//    	docConfinsReq.setDocumentFile(encodedDocumentFile);
//    	assertThrows(DigisignException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    	
//    	server.shutdown();
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithAdminLoginIdAndSignActionAuto() throws IOException {
//    	server.start(8095);
//		server.enqueue(new MockResponse().setBody("{\"JSONFile\":{\"result\":\"FE\",\"notif\":\"Format dokumen harus pdf\"}}"));
//		
//		OkHttpClient client = new OkHttpClient().newBuilder().build();
//		
//		client.newCall(new Request.Builder().url(server.url(urlSendDocument)).build());
//		
//    	docConfinsReq.setDocumentFile(encodedDocumentFile);
//    	signer[0].setEmail(GlobalVal.USER_ADMIN_LOGIN_ID);
//    	signer[0].setSignAction("at");
//    	docConfinsReq.setSigner(signer);
//    	assertThrows(DigisignException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    	
//    	server.shutdown();
//    }
//    
//    @Test
//    @Rollback(true)
//    void sendDocumentWithJobMF() {
//    	signer[0].setSignerType(GlobalVal.JOB_MULTIFINANCE);
//    	docConfinsReq.setDocumentFile("");
//    	docConfinsReq.setSigner(signer);
//    	assertThrows(DocumentException.class, () -> documentLogic.sendDocument(docConfinsReq, auditContext));
//    }
//    
//    @Test
//    @Rollback(true)
//    void getDocumentByUuid() {
//    	TrDocumentD doc = documentLogic.getDocumentByUuid(this.doc.getUuidDocument());
//    	assertNotNull(doc);
//    }
//    
//    @Test
//    @Rollback(true)
//    void getDocumentByUuidWithoutExistingData() {
//    	TrDocumentD doc = documentLogic.getDocumentByUuid(this.doc.getUuidDocument()+1);
//    	assertNull(doc);
//    }
}
