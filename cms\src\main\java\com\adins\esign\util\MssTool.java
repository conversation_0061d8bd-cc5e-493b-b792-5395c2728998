package com.adins.esign.util;

import java.math.BigDecimal;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Stack;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.configuration.jsse.TLSClientParameters;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.transport.http.HTTPConduit;
import org.slf4j.LoggerFactory;

import com.adins.esign.constants.GlobalVal;
import com.devskiller.friendly_id.FriendlyId;

import okhttp3.OkHttpClient;


public class MssTool {
	public static final long[] EMPTY_LONG_ARRAY = new long[0];
	public static final Long[] EMPTY_LONG_OBJECT_ARRAY = new Long[0];
	private static SecureRandom randGen = new SecureRandom();
	private static final org.slf4j.Logger LOG = LoggerFactory.getLogger(MssTool.class);
	
	public static final long WEB_CLIENT_DEFAULT_CONNECTION_TIMEOUT = 30_000; //30s
	public static final long WEB_CLIENT_DEFAULT_READ_TIMEOUT = 60_000; //60s
	
	private MssTool() {
		throw new IllegalStateException("Utility class");
	}
	  
	/**
	 * Get type of MasterSequence
	 * 
	 * @param isYear
	 * @param isMonth
	 * @param isDate
	 * @return
	 *   "D" - if sequence is start from 1 daily 
	 *   "M" - if sequence is start from 1 monthly
	 *   "Y" - if sequence is start from 1 annually
	 *   "0" - if sequence is none of above
	 */
		
	public static String sequenceDateType(String isYear, String isMonth, String isDate) {
		if ("1".equals(isYear) && "1".equals(isMonth) && "1".equals(isDate))
			return "D";
		
		if ("1".equals(isYear) && "1".equals(isMonth))
			return "M";
		
		if ("1".equals(isYear))
			return "Y";
			
		return "0";
	}
	
	public static boolean isNewYear(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return (cal.get(Calendar.MONTH) == Calendar.JANUARY &&
				cal.get(Calendar.DATE) == 1);
	}
	
	public static boolean isNewMonth(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return (cal.get(Calendar.DATE) == 1);
	}
	
	public static boolean isMultipleQuestion(String answerType) {
		return (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType));			 		
	}
	
	public static boolean isImageQuestion(String answerType) {
		return (GlobalVal.ANSWER_TYPE_IMAGE.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_IMAGE_WITH_GPS.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_DRAWING.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_PHOTO_KTP.equals(answerType));
	}
	
	public static boolean isChoiceQuestion(String answerType) {
		return (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_DROPDOWN.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_RADIO.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_TEXT_WITH_SUGGESTION.equals(answerType));		
	}
	
	public static boolean isChoiceWithoutDescriptionQuestion(String answerType) {
		return (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_DROPDOWN.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_RADIO.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_TEXT_WITH_SUGGESTION.equals(answerType));		
	}
	
	public static boolean isChoiceWithDescriptionQuestion(String answerType) {
		return (GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType) ||				
				GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(answerType) ||
				GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(answerType));		
	}
	
	public static int randomNumberGenerator(int length) {
		int startNum = (int) Math.pow(10,(double) length - 1);
		int range = (int) (Math.pow(10, length) - startNum + 1);

		return randGen.nextInt(range) + startNum;
	}
	
	/**
	 * 
	 * @param x must be >= 1
	 * @return randomly generated number where 1 <= result <= x
	 */
	public static int generateRandomNumber(int x) {
		return randGen.nextInt(x) + 1;
	}
	
	/**
	 * <p>Checking string result of LiteDMS ILDMSDocWS.uploadImage
	 * whether the upload success of failed.
	 * 
	 * <p>example result:
	 * <li><code>success-21-Document Created : Bukti Kepemilikan Rumah Pemohon_SVY16000039</code></li>
	 * <li><code>Failed-Error in Create Opportunity Document - Error : failed-Invalid login Name : YUWONO</code></li>
	 * 
	 * @param wsResult string result from uploadImage webservice
	 * @return
	 * 	<p><b><code>true<code></b> if result starts with "success" else return <b><code>false<code></b>  
	 */
	public static boolean isDmsUploadSuccess(String wsResult) {
		return StringUtils.startsWithIgnoreCase(wsResult, "success");
	}
	
	public static String readableFileSize(long size) {
	    if(size <= 0) return "0";
	    final String[] units = new String[] { "B", "kB", "MB", "GB", "TB" };
	    int digitGroups = (int) (Math.log10(size)/Math.log10(1024));
	    return new DecimalFormat("#,##0.#").format(size/Math.pow(1024, digitGroups)) + " " + units[digitGroups];
	}
	
	public static String toJenisDocDms(String refId) {
		if (StringUtils.isEmpty(refId))
			return refId;
		
		int lastIdx = refId.lastIndexOf('_');
		if (lastIdx == -1)
			return refId;
		
		return refId.substring(0, lastIdx);
	}
	

	/**
	 * 
	 * <pre>
     * MssTool.maskingString(null, 8, 0, '*')      = null
     * MssTool.maskingString("",  8, 0, '*')       = ""
     * MssTool.maskingString("0812", 8, 0, '*')    = "****"
     * MssTool.maskingString("081234567890", 8, 0, '*') = "********7890"
     * MssTool.maskingString("081234567890", 4, 4, '*') = "****3456****"
     * </pre>
	 * 
	 * @param in 
	 * @param maskPrefixLength number of chars from start that will be masked
	 * @param maskSuffixLength number of chars from behind that will be masked
	 * @param maskChar masking character
	 * @return
	 */
	public static String maskingString(String in, int maskPrefixLength, int maskSuffixLength, char maskChar) {
		if (StringUtils.isEmpty(in))
			return in;
		
		maskPrefixLength = (maskPrefixLength > in.length()) ?
				in.length() : maskPrefixLength;
		maskSuffixLength = (maskSuffixLength > in.length()) ?
				in.length() : maskSuffixLength;
				
		char[] chars = in.toCharArray();
		for (int i = 0; i < maskPrefixLength; i++) {
			chars[i] = maskChar;
		}
		
		for (int i=in.length()-1, j=0; j < maskSuffixLength; i--, j++) {
			chars[i] = maskChar;
		}
		
		return new String(chars);
	}

	public static Object[][] toSqlParams(Stack<Object[]> stack) {
		if (stack == null)
			return new Object[0][0];
		
	    Object[][] sqlParams = new Object[stack.size()][2];
	    for (int i = 0; i < stack.size(); i++) {
			Object[] objects = stack.get(i);
			sqlParams[i] = objects;
		}
	    
	    return sqlParams;
	}
	
	public static long[] strArrayToLongArray(String[] arr){
		if (arr == null || arr.length == 0) {
			return EMPTY_LONG_ARRAY;
		}
		
	    long[] resultArr = new long[arr.length];
	    for (int i = 0; i < arr.length; i++) {
	        resultArr[i] = Long.parseLong(arr[i]);
	    }

	    return resultArr;
	}
	
	public static Long[] strArrayToLongWrapperArray(String[] arr){
		if (arr == null || arr.length == 0) {
			return EMPTY_LONG_OBJECT_ARRAY;
		}
		
	    List<Long> resultList = new ArrayList<>(arr.length);
	    for (String s : arr) {
	        if (!StringUtils.isEmpty(s)) {
	        	resultList.add(new Long(s));
	        }
	    }

	    Long[] result = new Long[resultList.size()];
	    return resultList.toArray(result);
	}
	
	public static String formatDateToStringIn(Date inputDate, String dateFormat) {
		if (null == inputDate) {
			return null;
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);  
		return sdf.format(inputDate);
	}
	
	public static Date changeDateFormat(Date inputDate, String dateFormat) {
		String formattedDate = formatDateToStringIn(inputDate, dateFormat);
		return formatStringToDate(formattedDate, dateFormat);
	}

	public static Date formatStringToDate(String inputDateString, String dateFormat) {
		if (StringUtils.isEmpty(inputDateString)) {
			return null;
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
		try {
			return sdf.parse(inputDateString);
		} catch (ParseException e) {
			LOG.warn("Cannot parse '{}' to format '{}'", inputDateString, dateFormat, e);
			return null;
		}
	}
	
	public static String generateStampDutyNo() {
		return FriendlyId.createFriendlyId();
	}
	
	public static String parseFloatDecimal(String number, String decimalFormat) {
		return String.format(decimalFormat, new BigDecimal(number));
	}
	
	public static String changePrefixToPlus62(String phoneNo) {
		if (null == phoneNo) {
			return null;
		}
		
		if (StringUtils.startsWithIgnoreCase(phoneNo, "0")) {
			return "+62".concat(phoneNo.substring(1));
		}
		else if (StringUtils.startsWithIgnoreCase(phoneNo, "62")) {
			return "+".concat(phoneNo);
		}
		else if (StringUtils.startsWithIgnoreCase(phoneNo, "+62")) {
			return phoneNo;
		}
		else {
			throw new IllegalArgumentException();
		}
	}
	
	public static String changePrefixTo62(String phoneNo) {
		if (null == phoneNo) {
			return null;
		}
		
		if (StringUtils.startsWithIgnoreCase(phoneNo, "0")) {
			return "62".concat(phoneNo.substring(1));
		}
		
		return phoneNo;
	}
	
	public static String bytesToHex(byte[] hash) {
	    StringBuilder hexString = new StringBuilder(2 * hash.length);
	    for (int i = 0; i < hash.length; i++) {
	        String hex = Integer.toHexString(0xff & hash[i]);
	        if(hex.length() == 1) {
	            hexString.append('0');
	        }
	        hexString.append(hex);
	    }
	    return hexString.toString();
	}
	
	public static String getHashedString(String params) {
		String hashedString = StringUtils.EMPTY;
		try {
			MessageDigest digest = MessageDigest.getInstance(GlobalVal.HASH_SHA);
			byte[] hashedByteArray = digest.digest(params.getBytes());
			hashedString = MssTool.bytesToHex(hashedByteArray);
		} catch (NoSuchAlgorithmException e) {
			LOG.warn("Failed on hashing '{}' sha-256", params, e);
		}
		return hashedString;
	}
	
	public static void trustAllSslCertificate(WebClient client) {			
		HTTPConduit conduit = WebClient.getConfig(client).getHttpConduit();
		TLSClientParameters tlsParams = new TLSClientParameters();
		tlsParams.setHostnameVerifier(new HostnameVerifier() {			
			@Override
			public boolean verify(String hostname, SSLSession session) {
				return true;
			}
		});
		try {
			tlsParams.setSSLSocketFactory(mySslContext().getSocketFactory());
			conduit.setTlsClientParameters(tlsParams);
		}
		catch (KeyManagementException | NoSuchAlgorithmException e) {
			LOG.warn("Failed on setting trusts all ssl certs", e);
		}		
	}
	
	private static SSLContext mySslContext() throws NoSuchAlgorithmException, KeyManagementException {
		TrustManager[] trustAllCerts = new TrustManager[] { new X509TrustManager() {
		   @Override
		   public X509Certificate[] getAcceptedIssuers() {
		      return null;
		   }

		   @Override
		   public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {}

		   @Override
		   public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {}
		} };

		SSLContext sslContext = SSLContext.getInstance("SSL");
		sslContext.init(null, trustAllCerts, new SecureRandom());
		return sslContext;
	}
	
	public static void setWebClientConnReadTimeout(WebClient client, long connTimeoutMillis, long readTimeoutMillis) {
		HTTPConduit conduit = WebClient.getConfig(client).getHttpConduit();
		conduit.getClient().setConnectionTimeout(connTimeoutMillis);
		conduit.getClient().setReceiveTimeout(readTimeoutMillis);		
	}
	
	/**
	 * 
	 * <pre>
     * MssTool.maskEmailAddress("<EMAIL>", 2) = "ab**@gmail.com"
     * MssTool.maskEmailAddress("<EMAIL>", 3) = "abc*@gmail.com"
     * MssTool.maskEmailAddress("",  2)              = ""
     * MssTool.maskEmailAddress(null,  2)            = null
     * MssTool.maskEmailAddress("test",  2)          = "test"
     * </pre>
	 * 
	 * @param email 
	 * @param prefixLength number of characters that will not be masked
	 * @return
	 */
	public static String maskEmailAddress(String email, int prefixLength) {
		if (StringUtils.isBlank(email)) {
			return email;
		}
		// Regex example: (?<=.{3}).(?=.*@)
		String regex = "(?<=.{" + prefixLength + "}).(?=.*@)";
		return email.replaceAll(regex, "*");
	}
	
	public static String cutImageStringPrefix(String base64Image) {
		if (StringUtils.isEmpty(base64Image)) {
			return base64Image;
		}
		
		String prefix = StringUtils.EMPTY;
		if (base64Image.startsWith(GlobalVal.IMG_JPEG_PREFIX)) {
			prefix = GlobalVal.IMG_JPEG_PREFIX;
		} else if (base64Image.startsWith(GlobalVal.IMG_JPG_PREFIX)) {
			prefix = GlobalVal.IMG_JPG_PREFIX;
		} else if (base64Image.startsWith(GlobalVal.IMG_PNG_PREFIX)) {
			prefix = GlobalVal.IMG_PNG_PREFIX;
		}
		return base64Image.substring(prefix.length());
	}
	
	public static byte[] imageStringToByteArray(String base64Image) {
		if (StringUtils.isBlank(base64Image)) {
			// return empty byte array
			return new byte[0];
		}
		
		String imageString = cutImageStringPrefix(base64Image);
		return Base64.getDecoder().decode(imageString);
	}
	
	public static byte[] imageStringToByteArray(String base64Image, boolean returnNullIfEmpty) {
		if (returnNullIfEmpty && StringUtils.isBlank(base64Image)) {
			return null;
		}
		
		return imageStringToByteArray(base64Image);
	}
	
	public static OkHttpClient getUnsafeOkHttpClient() {
		try {
			final TrustManager[] trustAllCerts = new TrustManager[] {
					new X509TrustManager() {
						@Override
						public void checkClientTrusted(X509Certificate[] chain, String authType) {
							
						}
						
						@Override
						public void checkServerTrusted(X509Certificate[] chain, String authType) {
							
						}
						
						@Override
						public X509Certificate[] getAcceptedIssuers() {
							return new X509Certificate[]{};
						}
					}
				};
				
				SSLContext sslContext = SSLContext.getInstance("SSL");
				sslContext.init(null, trustAllCerts, new SecureRandom());
				
				OkHttpClient.Builder builder = new OkHttpClient.Builder();
				builder.sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0]);
				builder.hostnameVerifier((hostname, session) -> true);
				return builder.build();
		} catch (Exception e) {
			LOG.error("Failed to get http client: {}", e.getLocalizedMessage());
			return null;
		}
	}
	
	/**
	 * 
	 * <pre>
	 * MssTool.cutLastObjectFromListString("abc", ";") = ""
     * MssTool.cutLastObjectFromListString("abc;def", ";") = "abc"
     * MssTool.cutLastObjectFromListString("ab;cd;ef", ";") = "ab;cd"
     * MssTool.cutLastObjectFromListString("", ";") = ""
     * MssTool.cutLastObjectFromListString(null, ";") = null
     * MssTool.cutLastObjectFromListString("ab;cd;ef", "") = "ab;cd;ef"
     * MssTool.cutLastObjectFromListString("ab;cd;ef", null) = "ab;cd;ef"
     * </pre>
	 */
	public static String cutLastObjectFromListString(String listString, String separator) {
		if (StringUtils.isBlank(listString) || StringUtils.isBlank(separator)) {
			return listString;
		}
		
		String[] array = listString.split(separator);
		List<String> stringList = new LinkedList<>(Arrays.asList(array));
		int index = stringList.size() - 1;
		stringList.remove(index);
		return StringUtils.join(stringList, separator);
	}
	
	public static String getApplicationIpAddress() {
		try {
			return InetAddress.getLocalHost().getHostAddress();
		} catch (Exception e) {
			LOG.error("Failed to get IP address", e);
			return null;
		}
	}
	
	public static final String generateRandomCharacters(String pools, int length) {
		if (length < 1) {
			return null;
		}
		
		StringBuilder builder = new StringBuilder();
		for (int i = 0; i < length; i++) {
			int randomIndex = randGen.nextInt(pools.length());
			builder.append(pools.charAt(randomIndex));
		}
		return builder.toString();
	}
	
	public static final String urlEncode(String data) {
		try {
			return URLEncoder.encode(data, StandardCharsets.UTF_8.toString());
		} catch (Exception e) {
			LOG.error("Failed to URL encode", e);
			return null;
		}
	}
	
	/**
	 * Masks string, leaving the prefix and suffix unmasked
	 * <br><br>
	 * <code>
	 * MssTool.maskStringByIndex("0123456789", 3, 3, '*') = "012****789"
	 * <br>
	 * MssTool.maskStringByIndex("0123456789", 4, 3, '*') = "0123***789"
	 * </code>
	 */
	public static final String maskString(String input, int unmaskedPrefix, int unmaskedSuffix, char maskChar) {
		if (StringUtils.isBlank(input) || input.length() < unmaskedPrefix + unmaskedSuffix) {
			return input;
		}
		
		String prefixString = input.substring(0, unmaskedPrefix);
		String suffixString = input.substring(input.length() - unmaskedSuffix);
		
		int charsLengthToMask = input.length() - unmaskedPrefix - unmaskedSuffix;
		StringBuilder mask = new StringBuilder();
		for (int i = 0; i < charsLengthToMask; i++) {
			mask.append(maskChar);
		}
		
		return prefixString + mask.toString() + suffixString;
	}
	
	public static final String maskName(String name) {
		String[] separated = name.split(" ");
		StringBuilder masked = new StringBuilder();
		for (String n : separated) {
			String nMasked;
			if (n.length() > 6) {
				nMasked = maskString(n, 2, 2, '*');
			} else {
				nMasked = maskString(n, 1, 1, '*');
			}
			
			if (masked.toString().length() == 0) {
				masked.append(nMasked);
			} else {
				masked.append(" ").append(nMasked);
			}
			
		}
		
		return masked.toString();
	}
	
	public static final String maskData(String data) {
		
		if (StringUtils.isBlank(data)) {
			return data;
		}
		
		String masked = null;
		
		if (StringUtils.isNumeric(data)) {
			// mask phone
			masked = maskString(data, 4, 3, '*');
			
		} else if (data.contains("@")) {
			// mask email
			String beforeAt = data.split("@")[0];
			int prefix = beforeAt.length() > 7 ? 5 : beforeAt.length()/2;
			masked = maskEmailAddress(data, prefix);
			
		} else {
			// mask name
			masked = maskName(data);
			
		}
		
		return StringUtils.left(masked, 36);
		
	}
	
}
