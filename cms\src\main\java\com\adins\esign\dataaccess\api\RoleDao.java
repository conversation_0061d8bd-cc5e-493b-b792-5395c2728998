package com.adins.esign.dataaccess.api;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.ListRoleManagamentBean;

public interface RoleDao {
	// am_msrole
	void insertRole(AmMsrole newRole);
	void updateRole(AmMsrole role);
	
	AmMsrole getRoleById(long idRole);
	AmMsrole getRoleByCodeAndTenantCode(String roleCode, String tenantCode);
	AmMsrole getRoleByCodeAndTenantCodeV2(String roleCode, String tenantCode);
	AmMsrole getRoleByCodeAndTenantCodeNewTran(String roleCode, String tenantCode);
	AmMsrole getDataRoleByIdUser(long idMsUser, String roleCode, String tenantCode);
	AmMsrole getRoleByCode(String roleCode, String tenantCode);
	AmMsrole getRoleUserManagementByTenant(String roleCode, String tenantCode);
	AmMsrole getRoleByName(String roleName, String tenantCode);
	AmMsrole getRoleByCode(String roleCode);
	
	List<AmMsrole> getListRoleByIdUser(long idMsUser);
	List<AmMsrole> getListRoleByIdUserTenantCode(long idMsUser, String tenantCode);
	
	List<ListRoleManagamentBean> getListRoleManagement(String roleName, String tenantCode, String status, int min, int max);
	
	BigInteger countListRoleManagement(String roleName, String tenantCode, String status);
	
	// am_memberofrole
	void insertMemberOfRole(AmMemberofrole memberOfRole);
	void insertMemberOfRoleNewTran(AmMemberofrole memberOfRole);
	void updateMemberofRole(AmMemberofrole memberofRole);
	
	AmMemberofrole getRoleIsAdmin(String callerId);
	AmMemberofrole getMemberofroleByUser(AmMsuser user);
	AmMemberofrole getMemberofroleByLoginIdRoleTenantCode(String loginId, String tenantCode);
	AmMemberofrole getMemberofroleByAmMsuserAndMsTenant(AmMsuser user, MsTenant tenant);
	AmMemberofrole getMemberofrole(MsTenant tenant, AmMsuser user, String roleCode);
	AmMemberofrole getMemberofrole(String loginId, AmMsrole role);
	AmMemberofrole getMemberofrole(AmMsuser user, AmMsrole role);
	AmMemberofrole getMemberofroleNewTran(AmMsuser user, AmMsrole role);
	
	List<AmMemberofrole> getListMemberofroleByRoleId(long idRole);
	
	boolean isMenuAvailableForTenantUser(String loginId, String tenantCode, String menuCode);
	List<Map<String, Object>> getRoleListByUserManagement(String tenantCodeR);
	
}
