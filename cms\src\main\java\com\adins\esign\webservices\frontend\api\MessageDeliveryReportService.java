package com.adins.esign.webservices.frontend.api;

import com.adins.esign.webservices.model.GetListDeliveryReportForMessageCheckingRequest;
import com.adins.esign.webservices.model.GetListDeliveryReportForMessageCheckingResponse;
import com.adins.esign.webservices.model.GetListMessageDeliveryReportRequest;
import com.adins.esign.webservices.model.GetListMessageDeliveryReportResponse;

public interface MessageDeliveryReportService {

	GetListMessageDeliveryReportResponse listMessageDeliveryReport (GetListMessageDeliveryReportRequest request);
	GetListDeliveryReportForMessageCheckingResponse listDeliveryReportForMessageChecking(GetListDeliveryReportForMessageCheckingRequest request);
}
