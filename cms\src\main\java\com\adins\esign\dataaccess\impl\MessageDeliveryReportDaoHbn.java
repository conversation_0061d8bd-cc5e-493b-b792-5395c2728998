package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.MessageDeliveryReportDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrMessageDeliveryReport;
import com.adins.esign.model.custom.GetListDeliveryReportForMessageCheckingResponseBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.GetListDeliveryReportForMessageCheckingResponse;
import com.adins.esign.webservices.model.GetListMessageDeliveryReportRequest;

@Transactional
@Component
public class MessageDeliveryReportDaoHbn extends BaseDaoHbn implements MessageDeliveryReportDao {

	@Override
	public List<Map<String, Object>> getListMessageDelivery(GetListMessageDeliveryReportRequest request, int min,
			int max, Date reportTimeStart, Date reportTimeEnd, Date requestTimeStart, Date requestTimeEnd, MsLov lov) {

		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		params.put("tenantCode", StringUtils.upperCase(request.getTenantCode()));

		String paramQuery = this.constructParamMessage(params, request.getvendorCode(), reportTimeStart, reportTimeEnd,
				requestTimeStart, requestTimeEnd, lov.getCode(), request.getRecipient(), request.getDeliveryStatus());

		StringBuilder query = new StringBuilder();
		query.append(" with listMessageDeliveryReport AS ( ")
				.append(" select mv.vendor_name AS \"vendorName\", ")
				.append(" mdr.report_time AS \"reportTime\", ")
				.append("COALESCE(mdr.request_time, NULL) AS \"requestTime\", ")
				.append(" mdr.recipient_detail AS \"recipient\", ")
				.append(" mdr.trx_no AS \"trxNo\", ")
				.append(" ml.description AS \"messageMedia\", ")
				.append(" mdr.delivery_status AS \"deliveryStatus\", "
						+ "row_number() over (order by mdr.report_time DESC) AS \"rowNum\" ")
				.append(" FROM tr_message_delivery_report mdr ")
				.append(" JOIN ms_vendor mv ON (mdr.id_ms_vendor = mv.id_ms_vendor) ")
				.append(" JOIN ms_lov ml ON (mdr.lov_message_media = ml.id_lov) ")
				.append(" JOIN ms_tenant mt ON (mdr.id_ms_tenant = mt.id_ms_tenant) ")
				.append(" WHERE mt.tenant_code = :tenantCode ")
				.append(paramQuery)
				.append(" order by mdr.report_time DESC ")
				.append(" ) ")
				.append(" SELECT \"vendorName\",\"reportTime\",\"requestTime\" , \"recipient\", \"trxNo\", ")
				.append(" \"messageMedia\" ,\"deliveryStatus\" FROM listMessageDeliveryReport "
						+ "WHERE \"rowNum\" between :min and :max ");
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	private String constructParamMessage(Map<String, Object> params, String vendorCode, Date reportTimeStart,
			Date reportTimeEnd, Date requestTimeStart, Date requestTimeEnd, String messageMedia,
			String recipient, String deliveryStatus) {

		StringBuilder query = new StringBuilder();

		if (StringUtils.isNotBlank(vendorCode)) {
			query.append(" and mv.vendor_code LIKE :vendorCode ");
			params.put("vendorCode", StringUtils.upperCase(vendorCode));

		}

		if (StringUtils.isNotBlank(messageMedia)) {
			query.append(" and ml.code LIKE :messageMedia ");
			params.put("messageMedia", "%" + messageMedia + "%");
		}

		if (StringUtils.isNotBlank(recipient)) {
			query.append(" and mdr.recipient_detail LIKE :recipient ");
			params.put("recipient", "%" + recipient + "%");
		}

		if (StringUtils.isNotBlank(deliveryStatus)) {
			query.append(" and mdr.delivery_status = :deliveryStatus ");
			params.put("deliveryStatus", deliveryStatus);
		}

		if (reportTimeStart == null && reportTimeEnd == null && requestTimeStart == null && requestTimeEnd == null) {
			query.append("and DATE(mdr.report_time) >= date_trunc('MONTH', now()) and mdr.report_time <= now() ");
			query.append("and DATE(mdr.request_time) >= date_trunc('MONTH', now()) and mdr.request_time <= now() ");
		} else {
			if (reportTimeStart != null && reportTimeEnd != null) {
				query.append(
						"and DATE(mdr.report_time) >= :reportTimeStart and DATE(mdr.report_time) <= :reportTimeEnd ");
				params.put("reportTimeStart", reportTimeStart);
				params.put("reportTimeEnd", reportTimeEnd);
			}
			if (requestTimeStart != null && requestTimeEnd != null) {
				query.append(
						"and DATE(mdr.request_time) >= :requestTimeStart and DATE(mdr.request_time) <= :requestTimeEnd ");
				params.put("requestTimeStart", requestTimeStart);
				params.put("requestTimeEnd", requestTimeEnd);
			}
		}

		return query.toString();

	}

	@Override
	public Integer countListMessageDelivery(GetListMessageDeliveryReportRequest request, int min, int max,
			Date reportTimeStart, Date reportTimeEnd, Date requestTimeStart, Date requestTimeEnd, MsLov lov) {
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		params.put("tenantCode", StringUtils.upperCase(request.getTenantCode()));

		String paramQuery = this.constructParamMessage(params, request.getvendorCode(), reportTimeStart, reportTimeEnd,
				requestTimeStart, requestTimeEnd, lov.getCode(), request.getRecipient(), request.getDeliveryStatus());

		StringBuilder query = new StringBuilder();
		query.append(" with listMessageDeliveryReport AS ( ")
				.append(" select mv.vendor_name AS \"vendorName\", ")
				.append(" mdr.report_time AS \"reportTime\", ")
				.append(" mdr.recipient_detail AS \"recipient\", ")
				.append(" mdr.trx_no AS \"trxNo\", ")
				.append(" ml.description AS \"messageMedia\", ")
				.append(" mdr.delivery_status AS \"deliveryStatus\" ")
				.append(" FROM tr_message_delivery_report mdr ")
				.append(" JOIN ms_vendor mv ON (mdr.id_ms_vendor = mv.id_ms_vendor) ")
				.append(" JOIN ms_lov ml ON (mdr.lov_message_media = ml.id_lov) ")
				.append(" JOIN ms_tenant mt ON (mdr.id_ms_tenant = mt.id_ms_tenant) ")
				.append(" WHERE mt.tenant_code = :tenantCode ")
				.append(paramQuery)
				.append(" order by mdr.report_time DESC ")
				.append(" ) ")
				.append(" SELECT count(*) FROM listMessageDeliveryReport ");
		BigInteger totalData = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);

		return totalData.intValue();
	}

	@Override
	public TrMessageDeliveryReport getWhatsAppMessageDeliveryReport(MsTenant tenant, String phone,
			String deliveryStatus) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant());
		params.put("phone", phone);
		params.put(MsLov.LOV_GROUP_HBM, "MESSAGE_MEDIA");
		params.put(MsLov.CODE_HBM, "WA");
		params.put("deliveryStatus", deliveryStatus);

		StringBuilder query = new StringBuilder();
		query
				.append("select id_message_delivery_report ")
				.append("from tr_message_delivery_report mdr ")
				.append("join ms_lov ml on mdr.lov_message_media = ml.id_lov ")
				.append("where mdr.id_ms_tenant = :idMsTenant ")
				.append("and mdr.recipient_detail = :phone ")
				.append("and ml.lov_group = :lovGroup ")
				.append("and ml.code = :code ")
				.append("and mdr.delivery_status = :deliveryStatus ")
				.append("order by mdr.id_message_delivery_report desc limit 1 ");

		BigInteger idMessageDeliveryReport = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMessageDeliveryReport) {
			return null;
		}

		Map<String, Object> trueParam = new HashMap<>();
		trueParam.put("idMessageDeliveryReport", idMessageDeliveryReport.longValue());
		return managerDAO.selectOne("from TrMessageDeliveryReport mdr "
				+ "where mdr.idMessageDeliveryReport = :idMessageDeliveryReport ", trueParam);
	}

	@Override
	public GetListDeliveryReportForMessageCheckingResponse getListDeliveryReportForMessageChecking(String recipient,
			String messageMedia) {
		StringBuilder query = new StringBuilder();
		query.append(" select cast ( row_number() over (order by tmdr.report_time DESC) as varchar) AS \"no\" ")
				.append(" ,cast(report_time as varchar) as \"reportDate\",ml.description as \"messageMedia\",mv.vendor_name as \"vendor\" ")
				.append(" ,case when tmdr.delivery_status ='1' then 'Waiting' ")
				.append(" when tmdr.delivery_status ='2' then 'Failed' ")
				.append(" when tmdr.delivery_status ='3' then 'Delivered' ")
				.append(" when tmdr.delivery_status ='4' then 'Read' ")
				.append(" else  ")
				.append(" 'Not Started' ")
				.append(" end as \"deliveryStatus\" ")

				.append(" from tr_message_delivery_report tmdr  ")
				.append(" join ms_vendor mv on mv.id_ms_vendor = tmdr.id_ms_vendor  ")
				.append(" join ms_tenant mt on mt.id_ms_tenant = tmdr.id_ms_tenant ")
				.append(" join ms_lov ml on ml.id_lov = tmdr.lov_message_media ")
				.append(" where tmdr.recipient_detail =:recipientDetail ")
				.append(" and ml.code = :messageMedia")
				.append(" and mt.tenant_code ='ADINS' ")
				.append(" and mv.vendor_code ='ESG' ")
				.append(" and Date(report_time) = Date(TO_CHAR(NOW(), 'yyyy-MM-dd')) ");

		Object[][] param = { { "recipientDetail", StringUtils.upperCase(recipient) },
				{ "messageMedia", StringUtils.upperCase(messageMedia) }
		};
		List<GetListDeliveryReportForMessageCheckingResponseBean> listBean = this.managerDAO.selectForListString(
				GetListDeliveryReportForMessageCheckingResponseBean.class, query.toString(), param, null);
		GetListDeliveryReportForMessageCheckingResponse response = new GetListDeliveryReportForMessageCheckingResponse();
		response.setListDeliveryReport(listBean);
		response.setTotalPage(1);
		response.setPage(1);
		response.setTotalResult(listBean.size());
		return response;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertMessageDeliveryReport(TrMessageDeliveryReport messageDeliveryReport) {
		messageDeliveryReport.setUsrCrt(MssTool.maskData(messageDeliveryReport.getUsrCrt()));
		this.managerDAO.insert(messageDeliveryReport);
	}

	@Override
	public Integer countNotificationByPeriod(TrDocumentH docH, MsLov sendingPoint, Date periodStartTime,
			String recipientDetail) {
		Map<String, Object> params = new HashMap<>();
		params.put("sendingPoint", sendingPoint.getIdLov());
		params.put("periodStartTime", periodStartTime);
		params.put("recipientDetail", recipientDetail);

		StringBuilder query = new StringBuilder();
		query.append("SELECT COUNT(1) FROM tr_message_delivery_report mdr ");

		if (docH != null) {
			params.put("idDocH", docH.getIdDocumentH());
			query.append("JOIN tr_balance_mutation tbm ON tbm.trx_no = mdr.trx_no ")
					.append("WHERE tbm.id_document_h = :idDocH ");
		} else {
			query.append("WHERE 1=1 ");
		}

		query.append("AND mdr.lov_sending_point = :sendingPoint ")
				.append("AND mdr.request_time >= :periodStartTime ")
				.append("AND mdr.request_time <= NOW() ")
				.append("AND mdr.recipient_detail = :recipientDetail ")
				.append("AND mdr.delivery_status = '0' ");

		BigInteger count = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		return count.intValue();
	}
}
