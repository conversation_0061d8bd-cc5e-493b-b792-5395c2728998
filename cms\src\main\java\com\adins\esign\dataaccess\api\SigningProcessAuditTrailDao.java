package com.adins.esign.dataaccess.api;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.webservices.model.DownloadAuditTrailExcelRequest;
import com.adins.esign.webservices.model.InquiryAuditTrailSignProcessRequest;

public interface SigningProcessAuditTrailDao {
	void insertSigningProcessAuditTrail(TrSigningProcessAuditTrail auditTrail);
	void insertSigningProcessAuditTrailNewTr(TrSigningProcessAuditTrail auditTrail);
	void updateSigningProcessAuditTrail(TrSigningProcessAuditTrail auditTrail);
	TrSigningProcessAuditTrail getSigningProcessAuditTrailByIdAndEmail(long id,String email);
	void insertSigningProcessAuditTrailDetail(TrSigningProcessAuditTrailDetail auditTrailDetail);
	void insertSigningProcessAuditTrailDetailNewTr(TrSigningProcessAuditTrailDetail auditTrailDetail);
	List<Map<String, Object>> getListInquiryAuditTrail(InquiryAuditTrailSignProcessRequest request, MsVendorRegisteredUser vendorUser, int start, int end);
	BigInteger getCountListInquiryAuditTrail(InquiryAuditTrailSignProcessRequest request, MsVendorRegisteredUser vendorUser);
	List<Map<String, Object>> getInquiryAuditTrailDetail(long idSigningProcessAuditTrail);    
	List<Map<String, Object>> downloadListInquiryAuditTrail(DownloadAuditTrailExcelRequest request,
            AmMsuser user);
	List<TrSigningProcessAuditTrail> getListAuditTrailByIdInvitationLink(long idInvitationLink);
}
