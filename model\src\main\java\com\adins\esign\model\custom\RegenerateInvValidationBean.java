package com.adins.esign.model.custom;

import java.util.Objects;

public class RegenerateInvValidationBean {
    
    private String receiverDetail;
    private String vendorCode;

    public String getReceiverDetail() {
        return this.receiverDetail;
    }

    public void setReceiverDetail(String receiverDetail) {
        this.receiverDetail = receiverDetail;
    }

    public String getVendorCode() {
        return this.vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    @Override
    public boolean equals(Object object) {
        if (this == object) {
            return true;
        }

        if (null == object) {
            return false;
        }

        if (!(object instanceof RegenerateInvValidationBean)) {
            return false;
        }

        RegenerateInvValidationBean bean = (RegenerateInvValidationBean) object;
        
        if (!Objects.equals(receiverDetail, bean.getReceiverDetail())) {
            return false;
        }

        return Objects.equals(vendorCode, bean.getVendorCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.receiverDetail, this.vendorCode);
    }

    @Override
    public String toString() {
        return "{" +
            " receiverDetail='" + getReceiverDetail() + "'" +
            ", vendorCode='" + getVendorCode() + "'" +
            "}";
    }

}
