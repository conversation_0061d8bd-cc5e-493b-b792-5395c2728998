package com.adins.esign.validatorlogic.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.model.MsTenant;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericTenantValidatorLogic extends BaseLogic implements TenantValidatorLogic {
	
	// Test skenario 2
	// Test skenario 11
	
	// Test 123
	
	// test 32134
	
	@Override
	public MsTenant validateGetTenant(String tenantCode, boolean validateTenantExistence, AuditContext audit) {
		if (StringUtils.isBlank(tenantCode)) {
			throw new TenantException(getMessage(GlobalKey.MESSAGE_ERROR_TENANT_CODE_EMPTY, null, audit), ReasonTenant.TENANT_CODE_EMPTY);
		}
		// Test skenario 7
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(tenantCode);
		if (validateTenantExistence && null == tenant) {
			throw new TenantException(getMessage(GlobalKey.MESSAGE_ERROR_TENANT_NOT_FOUND, new String[] {tenantCode}, audit), 
					ReasonTenant.TENANT_NOT_FOUND);
		}
		// Test skenario 14
		return tenant;
	}

}
