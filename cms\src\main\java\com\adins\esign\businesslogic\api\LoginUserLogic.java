package com.adins.esign.businesslogic.api;

import com.adins.am.model.AmMsuser;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface LoginUserLogic {
    // String updateUserAvatar(String uuidUser, String avatar, AuditContext callerId);
	String checkPreRegister(long uuidMsUser, String psreCode, AuditContext auditContext);
	AmMsuser doLogin(String loginId, String password, AuditContext callerId);
}