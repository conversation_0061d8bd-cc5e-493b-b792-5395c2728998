package com.adins.esign.businesslogic.impl;

import java.util.Date;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.model.custom.EmailAttachmentBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.util.MssTool;

@Component
public class SpringEmailSenderLogic extends BaseLogic implements EmailSenderLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(SpringEmailSenderLogic.class);
	
	@Autowired private PersonalDataEncryptionLogic encryptionLogic;
	
	@Autowired @Qualifier("primaryMailSender") private JavaMailSender mailSender;
	@Autowired @Qualifier("backupMailSender") private JavaMailSender backupMailSender;
	
	@Async
	@Override
	public void sendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments, SigningProcessAuditTrailBean auditTrail) throws MessagingException {
		boolean success = true;
		try {
			doSendEmail(emailInfo, attachments);
		} catch (Exception e) {
			success = false;
		}
		
		byte[] encryptedPhone = encryptionLogic.encryptFromString(auditTrail.getPhone());
		TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
		trail.setPhoneNoBytea(encryptedPhone);
		trail.setHashedPhoneNo(MssTool.getHashedString(auditTrail.getPhone()));
		trail.setEmail(StringUtils.upperCase(auditTrail.getEmail()));
		trail.setAmMsUser(auditTrail.getUser());
		trail.setMsTenant(auditTrail.getTenant());
		trail.setMsVendor(auditTrail.getVendorPsre());
		trail.setNotificationMedia("EMAIL");
		trail.setLovSendingPoint(auditTrail.getLovSendingPoint());
		trail.setOtpCode(auditTrail.getOtpCode());
		trail.setLovProcessType(auditTrail.getLovProcessType());
		trail.setResultStatus(success ? "1" : "0");
		trail.setNotes(StringUtils.left(auditTrail.getNotes(), 200));
		trail.setTrInvitationLink(auditTrail.getInvLink());
		trail.setUsrCrt(StringUtils.upperCase(auditTrail.getEmail()));
		trail.setDtmCrt(new Date());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(trail);
		
		if (CollectionUtils.isEmpty(auditTrail.getDocumentDs())) {
			return;
		}
		
		for (TrDocumentD document : auditTrail.getDocumentDs()) {
			TrSigningProcessAuditTrailDetail trailDetail = new TrSigningProcessAuditTrailDetail();
			trailDetail.setSigningProcessAuditTrail(trail);
			trailDetail.setTrDocumentD(document);
			trailDetail.setUsrCrt(StringUtils.upperCase(auditTrail.getEmail()));
			trailDetail.setDtmCrt(new Date());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(trailDetail);
		}
	}
	
	@Async
	@Override
	public void sendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments) throws MessagingException {		
		doSendEmail(emailInfo, attachments);
	}
	
	private void doSendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments) throws MessagingException {
		if (checkEmailDestination(emailInfo.getTo())) {
			throw new IllegalArgumentException("Email destination is required.");
		}

		try {
			this.execSendEmail(mailSender, emailInfo, attachments, false);	
		}
		catch (Exception e) {
			LOG.error("Fail send email from primary", e);
			this.execSendEmail(backupMailSender, emailInfo, attachments, true);
		}
	}

	private void execSendEmail(JavaMailSender mailSender, EmailInformationBean emailInfo, EmailAttachmentBean[] attachments, boolean isBackup) throws MessagingException {				
		LOG.info("Sending mail '{}' to: {}, cc: {}, bcc: {}, backup={}", emailInfo.getSubject(), emailInfo.getTo(), emailInfo.getCc(), emailInfo.getBcc(), isBackup);
		MimeMessage email = mailSender.createMimeMessage();
		MimeMessageHelper helper = new MimeMessageHelper(email, true);
		helper.setTo(emailInfo.getTo());
		helper.setSubject(emailInfo.getSubject());
		helper.setText(emailInfo.getBodyMessage(), true);
		
		//START ESG-825, Nama pengirim diganti berdasarkan menggunakan email-hosting mana
		//Jika tidak disesuaikan, akan terjadi error 436 "MAIL FROM" doesn't conform with authentication
//		if (StringUtils.isNotBlank(emailInfo.getFrom())) {
//			helper.setFrom(emailInfo.getFrom());
//		}		
		helper.setFrom(((JavaMailSenderImpl) mailSender).getUsername());
		//END ESG-825
		
		if (StringUtils.isNotBlank(emailInfo.getReplyTo())) {
			helper.setReplyTo(emailInfo.getReplyTo());
		}
		if (null != emailInfo.getCc()) {
			helper.setCc(emailInfo.getCc());
		}
		if (null != emailInfo.getBcc()) {
			helper.setBcc(emailInfo.getBcc());
		}
		if (null != attachments) {
			checkAttachment(helper, attachments);
		}
		mailSender.send(email);
		LOG.info("Mail '{}' sent to: {}, cc: {}, bcc: {}, backup={}", emailInfo.getSubject(), emailInfo.getTo(), emailInfo.getCc(), emailInfo.getBcc(), isBackup);
	}
	
	private boolean checkEmailDestination(String[] to) {
		return ArrayUtils.isEmpty(to);
	}

	private void checkAttachment(MimeMessageHelper helper, EmailAttachmentBean[] attachments) throws MessagingException {
		for (EmailAttachmentBean attachment : attachments) {
			if (null == attachment.getBinary()) {
				LOG.info("Attachment '{}' is null, skipping attachment", attachment.getFileName());
				continue;
			}
			helper.addAttachment(attachment.getFileName(), new ByteArrayResource(attachment.getBinary()));
		}
	}

	@Override
	public void sendEmailWithoutAsync(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments) throws MessagingException {
		doSendEmail(emailInfo, attachments);
	}

}
