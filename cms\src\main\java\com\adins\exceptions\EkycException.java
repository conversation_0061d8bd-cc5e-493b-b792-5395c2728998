package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class EkycException extends AdInsException {
	private static final long serialVersionUID = 1L;
	
	public enum ReasonEkyc {
		NIK_TIDAK_SESUAI_KETENTUAN,
		NAMA_LENGKAP_TIDAK_SESUAI_KETENTUAN,
		TEMPAT_LAHIR_TIDAK_SESUAI_KETENTUAN,
		TGL_LAHIR_TIDAK_SESUAI_KETENTUAN,
		FOTO_TIDAK_SESUAI_KETENTUAN,
		CUSTOMER_ID_TIDAK_SESUAI_KETENTUAN,
		TRX_ID_TIDAK_SESUAI_KETENTUAN,
		TRX_SOURCE_TIDAK_SESUAI_KETENTUAN,
		REQUEST_TIDAK_DAPAT_DITERUSKAN,
		KESALAHAN_PROSES_DUKCAPIL,
		KESALAHAN_PROSES_FACE_RECOGNITION,
		UNKNOWN
	}
	
	private final ReasonEkyc reason;
	
	public EkycException(ReasonEkyc reason) {
		this.reason = reason;
	}
	
	public EkycException(String message, ReasonEkyc reason) {
		super(message);
		this.reason = reason;
	}
	
	public EkycException(Throwable ex, ReasonEkyc reason) {
		super(ex);
		this.reason = reason;
	}
	
	public EkycException(String message, Throwable ex, ReasonEkyc reason) {
		super(message, ex);
		this.reason = reason;
	}
	

	@Override
	public int getErrorCode() {
		if (null != this.reason) {
			switch (reason) {
			case NIK_TIDAK_SESUAI_KETENTUAN:
				return StatusCode.EKYC_NIK_TIDAK_SESUAI_KETENTUAN;
			case NAMA_LENGKAP_TIDAK_SESUAI_KETENTUAN:
				return StatusCode.EKYC_NAMA_LENGKAP_TIDAK_SESUAI_KETENTUAN;
			case TEMPAT_LAHIR_TIDAK_SESUAI_KETENTUAN:
				return StatusCode.EKYC_TEMPAT_LAHIR_TIDAK_SESUAI_KETENTUAN;
			case TGL_LAHIR_TIDAK_SESUAI_KETENTUAN:
				return StatusCode.EKYC_TGL_LAHIR_TIDAK_SESUAI_KETENTUAN;
			case FOTO_TIDAK_SESUAI_KETENTUAN:
				return StatusCode.EKYC_FOTO_TIDAK_SESUAI_KETENTUAN;
			case CUSTOMER_ID_TIDAK_SESUAI_KETENTUAN:
				return StatusCode.EKYC_CUSTOMER_ID_TIDAK_SESUAI_KETENTUAN;
			case TRX_ID_TIDAK_SESUAI_KETENTUAN:
				return StatusCode.EKYC_TRX_ID_TIDAK_SESUAI_KETENTUAN;
			case TRX_SOURCE_TIDAK_SESUAI_KETENTUAN:
				return StatusCode.EKYC_TRX_SOURCE_TIDAK_SESUAI_KETENTUAN;
			case REQUEST_TIDAK_DAPAT_DITERUSKAN:
				return StatusCode.EKYC_REQUEST_TIDAK_DAPAT_DITERUSKAN;
			case KESALAHAN_PROSES_DUKCAPIL:
				return StatusCode.EKYC_KESALAHAN_PROSES_DUKCAPIL;
			case KESALAHAN_PROSES_FACE_RECOGNITION:
				return StatusCode.EKYC_KESALAHAN_PROSES_FACE_RECOGNITION;
			case UNKNOWN:
				return StatusCode.UNKNOWN;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}

}
