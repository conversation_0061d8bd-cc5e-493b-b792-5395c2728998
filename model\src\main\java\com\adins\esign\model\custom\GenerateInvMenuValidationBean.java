package com.adins.esign.model.custom;

import java.util.Objects;

public class GenerateInvMenuValidationBean {
    
    private String idNo;
    private String psreCode;

    public String getIdNo() {
        return this.idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getPsreCode() {
        return this.psreCode;
    }

    public void setPsreCode(String psreCode) {
        this.psreCode = psreCode;
    }

    @Override
    public boolean equals(Object object) {
        if (this == object) {
            return true;
        }

        if (null == object) {
            return false;
        }

        if (!(object instanceof GenerateInvMenuValidationBean)) {
            return false;
        }

        GenerateInvMenuValidationBean bean = (GenerateInvMenuValidationBean) object;
        
        if (!Objects.equals(idNo, bean.getIdNo())) {
            return false;
        }

        return Objects.equals(psreCode, bean.getPsreCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.idNo, this.psreCode);
    }


    @Override
    public String toString() {
        return "{" +
            " idNo='" + getIdNo() + "'" +
            ", psreCode='" + getPsreCode() + "'" +
            "}";
    }

}
