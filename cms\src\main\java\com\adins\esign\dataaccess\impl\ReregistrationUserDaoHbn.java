package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.ReregistrationUserDao;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrReregistrationUser;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class ReregistrationUserDaoHbn extends BaseDaoHbn implements ReregistrationUserDao {

	@Override
	public void insertReregistrationUser(TrReregistrationUser reregistrationUser) {
		reregistrationUser.setUsrCrt(MssTool.maskData(reregistrationUser.getUsrCrt()));
		this.managerDAO.insert(reregistrationUser);
	}

	@Override
	public TrReregistrationUser getRegistrationUser(String nik, String phone, String email, String vendorCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrReregistrationUser.ID_NO_HBM, nik);
		params.put(TrReregistrationUser.PHONE_HBM, phone);
		params.put(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode));
		params.put("isActive", "1");
		
		// Parameter optional, tidak semua user memiliki email ketika register
		if (StringUtils.isNotBlank(email)) {
			params.put(TrReregistrationUser.EMAIL_HBM, StringUtils.upperCase(email));
		}
		
		StringBuilder query = new StringBuilder();
		query
			.append("from TrReregistrationUser tru ")
			.append("join fetch tru.msVendor mv ")
			.append("where tru.idNo = :idNo ")
			.append("and tru.phone = :phone ")
			.append("and mv.vendorCode = :vendorCode ")
			.append("and tru.isActive = :isActive ");
		
		if (StringUtils.isNotBlank(email)) {
			query.append("and tru.email = :email ");
		} else {
			query.append("and tru.email is null ");
		}
		
		return managerDAO.selectOne(query.toString(), params);
	}

	@Override
	public void updateReregistrationUser(TrReregistrationUser reregistrationUser) {
		reregistrationUser.setUsrUpd(MssTool.maskData(reregistrationUser.getUsrUpd()));
		managerDAO.update(reregistrationUser);
	}

}
