package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.am.model.AmMenuofrole;
import com.adins.am.model.AmMsmenu;
import com.adins.am.model.AmMsrole;
import com.adins.esign.model.custom.ListMenuBean;
import com.adins.esign.model.custom.MenuUserBean;

public interface MenuDao {
	
	// AmMsmenu
	List<AmMsmenu> getMenuByIdRole(long idRole);
	List<MenuUserBean> getMenuByRoleAndTenant(String roleCode, String tenantCode);
	AmMsmenu getMenuByCode(String menuCode);
	AmMsmenu getMenuById(long idMsMenu);
	List<ListMenuBean> getListManageableMenu();
	List<ListMenuBean> getListMenuOfRole(long idRole);
	
	// AmMenuofrole
	void insertMenuOfRole(AmMenuofrole menuOfRole);
	AmMenuofrole getMenuofrole(AmMsmenu menu, AmMsrole role);
	void deleteMenuOfRoleByIdRole(long idRole);
}
