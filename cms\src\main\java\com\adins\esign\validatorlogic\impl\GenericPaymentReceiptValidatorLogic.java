package com.adins.esign.validatorlogic.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.validatorlogic.api.PaymentReceiptValidatorLogic;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptRequest;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.LovException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.LovException.Reason;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericPaymentReceiptValidatorLogic extends BaseLogic implements PaymentReceiptValidatorLogic {
	
	private void validateIdType(String idTypeCode, String idNo, AuditContext audit) {
		if (StringUtils.isBlank(idNo)) {
			String var = getMessage("businesslogic.insertstamping.var.idno", null, audit);
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, audit), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(idTypeCode)) {
			String var = getMessage("businesslogic.insertstamping.var.idtype", null, audit);
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, audit), ReasonDocument.VAR_EMPTY);
		}
		
		MsLov lovIdType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_ID_TYPE, idTypeCode);
		if (null == lovIdType) {
			throw new LovException(getMessage("businesslogic.insertstamping.invalididtype", new Object[] {idTypeCode}, audit), Reason.CODE_INVALID);
		}
		
		if (lovIdType.getCode().equals(GlobalVal.CODE_LOV_TIPE_IDENTITAS_NIK) && idNo.length() != 16) {
			throw new DocumentException(getMessage("businesslogic.user.invalidniklength", null, audit), ReasonDocument.INVALID_ID);
		} else if (!(idNo.length() >= 15 && idNo.length() <= 16)){
			throw new DocumentException(getMessage("businesslogic.user.invalidnpwplength", null, audit), ReasonDocument.INVALID_ID);
		}
	}
	
	private void validateDateFormat(String date, String dateFormat, AuditContext audit) {
		if (StringUtils.isBlank(date)) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.docdate", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
		try {
			sdf.parse(date);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.global.dateformat", new Object[] {date, dateFormat},  this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_DATE_RANGE);
		}
	}

	@Override
	public void validateInsertPaymentReceipt(InsertStampingPaymentReceiptRequest request, AuditContext audit) {
		
		validateDateFormat(request.getDocDate(), GlobalVal.DATE_FORMAT, audit);

		if (StringUtils.isBlank(request.getDocName())) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.docname", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getDocNominal())) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.docnominal", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (!StringUtils.isNumeric(request.getDocNominal()) && !request.getDocNominal().matches("[0-9]{1,17}\\.[0-9]{2}")) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.insertstamping.invaliddocnominal", null, this.retrieveLocaleAudit(audit)), ReasonDocument.DOC_NOMINAL_INVALID);
		}
		
		if (StringUtils.isBlank(request.getDocumentFile())) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.file", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getDocumentNumber())) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.docnumber", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getDocumentTemplateCode())) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.doctemplate", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getDocumentTransactionId())) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.trxid", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		validateIdType(request.getIdType(), request.getIdNo(), audit);
		
		if (StringUtils.isBlank(request.getOfficeCode())) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.officeCode", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getOfficeName())) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.officeName", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getPeruriDocTypeId())) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.doctypeperuri", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getReturnStampResult())) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.returnresult", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getTaxOwedsName())) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.name", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_MANDATORY_PARAM, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByTenantTrxId(request.getDocumentTransactionId());
		if (null != docD) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.document.tenanttrxidexist", new Object[] {request.getDocumentTransactionId()}, this.retrieveLocaleAudit(audit))
					, ReasonDocument.TENANT_TRX_ID_EXIST);
		}
	}

}
