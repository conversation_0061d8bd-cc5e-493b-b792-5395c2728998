package com.adins.esign.job;

import java.util.Set;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.custom.SaveSigningResultDecryptedBean;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class SigningJob {
	private static final Logger LOG = LoggerFactory.getLogger(SigningJob.class);
	@Autowired private DocumentLogic documentLogic;
	
	public void takeQueueSaveSigningResult() {
		Set<SaveSigningResultDecryptedBean> saveSigningResultQ = QueuePublisher.getQueueSaveSigningResult();
		AuditContext audit = new AuditContext(GlobalVal.VENDOR_CODE_DIGISIGN);
		
		saveSigningResultQ.forEach(item -> {
			LOG.debug("Take queue save signing result: {}", item);

			try {
				LOG.info("documentLogic.saveSigningResult: {}", item);
			documentLogic.saveDocumentSignResultWithoutRolesAllowed(item, audit);
			} catch (Exception e) {
				LOG.error("Error on running take queue save signing result: {}", e.getLocalizedMessage(), e);
			}
		});

		saveSigningResultQ.clear();
		LOG.debug("Queue save Signing result empty! Consumer Finished");		
	}
}
