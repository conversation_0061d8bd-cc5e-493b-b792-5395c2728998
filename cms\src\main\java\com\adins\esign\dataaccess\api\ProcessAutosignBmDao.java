package com.adins.esign.dataaccess.api;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.esign.model.TrProcessAutosignBmH;
import com.adins.esign.model.custom.DetailImportAutosignBmBean;
import com.adins.esign.webservices.model.InquiryImportAutosignBmRequest;

public interface ProcessAutosignBmDao {

	void insertTrProcessExcelBm(TrProcessAutosignBmH trProcessExcelBmH);
	List<Map<String, Object>> getListImportProcessAutosignBm(InquiryImportAutosignBmRequest request, int min, int max, Date startDate, Date endDate);
	Integer countListImportProcessAutosignBm(InquiryImportAutosignBmRequest request, int min, int max, Date startDate, Date endDate);
	
	TrProcessAutosignBmH getProcessAutosignBmHeader(String tenantCode, String requestDate, String fileName);
	Integer countListOfProcessAutosignBmDetail(Long idProcessAutosignBmH);
	List<DetailImportAutosignBmBean> getListOfProcessAutosignBmDetail(Long idProcessAutosignBmH, int min, int max);
}
