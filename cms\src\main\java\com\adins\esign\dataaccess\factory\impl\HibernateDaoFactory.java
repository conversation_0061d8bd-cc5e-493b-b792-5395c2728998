package com.adins.esign.dataaccess.factory.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.dataaccess.api.OfficeDao;
import com.adins.esign.dataaccess.api.OtpDao;
import com.adins.esign.dataaccess.api.PaymentSignTypeDao;
import com.adins.esign.dataaccess.api.PeruriDocTypeDao;
import com.adins.esign.dataaccess.api.ProcessAutosignBmDao;
import com.adins.esign.dataaccess.api.ProvinceDao;
import com.adins.esign.dataaccess.api.RegionDao;
import com.adins.esign.dataaccess.api.ReregistrationUserDao;
import com.adins.esign.dataaccess.api.BackgroundProcessFailDao;
import com.adins.esign.dataaccess.api.BalanceMutationDao;
import com.adins.esign.dataaccess.api.BalanceTopUpDao;
import com.adins.esign.dataaccess.api.BalanceVendoroftenantDao;
import com.adins.esign.dataaccess.api.BusinessLineDao;
import com.adins.esign.dataaccess.api.ClientCallbackRequestDao;
import com.adins.esign.dataaccess.api.CommonDao;
import com.adins.esign.dataaccess.api.DailyRecapDao;
import com.adins.esign.dataaccess.api.DistrictDao;
import com.adins.esign.dataaccess.api.DocumentDao;
import com.adins.esign.dataaccess.api.DocumentInquiryDao;
import com.adins.esign.dataaccess.api.DocumentSigningRequestDao;
import com.adins.esign.dataaccess.api.EmailDao;
import com.adins.esign.dataaccess.api.ErrorHistoryDao;
import com.adins.esign.dataaccess.api.FaceVerifyDao;
import com.adins.esign.dataaccess.api.FeedbackDao;
import com.adins.esign.dataaccess.api.GeneralSettingDao;
import com.adins.esign.dataaccess.api.InvitationLinkDao;
import com.adins.esign.dataaccess.api.InvitationLinkHistoryDao;
import com.adins.esign.dataaccess.api.JobCheckRegisterStatusDao;
import com.adins.esign.dataaccess.api.JobDao;
import com.adins.esign.dataaccess.api.JobUpdatePsreIdDao;
import com.adins.esign.dataaccess.api.RoleDao;
import com.adins.esign.dataaccess.api.SchedulerJobDao;
import com.adins.esign.dataaccess.api.SignLinkRequestDao;
import com.adins.esign.dataaccess.api.SigningProcessAuditTrailDao;
import com.adins.esign.dataaccess.api.StampDutyDao;
import com.adins.esign.dataaccess.api.SubDistrictDao;
import com.adins.esign.dataaccess.api.LogDao;
import com.adins.esign.dataaccess.api.LovDao;
import com.adins.esign.dataaccess.api.ManualReportDao;
import com.adins.esign.dataaccess.api.MenuDao;
import com.adins.esign.dataaccess.api.MessageDeliveryReportDao;
import com.adins.esign.dataaccess.api.MsgTemplateDao;
import com.adins.esign.dataaccess.api.NotificationtypeoftenantDao;
import com.adins.esign.dataaccess.api.VendorDao;
import com.adins.esign.dataaccess.api.VendorRegisteredUserDao;
import com.adins.esign.dataaccess.api.VendorRegisteredUserHistoryDao;
import com.adins.esign.dataaccess.factory.api.DaoFactory;
import com.adins.esign.dataaccess.api.TenantDao;
import com.adins.esign.dataaccess.api.TenantSettingsDao;
import com.adins.esign.dataaccess.api.UrlForwarderDao;
import com.adins.esign.dataaccess.api.UserDao;
import com.adins.esign.dataaccess.api.UserDataAccessLogDao;
import com.adins.esign.dataaccess.api.UserActivityLogDao;
import com.adins.esign.dataaccess.api.UserHistoryDao;
import com.adins.esign.dataaccess.api.UseroftenantDao;
import com.adins.esign.dataaccess.api.OauthAccessTokenDBDao;

@Component
public class HibernateDaoFactory implements DaoFactory {
	
	@Autowired private UserDao userDao;
	@Autowired private OfficeDao officeDao;
	@Autowired private GeneralSettingDao gensetDao;
	@Autowired private RoleDao roleDao;
	@Autowired private LovDao lovDao;
	@Autowired private MenuDao menuDao;
	@Autowired private CommonDao commonDao;
	@Autowired private VendorDao vendorDao;
	@Autowired private MsgTemplateDao msgTemplateDao;
	@Autowired private DocumentDao documentDao;
	@Autowired private DocumentInquiryDao documentInquiryDao;
	@Autowired private LogDao logDao;
	@Autowired private TenantDao tenantDao;
	@Autowired private StampDutyDao stampDutyDao;
	@Autowired private BalanceMutationDao balanceMutationDao;
	@Autowired private FeedbackDao feedbackDao;
	@Autowired private DailyRecapDao dailyRecapDao;
	@Autowired private PaymentSignTypeDao paymentSignTypeDao;
	@Autowired private EmailDao emailDao;
	@Autowired private SchedulerJobDao schedulerJobDao;
	@Autowired private FaceVerifyDao faceVerifyDao;
	@Autowired private BusinessLineDao businessLineDao;
	@Autowired private RegionDao regionDao;
	@Autowired private InvitationLinkDao invitationLinkDao;
	@Autowired private BalanceVendoroftenantDao balanceVendoroftenantDao;
	@Autowired private OtpDao otpDao;
	@Autowired private ErrorHistoryDao errorHistoryDao;
	@Autowired private ProvinceDao provinceDao;
	@Autowired private SubDistrictDao subDistricDao;
	@Autowired private DistrictDao districDao;
	@Autowired private ReregistrationUserDao reregistrationUserDao;
	@Autowired private PeruriDocTypeDao peruriDocTypeDao;
	@Autowired private JobDao jobDao;
	@Autowired private UseroftenantDao useroftenantDao;
	@Autowired private VendorRegisteredUserDao vendorRegisteredUserDao;
	@Autowired private DocumentSigningRequestDao documentSigningRequestDao;
	@Autowired private SignLinkRequestDao signLinkRequestDao;
	@Autowired private JobUpdatePsreIdDao jobUpdatePsreIdDao;
	@Autowired private JobCheckRegisterStatusDao jobCheckRegisterStatusDao;
	@Autowired private MessageDeliveryReportDao messageDeliveryReportDao;
	@Autowired private ClientCallbackRequestDao clientCallbackRequestDao;
	@Autowired private UrlForwarderDao urlForwarderDao;
	@Autowired private NotificationtypeoftenantDao notificationtypeoftenantDao;
	@Autowired private UserDataAccessLogDao userDataAccessLogDao;
	@Autowired private UserActivityLogDao userActivityLogDao;
	@Autowired private TenantSettingsDao tenantSettingsDao;
	@Autowired private OauthAccessTokenDBDao oauthAccessTokenDBDao;
	@Autowired private ProcessAutosignBmDao processAutosignBmDao;
	@Autowired private UserHistoryDao userHistoryDao;
	@Autowired private VendorRegisteredUserHistoryDao vendorRegisteredUserHistoryDao;
	@Autowired private SigningProcessAuditTrailDao signingProcessAuditTrailDao;
	@Autowired private InvitationLinkHistoryDao invitationLinkHistoryDao;
	@Autowired private ManualReportDao manualReportDao;
	@Autowired private BalanceTopUpDao balanceTopUpDao;
	@Autowired private BackgroundProcessFailDao backgroundProcessFailDao;
	
	
	@Override
	public OfficeDao getOfficeDao() {
		return officeDao;
	}

	@Override
	public UserDao getUserDao() {
		return userDao;
	}

	@Override
	public GeneralSettingDao getGeneralSettingDao() {
		return gensetDao;
	}

	@Override
	public RoleDao getRoleDao() {
		return roleDao;
	}

	@Override
	public LovDao getLovDao() {
		return lovDao;
	}

	@Override
	public MenuDao getMenuDao() {
		return menuDao;
	}

	@Override
	public CommonDao getCommonDao() {
		return commonDao;
	}
	
	@Override
	public VendorDao getVendorDao() {
		return vendorDao;
	}

	@Override
	public MsgTemplateDao getMsgTemplateDao() {
		return msgTemplateDao;
	}
	
	@Override
	public DocumentDao getDocumentDao() {
		return documentDao;
	}

	@Override
	public LogDao getLogDao() {
		return logDao;
	}
	
	@Override
	public TenantDao getTenantDao() {
		return tenantDao;
	}

	@Override
	public StampDutyDao getStampDutyDao() {
		return stampDutyDao;
	}

	@Override
	public BalanceMutationDao getBalanceMutationDao() {
		return balanceMutationDao;
	}

	@Override
	public FeedbackDao getFeedbackDao() {
		return feedbackDao;
	}

	@Override
	public DailyRecapDao getDailyRecapDao() {
		return dailyRecapDao;
	}

	@Override
	public PaymentSignTypeDao getPaymentSignTypeDao() {
		return paymentSignTypeDao;
	}

	@Override
	public EmailDao getEmailDao() {
		return emailDao;
	}

	@Override
	public SchedulerJobDao getSchedulerJobDao() {
		return schedulerJobDao;
	}

	@Override
	public FaceVerifyDao getFaceVerifyDao() {
		return faceVerifyDao;
	}

	@Override
	public BusinessLineDao getBusinessLineDao() {
		return businessLineDao;
	}

	@Override
	public RegionDao getRegionDao() {
		return regionDao;
	}

	@Override
	public InvitationLinkDao getInvitationLinkDao() {
		return invitationLinkDao;
	}

	@Override
	public BalanceVendoroftenantDao getBalanceVendoroftenantDao() {
		return balanceVendoroftenantDao;
	}
	
	@Override
	public OtpDao getOtpDao() {
		return otpDao;
	}


	@Override
	public ErrorHistoryDao getErrorHistoryDao() {
		return errorHistoryDao;
	}

	@Override
	public ProvinceDao getProvinceDao() {
		return provinceDao;
	}
	
	@Override
	public SubDistrictDao getSubDistrictDao() {
		return subDistricDao;
	}
	
	@Override
	public DistrictDao getDistrictDao() {
		return districDao;
	}
	
	@Override
	public ReregistrationUserDao getReregistrationUserDao() {
		return reregistrationUserDao;
	}

	@Override
	public PeruriDocTypeDao getPeruriDocTypeDao() {
		return peruriDocTypeDao;
	}

	@Override
	public JobDao getJobDao() {
		return jobDao;
	}

	@Override
	public UseroftenantDao getUseroftenantDao() {
		return useroftenantDao;
	}

	@Override
	public VendorRegisteredUserDao getVendorRegisteredUserDao() {
		return vendorRegisteredUserDao;
	}

	@Override
	public DocumentSigningRequestDao getDocumentSigningRequestDao() {
		return documentSigningRequestDao;
	}

	@Override
	public SignLinkRequestDao getSignLinkRequestDao() {
		return signLinkRequestDao;
	}

	@Override
	public JobUpdatePsreIdDao getJobUpdatePsreIdDao() {
		return jobUpdatePsreIdDao;
	}

	@Override
	public JobCheckRegisterStatusDao getJobCheckRegisterStatusDao() {
		return jobCheckRegisterStatusDao;
	}

	@Override
	public MessageDeliveryReportDao getMessageDeliveryReportDao() {
		return messageDeliveryReportDao;
	}

	@Override
	public ClientCallbackRequestDao getClientCallbackRequestDao() {
		return clientCallbackRequestDao;
	}

	@Override
	public UrlForwarderDao getUrlForwarderDao() {
		return urlForwarderDao;
	}

	@Override
	public NotificationtypeoftenantDao getNotificationtypeoftenantDao() {
		return notificationtypeoftenantDao;
	}
	
	@Override
	public UserDataAccessLogDao getUserDataAccessLogDao() {
		return userDataAccessLogDao;
	}

	@Override
	public UserActivityLogDao getUserActivityLogDao() {
		return userActivityLogDao;
	}

	@Override
	public TenantSettingsDao getTenantSettingsDao() {
		return tenantSettingsDao;
	}
	
	@Override
	public OauthAccessTokenDBDao getOauthAccessTokenDBDao() {
		return oauthAccessTokenDBDao;
	}

	@Override
	public ProcessAutosignBmDao getProcessExcelBmDao() {
		return processAutosignBmDao;
	}
	
	@Override
	public UserHistoryDao getUserHistoryDao() {
		return userHistoryDao;
	}
	

	@Override
	public VendorRegisteredUserHistoryDao getVendorRegisteredUserHistoryDao() {
		return vendorRegisteredUserHistoryDao;
	}

	@Override
	public DocumentInquiryDao getDocumentInquiryDao() {
		return documentInquiryDao;
	}

	@Override
	public SigningProcessAuditTrailDao getSigningProcessAuditTrailDao() {
		return signingProcessAuditTrailDao;
	}

	@Override
	public InvitationLinkHistoryDao getInvitationLinkHistoryDao() {
		return invitationLinkHistoryDao;
	}

	@Override
	public ManualReportDao getManualReportDao() {
		return manualReportDao;
	}

	@Override
	public BalanceTopUpDao getBalanceTopUpDao() {
		return balanceTopUpDao;
	}

	@Override
	public BackgroundProcessFailDao getBackgroundProcessFailDao() {
		return backgroundProcessFailDao;
	}
}
