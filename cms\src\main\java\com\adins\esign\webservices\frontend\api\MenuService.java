package com.adins.esign.webservices.frontend.api;

import com.adins.esign.webservices.model.GetListMenuOfRoleRequest;
import com.adins.esign.webservices.model.GetListMenuResponse;
import com.adins.esign.webservices.model.UpdateMenuOfRoleRequest;
import com.adins.framework.service.base.model.MssRequestType;
import com.adins.framework.service.base.model.MssResponseType;

public interface MenuService {
	MssResponseType updateMenuOfRole(UpdateMenuOfRoleRequest request);
	GetListMenuResponse getListManageableMenu(MssRequestType request);
	GetListMenuResponse getListMenuOfRole(GetListMenuOfRoleRequest request);
}
