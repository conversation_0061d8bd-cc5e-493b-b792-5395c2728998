package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.SchedulerJobDao;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrSchedulerJob;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class SchedulerJobDaoHbn extends BaseDaoHbn implements SchedulerJobDao {
	
	@Override
	public void insertSchedulerJob(TrSchedulerJob schedulerJob) {
		schedulerJob.setUsrCrt(MssTool.maskData(schedulerJob.getUsrCrt()));
		managerDAO.insert(schedulerJob);	
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertSchedulerJobNewTrx(TrSchedulerJob schedulerJob) {
		schedulerJob.setUsrCrt(MssTool.maskData(schedulerJob.getUsrCrt()));
		managerDAO.insert(schedulerJob);	
	}
	
	@Override
	public void updateSchedulerJob(TrSchedulerJob schedulerJob) {
		schedulerJob.setUsrUpd(MssTool.maskData(schedulerJob.getUsrUpd()));
		managerDAO.update(schedulerJob);			
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateSchedulerJobNewTrx(TrSchedulerJob schedulerJob) {
		schedulerJob.setUsrUpd(MssTool.maskData(schedulerJob.getUsrUpd()));
		managerDAO.update(schedulerJob);			
	}

	@Override
	public TrSchedulerJob getSchedulerJob(String dateRecap, String jobType, String balanceType, MsTenant tenant) {
		Object [][] params = new Object[5][2] ;
		String balanceTypeQuery = this.constructParamsGetSchedJob(params, dateRecap, jobType, balanceType, tenant);
		
		StringBuilder sbQuery = new StringBuilder();
			sbQuery.append("from TrSchedulerJob sj ")
				.append(" join fetch sj.msLovByJobType ljt ")
				.append(" left join fetch sj.msLovByBalanceType lbt ")
				.append(" join fetch sj.msLovBySchedulerType lst ")
				.append(" where ljt.code=:jobType ")
				.append(balanceTypeQuery)
				.append(" and sj.schedulerStart >= :dateRecapStart ")
				.append(" and sj.schedulerStart <= :dateRecapEnd ")
				.append(" and sj.msTenant = :tenant");
		
		return this.managerDAO.selectOne(sbQuery.toString(), params);
	}

	@Override
	public TrSchedulerJob getSchedulerJobByTenant(String dateRecap, String jobType, String balanceType,String tenantCode ) {
		Object [][] params = new Object[6][2] ;
		String balanceTypeQuery = this.constructParamsGetSchedJob(params, dateRecap, jobType, balanceType,null);
		params[5][0] = 	"tenantCode";
		params[5][1] = 	tenantCode;
		StringBuilder sbQuery = new StringBuilder();
			sbQuery.append("from TrSchedulerJob sj ")
				.append(" join fetch sj.msLovByJobType ljt ")
				.append(" left join fetch sj.msLovByBalanceType lbt ")
				.append(" join fetch sj.msLovBySchedulerType lst ")
				.append(" join fetch sj.msTenant mt ")
				.append(" where ljt.code=:jobType ")
				.append(balanceTypeQuery)
				.append(" and mt.tenantCode = :tenantCode")
				.append(" and sj.schedulerStart >= :dateRecapStart ")
				.append(" and sj.schedulerStart <= :dateRecapEnd ");
		
		return this.managerDAO.selectOne(sbQuery.toString(), params);
	}
	
	private String constructParamsGetSchedJob(Object[][] params, String dateRecap, String jobType, String balanceType, MsTenant tenant) {

		String balanceTypeQuery = StringUtils.EMPTY;
		
		params[0][0] = 	"dateRecapStart";
		params[0][1] = 	MssTool.formatStringToDate(dateRecap + GlobalVal.SOD_TIME_MILL_SEC, GlobalVal.DATE_TIME_FORMAT_MIL_SEC);
		params[1][0] = 	"dateRecapEnd";
		params[1][1] = 	MssTool.formatStringToDate(dateRecap + GlobalVal.EOD_TIME_MILL_SEC, GlobalVal.DATE_TIME_FORMAT_MIL_SEC);
		params[2][0] = 	"jobType";
		params[2][1] = 	jobType;
		

		if (tenant != null) {
			params[3][0] = "tenant";
			params[3][1] = tenant;
		}

		if(StringUtils.isNotBlank(balanceType)) {
			balanceTypeQuery = " and lbt.code=:balanceType ";
			
			params[4][0] = 	"balanceType";
			params[4][1] = 	balanceType;
		}
		else if(StringUtils.isBlank(balanceType)) {
			balanceTypeQuery = " and lbt.code is null ";
		}	
		return balanceTypeQuery;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrSchedulerJob getSchedulerJobNewTrx(String dateRecap, String jobType, String balanceType) {
		Object [][] params = new Object[4][2] ;
		String balanceTypeQuery = this.constructParamsGetSchedJob(params, dateRecap, jobType, balanceType, null);
		
		StringBuilder sbQuery = new StringBuilder();
			sbQuery.append("from TrSchedulerJob sj ")
				.append(" join fetch sj.msLovByJobType ljt ")
				.append(" left join fetch sj.msLovByBalanceType lbt ")
				.append(" join fetch sj.msLovBySchedulerType lst ")
				.append(" where ljt.code=:jobType ")
				.append(balanceTypeQuery)
				.append(" and sj.schedulerStart >= :dateRecapStart ")
				.append(" and sj.schedulerStart <= :dateRecapEnd ");
		
		return this.managerDAO.selectOne(sbQuery.toString(), params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrSchedulerJob getSchedulerJobNewTrx(MsTenant tenant, String dateRecap, String jobType, String balanceType) {
		Map<String, Object> params = new HashMap<>();
		params.put("tenant", tenant);
		params.put("dateRecapStart", MssTool.formatStringToDate(dateRecap + GlobalVal.SOD_TIME_MILL_SEC, GlobalVal.DATE_TIME_FORMAT_MIL_SEC));
		params.put("dateRecapEnd", MssTool.formatStringToDate(dateRecap + GlobalVal.EOD_TIME_MILL_SEC, GlobalVal.DATE_TIME_FORMAT_MIL_SEC));
		params.put("balanceType", StringUtils.upperCase(balanceType));
		params.put("jobType", StringUtils.upperCase(jobType));
		
		return managerDAO.selectOne(
				"from TrSchedulerJob sj "
				+ "join fetch sj.msLovByJobType ljt "
				+ "left join fetch sj.msLovByBalanceType lbt "
				+ "join fetch sj.msLovBySchedulerType lst "
				+ "where ljt.code = :jobType "
				+ "and lbt.code = :balanceType "
				+ "and sj.schedulerStart >= :dateRecapStart "
				+ "and sj.schedulerStart <= :dateRecapEnd "
				+ "and sj.msTenant = :tenant ", params);
	}
}
