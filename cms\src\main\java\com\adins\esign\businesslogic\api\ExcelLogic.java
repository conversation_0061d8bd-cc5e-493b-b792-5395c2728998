package com.adins.esign.businesslogic.api;

import java.io.IOException;

import javax.annotation.security.RolesAllowed;

import com.adins.esign.util.OnExcelCallback;

public interface ExcelLogic {
	@RolesAllowed({"ROLE_DASHBOARD", "ROLE_BALANCE", "ROLE_BALANCE_ALL_TENANT"})
	byte[] generate(OnExcelCallback callback) throws IOException;
	byte[] generateEmbed(OnExcelCallback callback) throws IOException;
}
