package com.adins.esign.businesslogic.api;

import com.adins.esign.webservices.model.RequestSentOtpSigningRequest;
import com.adins.esign.webservices.model.RequestSentOtpSigningResponse;
import com.adins.esign.webservices.model.external.CheckVerificationStatusExternalRequest;
import com.adins.esign.webservices.model.external.CheckVerificationStatusExternalResponse;
import com.adins.esign.webservices.model.external.GetActivationLinkRequest;
import com.adins.esign.webservices.model.external.GetActivationLinkResponse;
import com.adins.esign.webservices.model.external.UpdateDataSignerExternalRequest;
import com.adins.esign.webservices.model.external.UpdateDataSignerExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface UserExternalLogic {
	CheckVerificationStatusExternalResponse checkVerificationStatusExternal(CheckVerificationStatusExternalRequest request, String xApiKey, AuditContext audit);
	GetActivationLinkResponse getActivationLinkExternal(GetActivationLinkRequest request, String xApiKey, AuditContext audit);
	RequestSentOtpSigningResponse sendOtpExternal(RequestSentOtpSigningRequest request, String xApiKey, AuditContext audit);
	UpdateDataSignerExternalResponse updateDataSignerExternal(UpdateDataSignerExternalRequest request, String xApiKey, AuditContext audit);
}
