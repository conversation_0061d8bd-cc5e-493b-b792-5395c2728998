package com.adins.esign.businesslogic.impl;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
//import com.adins.am.model.AmUserAutofillData;
import com.adins.am.model.AmUserpwdhistory;
import com.adins.esign.businesslogic.api.TestSetUpLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
//import com.adins.esign.model.MsBranch;
//import com.adins.esign.model.MsDocumentTemplate;
//import com.adins.esign.model.MsJob;
import com.adins.esign.model.MsLov;
//import com.adins.esign.model.MsPsre;
//import com.adins.esign.model.MsPsreRegisteredUser;
//import com.adins.esign.model.MsSignLocation;
import com.adins.esign.model.MsTenant;
//import com.adins.esign.model.TrAgreement;
//import com.adins.esign.model.TrDocument;
import com.adins.esign.model.custom.SignLocationBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.ZipcodeCityBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.DocumentConfinsRequest;
import com.adins.esign.webservices.model.DocumentTemplateGetOneRequest;
import com.adins.esign.webservices.model.SignLocationRequest;
import com.adins.framework.tool.password.PasswordHash;
import com.google.gson.Gson;

@Component
public class GenericTestSetUpLogic extends BaseLogic implements TestSetUpLogic {
//	@Autowired private Gson gson;
//	
//	@Transactional
//	@Override
//	public MsJob setUpJob(String jobCode, String isBranch, MsJob parentJob){
//
//    	MsJob newJob = new MsJob();
//    	newJob.setIsActive("1");
//    	newJob.setUsrCrt("JUNIT");
//    	newJob.setDtmCrt(new Date());
//    	newJob.setJobCode(jobCode);
//    	newJob.setDescription(jobCode + " DESC");
//    	newJob.setIsBranch(isBranch);
//    	newJob.setIsFieldPerson("0");
//    	newJob.setMsJob(parentJob);
//    	
//    	daoFactory.getJobDao().insertJob(newJob);
//    	
//    	return daoFactory.getJobDao().getJobByCode(jobCode);
//	}
//	
//	@Transactional
//	@Override	
//	public MsBranch setUpBranch(String branchCode, String branchName, MsBranch parentBranch, MsTenant tenant){
//		MsBranch newBranch = new MsBranch();
//		newBranch.setIsActive("1");
//		newBranch.setUsrCrt("JUNIT");
//		newBranch.setDtmCrt(new Date());
//		newBranch.setBranchCode(branchCode);
//		newBranch.setBranchName(branchName);
//		newBranch.setMsBranch(parentBranch);
//		newBranch.setMsTenant(tenant);
//		
//		daoFactory.getBranchDao().insertBranch(newBranch);
//		
//		return daoFactory.getBranchDao().getActiveBranchByCode(branchCode);
//	}
//	
//	@Transactional
//	@Override
//	public AmMsuser setUpUser(String loginId, String fullName, MsJob job, MsBranch branch, AmMsuser userSpv, AmMssubsystem subsystem){
//    	AmMsuser user = new AmMsuser();
//    	user.setIsActive("1");
//    	user.setIsDeleted("0");
//    	user.setUsrCrt("JUNIT");
//    	user.setDtmCrt(new Date());
//    	user.setLoginId(loginId);
//    	user.setFullName(fullName);
//    	user.setInitialName("UT");
//    	user.setEmail(loginId);
//    	user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);				
//    	user.setPassword(PasswordHash.createHash(daoFactory.getGeneralSettingDao()
//				.getGsValueByCode(GlobalKey.GENERALSETTING_DEFAULT_PASSWORD)));
//    	user.setFailCount(0);
//    	user.setIsLoggedIn("0");
//    	user.setIsLocked("0");
//    	user.setIsPasswordExpired("0");
//    	user.setIsDormant("0");
//    	user.setMsJob(job);
//    	user.setMsBranch(branch);
//    	user.setAmMsuser(userSpv);
//    	user.setAmMssubsystem(subsystem);
//    	user.setPhone("081234567891");
//    	user.setFcmToken("0");
//    	daoFactory.getUserDao().insertUser(user);
//    	
//    	return daoFactory.getUserDao().getUserByLoginId(loginId);
//	}
//	
//	@Transactional
//	@Override
//	public AmUserpwdhistory setUpUserPwdhistory(Date date, AmMsuser user) {
//		AmUserpwdhistory userPwdhistory = new AmUserpwdhistory();
//		
//		userPwdhistory.setUsrCrt("JUNIT");
//		userPwdhistory.setDtmCrt(date);
//		userPwdhistory.setAmMsuser(user);
//		userPwdhistory.setPassword(user.getPassword());
//		userPwdhistory.setChangeType("JUNIT");
//		
//		daoFactory.getUserDao().insertUserPwdhistory(userPwdhistory);
//		
//		return userPwdhistory;
//	}
//	
//	@Transactional
//	@Override
//	public AmUserAutofillData setUpUserAutofillData(AmMsuser user) {
//		AmUserAutofillData autofill = new AmUserAutofillData();
//		autofill.setUsrCrt("JUNIT");
//		autofill.setDtmCrt(new Date());
//		autofill.setAmMsuser(user);
//		autofill.setAddress("Jl. JUNIT");
//		autofill.setGender("M");
//		
//		ZipcodeCityBean zipcode = new ZipcodeCityBean();
//		zipcode.setProvinsi("PROVINSI JUNIT");
//		zipcode.setKelurahan("KEL JUNIT");
//		zipcode.setKecamatan("KEC JUNIT");
//		zipcode.setKota("KOTA JUNIT");
//		zipcode.setZipcode("12345");
//		autofill.setZipcodeBean(zipcode);
//		
//		autofill.setPlaceOfBirth("POB JUNIT");
//		autofill.setDateOfBirth(new Date());
//		autofill.setIdNo("1234567890123456");
//		daoFactory.getUserDao().insertUserAutofillData(autofill);
//		
//		return daoFactory.getUserDao().getUserDataByUuidUser(user.getUuidMsUser());
//	}
//	
//	@Transactional
//	@Override
//	public AmUserAutofillData setUpUserAutofillDataWithoutZipCode(AmMsuser user) {
//		AmUserAutofillData autofill = new AmUserAutofillData();
//		autofill.setUsrCrt("JUNIT");
//		autofill.setDtmCrt(new Date());
//		autofill.setAmMsuser(user);
//		autofill.setAddress("Jl. JUNIT");
//		autofill.setGender("M");
//		autofill.setZipcodeBean(null);
//		
//		autofill.setPlaceOfBirth("JUNIT");
//		autofill.setDateOfBirth(new Date());
//		autofill.setIdNo("1234567890123456");
//		daoFactory.getUserDao().insertUserAutofillData(autofill);
//		
//		return daoFactory.getUserDao().getUserDataByUuidUser(user.getUuidMsUser());
//	}
//	
//	@Override
//	public AmUsereventlog setUpUserEventLog(AmMsuser amMsuser, String activity, String consequence) {
//		AmUsereventlog newUserEventLog = new AmUsereventlog();
//		newUserEventLog.setAppEntityBean(new AppEntityBean());
//		newUserEventLog.setAmMsuser(amMsuser);
//		newUserEventLog.setActivityDate(new Date());
//		newUserEventLog.setActivity(activity);
//		newUserEventLog.setConsequence(consequence);
//		
//		daoFactory.getUserDao().insertUserEventLog(newUserEventLog);
//		
//		return daoFactory.getUserDao().getUserEventLogByActivity(activity);
//	}
//	
//	@Override
//	public UserBean setUpUserBean(AmMsuser user) {
//		UserBean userBean = new UserBean();
//		userBean.setLoginId(user.getLoginId());
//		userBean.setUserName(user.getFullName());
//		userBean.setEmail(user.getEmail());
//		userBean.setIdNo("1234567890123456");
//		userBean.setUserPob("POB JUNIT");	
//		userBean.setUserAddress("Jl. JUNIT");
//		userBean.setUserGender("M");
//		userBean.setUserPhone(user.getPhone());
//		userBean.setUserDob(MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_FORMAT));
//		
//		userBean.setProvinsi("PROVINSI JUNIT");
//		userBean.setKecamatan("KEL JUNIT");
//		userBean.setKelurahan("KEC JUNIT");
//		userBean.setKota("KOTA JUNIT");
//		userBean.setZipcode("12345");
//		
//		return userBean;
//	}
//	
//	@Override
//	public MsPsreRegisteredUser setUpPsreRegisteredUser(AmMsuser user, MsPsre psre) {
//		MsPsreRegisteredUser psreRegisteredUser = new MsPsreRegisteredUser();
//
//		psreRegisteredUser.setSignerRegisteredEmail(user.getEmail());
//		psreRegisteredUser.setIsActive("1");
//		psreRegisteredUser.setUsrCrt("JUNIT");
//		psreRegisteredUser.setDtmCrt(new Date());
//		psreRegisteredUser.setAmMsuser(user);
//		psreRegisteredUser.setMsPsre(psre);
//		psreRegisteredUser.setDigisignUserAutosignKey("AUTOKEY JUNIT");
//		
//		daoFactory.getPsreDao().insertPsreRegisteredUser(psreRegisteredUser);
//		
//		return psreRegisteredUser;
//	}
//	
////	
////	@Override
////	public AmGeneralsetting setUpGenSet() {
////		AmGeneralsetting genSet = new AmGeneralsetting();
////		genSet.setIsActive("1");
////		genSet.setUsrCrt("JUNIT");
////		genSet.setDtmCrt(new Date());
////		genSet.setGsCode("GS_JUNIT");
////		genSet.setGsPrompt("GENERAL SETTING JUNIT");
////		genSet.setGsValue("GS VALUE JUNIT");
////		
////    	this.managerDAO.insert(genSet);
////    	
////    	Object[][] sqlParams = { { Restrictions.eq("gsCode", "GS_JUNIT") } };
////    	return this.managerDAO.selectOne(AmGeneralsetting.class, sqlParams);
////	}
////	
////	@Transactional
////	@Override
////	public MsApikey setUpApikey(String key) {
////		MsApikey apikey = new MsApikey();
////		apikey.setApiKey(key);
////		apikey.setCode("JUNIT");
////		apikey.setDescription("DESC");
////		apikey.setDtmCrt(new Date());
////		apikey.setUsrCrt("JUNIT");
////		apikey.setIpAddresses("127.0.0.1");
////    	
////    	this.managerDAO.insert(apikey);
////
////     	return this.managerDAO.selectOne(MsApikey.class,
////    			new Object[][] { { Restrictions.eq("code", "JUNIT") }});
////	}
//
//	@Transactional
//	@Override
//	public MsLov setUpLovByCode(String lovCode) {
//		MsLov lov = new MsLov();
//		lov.setLovGroup("LOVGROUPJUNIT");
//		lov.setCode(lovCode);
//		lov.setDescription("DESCJUNIT");
//		lov.setSequence(10);
//		lov.setIsActive("1");
//		lov.setIsDeleted("0");
//		lov.setConstraint1("CONS1");
//		lov.setConstraint2("CONS2");
//		lov.setConstraint3("CONS3");
//		lov.setConstraint4("CONS4");
//		lov.setConstraint5("CONS5");
//		lov.setUsrCrt("JUNIT");
//		lov.setDtmCrt(new Date());
//		
//		daoFactory.getLovDao().insertLov(lov);
//		
//		return daoFactory.getLovDao().getMsLovByCode(lovCode);
//    }
//	
//	@Transactional
//	@Override
//	public MsLov setUpLovByGroupAndCode(String lovGroup, String lovCode) {
//		MsLov lov = new MsLov();
//		lov.setLovGroup(lovGroup);
//		lov.setCode(lovCode);
//		lov.setDescription("DESCJUNIT");
//		lov.setSequence(10);
//		lov.setIsActive("1");
//		lov.setIsDeleted("0");
//		lov.setConstraint1("CONS1");
//		lov.setConstraint2("CONS2");
//		lov.setConstraint3("CONS3");
//		lov.setConstraint4("CONS4");
//		lov.setConstraint5("CONS5");
//		lov.setUsrCrt("JUNIT");
//		lov.setDtmCrt(new Date());
//		
//		daoFactory.getLovDao().insertLov(lov);
//		
//		return daoFactory.getLovDao().getMsLovByGroupAndCode(lovGroup, lovCode);
//	}
//	    
////	@Transactional
////	@Override
////	public byte[] loadFiletoByteArray(File fileUpload) throws IOException {
////	        InputStream is = new FileInputStream(fileUpload);
////	 
////	        long length = fileUpload.length();
////	        byte[] bytes = new byte[(int)length];
////	         
////	        int offset = 0;
////	        int numRead = 0;
////	        while (offset < bytes.length
////	               && (numRead=is.read(bytes, offset, bytes.length-offset)) >= 0) {
////	            offset += numRead;
////	        }
////            is.close();
////
////	        if (offset < bytes.length) {
////	            throw new IOException("Could not completely read file "+fileUpload.getName());
////	        }
////	 
////	        return bytes;
////	    }
////
////	
////	@Override
////	public void setUpUserPwdHistory(AmMsuser user, boolean isExpired) {
////		long DAY_IN_MS = 1000 * 60 * 60 * 24;
////		
////		AmUserpwdhistory pwdhist = new AmUserpwdhistory();
////		pwdhist.setAmMsuser(user);
////		if(isExpired) {
////			pwdhist.setDtmCrt(new Date(System.currentTimeMillis() - (705 * DAY_IN_MS)));
////		}
////		else {
////			pwdhist.setDtmCrt(new Date());
////		}
////		pwdhist.setPassword("password");
////		pwdhist.setUsrCrt("JUNIT");
////		
////		this.managerDAO.insert(pwdhist);
////	}
////	
//	@Transactional
//	@Override
//	public AmGeneralsetting setUpGenSet(String gsCode) {
//		AmGeneralsetting genSet = new AmGeneralsetting();
//		genSet.setIsActive("1");
//		genSet.setUsrCrt("JUNIT");
//		genSet.setDtmCrt(new Date());
//		genSet.setGsCode(gsCode);
//		genSet.setGsPrompt("GENERAL SETTING JUNIT");
//		genSet.setGsValue("GS VALUE JUNIT");
//		
//		daoFactory.getGeneralSettingDao().insertGs(genSet);
//    	
//		return daoFactory.getGeneralSettingDao().getGsObjByCode(gsCode);
//	}
//
//	@Transactional
//	@Override
//	public MsDocumentTemplate setUpDocumentTemplate(String docTempCode, String docTempName, String isActive, MsTenant tenant) {
//		MsDocumentTemplate docTemp = new MsDocumentTemplate();
//		docTemp.setDocumentTemplateCode(docTempCode);
//		docTemp.setDocumentTemplateName(docTempName);
//		docTemp.setDocumentTemplateDescription("DOCUMENT TEMPLATE JUNIT");
//		docTemp.setNumberOfPage(5);
//		docTemp.setUsrCrt("JUNIT");
//		docTemp.setDtmCrt(new Date());
//		docTemp.setIsActive(isActive);
//		docTemp.setMsTenant(tenant);
//		daoFactory.getDocumentDao().insertDocumentTemplate(docTemp);
//		
//		return daoFactory.getDocumentDao().getDocumentTemplateByCode(docTempCode);
//	}
//
//	@Override
//	public DocumentTemplateRequest setUpDocumentTemplateRequest(String docTempCode, String docTempName, String tenantCode) {
//		DocumentTemplateRequest docReq = new DocumentTemplateRequest();
//		docReq.setDocumentTemplateCode(docTempCode);
//		docReq.setDocumentTemplateName(docTempName);
//		docReq.setDocumentTemplateDescription("DOCUMENT TEMPLATE JUNIT");
//		docReq.setNumberOfPage("5");
//		docReq.setTenantCode(tenantCode);
//		
//		String encodedDocumentExample = new String(Base64.getEncoder().encode("DOCUMENT EXAMPLE JUNIT".getBytes(StandardCharsets.UTF_8)));
//		docReq.setDocumentExample(encodedDocumentExample);
//		
//		return docReq;
//	}
//
//	@Override
//	public SignLocationRequest setUpSignLocationReqest(String docTempCode, String signerTypeCode, String signTypeCode) {
//		SignLocationRequest signLocReq = new SignLocationRequest();
//		signLocReq.setDocumentTemplateCode(docTempCode);
//		
//		SignLocationBean signLocBean = new SignLocationBean();
//		signLocBean.setAksiTtd("mt");
//		signLocBean.setKuser("1234567890123456");
//		signLocBean.setUser("ttd1");
//		signLocBean.setPage("1");
//		signLocBean.setLlx("12");
//		signLocBean.setLly("15");
//		signLocBean.setUrx("12");
//		signLocBean.setUry("15");
//		signLocBean.setVisible("1");
//		signLocReq.setSignLocation(signLocBean);
//		
//		signLocReq.setSignerTypeCode(signerTypeCode);
//		signLocReq.setSignTypeCode(signTypeCode);
//		signLocReq.setSignPage("1");
//		
//		return signLocReq;
//	}
//
//	@Override
//	public DocumentConfinsRequest setUpDocumentConfinsRequest(String docTempCode, String loginId, String branchCode) {
//		DocumentConfinsRequest docConfinsReq = new DocumentConfinsRequest();
//		docConfinsReq.setReferenceNo("REF JUNIT");
//		docConfinsReq.setDocumentId("DOC_JUNIT");
//		docConfinsReq.setBranch(branchCode);
//		docConfinsReq.setDocumentTemplateCode(docTempCode);
//		docConfinsReq.setIsSequence("0");
//		
//		SignerBean signer = new SignerBean();
//		signer.setSignAction("mt");
//		signer.setSignerType(GlobalVal.CODE_LOV_SIGNER_CUSTOMER);
//		signer.setSignSequence("0");
//		signer.setUserAddress("ALAMAT JUNIT");
//		signer.setUserGender("M");
//		signer.setKecamatan("KECAMATAN JUNIT");
//		signer.setKelurahan("KELURAHAN JUNIT");
//		signer.setZipcode("12345");
//		signer.setKota("KOTA JUNIT");
//		signer.setUserName("JUNIT");
//		signer.setUserPhone("081234567891");
//		signer.setUserDob("2000-06-07");
//		signer.setProvinsi("PROVINSI JUNIT");
//		signer.setIdNo("1234567890123456");
//		signer.setUserPob("JAKARTA");
//		signer.setEmail(loginId);
//		signer.setNpwpNo("");
//		signer.setPsreCode("");
//		
//		SignerBean[] listSigner = {signer};
//		
//		docConfinsReq.setSigner(listSigner);
//		
//		String pdf = "";
//		String CLASSPATH_PDF = "classpath:pdf/";
//		String pathPdfText = CLASSPATH_PDF+"pdfBased64.txt";
//		BufferedReader reader = null;
//		
//		try {
//			reader = new BufferedReader(new FileReader(ResourceUtils.getFile(pathPdfText)));
//		} catch (FileNotFoundException e1) {
//			e1.printStackTrace();
//		}
//		
//		try {
//			pdf = reader.readLine();
//		} catch (IOException e1) {
//			e1.printStackTrace();
//		}
//		
//		// String encodedDocumentFile = new String(Base64.getEncoder().encode(pdf.getBytes(StandardCharsets.UTF_8)));
//		
//		docConfinsReq.setDocumentFile(pdf);
//		
//		return docConfinsReq;
//	}
//
//	@Transactional
//	@Override
//	public MsSignLocation setUpSignLocation(MsDocumentTemplate docTemp, MsLov signerType, MsLov signType) {
//		MsSignLocation signLocation = new MsSignLocation();
//		
//		signLocation.setMsDocumentTemplate(docTemp);
//		signLocation.setMsLovSignerType(signerType);
//		signLocation.setMsLovSignType(signType);
//		signLocation.setSignPage(1);
//		
//		SignLocationBean signLocBean = new SignLocationBean();
//		signLocBean.setAksiTtd("mt");
//		signLocBean.setKuser("1234567890123456");
//		signLocBean.setUser("ttd1");
//		signLocBean.setPage("1");
//		signLocBean.setLlx("12");
//		signLocBean.setLly("15");
//		signLocBean.setUrx("12");
//		signLocBean.setUry("15");
//		signLocBean.setVisible("1");
//		
//		signLocation.setSignLocation(gson.toJson(signLocBean));
//		
//		signLocation.setUsrCrt("JUNIT");
//		signLocation.setDtmCrt(new Date());
//		
//		daoFactory.getDocumentDao().insertSignLocation(signLocation);
//		
//		return daoFactory.getDocumentDao().getListSignLocationByTemplateCode(docTemp.getDocumentTemplateCode()).get(0);
//	}
//
//	@Transactional
//	@Override
//	public TrDocument setUpTrDocument(MsDocumentTemplate docTemp, TrAgreement agreement, MsTenant tenant) {
//		TrDocument newDoc = new TrDocument();
//		newDoc.setIsSequence("0");
//		newDoc.setMsLovSignStatus(daoFactory.getLovDao().getMsLovByCode(GlobalVal.CODE_LOV_NEED_SIGN));
//		newDoc.setUsrCrt("JUNIT");
//		newDoc.setDtmCrt(new Date());
//		newDoc.setMsDocumentTemplate(docTemp);
//		newDoc.setTrAgreement(agreement);
//		newDoc.setMsTenant(tenant);
//		
//		String docId = daoFactory.getDocumentDao().insertDocument(newDoc);
//		
//		return daoFactory.getDocumentDao().getDocumentByDocId(docId);
//	}
//	
//	@Transactional
//	@Override
//	public TrAgreement setUpTrAgreement(String referenceNo, AmMsuser user, MsBranch branch, MsTenant tenant,
//			int totalDocument, int totalSigned) {
//		TrAgreement agreement = new TrAgreement();
//		agreement.setAmMsuser(user);
//		agreement.setMsBranch(branch);
//		agreement.setMsTenant(tenant);
//		agreement.setDtmCrt(new Date());
//		agreement.setUsrCrt("JUNIT");
//		agreement.setRefNumber(referenceNo);
//		agreement.setTotalDocument(5);
//		agreement.setTotalSigned(5);
//		daoFactory.getAgreementDao().insertAgreement(agreement);
//		
//		return daoFactory.getAgreementDao().getAgreementByRefNo(referenceNo);
//	}
//	
//	@Transactional
//	@Override
//	public MsTenant setUpTenant(String tenantCode, String tenantName) {
//		MsTenant tenant = new MsTenant();
//		tenant.setTenantCode(tenantCode);
//		tenant.setTenantName(tenantName);
//		tenant.setDtmCrt(new Date());
//		tenant.setUsrCrt("JUNIT");
//		daoFactory.getTenantDao().insertTenant(tenant);
//		
//		return daoFactory.getTenantDao().getTenantByCode(tenantCode);
//	}

}