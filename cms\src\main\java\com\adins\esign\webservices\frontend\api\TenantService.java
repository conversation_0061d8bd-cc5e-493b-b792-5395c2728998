package com.adins.esign.webservices.frontend.api;

import com.adins.esign.webservices.model.AddTenantRequest;
import com.adins.esign.webservices.model.AddTenantResponse;
import com.adins.esign.webservices.model.CheckLivenessFaceCompareServiceRequest;
import com.adins.esign.webservices.model.CheckLivenessFaceCompareServiceResponse;
import com.adins.esign.webservices.model.EditTenantRequest;
import com.adins.esign.webservices.model.EditTenantResponse;
import com.adins.esign.webservices.model.GetAutomaticStampingAfterSignRequest;
import com.adins.esign.webservices.model.GetAutomaticStampingAfterSignResponse;
import com.adins.esign.webservices.model.GetAvailableSendingPointInvitationRequest;
import com.adins.esign.webservices.model.GetAvailableSendingPointInvitationResponse;
import com.adins.esign.webservices.model.GetAvailableSendingPointRequest;
import com.adins.esign.webservices.model.GetAvailableSendingPointResponse;
import com.adins.esign.webservices.model.GetListTenantRequest;
import com.adins.esign.webservices.model.GetListTenantResponse;
import com.adins.esign.webservices.model.GetSmsDeliverySettingRequest;
import com.adins.esign.webservices.model.GetSmsDeliverySettingResponse;
import com.adins.esign.webservices.model.GetStatusEmailServiceTenantRequest;
import com.adins.esign.webservices.model.GetStatusEmailServiceTenantResponse;
import com.adins.esign.webservices.model.GetTenantRekonRequest;
import com.adins.esign.webservices.model.GetTenantRekonResponse;
import com.adins.esign.webservices.model.GetUploadUrlRequest;
import com.adins.esign.webservices.model.GetUploadUrlResponse;
import com.adins.esign.webservices.model.TenantDetailRequest;
import com.adins.esign.webservices.model.TenantDetailResponse;
import com.adins.esign.webservices.model.TenantSettingsEmbedRequest;
import com.adins.esign.webservices.model.TenantSettingsRequest;
import com.adins.esign.webservices.model.TenantSettingsResponse;
import com.adins.esign.webservices.model.TestTenantCallbackRequest;
import com.adins.esign.webservices.model.TestTenantCallbackResponse;
import com.adins.esign.webservices.model.TryTenantCallbackRequest;
import com.adins.esign.webservices.model.TryTenantCallbackResponse;
import com.adins.esign.webservices.model.UpdateDeliverySettingRequest;
import com.adins.esign.webservices.model.UpdateDeliverySettingResponse;
import com.adins.esign.webservices.model.UpdateTenantSettingsRequest;
import com.adins.esign.webservices.model.UpdateTenantSettingsResponse;

public interface TenantService {
	TenantSettingsResponse getTenantSettingsEmbed(TenantSettingsEmbedRequest request);
	TenantSettingsResponse getTenantSettings(TenantSettingsRequest request);
	UpdateTenantSettingsResponse updateTenantSettings(UpdateTenantSettingsRequest request);
	AddTenantResponse addTenant(AddTenantRequest request);
	EditTenantResponse editTenant(EditTenantRequest request);
	TenantDetailResponse getTenantDetail(TenantDetailRequest request);
	GetListTenantResponse getListTenant(GetListTenantRequest request);
	GetSmsDeliverySettingResponse getSmsDeliverySetting(GetSmsDeliverySettingRequest request);
	UpdateDeliverySettingResponse updateDeliverySetting(UpdateDeliverySettingRequest request);
	GetStatusEmailServiceTenantResponse getStatusEmailServiceTenant(GetStatusEmailServiceTenantRequest request);
	GetAutomaticStampingAfterSignResponse getAutomaticStampingAfterSign(GetAutomaticStampingAfterSignRequest request);
	GetUploadUrlResponse getUploadUrl (GetUploadUrlRequest request);
	GetTenantRekonResponse getTenantRekon (GetTenantRekonRequest request);
	CheckLivenessFaceCompareServiceResponse checkLivenessFaceCompareService(CheckLivenessFaceCompareServiceRequest request);
	TestTenantCallbackResponse testCallback(TestTenantCallbackRequest request);
	TryTenantCallbackResponse tryCallback(TryTenantCallbackRequest request);
	GetAvailableSendingPointResponse getAvailableSendingPoint(GetAvailableSendingPointRequest request);
	GetAvailableSendingPointInvitationResponse getAvailableSendingPointInvitation (GetAvailableSendingPointInvitationRequest request);
}
