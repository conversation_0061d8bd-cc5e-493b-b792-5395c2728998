package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsprovince;
import com.adins.esign.dataaccess.api.ProvinceDao;
import com.adins.esign.model.custom.ProvinceBean;
import com.adins.esign.model.custom.ProvinceExternalBean;
import com.adins.esign.util.MssTool;

import io.micrometer.core.instrument.util.StringUtils;

@Transactional
@Component
public class ProvinceDaoHbn extends BaseDaoHbn implements ProvinceDao {

	@Override
	public List<ProvinceBean> getProvinceList(String provinceName) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("provinceName", provinceName);
		
		StringBuilder query = new StringBuilder();
		query.append(" SELECT province_name AS \"namaProvinsi\", ")
		     .append(" id_msprovince AS \"idMsProvinsi\" ")
		     .append(" FROM am_msprovince ");
		
		if (StringUtils.isNotBlank(provinceName)) {
			 query.append(" WHERE province_name = TRIM(:provinceName )");
		}
		
		return this.managerDAO.selectForListString(ProvinceBean.class, query.toString(), params, null);
	}

	@Override
	public AmMsprovince getProvinceById(Long provinceId) {
		if (provinceId == null) {
			return null;
		}
		return this.managerDAO.selectOne(AmMsprovince.class,
				new Object[][] {{ Restrictions.eq(AmMsprovince.PROVINCE_ID_HBM, provinceId) }});
	}
	
	@Override
	public void insertAmMsprovince(AmMsprovince province) {
		province.setUsrCrt(MssTool.maskData(province.getUsrCrt()));
		this.managerDAO.insert(province);
	}

	@Override
	public void updateAmMsprovince(AmMsprovince province) {
		province.setUsrUpd(MssTool.maskData(province.getUsrUpd()));
		this.managerDAO.update(province);				
	}

	@Override
	public AmMsprovince getProvince(Long idMsprovince) {
		return this.managerDAO.selectOne(AmMsprovince.class, 
				new Object[][] {{ Restrictions.eq(AmMsprovince.ID_MSPROVINCE_HBM, idMsprovince) }});
	}

	@Override
	public List<ProvinceExternalBean> getProvinceExternalList() {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		
		query.append(" select province_name AS \"provinceName\" from am_msprovince ORDER by province_name asc ");
		
		return this.managerDAO.selectForListString(ProvinceExternalBean.class, query.toString(), params, null);
	}

	@Override
	public AmMsprovince getProvinceByName(String provinceName) {
		if (provinceName == null) {
			return null;
		}
		return this.managerDAO.selectOne(AmMsprovince.class,
				new Object[][] {{ Restrictions.eq(AmMsprovince.PROVINCE_NAME_HBM, org.apache.commons.lang3.StringUtils.upperCase(provinceName)) }});
	}

	
}
