package com.adins.esign.dataaccess.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;
import com.adins.esign.dataaccess.api.LovDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.LovBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.LovListRequest;
import com.adins.constants.AmGlobalKey;

@Transactional
@Component
@SuppressWarnings("unchecked")
public class LovDaoHbn extends BaseDaoHbn implements LovDao {
	
	@Override
	public MsLov getMsLovByCode (String lovCode) {
		if (StringUtils.isBlank(lovCode))
			return null;
		
		Object[][] queryParams = { 
				{Restrictions.eq(MsLov.CODE_HBM, StringUtils.upperCase(lovCode))},
				{Restrictions.eq(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1")}};
		return this.managerDAO.selectOne(MsLov.class, queryParams);
	}
	

	@Override
	public MsLov getMsLovByGroupAndCode(String lovGroup, String lovCode) {
		if (StringUtils.isBlank(lovGroup) || StringUtils.isBlank(lovCode))
			return null;
		
		Object[][] queryParams = {
				{Restrictions.eq(MsLov.LOV_GROUP_HBM, StringUtils.upperCase(lovGroup))},
				{Restrictions.eq(MsLov.CODE_HBM, StringUtils.upperCase(lovCode))},
				{Restrictions.eq(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1")}};
		return this.managerDAO.selectOne(MsLov.class, queryParams);
	}
	
	
	
	@Override
	public List<LovBean> getMsLovListByGroup(String lovGroup) {
		if (StringUtils.isBlank(lovGroup))
			return Collections.emptyList();

		Object[][] queryParams = {{Restrictions.eq(MsLov.LOV_GROUP_HBM, StringUtils.upperCase(lovGroup))} };
		Object[][] queryOrders = {{MsLov.SEQUENCE_HBM,"ASC"}};
		List<MsLov> lovList = (List<MsLov>) this.managerDAO.list(MsLov.class, queryParams, queryOrders ).get(AmGlobalKey.MAP_RESULT_LIST);
		List<LovBean> listResp = new ArrayList<>();
		for (MsLov lov : lovList ) {
			LovBean customBean = new LovBean();
			customBean.setLovGroupName(lov.getLovGroup());
			customBean.setCode(lov.getCode());
			customBean.setDescription(lov.getDescription());
			listResp.add(customBean);
		}
		return listResp;
	}

	@Override
	public void insertLov(MsLov insertLov) {
		insertLov.setUsrCrt(MssTool.maskData(insertLov.getUsrCrt()));
		this.managerDAO.insert(insertLov);
	}

	@Override
	public void updateLov(MsLov updateLov) {
		updateLov.setUsrUpd(MssTool.maskData(updateLov.getUsrUpd()));
		this.managerDAO.update(updateLov);
	}

	@Override
	public MsLov checkLovExist(MsLov checkLov) {
		Object[][] params = new Object[7][1];
		params[0][0] = Restrictions.eq(MsLov.LOV_GROUP_HBM, StringUtils.upperCase(checkLov.getLovGroup()));
		params[1][0] = Restrictions.eq(MsLov.CODE_HBM, StringUtils.upperCase(checkLov.getCode()));
		
		if( StringUtils.isBlank(checkLov.getConstraint1())){
			params[2][0] = Restrictions.isNull(MsLov.CONSTRAINT_1_HBM);
		}
		else {
			params[2][0] = Restrictions.eq(MsLov.CONSTRAINT_1_HBM, StringUtils.upperCase(checkLov.getConstraint1()));
		}
		
		if( StringUtils.isBlank(checkLov.getConstraint2())){
			params[3][0] = Restrictions.isNull(MsLov.CONSTRAINT_2_HBM);
		}
		else {
			params[3][0] = Restrictions.eq(MsLov.CONSTRAINT_2_HBM, StringUtils.upperCase(checkLov.getConstraint2()));
		}
		
		if( StringUtils.isBlank(checkLov.getConstraint3())){
			params[4][0] = Restrictions.isNull(MsLov.CONSTRAINT_3_HBM);
		}
		else {
			params[4][0] = Restrictions.eq(MsLov.CONSTRAINT_3_HBM, StringUtils.upperCase(checkLov.getConstraint3()));
		}
		
		if( StringUtils.isBlank(checkLov.getConstraint4())){
			params[5][0] = Restrictions.isNull(MsLov.CONSTRAINT_4_HBM);
		}
		else {
			params[5][0] = Restrictions.eq(MsLov.CONSTRAINT_4_HBM, StringUtils.upperCase(checkLov.getConstraint4()));
		}
		
		if( StringUtils.isBlank(checkLov.getConstraint5())){
			params[6][0] = Restrictions.isNull(MsLov.CONSTRAINT_5_HBM);
		}
		else {
			params[6][0] = Restrictions.eq(MsLov.CONSTRAINT_5_HBM, StringUtils.upperCase(checkLov.getConstraint5()));
		}
		return this.getManagerDAO().selectOne(MsLov.class, params);
	}

	@Override
	public int getLastSequence(String lovGroup) {
		Object[][] params2 = { {MsLov.LOV_GROUP_HBM, StringUtils.upperCase(lovGroup)} };
		return (Integer) this.getManagerDAO().selectOneNativeString(
				"select max(sequence) from ms_lov where LOV_GROUP = :lovGroup", params2);
	}

	@Override
	public List<Map<String, Object>> getMsLovListByGroupAndConstraint(LovListRequest request) {
		
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		
		query
			.append("select code, description, sequence, constraint_1, ")
			.append("constraint_2, constraint_3, constraint_4, constraint_5 from ms_lov ")
			.append("where lov_group = :lovGroup and is_active = '1' and is_deleted = '0' ");
		
		params.put(MsLov.LOV_GROUP_HBM, StringUtils.upperCase(request.getLovGroup()));
		
		if (null != request.getConstraint1() && request.getConstraint1().length() > 0) {
			query.append("and constraint_1 = :constraint1 ");
			params.put(MsLov.CONSTRAINT_1_HBM, StringUtils.upperCase(request.getConstraint1()));
		}
		if (null != request.getConstraint2() && request.getConstraint2().length() > 0) {
			query.append("and constraint_2 = :constraint2 ");
			params.put(MsLov.CONSTRAINT_2_HBM, StringUtils.upperCase(request.getConstraint2()));
		}
		if (null != request.getConstraint3() && request.getConstraint3().length() > 0) {
			query.append("and constraint_3 = :constraint3 ");
			params.put(MsLov.CONSTRAINT_3_HBM, StringUtils.upperCase(request.getConstraint3()));
		}
		if (null != request.getConstraint4() && request.getConstraint4().length() > 0) {
			query.append("and constraint_4 = :constraint4 ");
			params.put(MsLov.CONSTRAINT_4_HBM, StringUtils.upperCase(request.getConstraint4()));
		}
		if (null != request.getConstraint5() && request.getConstraint5().length() > 0) {
			query.append("and constraint_5 = :constraint5 ");
			params.put(MsLov.CONSTRAINT_5_HBM, StringUtils.upperCase(request.getConstraint5()));
		}
		
		query.append("order by description asc ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}


	@Override
	public List<Map<String, Object>> getMsLovListByLovGroup(String group) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		
		query
			.append("select code, description ")
			.append("from ms_lov ")
			.append("where lov_group = :lovGroup and is_active = '1' and is_deleted = '0' ");
		
		params.put(MsLov.LOV_GROUP_HBM, StringUtils.upperCase(group));
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsLov getMsLovByGroupAndCodeNewTrx(String lovGroup, String lovCode) {
		if (StringUtils.isBlank(lovGroup) || StringUtils.isBlank(lovCode))
			return null;
		
		Object[][] queryParams = {
				{Restrictions.eq(MsLov.LOV_GROUP_HBM, StringUtils.upperCase(lovGroup))},
				{Restrictions.eq(MsLov.CODE_HBM, StringUtils.upperCase(lovCode))},
				{Restrictions.eq(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1")}};
		return this.managerDAO.selectOne(MsLov.class, queryParams);
	}


	@Override
	public List<String> getListofCodeByLovGroup(String group) {
		Object[][] queryParams = {{MsLov.LOV_GROUP_HBM, StringUtils.upperCase(group)}};
		Map<String, Object> mapResultList = this.managerDAO.list(
				"from MsLov l "
				+ "where l.lovGroup = :lovGroup "
			, queryParams);
		
		List<MsLov> lovs = (List<MsLov>) mapResultList.get(AmGlobalKey.MAP_RESULT_LIST);
		List<String> codes = new ArrayList<>();
		for (MsLov lov : lovs) {
			codes.add(lov.getCode());
		}
		
		return codes;
	}


	@Override
	public MsLov getMsLovByIdLov(long idMsLov) {
		Object[][] queryParams = { 
				{Restrictions.eq(MsLov.ID_LOV_HBM, idMsLov)}};
		return this.managerDAO.selectOne(MsLov.class, queryParams);
	}
	
	@Override
	public List<LovBean> getCodeBalanceVendorOfTenantByIdMsTenant(long idMsTenant) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.ID_TENANT_HBM, idMsTenant);

		StringBuilder query = new StringBuilder();
		
		query
		.append("select mv.code ")
		.append("from ms_balancevendoroftenant bvt ")
		.append("join ms_lov mv on mv.id_lov = bvt.lov_balance_type ")
		.append("where bvt.id_ms_tenant = :idMsTenant ");
		
		return this.managerDAO.selectForListString(LovBean.class, query.toString(), params, null);
	}
	
}
