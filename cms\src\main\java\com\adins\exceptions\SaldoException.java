package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class Sal<PERSON>Exception extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonSaldo {
		BALANCE_TYPE_CODE_EMPTY,
		BALANCE_TYPE_NAME_EMPTY,
		BALANCE_TYPE_ALREADY_EXIST,
		BALANCE_TYPE_NOT_FOUND,
		BALANCE_NOT_ENOUGH,
		DECRYPT_FAILED,
		BALANCE_NOT_CONFIGURED,
		UNKNOWN
	}
	
	private final ReasonSaldo reason;
	
	public SaldoException(ReasonSaldo reason) {
		this.reason = reason;
	}
	
	public SaldoException(String message, ReasonSaldo reason) {
		super(message);
		this.reason = reason;
	}
	
	public SaldoException(Throwable ex, ReasonSaldo reason) {
		super(ex);
		this.reason = reason;
	}
	
	public SaldoException(String message, Throwable ex, ReasonSaldo reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	@Override
	public int getErrorCode() {
		if (null != this.reason) {
			switch (reason) {
			case BALANCE_TYPE_CODE_EMPTY:
				return StatusCode.SALDO_BALANCE_TYPE_CODE_EMPTY;
			case BALANCE_TYPE_NAME_EMPTY:
				return StatusCode.SALDO_BALANCE_TYPE_NAME_EMPTY;
			case BALANCE_TYPE_ALREADY_EXIST:
				return StatusCode.SALDO_BALANCE_TYPE_ALREADY_EXIST;
			case BALANCE_TYPE_NOT_FOUND:
				return StatusCode.SALDO_BALANCE_TYPE_NOT_FOUND;
			case DECRYPT_FAILED:
				return StatusCode.DECRYPT_FAILED;
			case BALANCE_NOT_ENOUGH:
				return StatusCode.BALANCE_NOT_ENOUGH;
			case BALANCE_NOT_CONFIGURED:
				return StatusCode.BALANCE_NOT_CONFIGURED;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}

}
