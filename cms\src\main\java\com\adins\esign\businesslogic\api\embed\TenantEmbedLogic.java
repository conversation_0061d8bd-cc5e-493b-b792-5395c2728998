package com.adins.esign.businesslogic.api.embed;

import com.adins.esign.webservices.model.TenantSettingsEmbedRequest;
import com.adins.esign.webservices.model.TenantSettingsResponse;
import com.adins.esign.webservices.model.embed.CheckLivenessFaceCompareServiceEmbedRequest;
import com.adins.esign.webservices.model.embed.CheckLivenessFaceCompareServiceEmbedResponse;
import com.adins.esign.webservices.model.embed.GetAvailableSendingPointEmbedRequest;
import com.adins.esign.webservices.model.embed.GetAvailableSendingPointEmbedResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface TenantEmbedLogic {
	CheckLivenessFaceCompareServiceEmbedResponse checkLivenessFaceCompareServiceEmbed(CheckLivenessFaceCompareServiceEmbedRequest request, AuditContext audit);
	TenantSettingsResponse getTenantSettingsEmbed(TenantSettingsEmbedRequest request, AuditContext audit);
	GetAvailableSendingPointEmbedResponse getAvailableSendingPointEmbed(GetAvailableSendingPointEmbedRequest request,AuditContext audit);
}
