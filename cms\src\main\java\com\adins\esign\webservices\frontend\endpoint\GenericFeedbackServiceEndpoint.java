package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.FeedbackLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.webservices.frontend.api.FeedbackService;
import com.adins.esign.webservices.model.FeedbackEmbedRequest;
import com.adins.esign.webservices.model.FeedbackRequest;
import com.adins.esign.webservices.model.FeedbackResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import io.swagger.annotations.Api;

@Component
@Path("/feedback")
@Api(value = "FeedbackService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericFeedbackServiceEndpoint implements FeedbackService {
	
	@Autowired UserLogic userLogic;
	@Autowired FeedbackLogic feedbackLogic;

	@Override
	@POST
	@Path("/s/save")
	public FeedbackResponse saveFeedback(FeedbackRequest request) {
		
		AuditContext audit = request.getAudit().toAuditContext();
		feedbackLogic.insertFeedback(request, audit);
		
		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		
		FeedbackResponse response = new FeedbackResponse();
		response.setStatus(status);
		return response;
	}

	@Override
	@POST
	@Path("/saveEmbed")
	public FeedbackResponse saveFeedbackEmbed(FeedbackEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		FeedbackResponse response = new FeedbackResponse();
		Status status = new Status();
		try {
			feedbackLogic.insertFeedbackEmbed(request, audit);
		} catch (IllegalArgumentException e) {
			status.setMessage(e.getMessage());
			status.setCode(200);
			response.setStatus(status);
			return response;
		}
		
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		
		return response;
	}

}
