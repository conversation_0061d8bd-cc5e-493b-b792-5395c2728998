package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.UserActivityLogDao;
import com.adins.esign.model.TrUserActivityLog;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class UserActivityLogDaoHbn extends BaseDaoHbn implements UserActivityLogDao {

	@Override
	public void insertUserActivityLog(TrUserActivityLog userActivityLog) {
		userActivityLog.setUsrCrt(MssTool.maskData(userActivityLog.getUsrCrt()));
		this.managerDAO.insert(userActivityLog);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertUserActivityLogNewTrx(TrUserActivityLog userActivityLog) {
		userActivityLog.setUsrCrt(MssTool.maskData(userActivityLog.getUsrCrt()));
		this.managerDAO.insert(userActivityLog);
	}

	@Override
	public TrUserActivityLog getLastLoginActivityByLoginId(String loginId) {
		if (StringUtils.isBlank(loginId)) {
			return null;
		}

		StringBuilder query = new StringBuilder();
		query
			.append("select ual.id_user_activity_log ")
			.append("from tr_user_activity_log ual ")
			.append("join am_msuser u on ual.id_ms_user = u.id_ms_user ")
			.append("join ms_lov lov on ual.lov_user_activity_log = lov.id_lov ")
			.append("where u.login_id = :loginId ")
			.append("and lov.lov_group = :lovGroup ")
			.append("and lov.code = :lovCode ")
			.append("order by ual.dtm_crt desc ")
			.append("limit 1");

		BigInteger idUserActivityLog = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(),
				new Object[][] {
					{"loginId", StringUtils.upperCase(loginId)},
					{"lovGroup", GlobalVal.LOV_GROUP_ACTIVITY_LOG_TYPE},
					{"lovCode", GlobalVal.CODE_LOV_ACTIVITY_LOG_TYPE_LOGIN}
				});

		if (null == idUserActivityLog) {
			return null;
		}

		return this.managerDAO.selectOne(
				"from TrUserActivityLog ual "
				+ "join fetch ual.idMsuser u "
				+ "join fetch ual.idMsTenant t "
				+ "join fetch ual.idMsRole r "
				+ "join fetch ual.lovUserActivityLog lov "
				+ "where ual.idUserActivityLog = :idUserActivityLog",
				new Object[][] {
					{"idUserActivityLog", idUserActivityLog.longValue()}
				});
	}
}
