package com.adins.esign.dataaccess.impl;

import static org.junit.Assert.assertNotNull;
import java.text.ParseException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.SchedulerJobDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.TrSchedulerJob;
import com.adins.framework.persistence.dao.api.ManagerDAO;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.AuditDataType;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class SchedulerJobDaoHbnTest {
	@Autowired private SchedulerJobDao schedJob;
	@Autowired private CommonLogic commonLogic;
	@Autowired
	protected ManagerDAO managerDAO;
	
	private AuditDataType auditData;
	private AuditContext auditContext;

	private MsLov balanceType;
	private MsLov jobType;
	
	@BeforeEach
	public void setUp() {
		auditData = new AuditDataType();
		auditData.setCallerId("INITIAL");
		
		auditContext = new AuditContext(auditData.getCallerId());
		
		balanceType = this.commonLogic.getLovByGroupAndCode(
				GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT,auditContext);
		
		jobType = this.commonLogic.getLovByGroupAndCode(
				GlobalVal.LOV_GROUP_JOB_TYPE, GlobalVal.CODE_LOV_JOB_TYPE_BALREM,auditContext);
		
	}
	
	@Order(1)
	@Test
	void getSchedulerJobTest() {
		TrSchedulerJob schedjob = schedJob.getSchedulerJob("2021-12-08", jobType.getCode(), balanceType.getCode(), null);
		assertNotNull(schedjob);
	}
	
	
	@Order(1)
	@Test
	void insertSchedulerJobTest() {
		String sql = "insert into tr_scheduler_job (scheduler_Start, usr_crt, dtm_crt) "
				+ "values (to_Date('2021-11-10 23:59:59.998', '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_MIL_SEC+"'),"
				+ " 'junit',current_timestamp)";
		this.managerDAO.insertNativeString(sql, null);
		
	}
}
