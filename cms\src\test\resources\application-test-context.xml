<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd        
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">	
	
	<aop:aspectj-autoproxy proxy-target-class="false" />
		
	<bean id="hikariConfig" class="com.zaxxer.hikari.HikariConfig">
	    <property name="driverClassName" value="org.postgresql.Driver" />	 
		<property name="jdbcUrl" value="*****************************************************************" />
		<property name="username" value="sa" />
		<property name="password" value="AdIns2021" />

	    <property name="minimumIdle" value="5" />
	    <property name="maximumPoolSize" value="200" />
	    <property name="connectionTestQuery" value="SELECT 1" />
	    <property name="transactionIsolation" value="TRANSACTION_READ_UNCOMMITTED" />
	</bean>
	
	<bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
	    <constructor-arg ref="hikariConfig" />
	</bean>

	<bean id="hibernateSessionFactoryBean" class="org.springframework.orm.hibernate5.LocalSessionFactoryBean">
		<property name="dataSource" ref="dataSource"/>
        <property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">org.hibernate.dialect.PostgreSQLDialect</prop>
				<prop key="show_sql">true</prop>
	            <prop key="hibernate.format_sql">false</prop>
                <prop key="hibernate.jdbc.use_streams_for_binary">true</prop>
            </props>
        </property>
		<property name="packagesToScan">
			<list>
				<value>com.adins.am.model</value>
				<value>com.adins.esign.model</value>
			</list>
		</property>
    </bean>
        	
	<bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
		<property name="basenames">
		  <list>
			<value>classpath:com/adins/esign/messages</value>
		  </list>
		</property>
    	<property name="cacheSeconds" value="-1"/>
	</bean>
	
	<bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
    	<property name="locations" value="classpath:application.properties"/>
	</bean>	
	
	<bean id="mailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
		<property name="protocol" value="smtp" />
		<property name="host" value="${spring.mail.host}" />
		<property name="port" value="${spring.mail.port}" />
		<property name="username" value="${spring.mail.username}" />
		<property name="password" value="${spring.mail.password}" />
		<property name="javaMailProperties">
			<props>
				<prop key="mail.smtp.auth">true</prop>
				<prop key="mail.smtp.starttls.enable">true</prop>
				<prop key="mail.smtp.connectiontimeout">5000</prop>
				<prop key="mail.smtp.timeout">5000</prop>
				<prop key="mail.smtp.writetimeout">5000</prop>
			</props>
		</property>
	</bean>
    
	<bean id="transactionManager" class="org.springframework.orm.hibernate5.HibernateTransactionManager">
	    <property name="sessionFactory" ref="hibernateSessionFactoryBean" />
	</bean>
	
    <bean id="globalManagerDAO" class="com.adins.framework.persistence.dao.hibernate.manager.global.GlobalManagerDao">
		<property name="sessionFactory" ref="hibernateSessionFactoryBean"/>
    </bean>
    
    <bean id="auditTrail" class="com.adins.framework.persistence.dao.hibernate.interceptor.audit.GenericAuditLog" scope="singleton">
    	<property name="sessionFactory" ref="hibernateSessionFactoryBean"/>
    	<property name="auditManager" ref="auditlogLogic"/>
    </bean>
    
    <bean id="defaultLdapBean" class="com.adins.framework.tool.ldap.UnboundidLdapLogicImpl">
    	<property name="host" value="judges" />
    	<property name="port" value="389" />
    </bean>
    
    <bean id="gson" class="com.google.gson.Gson" />
	
	<bean id="auditContextAspect" class="com.adins.framework.service.base.aspect.AuditContextAspect" scope="prototype" />
		
	<bean id="alicloud.ossClientBuilder" class="com.aliyun.oss.OSSClientBuilder" />
	
	<bean id="alicloud.ossClient" class="com.aliyun.oss.OSSClient" factory-bean="alicloud.ossClientBuilder" factory-method="build">
		<constructor-arg name="endpoint" value="${spring.cloud.alicloud.oss.endpoint}" />
		<constructor-arg name="accessKeyId" value="${spring.cloud.alicloud.access-key}"/>
		<constructor-arg name="secretAccessKey" value="${spring.cloud.alicloud.secret-key}" />
	</bean>
	
	<aop:config proxy-target-class="false">
	  <aop:aspect id="aspect.auditContext" ref="auditContextAspect">
	    <aop:pointcut id="blPointCut" expression="execution(* com.adins.esign.businesslogic.api..*(..))
	    		or execution(* com.adins.am.businesslogic.api..*(..))
	    		or execution(* com.adins.esign.dataaccess.api..*(..))
	    		or execution(* com.adins.esign.dataaccess.factory.api..*(..))" />
	    <aop:before method="putAuditContext" pointcut-ref="blPointCut" />
	    <aop:after method="removeAuditContext" pointcut-ref="blPointCut" />
	  </aop:aspect>
	</aop:config>
	
	<context:component-scan base-package="com.adins.am" />
	<context:component-scan base-package="com.adins.esign.job" />
	<context:component-scan base-package="com.adins.esign.businesslogic" />
	<context:component-scan base-package="com.adins.esign.dataaccess" />
	<context:component-scan base-package="com.adins.esign.dataaccess.factory" />
	
	
</beans>