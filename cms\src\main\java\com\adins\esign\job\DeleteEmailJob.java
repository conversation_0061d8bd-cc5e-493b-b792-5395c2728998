package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class DeleteEmailJob {
	private static final Logger LOG = LoggerFactory.getLogger(DeleteEmailJob.class);
	private static final String SCHEDULER = "SCHEDULER";
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void runDeleteEmail() {
		try {
			LOG.info("Job Delete Email Started");
			AuditContext auditContext = new AuditContext(SCHEDULER);
			schedulerLogic.deleteEmail(auditContext);
			LOG.info("Job Delete Email Finished");
		} catch (Exception e) {
			LOG.error("Error on running Delete Email job", e);
		}
	}
}
