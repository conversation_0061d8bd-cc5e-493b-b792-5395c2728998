package com.adins.esign.businesslogic.impl.embed;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.embed.RegionEmbedLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.RegionBean;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.webservices.model.RegionListResponse;
import com.adins.esign.webservices.model.embed.RegionListRequest;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericRegionEmbedLogic extends BaseLogic implements RegionEmbedLogic {
	
	@Autowired EmbedValidatorLogic embedValidatorLogic;
	@Autowired CommonLogic commonLogic;

	@Override
	public RegionListResponse getRegionListEmbed(RegionListRequest request, AuditContext audit) {
		EmbedMsgBeanV2 msgBean =  embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), false, audit);
		audit.setCallerId(msgBean.getDecryptedEmail());
		
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(getMessage("businesslogic.paymentsigntype.emptytenantcode", null, audit), ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {"Tenant code", request.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		RegionListResponse response = new RegionListResponse();
		List<RegionBean> beans = new ArrayList<>();
		
		List<MsRegion> regions = daoFactory.getRegionDao().getRegionListByTenant(tenant.getTenantCode());
		for (MsRegion region : regions) {
			RegionBean bean = new RegionBean();
			bean.setRegionCode(commonLogic.encryptMessageToString(region.getRegionCode(), tenant.getAesEncryptKey(), audit));
			bean.setRegionName(region.getRegionName());
			beans.add(bean);
		}
		
		response.setRegionList(beans);
		return response;
	}

}
