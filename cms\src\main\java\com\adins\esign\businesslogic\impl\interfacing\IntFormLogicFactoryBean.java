 package com.adins.esign.businesslogic.impl.interfacing;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.adins.am.model.AmGeneralsetting;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.interfacing.IntFormLogic;
import com.adins.esign.constants.enums.InterfaceType;
import com.adins.esign.dataaccess.factory.api.DaoFactory;
import com.adins.framework.persistence.dao.model.AuditContext;


@Component
public class IntFormLogicFactoryBean implements FactoryBean<IntFormLogic>, ApplicationContextAware {
    private ApplicationContext applicationContext;
    private IntNcFormLogic intNcLogic;
    private IntNoneFormLogic intNoneLogic;
    @Autowired private DaoFactory daoFactory; 
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    
    @Override
    public IntFormLogic getObject() throws Exception {
        AuditContext callerId = new AuditContext();
        callerId.setCallerId("SYSTEM");
        AmGeneralsetting amGsHost = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_INTERFACE_TYPE) ;
        String interfaceType = amGsHost.getGsValue();
        
        if (InterfaceType.CONFINS.toString().equals(interfaceType)) {
	    	if (intNcLogic == null) {
	    		intNcLogic = this.applicationContext.getBean(IntNcFormLogic.class);
	    	}
	    	
	    	return intNcLogic;
	    }
	    else {
	    	if (intNoneLogic == null) {
	    		intNoneLogic = this.applicationContext.getBean(IntNoneFormLogic.class);
	    	}
	    	
	    	return intNoneLogic;
	    }
    }

    @Override
    public Class<IntFormLogic> getObjectType() {
        return IntFormLogic.class;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }

}
