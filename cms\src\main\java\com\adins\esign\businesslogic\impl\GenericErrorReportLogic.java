package com.adins.esign.businesslogic.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.ErrorReportLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.webservices.model.ErrorHistoryActivationStatusRequest;
import com.adins.esign.webservices.model.ErrorHistoryActivationStatusResponse;
import com.adins.esign.webservices.model.ErrorHistoryRequest;
import com.adins.esign.webservices.model.ErrorHistoryResponse;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.ErrorHistoryException.ReasonErrorHistory;
import com.adins.exceptions.ErrorHistoryException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.esign.model.TrErrorHistory;
import com.adins.esign.model.TrErrorHistoryUserDetail;
import com.adins.esign.model.custom.ErrorHistoryActivationStatusBean;
import com.adins.esign.model.custom.ErrorHistoryBean;
import com.adins.esign.util.MssTool;

@Component
public class GenericErrorReportLogic extends BaseLogic implements ErrorReportLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericErrorReportLogic.class);
	
	@Autowired private UserLogic userLogic;
	
	public static final String ERR_HIST_RERUN_NOT_STARTED	= "0";
	public static final String ERR_HIST_RERUN_ERROR			= "1";
	public static final String ERR_HIST_RERUN_FINISHED		= "2";

	@Override
	public ErrorHistoryResponse getErrorHistory(ErrorHistoryRequest request, AuditContext audit) throws ParseException {

		ErrorHistoryResponse response = new ErrorHistoryResponse();
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		
		Date dateStart = null;
		Date dateEnd = null;
		
		//Validasi 30 hari
		if (!isDateRangeValid(request.getTanggalDari(), request.getTanggalSampai(), audit)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
					new Object[] {"listErrorHistory"}, this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_DATE_RANGE);
		}
		
		//Ubah tipe date jdi string
		if(StringUtils.isNotBlank(request.getTanggalDari())) {
			dateStart =  MssTool.formatStringToDate(request.getTanggalDari() + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC);
			
		}
		if(StringUtils.isNotBlank(request.getTanggalSampai())) {
			dateEnd = MssTool.formatStringToDate(request.getTanggalSampai() + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		//paging
		Integer totalData = daoFactory.getErrorHistoryDao().countListErrorHistory(request.getTenantCode(), 
				request.getModul(), request.getRefNumber(), request.getNamaKonsumen(), request.getCabang(), 
				request.getRegion(), request.getBusinessLine(), dateStart, dateEnd, request.getTipe());
		double totalPage = Math.ceil((double) totalData / maxRow);
        
        //panggil query list data
		List<ErrorHistoryBean> listErrorHistory = daoFactory.getErrorHistoryDao().getListErrorHistory(min, max, request.getTenantCode(), 
				request.getModul(), request.getRefNumber(), request.getNamaKonsumen(), request.getCabang(), 
				request.getRegion(), request.getBusinessLine(), dateStart, dateEnd, request.getTipe());
	
		response.setListErrorHistory(listErrorHistory);
		response.setPage(request.getPage());
		response.setTotalResult(totalData);
		response.setTotalPage((int)totalPage);
		
		return response;
	}
	
	
	private boolean isDateRangeValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);
		
		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return true;
		}
		if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
			try {
				sdf.parse(startDate);
			} catch (ParseException e) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
			try {
				sdf.parse(endDate);
			} catch (ParseException e) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		
		long dayCount = 0;
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			dayCount = (end.getTime() - start.getTime()) / (1000*60*60*24);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		LOG.info("Date range: {} hari", dayCount);
		return dayCount <= Long.valueOf(maxRangeDate);
	}

	@Override
	public void updateErrorHistoryRerunProcess(String errorHistoryNik, Long idVendor, AuditContext audit) {
		TrErrorHistory errorHistory = daoFactory.getErrorHistoryDao().getLatestErrorHistoryByNikAndIdMsVendor(errorHistoryNik, idVendor);
		if (null == errorHistory) {
			LOG.info("Send document error history for NIK {} is not found", errorHistoryNik);
			return;
		}
		
		boolean isCustomerActivated = true;
		boolean isSpouseActivated = true;
		boolean isGuarantorActivated = true;
		
		TrErrorHistoryUserDetail custDetail = daoFactory.getErrorHistoryDao().getErrorHistoryUserDetail(errorHistory, GlobalVal.CODE_LOV_SIGNER_TYPE_CUST);
		TrErrorHistoryUserDetail spouseDetail = daoFactory.getErrorHistoryDao().getErrorHistoryUserDetail(errorHistory, GlobalVal.CODE_LOV_SIGNER_TYPE_SPS);
		TrErrorHistoryUserDetail guarantorDetail = daoFactory.getErrorHistoryDao().getErrorHistoryUserDetail(errorHistory, GlobalVal.CODE_LOV_SIGNER_TYPE_GRT);
		
		String currVendorCode = errorHistory.getMsVendor().getVendorCode();
		
		if (null != custDetail && StringUtils.isNotBlank(custDetail.getUserIdno())) {
			isCustomerActivated = userLogic.isNikUserActivated(custDetail.getUserIdno(), currVendorCode, audit);
		}
		if (null != spouseDetail && StringUtils.isNotBlank(spouseDetail.getUserIdno())) {
			isSpouseActivated = userLogic.isNikUserActivated(spouseDetail.getUserIdno(), currVendorCode, audit);
		}
		if (null != guarantorDetail && StringUtils.isNotBlank(guarantorDetail.getUserIdno())) {
			isGuarantorActivated = userLogic.isNikUserActivated(guarantorDetail.getUserIdno(), currVendorCode, audit);
		}
		
		LOG.info("Ref number: {}, Customer activated: {}, Spouse activated: {}, Guarantor activated: {}, Vendor: {}",
				errorHistory.getRefNumber(), isCustomerActivated, isSpouseActivated, isGuarantorActivated, currVendorCode);
		
		if (isCustomerActivated && isSpouseActivated && isGuarantorActivated) {
			
			errorHistory.setRerunProcess(ERR_HIST_RERUN_ERROR);
			errorHistory.setDtmUpd(new Date());
			errorHistory.setUsrUpd(audit.getCallerId());
			daoFactory.getErrorHistoryDao().updateErrorHistory(errorHistory);
			
			LOG.info("Tenant: {}, Reference Number: {}, Vendor: {}, error history updated to ready call rerun API", errorHistory.getMsTenant().getTenantName(), errorHistory.getRefNumber(), currVendorCode);
		}
		
	}


	@Override
	public ErrorHistoryActivationStatusResponse getErrorHistActivationStatus(ErrorHistoryActivationStatusRequest request, AuditContext audit) {
		TrErrorHistory errorHistory = daoFactory.getErrorHistoryDao().getErrorHistoryByIdErrorHistory(request.getIdErrorHistory());
		if (null == errorHistory) {
			throw new ErrorHistoryException(messageSource.getMessage("businesslogic.errorhistory.notfound",
					null, retrieveLocaleAudit(audit)), ReasonErrorHistory.ERR_HIST_NOT_FOUND);
		}
		
		List<TrErrorHistoryUserDetail> errorHistoryDetail = daoFactory.getErrorHistoryDao().getErrorHistoryUserDetailByIdErrorHistory(request.getIdErrorHistory());
		String vendorCode = errorHistory.getMsVendor().getVendorCode();
		
		List<ErrorHistoryActivationStatusBean> listActStatus = new ArrayList<>();
		
		for (TrErrorHistoryUserDetail errorDetail : errorHistoryDetail) {
		    ErrorHistoryActivationStatusBean activationStatus = new ErrorHistoryActivationStatusBean();
		    // Set nilai properti activationStatus sesuai dengan nilai dari errorHistory
		    activationStatus.setIdNo(errorDetail.getUserIdno());
		    activationStatus.setName(errorDetail.getUserName());
		    activationStatus.setSignerType(errorDetail.getMsLov().getDescription());
		    activationStatus.setActivationStatus(userLogic.getNikUserActivationStatus(errorDetail.getUserIdno(), vendorCode, audit));
		    
		    // Tambahkan activationStatus ke dalam listActStatus
		    listActStatus.add(activationStatus);
		}
		
		ErrorHistoryActivationStatusResponse response = new ErrorHistoryActivationStatusResponse();
		response.setListActStatus(listActStatus);
		
		return response;
	}

}
