package com.adins.esign.dataaccess.impl;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.bind.DatatypeConverter;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.RandomStringGenerator;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.annotation.Commit;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserPersonalData;
//import com.adins.am.model.AmUserAutofillData;
import com.adins.am.model.AmUserpwdhistory;
import com.adins.esign.businesslogic.api.TestSetUpLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.UserDao;
import com.adins.esign.model.custom.PersonalDataBean;
//import com.adins.esign.model.MsBranch;
//import com.adins.esign.model.MsJob;
import com.adins.esign.model.custom.ZipcodeCityBean;
import com.adins.esign.util.MssTool;
import com.adins.framework.tool.password.PasswordHash;
import com.google.common.io.BaseEncoding;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class UserDaoHbnTest extends BaseLogic{
//	@Autowired private TestSetUpLogic testSetUpLogic;
	@Autowired private UserDao userDao;
//	
//	private MsJob job;
//	private MsBranch branch;
//	private AmMssubsystem subsystem;
	private AmMsuser user;
	private AmUserPersonalData userPersonalData;
	private PersonalDataBean personalDataBean;

	private long ID_USER = 409;
	private String b64Ktp = "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";
	private String b64Selfie = "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";
//	@BeforeEach
//	@Rollback(true)
//	public void setUp() {
//		job = daoFactory.getJobDao().getJobByCode(GlobalVal.JOB_ADMIN);
//		branch = daoFactory.getBranchDao().getActiveBranchByCode(GlobalVal.BRANCH_CODE_HO);
//		subsystem = daoFactory.getSubsystemDao().getSubsystemByName(GlobalVal.SUBSYSTEM_ESIGN);
//		this.user = testSetUpLogic.setUpUser("<EMAIL>", "JUNIT", job, branch, null, subsystem);
//	}
//	
//	@Order(1)
//	@Test
//	@Rollback(true)
//	void insertUserTest() {
//		AmMsuser newUser = new AmMsuser();
//    	newUser.setIsActive("1");
//    	newUser.setIsDeleted("0");
//    	newUser.setUsrCrt("JUNIT");
//    	newUser.setDtmCrt(new Date());
//    	newUser.setLoginId("<EMAIL>");
//    	newUser.setFullName("JUNIT");
//    	newUser.setInitialName("UT");
//    	newUser.setEmail("<EMAIL>");
//    	newUser.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);				
//    	newUser.setPassword(PasswordHash.createHash(daoFactory.getGeneralSettingDao()
//				.getGsValueByCode(GlobalKey.GENERALSETTING_DEFAULT_PASSWORD)));
//    	newUser.setFailCount(0);
//    	newUser.setIsLoggedIn("0");
//    	newUser.setIsLocked("0");
//    	newUser.setIsPasswordExpired("0");
//    	newUser.setIsDormant("0");
//    	newUser.setMsJob(job);
//    	newUser.setMsBranch(branch);
//    	newUser.setAmMsuser(null);
//    	newUser.setAmMssubsystem(subsystem);
//    	newUser.setPhone("081234567891");
//    	newUser.setFcmToken("0");
//    	
//    	userDao.insertUser(newUser);
//    	AmMsuser user = userDao.getUserByLoginId(newUser.getLoginId());
//    	assertEquals(user.getLoginId(), newUser.getLoginId());
//	}
//	
//	@Order(2)
//	@Test
//	@Rollback(true)
//	void updateUserTest() {
//		this.user.setLoginId("<EMAIL>");
//		userDao.updateUser(this.user);
//		
//		AmMsuser user = userDao.getUserByLoginId(this.user.getLoginId());
//		assertEquals(user.getLoginId(), this.user.getLoginId());
//	}
//	
//	@Order(3)
//	@Test
//	void getUserByLoginIdTest() {
//		AmMsuser user = userDao.getUserByLoginId(this.user.getLoginId());
//		assertEquals(user.getLoginId(), this.user.getLoginId());
//	}
//	
//	@Order(4)
//	@Test
//	void getUserByLoginIdNullTest() {
//		AmMsuser user = userDao.getUserByLoginId("INVALIDLOGINID");
//		assertNull(user);
//	}
//	
//	@Order(5)
//	@Test
//	void getUserByLoginIdNullTestWithEmptyLoginId() {
//		AmMsuser user = userDao.getUserByLoginId("");
//		assertNull(user);
//	}
//	
//	@Order(6)
//	@Test
//	void getUserByUuidTest() {
//		AmMsuser user = userDao.getUserByUuid(this.user.getUuidMsUser());
//		assertEquals(user.getLoginId(), this.user.getLoginId());
//	}
//	
//	@Order(7)
//	@Test
//	void getUserByUuidNullTest() {
//		AmMsuser user = userDao.getUserByUuid(this.user.getUuidMsUser() + 1);
//		assertNull(user);
//	}
//	
//	@Order(8)
//	@Test
//	@Rollback(true)
//	void getDateDiffPassHistByUuidTest() {
//		testSetUpLogic.setUpUserPwdhistory(new Date(), this.user);
//		int diff = userDao.getDateDiffPassHistByUuid(this.user.getUuidMsUser());
//		assertEquals(diff, 0);
//	}
//	
//	@Order(9)
//	@Test
//	void getListDataUserByUuidTest() {
//		List<Map<String, Object>> listDataUser = userDao.getListDataUserByUuid(String.valueOf(this.user.getUuidMsUser()));
//		
//		Map<String, Object> loginId = new HashMap<String, Object>();
//		loginId.put("d0", "login_id");
//		loginId.put("d1", this.user.getLoginId());
//		int index = listDataUser.indexOf(loginId);
//		assertEquals(listDataUser.get(index).get("d1"), this.user.getLoginId());
//	}
//	
//	@Order(10)
//	@Test
//	void getListDataUserByUuidNullTest() {
//		List<Map<String, Object>> listDataUser = userDao.getListDataUserByUuid(String.valueOf(this.user.getUuidMsUser() + 1));
//		assertTrue(listDataUser.isEmpty());
//	}
//	
//	@Order(11)
//	@Test
//	@Rollback(true)
//	void getUserDataByUuidUserTest() {
//		userAutofillData = testSetUpLogic.setUpUserAutofillData(user);
//		AmUserAutofillData userAutofillData = userDao.getUserDataByUuidUser(this.user.getUuidMsUser());
//		assertEquals(userAutofillData.getIdNo(), this.userAutofillData.getIdNo());
//	}
//	
//	@Order(12)
//	@Test
//	void getUserDataByUuidUserNullTest() {
//		AmUserAutofillData userAutofillData = userDao.getUserDataByUuidUser(this.user.getUuidMsUser());
//		assertNull(userAutofillData);
//	}
//	
//	@Order(13)
//	@Test
//	@Rollback(true)
//	void insertUserAutofillDataTest() {
//		ZipcodeCityBean zipcode = new ZipcodeCityBean();
//		zipcode.setProvinsi("PROVINSI JUNIT");
//		zipcode.setKelurahan("KEL JUNIT");
//		zipcode.setKecamatan("KEC JUNIT");
//		zipcode.setKota("KOTA JUNIT");
//		zipcode.setZipcode("12345");
//		
//		AmUserAutofillData newUserAutofillData = new AmUserAutofillData();
//		newUserAutofillData.setUsrCrt("JUNIT");
//		newUserAutofillData.setDtmCrt(new Date());
//		newUserAutofillData.setAmMsuser(this.user);
//		newUserAutofillData.setAddress("Jl. JUNIT");
//		newUserAutofillData.setGender("M");
//		newUserAutofillData.setZipcodeBean(zipcode);
//		newUserAutofillData.setPlaceOfBirth("POB JUNIT");
//		newUserAutofillData.setDateOfBirth(new Date());
//		newUserAutofillData.setIdNo("1234567890123456");
//		
//		userDao.insertUserAutofillData(newUserAutofillData);
//		AmUserAutofillData userAutofillData = userDao.getUserDataByUuidUser(this.user.getUuidMsUser());
//		assertEquals(userAutofillData.getIdNo(), newUserAutofillData.getIdNo());
//	}
//	
//	@Order(14)
//	@Test
//	@Rollback(true)
//	void getUserPwdhistoryTest() {
//		testSetUpLogic.setUpUserPwdhistory(new Date(), this.user);
//		AmUserpwdhistory userPwdhistory = userDao.getUserPwdhistory(this.user.getUuidMsUser());
//		assertEquals(userPwdhistory.getPassword(), this.user.getPassword());
//	}
//	
//	@Order(15)
//	@Test
//	void getUserPwdhistoryNullTest() {
//		AmUserpwdhistory userPwdhistory = userDao.getUserPwdhistory(this.user.getUuidMsUser());
//		assertNull(userPwdhistory);
//	}
//	
//	@Order(16)
//	@Test
//	@Rollback(true)
//	void insertUserPwdhistoryTest() {
//		AmUserpwdhistory newUserPwdhistory = new AmUserpwdhistory();
//		newUserPwdhistory.setUsrCrt("JUNIT");
//		newUserPwdhistory.setDtmCrt(new Date());
//		newUserPwdhistory.setAmMsuser(this.user);
//		newUserPwdhistory.setPassword(this.user.getPassword());
//		newUserPwdhistory.setChangeType("JUNIT");
//		
//		userDao.insertUserPwdhistory(newUserPwdhistory);
//		AmUserpwdhistory userPwdhistory = userDao.getUserPwdhistory(this.user.getUuidMsUser());
//		assertEquals(userPwdhistory.getPassword(), newUserPwdhistory.getPassword());
//	}
//	

	
	@Test
	@Rollback(true)
	void getUserDataByIdMsUserTest() {
		personalDataBean = userDao.getUserDataByIdMsUser(ID_USER, false);
		Assert.assertNotNull(personalDataBean);
	}
	
	@Test
	@Rollback(true)
	void updateUserPersonalDataTest() {
		personalDataBean = userDao.getUserDataByIdMsUser(ID_USER, false);
		personalDataBean.setIdNoRaw("");
		personalDataBean.setPhoneRaw("089987654321 UPD");
		personalDataBean.setAddressRaw("JALAN KENANGAN 20 UPD");
		userDao.updateUserPersonalData(personalDataBean);
		
		personalDataBean = userDao.getUserDataByIdMsUser(409, false);
		assertEquals(null, personalDataBean.getIdNoRaw());
		assertEquals("089987654321 UPD", personalDataBean.getPhoneRaw());
		assertEquals("JALAN KENANGAN 20 UPD", personalDataBean.getAddressRaw());
	}
	
	@Test
	void getUserDataByPhoneTest() {
		personalDataBean = userDao.getUserDataByIdMsUser(ID_USER, false);
		AmMsuser user = userDao.getUserByIdMsUser(ID_USER);
//		user.setHashedPhone(MssTool.getHashedString(personalDataBean.getPhoneRaw()));
//		userDao.updateUser(user);
		
		AmUserPersonalData personalPhone = userDao.getUserDataByPhone(personalDataBean.getPhoneRaw());
		assertEquals(personalPhone.getAmMsuser().getIdMsUser(), user.getIdMsUser());
	}
	
	@Test
	void getUserByPhoneTest() {
		AmMsuser user = userDao.getUserByIdMsUser(ID_USER);
		personalDataBean = userDao.getUserDataByIdMsUser(ID_USER, false);

		AmMsuser userPhone = userDao.getActiveUserByPhone(personalDataBean.getPhoneRaw());
		assertEquals(userPhone.getIdMsUser(), user.getIdMsUser());
	}
	
	@Rollback(false)
	@Test
	void insertUserPersonalDataTest() {
		Date sysdate = new Date();
		
		AmMsuser user = new AmMsuser();
		user.setFullName(RandomStringUtils.randomAlphanumeric(8));
		user.setPassword(PasswordHash.createHash("password"));
		user.setDtmCrt(sysdate);
		user.setUsrCrt("SUMA");
		user.setIsActive("1");
		user.setIsDeleted("0");
		userDao.insertUser(user);
		
		AmUserPersonalData personalData = new AmUserPersonalData();
		personalData.setAmMsuser(user);
		personalData.setDtmCrt(sysdate);
		personalData.setUsrCrt("SUMA");
		personalData.setEmail("<EMAIL>");
		personalData.setGender("M");
		personalData.setPlaceOfBirth("PONTIANAK");
		
		ZipcodeCityBean bean = new ZipcodeCityBean();
		bean.setKecamatan("CIPONDOH");
		bean.setKelurahan("KETAPANG");
		bean.setKota("TANGERANG");
		bean.setProvinsi("BANTEN");
		bean.setZipcode("15147");
		personalData.setZipcodeBean(bean);
		
		PersonalDataBean pdb = new PersonalDataBean();
		pdb.setUserPersonalData(personalData);
		pdb.setIdNoRaw("1234567890888888");
		pdb.setPhoneRaw("08152207011");
		pdb.setAddressRaw("AU3/89");
		
		userDao.insertUserPersonalData(pdb);
	}
	
	@Test
	void getUserDataByIdMsUserTest2() {
		String loginId = "<EMAIL>";
		String phoneRaw = "088210216096";
		AmMsuser user = userDao.getUserByLoginId(loginId);
		
		PersonalDataBean pdb = this.userDao.getUserDataByIdMsUser(user.getIdMsUser(), false);
//		assertEquals("1234567890888888", pdb.getIdNoRaw());
		assertEquals(phoneRaw, pdb.getPhoneRaw());
//		assertEquals("AU3/89", pdb.getAddressRaw());
//		String b64KtpCompare = (pdb.getPhotoIdRaw() == null) ? null : BaseEncoding.base64().encode(pdb.getPhotoIdRaw());
//		assertEquals(b64Ktp, b64KtpCompare);
//		String b64SelfieCompare = (pdb.getUserPersonalData().getPhotoSelf() == null) ? null : BaseEncoding.base64().encode(pdb.getUserPersonalData().getPhotoSelf());
//		assertEquals(b64Selfie, b64SelfieCompare);
	}
	
	@Test
	void compareHashedDataUserTest() {
		String loginId = "<EMAIL>";
		String phoneRaw = "00"; // 0 : hashed : 5feceb66ffc86f38d952786c6d696c79c2dbc239dd4e91b46729d73a27fb57e9
		
		String idNoRaw = "";

		AmMsuser user = userDao.getUserByLoginId(loginId);
		
		if (StringUtils.isNotBlank(idNoRaw)) {
			
			String idKtp = MssTool.getHashedString(idNoRaw);
//			assertEquals(user.getHashedIdNo(), idKtp);  //compare with hashed version
		}
	
		if (StringUtils.isNotBlank(phoneRaw)) {
			String phone =MssTool.getHashedString(phoneRaw);
			assertEquals(user.getHashedPhone(), phone);  //compare with hashed version
		}
	}
	
	@Rollback(false)
	@Test
	void updateUserPersonalDataTest2() {
		final long idMsUser = 98;
		PersonalDataBean pdb = this.userDao.getUserDataByIdMsUser(idMsUser, false);
		String base64PhotoSelfie = "";
		String base64PhotoKtp = "";

		try(BufferedReader br = new BufferedReader(new FileReader("C:\\Users\\<USER>\\Desktop\\SELFIE.txt"))) {
		    StringBuilder sb = new StringBuilder();
		    String line = br.readLine();

		    while (line != null) {
		        sb.append(line);
		        sb.append(System.lineSeparator());
		        line = br.readLine();
		    }
		    base64PhotoSelfie = sb.toString();
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		
		try(BufferedReader br = new BufferedReader(new FileReader("C:\\Users\\<USER>\\Desktop\\KTP.txt"))) {
		    StringBuilder sb = new StringBuilder();
		    String line = br.readLine();

		    while (line != null) {
		        sb.append(line);
		        sb.append(System.lineSeparator());
		        line = br.readLine();
		    }
		    base64PhotoKtp = sb.toString();
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		
		pdb.getUserPersonalData().setPhotoSelf(DatatypeConverter.parseBase64Binary(base64PhotoSelfie));
		pdb.setPhotoIdRaw(DatatypeConverter.parseBase64Binary(base64PhotoKtp));
		pdb.getUserPersonalData().setDtmUpd(new Date());
		this.userDao.updateUserPersonalData(pdb);
	}
	
	
	
	@Disabled("HANDLE WITH CARE, FOR EDITING EXISTING DATA")
	@Commit
	@Test
	void updateUserPersonalData() {
		String loginId = "<EMAIL>";
		String phoneRaw = "088210216096";
		AmMsuser user = userDao.getUserByLoginId(loginId);
		PersonalDataBean pdb = this.userDao.getUserDataByIdMsUser(user.getIdMsUser(), false);
		
		pdb.setPhoneRaw(phoneRaw);
		pdb.getUserPersonalData().setDtmUpd(new Date()); // for audit data manual update
		pdb.getUserPersonalData().setUsrUpd("MANUAL"); // for audit data manual update

		this.userDao.updateUserPersonalData(pdb);
	}
	
	@Disabled("HANDLE WITH CARE, FOR EDITING EXISTING DATA")
	@Commit
	@Test
	void updateUserDataPhoneKtp() {
		
		String loginId = "<EMAIL>";
		String phoneRaw = "000000"; 
		// 0 : hashed : 5feceb66ffc86f38d952786c6d696c79c2dbc239dd4e91b46729d73a27fb57e9
		// 00 : hashed :f1534392279bddbf9d43dde8701cb5be14b82f76ec6607bf8d6ad557f60f304e
		// 000 : hashed :2ac9a6746aca543af8dff39894cfe8173afba21eb01c6fae33d52947222855ef
		// 0000 : hashed :9af15b336e6a9619928537df30b2e6a2376569fcf9d7e773eccede65606529a0
		// 00000 : hashed :e7042ac7d09c7bc41c8cfa5749e41858f6980643bc0db1a83cc793d3e24d3f77
		// 000000 : hashed :91b4d142823f7d20c5f08df69122de43f35f057a988d9619f6d3138485c9a203

		String idNoRaw = "";
		AmMsuser user = userDao.getUserByLoginId(loginId);
	
		if (StringUtils.isNotBlank(idNoRaw)) {
	
			String idKtp = MssTool.getHashedString(idNoRaw);
			user.setHashedIdNo(idKtp); //save as hashed version
		}
	
		if (StringUtils.isNotBlank(phoneRaw)) {
			String phone =MssTool.getHashedString(phoneRaw);
			user.setHashedPhone(phone); //save as hashed version
		}
		user.setDtmUpd(new Date()); // for audit data manual update
		user.setUsrUpd("MANUAL"); // for audit data manual update
		userDao.updateUser(user);
	}
}
