package com.adins.esign.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserPersonalData;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.GetListPSrESettingBean;
import com.adins.esign.model.custom.ListPsrePriorityBean;
import com.adins.esign.model.custom.PsreVendorBean;
import com.adins.esign.model.custom.UpdatePSrERequest;
import com.adins.esign.model.custom.VendorBean;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.InvitationLinkValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.GetListPSrESettingRequest;
import com.adins.esign.webservices.model.GetListPSrESettingResponse;
import com.adins.esign.webservices.model.GetPsrePriorityRequest;
import com.adins.esign.webservices.model.GetPsrePriorityResponse;
import com.adins.esign.webservices.model.UpdatePsrePriorityRequest;
import com.adins.esign.webservices.model.UpdatePsrePriorityResponse;
import com.adins.esign.webservices.model.VendorListEmbedRequest;
import com.adins.esign.webservices.model.VendorListInvitationRegisterRequest;
import com.adins.esign.webservices.model.VendorListRequest;
import com.adins.esign.webservices.model.VendorListResponse;
import com.adins.esign.webservices.model.external.UpdatePsrePriorityExternalRequest;
import com.adins.esign.webservices.model.external.UpdatePsrePriorityExternalResponse;
import com.adins.exceptions.EntityNotFoundException;
import com.adins.exceptions.InvitationLinkException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.InvitationLinkException.ReasonInvitationLink;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Transactional
@Component
public class GenericVendorLogic extends BaseLogic implements VendorLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericRegistrationLogic.class);
	
	@Autowired private CommonLogic commonLogic;
	@Autowired private InvitationLinkValidatorLogic invLinkValidatorLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	
	@Override
	public MsVendor getVendorByCode(String vendorCode, AuditContext audit) {
		return daoFactory.getVendorDao().getVendorByCode(vendorCode);
	}

	@Override
	public MsVendorRegisteredUser getVendorRegisteredUserByLoginId(String loginId) {
		return daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginId(loginId);
	}
	
	@Override
	public void insertVendorRegisteredUser(MsVendorRegisteredUser user) {
		daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUser(user);
	}

	@Override
	public List<MsVendoroftenant> getListVendorTenant(AuditContext audit) {
		return daoFactory.getVendorDao().getListVendoroftenant();
	}
	
	@Override
	public MsVendoroftenant getVendorTenant(MsTenant tenant, MsVendor vendor, AuditContext audit) {
		return daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
	}
	
	@Override
	public MsVendoroftenant getVendorTenantByDocumentId(String documentId, AuditContext audit) {
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
		return getVendorTenant(docD.getMsTenant(), docD.getMsVendor(), audit);
	}
	
	@Override
	public MsVendoroftenant getVendorTenantByCode(String tenantCode, String vendorCode, AuditContext audit) {
		return daoFactory.getVendorDao().getVendorTenantByCode(tenantCode, vendorCode);
	}

	@Override
	public VendorListResponse getVendorList(VendorListRequest request, String vendorCodeExclude, AuditContext audit) {
		if (null == request.getTenantCode()) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_TENANT_CODE_EMPTY,
					null, this.retrieveLocaleAudit(audit)), ReasonVendor.TENANT_CODE_EMPTY);
		}
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_TENANT_CODE_INVALID,
					null, this.retrieveLocaleAudit(audit)), ReasonVendor.TENANT_NOT_FOUND);
		}
		
		VendorListResponse response = new VendorListResponse();
		List<VendorBean> vendorBeanList = new ArrayList<>();
		List<Map<String, Object>> vendorList = 	daoFactory.getVendorDao().getVendorList(
				request.getVendorTypeCode(), request.getTenantCode(), request.getVendorCode(), vendorCodeExclude);
		Iterator<Map<String, Object>> itr = vendorList.iterator();
		
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			VendorBean bean = new VendorBean();
			bean.setCode((String) map.get("d0"));
			bean.setName((String) map.get("d1"));
			vendorBeanList.add(bean);
		}
		response.setVendorList(vendorBeanList);
		return response;
	}
	
	@Override
	public VendorListResponse getVendorListEmbed(VendorListEmbedRequest request, String vendorCodeExclude, AuditContext audit) {
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		
		if (null == msgBean.getTenantCode()) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_TENANT_CODE_EMPTY,
					null, this.retrieveLocaleAudit(audit)), ReasonVendor.TENANT_CODE_EMPTY);
		}
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		if (null == tenant) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_TENANT_CODE_INVALID,
					null, this.retrieveLocaleAudit(audit)), ReasonVendor.TENANT_NOT_FOUND);
		}
		
		VendorListResponse response = new VendorListResponse();
		List<VendorBean> vendorBeanList = new ArrayList<>();
		List<Map<String, Object>> vendorList = 	daoFactory.getVendorDao().getVendorList(
				request.getVendorTypeCode(), msgBean.getTenantCode(), request.getVendorCode(), vendorCodeExclude);
		Iterator<Map<String, Object>> itr = vendorList.iterator();
		
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			VendorBean bean = new VendorBean();
			bean.setCode((String) map.get("d0"));
			bean.setName((String) map.get("d1"));
			vendorBeanList.add(bean);
			
			String code = (String) map.get("d0");
			String codeEncrypt = null;
			if(StringUtils.isNotBlank(code)){
				codeEncrypt = commonLogic.encryptMessageToString((String) map.get("d0"), audit);
			} else {
				codeEncrypt = "";
			}
			bean.setCodeEncrypt(codeEncrypt);
		}
		response.setVendorList(vendorBeanList);
		return response;
	}

	@Override
	public String getVendorEKYCStatus(String tenantCode, String vendorCode, AuditContext audit) {
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);
		if (vendor.getMsLovVendorType().getCode().equals(GlobalVal.CODE_LOV_VENDOR_TYPE_E_MATERAI)) {
			throw new IllegalArgumentException(this.messageSource.getMessage("businesslogic.vendor.ematerainotallowed", null, 
					this.retrieveLocaleAudit(audit)));
		}
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(tenantCode);
		MsVendoroftenant vendorOfTenant = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		
		return vendorOfTenant.getIsUseVendorEkyc();
	}

	@Override
	public VendorListResponse getVendorListInvReg(VendorListInvitationRegisterRequest request, AuditContext audit) {
		
		VendorListResponse response = new VendorListResponse();
		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByInvitationCode(invCode);
		if (null == invLink) {
			throw new InvitationLinkException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID,
					null, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INV_LINK_NOT_EXIST);
		}
		if (!"1".equals(invLink.getIsActive())) {
			String idNo = invLink.getIdNo();
			if (StringUtils.isNumeric(invLink.getReceiverDetail())) {
				AmMsuser user = daoFactory.getUserDao().getActiveUserByPhone(invLink.getReceiverDetail());
				if (null != user) {
					TrDocumentD pendingDocument = daoFactory.getDocumentDao().getEarliestUnsignedDocumentBySignerLoginId(user.getLoginId());
					if (null != pendingDocument) {
						Status status = new Status();
						status.setCode(StatusCode.INACTIVE_LINK);
						status.setMessage(this.messageSource.getMessage("businesslogic.user.userhaspendingdocument", 
								null, this.retrieveLocaleAudit(audit)));
						response.setDocumentId(pendingDocument.getDocumentId());
						response.setStatus(status);
						return response;
					}
					
					if (idNo == null) {
						AmUserPersonalData userPersonalData = daoFactory.getUserDao().getUserPersonalDataByIdMsUser(user);
						idNo = personalDataEncLogic.decryptToString(userPersonalData.getIdNoBytea());
					}
					String builder = "(" + idNo + ", " + invLink.getFullName() + ")" ;
					String [] message = {builder};
					throw new InvitationLinkException(this.messageSource.getMessage("businesslogic.invitationlink.useralreadyregistered",
							message, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.USER_REGISTERED);
				}
			} else {
				AmMsuser user = daoFactory.getUserDao().getUserByLoginId(invLink.getReceiverDetail());
				if (null != user) {
					TrDocumentD pendingDocument = daoFactory.getDocumentDao().getEarliestUnsignedDocumentBySignerLoginId(user.getLoginId());
					if (null != pendingDocument) {
						Status status = new Status();
						status.setCode(StatusCode.INACTIVE_LINK);
						status.setMessage(this.messageSource.getMessage("businesslogic.user.userhaspendingdocument", 
								null, this.retrieveLocaleAudit(audit)));
						response.setDocumentId(pendingDocument.getDocumentId());
						response.setStatus(status);
						return response;
					}
					
					if (idNo == null) {
						AmUserPersonalData userPersonalData = daoFactory.getUserDao().getUserPersonalDataByIdMsUser(user);
						idNo = personalDataEncLogic.decryptToString(userPersonalData.getIdNoBytea());
					}
					String builder = "(" + idNo + ", " + invLink.getFullName() + ")" ;
					String [] message = {builder};
					throw new InvitationLinkException(this.messageSource.getMessage("businesslogic.invitationlink.useralreadyregistered",
							message, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.USER_REGISTERED);
				}
			}
			
			throw new InvitationLinkException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INACTIVE_LINK,
					null, this.retrieveLocaleAudit(audit)), ReasonInvitationLink.INACTIVE_LINK);
		}
		
		if (StringUtils.isBlank(request.getVendorTypeCode())) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {"Vendor type"}, this.retrieveLocaleAudit(audit)) , ReasonVendor.VENDOR_TYPE_CODE_EMPTY);
		}
		if (!GlobalVal.CODE_LOV_VENDOR_TYPE_PSRE.equalsIgnoreCase(request.getVendorTypeCode())) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_INVALID_VENDOR_TYPE,
					new String[] {request.getVendorTypeCode()}, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_TYPE_CODE_INVALID);
		}
		
		VendorListRequest vendorListRequest = new VendorListRequest();
		vendorListRequest.setTenantCode(invLink.getMsTenant().getTenantCode());
		vendorListRequest.setVendorCode(invLink.getMsVendor().getVendorCode());
		vendorListRequest.setVendorTypeCode(request.getVendorTypeCode());
		
		// Exclude ESIGN vendor
		return this.getVendorList(vendorListRequest, GlobalVal.VENDOR_CODE_ESG, audit);
	}

	@Override
	public VendorListResponse getAllVendorList(AuditContext audit) {
		VendorListResponse response = new VendorListResponse();
		List<VendorBean> vendorBeanList = new ArrayList<>();
		List<Map<String, Object>> vendorList = 	daoFactory.getVendorDao().getAllVendorList();
		Iterator<Map<String, Object>> itr = vendorList.iterator();
		
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			VendorBean bean = new VendorBean();
			bean.setCode((String) map.get("d0"));
			bean.setName((String) map.get("d1"));
			vendorBeanList.add(bean);
		}
		response.setVendorList(vendorBeanList);
		return response;
	}
	
	
	@Override
	public VendorListResponse getVendorListV2(VendorListRequest request, String vendorCodeExclude, AuditContext audit) {
		if (null == request.getTenantCode()) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_TENANT_CODE_EMPTY,
					null, this.retrieveLocaleAudit(audit)), ReasonVendor.TENANT_CODE_EMPTY);
		}
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_TENANT_CODE_INVALID,
					null, this.retrieveLocaleAudit(audit)), ReasonVendor.TENANT_NOT_FOUND);
		}
		
		
		VendorListResponse response = new VendorListResponse();
		List<VendorBean> vendorBeanList = new ArrayList<>();
		List<Map<String, Object>> vendorList = 	daoFactory.getVendorDao().getVendorList(
				request.getVendorTypeCode(), request.getTenantCode(), request.getVendorCode(), vendorCodeExclude);
		Iterator<Map<String, Object>> itr = vendorList.iterator();
		
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			VendorBean bean = new VendorBean();
			bean.setCode((String) map.get("d0"));
			bean.setName((String) map.get("d1"));
			vendorBeanList.add(bean);
		}
		response.setVendorList(vendorBeanList);
		return response;
	}
	
	@Override
	public String checkNotificationTypeV2(String loginId, String vendorCode, AuditContext audit) {
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(loginId, vendorCode);
		
		if(null == vru.getEmailService()) {
			AmMsuser user;
	        boolean checkUserExistence = true;
	        if (StringUtils.isNumeric(loginId)) {
	            user = userValidatorLogic.validateGetUserByPhone(loginId, checkUserExistence, audit);
	        }else {
	            user = userValidatorLogic.validateGetUserByEmailv2(loginId, checkUserExistence, audit);
	        }
			if (user.getEmailService().equals("1")) {
				// Notifikasi akan dikirimkan melalui SMS ke no HP user tersebut
				return GlobalVal.NOTIF_TYPE_SMS;
			} else {
				// Notifikasi akan dikirimkan ke email pribadi user tersebut
				return GlobalVal.NOTIF_TYPE_EMAIL;
			}
		}else {
			if (vru.getEmailService().equals("1")) {
				// Notifikasi akan dikirimkan melalui SMS ke no HP user tersebut
				return GlobalVal.NOTIF_TYPE_SMS;
			} else {
				// Notifikasi akan dikirimkan ke email pribadi user tersebut
				return GlobalVal.NOTIF_TYPE_EMAIL;
			}
		}
	}

	@Override
	public GetListPSrESettingResponse getListPSrESetting(GetListPSrESettingRequest request, AuditContext audit) {
		
		GetListPSrESettingResponse response = new GetListPSrESettingResponse();
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		
		int totalResult = daoFactory.getVendorDao().countGetListPSrESetting(request);
		double totalPage = Math.ceil((double) totalResult / maxRow);
		
		List<Map<String, Object>> getListPSrESetting = daoFactory.getVendorDao().getListPSrESetting(request, min, max);
		
		List<GetListPSrESettingBean> listPSrESetting = new ArrayList<>();
		Iterator<Map<String, Object>> itr = getListPSrESetting.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			GetListPSrESettingBean listPSrESettingObject = new GetListPSrESettingBean();
			listPSrESettingObject.setVendorCode(((String) map.get("d0")));
			listPSrESettingObject.setVendorName(((String) map.get("d1")));
			listPSrESettingObject.setStatus(((String) map.get("d2")));
			listPSrESettingObject.setStatusOperating(((String) map.get("d3")));
			listPSrESettingObject.setPaymentSignType(((String) map.get("d4")));
			listPSrESettingObject.setCode(((String) map.get("d5")));
			
			listPSrESetting.add(listPSrESettingObject);
		}
		response.setGetListPSrESetting(listPSrESetting);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalResult);
		
		return response;
	}
	
	@Override
	public MssResponseType updateVendorPSrE(UpdatePSrERequest request,AuditContext audit) {
		
		if (StringUtils.isBlank(request.getVendorCode())) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_VENDOR_CODE_EMPTY,
					null, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_CODE_EMPTY);
		} else if (StringUtils.isBlank(request.getVendorName())) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_VENDOR_NAME_EMPTY,
					null, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_NAME_EMPTY);
		} else if ( StringUtils.isBlank(request.getStatus()) || (!request.getStatus().equals("1") && !request.getStatus().equals("0"))) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ACTIVEDEL,
					new String[] {"Vendor Status"}, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_STATUS_INVALID);
		} else if ( StringUtils.isBlank(request.getStatusOperating()) || (!request.getStatusOperating().equals("1") &&  !request.getStatusOperating().equals("0"))) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ACTIVEDEL,
					new String[] {"Vendor Status Operating"}, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_STATUS_INVALID);
		} else if (StringUtils.isBlank(request.getPaymentSignType())) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {"Payment Sign Type"}, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_PSRE_PAYMENT_SIGN_TYPE);
		}
			
		MsLov paymentSignType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_VENDOR_SIGN_PAYMENT_TYPE, request.getPaymentSignType());
		if (paymentSignType == null) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] { "Payment Sign Type", request.getPaymentSignType() }, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_PSRE_PAYMENT_SIGN_TYPE);

		}
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorPSrEByVendorCode(request.getVendorCode().toUpperCase());
		if (vendor == null) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_VENDOR_CODE_INVALID,
					new String[] {request.getVendorCode().toUpperCase()}, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_NOT_FOUND);
		}
		
		vendor.setVendorName(request.getVendorName().toUpperCase());
		vendor.setUsrUpd(audit.getCallerId());
		vendor.setDtmUpd(new Date());
		vendor.setIsActive(request.getStatus());
		vendor.setIsOperating(request.getStatusOperating());
		vendor.setMsLovVendorSignPaymentType(paymentSignType);
		daoFactory.getVendorDao().updateVendor(vendor);
		
		
		
		return  new MssResponseType();
	}

	@Override
	public GetPsrePriorityResponse getPsrePriority(GetPsrePriorityRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmail(audit.getCallerId(), false, audit);
		
		if (null == user) {
			throw new UserException(getMessage("businesslogic.user.usernotfoundwiththatemail", 
					new Object[] { audit.getCallerId() }, audit), ReasonUser.USER_NOT_FOUND_WITH_THAT_EMAIL);
		}
		
		MsUseroftenant userOfTenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCode(user.getIdMsUser(), tenant.getTenantCode());
		if (userOfTenant == null) {
			throw new UserException(getMessage("businesslogic.user.usertenantnotfound",
					new String[] { audit.getCallerId(), request.getTenantCode() }, audit),
					ReasonUser.USER_TENANT_NOT_FOUND);
		}
		
		List<ListPsrePriorityBean> listPsrePriority = daoFactory.getVendorDao().getListPsrePriority(request.getTenantCode());
		
		GetPsrePriorityResponse response = new GetPsrePriorityResponse();
		response.setListPsrePriority(listPsrePriority);
		
		return response;
	}

	@Override
	public UpdatePsrePriorityResponse updatePSrEPriority(UpdatePsrePriorityRequest request, AuditContext audit) {
		
		MsTenant tenant = validateTenant(request, audit);
		
		Map<String, String> seqNo = new HashMap<>();
		
		for(PsreVendorBean Psre: request.getVendor()) {
			
			if(StringUtils.isBlank(Psre.getVendorCode())) {
				throw new VendorException(this.messageSource.getMessage("businesslogic.user.vendorcannotbeempty", null, this.retrieveLocaleAudit(audit)),ReasonVendor.TENANT_CODE_EMPTY);
			}
			
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(StringUtils.upperCase(Psre.getVendorCode()));
			if(null == vendor) {
				throw new VendorException(this.messageSource.getMessage("businesslogic.saldo.vendornotexist", new String[] { Psre.getVendorCode()}, this.retrieveLocaleAudit(audit)),ReasonVendor.VENDOR_CODE_INVALID);
			}
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			
			if(null == vot){
				throw new VendorException(this.messageSource.getMessage("businesslogic.stampduty.invalidvendortenant", new String[] {tenant.getTenantName() ,vendor.getVendorName()}, this.retrieveLocaleAudit(audit)),ReasonVendor.INVALID_VENDOR_OFTENANT);
			}
			
			if(null == vot.getDefaultVendor()) {
				throw new VendorException(this.messageSource.getMessage("businesslogic.vendor.defaultvendornull", new String[] { vendor.getVendorName(),tenant.getTenantName()}, this.retrieveLocaleAudit(audit)),ReasonVendor.INVALID_VENDOR_OFTENANT);
			}
			
			if(StringUtils.isBlank(Psre.getPriority())) {
				throw new EntityNotFoundException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new Object[] { "Priority", Psre.getVendorCode()},
						this.retrieveLocaleAudit(audit)), Psre.getPriority());
			}
			
			if (seqNo.containsValue(Psre.getPriority())) {
				throw new VendorException(this.messageSource.getMessage("businesslogic.vendor.defaultvendormustunique", null, this.retrieveLocaleAudit(audit))
						, ReasonVendor.INVALID_VENDOR_OFTENANT);
			}
			seqNo.put(Psre.getVendorCode(), Psre.getPriority());

			
			vot.setDtmUpd(new Date());
			vot.setUsrUpd(request.getAudit().getCallerId());
			vot.setDefaultVendor(Psre.getPriority());
			daoFactory.getVendorDao().updateVendoroftenant(vot);	
		}
				
		
		return new UpdatePsrePriorityResponse();
	}
	
	@Override
	public UpdatePsrePriorityExternalResponse updatePSrEPriorityExternal(UpdatePsrePriorityExternalRequest request, String xApiKey, AuditContext audit) {
		
		Integer priority = 1;

		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);

		List<ListPsrePriorityBean> listPsrePriority = daoFactory.getVendorDao().getListPsrePriority(tenant.getTenantCode());

		commonValidatorLogic.validateNotNull(request.getPsreCode(), "PSrE Code", audit);
		
		Set<String> inputPrioritySet = new LinkedHashSet<>(request.getPsreCode());
		
		if (inputPrioritySet.isEmpty()) {
		   throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_EMPTY_PSRE_LIST,  null, this.retrieveLocaleAudit(audit)), ReasonVendor.PSRE_LIST_EMPTY);
		}
		
		List<String> finalPriorityList = new ArrayList<>(inputPrioritySet);

		for (ListPsrePriorityBean item : listPsrePriority) {
		    if (!inputPrioritySet.contains(item.getVendorCode())) {
		        finalPriorityList.add(item.getVendorCode());
		    }
		}

		LOG.info("Reordered Priorities: " + finalPriorityList.toString());

		
		for(String Psre: finalPriorityList) {
			
			String messageValidation = "";
			
			messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_VENDOR_CANNOT_BE_EMPTY, null, audit);
			
			commonValidatorLogic.validateNotNull(Psre, messageValidation, StatusCode.TENANT_CODE_EMPTY);
			
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(StringUtils.upperCase(Psre));
			
			messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_SALDO_VENDOR_NOT_EXIST, new String[] {Psre}, audit);
			
			commonValidatorLogic.validateNotNull(vendor, messageValidation, StatusCode.VENDOR_CODE_INVALID);
			
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			
			messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_SDT_VENDOR_TENANT_INVALID, new String[] {tenant.getTenantName() ,vendor.getVendorName()}, audit);
			
			commonValidatorLogic.validateNotNull(vot, messageValidation, StatusCode.INVALID_VENDOR_OFTENANT);
			
			messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_DEFAULT_VENDOR_NULL, new String[] {vendor.getVendorName(),tenant.getTenantName()}, audit);

			commonValidatorLogic.validateNotNull(vot.getDefaultVendor(), messageValidation, StatusCode.INVALID_VENDOR_OFTENANT);

			
			LOG.info(priority.toString());
			
			vot.setDtmUpd(new Date());
			vot.setUsrUpd(request.getAudit().getCallerId());
			vot.setDefaultVendor(priority.toString());
			daoFactory.getVendorDao().updateVendoroftenant(vot);
			priority++;
		}
				
		
		return new UpdatePsrePriorityExternalResponse();
	}
	
	private MsTenant validateTenant(UpdatePsrePriorityRequest request, AuditContext audit) {
		if(StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(this.messageSource.getMessage("businesslogic.paymentsigntype.emptytenantcode",null, this.retrieveLocaleAudit(audit) ),ReasonTenant.TENANT_CODE_EMPTY);
		} 
		MsUseroftenant userTenant = daoFactory.getUseroftenantDao().getUserTenantByLoginIdAndTenantCode(audit.getCallerId(), request.getTenantCode());
		
		if (null == userTenant) {
			throw new UserException(this.messageSource.getMessage("businesslogic.user.usernotfound",
			null, this.retrieveLocaleAudit(audit)),ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		
		MsTenant tenant =  daoFactory.getTenantDao().getTenantByCode(StringUtils.upperCase(request.getTenantCode()));
		if(null == tenant) {
			throw new TenantException(this.messageSource.getMessage("businesslogic.vendor.invalidtenantcode", null, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		
		if(!request.getTenantCode().equalsIgnoreCase(userTenant.getMsTenant().getTenantCode())) {
		 	throw new UserException(this.messageSource.getMessage("businesslogic.user.usertenantnotfound",
		 	new String[] { request.getAudit().getCallerId(), tenant.getTenantName()}, this.retrieveLocaleAudit(audit)),
		 	ReasonUser.USER_TENANT_NOT_FOUND);
		}
		return tenant;
	}

	@Override
	public MsVendor getVendorByEmailSender(String emailSender, AuditContext audit) {
		if (StringUtils.isBlank(emailSender)) {
			return null;
		}
		
		if (emailSender.contains("djelas")) {
			return daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_TEKENAJA);
		}
		
		if (emailSender.contains("digisign")) {
			return daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_DIGISIGN);
		}
		
		return null;
	}
	
}
