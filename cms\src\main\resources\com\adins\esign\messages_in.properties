multitenant.error.cannotempty = Login arguments tidak boleh kosong
multitenant.error.metadata = Tenants metadata harus di configure
multitenant.error.notenant = User ID tidak terdapat kode tenant!
multitenant.error.notcontain = User ID tidak terdapat tenant token!
multitenant.error.contain = User ID terdapat lebih dari 1 tenant token!

service.global.emptyaudit = Audit tidak boleh kosong
service.global.emptyparam = {0} tidak boleh kosong
service.global.existed = {0} sudah terdaftar dalam aplikasi
service.global.notvalid = {0} tidak valid
service.global.notvalidnumeric = {0} harus numerik
service.global.notvaliddate = {0} format tanggal harus {1}
service.global.notvalidyear = {0} tahun harus lebih besar dari {1}
service.global.maxlength = {0} panjang maksimum harus < {1}
service.global.minlength = {0} panjang minimum harus > {1}
service.global.lovnotvalid = LOV {0} tidak valid
service.global.mustgreaterthan = {0} harus diisi lebih besar dari {1}

msg.success.insert = Data berhasil di input.
msg.success.update = Data berhasil di ganti.
msg.success.delete = Data berhasil di hapus.

services.msg.success.regemail = Kami telah mengirimkan email Kode Aktivasi ke email Anda.
services.msg.success.regsms = Kami telah mengirimkan sms Kode Aktivasi ke nomor Anda.

businesslogic.notfound = Tidak ada yang ditemukan

businesslogic.global.action = {0} harus diisi {1}
businesslogic.global.activeDel = {0} harus diisi 0 or 1.
businesslogic.global.androididnotvalid = Device ID tidak terdaftar. Hubungi administrator.
businesslogic.global.dataisexist = Data {0} sudah ada.
businesslogic.global.datanotexist =  Data {0} : {1} tidak ada.
businesslogic.global.datanotfound = {0} {1} tidak ditemukan
businesslogic.global.datanotfound1 = {0} tidak ditemukan
businesslogic.global.datachanged = Data telah berubah, silahkan mengulangi transaksi
businesslogic.global.datahasachild = Penghapusan tidak selesai. Data mempunyai child.
businesslogic.global.datetimeexpired = {0} diisi minimal tanggal hari ini.
businesslogic.global.entitynotfound = {0} tidak tercatat di sistem
businesslogic.global.error = {0} Error.
businesslogic.global.errorduplicate = Error Duplikat {0}.
businesslogic.global.errorgenxld = Gagal membuat file XLS
businesslogic.global.invaliddatevalue = Tanggal perpanjangan harus lebih dari tanggal kadaluarsa.

businesslogic.global.mandatory = {0} harus diisi
businesslogic.global.mandatoryphoto = {0} kosong. Mohon cek dan ambil ulang foto.
businesslogic.global.cannotbechanged = {0} tidak bisa diubah
businesslogic.global.minlength = Panjang karakter {0} harus >= {1}
businesslogic.global.maxlength =  Panjang karakter {0} harus <= {1}
businesslogic.global.mustdecimal = {0} harus desimal
businesslogic.global.mustinterval = {0} harus diisi antara {1} dan {2}
businesslogic.global.mustunique = {0} harus unik
businesslogic.global.numeric = {0} harus diisi angka
businesslogic.global.dateformat = {0} harus menggunakan format tanggal {1}
businesslogic.global.paraminexist = Mohon sediakan parameter wajib : {0}
businesslogic.global.subsystem =  {0} harus diisi MO, MS, atau MC.
businesslogic.global.successrequestdownload = Request report telah diproses. Silahkan cek menu Report Result List
businesslogic.global.valuenotmatch = Nilai ini tidak sesuai dengan data validation restrictions yang didefinisikan untuk cell ini
businesslogic.global.invalidrole = Anda tidak bisa mengakses data orang lain
businesslogic.global.noresponse = Tidak mendapatkan respons. Mohon dicoba lagi.
businesslogic.global.daterangefiltermustbefilled = Kedua filter tanggal mulai dan akhir harus diisi.
businesslogic.global.datecompareinvalid = {0} tidak boleh melebihi {1}.
businesslogic.global.stringmaxlength = {0} tidak bisa melebihi {1} karakter
businesslogic.global.stringminlength = {0} harus setidaknya {1} karakter
businesslogic.global.status = Status tidak ditemukan

businesslogic.global.decimal.digit = {0} tidak bisa melebihi {1} digit
businesslogic.global.decimal.point = {0} tidak bisa memiliki {1} digit di belakang koma
businesslogic.global.decimal.maxvalue = {0} tidak bisa sama atau lebih besar dari {1}

businesslogic.error.changeexception = Status dari data ini telah berubah, mohon lakukan transaksi kembali
businesslogic.error.haschild = Penghapusan tidak dapat dilakukan karena data mempunyai anak
businesslogic.error.imagelocationnotdefined = Lokasi download image detail task belum didefinisikan
businesslogic.error.pdflocationnotdefined = Lokasi download pdf detail task belum didefinisikan
businesslogic.error.errorparsedate = Parse date error
businesslogic.error.parsedateformat = Format tanggal tidak sesuai, harap menggunakan format tanggal 'yyyy-MM-dd'
businesslogic.error.invaliddatevalue = Tanggal awal melebihi tanggal akhir.

businesslogic.context.jobalreadydeleted = Job user sudah dinonaktifkan
businesslogic.context.subsystemalreadydeleted = Subsystem user sudah dinonaktifkan
businesslogic.context.unauthorizedaccess = Unauthorized access
businesslogic.context.useralreadydeleted = User sudah dinonaktifkan / dihapus
businesslogic.context.useralreadylocked = User sudah terkunci, silahkan melakukan lupa kode akses untuk membuat kode akses baru
businesslogic.context.userinactive = Login gagal. Akun Anda sudah tidak aktif.

businesslogic.usermanagement.branchnotfound = Branch dengan code '{0}' tidak ditemukan
businesslogic.usermanagement.initialexist = Initial {0} sudah ada.
businesslogic.usermanagement.initialnamelength = Karakter initial name harus < 7
businesslogic.usermanagement.jobnotfound = Job dengan code '{0}' tidak ditemukan
businesslogic.usermanagement.loginexist = LoginID {0} sudah ada.
businesslogic.usermanagement.loginidexist = User dengan loginId {0} sudah ada
businesslogic.usermanagement.unauthorizedoperation = Unauthorized Operation.
businesslogic.usermanagement.usernotfound = User dengan LoginId '{0}' tidak ditemukan
businesslogic.usermanagement.rolenotexist = Tenant tidak memiliki role user management.
businesslogic.usermanagement.rolenotisusermanagement = Role bukan merupakan user management
businesslogic.usermanagement.nophoneisnotvalid = Nomor telepon tidak valid.
businesslogic.usermanagement.idNoisnotvalid = Jumlah digit KTP salah.
businesslogic.usermanagement.emailisnotvalid = Email tidak valid.
businesslogic.usermanagement.isusermanagementinvalid = Role {0} bukan userManagement.
businesslogic.usermanagement.usermanagementnotfound = User dengan Email / NIK {0} tidak ditemukan.
businesslogic.usermanagement.rolenotfound = Role tidak ditemukan.
businesslogic.usermanagement.rolecodenotfound = Role code dengan role code {0} tidak ditemukan
businesslogic.usermanagement.rolecodenotavailableintenant = Role code {0} tidak ada di tenant {1}
businesslogic.usermanagement.invalidtenantandcaller = callerId {0} tidak terdaftar di tenant {1}
businesslogic.usermanagement.datausermanagementnotfound = User dengan email {0} tidak ditemukan.
businesslogic.usermanagement.datauserempty = Email tidak boleh kosong.
businesslogic.usermanagement.userisnotactive = User bukan merupakan user aktif
businesslogic.usermanagement.calleridnotatenantofloginid = {0} bukanlah tenant dari {1}.
businesslogic.usermanagement.invalidroleanduserandtenant = {0} tidak memiliki role {1} dengan tenant {2}.
businesslogic.usermanagement.samenoktp = Nomor KTP yang diinput sudah sama dengan nomor KTP di sistem
businesslogic.usermanagement.idloginexist = Login Id sudah terdaftar.

businesslogic.usermanagement.officeisnotexist = Kantor tidak tersedia.
businesslogic.usermanagement.loginidisnotexist = User tidak tersedia.

businesslogic.job.entitynotuniquejob = Job ID {0} sudah ada.
businesslogic.job.jobresultnotfound = Job tidak ditemukan.

businesslogic.branch.entitynotexistbranchcode = Code Cabang {0} tidak terdaftar.
businesslogic.branch.entitynotuniquebranchcode = Code Cabang {0} already exist.
businesslogic.error.generatexls = Error saat mengeluarkan XLS

#ESG-957: Ubah istilah kata sandi -> kode akses
businesslogic.login.dormantstatus = Dormant status untuk username {0}. Harap hubungi administrator.
businesslogic.login.dormantstatus1 = Akun Anda dalam status dormant. Harap hubungi perusahaan dimana Anda akan melakukan transaksi.
businesslogic.login.failed = Login gagal. Username tidak ditemukan
businesslogic.login.idinexist = Login gagal. Periksa kembali inputan anda
businesslogic.login.idpassword = Login gagal. Periksa kembali username dan kode akses
businesslogic.login.inactive = Login gagal. Username {0} tidak aktif
businesslogic.login.inactiveadmin = Username {0} tidak aktif. Harap hubungi administrator.
businesslogic.login.inactivejob = Username {0} terdaftar di job yang tidak aktif. Harap hubungi administrator.
businesslogic.login.inactivesubsystem = Login gagal. Username {0} terdaftar di subsystem yang tidak aktif. Harap hubungi administrator.
businesslogic.login.invalididpassword = Username atau kode akses tidak valid.
businesslogic.login.locked = Login gagal. Username {0} terkunci
businesslogic.login.lockedadmin = Username {0} terkunci. Harap hubungi administrator.
businesslogic.login.loggedin = Username {0} sudah melakukan login.
businesslogic.login.notfieldperson = Login gagal. Username {0} bukan pengguna lapangan
businesslogic.login.relogin = Username {0} perlu login ulang.
businesslogic.login.takingdayoff = tidak dapat login. User sedang cuti
businesslogic.login.userpasswordrequired = Username atau kode akses harus diisi

businesslogic.generalsetting.gsinexist = General Setting tidak ada
businessLogic.generalsetting.gscodeempty = Gs Code tidak boleh kosong.
businesslogic.generalsetting.gsinexistwithcode = General Setting dengan kode {0} tidak ditemukan di sistem

businesslogic.changepassword.emptyoldnew = Kode akses lama dan baru tidak boleh kosong
businesslogic.changepassword.history = Kode akses tidak boleh menggunakan {0} kode akses sebelumnya
businesslogic.changepassword.incorrectold = Kode akses lama salah
businesslogic.changepassword.minchars = Minimal kode akses adalah 8 karakter
businesslogic.changepassword.success = Kode akses Anda sukses diubah
businesslogic.changepassword.nodoc = pengguna belum menerima dokumen

businesslogic.document.errorsend = Error ketika mengirimkan document ke {0} : {1}
businesslogic.document.stampdutynotenough = Materai tidak cukup
businesslogic.document.errorreadwritepdf = Error ketika membaca atau menulis pdf
businesslogic.document.samefile = File yang dikirim harus berbeda
businesslogic.document.errorsign = Error ketika tandatangan dokument : {0}, {1}
businesslogic.document.errorsignpagenumpage  = Sign Page ({0}) tidak boleh lebih besar dari Number of Page ({1})
businesslogic.document.missingsignertype = Signer Type untuk Sign Type {0} harus diisi
businesslogic.document.usercannotaccessdoc = User dengan role {0} tidak bisa akses dokumen
businesslogic.document.usercannotaccessdoc1 = User tidak bisa akses dokumen
businesslogic.document.custdocumentinaccessible = Customer tidak bisa akses dokumen
businesslogic.document.bmdocumentinaccessible = BM tidak bisa akses dokumen
businesslogic.document.customernotallowed = Pelanggan tidak bisa melakukan autosign
businesslogic.document.usermustbeactive = User harus melakukan aktivasi sebelum melakukan autosign
businesslogic.document.invaliddaterange = Range maksimum tanggal {0} adalah 30 hari
businesslogic.document.invaliddaterangegeneralsetting = Range maksimum tanggal {0} adalah {1} hari
businesslogic.document.cannotaccessothertenant = User tidak bisa mengakses dokumen tenant lain
businesslogic.document.alreadycanceled = Dokumen sudah dibatalkan
businesslogic.document.illegalissignloc = isSignLoc harus diisi dengan 1 atau 0
businesslogic.document.invalidinquirytype = Invalid inquiry type
businesslogic.document.invalidtenant = Tidak bisa memeriksa document template milik tenant lain
businesslogic.document.invalidrole = Hanya Admin Client yang bisa akses
businesslogic.document.invalidrolelegal = Hanya Admin Legal yang bisa akses
businesslogic.document.emptyemail = Email tidak boleh kosong
businesslogic.document.signernikemailalreadyexist = NIK {0} dan email {1} ini sudah digunakan oleh 2 user berbeda. Harap periksa kembali data yang dikirimkan.
businesslogic.document.signerphonenoemailalreadyexist = Nomor Telepon {0}  dan email {1} ini sudah digunakan oleh 2 user berbeda. Harap periksa kembali data yang dikirimkan.
businesslogic.document.signerphonenonikalreadyexist = Nomor Telepon {0} dan NIK ini {1} sudah digunakan. Gunakan nomor telepon dan NIK yang lain.
businesslogic.document.signerphonenoalreadyexist = Nomor Telepon ini {0} sudah digunakan. Gunakan nomor telepon yang lain.
businesslogic.document.mismatchemailandid = NIK ini {0} bukan milik user dengan email ini {1}.
businesslogic.document.mismatchemailandphone = No. Telp. ini {0} bukan milik user dengan email ini {1}.
businesslogic.document.mismatchemailandphone1 = Email {0} tidak terdaftar dengan No Telp {1}.
businesslogic.document.signernikalreadyexist = NIK ini {0} sudah digunakan. Gunakan NIK yang lain.
businesslogic.document.documentnotsignedyet = Dokumen belum ditandatangani semua
businesslogic.document.alreadysignedall = Dokumen sudah dibubuhi dengan e-meterai
businesslogic.document.generateexcelerror = Error ketika membuat excel
businesslogic.document.agreementnotfoundintenant = Kontrak {0} tidak ditemukan di tenant {1}
businesslogic.document.docnotfoundintenant = Dokumen {0} tidak ditemukan di tenant {1}
businesslogic.document.docnotfound = Dokumen tidak ditemukan untuk email Anda
businesslogic.document.sameemailinrequest = Email ini {0} digunakan oleh beberapa penanda tangan pada pengiriman dokumen ini
businesslogic.document.samenikinrequest = NIK ini {0} digunakan oleh beberapa penanda tangan pada pengiriman dokumen ini
businesslogic.document.samephoneinrequest = No. telp ini {0} digunakan oleh beberapa penanda tangan pada pengiriman dokumen ini
businesslogic.document.unsigneddocumentempty = Dokumen sudah ditanda tangani untuk pengguna {0}, silahkan cek monitoring dokumen
businesslogic.document.inactiveagreement = Kontrak {0} sudah tidak aktif
businesslogic.document.inactiveagreement1 = {0} {1} sudah tidak aktif
businesslogic.document.usednik = NIK {0} sudah menggunakan email {1}. Email {2} tidak bisa digunakan.
businesslogic.document.emailmandatory = Email harus diisi untuk NIK {0}
businesslogic.document.nikdoesnothaveemail = Kosongkan parameter email untuk NIK {0} karena sudah terdaftar dengan email dari sistem
businesslogic.document.nikdoesnothaveemailext = Penanda tangan dengan NIK {0} terdaftar dengan email berbeda. Isi dengan email yang benar atau kosongkan parameter email karena sudah terdaftar dengan email dari sistem
businesslogic.document.certexpired = Masa berlaku sertifikat elektronik Anda telah habis, mohon meminta pengiriman link registrasi ulang kepada admin.
businesslogic.document.alreadysigned = Dokumen sudah pernah ditandatangani oleh user
businesslogic.document.needonlineverif = Harap melakukan verifikasi online pada portal milik vendor.
businesslogic.document.invalidperuridoctype = Jenis Dokumen tidak valid.
businesslogic.document.mustbase64 = Dokumen harus dalam format Base64
businesslogic.document.templatedoesnothavesdt = Template dokumen {0} tidak menyimpan lokasi meterai
businesslogic.document.mandatorycannotbeempty = {0} tidak boleh kosong
businesslogic.document.agreementnotfound = Kontrak {0} tidak ditemukan
businesslogic.document.tenanttrxidexist = Dokumen dengan ID Transaksi {0} sudah ada
businesslogic.document.templatemusthavesdt = Templat Dokumen tidak Memiliki Meterai
businesslogic.document.invaliddaterangerecon = Range maximum tanggal {0} adalah 14 hari
businesslogic.document.invalidphonenik = Nomor Telepon terdaftar dengan NIK {0}, berbeda dengan NIK yang direquest yaitu {1}
businesslogic.document.invalidnikphone = NIK terdaftar dengan dengan nomor telepon {0}, berbeda dengan nomor telepon yang direquest yaitu {1}
businesslogic.document.invalidnikemail = NIK sudah terdaftar dengan email {0}, berbeda dengan email yang direquest yaitu {1}
businesslogic.document.invalidemailphone = Email terdaftar dengan nomor telepon {0}, berbeda dengan nomor telepon yang direquest yaitu {1}
businesslogic.document.invalidemailnik = Email terdaftar dengan NIK {0}, berbeda dengan NIK yang direquest yaitu {1}
businesslogic.document.emptydocumentid = Document Id tidak boleh kosong
businesslogic.document.documentnotfound = Document dengan document Id {0} tidak ditemukan
businesslogic.document.documentidnotfound = Document dengan ID {0} tidak ditemukan
businesslogic.document.documentnotfoundintenant = Document dengan document ID {0} tidak ditemukan di tenant {1}
businesslogic.document.usernotsignerofdocument = {0} bukan penanda tangan dokumen ini
businesslogic.document.tenantcannotaccessdoc = Dokumen tidak bisa diakses
businesslogic.document.unhandleddocumentvendor = Vendor {0} tidak dipetakan untuk melihat dokumen
businesslogic.document.autosignfailed = Tanda tangan otomatis gagal, harap kirim ulang dokumen
businesslogic.document.ipaddressempty = IP Address tidak boleh kosong / null
businesslogic.document.browserempty = Browser tidak boleh kosong / null
businesslogic.document.invalidvendorsign = Hanya dokumen dengan Vendor {0} yang dapat direquest
businesslogic.document.requestsignconfirmationduplicate = Request anda dengan document id {0} sudah diterima atau sedang dalam proses, harap menunggu.!
businesslogic.document.documentinsignprogress = Document dengan ID {0} sedang dalam proses tanda tangan
businesslogic.document.signerinvalid = User {0} bukan signer dari document {1}
businesslogic.document.requestsignconfirmationdone = request anda dengan document id {0} sudah selesai di sign.
businesslogic.document.documentfinishedsign = Document dengan Id {0} sudah selesai di tanda tangan.
businesslogic.document.usercannotdoautosign = User {0} tidak bisa melakukan autosign. Mohon kirim ulang dokumen.
businesslogic.document.documentisbeingsigned = Document dengan ID {0} sedang dalam proses tanda tangan
businesslogic.document.documentwithidnotfound = Document dengan ID {0} tidak ditemukan
businesslogic.document.contractisinstampingprocess = Kontrak {0} sedang dalam proses stamping
businesslogic.document.invalidrefno = No kontrak {0} tidak ditemukan
businesslogic.document.verificationcannotbeempty = Verifikasi tanda tangan Dokumen tidak boleh kosong ! Silahkan masukan OTP atau foto diri.
businesslogic.document.referencenoempty = Ref Number tidak boleh kosong
businesslogic.document.contractalreadysignedandstamped = Kontrak {0} sudah ditandatangani dan distamping
businesslogic.document.invalidrefno = No kontrak {0} tidak ditemukan
businesslogic.document.verificationcannotbeempty = Verifikasi tanda tangan Dokumen tidak boleh kosong ! Silahkan masukan OTP atau foto diri.
businesslogic.document.contractalreadysigned = Kontrak {0} sudah selesai ditanda tangan
businesslogic.document.contractisinsigningprocess = Kontrak {0} sedang dalam proses tanda tangan
businesslogic.document.userisnotthesignerofthecontract = User bukan penandatangan dari kontrak {0}
businesslogic.document.docnotfounds = Dokumen Tidak Ditemukan
businesslogic.document.alldocumentalreadysigned = Semua dokumen sudah ditandatangan oleh user
businesslogic.document.mismatchedrefno = No. Kontrak harus sama pada semua dokumen
businesslogic.document.docfileinvalid = String Base64 Document File tidak valid
businesslogic.document.doctempcodeorsignlocmustbefilled = Kode Templat Dokumen atau lokasi tanda tangan harus diisi
businesslogic.document.stampsignincomplete = Pastikan lokasi tanda tangan dan meterai sudah lengkap
businesslogic.document.agreementnotfoundintenant2 = Dokumen dengan {0}{1} tidak ditemukan di tenant {2}
businesslogic.document.contractalreadysigned2 = Dokumen dengan {0}{1} sudah selesai ditanda tangan
businesslogic.document.contractisinsigningprocess2 = Dokumen dengan {0}{1} sedang dalam proses tanda tangan
businesslogic.document.userisnotthesignerofthecontract2 = User bukan penandatangan dari dokumen dengan {0}{1}
businesslogic.document.contractalreadysignedandstamped2 = Dokumen dengan {0} {1} sudah ditandatangani dan distamping
businesslogic.document.contractisinstampingprocess2 = Dokumen dengan {0} {1} sedang dalam proses stamping
businesslogic.document.invalidsignaction = Sign Action harus diisi 'at' atau 'mt'
businesslogic.document.invalidsignertype = Signer Type {0} tidak valid
businesslogic.document.mustsendsigner = Data untuk signer type {0} harus dikirimkan untuk templat dokumen {1}
businesslogic.document.checkautosigndata = Mohon periksa kembali keabsahan data yang digunakan untuk autosign
businesslogic.document.documentnostamp = Dokumen dengan {0} {1} tidak membutuhkan materai
businesslogic.document.signernotactivated = Anda belum terdaftar ke {0}
businesslogic.document.mustfirstlysignbyother = Dokumen harus ditandatangani oleh {0} terlebih dahulu
businesslogic.document.cannotbesequential = Templat Dokumen {0} belum dilakukan pengaturan untuk tanda tangan sekuensial
businesslogic.document.cannotautosignwithvendor = Tidak bisa tanda tangan otomatis dengan {0}
businesslogic.document.cannotautosignwhensequential = Tidak bisa tanda tangan otomatis untuk dokumen sekuensial
businesslogic.document.cannotautosignwithouttemplate = Tanda tangan otomatis harus dilakukan dengan templat dokumen
businesslogic.document.maxsignexceeded = Setiap penanda tangan hanya bisa tanda tangan 1 kali pada setiap dokumen dengan {0}
businesslogic.document.mustotp = Vendor {0} harus menggunakan verifikasi OTP
businesslogic.document.seqnomustbeunique = Urutan tanda tangan harus unik
businesslogic.document.seqmustbefilled = Urutan tanda tangan harus diisi untuk dokumen dengan tanda tangan sekuensial
businesslogic.document.documentnameempty = Nama dokumen tidak boleh kosong
businesslogic.document.docfileempty = File dokumen tidak boleh kosong
businesslogic.document.stampinglocationempty = Dokumen harus memiliki setidaknya 1 materai
businesslogic.document.peruridoctypeinvalid = Tipe dokumen peruri tidak valid
businesslogic.document.documentvendornotmatch = Tidak dapat mengecek dokumen dengan vendor yang berbeda
businesslogic.document.verificationotpcannotbeempty = Verifikasi tanda tangan Dokumen tidak boleh kosong ! Silahkan masukan OTP.
businesslogic.document.cannotcancelalreadysigned = {0} sudah selesai ditandatangani. Tanda tangan tidak bisa dibatalkan.
businesslogic.document.doctekenordigi = Harap mengambil link tanda tangan untuk melanjutkan tanda tangan.
businesslogic.document.psrenootp = PSrE untuk Kontrak {0} tidak perlu melakukan request OTP melalui metode ini.
businesslogic.document.documentdoesnotbelongtorefnumber =  Document bukan milik reference number yang diberikan
businesslogic.document.cannotsignwithvendor = Dokumen dengan vendor {0} belum dapat melakukan tanda tangan.
businesslogic.document.documentsignvendornotmatch = Tidak dapat melakukan tanda tangan dengan vendor yang berbeda.
businesslogic.document.documentotpnotsame = Tidak ada request OTP untuk document yang akan di tanda tangan. Harap melakukan request OTP terlebih dahulu.
businesslogic.document.documentsignlinkwithvendornotready = Dokumen dengan vendor {0} belum dapat melakukan request sign link, bisa langsung melakukan request tanda tangan
businesslogic.document.documentisnotvalidatetostamp = Dokumen tidak memenuhi kualifikasi untuk dibubuhi e-meterai.
businesslogic.document.certnotfound = Sertifikat elektronik tidak ditemukan.
businesslogic.document.branchnotfound = Cabang tidak tersedia.
businesslogic.document.invalidrefnumberlength = Panjang Nomor Referensi tidak boleh lebih dari 50.
businesslogic.document.documentownernotexists = Pemilik dokumen {0} tidak ditemukan.
businesslogic.document.highestpriortyunsigned = Ada dokumen yang lebih prioritas yang perlu ditandatangani terlebih dahulu.
businesslogic.document.customsignnotexist = Foto guratan tanda tangan untuk NIK {0} tidak tersedia. Harap unggah foto guratan tanda tangan terlebih dahulu.
businesslogic.document.vendorregistrationidempty = Vendor registrasi id untuk signer {0} tidak ada.
businesslogic.document.notactive = Signer {0} belum aktivasi
businesslogic.document.certdate.notfound = Tanggal kedaluwarsa sertifikat signer {0} tidak ditemukan.
businesslogic.document.poacert.incomplete = Sertifikat POA signer {0} tidak lengkap atau tidak ada.
businesslogic.document.poacert.expired = Sertifikat POA untuk signer {0} sudah kedaluwarsa.

businesslogic.insertstamping.var.stampduty 		= Meterai
businesslogic.insertstamping.var.docno 			= Nomor Dokumen
businesslogic.insertstamping.var.docname 		= Nama Dokumen
businesslogic.insertstamping.var.docdate 		= Tanggal Dokumen
businesslogic.insertstamping.var.doctype 		= Tipe Dokumen
businesslogic.insertstamping.var.doctypeperuri	= Jenis Dokumen
businesslogic.insertstamping.var.doctemplate	= Templat Dokumen
businesslogic.insertstamping.var.file			= Dokumen
businesslogic.insertstamping.var.docnominal		= Nominal Dokumen
businesslogic.insertstamping.var.regionCode		= Kode Region
businesslogic.insertstamping.var.regionName		= Nama Region
businesslogic.insertstamping.var.officeCode		= Kode Cabang
businesslogic.insertstamping.var.officeName		= Nama Cabang
businesslogic.insertstamping.var.userEmail		= E-mail pengguna
businesslogic.insertstamping.var.stampLoc		= Lokasi Stamping dan/atau Bagian dari Lokasi Stamping
businesslogic.insertstamping.var.doctemplate	= Templat Dokumen
businesslogic.insertstamping.var.docnumber		= Nomor Dokumen
businesslogic.insertstamping.var.trxid			= Nomor Transaksi Dokumen
businesslogic.insertstamping.var.idno			= NIK/NPWP Pemungut
businesslogic.insertstamping.var.idtype			= Tipe Identitas Pemungut
businesslogic.insertstamping.var.name			= Nama Pemungut
businesslogic.insertstamping.var.taxtype		= Tipe Pajak
businesslogic.insertstamping.var.returnresult	= Flag Return Hasil Stamping
businesslogic.insertstamping.invaliddocnominal	= Nominal Dokumen hanya bisa diisi dengan angka
businesslogic.insertstamping.invalididtype		= Tipe Identitas {0} tidak terdaftar pada sistem
businesslogic.insertstamping.doctemplateorloc	= Mohon isi Lokasi Stamping atau Templat Dokumen yang valid.
businesslogic.insertstamping.pemungutnotavailable = Tipe Pajak Pemungut tidak tersedia. Harap melakukan permintaan Stamping dengan tipe pajak Non-Pemungut.

businesslogic.insertmanualsign.var.refno	 		= Nomor Kontrak
businesslogic.insertmanualsign.var.paymenttype 		= Tipe Pembayaran
businesslogic.insertmanualsign.var.automaticstamp 	= Stamp Meterai Otomatis
businesslogic.insertmanualsign.var.signername 				= Nama Penandatangan dengan No. HP. {0}
businesslogic.insertmanualsign.var.signerphone 				= No. HP. Penandatangan dengan Nama {0}
businesslogic.insertmanualsign.var.signerphonenamewithemail	= Nama dan No. HP. Penandatangan dengan Email {0}
businesslogic.insertmanualsign.var.signerphonename			= Nama dan No. HP. Penandatangan
businesslogic.insertmanualsign.var.signerempty				= Dokumen harus memiliki setidaknya 1 penandatangan
businesslogic.insertmanualsign.signerberegistered			= Penandatangan {0} tidak terdaftar di Esignhub
businesslogic.insertmanualsign.signermustsign				= Penandatangan {0} harus menandatangani dokumen
businesslogic.insertmanualsign.signermustberegisteredtopsre	= Penandatangan {0} belum terdaftar di PSRE {1}
businesslogic.insertmanualsign.signeremailmismatch			= Penandatangan {0} harus menggunakan email yang sudah terdaftar di Esignhub
businesslogic.insertmanualsign.signerphonemismatch			= Penandatangan {0} harus menggunakan nomor telepon yang sudah terdaftar di Esignhub
businesslogic.insertmanualsign.mustuseqrwithvendor			= QR harus digunakan untuk vendor {0}

businesslogic.emailsender.sendemailerror = Muncul error saat mengirim email

businesslogic.email.reademailerror = Muncul error saat membaca email
businesslogic.email.deleteemailerror = Muncul error saat menghapus email

businesslogic.paymentsigntype.emptytenantcode = Tenant code tidak bisa kosong
businesslogic.paymentsigntype.tenantnotfound = Tenant tidak ditemukan
businesslogic.paymentsigntype.invalidrole = Hanya admin client {0} yang bisa akses

businesslogic.report.errordata = Error ketika mengambil data
businesslogic.report.excelresult = Lokasi hasil excel tidak terdefinisi
businesslogic.report.nocsv = File CSV tidak ditemukan

businesslogic.stampduty.errorparsingfee = Error ketika mengambil harga materai
businesslogic.stampduty.errorparsingqty = Error ketika mengambil jumlah materai
businesslogic.stampduty.usercannotcreate = User tidak bisa membuat materai
businesslogic.stampduty.invalidvendortenant = Tenant {0} tidak memiliki {1} sebagai vendor
businesslogic.stampduty.vendorcannotcreate = Vendor {0} tidak bisa membuat materai
businesslogic.stampduty.emptyinvoiceno = Invoice no tidak boleh kosong
businesslogic.stampduty.noinvoiceno = Invoice no tidak ditemukan
businesslogic.stampduty.vendorcannotdelete = Vendor {0} tidak bisa menghapus meterai

businesslogic.stampduty.flaggedforautomaticstamp = Dokumen akan secara otomatis dibubuhkan e-Meterai jika sudah selesai ditandatangani
businesslogic.stampduty.needsignbeforestamp = Dokumen perlu selesai ditandatangani sebelum mulai pembubuhan e-Meterai
businesslogic.stampduty.onstampingprocess = Dokumen sudah dalam proses pembubuhan e-Meterai
businesslogic.stampduty.doesnotneedstamp = Dokumen tidak membutuhkan e-Meterai
businesslogic.stampduty.cannotretrystamp = Dokumen ini tidak bisa dilakukan percobaan stamping

businesslogic.emeterai.emptylogincredential = Kredensial login kosong
businesslogic.emeterai.failedtogetossdocument = Gagal mengambil dokumen {0} dari OSS
businesslogic.emeterai.cannotretry = Dokumen tidak bisa diproses stamping ulang. Status stamping dokumen: {0}
businesslogic.emeterai.cannotretryfromupload = Stamping dokumen tidak bisa dicoba dari upload ulang
businesslogic.emeterai.startretryfromupload = Proses retry stamping from upload dimulai
businesslogic.emeterai.stampfailed = Stamping gagal. Silahkan coba kembali nanti

businesslogic.emeterai.onprem.filecheckfailed = Dokumen hasil stamp belum ada

#ESG-957: Ubah istilah kata sandi -> kode akses
AbstractUserDetailsAuthenticationProvider.badCredentials=Login gagal. Periksa kembali username dan kode akses

businesslogic.user.updateavatar = Avatar user berhasil diperbaharui
businesslogic.user.usernotfound = User tidak ditemukan
businesslogic.user.invalidparam = Invalid parameter : {0}
businesslogic.user.erroractivation = Error ketika aktivasi user : {0}
businesslogic.user.invalidloginid = callerId dan loginId harus sama
businesslogic.user.inactiveuser = User tidak aktif atau tidak ada
businesslogic.user.invalidnewpassword = Kode akses baru harus mengandung huruf kapital, huruf kecil, angka, dan karakter spesial.
businesslogic.user.minlengthpassword = Kode akses harus memiliki minimal 8 karakter
businesslogic.user.passwordhasbeenused = Kode akses baru sudah pernah dipakai
businesslogic.user.incorrectresetcode = Reset code salah
businesslogic.user.invalidtenant = Tidak bisa mengakses user milik tenant lain
businesslogic.user.invalidrole = User hanya bisa diakses oleh admin
businesslogic.user.validrole = Hanya role {0} yang bisa akses
businesslogic.user.alreadyregistered = User sudah terdaftar
businesslogic.user.invalidotpcode = Kode OTP salah
businesslogic.user.expiredotpcode = Kode OTP Anda sudah kadaluarsa
businesslogic.user.personaldatanotfound = User personal data tidak ditemukan
businesslogic.user.nodoctosign = Tidak ada dokumen yang perlu ditanda tangan
businesslogic.user.errorinsertuser = Error ketika tambah user baru
businesslogic.usermanagement.phonealreadyexist = No HP sudah terdaftar
businesslogic.user.incorrectresetpasswordlinkcode = OTP reset kode akses salah
businesslogic.user.invalidniklength = Panjang NIK harus 16 digit
businesslogic.user.invalidnpwplength = Panjang NPWP harus 15-16 digit
businesslogic.user.maxresetpasswordreached = Tidak dapat melakukan reset kode akses lagi hari ini
businesslogic.user.phonealreadyregistered = Nomor HP {0} sudah terdaftar
businesslogic.user.phonealreadyregistered1 = Nomor HP {0} sudah terdaftar dengan email {1}
businesslogic.user.emailalreadyregistered = Email {0} sudah terdaftar
businesslogic.user.cannotsentemail = Harus kirim OTP via SMS untuk Pengguna {0}
businesslogic.user.verifynotmatch = Tidak sesuai dengan verifikasi awal
businesslogic.user.invalidphone = Nomor telepon tidak valid : {0}
businesslogic.user.invalidemail = Email tidak valid : {0}
businesslogic.user.nikalreadyregistered = NIK {0} sudah terdaftar dengan email {1}
businesslogic.user.nikalreadyregistered1 = NIK {0} sudah terdaftar
businesslogic.user.pleaseregister = Anda sudah menerima permintaan tanda tangan. Mohon registrasi melalui link permintaan tanda tangan yang telah dikirim.
businesslogic.user.continuesign = User sudah selesai registrasi dan aktivasi. Silahkan lanjutkan tanda tangan melalui link permintaan tanda tangan yang telah dikirim.
businesslogic.user.doubleemail = Email ini {0} digunakan lebih dari satu user
businesslogic.user.doublephone = No. Telepon ini {0} digunakan lebih dari satu user
businesslogic.user.doublenik = NIK {0} digunakan lebih dari satu user
businesslogic.user.invalidgender = Kode jenis kelamin tidak valid
businesslogic.user.usertenantnotfound = User {0} tidak ditemukan di tenant {1}
businesslogic.user.invalidupdatecode = Update code tidak valid
businesslogic.user.userhaspendingdocument = Anda memiliki dokumen yang perlu ditandatangani. Silahkan login.
businesslogic.user.alreadyactivated = User sudah selesai registrasi dan aktivasi. Silahkan menunggu permintaan tanda tangan.
businesslogic.user.useremailhasdocument = Pengguna dengan email {0} sudah memiliki dokumen yang perlu ditandatangani
businesslogic.user.userphonehasdocument = Pengguna dengan no HP {0} sudah memiliki dokumen yang perlu ditandatangani
businesslogic.user.noupdate = Tidak ada data yang perlu diperbarui pada user.
businesslogic.user.cannotupdate = Tidak bisa memperbarui data user karena user sudah selesai registrasi dan aktivasi.
businesslogic.user.cannotupdateemail = Tidak bisa memperbarui email user karena email saat ini sudah dibuatkan sistem.
businesslogic.user.usednikphone = NIK dan No HP sudah digunakan di email {0}
businesslogic.user.usednik = NIK sudah digunakan di email {0}
businesslogic.user.userdataused = Data {0} sudah ada, silahkan menunggu / menggunakan link tanda tangan
businesslogic.user.phonenotregistered = Penandatangan dengan Tipe {0} dan No HP {1} belum melakukan registrasi
businesslogic.user.emailnotregistered = Penandatangan dengan Tipe {0} dan Email {1} belum melakukan registrasi
businesslogic.user.niknotregistered = Penandatangan dengan Tipe {0} dan NIK {1} belum melakukan registrasi
businesslogic.user.phonenotactivated = Penandatangan dengan Tipe {0} dan No HP {1} belum melakukan aktivasi
businesslogic.user.emailnotactivated = Penandatangan dengan Tipe {0} dan Email {1} belum melakukan aktivasi
businesslogic.user.niknotactivated = Penandatangan dengan Tipe {0} dan NIK {1} belum melakukan aktivasi
businesslogic.user.emailnotfound = Email {0} tidak ditemukan di eSignHub
businesslogic.user.notregistered = User Belum Registrasi
businesslogic.user.nikusedinemail = Email {0} tidak bisa digunakan untuk NIK {1} karena sudah terdaftar dengan email lain
businesslogic.user.nikusedinphone = No HP {0} tidak bisa digunakan untuk NIK {1} karena sudah terdaftar dengan No HP lain
businesslogic.user.phoneusedbyother = No HP {0} sudah digunakan oleh pengguna lain
businesslogic.user.emailuserbyother = Email {0} sudah digunakan oleh pengguna lain
businesslogic.user.vendorcannotbeempty = Vendor Code tidak boleh kosong
businesslogic.user.invlinknotexist = Link undangan tidak ditemukan.
businesslogic.user.fillatleastone = Email atau No. Telp. harus diisi.
businesslogic.user.errorchangedigidata = Error Ganti Data Digisign: request forbidden by administrative rules.
businesslogic.user.canreregister = Anda bisa melakukan registrasi ulang ke {0}
businesslogic.user.signernotallowed = Penandatangan dengan Tipe {0} dan No HP {1} tidak di izinkan tanda tangan otomatis
businesslogic.user.tenantwithcalleridnotfound = {0} bukan merupakan tenant {1}
businesslogic.user.calleridisnotadmclient = {0} bukan merupakan admin client
businesslogic.user.idnoalreadyregistered = No KTP {0} sudah terdaftar pada user lain
businesslogic.user.invalidpasswordcomplexity = Kode akses harus mengandung huruf kapital, huruf kecil, angka, dan karakter spesial.
businesslogic.user.usernotregisteredinvendor = User tidak terdaftar pada vendor tersebut
businesslogic.user.usernotfoundwiththatemail = User dengan email {0} tidak ditemukan
businesslogic.user.signeremailempty = Signer email tidak boleh kosong
businesslogic.user.vendorcodeempty = Vendor code tidak boleh kosong
businesslogic.user.vendornameempty = Nama vendor tidak boleh kosong
businesslogic.user.passwordnotmatch = Password tidak cocok
businesslogic.user.passwordcannotbeemtpy = Password tidak boleh kosong
businesslogic.user.maxotpactivationuserreached = Tidak dapat meminta permintaan OTP aktivasi user lagi hari ini
businesslogic.user.userhasnotregistered = User belum registrasi
businesslogic.user.useralreadyactivated = User sudah aktivasi
businesslogic.user.phonenoisempty = Nomor handphone kosong
businesslogic.user.usernotfoundwiththatphoneno = User dengan nomor handphone {0} tidak ditemukan
businesslogic.user.phonenotmatchwithinvitation = Nomor handphone tidak sesuai dengan data undangan
businesslogic.user.maxotpsigningverificationreached = Tidak dapat meminta permintaan OTP verifikasi penandatanganan lagi hari ini
businesslogic.user.userhasnotactivated = User belum aktivasi
businesslogic.user.usernotregisteredintenant = User tidak terdaftar pada tenant tersebut
businesslogic.user.usernotsigner = user {0} bukan penanda tangan
businesslogic.user.userdoesnothaveanydocument = User belum memiliki dokumen
businesslogic.user.maxlivenessfacecomparereached = Percobaan verifikasi wajah sudah melewati batas harian
businesslogic.user.nikisnotnumber = NIK harus berupa angka
businesslogic.user.alreadyregisteredinesignhub = Anda sudah pernah terdaftar di eSignHub
businesslogic.user.usernotfoundwiththatid = User dengan NIK {0} tidak ditemukan
businesslogic.user.invalidemailformat = Format <NAME_EMAIL>
businesslogic.user.invalidphonenoformat = Format nomor handphone tidak valid
businesslogic.user.invaliddateformat = Tanggal harus menggunakan format {0}
businesslogic.user.usernamenotmatch = Nama user tidak cocok
businesslogic.user.phonenocannotbeempty = Nomor handphone tidak boleh kosong
businesslogic.user.emailcannotbeempty = Email tidak boleh kosong
businesslogic.user.userwithphoneandemailnotfound = User dengan nomor handphone {0} dan email {1} tidak ditemukan di sistem
businesslogic.user.nikregistered = NIK sudah terdaftar
businesslogic.user.userwithemailnotregisteredwithphone = User dengan email {0} tidak terdaftar dengan nomor handphone {1}
businesslogic.user.userwithphonenotregisteredwithemail = User dengan nomor handphone {0} tidak terdaftar dengan email {1}
businesslogic.user.userwithemailhasnotactivated = User dengan email {0} belum aktivasi
businesslogic.user.updatesuccess = Data user berhasil diperbarui
businesslogic.user.invalidusertype = Tipe User {0} tidak valid
businesslogic.user.facenotdetected = Wajah tidak terdeteksi. Pastikan wajah Anda terlihat dengan jelas
businesslogic.user.morethanonefacedetected = Lebih dari satu wajah terdeteksi. Pastikan hanya satu wajah yang terlihat
businesslogic.user.faceoutofposition = Wajah terdeteksi berada di luar frame. Pastikan wajah Anda berada di dalam frame dengan benar
businesslogic.user.facetooclose = Wajah terdeteksi terlalu dekat dengan kamera. Jauhkan jarak antara wajah Anda dengan kamera
businesslogic.user.facetoofar = Wajah terdeteksi terlalu jauh dengan kamera. Dekatkan jarak antara wajah Anda dengan kamera
businesslogic.user.eyeglassesdetected = Terdeteksi kacamata. Mohon untuk melepas kacamata Anda dan pastikan wajah terlihat tanpa kacamata
businesslogic.user.facetoobright = Wajah terlalu terang. Pastikan kondisi pencahayaan yang memadai dan hindari cahaya yang berlebihan pada wajah Anda
businesslogic.user.backlightdetected = Terdeteksi cahaya terang di belakang. Mohon ambil ulang foto tanpa ada cahaya terang di belakang wajah.
businesslogic.user.facetoodark = Wajah terlalu gelap. Pastikan kondisi pencahayaan yang cukup terang agar wajah Anda terlihat dengan jelas
businesslogic.user.maskingfailed = Wajah terdeteksi berada di luar frame. Pastikan wajah Anda berada di dalam frame dengan benar
businesslogic.user.dontneedactivate = User tidak perlu melakukan aktivasi lagi untuk vendor {0}.
businesslogic.user.requestotp = User harus melakukan request OTP terlebih dahulu.
businesslogic.user.notactivated = User {0} perlu melakukan aktivasi.
businesslogic.user.urlforwardercodeinvalid = Tautan tidak valid. Harap memastikan tautan yang dibuka sudah lengkap sesuai dengan data yang diterima.
businesslogic.user.urlforwarderexpired = Tautan sudah kedaluwarsa.
businesslogic.user.invitationnotsentviawa = Pengiriman ulang undangan dengan WhatsApp gagal dilakukan.
businesslogic.user.resetcodenotrequested = User harus melakukan request Reset Code terlebih dahulu.
businesslogic.user.otpsendingmedianotvalid = Media Pengiriman OTP tidak Valid.
businesslogic.user.userregisterwithoutemail = Pengguna melakukan registrasi tanpa email, harap melakukan pengiriman otp melalui SMS atau Whatsapp.
businesslogic.user.usercertificateactivestatusexpired = Sertifikat elektronik anda untuk PSrE {0} sudah kadaluarsa, harap lakukan registrasi ulang.
businesslogic.user.notifsendingmedianotvalid = Media Pengiriman notifikasi tidak valid.
businesslogic.user.notifsendingmediaandnotifgatewaynotvalid = Media pengiriman notifikasi dan gateway notifikasi tidak valid.

businesslogic.userval.emailusedbymany = Email {0} digunakan oleh {1} pengguna berbeda
businesslogic.userval.phoneusedbymany = No HP {0} digunakan oleh {1} pengguna berbeda
businesslogic.userval.emailnotfound = Email {0} tidak ditemukan di sistem
businesslogic.userval.phonenotfound = Phone {0} tidak ditemukan di sistem
businesslogic.userval.niknotfound = NIK {0} tidak ditemukan di sistem
businesslogic.userval.phoneandemailusedbymany = Nomor handphone {0} dan email {1} digunakan oleh {2} pengguna berbeda
businesslogic.userval.phoneandemailnotfound = Nomor handphone {0} dan email {1} tidak ditemukan di sistem

businesslogic.inquiry.downloadPdfZip = Request download detail task has been process. Please check Report Result List menu
businesslogic.inquiry.noData = There is no data to download!

businesslogic.feedback.invalidvalue = Feedback Value harus bernilai antara 1 dan 5
businesslogic.feedback.invaliddocid = Dokumen dengan Document ID {0} tidak ada

businesslogic.saldo.invalidtenant = Tidak bisa request saldo tenant lain
businesslogic.saldo.invalidrole = Hanya bisa diakses oleh Admin.
businesslogic.saldo.tenantnotexist = Tidak ada Tenant dengan code {0}
businesslogic.saldo.vendornotexist = Tidak ada Vendor dengan code {0}
businesslogic.saldo.topupdatenotexist = Tidak ada tanggal Top Up dengan tanggal {0}
businesslogic.saldo.cannottopup = Tidak bisa melakukan top up untuk balance type SDT
businesslogic.saldo.vendortypeinvalid = Vendor dengan Tipe Vendor E-Materai Tidak bisa melakukan top up
businesslogic.saldo.pleasechoosebalancetype = Mohon pilih tipe saldo terlebih dahulu
businesslogic.saldo.balancetypealreadyexist = Balance Type dengan code {0} sudah terdaftar
businesslogic.saldo.balancetypenotfound = Balance Type dengan code {0} tidak ditemukan
businesslogic.saldo.balancemutationnotfound = Balance Mutation dengan id {0} tidak ditemukan
businesslogic.saldo.idbalancemutationinvalid = Data Id Balance Mutation tidak valid
businesslogic.saldo.balancenotenough = Saldo {0} tidak cukup
businesslogic.saldo.balancesarenotenough = Saldo {0} dan Saldo {1} tidak cukup
businesslogic.saldo.balancenotconfigured = Saldo {0} belum dikonfigurasi untuk {1}
businesslogic.saldo.successedit = Saldo berhasil diubah

businesslogic.tenant.invalidtenant = Tidak bisa request tenant lain
businesslogic.tenant.faceverifyserviceinactive = Face Verify untuk tenant ini tidak aktif
businesslogic.tenant.faceverifyserviceerrorselfiespoof = Kami belum dapat mendeteksi wajah anda, silahkan diulangi kembali dengan pencahayaan yang lebih baik
businesslogic.tenant.invalidthresholdbalance = Batas saldo harus > 0
businesslogic.tenant.apikeyvalidationerror = Error validasi tenant x-api-key
businesslogic.tenant.apikeyinvalidforagreement = Tenant dari kontrak berbeda dengan tenant dari x-api-key
businesslogic.tenant.apikeyinvalidforagreement = Tenant dari dokumen berbeda dengan tenant dari x-api-key
businesslogic.tenant.tenantnotfound = Tenant {0} tidak terdaftar di eSignHub
businesslogic.tenant.tenantcodeempty = Tenant code tidak boleh kosong
businesslogic.tenant.tenantlivenessfacecompareservicesnotactive = Servis liveness face compare untuk tenant {0} tidak aktif
businesslogic.tenant.tenantwithtenantcodenotregistered = Tenant dengan tenant code {0} tidak terdaftar di eSignHub
businesslogic.tenant.incorrectapikey = API Key salah
businesslogic.tenant.invalidcallbackurl = URL Callback tidak valid. Harus diawali dengan https:// atau http://
businesslogic.tenant.invaliduploadurl = URL Upload tidak valid. Harus diawali dengan https:// atau http://
businesslogic.tenant.invalidusewamessage = Value Use WA Message harus 0 atau 1
businesslogic.tenant.invalidredirectactivationurl = URL Redirect Aktivasi tidak valid. Harus diawali dengan https:// atau http://
businesslogic.tenant.invalidredirectsigningurl = URL Redirect Tanda Tangan tidak valid. Harus diawali dengan https:// atau http://
businesslogic.tenant.msgtemplatewithotpactiveduration = Message Template tidak ditemukan untuk durasi OTP aktif : {0}
businessLogic.tenant.invalidregion = Tidak ada region dengan code {0}
businessLogic.tenant.invalidbusinessline = Tidak ada lini bisnis dengan code {0}

businesslogic.vendor.emptyvendortypecode = Vendor type code tidak boleh kosong
businesslogic.vendor.invalidnotificationgateway = Notification Gateway tidak ditemukan
businesslogic.vendor.emptytenantcode = Tenant code tidak boleh kosong
businesslogic.vendor.invalidtenantcode = Tenant tidak ditemukan
businesslogic.vendor.usertenantnotfound = User {0} tidak bisa ditemukan in tenant {1}
businesslogic.vendor.ematerainotallowed = Tidak bisa melakukan request untuk Vendor yang memiliki Tipe Vendor 'E-Materai'
businesslogic.vendor.invalidtenant = Tidak bisa request status Vendor milik Tenant lain
businesslogic.vendor.invalidvendortype = Tipe vendor {0} tidak valid
businesslogic.vendor.defaultvendornotfound = Default vendor untuk tenant {0} tidak ditemukan
businesslogic.vendor.vendornotexistoractiveintenant = Psre {0} tidak dimiliki atau terdaftar sebagai tenant aktif di tenant {1}
businesslogic.vendor.vendornotactive = Psre {0} tidak aktif.
businesslogic.vendor.vendornotoperating = Psre {0} tidak beroperasi.
businesslogic.vendor.registeredusernotfound=Tidak Ditemukan Vendor Registered User
businesslogic.vendor.vendorcodeinvalid = Vendor code {0} tidak valid
businesslogic.vendor.vendorcannotresendactlink = Vendor code {0} tidak dapat kirim ulang link aktivasi
businesslogic.vendor.vendorcodeinvlink  = Vendor {0} berbeda dengan vendor invitation link
businesslogic.vendor.registeredinvendor = Anda sudah terdaftar di PSrE {0}
businesslogic.vendor.unhandledvendorcode = Unhandled vendor code: {0}
businesslogic.vendor.cannotupdatevendor = Data user vendor tidak dapat di update
businesslogic.vendor.mvrunull = Data user dengan vendor {0} tidak dapat ditemukan
businesslogic.vendor.cannotsendphone = vendor {0} tidak dapat mengirim OTP ke nomor hp
businesslogic.vendor.registereduserv2 = User {0} terdaftar di PSrE {1}
businesslogic.vendor.balancvendoroftenantnotfound = Saldo {0} dari {1} untuk Tenan {2} tidan ditemukan
businesslogic.vendor.vendorregisteredusernotfound = User tidak ditemukan di Vendor Registered User
businesslogic.vendor.vendorcodepayment  = Vendor {0} berbeda dengan vendor DocumentId
businesslogic.vendor.unhandledregistrationvendor = Tidak bisa melakukan registrasi {0}
businesslogic.vendor.defaultvendornull = Vendor {0} bukan default vendor di tenant {1}
businesslogic.vendor.defaultvendormustunique = Urutan default vendor harus unik
businesslogic.vendor.vendornotsupporthashsign = Tidak dapat melakukan Hash Sign untuk PSrE {0}
businesslogic.vendor.vendornotsupportdownloadcert = Tidak dapat melakukan Download Certificate untuk PSrE {0}
businesslogic.vendor.emptypsrelist = List PsrE kosong;

businesslogic.email.couldnotconnecttostore = Tidak bisa terhubung ke message store

businesslogic.sms.sendsmserror = Error ketika kirim SMS

businesslogic.liveness.emptyphotobase64 = PhotoBase64 tidak boleh kosong
businesslogic.liveness.emptyselfiebase64 = Swafoto tidak boleh kosong
businesslogic.liveness.emptyidphotobase64 = Foto KTP tidak boleh kosong
businesslogic.liveness.emptyselfiedbbase64 = Swafoto dari Basis Data kosong

businesslogic.embedmsg.invalidmsg = Msg tidak valid
businesslogic.embedmsg.emptymsg = Pesan terenkripsi kosong
businesslogic.embedmsg.invalidencryptedobject = Data terenkripsi yang dikirim tidak valid
businesslogic.embedmsg.datanotfoundinencryptedobject = Data {0} tidak ditemukan di data terenkripsi
businesslogic.embedmsg.invalidencryptedocument = Dokumen Id yang di kirim tidak valid
businesslogic.embedmsg.sessionexpired = Halaman yang Anda buka sudah kadaluwarsa. Untuk melanjutkan menggunakan layanan kami, silakan buka kembali menu ini.

businesslogic.invitationlink.phonenull = Nomor telepon harus diisi
businesslogic.invitationlink.processonlyforinvitationbyemail = {0} dibatalkan, hanya untuk invitation by Email
businesslogic.invitationlink.invitationlinknotexist = Tidak ada Invitation Link dengan code {0}
businesslogic.invitationlink.cannotsendotptoemail = Tidak bisa mengirimkan OTP ke {0}
businesslogic.invitationlink.cannotverifyotptoemail = Tidak bisa memverifikasi OTP ke {0}
businesslogic.invitationlink.invalidphoneno = Nomor telepon {0} tidak valid. Pastikan sudah memasukkan nomor telepon yang benar.
businesslogic.invitationlink.invalidemail = E-mail {0} tidak valid. Pastikan sudah memasukkan e-mail yang benar.
businesslogic.invitationlink.inactivelink = Link undangan sudah tidak aktif.
businesslogic.invitationlink.invalidlink = Link registrasi tidak valid.
businesslogic.invitationlink.useralreadyregistered = User {0} sudah terdaftar. Silahkan menunggu link permintaan tanda tangan.
businesslogic.invitationlink.invalidinvitationby = Invalid invitation method
businesslogic.invitationlink.invitationsent = Undangan terkirim ke {0}
businesslogic.invitationlink.invitationdatainvalid = Data penerima undangan tidak valid
businesslogic.invitationlink.invalidreceiverdetail = Receiver detail harus diisi dengan {0}
businesslogic.invitationlink.invalidexcel = Anda tidak mempunyai data untuk report excel
businesslogic.invitationlink.invitationlinknotexistwithdata = Tidak ada Invitation Link yang cocok dengan data yang disediakan.
businesslogic.invitationlink.existingphone = Nomor telp. ini {0} sudah digunakan di link undangan dengan NIK berbeda
businesslogic.invitationlink.existingemail = Email ini {0} sudah digunakan di link undangan dengan NIK berbeda
businesslogic.invitationlink.existingidno = NIK ini {0} sudah terdaftar
businesslogic.invitationlink.mismatchreceiverdetail = {0} harus sama dengan {0} pada Receiver Detail
businesslogic.invitationlink.phoneusedinemail = No HP {0} sudah digunakan untuk email {1}
businesslogic.invitationlink.emailusedinnik = Email {0} sudah digunakan untuk NIK {1}
businesslogic.invitationlink.emailnikusedinphone = Email {0} dan NIK {1} sudah digunakan untuk no HP {2}
businesslogic.invitationlink.emailphoneusedinnik = Email {0} dan No HP {1} sudah digunakan untuk NIK {2}
businesslogic.invitationlink.decrypterror = Link terpotong pada saat copy dari eSignHub. Mohon copy link dengan benar dan lengkap. Silahkan coba kembali.
businesslogic.invitationlink.cannotEditUser = Data pengguna yang terdaftar pada vendor {0} tidak bisa diubah.
businesslogic.invitationlink.existingphoneotherlink = Nomor telephone {0} sudah digunakan oleh link undangan lain
businesslogic.invitationlink.existingemailotherlink = Email {0}  sudah digunakan oleh link undangan lain
businesslogic.invitationlink.existingidnootherlink = Nomor id {0}  sudah digunakan oleh link undangan lain
businesslogic.invitationlink.registeredtomainvendor = Pengguna {0} sudah terdaftar pada vendor utama. Silahkan gunakan akun yang sudah terdaftar.
businesslogic.invitationlink.invitationexpired = Undangan registrasi Anda sudah kadaluwarsa. Harap lakukan pembuatan ulang undangan registrasi untuk melanjutkan proses registrasi.
businesslogic.invitationlink.cannotregenerate = Tidak bisa generate ulang invitation link. User sudah register.
businesslogic.invitationlink.cannotregeneratelinkexisted = Tidak bisa generate ulang invitation link. Sudah ada link registrasi dengan {0}.
businesslogic.invitationlink.cannotregenerateforvendor = Tidak bisa generate ulang invitation link dengan {0}.
businesslogic.invitationlink.cannotuseemailforinvbysms = Email tidak bisa digunakan untuk Invitation by SMS
businesslogic.invitationlink.failedtoresend = Link undangan gagal dikirim ulang
businesslogic.invitationlink.phoneoremailmustbefilled = Nomor Telepon atau Email harus diisi
businesslogic.invitationlink.genonlyforvendor = Link undangan hanya bisa dibuat untuk PSrE {0}
businesslogic.invitationlink.activeecert = Sertifikat Elektronik milik {0} masih aktif
businesslogic.invitationlink.errorhappened = Telah terjadi error. Harap periksa kembali data yang dikirim.

businesslogic.register.alreadyregistered = Anda sudah pernah terdaftar di eSignHub
businesslogic.register.nikusedbyotherphone = NIK sudah digunakan oleh no telp yang berbeda dari data yang dikirim
businesslogic.register.nikusedbyotheremail = NIK sudah digunakan oleh email yang berbeda dari data yang dikirim
businesslogic.register.nikusedbyotherphoneemail = NIK sudah digunakan oleh no telp dan email yang berbeda dari data yang dikirim
businesslogic.register.phoneusedbyothernik = No Telp sudah digunakan oleh NIK yang berbeda dari data yang dikirim
businesslogic.register.phoneusedbyothernikemail = No Telp sudah digunakan oleh NIK dan email yang berbeda dari data yang dikirim
businesslogic.register.emailusedbyothernik = Email sudah digunakan oleh NIK yang berbeda dari data yang dikirim
businesslogic.register.emailusedbyothernikphone = Email sudah digunakan oleh NIK dan no telp yang berbeda dari data yang dikirim
businesslogic.register.phoneemailusedbyothernik = No Telp dan Email sudah digunakan oleh NIK yang berbeda dari data yang dikirim
businesslogic.register.phonenikusedbyotheremail = No Telp dan NIK sudah digunakan oleh Email yang berbeda dari data yang dikirim
businesslogic.register.emailnikusedbyotherphone = Email dan NIK sudah digunakan oleh No Telp yang berbeda dari data yang dikirim
businesslogic.register.phonenotbelongtonik = No Telp bukan milik NIK {0}
businesslogic.register.emailnotbelongtonik = Email bukan milik NIK {0}
businesslogic.register.phoneemailnotbelongtonik = No Telp dan email bukan milik NIK {0}
businesslogic.register.verificationinprogress = Registrasi Anda sedang dalam proses verifikasi
businesslogic.register.maxattemptsreached = Percobaan registrasi Anda sudah mencapai batas harian maksimum ({0} kali). Mohon mencoba registrasi kembali besok.

businesslogic.digisign.registered = Anda sudah terdaftar ke Digisign sebelumnya. Silahkan menunggu link permintaan tanda tangan dikirimkan.
businesslogic.digisign.registerednotactivated = Anda sudah terdaftar. Silahkan lanjutkan ke proses aktivasi.
businesslogic.digisign.errorprocessingselfie = Error ketika memproses foto selfie
businesslogic.digisign.errorprocessingktp = Error ketika memproses foto KTP
businesslogic.digisign.connectiontimeout = Terdapat gangguan server, Silahkan hubungi penyedia jasa layanan
businesslogic.digisign.readtimeout		= Terdapat gangguan server, Silahkan hubungi penyedia jasa layanan

businesslogic.tekenaja.errorprocessingselfie = Error ketika memproses foto selfie
businesslogic.tekenaja.errorprocessingktp = Error ketika memproses foto KTP
businesslogic.tekenaja.waitforsign = Anda sudah terdaftar di TekenAja. Silahkan menunggu link permintaan tanda tangan dikirim.
businesslogic.tekenaja.pleaseverifyemail = Anda sudah terdaftar di TekenAja. Silakan verifikasi melalui email yang telah dikirim.
businesslogic.tekenaja.errordownloadcert = Error {0}
businesslogic.tekenaja.certificaterenewed = Sertifikat telah diperbarui

businesslogic.tekenaja.hashsign.sentotpfailed = Sent OTP Gagal
businesslogic.tekenaja.hashsign.userhasnotregistered = Pengguna belum melakukan registrasi
businesslogic.tekenaja.hashsign.failedsign = Hash Sign Gagal
businesslogic.tekenaja.hashsign.invalidotp = Kode OTP tidak valid
businesslogic.tekenaja.hashsign.otpexpired = Kode OTP sudah kadaluarsa
businesslogic.tekenaja.hashsign.systemfailure = Mohon maaf atas ketidaknyamanannya, mohon coba beberapa saat lagi

businesslogic.vida.livenessfailed = Verifikasi Liveness gagal. Harap mengambil Foto Selfie langsung. Pastikan wajah anda terlihat jelas tidak tertutup oleh aksesoris.
businesslogic.vida.selfphotonotfound = Self photo tidak ditemukan di sistem
businesslogic.vida.selfphotonotfound = Self photo tidak ditemukan di sistem

businesslogic.privy.prifyidnotfound = Privy ID tidak ditemukan di sistem
businesslogic.privy.tokennotfound = Token tidak ditemukan di sistem
businesslogic.privy.failedtogetlivenessurl = Gagal mendapatkan URL Liveness
businesslogic.privy.livenessnotconfigured = Tenant Setting {0} belum diconfigurasi untuk tenant {1}.

businesslogic.privy.general.failedtogettoken = Gagal mengambil token
businesslogic.privy.general.failedtogeneratesignature = Gagal menghasilkan signature Privy

businesslogic.document.archived.restoring = Dokumen {0} diarsipkan dan sedang dipulihkan. Silakan coba lagi dalam 1 menit.
businesslogic.document.restoring = Dokumen {0} sedang dipulihkan. Silakan coba lagi dalam 1 menit.
businesslogic.document.archived.restore.error = Terjadi kesalahan saat memulihkan dokumen {0}: {1}. Silakan coba lagi nanti.
businesslogic.document.archived.check.error = Terjadi kesalahan saat memeriksa status pemulihan dokumen {0}: {1}. Silakan coba lagi nanti.
businesslogic.document.archived.not.signed.or.stamped = Dokumen {0} harus ditandatangani atau distempel sebelum dapat dipulihkan.

businesslogic.job.emptyjobtype = Job type tidak boleh kosong
businesslogic.job.emptyidjobresult = Id job result tidak boleh kosong

businesslogic.errorhistory.notfound = Error history tidak ditemukan
businesslogic.errorhistory.customer = Customer
businesslogic.errorhistory.spouse = Spouse
businesslogic.errorhistory.guarantor = Guarantor

businesslogic.province.notfound = Provinsi dengan ID {0} tidak tercatat di sistem
businesslogic.province.provincewithnamenotfound = Provinsi dengan nama {0} tidak ditemukan di sistem

businesslogic.district.notfound = Kota dengan ID {0} tidak tercatat di sistem
businesslogic.district.invalidprovince = {0} tidak terletak di Provinsi {1}
businesslogic.district.districtwithnamenotfound = Kota dengan nama {0} tidak ditemukan di sistem

businesslogic.subdistrict.notfound = Kecamatan dengan ID {0} tidak tercatat di sistem
businesslogic.subdistrict.invaliddistrict = Kecamatan {0} tidak terdapat di {1}

businesslogic.external.emailnotfound = User dengan email {0} tidak ditemukan
businesslogic.external.emailempty = email tidak boleh kosong
businesslogic.external.idnoinvalid = Panjang NIK harus 16 digit
businesslogic.external.idnumber = NIK harus berupa angka
businesslogic.external.idempty = NIK tidak boleh kosong
businesslogic.external.phonenumber = No Handphone harus angka
businesslogic.external.phoneempty = No Handphone tidak boleh kosong
businesslogic.external.emailinvalid = Format <NAME_EMAIL>
businesslogic.external.datatypeinvalid = Data type tidak valid
businesslogic.external.idnotfound = User dengan NIK {0} tidak ditemukan
businesslogic.external.phonenotfound = User dengan nomor telepon {0} tidak di temukan
businesslogic.external.invaliddateformat = {0} harus menggunakan format tanggal {1}
businesslogic.external.failedprocessingphoto = Gagal memproses {0}. Mohon ambil foto ulang.
businesslogic.external.tknajaemailservicezero = Link aktivasi sudah dikirimkan ke email pengguna, harap cek email yang terdaftar untuk melanjutkan proses aktivasi.
businesslogic.external.tknajaemailserviceone = Link aktivasi sudah dikirimkan ke nomor telepon pengguna, harap cek SMS yang diterima untuk melanjutkan proses aktivasi.
businesslogic.external.pemungutaccnotavailable = Akun Pemungut tidak tersedia. Harap melakukan permintaan Stamping dengan tipe pajak Non-Pemungut.

businesslogic.embedwebview.decrypterror = Link yang dibuka tidak lengkap. Mohon copy link dengan lengkap.
businesslogic.embedwebview.signlinkrequestnotfound = Permintaan link tanda tangan tidak ditemukan

businesslogic.message.datestartgreaterthandateend = {0} harus lebih besar dari {1}

businesslogic.autosign.base64notvalid = File base64 tidak valid;
businesslogic.autosign.executiondatenotvalid = Execution date tidak valid
businesslogic.autosign.emptyfilename = Filename tidak boleh kosong
businesslogic.autosign.emptybase64 = base64 tidak boleh kosong
businesslogic.autosign.emptyexecutiontime = Execution time tidak boleh kosong;
businesslogic.autosign.invaliddaterange = Range maksimum tanggal {0} adalah 30 hari
bussinesslogic.autosign.headernotfound = Header data import tidak ditemukan
businesslogic.autosign.invalidstatusimport = Status Import tidak valid
businesslogic.autosign.emptytemplatefile = File template import autosign bm tidak dapat ditemukan

businesslogic.tenantsettings.tenantsettingsnotfound = Tenant Setting tidak dapat ditemukan
businesslogic.tenantsettings.tenantsettingsnotfoundfortenant = Setting {0} tidak ditemukan untuk tenant {1}
businesslogic.tenantsettings.settingvalueempty = Nilai Setting untuk tipe {0} kosong

businesslogic.role.successinput = Role berhasil disimpan
businesslogic.role.successedit = Role berhasil diubah
businesslogic.role.rolewithnamealreadyexist = Role dengan nama {0} sudah ada di sistem
businesslogic.role.rolename = Nama Role
businesslogic.role.admesignnotallowed = Role dengan nama Admin Esign atau kode ADMESIGN tidak diperbolehkan

businesslogic.menu.menunotactive = Menu {0} tidak aktif
businesslogic.menu.menunotmanageable = Menu {0} tidak bisa diberikan ke role
businesslogic.menu.pathempty = Path menu {0} kosong
businesslogic.menu.menunotfound = Menu dengan kode {0} tidak ditemukan pada sistem

scheduler.failed.initialization = Failed upon initializing scheduler job

endpoint.deprecated = Endpoint ini sudah tidak bisa digunakan

vfirst.52992 = Username / Password salah
vfirst.57089 = Kontrak sudah kedaluwarsa
vfirst.57090 = User Credit sudah kedaluwarsa
vfirst.57091 = User tidak aktif
vfirst.65280 = Layanan tidak tersedia sementara
vfirst.65535 = Pesan yang dikirim tidak sesuai dengan DTD
vfirst.28673 = Nomor tujuan tidak numerik
vfirst.28674 = Nomor tujuan kosong
vfirst.28675 = Alamat tujuan kosong
vfirst.28676 = Templat tidak cocok
vfirst.28677 = UDH tidak sah/pesan SPAM
vfirst.28678 = Coding tidak sah
vfirst.28679 = Pesan SMS kosong
vfirst.28680 = ID pengirim tidak sah
vfirst.28681 = Pengiriman SMS yang sama sudah dilakukan, harap tunggu beberapa saat untuk melakukan permintaan pengiriman yang sama
vfirst.28682 = ID penerima tidak sah
vfirst.408   = Request Timed Out