package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmGeneralsetting;
import com.adins.esign.dataaccess.api.CommonDao;
import com.adins.esign.model.MsPeruriDocType;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.DocumentEMateraiTypeBean;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class CommonDaoHbn extends BaseDaoHbn implements CommonDao{

	@Override
	public String getUuidDao() {
		return this.getManagerDAO().getUUID();
	}

	@Override
	public long nextSequenceTrBalanceMutationTrxNo() {
		BigInteger result = (BigInteger) this.managerDAO.selectOneNativeString("select nextval('sq_trbalancemutation_trxno')", null);
		return result.longValue();
	}

	@Override
	public AmGeneralsetting getGeneralSetting(String code) {
		return this.managerDAO.selectOne(AmGeneralsetting.class, new Object[][] {{Restrictions.eq("gsCode", code)}});
	}

	@Override
	public AmGeneralsetting getGeneralSettingByTenant(String code, MsTenant tenant) {
		return this.managerDAO.selectOne(AmGeneralsetting.class, new Object[][] {
			{Restrictions.eq("gsCode", code)},
			{Restrictions.eq("msTenant", tenant)}
		});
	}

	@Override
	public void updateGeneralSetting(AmGeneralsetting generalSetting) {
		generalSetting.setUsrUpd(MssTool.maskData(generalSetting.getUsrUpd()));
		this.managerDAO.update(generalSetting);		
	}

	@Override
	public void insertMsPeruriDocType(MsPeruriDocType msPeruriDocType) {
		msPeruriDocType.setUsrCrt(MssTool.maskData(msPeruriDocType.getUsrCrt()));
		this.managerDAO.insert(msPeruriDocType);
	}

	@Override
	public void updateMsPeruriDocType(MsPeruriDocType msPeruriDocType) {
		msPeruriDocType.setUsrUpd(MssTool.maskData(msPeruriDocType.getUsrUpd()));
		this.managerDAO.update(msPeruriDocType);
	}

	@Override
	public MsPeruriDocType getMsPeruriDocTypeByDocId(String peruriDocId) {
		if (peruriDocId == null) {
			return null;
		}
		return this.managerDAO.selectOne(MsPeruriDocType.class,
				new Object[][] {{ Restrictions.eq(MsPeruriDocType.PERURI_DOC_ID_HBM, peruriDocId) }});
	}

	@Override
	public List<DocumentEMateraiTypeBean> getListDocumentEMateraiType(String isActive) {
		Map<String, Object> params = new HashMap<>();
		params.put("isActive", isActive);
		StringBuilder query = new StringBuilder();
		query.append(" SELECT doc_code AS \"documentCode\", ")
		.append(" doc_name AS \"documentName\", ")
		.append(" CAST(peruri_doc_id AS VARCHAR) AS \"peruriDocId\" ")
		.append(" FROM ms_peruri_doc_type WHERE is_active = :isActive ");
		
		return this.managerDAO.selectForListString(DocumentEMateraiTypeBean.class, query.toString(), params, null);
	}

	@Override
	public void deactiveDocumentNotInDocId(List<String> docId, String usrUpd) {
		Map<String, Object> params = new HashMap<>();
		params.put("usrUpd", MssTool.maskData(usrUpd));
		params.put("docId", docId);

		StringBuilder query = new StringBuilder();
		query.append(" UPDATE ms_peruri_doc_type SET is_Active = '0', dtm_upd = NOW(), usr_upd = :usrUpd WHERE peruri_doc_id NOT IN :docId ");
		
		managerDAO.updateNativeString(query.toString(), params);
	}
	
}
