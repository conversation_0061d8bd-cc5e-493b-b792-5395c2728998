package com.adins.esign.businesslogic.impl.embed;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.embed.SaldoEmbedLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.esign.webservices.model.embed.SignBalanceAvailabilityEmbedRequest;
import com.adins.esign.webservices.model.embed.SignBalanceAvailabilityEmbedResponse;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.EmbedMsgException;
import com.adins.exceptions.LovException;
import com.adins.exceptions.PaymentSignTypeException;
import com.adins.exceptions.SaldoException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.EmbedMsgException.ReasonEmbedMsg;
import com.adins.exceptions.LovException.Reason;
import com.adins.exceptions.PaymentSignTypeException.ReasonPaymentSignType;
import com.adins.exceptions.SaldoException.ReasonSaldo;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Component
public class GenericSaldoEmbedLogic extends BaseLogic implements SaldoEmbedLogic{

	@Autowired private CommonLogic commonLogic;
	@Autowired private EmbedValidatorLogic embedValidatorLogic;
	@Autowired private VendorValidatorLogic vendorValidatorLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	
	private void tenantVendorValidation(String tenantCode, String vendorCode, MsTenant tenant, MsVendor vendor, 
			AuditContext audit) {
		if (StringUtils.isBlank(tenantCode) || null == tenant) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {MsTenant.TENANT_CODE_HBM}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_CODE_EMPTY);
		}
		if (StringUtils.isBlank(vendorCode) || null == vendor) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {MsVendor.VENDOR_CODE_HBM}, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_CODE_INVALID);
		}
	}
	
	@Override
	public SignBalanceAvailabilityEmbedResponse getSignBalanceAvailabilityEmbed(SignBalanceAvailabilityEmbedRequest request, AuditContext audit) {
		
		String validationMessage = "";
		
		EmbedMsgBeanV2 msg = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(),request.getTenantCode(), true, audit);
		MsVendor vendor = vendorValidatorLogic.validateGetVendor(request.getVendorCode(), true, audit);
		
		this.tenantVendorValidation(request.getTenantCode(), request.getVendorCode(), msg.getMsTenant(), vendor, audit);

		List<String> listDocIdEncrypt = request.getListDocumentId();
		List<String> listDocIdDecrypt = new ArrayList<>();
		String[] listDocumentId = listDocIdEncrypt.toArray(new String[listDocIdEncrypt.size()]);
		
		for (int i = 0; i<listDocIdEncrypt.size(); i++) {
			try {
				String documentId = commonLogic.decryptMessageToString(listDocumentId[i], msg.getMsTenant().getAesEncryptKey(), audit);
				listDocIdDecrypt.add(documentId);
			} catch (Exception e) {
				throw new EmbedMsgException(getMessage("businesslogic.embedmsg.invalidencryptedocument", null, audit), e, ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
			}
		}
			
		String[] listDocumentIdDecrypt = listDocIdDecrypt.toArray(new String[listDocIdDecrypt.size()]);

		
		for(int i = 0; i<listDocIdDecrypt.size(); i++) {
			validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit);
			commonValidatorLogic.validateNotNull(listDocumentIdDecrypt[i], validationMessage, StatusCode.EMPTY_DOCUMENT_ID);
	
			
			TrDocumentD trDocumentD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listDocumentIdDecrypt[i]);
			validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_FOUND, new Object[] {listDocumentIdDecrypt[i]}, audit);
			commonValidatorLogic.validateNotNull(trDocumentD, validationMessage, StatusCode.DOCUMENT_NOT_FOUND);

		}
		
		List<BigInteger> idUser = daoFactory.getBalanceMutationDao().getIdUserByListDoc(listDocumentIdDecrypt);
		if (!idUser.contains(BigInteger.valueOf(msg.getAmMsuser().getIdMsUser()))) {
			throw new UserException(getMessage("businesslogic.user.usernotsigner", new Object[] {msg.getAmMsuser().getLoginId()}, audit),
					ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		
		
		
		String paymentSignType = daoFactory.getBalanceMutationDao().getPaymentSignType(listDocumentIdDecrypt);
		String paymentName = null;
		
		if (paymentSignType.equals("TTD")) {
			paymentSignType = "SGN";
			paymentName = "Sign";
		} else if (paymentSignType.equals("DOC")) {
			paymentSignType = "DOC";
			paymentName = "Document";
		} 
		
		
		MsLov balanceTypeLov = commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, paymentSignType, audit);
		
		
		validationMessage = getMessage("service.global.lovnotvalid", new Object[] {balanceTypeLov}, audit);
		commonValidatorLogic.validateNotNull(balanceTypeLov, validationMessage, StatusCode.LOV_CODE_INVALID);
		
	
		
		
		BigInteger balance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(msg.getMsTenant(), vendor, balanceTypeLov);
		BigInteger paymentDoc = daoFactory.getBalanceMutationDao().getPaymentByDoc(listDocumentIdDecrypt);
		
		if (vendor.getIdMsVendor() != paymentDoc.longValue()) {
			throw new VendorException(messageSource.getMessage("businesslogic.vendor.vendorcodepayment", new Object[] {request.getVendorCode()}, this.retrieveLocaleAudit(audit)),
					ReasonVendor.VENDOR_CODE_INVALID);
		}
		
		
		validationMessage = getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_TYPE_NOT_FOUND, new Object[] {paymentSignType}, audit);
		commonValidatorLogic.validateNotNull(balance, validationMessage, StatusCode.PAYMENT_SIGN_TYPE_BALANCE_NULL);
	
		
		BigInteger total = null;
		
		if ("DOC".equals(paymentSignType)) {
			total = daoFactory.getDocumentDao().getDocNeeded(listDocumentIdDecrypt , msg.getAmMsuser());
		} else {
			total = daoFactory.getDocumentDao().getSignNeeded(listDocumentIdDecrypt ,msg.getAmMsuser());
		}
		
		if (balance.compareTo(total) < 0) {
			throw new SaldoException(getMessage("businesslogic.saldo.balancenotenough", new Object[] {paymentName}, audit), ReasonSaldo.BALANCE_NOT_ENOUGH);
		}
		
		Status status = new Status();
		status.setCode(0);
		status.setMessage("Success");
		SignBalanceAvailabilityEmbedResponse response = new SignBalanceAvailabilityEmbedResponse();
		response.setStatus(status);
		return response;
	}
	
	
	

}
