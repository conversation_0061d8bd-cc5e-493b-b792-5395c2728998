package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.interfacing.EmateraiLogic;
import com.adins.esign.businesslogic.api.interfacing.EmeteraiPajakkuLogic;
import com.adins.esign.businesslogic.api.interfacing.EmeteraiRkgLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class AttachMeteraiJob extends BaseLogic {
	private static final Logger LOG = LoggerFactory.getLogger(AttachMeteraiJob.class);
	private static final String SCHEDULER = "SCHEDULER";
	
	@Value("${e-meterai.pajakku.processnumber}") private String processNumber;
	
	@Autowired EmateraiLogic emateraiLogic;
	@Autowired EmeteraiRkgLogic emeteraiRkgLogic;
	@Autowired EmeteraiPajakkuLogic emeteraiPajakkuLogic;
	
//	public void runAttachMeterai() {
//		try {
//			LOG.info("Job Attach Meterai MCP Started");
//			AuditContext auditContext = new AuditContext(SCHEDULER);
//			List<TrDocumentH> listDocumentH = daoFactory.getDocumentDao().getListDocumentHeaderByProsesMeterai(
//					Short.valueOf(GlobalVal.STATUS_ATTACH_METERAI_ERROR));
//			for (TrDocumentH documentH : listDocumentH) {
//				emateraiLogic.attachMeterai(documentH, auditContext);
//			}
//			LOG.info("Job Attach Meterai MCP Finished");
//		} catch (Exception e) {
//			LOG.error("Error on running Job Attach Meterai MCP", e);
//		}
//	}
//	
//	public void runAttachMeteraiRkg() {
//		try {
//			LOG.info("Job Attach Meterai RKG Started");
//			AuditContext auditContext = new AuditContext(SCHEDULER);
//			List<TrDocumentH> listDocumentH = daoFactory.getDocumentDao().getListDocumentHeaderByProsesMeterai(
//					Short.valueOf(GlobalVal.STATUS_ATTACH_METERAI_ERROR));
//			for (TrDocumentH documentH : listDocumentH) {
//				emeteraiRkgLogic.attachMeteraiRkg(documentH, auditContext);
//			}
//			LOG.info("Job Attach Meterai RKG Finished");
//		} catch (Exception e) {
//			LOG.error("Error on running Job Attach Meterai RKG", e);
//		}
//	}
	
	public void runAttachMeteraiPajakku() {
		try {
			LOG.info("Job attach meterai {} Pajakku started", processNumber);
			AuditContext auditContext = new AuditContext(SCHEDULER);
			emeteraiPajakkuLogic.attachMeteraiPajakku(auditContext);
			LOG.info("Job attach meterai {} Pajakku finished", processNumber);
		} catch (Exception e) {
			LOG.error("Error on running Job attach meterai {} Pajakku", processNumber, e);
		}
	}
}
