package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class DuplicateRequestException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public DuplicateRequestException() {
		super();
	}

	public DuplicateRequestException(String message) {
		super(message);
	}

	@Override
	public int getErrorCode() {
		return StatusCode.DUPLICATE_REQUEST;
	}

}
