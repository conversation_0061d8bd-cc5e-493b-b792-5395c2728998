package com.adins.esign.businesslogic.api.interfacing;

import java.io.IOException;

import com.adins.esign.model.TrDocumentH;
import com.adins.esign.webservices.model.UpdateStampDutyStatusResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface EmeteraiRkgLogic {
	UpdateStampDutyStatusResponse attachMeteraiRkg(TrDocumentH documentH, AuditContext audit) throws IOException;
}
