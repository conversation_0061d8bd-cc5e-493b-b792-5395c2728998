package com.adins.esign.webservices.frontend.api;

import com.adins.esign.webservices.model.AddMenuOfRoleRequest;
import com.adins.esign.webservices.model.AddRoleManagementRequest;
import com.adins.esign.webservices.model.AddRoleRequest;
import com.adins.esign.webservices.model.EditRoleManagementRequest;
import com.adins.esign.webservices.model.GetListRoleManagementRequest;
import com.adins.esign.webservices.model.GetListRoleManagementResponse;
import com.adins.esign.webservices.model.GetListRoleRequest;
import com.adins.esign.webservices.model.GetListRoleResponse;
import com.adins.framework.service.base.model.MssResponseType;

public interface RoleService {
	MssResponseType addRole(AddRoleRequest request);
	MssResponseType addMenuOfRole(AddMenuOfRoleRequest request);
	GetListRoleResponse getListRole(GetListRoleRequest request);
	GetListRoleManagementResponse getListRoleManagement(GetListRoleManagementRequest request);
	MssResponseType addRoleManagement(AddRoleManagementRequest request);
	MssResponseType editRoleManagement(EditRoleManagementRequest request);
}
