package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class RegisterPrivyJob {
	private static final Logger LOG = LoggerFactory.getLogger(RegisterPrivyJob.class);
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void runCheckRegisterStat() {
		try {
			LOG.info("Check Register Status Privy job started");
			AuditContext audit = new AuditContext("CHECK REGIS STATUS SCHEDULER");
			schedulerLogic.registerPrivy(audit);
			LOG.info("Check Register Status Privy job finished");
		} catch (Exception e) {
			LOG.error("Error on Check Register Status Privy job", e);
		}
	}
}
