package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.BalanceMutationDao;
import com.adins.esign.dataaccess.factory.api.DaoFactory;
import com.adins.esign.model.<PERSON>Lov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.BalanceBean;
import com.adins.esign.model.custom.BalanceMutationBean;
import com.adins.esign.model.custom.BalanceMutationExternalBean;
import com.adins.esign.model.custom.CancelBalanceMutationBean;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class BalanceMutationDaoHbn extends BaseDaoHbn implements BalanceMutationDao {
	@Autowired DaoFactory daoFactory;
	
	private static final String PARAM_ID_TENANT = "idTenant";
	private static final String PARAM_ID_BALANCE_TYPE = "idBalanceType";

	@Override
	public void insertTrBalanceMutation(TrBalanceMutation balanceMutation) {
		balanceMutation.setUsrCrt(MssTool.maskData(balanceMutation.getUsrCrt()));
		this.managerDAO.insert(balanceMutation);
	}

	@Override
	public BigInteger getSingleBalanceByVendorAndTenant(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov) {
		Map<String, Object> params = new HashMap<>();
		params.put("date", new Date());
		params.put("balanceType", balanceTypeLov.getIdLov());
		params.put("vendorId", vendor.getIdMsVendor());
		params.put("tenantId", tenant.getIdMsTenant());
		
		StringBuilder query = new StringBuilder();
		query.append("with bdc as ( ")
			.append("     Select recap_date, recap_total_balance ")
			.append("     from   tr_balance_daily_recap bdc ")
			.append("     where  bdc.lov_balance_type = :balanceType ")
					.append( " and bdc.id_ms_tenant = :tenantId and bdc.id_ms_vendor = :vendorId")
					.append( " and bdc.recap_date <= :date ")
			.append("    order by bdc.recap_date desc limit 1 ")
			.append(" ) ")
			.append("select CAST(SUM(COALESCE(recap_total_balance,0) + COALESCE(number_of_use,0)) AS BIGINT)  ")
			.append("from bdc ")
			.append("left join lateral (Select SUM(qty) as number_of_use ")
			.append("                   from tr_balance_mutation bm")
			.append("                   where DATE(bm.trx_date)>bdc.recap_date ")
			.append("                     and bm.lov_balance_type = :balanceType ")
			.append("                     and bm.id_ms_tenant= :tenantId and bm.id_ms_vendor= :vendorId ")
			.append(") bm on TRUE ");
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		 
	}
	
	private String constructParamListBalanceMutation(Map<String, Object> params, String transactionType,
			Date dateStart, Date dateEnd, String documentType, String refNo, String documentName, String officeCode,String regionCode,String businessLineCode,
			String status) {
		StringBuilder query = new StringBuilder();

		if(StringUtils.isNotBlank(refNo)) {
			query.append("and upper(bm.ref_no) like :refNo ");
			params.put("refNo", "%" + StringUtils.upperCase(refNo) + "%");
		}
		if(StringUtils.isNotBlank(transactionType)) {
			query.append("and trxType.code = :transactionType ");
			params.put("transactionType", StringUtils.upperCase(transactionType));
		}
		if(StringUtils.isNotBlank(documentType)) {
			query.append("and mlovDocType.code = :documentType ");
			params.put("documentType", documentType);
		}
		if(StringUtils.isNotBlank(documentName)) {
			query.append("and (dt.doc_template_name like :documentName or (dt.id_doc_template is null and upper(docd.document_name) like :documentName)) ");
			params.put("documentName", "%" + StringUtils.upperCase(documentName) + "%");
		}
		if(StringUtils.isNotBlank(officeCode)) {
			query.append("and office.office_code= :officeCode ");
			params.put("officeCode", officeCode);
		}
		if(dateStart != null && dateEnd != null) {
			query.append("and DATE(bm.trx_date) >= :startDate ")
				 .append("and DATE(bm.trx_date) <= :endDate ");
			params.put("startDate", dateStart);
			params.put("endDate", dateEnd);
		}else {
			// ESH-925: Perbaikan default filter tanggal tidak menampilkan data di tanggal 1. Ditambah - interval '1 day'
			query.append("and DATE(bm.trx_date) >= date_trunc('MONTH', now()) - interval '1 day' ")
			 	 .append("and DATE(bm.trx_date) <= now() ");
			params.put("startDate", dateStart);
		}
		if(StringUtils.isNotBlank(status)) {
			if(status.equalsIgnoreCase(GlobalVal.BALANCE_MUTATION_STATUS_SUCCESS)) {
				query.append("and QTY != 0");
			}else if (status.equalsIgnoreCase(GlobalVal.BALANCE_MUTATION_STATUS_FAILED)){
				query.append("and QTY = 0");
			}
		} 
		if(StringUtils.isNotBlank(regionCode)) {
			query.append("and region.region_code= :regionCode ");
			params.put("regionCode", regionCode);
		}
		if(StringUtils.isNotBlank(businessLineCode)) {
			query.append("and businessLine.business_line_code= :businessLineCode ");
			params.put("businessLineCode", businessLineCode);
		}
		
		return query.toString();
	}
	@Override
	public List<BalanceMutationExternalBean> getListBalanceMutationExternal(MsTenant tenant,  MsLov balanceTypeLov,
			Date dateStart, Date dateEnd,  String officeCode,String regionCode,String businessLineCode) {
		
		String conditionSDT = "'" +  GlobalVal.CODE_LOV_BALANCE_TYPE_SDT + "','" +  GlobalVal.CODE_LOV_BALANCE_TYPE_SDT_POSTPAID + "'";
		
		Map<String, Object> params = new HashMap<>();
		params.put(PARAM_ID_TENANT, tenant.getIdMsTenant());
		params.put(PARAM_ID_BALANCE_TYPE, balanceTypeLov.getIdLov());
		params.put("regionCode",regionCode);
		params.put("businessLineCode",regionCode);
		
		String paramQueryListBalanceMutation = this.constructParamListBalanceMutation(params, null,
				dateStart, dateEnd, null, null, null, officeCode,regionCode,businessLineCode, null);
		StringBuilder query = new StringBuilder();

		query.append(" with bdc AS ( ")
				.append(" SELECT recap_date,  ")
				.append("        recap_total_balance ")
				.append(" FROM  tr_balance_daily_recap bdc ")
				.append(" WHERE 1=1 ")
				.append("    AND  bdc.id_ms_tenant = :idTenant ")
				.append("    AND  bdc.lov_balance_type = :idBalanceType ")
				.append("    AND  bdc.recap_date <= ");
				if (dateStart == null) {
					query.append("date_trunc('month', now()) - interval '1 day' ");
				}
				else {
					query.append(" (CAST(:startDate AS DATE) - INTERVAL '1 DAY') ");
				}
		query.append(" ORDER BY bdc.recap_date DESC limit 1 ")
			.append(" ) ");

		query.append("select * from ( ")
			 .append(" select TO_CHAR(bm.trx_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC+"') as \"trxDate\", ")
			 .append("       balancetype.description as \"balanceType\", ")
			 .append("      bm.ref_no as \"refNo\", ")
			 .append("       mlovDocType.description as \"documentType\", ") 
			.append("       case when docd.id_ms_doc_template is null then docd.document_name ")
			.append(" else dt.doc_template_name end as \"documentName\", ")
			.append("       CAST(bm.qty AS VARCHAR), ")
			 .append("       COALESCE(userCust2.full_name, COALESCE(userCust.full_name, bm.usr_crt)) as \"customerName\", ")
			 .append("       office.office_name as \"office\", ")
			 .append("       bm.notes as \"notes\", ")
			 .append("       bm.trx_no as \"trxNo\", ")
			 .append("       region.region_name as \"region\", ")
			 .append("       businessLine.business_line_name as \"businessLine\", ")
			 .append("       row_number() over (order by bm.trx_date asc) as \"no\", ")
			 .append("       bm.vendor_trx_no as \"vendorTrxNo\", ")
			 .append("       TO_CHAR(docd.request_date,'"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC+"') as \"requestDate\", ")
			
			 .append("       TO_CHAR(docd.completed_date,'"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC+"') as \"signCompleteDate\", ")
			 .append("       case when docd.total_sign is not null then CONCAT(docd.total_signed , '/',docd.total_sign) ")
			 .append("       else null end as \"signProcess\" ,")
			 .append("       mlovSignStatus.description as \"documentSignStatus\", ")
			 .append("       CASE ")
			 .append("       WHEN doch.proses_materai is null then null ")
			 .append("       WHEN doch.proses_materai IN (1, 51, 61, 521,71) THEN 'Failed' ")
			 .append("       WHEN doch.proses_materai IN (3, 53, 523, 63,73) THEN 'Success' ")
			 .append("       WHEN doch.proses_materai IN (2, 5, 55, 52, 62, 65, 525, 522,72,75,74) THEN 'In Progress' ")
			 .append("       ELSE 'Not Started' ")
			 .append("       END as \"stampingProcess\" ")
			 
			 .append(" from tr_balance_mutation bm ")
			 .append(" join bdc ON 1=1 ")
			 .append(" join ms_lov balancetype            ON balancetype.id_lov = bm.lov_balance_type ")
			 .append(" left join am_msuser u          ON u.login_id = bm.usr_crt ")
			 .append(" left join tr_document_d  docd  ON docd.id_document_d = bm.id_document_d ")
			 .append("                   AND  docd.id_ms_tenant = :idTenant and '" + balanceTypeLov.getCode() + "' in ( " +conditionSDT +" ) ")
			 .append(" left join tr_document_h doch   ON doch.id_document_h = bm.id_document_h ")
			 .append("                   AND    doch.id_ms_tenant = :idTenant and '" + balanceTypeLov.getCode() + "' in ( " +conditionSDT +" ) ")
			 .append(" left join ms_doc_template AS dt     ON dt.id_doc_template = docd.id_ms_doc_template and '" + balanceTypeLov.getCode() + "' in ( " +conditionSDT +" ) ")
			 .append(" left join ms_lov    AS mlovDocType  ON mlovDocType.id_lov = doch.lov_doc_type and '" + balanceTypeLov.getCode() + "' in ( " +conditionSDT +" ) ")
			 .append(" left join ms_lov    AS mlovSignStatus  ON mlovSignStatus.id_lov = docd.lov_sign_status and '" + balanceTypeLov.getCode() + "' in ( " +conditionSDT +" ) ")
			 .append(" left join am_msuser AS userCust     ON userCust.id_ms_user = doch.id_msuser_customer")
			 .append(" left join am_msuser AS userCust2 ON userCust2.id_ms_user = bm.id_ms_user")
			 .append(" left join ms_office office on bm.id_ms_office = office.id_ms_office")
			 .append(" left join ms_region AS region on region.id_ms_region = office.id_ms_region")
			 
			 .append(" left join ms_business_line businessLine ON businessLine.id_ms_business_line = bm.id_ms_business_line")
			 .append(" where DATE(bm.trx_date) > bdc.recap_date ")
			 .append("    AND  bm.id_ms_tenant = :idTenant ")
			 .append("    AND  bm.lov_balance_type = :idBalanceType ")
			 
			 .append(paramQueryListBalanceMutation)
			 .append(" order by bm.trx_date,bm.trx_no ")
			 .append(" ) as result ");


		return this.managerDAO.selectForListString(BalanceMutationExternalBean.class, query.toString(), params, null);
	}
	
	
	@Override
	public List<BalanceMutationBean> getListBalanceMutation(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov,
			String transactionType, Date dateStart, Date dateEnd, String documentType, String refNo,
			String documentName, int min, int max, String officeCode, String status) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(PARAM_ID_TENANT, tenant.getIdMsTenant());
		params.put("idVendor", vendor.getIdMsVendor());
		params.put(PARAM_ID_BALANCE_TYPE, balanceTypeLov.getIdLov());
		documentType = documentType.toUpperCase();
		
		String paramQueryListBalanceMutation = this.constructParamListBalanceMutation(params, transactionType,
				dateStart, dateEnd, documentType, refNo, documentName, officeCode,null,null, status);
		StringBuilder query = new StringBuilder();

		query.append(" with bdc AS ( ")
				.append(" SELECT recap_date,  ")
				.append("        recap_total_balance ")
				.append(" FROM  tr_balance_daily_recap bdc ")
				.append(" WHERE 1=1 ")
				.append("    AND  bdc.id_ms_tenant = :idTenant ")
				.append("    AND  bdc.id_ms_vendor = :idVendor ")
				.append("    AND  bdc.lov_balance_type = :idBalanceType ")
				.append("    AND  bdc.recap_date <= ");
				if (dateStart == null) {
					// ESH-925: Perbaikan default filter tanggal tidak menampilkan data di tanggal 1. Ditambah - interval '1 day'
					query.append("date_trunc('month', now()) - interval '1 day' ");
				}
				else {
					//query.append(" :startDate ");'
					query.append(" (CAST(:startDate AS DATE) - INTERVAL '1 DAY') ");
				}
		query.append(" ORDER BY bdc.recap_date DESC limit 1 ")
			.append(" ) ");

		query.append("select * from ( ")
			 .append(" select TO_CHAR(bm.trx_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC+"') as \"transactionDate\", ")
			 .append("       trxType.description as \"transactionType\", ")
			 .append("       bm.ref_no as \"refNumber\", ")
			 .append("       mlovDocType.code as \"documentType\", ") 
			// Check if dt.id_doc_template is null
			.append("       case when docd.id_ms_doc_template is null then docd.document_name ")
			.append(" else dt.doc_template_name end as \"documentName\", ")
			.append("       CAST(bm.qty AS VARCHAR), ")
			 .append("       COALESCE(userCust2.full_name, COALESCE(userCust.full_name, bm.usr_crt)) as \"customerName\", ")
			 .append("       office.office_name as \"officeName\", ")
			 .append("       CAST(COALESCE(bdc.recap_total_balance,0) + sum(COALESCE(bm.qty,0)) OVER (order by trx_date asc,trx_no ) as VARCHAR) as \"balance\", ")
			 .append("       bm.notes as \"notes\", ")
			 .append("       bm.trx_no as \"transactionNo\", ")
			 .append("       region.region_name as \"region\", ")
			 .append("       businessLine.business_line_name as \"businessLine\", ")
			 .append("       row_number() over (order by bm.trx_date asc) as \"rowNum\" ")
			 .append(" from tr_balance_mutation bm ")
			 .append(" join bdc ON 1=1 ")
			 .append(" join ms_lov trxType            ON trxType.id_lov = bm.lov_trx_type ")
			 .append(" left join am_msuser u          ON u.login_id = bm.usr_crt ")
			 .append(" left join tr_document_d  docd  ON docd.id_document_d = bm.id_document_d ")
			 .append("                         AND  docd.id_ms_tenant = :idTenant ")
			 //.append("                         AND  docd.id_ms_vendor = :idVendor ")
			 .append(" left join tr_document_h doch   ON doch.id_document_h = bm.id_document_h ")
			 .append("                         AND    doch.id_ms_tenant = :idTenant  ")
			 .append(" left join ms_doc_template AS dt     ON dt.id_doc_template = docd.id_ms_doc_template ")
			 .append(" left join ms_lov    AS mlovDocType  ON mlovDocType.id_lov = doch.lov_doc_type ")
			 .append(" left join am_msuser AS userCust     ON userCust.id_ms_user = doch.id_msuser_customer")
			 .append(" left join am_msuser AS userCust2 ON userCust2.id_ms_user = bm.id_ms_user")
			 .append(" left join ms_office office on bm.id_ms_office = office.id_ms_office")
			 .append(" left join ms_region AS region on region.id_ms_region = office.id_ms_region")
			 .append(" left join ms_business_line businessLine ON businessLine.id_ms_business_line = bm.id_ms_business_line")
			 .append(" where DATE(bm.trx_date) > bdc.recap_date ")
			    .append("    AND  bm.id_ms_tenant = :idTenant ")
				.append("    AND  bm.id_ms_vendor = :idVendor ")
				.append("    AND  bm.lov_balance_type = :idBalanceType ")
			 .append(paramQueryListBalanceMutation)
			 .append(" order by bm.trx_date,bm.trx_no ")
			 .append(" ) as result ");
		if (min != 0 && max != 0) {
			query.append("where \"rowNum\" between :min and :max");
			params.put("min", min);
			params.put("max", max);
		}

		return this.managerDAO.selectForListString(BalanceMutationBean.class, query.toString(), params, null);
	}

	@Override
	public int countBalanceMutation(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov,
			String transactionType, Date dateStart, Date dateEnd, String documentType, String refNo,
			String documentName, String officeCode, String status) {
		Map<String, Object> params = new HashMap<>();
		params.put(PARAM_ID_TENANT, tenant.getIdMsTenant());
		params.put("idVendor", vendor.getIdMsVendor());
		params.put(PARAM_ID_BALANCE_TYPE, balanceTypeLov.getIdLov());
		
		String paramQueryListBalanceMutation = this.constructParamListBalanceMutation(params, transactionType,
				dateStart, dateEnd, documentType, refNo, documentName, officeCode,null,null,status);
		StringBuilder query = new StringBuilder();

		query.append(" with bdc AS ( ")
		.append(" SELECT recap_date,  ")
		.append("        recap_total_balance ")
		.append(" FROM  tr_balance_daily_recap bdc ")
		.append(" WHERE 1=1 ")
		.append("    AND  bdc.id_ms_tenant = :idTenant ")
		.append("    AND  bdc.id_ms_vendor = :idVendor ")
		.append("    AND  bdc.lov_balance_type = :idBalanceType ")
		.append("    AND  bdc.recap_date <= ");
		if (dateStart == null) {
			// ESH-925: Perbaikan default filter tanggal tidak menampilkan data di tanggal 1. Ditambah - interval '1 day'
			query.append("date_trunc('month', now()) - interval '1 day' ");
		}
		else {
			//query.append(":startDate ");
			query.append(" (CAST(:startDate AS DATE) - INTERVAL '1 DAY') ");
		}
			query.append(" ORDER BY bdc.recap_date DESC limit 1 ")
	.append(" ) ");
		query.append(" select count (*) ")
		 .append(" from tr_balance_mutation bm ")
		 .append(" join bdc ON 1=1 ")
		 .append(" join ms_lov trxType            ON trxType.id_lov = bm.lov_trx_type ")
		 .append(" left join am_msuser u          ON u.login_id = bm.usr_crt ")
		 .append(" left join tr_document_d  docd  ON docd.id_document_d = bm.id_document_d ")
		 .append("                         AND  docd.id_ms_tenant = bm.id_ms_tenant")
//		 .append("                         AND  docd.id_ms_vendor = bm.id_ms_vendor ")
		 .append(" left join tr_document_h doch   ON doch.id_document_h = bm.id_document_h ")
		 .append("                         AND  doch.id_ms_tenant = bm.id_ms_tenant ")
		 .append(" left join ms_doc_template AS dt     ON dt.id_doc_template = docd.id_ms_doc_template ")
		 .append(" left join ms_lov    AS mlovDocType  ON mlovDocType.id_lov = doch.lov_doc_type ")
		 .append(" left join am_msuser AS userCust     ON userCust.id_ms_user = doch.id_msuser_customer")
		 .append(" left join ms_office AS office ON u.id_ms_office = office.id_ms_office")
		 .append(" where DATE(bm.trx_date) > bdc.recap_date ")
		    .append("    AND  bm.id_ms_tenant = :idTenant ")
			.append("    AND  bm.id_ms_vendor = :idVendor ")
			.append("    AND  bm.lov_balance_type = :idBalanceType ")
		 .append(paramQueryListBalanceMutation);
		
		return  ((BigInteger) this.getManagerDAO().selectOneNativeString(query.toString(), params)).intValue();
	}
	
	@Override
	public List<CancelBalanceMutationBean> getListBalanceMutationByRefNo(String refNo) {
		StringBuilder query = new StringBuilder();
		query.append("SELECT balanceType.code as \"balanceType\", bm.id_stamp_duty as \"idStampDuty\", ")
			 .append("bm.id_document_d as \"idDocumentD\" ")
			 .append("FROM tr_balance_mutation bm ")
			 .append("JOIN ms_lov balanceType on balanceType.id_lov = bm.lov_balance_type ")
			 .append("WHERE bm.ref_no LIKE :refNo ")
			 .append("ORDER BY bm.id_stamp_duty asc ");
		Map<String, Object> params = new HashMap<>();
		params.put("refNo", refNo);
		
		return this.managerDAO.selectForListString(CancelBalanceMutationBean.class, query.toString(), params, null);
	}

	@Override
	public List<BalanceBean> getBalanceByVendorAndTenant(String tenantCode, String vendorCode, String balanceType) {
		Map<String, Object> params = new HashMap<>();
		params.put("date", new Date());
		
		String paramQueryTenantVendor = this.constructParamVendorTenant(params, tenantCode, vendorCode);
		
		StringBuilder query = new StringBuilder();
		query.append("select lov_group as \"lovGroup\", code, description, ")
			 .append("       CAST(SUM(COALESCE(recap_total_balance,0) + COALESCE(number_of_use,0)) AS BIGINT) as \"currentBalance\" ")
			 .append("from ms_lov ")
			 .append("left join lateral (Select recap_date, recap_total_balance, bdc.id_ms_tenant, bdc.id_ms_vendor ")
			 .append(" from   tr_balance_daily_recap bdc ")
			 .append("                   join ms_tenant as t on t.id_ms_tenant = bdc.id_ms_tenant ")
			 .append("                   join ms_vendor as v on v.id_ms_vendor = bdc.id_ms_vendor ")
			 .append("                   where bdc.lov_balance_type = ms_lov.id_lov and bdc.recap_date <= :date ")
			 .append(paramQueryTenantVendor)
			 .append("                  order by bdc.recap_date desc limit 1 ")
			 .append(") bdc ON TRUE ")
			 .append("left join lateral (Select SUM(qty) as number_of_use ")
			 .append("                   from tr_balance_mutation bm")
			 .append("                   where bm.lov_balance_type = ms_lov.id_lov and DATE(bm.trx_date)>bdc.recap_date ")
			 .append("                         and bm.id_ms_tenant=bdc.id_ms_tenant and bm.id_ms_vendor=bdc.id_ms_vendor ")
			 .append(") bm on TRUE ")
			 .append("join ms_balancevendoroftenant bvot on bvot.id_ms_tenant = bdc.id_ms_tenant and bvot.id_ms_vendor = bdc.id_ms_vendor ")
			 .append(" where lov_group='"+GlobalVal.LOV_GROUP_BALANCE_TYPE+"' and ms_lov.id_lov = bvot.lov_balance_type ");
		if (StringUtils.isNotBlank(balanceType)) {
			query.append(" and code = '"+balanceType+"' ");
		}
		query.append(" group by lov_group,code,description");
		
		
		return this.managerDAO.selectForListString(BalanceBean.class, query.toString(), params, null);
	}
	
	private String constructParamVendorTenant(Map<String, Object> params, String tenantCode, String vendorCode) {
		StringBuilder query = new StringBuilder();
		if (StringUtils.isNotBlank(tenantCode)) {
			query.append(" and tenant_code = :tenantCode ");
			params.put("tenantCode", StringUtils.upperCase(tenantCode));
		}
		if (StringUtils.isNotBlank(vendorCode)) {
			query.append(" and vendor_code = :vendorCode ");
			params.put("vendorCode", StringUtils.upperCase(vendorCode));
		}
		return query.toString();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrBalanceMutation> getListBalanceMutationByRefNoAndTenantCode(String refNo, String tenantCode) {
		Object[][] queryParams = {
				{"refNumber", refNo},
				{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return (List<TrBalanceMutation>) this.managerDAO.list(
				"from TrBalanceMutation bm "
				+ "join fetch bm.msTenant t "
				+ "where bm.refNo = :refNumber and t.tenantCode = :tenantCode ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrBalanceMutation> getListStampBalanceMutationByRefNoAndTenantCode(String refNo, String tenantCode, MsLov idLov) {
		Object[][] queryParams = {
				{"refNumber", refNo},
				{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)},
				{"lovBalanceType", idLov}
		};
		
		return (List<TrBalanceMutation>) this.managerDAO.list(
				"from TrBalanceMutation bm "
				+ "join fetch bm.msTenant t "
				+ "where bm.msLovByLovBalanceType = :lovBalanceType and bm.refNo = :refNumber and t.tenantCode = :tenantCode ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public void updateListBalanceMutation(List<TrBalanceMutation> listBm) {
		for (TrBalanceMutation balanceMutation : listBm) {
			balanceMutation.setUsrUpd(MssTool.maskData(balanceMutation.getUsrUpd()));
		}
		this.managerDAO.update(listBm, listBm.size());
	}

	@Override
	public void updateTrBalanceMutation(TrBalanceMutation balanceMutation) {
		balanceMutation.setUsrUpd(MssTool.maskData(balanceMutation.getUsrUpd()));
		this.managerDAO.update(balanceMutation);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateTrBalanceMutationNewTran(TrBalanceMutation balanceMutation) {
		balanceMutation.setUsrUpd(MssTool.maskData(balanceMutation.getUsrUpd()));
		this.managerDAO.update(balanceMutation);
	}

	@Override
	public TrBalanceMutation getStampDutyBalanceMutation(TrStampDuty stampDuty) {
		if (null == stampDuty) {
			return null;
		}
		return this.managerDAO.selectOne(TrBalanceMutation.class,
				new Object[][] {{ Restrictions.eq(TrBalanceMutation.TR_STAMP_DUTY_HBM, stampDuty) }});
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertTrBalanceMutationNewTran(TrBalanceMutation balanceMutation) {
		balanceMutation.setUsrCrt(MssTool.maskData(balanceMutation.getUsrCrt()));
		this.managerDAO.insert(balanceMutation);
	}

	@Override
	public TrBalanceMutation getLatestStampDutyBalanceMutation(TrDocumentD document) {
		boolean isPostapid = "1".equals(document.getTrDocumentH().getIsPostpaidStampduty());
		
		Object[][] balanceTypeParams = {
				{Restrictions.eq(MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_BALANCE_TYPE)},
				{Restrictions.eq(MsLov.CODE_HBM, isPostapid ? GlobalVal.CODE_LOV_BALANCE_TYPE_SDT_POSTPAID : GlobalVal.CODE_LOV_BALANCE_TYPE_SDT)}
		};
		MsLov lovBalanceType = managerDAO.selectOne(MsLov.class, balanceTypeParams);
		
		Object[][] trxTypeParams = {
				{Restrictions.eq(MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_TRX_TYPE)},
				{Restrictions.eq(MsLov.CODE_HBM, isPostapid ? GlobalVal.CODE_LOV_TRX_TYPE_USDT_POSTPAID : GlobalVal.CODE_LOV_TRX_TYPE_USDT)}
		};
		MsLov lovTrxType = managerDAO.selectOne(MsLov.class, trxTypeParams);
		
		Map<String, Object> params = new HashMap<>();
		params.put("lovBalanceType", lovBalanceType.getIdLov());
		params.put("lovTrxType", lovTrxType.getIdLov());
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, document.getIdDocumentD());
		params.put(TrDocumentH.ID_DOCUMENT_H_HBM, document.getTrDocumentH().getIdDocumentH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_balance_mutation from tr_balance_mutation ")
			.append("where id_document_h = :idDocumentH ")
			.append("and id_document_d = :idDocumentD ")
			.append("and lov_balance_type = :lovBalanceType ")
			.append("and lov_trx_type = :lovTrxType ")
			.append("order by id_balance_mutation desc limit 1 ");
		
		BigInteger idBalanceMutation = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		return managerDAO.selectOne(
				"from TrBalanceMutation bm "
				+ "join fetch bm.msLovByLovBalanceType "
				+ "join fetch bm.msLovByLovTrxType "
				+ "join fetch bm.msTenant "
				+ "join fetch bm.msVendor "
				+ "join fetch bm.trDocumentD "
				+ "join fetch bm.trDocumentH "
				+ "join fetch bm.trStampDuty "
				+ "where bm.idBalanceMutation = :idBalanceMutation",
				new Object[][] {{"idBalanceMutation", idBalanceMutation.longValue()}});
	}

	@Override
	public BigInteger getSingleBalanceByVendorAndTenantAndType(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov) {
		Map<String, Object> params = new HashMap<>();
		params.put("date", new Date());
		params.put("balanceType", balanceTypeLov.getIdLov());
		params.put("vendorId", vendor.getIdMsVendor());
		params.put("tenantId", tenant.getIdMsTenant());
		
		StringBuilder query = new StringBuilder();
		query.append("with bdc as ( ")
			.append("     Select recap_date, recap_total_balance ")
			.append("     from   tr_balance_daily_recap bdc ")
			.append("     where  bdc.lov_balance_type = :balanceType ")
					.append( " and bdc.id_ms_tenant = :tenantId and bdc.id_ms_vendor = :vendorId")
					.append( " and bdc.recap_date <= :date ")
			.append("    order by bdc.recap_date desc limit 1 ")
			.append(" ) ")
			.append("select CAST(SUM(COALESCE(recap_total_balance,0) + COALESCE(number_of_use,0)) AS BIGINT)  ")
			.append("from bdc ")
			.append("left join lateral (Select SUM(qty) as number_of_use ")
			.append("                   from tr_balance_mutation bm")
			.append("                   where DATE(bm.trx_date)>bdc.recap_date ")
			.append("                     and bm.lov_balance_type = :balanceType ")
			.append("                     and bm.id_ms_tenant= :tenantId and bm.id_ms_vendor= :vendorId ")
			.append(") bm on TRUE ");
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public String getPaymentSignType(String[] documentId) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.DOC_ID_HBM, documentId);
		
		query.append("SELECT DISTINCT(ms_lov.CODE) FROM tr_document_d JOIN ms_lov ON lov_payment_sign_type = id_lov WHERE document_id IN (:docId)");
		
		return (String) managerDAO.selectOneNativeString(query.toString(), params);
		
	}

	@Override
	public BigInteger getPaymentByDoc(String[] documentId) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.DOC_ID_HBM, documentId);
		
		query.append("SELECT DISTINCT(id_ms_vendor) FROM tr_document_d \r\n" + 
				"WHERE document_id IN (:docId)");
		
		return (BigInteger) this.getManagerDAO().selectOneNativeString(query.toString(), params);
	}
	
	@Override
	public List<BigInteger> getIdUserByListDoc(String[] documentId) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.DOC_ID_HBM, documentId);
		
		query.append("SELECT DISTINCT (a.id_ms_user) FROM tr_document_d b JOIN tr_document_d_sign a ON (a.id_document_d = b.id_document_d)\r\n" + 
				"WHERE document_id IN (:docId)");
		
		List<Map<String, Object>> result = this.getManagerDAO().selectAllNativeString(query.toString(), params);
		
		Iterator<Map<String, Object>> itr = result.iterator();
		List<BigInteger> listUser = new ArrayList<>();
		
		while(itr.hasNext()) {
			Map<String, Object> map = itr.next();
			BigInteger id = (BigInteger) map.get("d0");
			listUser.add(id);
			
		}
		
		return listUser;
	}

	@Override
	public TrBalanceMutation getBalanceMutationByTrxNo(String trxNo) {
		Object[][] params = new Object[][] {{Restrictions.eq("trxNo", trxNo)}};
		return managerDAO.selectOne(TrBalanceMutation.class, params);
	}

	@Override
	public TrBalanceMutation getBalanceMutationByIdBalanceMutation(long idBalanceMutation) {
		Object[][] params = new Object[][] {{Restrictions.eq("idBalanceMutation", idBalanceMutation)}};
		return managerDAO.selectOne(TrBalanceMutation.class, params);
	}
	
	@Override
	public TrBalanceMutation getBalanceMutationByTrxNoAndTenant(String trxNo, MsTenant tenant) {
		Map<String, Object> params = new HashMap<>();
		params.put("trxNo", trxNo);
		params.put("tenant", tenant);
		
		return managerDAO.selectOne(
				"from TrBalanceMutation bm "
				+ "join fetch bm.msTenant mt "
				+ "join fetch bm.msVendor mv "
				+ "where bm.trxNo = :trxNo "
				+ "and bm.msTenant = :tenant ", params);
	}

	@Override
	public Long countVerifUsageBalanceMutation(MsVendor vendor, Date trxDate, String usrCrt) {
		
		String startTime = MssTool.formatDateToStringIn(trxDate, GlobalVal.DATE_FORMAT) + " " + GlobalVal.SOD_TIME;
		String endTime = MssTool.formatDateToStringIn(trxDate, GlobalVal.DATE_FORMAT) + " " + GlobalVal.EOD_TIME;
		
		Map<String, Object> params = new HashMap<>();
		params.put("startTime", MssTool.formatStringToDate(startTime, GlobalVal.DATE_TIME_FORMAT_SEC));
		params.put("endTime", MssTool.formatStringToDate(endTime, GlobalVal.DATE_TIME_FORMAT_SEC));
		params.put("balanceTypeCode", GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
		params.put("trxTypeCode", GlobalVal.CODE_LOV_TRX_TYPE_UVRF);
		params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
		params.put("usrCrt", usrCrt);
		params.put("qty", -1);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(1) ")
			.append("from tr_balance_mutation ")
			.append("where trx_date >= :startTime and trx_date <= :endTime ")
			.append("and lov_balance_type = (select id_lov from ms_lov where lov_group = 'BALANCE_TYPE' and code = :balanceTypeCode) ")
			.append("and lov_trx_type = (select id_lov from ms_lov where lov_group = 'TRX_TYPE' and code = :trxTypeCode) ")
			.append("and id_ms_vendor = :idMsVendor ")
			.append("and usr_crt = :usrCrt ")
			.append("and qty = :qty ");
		
		BigInteger count = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == count) {
			return 0L;
		}
		
		return count.longValue();
	}

	@Override
	public TrBalanceMutation getBalanceMutationByTenantAndVendorAndTopupDate(MsTenant tenant, MsVendor vendor,
			Date topupDate) {
		Map<String, Object> params = new HashMap<>();
		params.put("vendor", vendor);
		params.put("tenant", tenant);
		params.put("topupDate", topupDate);
		
		return managerDAO.selectOne(
				"from TrBalanceMutation bm "
				+ "join fetch bm.msTenant mt "
				+ "join fetch bm.msVendor mv "
				+ "where bm.msVendor = :vendor "
				+ "and bm.trxDate = :topupDate "
				+ "and bm.msTenant = :tenant ", params);
	}

	



}
