package com.adins.esign.businesslogic.api;

import java.io.IOException;

import com.adins.esign.model.TrDocumentD;

public interface FileAccessLogic {
	// On-Premise Peruri, akses ke folder /UNSIGNED/
	void storeBaseStampDocument(byte[] fileContent, TrDocumentD document) throws IOException;
	byte[] getBaseStampDocument(TrDocumentD document) throws IOException;
	boolean deleteBaseStampDocument(TrDocumentD document);

	// On-Premise Peruri, akses ke folder /SIGNED/
	byte[] getStampedDocument(TrDocumentD document) throws IOException;
	boolean isStampedDocumentExists(TrDocumentD document);
	boolean deleteStampedDocument(TrDocumentD document);
	boolean deleteStampedDocumentbckp(TrDocumentD document);

	// On-Premise Peruri, akses ke folder /STAMP/
	void storeStampQr(byte[] imageContent, String serialNumber) throws IOException;
	byte[] getStampQr(String serialNumber) throws IOException;
	boolean deleteStampQr(String serialNumber);
}
