package com.adins.esign.dataaccess.api;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.BalanceBean;
import com.adins.esign.model.custom.BalanceMutationBean;
import com.adins.esign.model.custom.BalanceMutationExternalBean;
import com.adins.esign.model.custom.CancelBalanceMutationBean;

public interface BalanceMutationDao {
	// tr_balance_mutation
	void insertTrBalanceMutation(TrBalanceMutation balanceMutation);
	void insertTrBalanceMutationNewTran(TrBalanceMutation balanceMutation);
	void updateTrBalanceMutation(TrBalanceMutation balanceMutation);
	void updateTrBalanceMutationNewTran(TrBalanceMutation balanceMutation);
	TrBalanceMutation getBalanceMutationByTrxNo(String trxNo);
	TrBalanceMutation getBalanceMutationByTrxNoAndTenant(String trxNo, MsTenant tenant);
	TrBalanceMutation getBalanceMutationByTenantAndVendorAndTopupDate(MsTenant tenant, MsVendor vendor, Date topupDate);
	TrBalanceMutation getBalanceMutationByIdBalanceMutation(long idBalanceMutation);
	
	// Digunakan untuk validasi jumlah percobaan registrasi
	Long countVerifUsageBalanceMutation(MsVendor vendor, Date trxDate, String usrCrt);
	
	// list tr_balance_mutation
	List<TrBalanceMutation> getListBalanceMutationByRefNoAndTenantCode(String refNo, String tenantCode);
	List<TrBalanceMutation> getListStampBalanceMutationByRefNoAndTenantCode(String refNo, String tenantCode, MsLov idLov);

	void updateListBalanceMutation(List<TrBalanceMutation> listBm);
	
	BigInteger getSingleBalanceByVendorAndTenant(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov);
	List<BalanceBean> getBalanceByVendorAndTenant(String tenantCode, String vendorCode, String balanceType);
	List<BalanceMutationExternalBean> getListBalanceMutationExternal(MsTenant tenant,  MsLov balanceTypeLov,
			Date dateStart, Date dateEnd,  String officeCode,String regionCode,String businessLineCode);
	List<BalanceMutationBean> getListBalanceMutation(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov,
													String transactionType, Date dateStart, Date dateEnd,
													String documentType, String refNo, String documentName, int min, int max, String officeCode,
													String status);
	int countBalanceMutation(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov,
			String transactionType, Date dateStart, Date dateEnd,
			String documentType, String refNo, String documentName, String officeCode, String status);
	List<CancelBalanceMutationBean> getListBalanceMutationByRefNo(String refNo);
	
	
	
	// Generate / Stamp E-Meterai Balance Mutation
	TrBalanceMutation getStampDutyBalanceMutation(TrStampDuty stampDuty);
	TrBalanceMutation getLatestStampDutyBalanceMutation(TrDocumentD document);
	
	
	BigInteger getSingleBalanceByVendorAndTenantAndType(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov );
	
	String getPaymentSignType(String[] documentId);
	
	BigInteger getPaymentByDoc(String[] documentId);
	
	List<BigInteger> getIdUserByListDoc(String[] documentId);
	
	
}
