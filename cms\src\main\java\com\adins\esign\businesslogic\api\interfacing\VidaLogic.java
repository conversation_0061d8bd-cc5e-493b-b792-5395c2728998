package com.adins.esign.businesslogic.api.interfacing;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Date;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.ws.rs.core.MultivaluedMap;

import com.adins.am.model.AmMsuser;
import com.adins.esign.constants.enums.RegistrationType;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDSign;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.custom.RegisterVerificationStatusBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.vida.VidaRegisterResponseContainer;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.vida.PoaRequest;
import com.adins.esign.webservices.model.vida.PoaResponse;
import com.adins.esign.webservices.model.vida.PoaStatusCheckResponse;
import com.adins.esign.webservices.model.vida.VidaRegisterResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface VidaLogic {
	String generateToken(MsVendor vendor, MsTenant tenant) throws IOException;
	String cvvEncryption(MsVendorRegisteredUser vru, MsTenant tenant, AuditContext audit) throws InvalidKeyException, InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, UnsupportedEncodingException;
	
	// PoA
	PoaResponse doPoa(PoaRequest request, MsTenant tenant, TrDocumentD docD, TrDocumentH docH, TrDocumentDSign docDSign, AuditContext audit) throws IOException, InterruptedException;
	PoaRequest buildPoaRequest(String email, String idNo, String pdfFile, String signLoc, int page, boolean qrEnable, String ipAddr, TrDocumentDSign docDSign, MsTenant tenant, AuditContext audit)  throws InvalidKeyException, InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, UnsupportedEncodingException;
	PoaStatusCheckResponse poaStatusCheck(String transactionId, MultivaluedMap<String, String> mapHeader) throws IOException;
	
	// Vida registration
	VidaRegisterResponseContainer register(UserBean userData, MsTenant tenant, Date consentTime, String reservedTrxNo, AuditContext audit);
	VidaRegisterResponseContainer registerExternal(RegisterExternalRequest request, MsTenant tenant, Date consentTime, String reservedTrxNo, AuditContext audit);
	AmMsuser insertRegisteredUser(VidaRegisterResponse registerResponse, UserBean userData, MsTenant tenant, RegistrationType registrationType, MsLov lovUserType, AuditContext audit);
	AmMsuser insertExternalRegisteredUser(VidaRegisterResponse registerResponse, RegisterExternalRequest request, MsTenant tenant, AuditContext audit);
	boolean isRegisterLivenessFailed(VidaRegisterResponse registerResponse, AuditContext audit);
	String getFailedRegisterMessage(VidaRegisterResponse registerResponse, AuditContext audit);
	String getErrorRegisterMessage(VidaRegisterResponse registerResponse, AuditContext audit);
	RegisterVerificationStatusBean buildVerificationStatusBean(VidaRegisterResponse registerResponse, AuditContext audit);
	
	Date getConsentTime();
}
