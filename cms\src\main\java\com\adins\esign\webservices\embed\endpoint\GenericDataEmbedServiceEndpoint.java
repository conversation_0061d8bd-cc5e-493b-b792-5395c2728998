package com.adins.esign.webservices.embed.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.embed.OfficeEmbedLogic;
import com.adins.esign.businesslogic.api.embed.RegionEmbedLogic;
import com.adins.esign.webservices.embed.api.DataEmbedService;
import com.adins.esign.webservices.model.OfficeListResponse;
import com.adins.esign.webservices.model.RegionListResponse;
import com.adins.esign.webservices.model.embed.OfficeListRequest;
import com.adins.esign.webservices.model.embed.RegionListRequest;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/embed/data")
@Api(value = "DataEmbedService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericDataEmbedServiceEndpoint implements DataEmbedService{
	
	@Autowired RegionEmbedLogic regionEmbedLogic;
	@Autowired OfficeEmbedLogic officeEmbedLogic;

	@Override
	@POST
	@Path("/officeListEmbed")
	public OfficeListResponse getOfficeListEmbed(OfficeListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return officeEmbedLogic.getOfficeListEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/regionListEmbed")
	public RegionListResponse getRegionListEmbed(RegionListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return regionEmbedLogic.getRegionListEmbed(request, audit);
	}

}
