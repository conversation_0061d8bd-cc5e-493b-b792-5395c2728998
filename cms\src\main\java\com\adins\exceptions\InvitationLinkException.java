package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class InvitationLinkException extends AdInsException {
	private static final long serialVersionUID = 1L;
	
	public enum ReasonInvitationLink {
		PROCESS_FOR_INV_BY_EMAIL_ONLY,
		INV_LINK_NOT_EXIST,
		CANNOT_SEND_OTP_TO_EMAIL,
		CANNOT_SEND_OTP_TO_PHONE,
		CANNOT_VERIFY_OTP_TO_EMAIL,
		PHONE_NO_NULL,
		INVALID_PHONE_NO,
		INVALID_EMAIL,
		INVALID_INV_LINK,
		INACTIVE_LINK,
		DOUBLED_EMAIL,
		DOUBLED_PHONE,
		DOUBLE_NIK,
		USER_REGISTERED,
		INVALID_INV_BY,
		INVALID_TENANT,
		INVALID_RECEIVER_DETAIL,
		MISMATCH_RECEIVER_DETAIL,
		DECRYPT_CODE_ERROR,
		EMPTY_VENDOR,
		CAN<PERSON><PERSON>_UPDATE,
		MISMATCH_VENDOR,
		INVITATION_EXPIRED,
		CANNOT_REGENERATE,
		RECEIVER_DETAIL_EMPTY,
		ACTIVE_ECERT,
		DOCUMENT_NOT_FOUND,
		UNKOWN
	}
	
	private final ReasonInvitationLink reason;
	
	public InvitationLinkException(ReasonInvitationLink reason) {
		this.reason = reason;
	}
	
	public InvitationLinkException(String message, ReasonInvitationLink reason) {
		super(message);
		this.reason = reason;
	}
	
	public InvitationLinkException(Throwable ex, ReasonInvitationLink reason) {
		super(ex);
		this.reason = reason;
	}
	
	public InvitationLinkException(String message, Throwable ex, ReasonInvitationLink reason) {
		super(message, ex);
		this.reason = reason;
	}

	public ReasonInvitationLink getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
			case PROCESS_FOR_INV_BY_EMAIL_ONLY:
				return StatusCode.PROCESS_FOR_INV_BY_EMAIL_ONLY;
			case INV_LINK_NOT_EXIST:
				return StatusCode.INV_LINK_NOT_EXIST;
			case CANNOT_SEND_OTP_TO_EMAIL:
				return StatusCode.CANNOT_SEND_OTP_TO_EMAIL;
			case CANNOT_VERIFY_OTP_TO_EMAIL:
				return StatusCode.CANNOT_VERIFY_OTP_TO_EMAIL;
			case INVALID_INV_LINK:
				return StatusCode.INVALID_INVITATION_LINK;
			case INVALID_EMAIL:
				return StatusCode.INV_LINK_INVALID_EMAIL;
			case INVALID_PHONE_NO:
				return StatusCode.INV_LINK_INVALID_PHONE;
			case PHONE_NO_NULL:
				return StatusCode.PHONE_NO_NULL;
			case USER_REGISTERED:
				return StatusCode.INV_LINK_USER_REGISTERED;
			case INACTIVE_LINK:
				return StatusCode.INV_LINK_INACTIVE;
			case INVALID_INV_BY:
				return StatusCode.INVALID_INV_BY;
			case INVALID_TENANT:
				return StatusCode.TENANT_CODE_INVALID;
			case INVALID_RECEIVER_DETAIL:
				return StatusCode.INVALID_RECEIVER_DETAIL;
			case MISMATCH_RECEIVER_DETAIL:
				return StatusCode.MISMATCH_RECEIVER_DETAIL;
			case DOUBLED_EMAIL:
				return StatusCode.DOUBLE_EMAIL;
			case DOUBLED_PHONE:
				return StatusCode.DOUBLE_PHONE;
			case DOUBLE_NIK:
				return StatusCode.DOUBLE_NIK;
			case DECRYPT_CODE_ERROR:
				return StatusCode.DECRYPT_CODE_ERROR;
			case EMPTY_VENDOR:
				return StatusCode.EMPTY_VENDOR;
			case CANNOT_UPDATE:
				return StatusCode.CANNOT_UPDATE;
			case MISMATCH_VENDOR:
				return StatusCode.MISMATCH_VENDOR;
			case INVITATION_EXPIRED:
				return StatusCode.INVITATION_EXPIRED;
			case CANNOT_REGENERATE:
				return StatusCode.CANNOT_REGENERATE;
			case RECEIVER_DETAIL_EMPTY:
				return StatusCode.RECEIVER_DETAIL_EMPTY;
			case ACTIVE_ECERT:
				return StatusCode.ACTIVE_ECERT;
			case DOCUMENT_NOT_FOUND:
				return StatusCode.DOCUMENT_NOT_FOUND_INV;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}

}
