package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.BalanceVendoroftenantDao;
import com.adins.esign.model.MsBalancevendoroftenant;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.custom.BalanceVendoroftenantBean;

@Transactional
@Component
public class BalanceVendoroftenantDaoHbn extends BaseDaoHbn implements BalanceVendoroftenantDao {

	@Override
	public List<BalanceVendoroftenantBean> getListBalanceVendoroftenant(String tenantCode, String vendorCode) {
		StringBuilder query = new StringBuilder();
		query.append("select distinct l.code as \"balanceTypeCode\", l.description as \"balanceTypeName\" ")
			 .append("from ms_balancevendoroftenant bvot ")
			 .append("join ms_lov l on l.id_lov = bvot.lov_balance_type ")
			 .append("join ms_vendor v on v.id_ms_vendor = bvot.id_ms_vendor ")
			 .append("join ms_tenant t on t.id_ms_tenant = bvot.id_ms_tenant ")
			 .append("where l.is_active = '1' and bvot.is_active = '1' and t.tenant_code = :tenantCode ");
		
		Map<String, Object> param = new HashMap<>();
		if (StringUtils.isNotBlank(vendorCode)) {
			query.append("and v.vendor_code = :vendorCode");
			param.put("vendorCode", vendorCode);
		}
		param.put("tenantCode", tenantCode);
		return this.managerDAO.selectForListString(BalanceVendoroftenantBean.class, query.toString(), param, null);
	}

	@Override
	public MsBalancevendoroftenant getBalanceVendorOfTenantByBalanceTypeAndTenant(String balanceType, String tenantCode, String vendorCode) {

	    Object[][] params = new Object[][] {
	            {MsTenant.TENANT_CODE_HBM, tenantCode},
	            {MsVendor.VENDOR_CODE_HBM, vendorCode},
	            {MsLov.CODE_HBM, balanceType}
	    };

	    return this.managerDAO.selectOne(
	            "FROM MsBalancevendoroftenant bvot " +
	                    "JOIN FETCH bvot.msVendor mv " +  // Use property name 'msVendor' instead of 'MsVendor'
	                    "JOIN FETCH bvot.msTenant mt " +
	                    "JOIN FETCH bvot.msLov ml1 " +
	                    "WHERE mt.tenantCode = :tenantCode " +
	                    "AND mv.vendorCode = :vendorCode " +  // Use property name 'vendorCode' instead of 'vendor_Code'
	                    "AND ml1.code = :code " +
	                    "AND ml1.isActive = '1' " +
	                    "AND mv.isActive = '1' " +
	                    "AND mt.isActive = '1' " +
	                    "AND (bvot.isHidden IS NULL OR bvot.isHidden = '0')", params);
	}
	
	

}
