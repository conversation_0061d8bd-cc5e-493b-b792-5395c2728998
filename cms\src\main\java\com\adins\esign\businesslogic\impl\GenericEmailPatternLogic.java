package com.adins.esign.businesslogic.impl;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.EmailPatternLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsEmailPattern;
import com.adins.esign.model.TrUrlForwarder;
import com.adins.esign.util.MssTool;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericEmailPatternLogic extends BaseLogic implements EmailPatternLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericEmailPatternLogic.class);
	
	@Autowired private CommonLogic commonLogic;
	
	@Value("${link.forwarder.url}") private String linkForwarderUrl;
	
	private String generateUrlForwarderCode() {
		String code = MssTool.generateRandomCharacters(GlobalVal.CHRS, 6);
		TrUrlForwarder urlForwarder = daoFactory.getUrlForwarderDao().getUrlForwarderByCode(code);
		
		while (urlForwarder != null) {
			code = MssTool.generateRandomCharacters(GlobalVal.CHRS, 6);
			urlForwarder = daoFactory.getUrlForwarderDao().getUrlForwarderByCode(code);
		}
		
		return code;
	}

	@Override
	public String shortenParsedEmailContent(MsEmailPattern emailPattern, AmMsuser user, NotificationType notifType, String parsedContent, AuditContext audit) {
		
		TrUrlForwarder urlForwarder = daoFactory.getUrlForwarderDao().getLatestUrlForwarder(emailPattern, user);
		
		if (null != urlForwarder) {
			urlForwarder.setUrlLink(null);
			urlForwarder.setUsrUpd(audit.getCallerId());
			urlForwarder.setDtmUpd(new Date());
			daoFactory.getUrlForwarderDao().updateUrlForwarder(urlForwarder);
		}
		
		String code = generateUrlForwarderCode();
		LOG.debug("Forwarder code {} generated for email pattern {} and user {}", code, emailPattern.getSubjectEmail(), user.getLoginId());
		
		TrUrlForwarder newForwarder = new TrUrlForwarder();
		newForwarder.setUrlCode(code);
		newForwarder.setUrlLink(parsedContent);
		newForwarder.setMsEmailPattern(emailPattern);
		newForwarder.setAmMsuser(user);
		newForwarder.setUsrCrt(audit.getCallerId());
		newForwarder.setDtmCrt(new Date());
		daoFactory.getUrlForwarderDao().insertUrlForwarder(newForwarder);
		
		// Kalau kirim WA, hanya return encrypted code karena URL utama sudah ada di template
		if (NotificationType.WHATSAPP == notifType) {
			return MssTool.urlEncode(commonLogic.encryptMessageToString(code, audit));
		}
		
		return linkForwarderUrl + MssTool.urlEncode(commonLogic.encryptMessageToString(code, audit));
	}

}
