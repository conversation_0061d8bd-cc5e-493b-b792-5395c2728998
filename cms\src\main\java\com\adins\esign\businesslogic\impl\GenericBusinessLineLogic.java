package com.adins.esign.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.BusinessLineLogic;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.BusinessLineBean;
import com.adins.esign.webservices.model.BusinessLineListRequest;
import com.adins.esign.webservices.model.BusinessLineListResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericBusinessLineLogic extends BaseLogic implements BusinessLineLogic {

	@Override
	public BusinessLineListResponse getBusinessLineList(BusinessLineListRequest request, AuditContext audit) {
		BusinessLineListResponse response = new BusinessLineListResponse();
		List<BusinessLineBean> businessLineBeans = new ArrayList<>();
		
		List<MsBusinessLine> businessLines = daoFactory.getBusinessLineDao().getBusinessLineByTenant(request.getTenantCode());
		for (MsBusinessLine businessLine : businessLines) {
			BusinessLineBean bean = new BusinessLineBean();
			bean.setBusinessLineCode(businessLine.getBusinessLineCode());
			bean.setBusinessLineName(businessLine.getBusinessLineName());
			businessLineBeans.add(bean);
		}
		response.setBusinessLineList(businessLineBeans);
		return response;
	}
	
	@Override
	public MsBusinessLine insertUnregisteredBusinessLine(String businessLineCode, String businessLineName, MsTenant tenant, AuditContext audit) {
		if (StringUtils.isBlank(businessLineCode)) {
			return null;
		}
		
		MsBusinessLine businessLine = daoFactory.getBusinessLineDao().getBusinessLineByCodeAndTenant(businessLineCode, tenant.getTenantCode());
		if (null == businessLine) {
			businessLine = new MsBusinessLine();
			businessLine.setBusinessLineCode(StringUtils.upperCase(businessLineCode));
			businessLine.setBusinessLineName(StringUtils.upperCase(businessLineName));
			businessLine.setMsTenant(tenant);
			businessLine.setUsrCrt(audit.getCallerId());
			businessLine.setDtmCrt(new Date());
			daoFactory.getBusinessLineDao().insertBusinessLine(businessLine);
		}
		return businessLine;
	}

}
