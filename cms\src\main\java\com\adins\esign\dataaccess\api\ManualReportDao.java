package com.adins.esign.dataaccess.api;

import java.math.BigInteger;
import java.util.List;

import com.adins.esign.model.TrManualReport;
import com.adins.esign.model.custom.ListManualReportBean;
import com.adins.esign.webservices.model.GetListManualReportRequest;

public interface ManualReportDao {
	void insertTrManualReport(TrManualReport trManualReport);
	void deleteTrManualReport(TrManualReport trManualReport);
	BigInteger countListManualReport(GetListManualReportRequest request);
	List<ListManualReportBean> getListManualReport(GetListManualReportRequest request, int start, int end);
	TrManualReport getManualReportByTenantCodeAndReportTypeAndFileName (String tenantCode,String reportType, String fileName);
	TrManualReport getManualReportByTenantCodeAndFileName (String tenantCode, String fileName);
}
