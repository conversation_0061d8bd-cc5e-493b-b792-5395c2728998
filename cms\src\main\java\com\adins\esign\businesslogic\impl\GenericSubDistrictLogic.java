package com.adins.esign.businesslogic.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsdistrict;
import com.adins.am.model.AmMsprovince;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.SubDistrictLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.SubDistrictBean;
import com.adins.esign.model.custom.SubDistrictExternalBean;
import com.adins.esign.validatorlogic.api.InvitationLinkValidatorLogic;
import com.adins.esign.webservices.model.GetSubDistrictByInvitationRequest;
import com.adins.esign.webservices.model.GetSubDistrictEmbedRequest;
import com.adins.esign.webservices.model.GetSubDistrictRequest;
import com.adins.esign.webservices.model.GetSubDistrictResponse;
import com.adins.esign.webservices.model.external.GetSubDistrictListExternalRequest;
import com.adins.esign.webservices.model.external.GetSubDistrictListExternalResponse;
import com.adins.exceptions.LocationException;
import com.adins.exceptions.LocationException.ReasonLocation;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericSubDistrictLogic extends BaseLogic implements SubDistrictLogic {
	
	@Autowired private CommonLogic commonLogic;
	
	@Autowired private InvitationLinkValidatorLogic invLinkValidatorLogic;
	@Autowired private TenantLogic tenantLogic;

	@Override
	public GetSubDistrictResponse getSubDistrictList(GetSubDistrictRequest request, AuditContext audit) {
		GetSubDistrictResponse response = new GetSubDistrictResponse();
		String trim = StringUtils.trim(request.getSubDistrictName());
		String toUpper = StringUtils.upperCase(trim);
		List<SubDistrictBean> listSubDistrict = daoFactory.getSubDistrictDao().
				getListSubDistrict(toUpper,	request.getDistrictId());
		
		response.setListSubDistrict(listSubDistrict);
		
		if(listSubDistrict.isEmpty()) {
			List<SubDistrictBean> listSubDistrictByDistrict = daoFactory.getSubDistrictDao().getListSubDistrict(null ,request.getDistrictId());
			response.setListSubDistrict(listSubDistrictByDistrict);
			return response;
		}
		
		return response;
	}

	@Override
	public GetSubDistrictResponse getSubDistrictListEmbed(GetSubDistrictEmbedRequest request, AuditContext audit) {
		commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);

		GetSubDistrictRequest subDistrictListRequest = new GetSubDistrictRequest();
		subDistrictListRequest.setSubDistrictName(request.getSubDistrictName());
		subDistrictListRequest.setDistrictId(request.getDistrictId());
		
		return this.getSubDistrictList(subDistrictListRequest, audit);
	}

	@Override
	public GetSubDistrictResponse getSubDistrictListByInvitation(GetSubDistrictByInvitationRequest request,
			AuditContext audit) {
		
		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);
		
		GetSubDistrictRequest subDistrictListRequest = new GetSubDistrictRequest();
		subDistrictListRequest.setSubDistrictName(request.getSubDistrictName());
		subDistrictListRequest.setDistrictId(request.getDistrictId());
		
		return this.getSubDistrictList(subDistrictListRequest, audit);
	}

	@Override
	public GetSubDistrictListExternalResponse getSubDistrictListExternal(GetSubDistrictListExternalRequest request,
			String apiKey, AuditContext audit) {
		
		tenantLogic.getTenantFromXApiKey(apiKey, audit);
		
		if (StringUtils.isBlank(request.getProvinceName())) {
			throw new LocationException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { "Province Name" }, this.retrieveLocaleAudit(audit)), ReasonLocation.PROVINCE_NAME_EMPTY);
		}
		
		AmMsprovince province = daoFactory.getProvinceDao().getProvinceByName(request.getProvinceName());
		
		if (province == null) {
			throw new LocationException(getMessage("businesslogic.province.provincewithnamenotfound",
					new Object[] { request.getProvinceName() }, audit), ReasonLocation.PROVINCE_NOT_FOUND);
		}
		
		if (StringUtils.isBlank(request.getDistrictName())) {
			throw new LocationException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { "District Name" }, this.retrieveLocaleAudit(audit)), ReasonLocation.DISTRICT_NAME_EMPTY);
		}
		
		AmMsdistrict district = daoFactory.getDistrictDao().getDistrictByName(request.getDistrictName());
		if (district == null) {
			throw new LocationException(getMessage("businesslogic.district.districtwithnamenotfound",
					new Object[] { request.getDistrictName() }, audit), ReasonLocation.DISTRICT_NOT_FOUND);
		}
		
		if (province.getIdMsprovince() != district.getAmMsprovince().getIdMsprovince()) {
			throw new LocationException(this.messageSource.getMessage("businesslogic.district.invalidprovince",
					new Object[] { district.getDistrictName(), province.getProvinceName() }, this.retrieveLocaleAudit(audit)), ReasonLocation.DISTRICT_NAME_EMPTY);
		}
		
		List<SubDistrictExternalBean> subDistrictList = daoFactory.getSubDistrictDao().getSubDistrictExternalList(district.getIdMsdistrict());
		
		GetSubDistrictListExternalResponse response = new GetSubDistrictListExternalResponse();
		response.setSubDistrictList(subDistrictList);
		return response;
	}

	

}
