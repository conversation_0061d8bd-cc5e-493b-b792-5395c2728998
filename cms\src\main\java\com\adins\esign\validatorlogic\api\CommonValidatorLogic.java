package com.adins.esign.validatorlogic.api;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface CommonValidatorLogic {
	void validateNotNull(Object object, String objectName, AuditContext audit);
	void validateNotNull(Object object, String statusMessage, int errorCode);
	void validateMustNull(Object object, String statusMessage, int errorCode);
	void validateMustEquals(Object object , Object objectCompare, String statusMessage, int errorCode );
	void validateMustNotEquals(Object object , Object objectCompare, String statusMessage, int errorCode );
	void validateListNotEmpty(List list, String statusMessage, int errorCode);
}
