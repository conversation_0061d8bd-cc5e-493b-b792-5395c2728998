package com.adins.esign.businesslogic.impl.provider;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.businesslogic.api.MenuLogic;
import com.adins.am.businesslogic.api.RoleLogic;
import com.adins.am.model.AmMsmenu;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.OauthAccessTokenDB;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.interfacing.IntFormLogic;
import com.adins.esign.constants.enums.InterfaceType;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.password.PasswordHash;

@Component
@Qualifier("customAuthenticationProvider")
public class CustomAuthenticationProvider extends BaseLogic implements AuthenticationProvider, MessageSourceAware {
	@Autowired
	@Qualifier("intFormLogicFactoryBean")
	private IntFormLogic intFormLogic;
	
	@Autowired private RoleLogic roleLogic;
	@Autowired private MenuLogic menuLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;

	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	private AmMsuser validateUserStatus(String loginId, AuditContext auditContext) {
		
		AmMsuser user = null;
		if (StringUtils.isNumeric(loginId)) {
			user = userValidatorLogic.validateGetUserByPhone(loginId, false, auditContext);//login with phone_no
		} else {
			user = userValidatorLogic.validateGetUserByEmailv2(loginId, false, auditContext);//login with email
		}
		
		if (user == null) {
			throw new BadCredentialsException(getMessage("businesslogic.login.idpassword", null, auditContext));
        }
		if (!"1".equals(user.getIsActive())) {
			throw new BadCredentialsException(getMessage("businesslogic.context.userinactive", null, auditContext));
		}
		if ("1".equals(user.getIsDormant())) {
			throw new BadCredentialsException(getMessage("businesslogic.login.dormantstatus1", null, auditContext));
		}
		if ("1".equals(user.getIsLocked())) {
			throw new BadCredentialsException(getMessage("businesslogic.context.useralreadylocked", null, auditContext));
		}
		
		return user;
	}

	@Override
	@Transactional(noRollbackFor={BadCredentialsException.class})
	public Authentication authenticate(Authentication authentication) throws AuthenticationException {
		
		String loginId = StringUtils.trim((String) authentication.getPrincipal());
		String password = (String) authentication.getCredentials();
		
		AuditContext auditContext = new AuditContext();
		auditContext.setCallerId(loginId);
		
		String allowConcurent = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_ALLOW_CONCURENT_SESSION);
		
		AmMsuser user = validateUserStatus(loginId, auditContext);
		
		if (!"1".equals(allowConcurent)) {
			OauthAccessTokenDB oauthAccessToken = daoFactory.getOauthAccessTokenDBDao().getOauthByUserName(StringUtils.upperCase(loginId));
			if (null != oauthAccessToken) {
				daoFactory.getOauthAccessTokenDBDao().deleteOauthByUserName(oauthAccessToken);
			}
			
			deleteAllOauthTokenUser(user);
		}
		
		if (user.getPassword().contains("newInv")) {
			throw new AuthenticationServiceException("Anda sudah berhasil registrasi. Harap menunggu permintaan tanda tangan dikirimkan.");
		}
		
		String interfaceType = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_INTERFACE_TYPE) ;
		boolean isCanLogin = false;
		if (InterfaceType.CONFINS.toString().equals(interfaceType)) {
			isCanLogin = intFormLogic.authenticateUser(user, password);
		} else if (InterfaceType.DB.toString().equals(interfaceType) && PasswordHash.validatePassword(password, user.getPassword())) {
			isCanLogin = true;
		}
		
		if (!isCanLogin) {
			int failCount = user.getFailCount();
			String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_FAIL_COUNT);
			Integer maxFailCount =  Integer.parseInt(gsValue);
			failCount = (failCount < maxFailCount) ? failCount + 1 : failCount;
			
			user.setFailCount(failCount);
			user.setPrevLoggedFail(user.getLastLoggedFail());
			user.setLastLoggedFail(new Date());
			user.setDtmUpd(new Date());
			user.setUsrUpd(String.valueOf(user.getIdMsUser()));
			
			if (failCount >= maxFailCount) {
				user.setIsLocked("1");
				user.setLastLocked(new Date());
			}
			
			daoFactory.getUserDao().updateUser(user);
			
			throw new BadCredentialsException(this.messageSource.getMessage("businesslogic.login.idpassword", null, this.retrieveLocaleAudit(auditContext)));
		}
		
		user.setFailCount(0);
		user.setIsLoggedIn("1");
		user.setPrevLoggedIn(user.getLastLoggedIn());
		user.setLastLoggedIn(new Date());
		user.setDtmUpd(new Date());
		user.setUsrUpd(String.valueOf(user.getIdMsUser()));
		daoFactory.getUserDao().updateUser(user);
			
		List<GrantedAuthority> auth = new ArrayList<>();
		auth.add(new SimpleGrantedAuthority("ROLE_APP"));
		
		HashMap<String, List<String>> tenantRoleMap = new HashMap<>();
		this.constructMapTenantRoles(tenantRoleMap, auth, user, auditContext);
		return new EsignAuthenticationToken(loginId, password, auth, user, tenantRoleMap);
	}


	private void constructMapTenantRoles(HashMap<String, List<String>> mapTenantRole, List<GrantedAuthority> auth, AmMsuser user, AuditContext auditContext) {
		List<MsTenant> tenantList = tenantLogic.getListTenantByUser(user, auditContext); 

		for (MsTenant tenant : tenantList) {
			List<AmMsrole> roleList = roleLogic.getListRoleByIdUserTenantCode(
					user.getIdMsUser(), tenant.getTenantCode(),auditContext);
			
			List<String> roleListTenantString = new ArrayList<>();

			for (AmMsrole role : roleList) {
				//new role based on menu
				List<AmMsmenu> menuList = menuLogic.getMenuListByIdRole(role.getIdMsRole(), auditContext);
				for (AmMsmenu menu : menuList) {
					String roleAuth = "ROLE_"+menu.getMenuCode();
					auth.add(new SimpleGrantedAuthority(roleAuth));
					roleListTenantString.add(roleAuth);
				}
			}
			mapTenantRole.put(tenant.getTenantCode(), roleListTenantString);
		}	
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return true;
	}
	
	@Async
	private void deleteAllOauthTokenUser(AmMsuser user) {
		
		List<MsVendorRegisteredUser> vendorusers = daoFactory.getVendorRegisteredUserDao().getListVendorRegisteredUserByIdMsUser(user.getIdMsUser());
		
		for (int i = 0; i < vendorusers.size(); i++) {			
            MsVendorRegisteredUser vendorUser = vendorusers.get(i);

			List<OauthAccessTokenDB> oauthAccessTokens = daoFactory.getOauthAccessTokenDBDao().getOauthByUpperUserName(StringUtils.upperCase(vendorUser.getSignerRegisteredEmail()));
			
			if (null != oauthAccessTokens) {
				for (int f = 0; f < oauthAccessTokens.size(); f++) {
					OauthAccessTokenDB oauthAccessToken = oauthAccessTokens.get(f);
					daoFactory.getOauthAccessTokenDBDao().deleteOauthByUserName(oauthAccessToken);
				}
			}
			
			if (null != vendorUser.getPhoneBytea()) {
				String userPhoneNumber = personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea());
				
				List<OauthAccessTokenDB> oauthAccessTokens2 = daoFactory.getOauthAccessTokenDBDao().getOauthByUpperUserName(StringUtils.upperCase(userPhoneNumber));
				
				if (null != oauthAccessTokens2) {
					for (int h = 0; h < oauthAccessTokens2.size(); h++) {
						OauthAccessTokenDB oauthAccessToken = oauthAccessTokens2.get(h);
						daoFactory.getOauthAccessTokenDBDao().deleteOauthByUserName(oauthAccessToken);
					}
				}
			}
			
			
		}
	}

}
