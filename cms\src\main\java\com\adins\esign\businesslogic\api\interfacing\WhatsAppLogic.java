package com.adins.esign.businesslogic.api.interfacing;

import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.webservices.model.SendWhatsAppRequest;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface WhatsAppLogic {
	/**
	 * Insert balance mutation with <code>Propagation.REQUIRES_NEW</code> transaction.<br>
	 * If error occurred with database session, may be needed to get <PERSON><PERSON><PERSON><PERSON>, AmMs<PERSON>, or TrDocumentH with <code>Propagation.REQUIRES_NEW</code> transaction.
	 */
	void sendMessage(SendWhatsAppRequest request, AuditContext audit);

	void sendMessage(SendWhatsAppRequest request, SigningProcessAuditTrailBean auditTrail, AuditContext audit);
	
	boolean sendMessageNotAsync(SendWhatsAppRequest request, AuditContext audit);
	
	boolean sendMessageNotAsync(SendWhatsAppRequest request, SigningProcessAuditTrailBean auditTrail, AuditContext audit);
	
	boolean needtoCutBalance(MsTenant tenant, String phoneNumber, boolean isOtp);
}