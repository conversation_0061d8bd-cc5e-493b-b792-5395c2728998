-- Table: tr_document_d_stampduty

-- DROP TABLE IF EXISTS tr_document_d_stampduty;

CREATE TABLE IF NOT EXISTS tr_document_d_stampduty
(
    id_document_d_stampduty bigint NOT NULL GENERATED ALWAYS AS IDENTITY ( INCREMENT 1 START 1 MINVALUE 1 MAXVALUE 9223372036854775807 CACHE 1 ),
    sign_location character varying(250) COLLATE pg_catalog."default",
    sign_page integer,
    seq_no smallint,
    transform text COLLATE pg_catalog."default",
    stamping_date timestamp without time zone,
    id_stamp_duty bigint,
    id_document_d bigint,
    usr_crt character varying(36) COLLATE pg_catalog."default" NOT NULL,
    dtm_crt timestamp without time zone NOT NULL,
    usr_upd character varying(36) COLLATE pg_catalog."default",
    dtm_upd timestamp without time zone,
    CONSTRAINT tr_document_d_stampduty_pkey PRIMARY KEY (id_document_d_stampduty),
    CONSTRAINT fk_docdstduty_docd FOREIGN KEY (id_document_d)
        REFERENCES tr_document_d (id_document_d) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT fk_docdstduty_stampduty FOREIGN KEY (id_stamp_duty)
        REFERENCES tr_stamp_duty (id_stamp_duty) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
)

TABLESPACE pg_default;

ALTER TABLE IF EXISTS tr_document_d_stampduty
    OWNER to sa;
-- Index: ix_docdstduty_docd

-- DROP INDEX IF EXISTS ix_docdstduty_docd;

CREATE INDEX IF NOT EXISTS ix_docdstduty_docd
    ON tr_document_d_stampduty USING btree
    (id_document_d ASC NULLS LAST)
    TABLESPACE esign_data;
-- Index: ix_docdstduty_stduty

-- DROP INDEX IF EXISTS ix_docdstduty_stduty;

CREATE INDEX IF NOT EXISTS ix_docdstduty_stduty
    ON tr_document_d_stampduty USING btree
    (id_stamp_duty ASC NULLS LAST)
    TABLESPACE esign_data;