package com.adins.esign.dataaccess.impl;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
//import com.adins.esign.model.MsBranch;

@Transactional
@Component
public class OfficeDaoHbnTest  {

//	@Override
//	public MsBranch getMsBranch(String branchCode) {
//		if (StringUtils.isBlank(branchCode))
//			return null;
//		
//		Object[][] queryParams = { {Restrictions.eq("branchCode", branchCode)} };
//		return this.managerDAO.selectOne(MsBranch.class, queryParams);
//	}

}
