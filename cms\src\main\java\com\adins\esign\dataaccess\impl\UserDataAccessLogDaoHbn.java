package com.adins.esign.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.UserDataAccessLogDao;
import com.adins.esign.model.TrUserDataAccessLog;
import com.adins.esign.util.MssTool;
@Transactional
@Component
public class UserDataAccessLogDaoHbn extends BaseDaoHbn implements UserDataAccessLogDao {
	
	@Override
	public void insertUserDataAccessLog(TrUserDataAccessLog userDataAccessLog) {
		userDataAccessLog.setUsrCrt(MssTool.maskData(userDataAccessLog.getUsrCrt()));
		this.managerDAO.insert(userDataAccessLog);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertUserDataAccessLogNewTrx(TrUserDataAccessLog userDataAccessLog) {
		userDataAccessLog.setUsrCrt(MssTool.maskData(userDataAccessLog.getUsrCrt()));
		this.managerDAO.insert(userDataAccessLog);
	}
}
