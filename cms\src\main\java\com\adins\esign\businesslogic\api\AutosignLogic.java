package com.adins.esign.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import com.adins.esign.webservices.model.DetailImportAutosignBmRequest;
import com.adins.esign.webservices.model.DetailImportAutosignBmResponse;
import com.adins.esign.webservices.model.DownloadDocumentDigisignRequest;
import com.adins.esign.webservices.model.DownloadTemplateExcelImportAutosignBmRequest;
import com.adins.esign.webservices.model.DownloadTemplateExcelImportAutosignBmResponse;
import com.adins.esign.webservices.model.ImportAutosignBmDataRequest;
import com.adins.esign.webservices.model.ImportAutosignBmDataResponse;
import com.adins.esign.webservices.model.InquiryImportAutosignBmRequest;
import com.adins.esign.webservices.model.InquiryImportAutosignBmResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface AutosignLogic {
	
	@RolesAllowed("INQUIRY_IMPORT_BM")
	ImportAutosignBmDataResponse importDataBmAutosign(ImportAutosignBmDataRequest request, AuditContext audit);
	
	@RolesAllowed("INQUIRY_IMPORT_BM")
	InquiryImportAutosignBmResponse inquiryImportAutosignBm(InquiryImportAutosignBmRequest request, AuditContext audit);
	
	@RolesAllowed("INQUIRY_IMPORT_BM")
	DetailImportAutosignBmResponse detailImportAutosignBm(DetailImportAutosignBmRequest request, AuditContext audit);
	
	@RolesAllowed("INQUIRY_IMPORT_BM")
	DownloadTemplateExcelImportAutosignBmResponse downloadTemplateExcelImportAutosignBm(DownloadTemplateExcelImportAutosignBmRequest request, AuditContext audit);
}
