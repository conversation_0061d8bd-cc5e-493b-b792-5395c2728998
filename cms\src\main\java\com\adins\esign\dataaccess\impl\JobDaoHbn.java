package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.esign.dataaccess.api.JobDao;
import com.adins.esign.model.MsJob;
import com.adins.esign.model.TrJobResult;
import com.adins.esign.model.custom.ListJobResultBean;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class JobDaoHbn extends BaseDaoHbn implements JobDao{

	@Override
	public List<Map<String, Object>> getListJobByJobType(String jobType) {
		Map<String, Object> params = new HashMap<>();
		params.put("jobType", jobType);
		
		StringBuilder query = new StringBuilder();
		query
			.append(" select id_ms_job as \"jobId\", job_code as \"jobCode\", job_name as \"jobName\", job_type as \"jobType\", ")
			.append(" is_active as \"isActive\", result_upload_location as \"resultUploadLocation\", result_file_format as \"resultFileFormat\" ")
			.append(" from ms_job")
			.append(" WHERE is_active ='1' AND job_type =:jobType ");
		
		//return this.managerDAO.selectForListString(JobBean.class, query.toString(), params, null);
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public TrJobResult checkJobResult(String requestParams, String tenantCode, String msJobCode) {

		//Query ini digunakan untuk pengecekan duplikat pada saat user request rekonsil. Jika duplikat maka tolak.
		
		Map<String, Object> params = new HashMap<>();
		params.put("requestParams", requestParams);
		params.put("msJobCode", StringUtils.upperCase(msJobCode));
		if(StringUtils.isNotBlank(tenantCode)) {
			params.put("tenantCode", StringUtils.upperCase(tenantCode));
		}
		
		if(StringUtils.isNotBlank(tenantCode)) {
			return managerDAO.selectOne(
					" from TrJobResult tjr "
							+ " join fetch tjr.msJob mj "
							+ " join fetch tjr.msTenant mt "
							+ " where mj.jobCode = :msJobCode "
							+ " and mt.tenantCode = :tenantCode "
							+ " and tjr.requestParams = :requestParams "
							+ " and tjr.processStatus = '0' "
							+ " or tjr.processStatus = '1' ", params);
		}
		else {
			return managerDAO.selectOne(
					" from TrJobResult tjr "
							+ " join fetch tjr.msJob mj "
							+ " where mj.jobCode = :msJobCode "
							+ " and tjr.requestParams = :requestParams "
							+ " and tjr.processStatus = '0' "
							+ " or tjr.processStatus = '1' ", params);
		}
	}
	
	@Override
	public void insertTrJobResult(TrJobResult newJobResult) {
		newJobResult.setUsrCrt(MssTool.maskData(newJobResult.getUsrCrt()));
		this.managerDAO.insert(newJobResult);
	}

	@Override
	public MsJob getMsJob(String msJobCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("msJobCode", StringUtils.upperCase(msJobCode));
		
		return managerDAO.selectOne(
				" from MsJob mj "
						+ " where mj.jobCode = :msJobCode ", params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrJobResult getNewJobResultByTenantUserAndIdJobResult(String tenantCode, long idUser, Long idJobResult) {
		Map<String, Object> params = new HashMap<>();
		params.put("tenant", StringUtils.upperCase(tenantCode));
		params.put("idUser", idUser);
		params.put("idJobResult", idJobResult);
		
		return managerDAO.selectOne("from TrJobResult tjr " 
									+ "join fetch tjr.msTenant mt "
									+ "join fetch tjr.amMsuser mu "
									+ "where tjr.idJobResult = :idJobResult and mu.idMsUser = :idUser "
									+ "and mt.tenantCode = :tenant and tjr.processStatus = 0 ", params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateJobResult(TrJobResult jobResult) {
		jobResult.setUsrUpd(MssTool.maskData(jobResult.getUsrUpd()));
		managerDAO.update(jobResult);
	}

	@Override
	public List<ListJobResultBean> getListJobRekonResult(int min, int max, Long idJob, String requestBy, 
			Short processStatus, Date requestDateStart, Date requestDateEnd) {

		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		
		String paramQueryTenant = this.constructParamList(params,  idJob, requestBy, processStatus, requestDateStart, requestDateEnd);		

		StringBuilder query = new StringBuilder();
		query.append(" WITH jobList AS ( ")
					.append(" SELECT tjr.id_job_result AS \"idJobResult\", tjr.id_ms_job AS \"idMsJob\" , tjr.id_ms_tenant AS \"idMsTenant\", tjr.id_ms_user AS \"idMsUser\", mt.tenant_code AS \"tenantCode\", ")
					.append(" TO_CHAR(tjr.process_start_time, 'YYYY-MM-DD HH24:MI:SS') AS \"processStartTime\",  TO_CHAR(tjr.process_finish_time, 'YYYY-MM-DD HH24:MI:SS' ) AS \"processFinishTime\", ")
					    .append(" CASE WHEN CAST(tjr.process_status AS text) = '0' THEN 'New' ")
			 	 	    .append(" WHEN CAST(tjr.process_status AS text) = '1' THEN 'In Progress' ")
			 	 	    .append(" WHEN CAST(tjr.process_status AS text) = '2' THEN 'Completed' ")
			 	 	    .append(" WHEN CAST(tjr.process_status AS text) = '3' THEN 'Cancelled' ")
			 	 	    .append(" WHEN CAST(tjr.process_status AS text) = '4' THEN 'Failed' ")
			 	 	    .append(" WHEN CAST(tjr.process_status AS text) = '5' THEN 'Deleted' ")
			 	 	    .append(" END AS \"processStatus\",  ")
			 	 	   
			 	 	  .append(" CASE WHEN process_status = 0 THEN '1' ")
			 	 	  .append(" ELSE '0' ")
			 	 	  .append(" END AS \"isNew\",  ")
					.append(" tjr.lov_job_type AS \"lovJobType\", mj.job_name \"jobName\", ")
					.append(" amu.login_id AS \"requestBy\", TO_CHAR(tjr.dtm_crt, 'YYYY-MM-DD HH24:MI:SS' ) AS \"dtmCrt\", ")
					.append(" CASE  ")
						.append(" WHEN process_finish_time IS null THEN CAST(datediff('s', CAST(tjr.process_start_time AS timestamp), CAST(NOW() AS timestamp)) AS text) ")
						.append(" ELSE CAST(datediff('s', CAST(tjr.process_start_time AS timestamp), CAST(tjr.process_finish_time AS timestamp)) AS text) ")
					.append(" END ")
					.append(" AS \"process\", ")
					.append(" row_number() OVER (ORDER BY tjr.id_job_result) AS rowNum ")
					.append(" FROM tr_job_result tjr ")
					.append(" JOIN ms_job mj ON mj.id_ms_job = tjr.id_ms_job  ")
					.append(" JOIN am_msuser amu ON amu.id_ms_user = tjr.id_ms_user  ")
					.append(" JOIN ms_tenant mt ON mt.id_ms_tenant = tjr.id_ms_tenant ")
					.append(" WHERE 1 = 1 ")
					.append(paramQueryTenant)
					.append(" ORDER BY tjr.dtm_crt DESC ")
				.append(" ) ")
				.append(" SELECT \"idJobResult\" , \"idMsJob\", \"dtmCrt\", ")
				.append(" \"processStartTime\", \"processFinishTime\", \"processStatus\", \"isNew\", ")
				.append(" \"jobName\", \"process\" ,\"requestBy\", \"idMsTenant\", \"tenantCode\" ")
				.append(" FROM jobList WHERE rowNum BETWEEN :min AND :max ");
		
		return this.managerDAO.selectForListString(ListJobResultBean.class, query.toString(), params, null);
	}
	
	private String constructParamList(Map<String, Object> params, Long idMsJob, String loginId, Short processStatus, Date requestDateStart, Date requestDateEnd) {
		StringBuilder query = new StringBuilder();
		
		if (null != idMsJob) {
			query.append(" AND mj.id_ms_job = :idMsJob ");
			params.put(MsJob.ID_JOB_HBM, idMsJob);
		}
		
		if (StringUtils.isNotBlank(loginId)) {
			query.append(" AND amu.login_id = :loginId ");
			params.put(AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId));
		}
		
		
		if (null != processStatus) {
			query.append(" AND tjr.process_status = :processStatus ");
			params.put(TrJobResult.JOB_PROCESS, processStatus);
		}
		
		 if(requestDateStart != null && requestDateEnd != null) {			
				query.append("AND tjr.dtm_crt >= :requestDateStart AND tjr.dtm_crt <= :requestDateEnd ");
				params.put("requestDateStart", requestDateStart);
				params.put("requestDateEnd", requestDateEnd);
			} else {
				 	 query.append("AND tjr.dtm_crt >= date_trunc('MONTH', now()) and tjr.dtm_crt <= now() ");
				 
			}
		
		return query.toString();
	}
	
	
	@Override
	public int getCountListJobRekonResult(Long idJob, String requestBy, 
			Short processStatus, Date requestDateStart, Date requestDateEnd) {

		Map<String, Object> params = new HashMap<>();
		
		String paramQueryTenant = this.constructParamList(params, idJob, requestBy, processStatus, requestDateStart, requestDateEnd);		

		StringBuilder query = new StringBuilder();
		query.append(" WITH jobList AS ( ")
					.append(" SELECT tjr.id_job_result AS \"idJobResult\", tjr.id_ms_job AS \"idMsJob\" , tjr.id_ms_tenant AS \"idMsTenant\", tjr.id_ms_user AS \"idMsUser\",  ")
					.append(" TO_CHAR(tjr.process_start_time, 'YYYY-MM-DD HH24:MI:SS') AS \"processStartTime\",  TO_CHAR(tjr.process_finish_time, 'YYYY-MM-DD HH24:MI:SS' ) AS \"processFinishTime\", CAST(tjr.process_status AS text) AS \"processStatus\", tjr.lov_job_type AS \"lovJobType\", mj.job_name \"jobName\", ")
					.append(" amu.login_id AS \"loginId\", TO_CHAR(tjr.dtm_crt, 'YYYY-MM-DD HH24:MI:SS' ) AS \"dtmCrt\", ")
					.append(" CASE  ")
						.append(" WHEN process_finish_time IS null THEN CAST(datediff('s', CAST(tjr.process_start_time AS timestamp), CAST(NOW() AS timestamp)) AS text) ")
						.append(" ELSE CAST(datediff('s', CAST(tjr.process_start_time AS timestamp), CAST(tjr.process_finish_time AS timestamp)) AS text) ")
					.append(" END ")
					.append(" AS \"process\", ")
					.append(" row_number() OVER (ORDER BY tjr.id_job_result) AS rowNum ")
					.append(" FROM tr_job_result tjr ")
					.append(" JOIN ms_job mj ON mj.id_ms_job = tjr.id_ms_job  ")
					.append(" JOIN am_msuser amu ON amu.id_ms_user = tjr.id_ms_user  ")
					.append(" JOIN ms_tenant mt ON mt.id_ms_tenant = tjr.id_ms_tenant ")
					.append(" WHERE 1 = 1 ")
					.append(paramQueryTenant)
					.append(" ORDER BY tjr.dtm_crt ")
				.append(" ) ")
				.append(" SELECT COUNT(\"idJobResult\") ")
				.append(" FROM jobList ");
		
		BigInteger totalData = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		
		return totalData.intValue();
	}

	@Override
	public TrJobResult getJobResultById(Long idJobResult) {
		Object[][] queryParams = { 
				{ Restrictions.eq(TrJobResult.ID_JOB_RESULT, idJobResult)}};
		return this.managerDAO.selectOne(TrJobResult.class, queryParams);
	}
	
}
