package com.adins.am.model;
// Generated 09-Sep-2021 22:49:32 by Hibernate Tools 5.2.12.Final

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.am.model.custom.UpdateableEntity;
import com.adins.esign.model.custom.ZipcodeCityBean;

/**
 * AmUserPersonalData generated by hbm2java
 */
@SuppressWarnings("squid:S107")
@Entity
@Table(name = "am_user_personal_data")
public class AmUserPersonalData extends UpdateableEntity implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	public static final String PHONE_HBM = "phone";
	private long idUserPersonalData;
	private AmMsuser amMsuser;
	private String gender;
	private Date dateOfBirth;
	private String placeOfBirth;
	private String email;
	private byte[] photoSelf;
	private byte[] idNoBytea;
	private byte[] phoneBytea;
	private byte[] addressBytea;
	private byte[] photoIdBytea;
	
	@Embedded
	private transient ZipcodeCityBean zipcodeBean;
	
	public AmUserPersonalData() {
	}

	public AmUserPersonalData(long idUserPersonalData, AmMsuser amMsuser, String usrCrt, Date dtmCrt) {
		this.idUserPersonalData = idUserPersonalData;
		this.amMsuser = amMsuser;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
	}

	public AmUserPersonalData(long idUserPersonalData, AmMsuser amMsuser, String usrCrt, Date dtmCrt, String usrUpd,
			Date dtmUpd, String gender, Date dateOfBirth, byte[] photoSelf, String placeOfBirth, String email,
			byte[] idNoBytea, byte[] phoneBytea, byte[] addressBytea, byte[] photoIdBytea) {
		this.idUserPersonalData = idUserPersonalData;
		this.amMsuser = amMsuser;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
		this.gender = gender;
		this.dateOfBirth = dateOfBirth;
		this.photoSelf = photoSelf;
		this.placeOfBirth = placeOfBirth;
		this.email = email;
		this.idNoBytea = idNoBytea;
		this.phoneBytea = phoneBytea;
		this.addressBytea = addressBytea;
		this.photoIdBytea = photoIdBytea;
	}
	
	public AmUserPersonalData(AmUserPersonalData clone) {
        this.idUserPersonalData = clone.idUserPersonalData;
        this.amMsuser = clone.amMsuser;
        this.gender = clone.gender;
        this.dateOfBirth = clone.dateOfBirth ;
        this.placeOfBirth = clone.placeOfBirth;
        this.email = clone.email;
        this.photoSelf = (clone.photoSelf != null) ? clone.photoSelf.clone() : null;
        this.idNoBytea = (clone.idNoBytea != null) ? clone.idNoBytea.clone() : null;
        this.phoneBytea = (clone.phoneBytea != null) ? clone.phoneBytea.clone() : null;
        this.addressBytea = (clone.addressBytea != null) ? clone.addressBytea.clone() : null;
        this.photoIdBytea = (clone.photoIdBytea != null) ? clone.photoIdBytea.clone() : null;
        this.zipcodeBean = clone.zipcodeBean; 
        this.usrCrt = clone.usrCrt;
		this.dtmCrt = clone.dtmCrt;
		this.usrUpd = clone.usrUpd;
		this.dtmUpd = clone.dtmUpd;
    }
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_user_personal_data", unique = true, nullable = false)
	public long getIdUserPersonalData() {
		return this.idUserPersonalData;
	}

	public void setIdUserPersonalData(long idUserPersonalData) {
		this.idUserPersonalData = idUserPersonalData;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@Column(name = "gender", length = 2)
	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "date_of_birth", length = 13)
	public Date getDateOfBirth() {
		return this.dateOfBirth;
	}

	public void setDateOfBirth(Date dateOfBirth) {
		this.dateOfBirth = dateOfBirth;
	}

	@Column(name = "photo_self")
	public byte[] getPhotoSelf() {
		return this.photoSelf;
	}

	public void setPhotoSelf(byte[] photoSelf) {
		this.photoSelf = photoSelf;
	}

	@Column(name = "place_of_birth", length = 100)
	public String getPlaceOfBirth() {
		return this.placeOfBirth;
	}

	public void setPlaceOfBirth(String placeOfBirth) {
		this.placeOfBirth = placeOfBirth;
	}


	@Column(name = "email", length = 80)
	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	public ZipcodeCityBean getZipcodeBean() {
		return zipcodeBean;
	}

	public void setZipcodeBean(ZipcodeCityBean zipcodeBean) {
		this.zipcodeBean = zipcodeBean;
	}
	
	@Column(name = "id_no_bytea")
	public byte[] getIdNoBytea() {
		return this.idNoBytea;
	}

	public void setIdNoBytea(byte[] idNoBytea) {
		this.idNoBytea = idNoBytea;
	}

	@Column(name = "phone_bytea")
	public byte[] getPhoneBytea() {
		return this.phoneBytea;
	}

	public void setPhoneBytea(byte[] phoneBytea) {
		this.phoneBytea = phoneBytea;
	}
	
	@Column(name = "address_bytea")
	public byte[] getAddressBytea() {
		return this.addressBytea;
	}

	public void setAddressBytea(byte[] addressBytea) {
		this.addressBytea = addressBytea;
	}

	@Column(name = "photo_id_bytea")
	public byte[] getPhotoIdBytea() {
		return this.photoIdBytea;
	}

	public void setPhotoIdBytea(byte[] photoIdBytea) {
		this.photoIdBytea = photoIdBytea;
	}

}
