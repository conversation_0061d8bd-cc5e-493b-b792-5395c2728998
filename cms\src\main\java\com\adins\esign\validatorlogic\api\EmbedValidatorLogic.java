package com.adins.esign.validatorlogic.api;

import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface EmbedValidatorLogic {
	EmbedMsgBean validateEmbedMessage(String encryptedMsg, AuditContext audit);
	EmbedMsgBeanV2 validateEmbedMessageV2(String encryptedMsg, String tenantCode, boolean userMustExists, AuditContext audit);
}
