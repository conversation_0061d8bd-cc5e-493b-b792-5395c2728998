package com.adins.esign.businesslogic.api.embed;

import com.adins.esign.webservices.model.embed.GetSignerDetailEmbedRequest;
import com.adins.esign.webservices.model.embed.GetSignerDetailEmbedResponse;
import com.adins.esign.webservices.model.embed.SentOtpSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.SentOtpSigningEmbedResponse;
import com.adins.esign.webservices.model.embed.SignerDataVerificationEmbedRequest;
import com.adins.esign.webservices.model.embed.SignerDataVerificationEmbedResponse;
import com.adins.esign.webservices.model.embed.VerifyLivenessFaceCompareEmbedRequest;
import com.adins.esign.webservices.model.embed.VerifyLivenessFaceCompareEmbedResponse;
import com.adins.esign.webservices.model.embed.VerifyOtpSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.VerifyOtpSigningEmbedResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface UserEmbedLogic {

	SignerDataVerificationEmbedResponse signerDataVerificationEmbed(SignerDataVerificationEmbedRequest request, AuditContext audit);
	GetSignerDetailEmbedResponse getSignerDetailEmbed(GetSignerDetailEmbedRequest request, AuditContext audit);
	VerifyLivenessFaceCompareEmbedResponse verifyLivenessFaceCompareEmbed(VerifyLivenessFaceCompareEmbedRequest request, AuditContext audit);
	SentOtpSigningEmbedResponse sentOtpSigningEmbed(SentOtpSigningEmbedRequest request, AuditContext audit);
	VerifyOtpSigningEmbedResponse verifyOtpSigningEmbed(VerifyOtpSigningEmbedRequest request, AuditContext audit);
}
