package com.adins.esign.webservices.frontend.endpoint;

import java.text.ParseException;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.NoSuchMessageException;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.webservices.frontend.api.SaldoService;
import com.adins.esign.webservices.model.BalanceEmbedRequest;
import com.adins.esign.webservices.model.BalanceRequest;
import com.adins.esign.webservices.model.BalanceResponse;
import com.adins.esign.webservices.model.CheckThresholdRequest;
import com.adins.esign.webservices.model.CheckThresholdResponse;
import com.adins.esign.webservices.model.DownloadListBalanceMutationEmbedRequest;
import com.adins.esign.webservices.model.DownloadListBalanceMutationRequest;
import com.adins.esign.webservices.model.DownloadListBalanceMutationResponse;
import com.adins.esign.webservices.model.ExtendTopUpBalanceRequest;
import com.adins.esign.webservices.model.GetListBalanceMutationEmbedRequest;
import com.adins.esign.webservices.model.GetListTopupBalanceRequest;
import com.adins.esign.webservices.model.GetListTopupBalanceResponse;
import com.adins.esign.webservices.model.ListBalanceHistoryRequest;
import com.adins.esign.webservices.model.ListBalanceHistoryResponse;
import com.adins.esign.webservices.model.ListBalanceTenantRequest;
import com.adins.esign.webservices.model.ListBalanceTenantResponse;
import com.adins.esign.webservices.model.ListBalanceTypeByVendorAndTenantRequest;
import com.adins.esign.webservices.model.ListBalanceTypeByVendorAndTenantResponse;
import com.adins.esign.webservices.model.ListBalanceVendoroftenantRequest;
import com.adins.esign.webservices.model.ListBalanceVendoroftenantResponse;
import com.adins.esign.webservices.model.SaldoConfigurationRequest;
import com.adins.esign.webservices.model.SaldoConfigurationResponse;
import com.adins.esign.webservices.model.SignBalanceAvailabilityRequest;
import com.adins.esign.webservices.model.SignBalanceAvailabilityResponse;
import com.adins.esign.webservices.model.UpdateBalanceTenantRequest;
import com.adins.esign.webservices.model.UpdateBalanceTenantResponse;
import com.adins.esign.webservices.model.UpdateRefNumberRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;

import io.swagger.annotations.Api;

@Component
@Path("/saldo")
@Api(value = "SaldoService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericSaldoServiceEndpoint implements SaldoService{
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericSaldoServiceEndpoint.class);
	
	@Autowired SaldoLogic saldoLogic;

	@Override
	@POST
	@Path("/s/balance")
	public BalanceResponse getBalance(BalanceRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		return saldoLogic.getBalance(request, audit);
	}
	
	@Override
	@POST
	@Path("/balanceEmbed")
	public BalanceResponse getBalanceEmbed(BalanceEmbedRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		return saldoLogic.getBalanceEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/s/listBalanceMutation")
	public ListBalanceHistoryResponse getListBalanceHistory(ListBalanceHistoryRequest request) throws ParseException {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		return saldoLogic.getListBalanceMutation(request, audit);
	}
	
	@Override
	@POST
	@Path("/listBalanceMutationEmbed")
	public ListBalanceHistoryResponse getListBalanceMutationEmbed(GetListBalanceMutationEmbedRequest request)
			throws ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return saldoLogic.getListBalanceMutationEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/s/topup")
	public SaldoConfigurationResponse configureSaldo(SaldoConfigurationRequest request) throws ParseException {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		try {
			return saldoLogic.configureSaldo(request, audit);
		} catch (IllegalAccessException | NoSuchMessageException | ParseException e) {
			LOG.error("Configure saldo error", e);
			SaldoConfigurationResponse response = new SaldoConfigurationResponse();
			Status status = new Status();
			status.setCode(200);
			status.setMessage(e.getMessage());
			response.setStatus(status);
			return response;
		}
	}
	
	@Override
	@POST
	@Path("/s/getBalanceMutationFile")
	public DownloadListBalanceMutationResponse downloadListBalanceMutation(DownloadListBalanceMutationRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		try {
			return saldoLogic.downloadListBalanceMutation(request, audit);
		} catch (ParseException e) {
			LOG.error("Download list balance mutation error", e);
			DownloadListBalanceMutationResponse response = new DownloadListBalanceMutationResponse();
			Status status = new Status();
			status.setCode(200);
			status.setMessage(e.getMessage());
			response.setStatus(status);
			return response;
		}
	}
	
	@Override
	@POST
	@Path("/getBalanceMutationEmbedFile")
	public DownloadListBalanceMutationResponse downloadListBalanceMutationEmbed(DownloadListBalanceMutationEmbedRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		try {
			return saldoLogic.downloadListBalanceMutationEmbed(request, audit);
		} catch (ParseException e) {
			LOG.error("Download list balance mutation embed error", e);
			DownloadListBalanceMutationResponse response = new DownloadListBalanceMutationResponse();
			Status status = new Status();
			status.setCode(200);
			status.setMessage(e.getMessage());
			response.setStatus(status);
			return response;
		}
	}

	@Override
	@POST
	@Path("/s/getListBalanceTenant")
	public ListBalanceTenantResponse getListBalanceTenant(ListBalanceTenantRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		return saldoLogic.getListBalanceTenant(request, audit);
	}

	@Override
	@POST
	@Path("/s/updateBalanceTenant")
	public UpdateBalanceTenantResponse updateBalanceTenant(UpdateBalanceTenantRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		return saldoLogic.updateBalanceTenant(request, audit);
	}

	@Override
	@POST
	@Path("/getListBalanceVendoroftenant")
	public ListBalanceVendoroftenantResponse getListBalanceVendoroftenant(ListBalanceVendoroftenantRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return saldoLogic.getListBalanceVendoroftenant(request, audit);
	}

	@Override
	@POST
	@Path("/checkThreshold")
	public CheckThresholdResponse checkThreshold(CheckThresholdRequest request) throws ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return saldoLogic.checkThreshold(request, audit);
	}

	@Override
	@POST
	@Path("/s/getListBalanceType")
	public ListBalanceTypeByVendorAndTenantResponse getListBalanceTypeByVendorAndTenant(
			ListBalanceTypeByVendorAndTenantRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return saldoLogic.getListBalanceTypeByVendorAndTenant(request, audit);
	}

	@Override
	@POST
	@Path("/s/SignBalanceAvailability")
	public SignBalanceAvailabilityResponse signBalanceAvailability(SignBalanceAvailabilityRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return saldoLogic.getSignBalanceAvailability(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/balanceAllTenant")
	public BalanceResponse getBalanceAllTenant(BalanceRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		return saldoLogic.getBalanceAllTenant(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/listBalanceMutationAllTenant")
	public ListBalanceHistoryResponse getListBalanceMutationAllTenant(ListBalanceHistoryRequest request) throws ParseException {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		return saldoLogic.getListBalanceMutationAllTenant(request, audit);
	}

	@Override
	@POST
	@Path("/s/getListTopupBalance")
	public GetListTopupBalanceResponse getListTopupBalance(GetListTopupBalanceRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return saldoLogic.getListTopupBalance(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/saveExtendBalanceTopUp")
	public MssResponseType extendTopupBalance(ExtendTopUpBalanceRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return saldoLogic.extendTopupBalance(request, audit);
	}

	@Override
	@POST
	@Path("/s/updateBalanceByRefNumber")
	public MssResponseType updateRefNumber(UpdateRefNumberRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return saldoLogic.updateRefNumber(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/getBalanceMutationAllTenantFile")
	public DownloadListBalanceMutationResponse downloadListBalanceMutationAllTenant(DownloadListBalanceMutationRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		try {
			return saldoLogic.downloadListBalanceMutationAllTenant(request, audit);
		} catch (ParseException e) {
			LOG.error("Download list balance mutation error", e);
			DownloadListBalanceMutationResponse response = new DownloadListBalanceMutationResponse();
			Status status = new Status();
			status.setCode(200);
			status.setMessage(e.getMessage());
			response.setStatus(status);
			return response;
		}
	}
	
}
