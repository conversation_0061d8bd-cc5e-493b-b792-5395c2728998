package com.adins.am.businesslogic;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.framework.persistence.dao.api.AuditLog;
import com.adins.framework.persistence.dao.api.ManagerDAO;

@Component
@Transactional
public abstract class BaseDaoHbn {

	@Autowired
	protected ManagerDAO managerDAO;
	@Autowired
	protected AuditLog auditManager;
	@Autowired
	protected PersonalDataEncryptionLogic personalDataEncLogic;
	
	public ManagerDAO getManagerDAO() {
		return managerDAO;
	}

	

}
