package com.adins.esign.webservices.external.api;

import com.adins.esign.webservices.model.CheckRegistrationRequest;
import com.adins.esign.webservices.model.CheckRegistrationResponse;
import com.adins.esign.webservices.model.RequestSentOtpSigningRequest;
import com.adins.esign.webservices.model.RequestSentOtpSigningResponse;
import com.adins.esign.webservices.model.external.CheckVerificationStatusExternalRequest;
import com.adins.esign.webservices.model.external.CheckVerificationStatusExternalResponse;
import com.adins.esign.webservices.model.external.DownloadUserCertificateRequest;
import com.adins.esign.webservices.model.external.DownloadUserCertificateResponse;
import com.adins.esign.webservices.model.external.GeneratInvLinkExternalRequest;
import com.adins.esign.webservices.model.external.GeneratInvLinkExternalResponse;
import com.adins.esign.webservices.model.external.GetActivationLinkRequest;
import com.adins.esign.webservices.model.external.GetActivationLinkResponse;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.external.RegisterExternalResponse;
import com.adins.esign.webservices.model.external.UpdateDataSignerExternalRequest;
import com.adins.esign.webservices.model.external.UpdateDataSignerExternalResponse;

public interface UserExternalService {
	RegisterExternalResponse register(RegisterExternalRequest request);
	RequestSentOtpSigningResponse requestSentOtpSigning(RequestSentOtpSigningRequest request);
	CheckRegistrationResponse checkRegistration(CheckRegistrationRequest request);
	GeneratInvLinkExternalResponse generateInvLink(GeneratInvLinkExternalRequest request) throws Exception;
	CheckVerificationStatusExternalResponse checkVerificationStatusExternal(CheckVerificationStatusExternalRequest request);
	GetActivationLinkResponse getActivationLinkExternal(GetActivationLinkRequest request);
	DownloadUserCertificateResponse downloadUserCertificate(DownloadUserCertificateRequest request);
	UpdateDataSignerExternalResponse updateDataSignerExternal(UpdateDataSignerExternalRequest request);
}
