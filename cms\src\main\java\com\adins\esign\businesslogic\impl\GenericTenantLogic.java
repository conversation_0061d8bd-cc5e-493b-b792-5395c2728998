package com.adins.esign.businesslogic.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.ContentDisposition;
import org.apache.cxf.jaxrs.ext.multipart.MultipartBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.businesslogic.api.MenuLogic;
import com.adins.am.businesslogic.api.RoleLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsmenu;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.OfficeLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.constants.MediaType;
import com.adins.esign.constants.enums.NotificationSendingPoint;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsNotificationtypeoftenant;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrFaceVerify;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.custom.BalanceVendoroftenantBean;
import com.adins.esign.model.custom.CallbackActivationRequestBean;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.FaceMatchLivenessNodefluxRequest;
import com.adins.esign.model.custom.FaceMatchLivenessNodefluxResponse;
import com.adins.esign.model.custom.FaceMatchLivenessRequest;
import com.adins.esign.model.custom.GenerateNodefluxTokenBean;
import com.adins.esign.model.custom.GenerateTokenNodefluxResponse;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.model.custom.TenantCallbackBean;
import com.adins.esign.model.custom.TenantCallbackRequestBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.InvitationLinkValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.webservices.model.AddTenantRequest;
import com.adins.esign.webservices.model.AddTenantResponse;
import com.adins.esign.webservices.model.CheckLivenessFaceCompareServiceRequest;
import com.adins.esign.webservices.model.CheckLivenessFaceCompareServiceResponse;
import com.adins.esign.webservices.model.EditTenantRequest;
import com.adins.esign.webservices.model.EditTenantResponse;
import com.adins.esign.webservices.model.FaceVerifyRequest;
import com.adins.esign.webservices.model.FaceVerifyResponse;
import com.adins.esign.webservices.model.GetAutomaticStampingAfterSignRequest;
import com.adins.esign.webservices.model.GetAutomaticStampingAfterSignResponse;
import com.adins.esign.webservices.model.GetAvailableSendingPointInvitationRequest;
import com.adins.esign.webservices.model.GetAvailableSendingPointInvitationResponse;
import com.adins.esign.webservices.model.GetAvailableSendingPointRequest;
import com.adins.esign.webservices.model.GetAvailableSendingPointResponse;
import com.adins.esign.webservices.model.GetListTenantRequest;
import com.adins.esign.webservices.model.GetListTenantResponse;
import com.adins.esign.webservices.model.GetSmsDeliverySettingRequest;
import com.adins.esign.webservices.model.GetSmsDeliverySettingResponse;
import com.adins.esign.webservices.model.GetStatusEmailServiceTenantRequest;
import com.adins.esign.webservices.model.GetStatusEmailServiceTenantResponse;
import com.adins.esign.webservices.model.GetStatusStampingOtomatisTenantRequest;
import com.adins.esign.webservices.model.GetStatusStampingOtomatisTenantResponse;
import com.adins.esign.webservices.model.GetTenantRekonRequest;
import com.adins.esign.webservices.model.GetTenantRekonResponse;
import com.adins.esign.webservices.model.GetUploadUrlRequest;
import com.adins.esign.webservices.model.GetUploadUrlResponse;
import com.adins.esign.webservices.model.LivenessEmbedRequest;
import com.adins.esign.webservices.model.LivenessRequest;
import com.adins.esign.webservices.model.LivenessResponse;
import com.adins.esign.webservices.model.TenantDetailRequest;
import com.adins.esign.webservices.model.TenantDetailResponse;
import com.adins.esign.webservices.model.TenantSettingsEmbedRequest;
import com.adins.esign.webservices.model.TenantSettingsRequest;
import com.adins.esign.webservices.model.TenantSettingsResponse;
import com.adins.esign.webservices.model.TestTenantCallbackRequest;
import com.adins.esign.webservices.model.TestTenantCallbackResponse;
import com.adins.esign.webservices.model.TryTenantCallbackRequest;
import com.adins.esign.webservices.model.TryTenantCallbackResponse;
import com.adins.esign.webservices.model.UpdateDeliverySettingRequest;
import com.adins.esign.webservices.model.UpdateDeliverySettingResponse;
import com.adins.esign.webservices.model.UpdateTenantSettingsRequest;
import com.adins.esign.webservices.model.UpdateTenantSettingsResponse;
import com.adins.esign.webservices.model.VerifySelfieResponse;
import com.adins.exceptions.FormatException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.FormatException.ReasonFormat;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.exceptions.UserException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.AuditDataType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.tool.password.PasswordHash;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;

@Transactional
@Component
public class GenericTenantLogic extends BaseLogic implements TenantLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericTenantLogic.class);
    @Autowired private Gson gson;

    private String headerContentType = HttpHeaders.CONTENT_TYPE;
    private String headerMultipart = javax.ws.rs.core.MediaType.MULTIPART_FORM_DATA;
    
    // Hardcoded berdasarkan panjang max character ms_tenant.email_reminder_dest
    private static final Integer EMAIL_REMINDER_DEST_MAX_LENGTH = 300;
    private static final String EMAIL_REMINDER_DEST_SEPARATOR = ",";
    
    private static final String BAL_REMINDER_RECEIVER_LABEL = "Balance Reminder Email Receiver";
    private static final String TENANT_CODE_LABEL = "Tenant code";
    private static final String PREFIX_HTTP = "http://";
    private static final String PREFIX_HTTPS = "https://";
    private static final String CALLBACK_DUMMY_EMAIL = "<EMAIL>";
    
    private static final String MSG_ACTIVE_DEL = "businesslogic.global.activeDel";
    private static final String MSG_DATA_NOT_FOUND	= "businesslogic.global.datanotfound";
    
	@Autowired private UserLogic userLogic;
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private RoleLogic roleLogic;
	@Autowired private MenuLogic menuLogic;
	@Autowired private OfficeLogic officeLogic;
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private BalanceValidatorLogic balanceValidatorLogic;
	@Autowired private InvitationLinkValidatorLogic invLinkValidatorLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	
	@Value("${nodeflux.authkey}") private String authKeyNodeflux;
	@Value("${nodeflux.xtimestamp}") private String xtimestampNodeflux;
	@Value("${nodeflux.facematchliveness.url}") private String urlFaceMatchNodeflux;

	@Override
	public MsTenant getTenantById(long idMsTenant, AuditContext callerId) {		
		return daoFactory.getTenantDao().getTenantById(idMsTenant);
	}

	@Override
	public List<MsTenant> getListTenant(AuditContext callerId) {
		return daoFactory.getTenantDao().getListTenant();
	}
	
	@Override
	public List<MsTenant> getListTenantByUser(AmMsuser user, AuditContext callerId) {
		return daoFactory.getTenantDao().getListTenantByUser(user);
	}

	@Override
	public MsTenant getTenantByCode(String tenantCode, AuditContext callerId) {
		return daoFactory.getTenantDao().getTenantByCode(tenantCode);
	}

	@Override
	public MsUseroftenant getUseroftenant(AmMsuser user, MsTenant tenant, AuditContext callerId) {
		return daoFactory.getTenantDao().getUseroftenantByUserTenant(user, tenant);
	}

	@Override
	public MsUseroftenant getUseroftenant(String loginId, String tenantCode, AuditContext callerId) {
		return daoFactory.getTenantDao().getUseroftenantByLoginIdTenantCode(loginId, tenantCode);
	}

	@Override
	public GetListTenantResponse getListTenantCodeAndName(GetListTenantRequest request, AuditContext audit) {
		GetListTenantResponse response = new GetListTenantResponse();
		response.setTenantList(daoFactory.getTenantDao().getListTenant(request.getTenantName(), request.getStatus()));
		return response;
	}

	@Override
	public String checkNotificationType(String tenantCode, String loginId, String vendorCode, AuditContext audit) {
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(loginId, vendorCode);		
		if ("1".equals(vru.getEmailService())) {
			// Notifikasi akan dikirimkan melalui SMS ke no HP user tersebut
			return GlobalVal.NOTIF_TYPE_SMS;
		}
		
		// Notifikasi akan dikirimkan ke email pribadi user tersebut
		return GlobalVal.NOTIF_TYPE_EMAIL;
	}

	@Override
	public LivenessResponse getTenantLiveness(LivenessRequest request, AuditContext audit) {
		return this.getTenantLive(request, audit);
	}
	
	@Override
	public LivenessResponse getTenantLivenessEmbed(LivenessEmbedRequest request, AuditContext audit) {
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);

		LivenessRequest livenessRequest = new LivenessRequest();
		livenessRequest.setTenantCode(msgBean.getTenantCode());
		audit.setCallerId(msgBean.getEmail());
		
		return this.getTenantLive(livenessRequest, audit);
	}
	
	private LivenessResponse getTenantLive(LivenessRequest request, AuditContext audit) {
		LivenessResponse response = new LivenessResponse();
		Status status = new Status();
		if (StringUtils.isBlank(request.getTenantCode())) {
			status.setCode(200);
			status.setMessage(getMessage("businesslogic.vendor.emptytenantcode", null, audit));
			response.setStatus(status);
			return response;
		}
		
		MsTenant tenant = this.getTenantByCode(request.getTenantCode(), audit);
		if (null == tenant) {
			status.setCode(200);
			status.setMessage(getMessage("businesslogic.saldo.tenantnotexist", new Object[] {request.getTenantCode()}, audit));
			response.setStatus(status);
			return response;
		}
		
		status.setCode(200);
		status.setMessage(getMessage("businesslogic.tenant.invalidtenant", null, audit));
		response.setStatus(status);
		
		AmMsuser user = userLogic.getUserByLoginId(audit.getCallerId());
		List<MsTenant> tenantList = this.getListTenantByUser(user, audit);
		for (int i = 0; i < tenantList.size(); i++) {
			if (tenantList.get(i).getTenantCode().equals(tenant.getTenantCode())) {
				response.setStatusFaceVerify(tenant.getFaceVerifyService());
				status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
				status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
				response.setStatus(status);
			}
		}
		
		return response;
	}

	@Override
	public FaceVerifyResponse getLivenessCheck(FaceVerifyRequest request, AuditContext audit) throws IOException {
		FaceVerifyResponse response = new FaceVerifyResponse();
		Status status = new Status();
		if (StringUtils.isBlank(request.getPhotoBase64())) {
			status.setCode(200);
			status.setMessage(this.messageSource.getMessage("businesslogic.liveness.emptyphotobase64", 
					new Object[] {}, this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
			return response;
		}
		
		LivenessRequest livenessRequest = new LivenessRequest();
		AuditDataType auditDataType = new AuditDataType();
		auditDataType.setCallerId(audit.getCallerId());
		livenessRequest.setAudit(auditDataType);
		livenessRequest.setTenantCode(request.getTenantCode());
		
		LivenessResponse livenessReponse = this.getTenantLiveness(livenessRequest, audit);
		if (GlobalVal.STATUS_CODE_SUCCESS != livenessReponse.getStatus().getCode()) {
			status = livenessReponse.getStatus();
			response.setStatus(status);
			return response;
		}
		
		if (livenessReponse.getStatusFaceVerify().equals(GlobalVal.FACE_VERIFY_SERVICE_INACTIVE)) {
			status.setCode(200);
			status.setMessage(getMessage("businesslogic.tenant.faceverifyserviceinactive", new Object[] {}, audit));
			response.setStatus(status);
			return response;
		}
		
		MsTenant tenant = this.getTenantByCode(request.getTenantCode(), audit);
		AmMsuser user = userLogic.getUserByLoginId(audit.getCallerId());
	
		PersonalDataBean userPersonalData = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		String tenantCode = tenant.getTenantCode();
		String refNumber = StringUtils.isNotBlank(request.getRefNumber()) ? request.getRefNumber() : StringUtils.EMPTY;
		String nik = userPersonalData.getIdNoRaw();
		byte[] idPhotoByteArray = this.convertImageString(request.getPhotoBase64());
		String fileNameSelfieReal = cloudStorageLogic.storeTransactionSelfie(tenantCode, nik, refNumber, idPhotoByteArray);
		String fileNameSelfieOss = String.format(GlobalVal.PERSONAL_SELFIE_FILENAME_FORMAT, nik);
		
		String urlLivenessFacecompare = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_URL_LIVENESS_FACE_COMPARE, audit);
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerContentType, headerMultipart);
		WebClient client = WebClient.create(urlLivenessFacecompare).headers(mapHeader);

		List<Attachment> atts = new LinkedList<>();

		if (null != request.getPhotoBase64()) {
			ContentDisposition cdImg1 = new ContentDisposition("form-data; name=\"img1\"; filename=\""+fileNameSelfieReal+"\"");
			String photoClean = request.getPhotoBase64().replace(GlobalVal.IMG_JPG_PREFIX, "");
			byte[] dataImagePhoto = Base64.getDecoder().decode(photoClean);
			atts.add(new Attachment("img1", new ByteArrayInputStream(dataImagePhoto), cdImg1));
		}
		
		if (null != userPersonalData.getSelfPhotoRaw()) {
			ContentDisposition cdImg2 = new ContentDisposition("form-data; name=\"img2\"; filename=\""+fileNameSelfieOss+"\"");
			atts.add(new Attachment("img2", new ByteArrayInputStream(userPersonalData.getSelfPhotoRaw()), cdImg2));
		}
		
		if (null != tenant.getLivenessKey()) {
			ContentDisposition cdKey = new ContentDisposition("form-data; name=\"key\"");
			atts.add(new Attachment("key", new ByteArrayInputStream(tenant.getLivenessKey().getBytes()), cdKey));
		}
		
		if (null != tenant.getTenantCode()) {
			ContentDisposition cdTenantCode = new ContentDisposition("form-data; name=\"tenant_code\"");
			atts.add(new Attachment("tenant_code", new ByteArrayInputStream(tenant.getTenantCode().getBytes()), cdTenantCode));
		}
		
		if (null != nik) {
			ContentDisposition cdNik = new ContentDisposition("form-data; name=\"nik\"");
			atts.add(new Attachment("nik", new ByteArrayInputStream(nik.getBytes()), cdNik));
		}
		
		MultipartBody body = new MultipartBody(atts);
		Date date = new Date();
		Response clientResponse = client.post(body);

		InputStreamReader isReader = new InputStreamReader((InputStream) clientResponse.getEntity());
		String result = StringUtils.EMPTY;
		try {
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			LOG.error("Error on Liveness check result", e);
		}
		LOG.info("Liveness check result: {}", result);

		TrFaceVerify faceVerify = new TrFaceVerify();
		VerifySelfieResponse verifySelfieResponse = gson.fromJson(result, VerifySelfieResponse.class);
		boolean isCompare = Boolean.parseBoolean(verifySelfieResponse.getResult()[0].getFaceCompare().getCompare());
		boolean isLive = Boolean.parseBoolean(verifySelfieResponse.getResult()[0].getFaceLiveness().getLive());
		if (verifySelfieResponse.getStatus().equalsIgnoreCase(GlobalVal.STATUS_VERIFY_SUCCESS) && isCompare && isLive) {
			response.setLivenessCheck(GlobalVal.STATUS_LIVENESS_CHECK_SUCCESS);
			status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
			status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
			response.setStatus(status);
			faceVerify.setVerifyResult(GlobalVal.STATUS_VERIFY_SUCCESS);
		} else {
			response.setLivenessCheck(GlobalVal.STATUS_LIVENESS_CHECK_FAILED);
			status.setCode(StatusCode.LIVENESS_CHECK_FAILED);
			if (GlobalVal.FACE_VERIFY_SERVICE_ERROR_SELFIE_SPOOF.equalsIgnoreCase(verifySelfieResponse.getError()) ||
					StringUtils.isBlank(verifySelfieResponse.getError())) {
				status.setMessage(this.messageSource.getMessage("businesslogic.tenant.faceverifyserviceerrorselfiespoof", 
						new Object[] {}, this.retrieveLocaleAudit(audit)));
			} else {
				status.setMessage(verifySelfieResponse.getError());
			}
			response.setStatus(status);
			faceVerify.setVerifyResult(GlobalVal.STATUS_VERIFY_FAILED + " - " + verifySelfieResponse.getError());
		}
		
		faceVerify.setLiveCheckDate(date);
		faceVerify.setAmMsuser(user);
		faceVerify.setUserPhoto(fileNameSelfieReal.getBytes());
		faceVerify.setUsrCrt(user.getLoginId());
		faceVerify.setDtmCrt(new Date());
		
		TrDocumentH documentHeader = null;
		TrDocumentD documentDetail = null;
		if (StringUtils.isNotBlank(refNumber)) {
			documentHeader = daoFactory.getDocumentDao().getDocumentHeaderByRefNo(refNumber);
			documentDetail = daoFactory.getDocumentDao().getDocumentDetailByDocumentHeaderId(documentHeader.getIdDocumentH());

			faceVerify.setTrDocumentH(documentHeader);
			faceVerify.setTrDocumentD(documentDetail);
		}
		
		daoFactory.getFaceVerifyDao().insertFaceVerify(faceVerify);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_FVRF);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UFVRF);
		int qty = -1;
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		String notes = user.getFullName() + " - " + faceVerify.getVerifyResult();
		saldoLogic.insertBalanceMutation(null, documentHeader, documentDetail, balanceType, trxType, 
				tenant, vendor, date, refNumber, qty, String.valueOf(nextTrxNo), user, notes, null, audit);
		
		return response;
	}
	
	private byte[] convertImageString(String image) {
		String prefix = "";
		if (image.startsWith(GlobalVal.IMG_JPEG_PREFIX)) {
			prefix = GlobalVal.IMG_JPEG_PREFIX;
		} else if (image.startsWith(GlobalVal.IMG_JPG_PREFIX)) {
			prefix = GlobalVal.IMG_JPG_PREFIX;
		} else if (image.startsWith(GlobalVal.IMG_PNG_PREFIX)) {
			prefix = GlobalVal.IMG_PNG_PREFIX;
		}
		String imageString = image.substring(prefix.length());
		return Base64.getDecoder().decode(imageString);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public TenantSettingsResponse getTenantSettings(TenantSettingsRequest request, AuditContext audit) {

		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {TENANT_CODE_LABEL, request.getTenantCode()}, audit), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		if (GlobalVal.TENANT_SETTING_REF_NO_LABEL.equalsIgnoreCase(request.getParam())) {
			TenantSettingsResponse response = new TenantSettingsResponse();
			response.setRefNumberLabel(tenant.getRefNumberLabel());
			return response;
		}
		
		Map<String, Object> bean = gson.fromJson(tenant.getThresholdBalance(), Map.class);
		
		TenantSettingsResponse response = new TenantSettingsResponse();
		response.setTenantCode(tenant.getTenantCode());
		response.setTenantName(tenant.getTenantName());
		response.setRefNumberLabel(tenant.getRefNumberLabel());
		response.setThresholdBalance(bean);
		response.setUploadUrl(tenant.getUploadUrl());
		response.setActivationCallbackUrl(tenant.getActivationCallbackUrl());
		response.setUseWaMessage(tenant.getUseWaMessage());
		response.setClientCallbackUrl(tenant.getClientCallbackUrl());
		response.setClientActivationRedirectUrl(tenant.getClientActivationRedirectUrl());
		response.setClientSigningRedirectUrl(tenant.getClientSigningRedirectUrl());
		response.setEmailReminderDest(Arrays.asList(tenant.getEmailReminderDest().split(EMAIL_REMINDER_DEST_SEPARATOR)));
		return response;
	}
	
	private void validateUpdateTenantSettingsRequest(UpdateTenantSettingsRequest request, AuditContext audit) {
		// Validasi URL callback aktivasi
		if (StringUtils.isNotBlank(request.getActivationCallbackUrl()) && !StringUtils.startsWith(request.getActivationCallbackUrl(), PREFIX_HTTPS) && !StringUtils.startsWith(request.getActivationCallbackUrl(), PREFIX_HTTP)) {
			throw new TenantException(getMessage("businesslogic.tenant.invalidcallbackurl", null, audit), ReasonTenant.INVALID_CALLBACK_URL);
		}
		
		// Validasi URL upload
		if (StringUtils.isNotBlank(request.getUploadUrl()) && !StringUtils.startsWith(request.getUploadUrl(), PREFIX_HTTPS) && !StringUtils.startsWith(request.getUploadUrl(), PREFIX_HTTP)) {
			throw new TenantException(getMessage("businesslogic.tenant.invaliduploadurl", null, audit), ReasonTenant.INVALID_CALLBACK_URL);
		}
		
		// Validasi URL Callback 
		if (StringUtils.isNotBlank(request.getClientCallbackUrl()) && !StringUtils.startsWith(request.getClientCallbackUrl(), PREFIX_HTTPS) && !StringUtils.startsWith(request.getClientCallbackUrl(), PREFIX_HTTP)) {
			throw new TenantException(getMessage("businesslogic.tenant.invalidcallbackurl", null, audit), ReasonTenant.INVALID_CLIENT_URL);
		}
		
		// Validasi URL Redirect Activation
		if (StringUtils.isNotBlank(request.getClientActivationRedirectUrl()) && !StringUtils.startsWith(request.getClientActivationRedirectUrl(), PREFIX_HTTPS) && !StringUtils.startsWith(request.getClientActivationRedirectUrl(), PREFIX_HTTP)) {
			throw new TenantException(getMessage("businesslogic.tenant.invalidredirectactivationurl", null, audit), ReasonTenant.INVALID_CLIENT_URL);
		}
		
		// Validasi URL Redirect Singing
		if (StringUtils.isNotBlank(request.getClientSigningRedirectUrl()) && !StringUtils.startsWith(request.getClientSigningRedirectUrl(), PREFIX_HTTPS) && !StringUtils.startsWith(request.getClientSigningRedirectUrl(), PREFIX_HTTP)) {
			throw new TenantException(getMessage("businesslogic.tenant.invalidredirectsigningurl", null, audit), ReasonTenant.INVALID_CLIENT_URL);
		}
		
		// Validasi theshold balance
		this.validateThresholdBalance(request.getThresholdBalance(), audit);
	}

	@Override
	public UpdateTenantSettingsResponse updateTenantSettings(UpdateTenantSettingsRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		validateUpdateTenantSettingsRequest(request, audit);
		
		// Validasi length email receiver
		String emailReminderDest = StringUtils.join(request.getEmailReminderDest(), EMAIL_REMINDER_DEST_SEPARATOR);
		if (emailReminderDest.length() > EMAIL_REMINDER_DEST_MAX_LENGTH) {
			throw new TenantException(getMessage("service.global.maxlength",
					new Object[] {BAL_REMINDER_RECEIVER_LABEL, EMAIL_REMINDER_DEST_MAX_LENGTH}, audit), ReasonTenant.REMINDER_RECEIVER_TOO_LONG);
		}
		
		if (StringUtils.isNotBlank(request.getUploadUrl())) {
			tenant.setUploadUrl(request.getUploadUrl());
		} else {
			tenant.setUploadUrl(null);
		}
		
		if (StringUtils.isNotBlank(request.getActivationCallbackUrl())) {
			tenant.setActivationCallbackUrl(request.getActivationCallbackUrl());
		} else {
			tenant.setActivationCallbackUrl(null);
		}
		
		boolean automaticSign = request.isAutomaticSign();
		String thresholdJson = gson.toJson(request.getThresholdBalance());
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		
		if ("1".equals(request.getUseWaMessage())) {
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA, tenant, vendor, audit);
		} else if ("0".equals(request.getUseWaMessage())) {
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, tenant, vendor, audit);
		} else {
			throw new TenantException(getMessage("businesslogic.tenant.invalidusewamessage", null, audit), ReasonTenant.INVALID_USE_WA_MESSAGE);
		}
		
		if (StringUtils.isNotBlank(request.getClientCallbackUrl())) {
			tenant.setClientCallbackUrl(request.getClientCallbackUrl());
		} else {
			tenant.setClientCallbackUrl(null);
		}
		
		if (StringUtils.isNotBlank(request.getClientActivationRedirectUrl())) {
			tenant.setClientActivationRedirectUrl(request.getClientActivationRedirectUrl());
		} else {
			tenant.setClientActivationRedirectUrl(null);
		}
		
		if (StringUtils.isNotBlank(request.getClientSigningRedirectUrl())) {
			tenant.setClientSigningRedirectUrl(request.getClientSigningRedirectUrl());
		} else {
			tenant.setClientSigningRedirectUrl(null);
		}
		
		tenant.setThresholdBalance(thresholdJson);
		tenant.setEmailReminderDest(StringUtils.upperCase(emailReminderDest));
		tenant.setRefNumberLabel(request.getRefNumberLabel());
		tenant.setAutomaticStampingAfterSign(automaticSign ? "1" : "0");
		tenant.setDtmUpd(new Date());
		tenant.setUsrUpd(audit.getCallerId());
		tenant.setUseWaMessage(request.getUseWaMessage());
		daoFactory.getTenantDao().updateTenant(tenant);
		
		return new UpdateTenantSettingsResponse();
	}
	
	@Override
	public TenantSettingsResponse getTenantSettingsEmbed(TenantSettingsEmbedRequest request, AuditContext audit) {
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		audit.setCallerId(msgBean.getEmail());
		
		TenantSettingsRequest settingRequest = new TenantSettingsRequest();
		settingRequest.setTenantCode(msgBean.getTenantCode());
		settingRequest.setParam(request.getParam());
		
		return getTenantSettings(settingRequest, audit);
	}

	@Override
	public FaceVerifyResponse getLivenessCheckNodeflux(FaceMatchLivenessRequest request, AuditContext audit) {
		FaceVerifyResponse response = new FaceVerifyResponse();
		AmMsuser user = userLogic.getUserByLoginId(audit.getCallerId());
		PersonalDataBean userBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), true);
		Status status = new Status();
		if (StringUtils.isBlank(request.getSelfieBase64())) {
			status.setCode(200);
			status.setMessage(this.messageSource.getMessage("businesslogic.liveness.emptyselfiebase64", 
					new Object[] {}, this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
			return response;
		}
		
		//membandingkan foto saat mau ttd dengan foto yang disimpan di database
		if (userBean.getSelfPhotoRaw() != null) {
			String base64 = Base64.getEncoder().encodeToString(userBean.getSelfPhotoRaw());
			request.setSelfieDBBase64("data:image/jpeg;base64," + base64);
		} else {
			status.setCode(200);
			status.setMessage(this.messageSource.getMessage("businesslogic.liveness.emptyselfiedbbase64", 
					new Object[] {}, this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
			return response;
		}
		
		LivenessRequest livenessRequest = new LivenessRequest();
		AuditDataType auditDataType = new AuditDataType();
		auditDataType.setCallerId(audit.getCallerId());
		livenessRequest.setAudit(auditDataType);
		livenessRequest.setTenantCode(request.getTenantCode());
		
		LivenessResponse livenessReponse = this.getTenantLiveness(livenessRequest, audit);
		if (GlobalVal.STATUS_CODE_SUCCESS != livenessReponse.getStatus().getCode()) {
			status = livenessReponse.getStatus();
			response.setStatus(status);
			return response;
		} else {
			if (livenessReponse.getStatusFaceVerify().equals(GlobalVal.FACE_VERIFY_SERVICE_INACTIVE)) {
				status.setCode(200);
				status.setMessage(this.messageSource.getMessage("businesslogic.tenant.faceverifyserviceinactive", 
						new Object[] {}, this.retrieveLocaleAudit(audit)));
				response.setStatus(status);
				return response;
			}
		}
		
		MsTenant tenant = this.getTenantByCode(request.getTenantCode(), audit);
	
		PersonalDataBean userPersonalData = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
		String tenantCode = tenant.getTenantCode();
		String refNumber = StringUtils.isNotBlank(request.getRefNumber()) ? request.getRefNumber() : StringUtils.EMPTY;
		String nik = userPersonalData.getIdNoRaw();
		byte[] idPhotoByteArray = this.convertImageString(request.getSelfieBase64());
		String fileName = cloudStorageLogic.storeTransactionSelfie(tenantCode, nik, refNumber, idPhotoByteArray);

		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.AUTHORIZATION, authKeyNodeflux);
		mapHeader.add("x-nodeflux-timestamp", xtimestampNodeflux);
		WebClient client = WebClient.create(urlFaceMatchNodeflux).headers(mapHeader);

		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		FaceMatchLivenessNodefluxRequest body = new FaceMatchLivenessNodefluxRequest();
		if (!request.getSelfieBase64().contains("data:image")) 
			request.setSelfieBase64("data:image/jpeg;base64," + request.getSelfieBase64());
		String[] images = new String[] {request.getSelfieDBBase64(), request.getSelfieBase64()};
		body.setImages(images);
		body.setClientRef(request.getRefNumber());
		Date date = new Date();
		String nodefluxReq = gson.toJson(body);
		LOG.info("JSON Request to Nodeflux for Face Match and Liveness: \n {}", nodefluxReq);
		Response clientResponse = client.post(nodefluxReq);

		InputStreamReader isReader = new InputStreamReader((InputStream) clientResponse.getEntity());
		String result = StringUtils.EMPTY;
		try {
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			LOG.error("Error on Liveness check result", e);
		}
		LOG.info("Liveness check result: {}", result);

		TrFaceVerify faceVerify = new TrFaceVerify();
		FaceMatchLivenessNodefluxResponse faceMatchLivenessReponse = gson.fromJson(result, FaceMatchLivenessNodefluxResponse.class);
		if (faceMatchLivenessReponse.isOk() && faceMatchLivenessReponse.getResult().length > 0
				&& faceMatchLivenessReponse.getResult()[0] != null && faceMatchLivenessReponse.getResult()[0].getFaceLiveness().isLive()
				&& faceMatchLivenessReponse.getResult()[1] != null && faceMatchLivenessReponse.getResult()[1].getFaceMatch().isMatch()) {
			response.setLivenessCheck(GlobalVal.STATUS_LIVENESS_CHECK_SUCCESS);
			status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
			status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
			response.setStatus(status);
			faceVerify.setVerifyResult(GlobalVal.STATUS_VERIFY_SUCCESS);
		} else {
			response.setLivenessCheck(GlobalVal.STATUS_LIVENESS_CHECK_FAILED);
			status.setCode(StatusCode.LIVENESS_CHECK_FAILED);
			status.setMessage(faceMatchLivenessReponse.getMessage());
			response.setStatus(status);
			faceVerify.setVerifyResult(GlobalVal.STATUS_VERIFY_FAILED + " - " + faceMatchLivenessReponse.getMessage());
		}
		
		faceVerify.setLiveCheckDate(date);
		faceVerify.setAmMsuser(user);
		faceVerify.setUserPhoto(fileName.getBytes());
		faceVerify.setUsrCrt(user.getLoginId());
		faceVerify.setDtmCrt(new Date());
		
		TrDocumentH documentHeader = null;
		TrDocumentD documentDetail = null;
		if (StringUtils.isNotBlank(refNumber)) {
			documentHeader = daoFactory.getDocumentDao().getDocumentHeaderByRefNo(refNumber);
			
			// FIXME: POTENSI ERROR KALAU ADA 2 DOKUMEN DALAM 1 KONTRAK
			documentDetail = daoFactory.getDocumentDao().getDocumentDetailByDocumentHeaderId(documentHeader.getIdDocumentH());

			faceVerify.setTrDocumentH(documentHeader);
			faceVerify.setTrDocumentD(documentDetail);
		}
		
		daoFactory.getFaceVerifyDao().insertFaceVerify(faceVerify);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_FVRF);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UFVRF);
		int qty = -1;
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		String notes = user.getFullName() + " - " + faceVerify.getVerifyResult();
		saldoLogic.insertBalanceMutation(null, documentHeader, documentDetail, balanceType, trxType, 
				tenant, vendor, date, refNumber, qty, String.valueOf(nextTrxNo), user, notes, null, audit);
		
		return response;
	}
	
	public void generateTokenNodeflux(MultivaluedMap<String, String> mapHeader) {
		String urlGenerateToken = "https://backend.cloud.nodeflux.io/auth/signatures";
		GenerateNodefluxTokenBean generateTokenBean = new GenerateNodefluxTokenBean();
		generateTokenBean.setAccessKey("A7FTRZZ1MCZY4L8VXDT3IQ33Q");
		generateTokenBean.setSecretKey("3RptXc7ZOtTngzDlnWHfLjF2Il3S2AIOiRHT-e3GjneO29unQICN5gtkOetCSpLO");
		
		WebClient generateTokenClient = WebClient.create(urlGenerateToken);
		Response generateTokenResponse = generateTokenClient.post(gson.toJson(generateTokenBean));
		InputStreamReader isReader = new InputStreamReader((InputStream) generateTokenResponse.getEntity());
		String result = StringUtils.EMPTY;
		try {
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			LOG.error("Error on Generate Token Nodeflux", e);
		}
		
		GenerateTokenNodefluxResponse response = gson.fromJson(result, GenerateTokenNodefluxResponse.class);
		String[] date = response.getHeaders().getxNodefluxTimestamp().split("T");
		StringBuilder auth = new StringBuilder();
		auth.append("NODEFLUX-HMAC-SHA256 Credential=")
			.append(generateTokenBean.getAccessKey() + "/")
			.append(date[0]+"/")
			.append("nodeflux.api.v1beta1.ImageAnalytic/StreamImageAnalytic, SignedHeaders=x-nodeflux-timestamp, Signature=")
			.append(response.getToken());
		
		mapHeader.add(HttpHeaders.AUTHORIZATION, auth.toString());
		mapHeader.add("x-nodeflux-timestamp", response.getHeaders().getxNodefluxTimestamp());
	}

	@Override
	public AddTenantResponse addTenant(AddTenantRequest request, AuditContext audit) {
		
		this.validateAddTenantRequest(request, audit);
		this.validateThresholdBalance(request.getThresholdBalance(), audit);
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null != tenant) {
			throw new TenantException(this.messageSource.getMessage("service.global.existed",
					new String[] {"Tenant code " + request.getTenantCode()}, 
					this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_CODE_EXISTED);
		}
				
		String thresholdJson = gson.toJson(request.getThresholdBalance());
		String emailReminderDest = StringUtils.join(request.getEmailReminderDest(), EMAIL_REMINDER_DEST_SEPARATOR);
		if (emailReminderDest.length() > EMAIL_REMINDER_DEST_MAX_LENGTH) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EXISTED,
					new String[] {BAL_REMINDER_RECEIVER_LABEL, String.valueOf(EMAIL_REMINDER_DEST_MAX_LENGTH)}, 
					this.retrieveLocaleAudit(audit)), ReasonTenant.REMINDER_RECEIVER_TOO_LONG);
		}
		
		tenant = new MsTenant();
		tenant.setTenantCode(StringUtils.upperCase(request.getTenantCode()));
		tenant.setTenantName(request.getTenantName());
		tenant.setApiKey(request.getApiKey());
		tenant.setThresholdBalance(thresholdJson);
		tenant.setEmailReminderDest(emailReminderDest);
		tenant.setRefNumberLabel(request.getRefNumberLabel());
		tenant.setIsActive("1");
		tenant.setUsrCrt(audit.getCallerId());
		tenant.setDtmCrt(new Date());
		daoFactory.getTenantDao().insertTenant(tenant);
		
		AmMsrole role = roleLogic.insertRole(GlobalVal.ROLE_ADMIN_CLIENT, GlobalVal.ROLE_NAME_ADMIN_CLIENT, tenant, audit);
		
		List<String> admClientMenus = new ArrayList<>();
		admClientMenus.add(GlobalVal.MENU_BALANCE);
		admClientMenus.add(GlobalVal.MENU_STAMP_DUTY);
		admClientMenus.add(GlobalVal.MENU_INQ_USER);
		admClientMenus.add(GlobalVal.MENU_FEEDBACK);
		admClientMenus.add(GlobalVal.MENU_EMPLOYEE);
		admClientMenus.add(GlobalVal.MENU_CUSTOMER);
		admClientMenus.add(GlobalVal.MENU_TENANT_SETTING);
		
		for (String menuCode : admClientMenus) {
			AmMsmenu menu = daoFactory.getMenuDao().getMenuByCode(menuCode);
			menuLogic.insertMenuOfRole(menu, role, audit);
		}
		MsOffice office = officeLogic.insertOffice(GlobalVal.OFFICE_HO, GlobalVal.OFFICE_CODE_HO, tenant, audit);
		
		AmMsuser newUser = daoFactory.getUserDao().getUserByLoginId(request.getEmailUserAdmin());
		if (null != newUser) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EXISTED,
					new String[] {"Email " + request.getEmailUserAdmin()},
					this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_UNIQUE);
		}
		
		newUser = new AmMsuser();
		SignerBean signerBean = new SignerBean();
		signerBean.setLoginId(request.getEmailUserAdmin());
		signerBean.setUserName("");
		signerBean.setEmailService("0");

		newUser.setIsActive("1");
		newUser.setIsDeleted("0");
		newUser.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		newUser.setDtmCrt(new Date());
		newUser.setLoginId(StringUtils.upperCase(signerBean.getLoginId()));
		newUser.setFullName(StringUtils.upperCase(signerBean.getUserName()));
		String[] separated = signerBean.getUserName().split(" ");
		newUser.setInitialName(StringUtils.upperCase(separated[0]));
		newUser.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
		newUser.setPassword(PasswordHash.createHash(request.getPasswordUserAdmin()));
		newUser.setChangePwdLogin("1");
		newUser.setFailCount(0);
		newUser.setIsLoggedIn("0");
		newUser.setIsLocked("0");
		newUser.setIsDormant("0");
		newUser.setMsOffice(office);
		newUser.setEmailService(signerBean.getEmailService());
		newUser.setMsEmailHosting(daoFactory.getEmailDao().getEmailHostingById(signerBean.getIdEmailHosting()));
		newUser.setIsDormant("2");

		if (StringUtils.isNotBlank(signerBean.getIdNo())) {
			String idKtp = MssTool.getHashedString(signerBean.getIdNo());
			newUser.setHashedIdNo(idKtp); // save as hashed version
		}

		if (StringUtils.isNotBlank(signerBean.getUserPhone())) {
			String phone = MssTool.getHashedString(signerBean.getUserPhone());
			newUser.setHashedPhone(phone); // save as hashed version
		}
		
		AmMsuser userNIK = daoFactory.getUserDao().getUserByIdNo(signerBean.getIdNo());
		
		
		if (null == userNIK) {
			daoFactory.getUserDao().insertUser(newUser);
		}
		else {
			newUser.setIdMsUser(userNIK.getIdMsUser());
		}
		
		AmGeneralsetting defaultVendor = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_DEFAULT_VENDOR_INSERT_USER_MANAGEMENT);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(defaultVendor.getGsValue());
		
		MsVendorRegisteredUser vendorRegisteredUser = new MsVendorRegisteredUser();
		vendorRegisteredUser.setMsVendor(vendor);
		vendorRegisteredUser.setAmMsuser(newUser);
		vendorRegisteredUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmailUserAdmin()));
		vendorRegisteredUser.setIsActive("1");
		vendorRegisteredUser.setIdMsVendorRegisteredUser(1);
		vendorRegisteredUser.setUsrCrt(audit.getCallerId());
		vendorRegisteredUser.setDtmCrt(new Date());
		vendorRegisteredUser.setIsRegistered("1");
		vendorRegisteredUser.setEmailService("0");
		daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUser(vendorRegisteredUser);
		
		roleLogic.insertMemberofrole(role, newUser, audit);
		this.insertUseroftenant(newUser, tenant, audit);
		return new AddTenantResponse();
	}
	
	private void validateThresholdBalance(Map<String, Object> thresholdbalances, AuditContext audit) {
		for (Map.Entry<String, Object> balance : thresholdbalances.entrySet()) {
			String balanceCode = balance.getKey();
			MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceCode);
			if (null == balanceType) {
				throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new String[] {"Balance", balanceCode}, 
						this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_BALANCE_TYPE_NOT_FOUND);
			}
			
			Integer threshold = (Integer) balance.getValue();
			if (threshold < 0) {
				throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_TENANT_INVALID_THRESHOLD_BALANCE,
						null, this.retrieveLocaleAudit(audit)), ReasonTenant.INVALID_THRESHOLD);
			}
		}
	}
	
	private void validateAddTenantRequest(AddTenantRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] {TENANT_CODE_LABEL}, this.retrieveLocaleAudit(audit)), ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
		if (StringUtils.isBlank(request.getTenantName())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] {"Tenant Name"}, this.retrieveLocaleAudit(audit)), ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
		if (StringUtils.isBlank(request.getApiKey())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] {"API Key"}, this.retrieveLocaleAudit(audit)), ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
		if (StringUtils.isBlank(request.getRefNumberLabel())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] {"Ref Number Label"}, this.retrieveLocaleAudit(audit)), ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
		if (StringUtils.isBlank(request.getEmailUserAdmin())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] {"Email"}, this.retrieveLocaleAudit(audit)), ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
		if (StringUtils.isBlank(request.getPasswordUserAdmin())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] {"Password"}, this.retrieveLocaleAudit(audit)), ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
	}
	
	private void validateEditTenantRequest(EditTenantRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] {TENANT_CODE_LABEL}, this.retrieveLocaleAudit(audit)), ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
		if (StringUtils.isBlank(request.getTenantName())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] {"Tenant Name"}, this.retrieveLocaleAudit(audit)), ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
		if (StringUtils.isBlank(request.getApiKey())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] {"API Key"}, this.retrieveLocaleAudit(audit)), ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
		if (StringUtils.isBlank(request.getRefNumberLabel())) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY,
					new String[] {"Ref Number Label"}, this.retrieveLocaleAudit(audit)), ReasonUser.PASSWORD_CANNOT_BE_EMPTY);
		}
		
		validateNumeric(request.getInvitationLinkActiveDuration(), "invitationLinkActiveDuration", audit);
		validateNumeric(request.getOtpActiveDuration(), "otpActiveDuration", audit);
		
		validateFlagValue(request.getEmailService(), "emailService", audit);
		validateFlagValue(request.getAutomaticStampingAfterSign(), "automaticStampingAfterSign", audit);
		validateFlagValue(request.getUseStandardUrl(), "useStandardUrl", audit);
		validateFlagValue(request.getUseCustomSignImage(), "useCustomImage", audit);
		validateFlagValue(request.getUseLivenessFacecompareFirst(), "useLivenessFacecompareFirst", audit);
		validateFlagValue(request.getNeedOtpForSigning(), "needOtpForSigning", audit);
		validateFlagValue(request.getNeedPasswordForSigning(), "needPasswordForSigning", audit);
		validateFlagValue(request.getSendCertNotifBySms(), "sendCertNotifBySms", audit);
		validateFlagValue(request.getLivenessFacecompareServices(), "livenessFaccompareServices", audit);
		validateFlagValue(request.getSplitLivenessFacecompareBill(), "splitLivenessFacecompareBill", audit);
		validateFlagValue(request.getSignRequestNotification(), "signRequestNotification", audit);
		validateFlagValue(request.getSendOtpByEmail(), "sendOtpByEmail", audit);
	}
	
	private void validateNumeric(String value, String paramName, AuditContext audit) {
		if (!StringUtils.isNumeric(value)) {
			throw new FormatException(getMessage("service.global.notvalidnumeric", new Object[] {paramName}, audit), ReasonFormat.INVALID_FORMAT);
		}
	}
	
	private void validateFlagValue(String value, String paramName, AuditContext audit) {
		if (StringUtils.isNotBlank(value) && (!"1".equals(value) && !"0".equals(value))) {
			throw new FormatException(getMessage(MSG_ACTIVE_DEL, new Object[] {paramName}, audit), ReasonFormat.INVALID_FORMAT);
		}
	}

	@Override
	public void insertUseroftenant(AmMsuser user, MsTenant tenant, AuditContext audit) {
		MsUseroftenant useroftenant = new MsUseroftenant();
		useroftenant.setAmMsuser(user);
		useroftenant.setMsTenant(tenant);
		useroftenant.setDtmCrt(new Date());
		useroftenant.setUsrCrt(audit.getCallerId());
		daoFactory.getTenantDao().insertUserOfTenant(useroftenant);
	}

	@Override
	public EditTenantResponse editTenant(EditTenantRequest request, AuditContext audit) {
		this.validateEditTenantRequest(request, audit);
		this.validateThresholdBalance(request.getThresholdBalance(), audit);
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		String thresholdJson = gson.toJson(request.getThresholdBalance());
		String emailReminderDest = StringUtils.join(request.getEmailReminderDest(), EMAIL_REMINDER_DEST_SEPARATOR);
		if (emailReminderDest.length() > EMAIL_REMINDER_DEST_MAX_LENGTH) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EXISTED,
					new String[] {BAL_REMINDER_RECEIVER_LABEL, String.valueOf(EMAIL_REMINDER_DEST_MAX_LENGTH)}, 
					this.retrieveLocaleAudit(audit)), ReasonTenant.REMINDER_RECEIVER_TOO_LONG);
		}
		
		tenant.setTenantName(request.getTenantName());
		tenant.setApiKey(request.getApiKey());
		tenant.setThresholdBalance(thresholdJson);
		tenant.setEmailReminderDest(emailReminderDest);
		tenant.setRefNumberLabel(request.getRefNumberLabel());
		
		//4.12.0
		tenant.setEmailService(request.getEmailService());
		tenant.setLivenessKey(request.getLivenessKey());
		tenant.setMeteraiStampingResultUrl(request.getMeteraiStampingResultUrl());
		tenant.setAutomaticStampingAfterSign(request.getAutomaticStampingAfterSign());
		tenant.setLivenessFaceCompareServices(request.getLivenessFacecompareServices());
		tenant.setInvitationLinkActiveDuration(Integer.parseInt(request.getInvitationLinkActiveDuration()));
		tenant.setSplitLivenessFaceCompareBill(request.getSplitLivenessFacecompareBill());
		tenant.setUseStandardUrl(request.getUseStandardUrl());
		tenant.setUseCustomSignImage(request.getUseCustomSignImage());
		tenant.setActivationCallbackUrl(request.getActivationCallbackUrl());
		tenant.setSignRequestNotification(request.getSignRequestNotification());
		tenant.setAesEncryptKey(request.getAesEncryptKey());
		tenant.setNeedPasswordForSigning(request.getNeedPasswordForSigning());
		tenant.setNeedOtpForSigning(request.getNeedOtpForSigning());
		tenant.setUseLivenessFacecompareFirst(request.getUseLivenessFacecompareFirst());
		tenant.setSendCertNotifBySms(request.getSendCertNotifBySms());
		MsLov lovVendorStamping = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_VENDOR_STAMPING, request.getVendorStamping());
		if (StringUtils.isNotBlank(request.getVendorStamping())) {
			commonValidatorLogic.validateNotNull(lovVendorStamping, getMessage(MSG_DATA_NOT_FOUND, new Object[] {"Vendor Stamping", request.getVendorStamping()}, audit), StatusCode.INVALID_CONDITION);
		}
		tenant.setLovVendorStamping(StringUtils.isNotBlank(request.getVendorStamping()) ? lovVendorStamping : null);
		tenant.setOtpActiveDuration(Integer.parseInt(request.getOtpActiveDuration()));
		tenant.setSentOtpByEmail(request.getSendOtpByEmail());
		MsLov lovSmsGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, request.getSmsGateway());
		if (StringUtils.isNotBlank(request.getSmsGateway())) {
			commonValidatorLogic.validateNotNull(lovSmsGateway, getMessage(MSG_DATA_NOT_FOUND, new Object[] {"SMS Gateway", request.getSmsGateway()}, audit), StatusCode.INVALID_CONDITION);
		}
		tenant.setLovSmsGateway(StringUtils.isNotBlank(request.getDefaultOtpSendingOptions()) ? lovSmsGateway : null);
		MsLov lovOtpSendPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DEFAULT_SENDING_OPTION, request.getDefaultOtpSendingOptions());
		if (StringUtils.isNotBlank(request.getDefaultOtpSendingOptions())) {
			commonValidatorLogic.validateNotNull(lovOtpSendPoint, getMessage(MSG_DATA_NOT_FOUND, new Object[] {"Default OTP Sending Option", request.getDefaultOtpSendingOptions()}, audit), StatusCode.INVALID_CONDITION);
		}
		tenant.setLovDefaultOtpSendingOption(StringUtils.isNotBlank(request.getDefaultOtpSendingOptions()) ? lovOtpSendPoint : null);
		
		tenant.setUsrUpd(audit.getCallerId());
		tenant.setDtmUpd(new Date());
		
		daoFactory.getTenantDao().updateTenant(tenant);
		return new EditTenantResponse();
	}

	@Override
	@SuppressWarnings("serial")
	public TenantDetailResponse getTenantDetail(TenantDetailRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		Type empMapType = new TypeToken<Map<String, Object>>() {}.getType();
		Map<String, Object> bean = gson.fromJson(tenant.getThresholdBalance(), empMapType);
		
		TenantDetailResponse response = new TenantDetailResponse();
		response.setTenantCode(tenant.getTenantCode());
		response.setTenantName(tenant.getTenantName());
		response.setApiKey(tenant.getApiKey());
		response.setEmailReminderDest(Arrays.asList(tenant.getEmailReminderDest().split(EMAIL_REMINDER_DEST_SEPARATOR)));
		response.setThresholdBalance(bean);
		response.setRefNumberLabel(tenant.getRefNumberLabel());
		response.setEmailService(tenant.getEmailService());
		response.setLivenessKey(tenant.getLivenessKey());
		response.setMeteraiStampingResultUrl(tenant.getMeteraiStampingResultUrl());
		response.setAutomaticStampingAfterSign(tenant.getAutomaticStampingAfterSign());
		response.setLivenessFacecompareServices(tenant.getLivenessFaceCompareServices());
		response.setInvitationLinkActiveDuration(tenant.getInvitationLinkActiveDuration());
		response.setSplitLivenessFacecompareBill(tenant.getSplitLivenessFaceCompareBill());
		response.setUseStandardUrl(tenant.getUseStandardUrl());
		response.setUseCustomSignImage(tenant.getUseCustomSignImage());
		response.setActivationCallbackUrl(tenant.getActivationCallbackUrl());
		response.setSignRequestNotification(tenant.getSignRequestNotification());
		response.setAesEncryptKey(tenant.getAesEncryptKey());
		response.setNeedPasswordForSigning(tenant.getNeedPasswordForSigning());
		response.setNeedOtpForSigning(tenant.getNeedOtpForSigning());
		response.setUseLivenessFacecompareFirst(tenant.getUseLivenessFacecompareFirst());
		response.setSendCertNotifBySms(tenant.getSendCertNotifBySms());
		response.setVendorStamping(null != tenant.getLovVendorStamping() ? tenant.getLovVendorStamping().getCode() : null);
		response.setOtpActiveDuration(tenant.getOtpActiveDuration());
		response.setSendOtpByEmail(tenant.getSentOtpByEmail());
		response.setSmsGateway(null != tenant.getLovSmsGateway() ? tenant.getLovSmsGateway().getCode() : null);
		response.setDefaultOtpSendingOptions(null != tenant.getLovDefaultOtpSendingOption() ? tenant.getLovDefaultOtpSendingOption().getCode() : null);
		
		return response;
	}

	@Override
	public GetListTenantResponse getListTenantPaging(GetListTenantRequest request, AuditContext audit) {
		GetListTenantResponse response = new GetListTenantResponse();
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		int totalResult = daoFactory.getTenantDao().countListTenantPaging(request.getTenantName(), request.getStatus());
		double totalPage =  Math.ceil((double) totalResult / maxRow);
		response.setPage(request.getPage());
		response.setTotalResult(totalResult);
		response.setTotalPage((int) totalPage);
		response.setTenantList(daoFactory.getTenantDao().getListTenantPaging(request.getTenantName(), request.getStatus(), min, max));
		return response;
	}

	@Override
	public MsTenant getTenantFromXApiKey(String xApiKey, AuditContext audit) {
		if (StringUtils.isBlank(xApiKey)) {
			throw new TenantException(this.messageSource.getMessage("service.global.emptyparam",
					new String[] {"x-api-key"}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_API_KEY_EMPTY);
		}
		
		String[] splitApiKey = xApiKey.split("@");
		if (splitApiKey.length <= 1) {
			throw new TenantException(this.messageSource.getMessage("businesslogic.tenant.apikeyvalidationerror",
					null, this.retrieveLocaleAudit(audit)), ReasonTenant.VALIDATE_TENANT_API_KEY_ERROR);
		}
		
		String apiKey = splitApiKey[0];
		String tenantCode = splitApiKey[splitApiKey.length - 1];
		MsTenant tenant = daoFactory.getTenantDao().getTenantByApiKeyAndTenantCode(apiKey, tenantCode);
		if (null == tenant) {
			throw new TenantException(messageSource.getMessage("businesslogic.paymentsigntype.tenantnotfound", null,
					retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		return tenant;
	}

	@Override
	public GetSmsDeliverySettingResponse getSmsDeliverySettingDetail(GetSmsDeliverySettingRequest request,
			AuditContext audit) {
		GetSmsDeliverySettingResponse response = new GetSmsDeliverySettingResponse();
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {TENANT_CODE_LABEL, request.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		AmGeneralsetting invLink = daoFactory.getCommonDao().getGeneralSettingByTenant("ADDON_INV_LINK", tenant);
		AmGeneralsetting signLink = daoFactory.getCommonDao().getGeneralSettingByTenant("ADDON_SIGN_LINK", tenant);
		
		boolean inv = "1".equals(invLink.getGsValue());
		boolean sign = "1".equals(signLink.getGsValue());
		
		response.setAddonInvLink(inv);
		response.setAddonSignLink(sign);
		return response;
	}

	
	@Override
	public UpdateDeliverySettingResponse updateDeliverySetting(UpdateDeliverySettingRequest request,
			AuditContext audit) {
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {TENANT_CODE_LABEL, request.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		AmGeneralsetting invLink = daoFactory.getCommonDao().getGeneralSettingByTenant("ADDON_INV_LINK", tenant);
		AmGeneralsetting signLink = daoFactory.getCommonDao().getGeneralSettingByTenant("ADDON_SIGN_LINK", tenant);
		
		boolean inv = request.isAddonInvLink();
		boolean sign = request.isAddonSignLink();
		
		if(inv) {
			invLink.setGsValue("1");
		}else {
			invLink.setGsValue("0");
		}
		
		if(sign) {
			signLink.setGsValue("1");
		}else {
			signLink.setGsValue("0");
		}
		
		invLink.setUsrUpd(audit.getCallerId());
		invLink.setDtmUpd(new Date());
		
		signLink.setUsrUpd(audit.getCallerId());
		signLink.setDtmUpd(new Date());
		
		daoFactory.getCommonDao().updateGeneralSetting(invLink);
		daoFactory.getCommonDao().updateGeneralSetting(signLink);

		return new UpdateDeliverySettingResponse();
	}

	@Override
	public GetStatusStampingOtomatisTenantResponse getStatusStampingOtomatisTenant(GetStatusStampingOtomatisTenantRequest request, AuditContext audit) {
		GetStatusStampingOtomatisTenantResponse response = new GetStatusStampingOtomatisTenantResponse();
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {TENANT_CODE_LABEL, request.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		String statusStamping;
		if(null == tenant.getAutomaticStampingAfterSign()) {
			statusStamping = "";
		} else {
			statusStamping = tenant.getAutomaticStampingAfterSign();
		}
		response.setStatusStampingOtomatis(statusStamping);
		
		return response;
	}

	@Override
	public GetStatusEmailServiceTenantResponse getStatusEmailServiceTenant(GetStatusEmailServiceTenantRequest request,
			AuditContext audit) {
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if(null == tenant) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {TENANT_CODE_LABEL, request.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		GetStatusEmailServiceTenantResponse response = new GetStatusEmailServiceTenantResponse();
		response.setTenantCode(tenant.getTenantCode());
		response.setTenantName(tenant.getTenantName());
		response.setEmailService(tenant.getEmailService());
		return response;
	}

	@Override
	public GetAutomaticStampingAfterSignResponse getAutomaticStampingAfterSign(
			GetAutomaticStampingAfterSignRequest request, AuditContext audit) {
		GetAutomaticStampingAfterSignResponse response = new GetAutomaticStampingAfterSignResponse();		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {TENANT_CODE_LABEL, request.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		boolean automaticSign = "1".equals(tenant.getAutomaticStampingAfterSign());
		response.setAutomaticSign(automaticSign);
		return response;
	}


	@Override
	public GetUploadUrlResponse getUploadUrl(GetUploadUrlRequest request, AuditContext audit) {
		GetUploadUrlResponse response = new GetUploadUrlResponse();
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {TENANT_CODE_LABEL, request.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		String  url = tenant.getUploadUrl();
		response.setUploadUrl(url);
		return response;
	}


	
	@Override
	public GetTenantRekonResponse getTenantRekon(GetTenantRekonRequest request, AuditContext audit) {
		GetTenantRekonResponse response = new GetTenantRekonResponse();
		response.setListTenantRekon(daoFactory.getTenantDao().getListTenantRekon(request.getVendorCode()));
		if(null!=request.getVendorCode() && response.getListTenantRekon().isEmpty()) {
			String message = String.format("Vendor %s tidak ditemukan", request.getVendorCode());
			throw new VendorException(message, ReasonVendor.VENDOR_CODE_INVALID);
		}
		return response;
	}

	@Override
	public CheckLivenessFaceCompareServiceResponse checkLivenessFaceCompareService(CheckLivenessFaceCompareServiceRequest request, AuditContext audit) {
				
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(getMessage("businesslogic.tenant.tenantcodeempty", null, audit), 
					ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		MsTenant msTenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		CheckLivenessFaceCompareServiceResponse response = new CheckLivenessFaceCompareServiceResponse();
		response.setLivenessFacecompareServicesStatus("1".equals(msTenant.getLivenessFaceCompareServices()) ? "1" : "0");
		response.setMustLivenessFaceCompareFirst("1".equals(msTenant.getUseLivenessFacecompareFirst()) ? "1" : "0");
		return response;
	}

	@Override
	public CheckLivenessFaceCompareServiceResponse checkLivenessFaceCompareServiceFullApi(
			CheckLivenessFaceCompareServiceRequest request, AuditContext audit) {
		return this.checkLivenessFaceCompareService(request, audit);
	}
	
	private TestTenantCallbackResponse testActivationCallback(MsTenant tenant, String url) {
		String responseBody = null;
		String responseCode = null;
		try {
			// Prepare header and URL
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
			mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			mapHeader.add(HttpHeaders.X_API_KEY, tenant.getApiKey());
			WebClient client = WebClient.create(url).headers(mapHeader);
			
			// Prepare body
			CallbackActivationRequestBean requestBean = new CallbackActivationRequestBean();
			requestBean.setEmail(CALLBACK_DUMMY_EMAIL);
			requestBean.setNik("1234567890123456");
			requestBean.setActivationStatus(GlobalVal.SERVICES_RESULT_SUCCESS);
			String jsonRequest = gson.toJson(requestBean);
			
			// Get response
			Response callbackResponse = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) callbackResponse.getEntity());
			responseBody = IOUtils.toString(isReader);
			
			responseCode = callbackResponse.getStatus() + " " + callbackResponse.getStatusInfo().getReasonPhrase();
			
			TestTenantCallbackResponse response = new TestTenantCallbackResponse();
			response.setResponseBody(responseBody);
			response.setResponseCode(responseCode);
			return response;
			
		} catch (Exception e) {
			LOG.error("Failed to test tenant {} callback with exception: {}", tenant.getTenantName(), e.getLocalizedMessage(), e);
			TestTenantCallbackResponse response = new TestTenantCallbackResponse();
			response.setResponseBody(responseBody);
			response.setResponseCode(responseCode);
			response.setErrorMessage(e.getLocalizedMessage());
			return response;
		}
	}
	
	private TenantCallbackRequestBean createDummyActivationCallbackRequest() {
		TenantCallbackBean data = new TenantCallbackBean();
		data.setEmail(CALLBACK_DUMMY_EMAIL);
		data.setPhone("08000000000");
		
		TenantCallbackRequestBean bean = new TenantCallbackRequestBean();
		bean.setCallbackType("ACTIVATION_COMPLETE");
		bean.setTimeStamp(MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_TIME_FORMAT_SEC));
		bean.setData(data);
		bean.setMessage("Success");
		return bean;
	}
	
	private TenantCallbackRequestBean createDummySignCallbackRequest() {
		TenantCallbackBean data = new TenantCallbackBean();
		data.setEmail(CALLBACK_DUMMY_EMAIL);
		data.setDocumentId("00155D0B-7502-A892-11EE-3A6D7CECE410");
		
		TenantCallbackRequestBean bean = new TenantCallbackRequestBean();
		bean.setCallbackType("SIGNING_COMPLETE");
		bean.setTimeStamp(MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_TIME_FORMAT_SEC));
		bean.setData(data);
		bean.setMessage("Success");
		return bean;
	}
	
	private TryTenantCallbackResponse tryTenantCallback(MsTenant tenant, String url) {
		String responseBody = null;
		String responseCode = null;
		try {
			// Prepare header and URL
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
			mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			mapHeader.add(HttpHeaders.X_API_KEY, tenant.getApiKey());
			WebClient client = WebClient.create(url).headers(mapHeader);
			
			int randomNumber = MssTool.generateRandomNumber(100);
			
			// Prepare body
			TenantCallbackRequestBean requestBean = (randomNumber % 2 == 0) ? createDummyActivationCallbackRequest() : createDummySignCallbackRequest();
			String jsonRequest = gson.toJson(requestBean);
			
			// Get response
			Response callbackResponse = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) callbackResponse.getEntity());
			responseBody = IOUtils.toString(isReader);
			
			responseCode = callbackResponse.getStatus() + " " + callbackResponse.getStatusInfo().getReasonPhrase();
			
			TryTenantCallbackResponse response = new TryTenantCallbackResponse();
			response.setResponseBody(responseBody);
			response.setResponseCode(responseCode);
			return response;
			
		} catch (Exception e) {
			LOG.error("Failed to test tenant {} callback with exception: {}", tenant.getTenantName(), e.getLocalizedMessage(), e);
			TryTenantCallbackResponse response = new TryTenantCallbackResponse();
			response.setResponseBody(responseBody);
			response.setResponseCode(responseCode);
			response.setErrorMessage(e.getLocalizedMessage());
			return response;
		}
	}

	@Override
	public TestTenantCallbackResponse testCallback(TestTenantCallbackRequest request, AuditContext audit) {
		
		if (!StringUtils.startsWith(request.getUrl(), PREFIX_HTTPS) && !StringUtils.startsWith(request.getUrl(), PREFIX_HTTP)) {
			throw new TenantException(getMessage(GlobalKey.MESSAGE_ERROR_TENANT_INVALID_CALLBACK_URL, null, audit), ReasonTenant.INVALID_CALLBACK_URL);
		}
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		if ("ACTIVATION".equals(request.getCallbackType())) {
			return testActivationCallback(tenant, request.getUrl());
		}
		
		return new TestTenantCallbackResponse();
	}

	@Override
	public TenantSettingsResponse getTenantSettingsWithoutSecurity(TenantSettingsRequest request, AuditContext audit) {
		return getTenantSettings(request, audit);
	}

	@Override
	public TryTenantCallbackResponse tryCallback(TryTenantCallbackRequest request, AuditContext audit) {
		if (!StringUtils.startsWith(request.getUrl(), PREFIX_HTTPS) && !StringUtils.startsWith(request.getUrl(), PREFIX_HTTP)) {
			throw new TenantException(getMessage(GlobalKey.MESSAGE_ERROR_TENANT_INVALID_CALLBACK_URL, null, audit), ReasonTenant.INVALID_CALLBACK_URL);
		}
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		return tryTenantCallback(tenant, request.getUrl());
	}
	
	private NotificationType getSmsNotificationType(MsLov lovSmsGateway) {
		if (null == lovSmsGateway || GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST.equals(lovSmsGateway.getCode())) {
			return NotificationType.SMS_VFIRST;
		}
		
		if (GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS.equals(lovSmsGateway.getCode())) {
			return NotificationType.SMS_JATIS;
		}
		
		return NotificationType.SMS_VFIRST;
	}
	
	private NotificationType getWaNotificationType(MsLov lovWaGateway) {
		if (null == lovWaGateway || GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS.equals(lovWaGateway.getCode())) {
			return NotificationType.WHATSAPP_HALOSIS;
		}
		
		if (GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP.equals(lovWaGateway.getCode())) {
			return NotificationType.WHATSAPP;
		}
		
		return NotificationType.WHATSAPP_HALOSIS;
	}
	
	/**
	 * Priority:
	 * 1. mustUseSmsFirst
	 * 2. mustUseWaFirst
	 */
	private NotificationType getNotificationType(NotificationSendingPoint sendingPoint, boolean useEmailService, boolean useWaMessage, boolean mustUseWaFirst, boolean sendOtpByEmail, boolean mustUseSmsFirst, MsLov lovSmsGateway, MsLov lovWaGateway) {
		if (sendingPoint.isOtp()) {
			if (mustUseSmsFirst) {
				return getSmsNotificationType(lovSmsGateway);
			}
			
			if (mustUseWaFirst) {
				return getWaNotificationType(lovWaGateway);
			}
			
			if (sendingPoint != NotificationSendingPoint.OTP_ACT && sendOtpByEmail && !useEmailService) {
				return NotificationType.EMAIL;
			}
			
			if (useWaMessage) {
				return getWaNotificationType(lovWaGateway);
			}
			
			return getSmsNotificationType(lovSmsGateway);
		}
		
		// Non OTP notification
		if (mustUseSmsFirst) {
			return getSmsNotificationType(lovSmsGateway);
		}
		
		if (mustUseWaFirst) {
			return getWaNotificationType(lovWaGateway);
		}
		
		if (!useEmailService) {
			return NotificationType.EMAIL;
		}
		
		if (useWaMessage) {
			return getWaNotificationType(lovWaGateway);
		}
		
		return getSmsNotificationType(lovSmsGateway);
	}

	@Override
	public NotificationType getNotificationType(MsTenant tenant, NotificationSendingPoint sendingPoint, String emailService) {
		MsNotificationtypeoftenant notificationType = daoFactory.getNotificationtypeoftenantDao().getNotificationType(tenant, sendingPoint.toString());
		boolean useEmailService = "1".equals(emailService);
		if (null == notificationType) {
			MsLov halosisWaGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);
			boolean useWaMessage = "1".equals(tenant.getUseWaMessage());
			boolean mustUseWaFirst = "1".equals(tenant.getMustUseWaFirst());
			boolean sendOtpByEmail = "1".equals(tenant.getSentOtpByEmail());
			boolean mustUseSmsFirst = false;
			return getNotificationType(sendingPoint, useEmailService, useWaMessage, mustUseWaFirst, sendOtpByEmail, mustUseSmsFirst, tenant.getLovSmsGateway(), halosisWaGateway);
		}
		
		boolean useWaMessage = "1".equals(notificationType.getUseWaMessage());
		boolean mustUseWaFirst = "1".equals(notificationType.getMustUseWaFirst());
		boolean sendOtpByEmail = "1".equals(notificationType.getSendOtpByEmail());
		boolean mustUseSmsFirst = "1".equals(notificationType.getMustUseSmsFirst());
		return getNotificationType(sendingPoint, useEmailService, useWaMessage, mustUseWaFirst, sendOtpByEmail, mustUseSmsFirst, notificationType.getLovSmsGateway(), notificationType.getLovWaGateway());
	}
	

	@Override
	public GetAvailableSendingPointResponse getAvailableSendingPoint (GetAvailableSendingPointRequest request, AuditContext audit) {
		
		
		String messageValidation = "";
		BigInteger id = daoFactory.getUserDao().getIdMsUserByLoginId(request.getLoginId());
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_EMAILNOTFOUND,
				new Object[] { request.getLoginId() }, audit);
		commonValidatorLogic.validateNotNull(id, messageValidation, StatusCode.USER_NOT_FOUND);
		
		
		Long idMsUser = id.longValue();		
		
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(getMessage(GlobalKey.MESSAGE_ERROR_TENANT_CODE_EMPTY, null,audit), ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(idMsUser, request.getVendorCode());
		
		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_OTP_SIGN_BY_EMAIL);
		
		List<BalanceVendoroftenantBean> listBalanceVendoroftenant = daoFactory.getBalanceVendoroftenantDao().getListBalanceVendoroftenant(tenant.getTenantCode(), GlobalVal.VENDOR_CODE_ESG);
		
		List<String> listOtpSendingOption = new ArrayList<>();
		for (int i = 0; i < listBalanceVendoroftenant.size(); i++) {
			if (listBalanceVendoroftenant.get(i).getBalanceTypeCode().equals(GlobalVal.CODE_LOV_BALANCE_TYPE_SMS)) {
				listOtpSendingOption.add(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
			} else if (listBalanceVendoroftenant.get(i).getBalanceTypeCode().equals(GlobalVal.CODE_LOV_BALANCE_TYPE_WA)) {
				listOtpSendingOption.add(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
			}
		}
		

		if (null != tenantSettings && tenantSettings.getSettingValue().equals("1") && vendorUser.getEmailService().equals("0")) {
			listOtpSendingOption.add(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL);
		}
		
		GetAvailableSendingPointResponse response = new GetAvailableSendingPointResponse();
		response.setListAvailableOptionSendingPoint(listOtpSendingOption);
		
		String defaultSendingPoint = null;
		if (tenant.getLovDefaultOtpSendingOption() != null) {
			if (tenant.getLovDefaultOtpSendingOption().getCode().equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS) || tenant.getLovDefaultOtpSendingOption().getCode().equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA) || tenant.getLovDefaultOtpSendingOption().getCode().equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL)) {
				if (listOtpSendingOption.contains(tenant.getLovDefaultOtpSendingOption().getCode())) {
					defaultSendingPoint = tenant.getLovDefaultOtpSendingOption().getCode();
				} else {
					defaultSendingPoint = GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS;
				}
			} else {
				defaultSendingPoint = GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS;

			}
	
		} else {
			defaultSendingPoint = GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS;
		}
		response.setDefaultAvailableOptionSendingPoint(defaultSendingPoint);
		return response;
		
	}
	
	@Override
	public GetAvailableSendingPointInvitationResponse getAvailableSendingPointInvitation (GetAvailableSendingPointInvitationRequest request, AuditContext audit) {
		
		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		TrInvitationLink invLink = invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);
		MsTenant tenant = invLink.getMsTenant();
		
		List<BalanceVendoroftenantBean> listBalanceVendoroftenant = daoFactory.getBalanceVendoroftenantDao().getListBalanceVendoroftenant(tenant.getTenantCode(), GlobalVal.VENDOR_CODE_ESG);
		
		List<String> listOtpSendingOption =   new ArrayList<>();
		for (int i = 0; i < listBalanceVendoroftenant.size(); i++) {
			if (listBalanceVendoroftenant.get(i).getBalanceTypeCode().equals(GlobalVal.CODE_LOV_BALANCE_TYPE_SMS)) {
				listOtpSendingOption.add(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
			} else if (listBalanceVendoroftenant.get(i).getBalanceTypeCode().equals(GlobalVal.CODE_LOV_BALANCE_TYPE_WA)) {
				listOtpSendingOption.add(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
			}
		}
		
		GetAvailableSendingPointInvitationResponse response = new GetAvailableSendingPointInvitationResponse();
		response.setListAvailableOptionSendingPoint(listOtpSendingOption);
		
		String defaultSendingPoint = null;
		if (tenant.getLovDefaultOtpSendingOption() != null ) {
			if (tenant.getLovDefaultOtpSendingOption().getCode().equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS) || tenant.getLovDefaultOtpSendingOption().getCode().equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA)) {
				defaultSendingPoint = tenant.getLovDefaultOtpSendingOption().getCode();
			} else {
				defaultSendingPoint = GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS;
			}
	
		} else {
			defaultSendingPoint = GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS;
		}
		response.setDefaultAvailableOptionSendingPoint(defaultSendingPoint);
		return response;
		
	}
}
