package com.adins.esign.dataaccess.api;

import java.util.Date;
import java.util.List;

import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.custom.InvitationReportBean;
import com.adins.esign.model.custom.InvitationBean;

public interface InvitationLinkDao {
	
	void insertInvitationLink(TrInvitationLink invLink);
	
	void updateInvitationLink(TrInvitationLink invitationLink);
	void updateInvitationLinkNewTran(TrInvitationLink invitationLink);
	
	void deleteInvitationLink(TrInvitationLink invLink);
	
	TrInvitationLink getInvitationLinkByInvitationCode(String invitationCode);
	TrInvitationLink getInvitationLinkByInvitationCodeNewTran(String invitationCode);
	
	TrInvitationLink getInvitationLinkByRecieverDetail(String recieverDetail);
	TrInvitationLink getLatestInvitationLinkByReceiverDetail(String recieverDetail);
	TrInvitationLink getInvitationLinkByRecieverDetailV2(String recieverDetail, String vendorCode);
	TrInvitationLink getInvLinkByReceiverDetailAndVendorCode(String receiverDetail, String vendorCode);
	
	TrInvitationLink getInvitationByIdNo(String idNo);
	TrInvitationLink getInvitationByIdNoV2(String idNo, String vendorCode);
	TrInvitationLink getInvitationByIdNoAndVendorCode(String idNo, String vendorCode);
	
	TrInvitationLink getInvitationByPhone(String phone);
	TrInvitationLink getInvitationByPhoneAndVendorCode(String phone, String vendorCode);
	
	TrInvitationLink getInvitationByEmail(String email);
	TrInvitationLink getInvitationByEmailAndVendorCode(String email, String vendorCode);
	
	List<InvitationReportBean> getListInvitationReport(Date tanggalPengirimanDari,
			Date tanggalPegirimanSampai, String tenantCode, String nama, String pengirimanMelalui, String penerimaUndangan,
			String statusUndangan, String statusRegistrasi);
	List<InvitationBean> getListInvitation(int min, int max, Date tanggalPengirimanSampai, Date tanggalPengirimanDari,
			String tenantCode, String nama, String pengirimanMelalui, String penerimaUndangan, String statusUndangan,
			String statusRegistrasi);
	Integer countListInvitation(String tenantCode, String nama, String pengirimanMelalui, String penerimaUndangan,
			String statusUndangan, String statusRegistrasi, Date tanggalPengirimanDari, Date tanggalPengirimanSampai);
	TrInvitationLink getInvitationLinkByRecieverDetailAndIdMsVendor(String recieverDetail, Long idMsVendor);
	TrInvitationLink getInvitationLinkByRecieverDetailAndIdMsVendorNewTrx(String recieverDetail, Long idMsVendor);
	
	List<TrInvitationLink> getListInvitationLinkByRecieverDetail(String recieverDetail);
	List<TrInvitationLink> getListInvitationLinkByRecieverDetail(String recieverDetail, String tenantCode);
	
	List<TrInvitationLink> getListInvitationByIdNo(String idNo);
	List<TrInvitationLink> getListInvitationByIdNo(String idNo, String tenantCode);
	
	List<TrInvitationLink> getListInvitationByPhone(String phone);
	List<TrInvitationLink> getListInvitationByPhone(String phone, String tenantCode);
	
	List<TrInvitationLink> getListInvitationByEmail(String email);
	List<TrInvitationLink> getListInvitationByEmail(String email, String tenantCode);
	
	List<TrInvitationLink> getListInvitationByIdNoOtherVendor(String idNo, String vendorCode);
	List<TrInvitationLink> getListInvitationByPhoneOtherVendor(String phone, String vendorCode);
	List<TrInvitationLink> getListInvitationByEmailOtherVendor(String email, String vendorCode);
	
	TrInvitationLink getInvitationLinkByPhoneAndVendorCodeOtherId(String phone, String vendorCode, Long idInvitationLink);
	TrInvitationLink getInvitationLinkByEmailAndVendorCodeOtherId(String email, String vendorCode, Long idInvitationLink);
	
	

}
