package com.adins.esign.businesslogic.api.interfacing;

import java.io.IOException;

import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.custom.DownloadStampedDocResponse;
import com.adins.esign.model.custom.EmeteraiPajakkuLoginResponseBean;
import com.adins.esign.model.custom.PajakkuDocumentTypeBean;
import com.adins.esign.webservices.model.UpdateStampDutyStatusResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface EmeteraiPajakkuLogic {
	UpdateStampDutyStatusResponse attachMeteraiPajakku(AuditContext audit);
	
	PajakkuDocumentTypeBean getDocumentType() throws IOException;
	EmeteraiPajakkuLoginResponseBean getToken() throws IOException;
	DownloadStampedDocResponse downloadStampedDoc(TrDocumentD documentD, AuditContext audit) throws IOException;
	DownloadStampedDocResponse downloadStampedDoc(TrDocumentD documentD, String peruriLoginToken, AuditContext audit);
}
