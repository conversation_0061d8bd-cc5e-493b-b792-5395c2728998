package com.adins.esign.webservices.frontend.endpoint;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.DocumentInquiryLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.SignConfirmationLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.SaveSigningResultDecryptedBean;
import com.adins.esign.webservices.frontend.api.DocumentService;
import com.adins.esign.webservices.model.ActivationStatusByDocumentIdRequest;
import com.adins.esign.webservices.model.ActivationStatusByDocumentIdResponse;
import com.adins.esign.webservices.model.BulkSignDocumentEmbedRequest;
import com.adins.esign.webservices.model.BulkSignDocumentRequest;
import com.adins.esign.webservices.model.BulkSignDocumentResponse;
import com.adins.esign.webservices.model.CancelDigitalSignRequest;
import com.adins.esign.webservices.model.CancelDigitalSignResponse;
import com.adins.esign.webservices.model.CancelSignNormalRequest;
import com.adins.esign.webservices.model.CheckDocTemplateRequest;
import com.adins.esign.webservices.model.CheckDocTemplateResponse;
import com.adins.esign.webservices.model.CheckDocumentBeforeSigningRequest;
import com.adins.esign.webservices.model.CheckDocumentBeforeSigningResponse;
import com.adins.esign.webservices.model.CheckDocumentSendStatusEmbedRequest;
import com.adins.esign.webservices.model.CheckDocumentSendStatusRequest;
import com.adins.esign.webservices.model.CheckDocumentSendStatusResponse;
import com.adins.esign.webservices.model.DocumentExcelReportResponse;
import com.adins.esign.webservices.model.DocumentSignDetailsRequest;
import com.adins.esign.webservices.model.DocumentSignDetailsResponse;
import com.adins.esign.webservices.model.DocumentTemplateAddUpdateRequest;
import com.adins.esign.webservices.model.DocumentTemplateAddUpdateResponse;
import com.adins.esign.webservices.model.DocumentTemplateGetOneRequest;
import com.adins.esign.webservices.model.DocumentTemplateGetOneResponse;
import com.adins.esign.webservices.model.DocumentTemplateListEmbedRequest;
import com.adins.esign.webservices.model.DocumentTemplateListEmbedResponse;
import com.adins.esign.webservices.model.DocumentTemplateListRequest;
import com.adins.esign.webservices.model.DocumentTemplateListResponse;
import com.adins.esign.webservices.model.DocumentTemplateSignLocListEmbedRequest;
import com.adins.esign.webservices.model.DocumentTemplateSignLocListEmbedResponse;
import com.adins.esign.webservices.model.DownloadManualReportRequest;
import com.adins.esign.webservices.model.DownloadManualReportResponse;
import com.adins.esign.webservices.model.DownloadStampedFileFromDmsRequest;
import com.adins.esign.webservices.model.DownloadStampedFileFromDmsResponse;
import com.adins.esign.webservices.model.DummyClientURLUploadRequest;
import com.adins.esign.webservices.model.DummyClientURLUploadResponse;
import com.adins.esign.webservices.model.GetDocumentIdRequest;
import com.adins.esign.webservices.model.GetDocumentIdResponse;
import com.adins.esign.webservices.model.GetListReportRequest;
import com.adins.esign.webservices.model.GetListReportResponse;
import com.adins.esign.webservices.model.InsertDocumentManualSignRequest;
import com.adins.esign.webservices.model.InsertDocumentManualSignResponse;
import com.adins.esign.webservices.model.InsertDocumentStampingRequest;
import com.adins.esign.webservices.model.InsertDocumentStampingResponse;
import com.adins.esign.webservices.model.ListInquiryDocumentEmbedRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentNormalRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentResponse;
import com.adins.esign.webservices.model.ResendNotifSignRequest;
import com.adins.esign.webservices.model.ResendNotifSignResponse;
import com.adins.esign.webservices.model.ResendSignNotificationRequest;
import com.adins.esign.webservices.model.ResendSignNotificationResponse;
import com.adins.esign.webservices.model.ResumeSendDocumentRequest;
import com.adins.esign.webservices.model.ResumeSendDocumentResponse;
import com.adins.esign.webservices.model.RetryLatestStampFromUploadRequest;
import com.adins.esign.webservices.model.RetryLatestStampFromUploadResponse;
import com.adins.esign.webservices.model.RetryStampingEmbedRequest;
import com.adins.esign.webservices.model.RetryStampingEmbedResponse;
import com.adins.esign.webservices.model.RetryStampingMeteraiDocumentResponse;
import com.adins.esign.webservices.model.RetryStampingNormalRequest;
import com.adins.esign.webservices.model.RetryStampingNormalResponse;
import com.adins.esign.webservices.model.RetryStampingRequest;
import com.adins.esign.webservices.model.RetryStampingResponse;
import com.adins.esign.webservices.model.SaveManualStampRequest;
import com.adins.esign.webservices.model.SaveSignResultRequest;
import com.adins.esign.webservices.model.SaveSignResultResponse;
import com.adins.esign.webservices.model.SignConfirmDocumentRequest;
import com.adins.esign.webservices.model.SignConfirmDocumentResponse;
import com.adins.esign.webservices.model.SignDocumentEmbedRequest;
import com.adins.esign.webservices.model.SignDocumentRequest;
import com.adins.esign.webservices.model.SignDocumentResponse;
import com.adins.esign.webservices.model.SignLinkRequest;
import com.adins.esign.webservices.model.SignLinkResponse;
import com.adins.esign.webservices.model.SignerListEmbedRequest;
import com.adins.esign.webservices.model.SignerListRequest;
import com.adins.esign.webservices.model.SignerListResponse;
import com.adins.esign.webservices.model.StampDocumentRequest;
import com.adins.esign.webservices.model.StampDocumentResponse;
import com.adins.esign.webservices.model.StartStampingMeteraiRequest;
import com.adins.esign.webservices.model.StartStampingMeteraiResponse;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.embed.RetryLatestStampFromUploadEmbedRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.google.gson.Gson;

import io.swagger.annotations.Api;

@Component
@Path("/document")
@Api(value = "DocumentService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericDocumentServiceEndpoint implements DocumentService {

	@Autowired private DocumentLogic documentLogic;
	@Autowired private DocumentInquiryLogic documentInquiryLogic;
	@Autowired private DigisignLogic digisignLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private SignConfirmationLogic signConfirmationLogic;
	@Autowired private Gson gson;
	@Autowired Environment env;
	@Value("${spring.mail.username}") private String fromEmailAddr;
	
	@Override
	@POST
	@Path("/s/listDocumentTemplate")
	public DocumentTemplateListResponse listDocumentTemplate(DocumentTemplateListRequest docRequest) {
		AuditContext auditContext = docRequest.getAudit().toAuditContext();
		int page = docRequest.getPage();
		int pageSize = GlobalVal.ROW_PER_PAGE;
		
		return this.documentLogic.listDocumentTemplate(docRequest.getDocumentTemplateCode(), 
				docRequest.getDocumentTemplateName(), docRequest.getIsActive(),	page, pageSize, docRequest.getTenantCode(), auditContext);
	}
	
	@Override
	@POST
	@Path("/listDocumentTemplateEmbed")
	public DocumentTemplateListEmbedResponse listDocumentTemplateEmbed(DocumentTemplateListEmbedRequest request) {	
		AuditContext audit = request.getAudit().toAuditContext();
		return this.documentLogic.listDocumentTemplateEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/listDocumentTemplateSignLocEmbed")
	public DocumentTemplateSignLocListEmbedResponse listDocumentTemplateSignLocEmbed(DocumentTemplateSignLocListEmbedRequest request) {	
		AuditContext audit = request.getAudit().toAuditContext();
		return this.documentLogic.listDocumentTemplateSignLocEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/addDocumentTemplate")
	public DocumentTemplateAddUpdateResponse insertDocumentTemplate(DocumentTemplateAddUpdateRequest docRequest) {
		DocumentTemplateAddUpdateResponse response = new DocumentTemplateAddUpdateResponse();
		AuditContext auditContext = docRequest.getAudit().toAuditContext();
		try {
			this.documentLogic.insertDocumentTemplate(docRequest, auditContext);
		} catch (Exception e) {
			Status status = new Status();
			status.setCode(200);
			status.setMessage(e.getClass().getCanonicalName()+" : " + e.getMessage());
			response.setStatus(status);
		}
		
		return response;
	}
	
	@Override
	@POST
	@Path("/s/getDocumentTemplate")
	public DocumentTemplateGetOneResponse getDocumentTemplate(DocumentTemplateGetOneRequest docRequest) {
		AuditContext auditContext = docRequest.getAudit().toAuditContext();
		return this.documentLogic.getDocumentTemplate(docRequest, auditContext);
	}

	@Override
	@POST
	@Path("/s/updateDocumentTemplate")
	public DocumentTemplateAddUpdateResponse updateDocumentTemplate(DocumentTemplateAddUpdateRequest docRequest) {
		DocumentTemplateAddUpdateResponse resp = new DocumentTemplateAddUpdateResponse();
		
		AuditContext audit = docRequest.getAudit().toAuditContext();
		if ("0".equals(docRequest.getIsSignLocOnly())) {
			documentLogic.updateDocumentTemplate(docRequest, audit);
		} else if ("1".equals(docRequest.getIsSignLocOnly())) {
			documentLogic.updateSignLocation(docRequest, audit);
		} else {
			Status status = new Status();
			status.setCode(200);
			status.setMessage("isSignLocOnly harus diisi dengan 1 atau 0 / isSignLocOnly must only contain 1 or 0");
			resp.setStatus(status);
		}
			
		return resp;
	}

	@Override
	@POST
	@Path("/s/signDocument")
	public SignDocumentResponse signDocument(SignDocumentRequest signDocRequest) throws IOException, ParseException {
		AuditContext auditContext = signDocRequest.getAudit().toAuditContext();
		return documentLogic.signDocument(signDocRequest, auditContext);
	}
	
	@Override
	@POST
	@Path("/signDocumentEmbed")
	public SignDocumentResponse signDocumentEmbed(SignDocumentEmbedRequest request) throws IOException, ParseException {
		AuditContext auditContext = request.getAudit().toAuditContext();
		return documentLogic.signDocumentEmbed(request, auditContext);
	}

	@Override
	@POST
	@Path("/saveResultSign")
	public SaveSignResultResponse saveSignResult(SaveSignResultRequest saveSignRequest) throws IOException {
		AuditContext audit = new AuditContext(saveSignRequest.getAudit().getCallerId());
		String decrypted = digisignLogic.decryptMessage(saveSignRequest.getMsg(), saveSignRequest.getTenantCode());
		SaveSigningResultDecryptedBean bean = gson.fromJson(decrypted, SaveSigningResultDecryptedBean.class);
		
		return documentLogic.saveDocumentSignResult(bean, audit);
	}

	@Override
	@POST
	@Path("/s/inquiry")
	public ListInquiryDocumentResponse getListInquiryDocument(ListInquiryDocumentRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();
		return documentInquiryLogic.getListInquiryDocument(request, auditContext);
	}
	
	@Override
	@POST
	@Path("/inquiryEmbed")
	public ListInquiryDocumentResponse getListInquiryDocumentEmbed(ListInquiryDocumentEmbedRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();
		return documentLogic.getListInquiryDocumentEmbed(request, auditContext);
	}
	
	//untuk list monitoring dokumen
	@Override
	@POST
	@Path("/s/inquiryNormal")
	public ListInquiryDocumentResponse getListInquiryDocumentNormal(ListInquiryDocumentNormalRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();
		return documentLogic.getListInquiryDocumentNormal(request, auditContext);
	}

	@Override
	@POST
	@Path("/viewDocument")
	public ViewDocumentResponse getDocumentFile(ViewDocumentRequest request) throws IOException {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.viewDocument(request, audit);
	}
	
	@Override
	@POST
	@Path("/viewDocumentEmbed")
	public ViewDocumentResponse getDocumentFileEmbed(ViewDocumentRequest request) throws IOException {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.viewDocumentEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/s/viewSigner")
	public SignerListResponse getSignerList(SignerListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.getDocumentSigners(request, audit);
	}
	
	@Override
	@POST
	@Path("/viewSignerEmbed")
	public SignerListResponse getSignerListEmbed(SignerListEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.getDocumentSignersEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/s/bulkSignDocument")
	public BulkSignDocumentResponse bulkSignDocument(BulkSignDocumentRequest request) throws IOException, ParseException {
  		AuditContext auditContext = request.getAudit().toAuditContext();
		return documentLogic.bulkSignDocument(request, auditContext);
	}
	
	@Override
	@POST
	@Path("/bulkSignDocumentEmbed")
	public BulkSignDocumentResponse bulkSignDocumentEmbed(BulkSignDocumentEmbedRequest request) throws IOException, ParseException {
  		AuditContext auditContext = request.getAudit().toAuditContext();
		return documentLogic.bulkSignDocumentEmbed(request, auditContext);
	}

	@Override
	@POST
	@Path("/s/checkDocumentTemplateExist")
	public CheckDocTemplateResponse checkDocTemplateExist(CheckDocTemplateRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.checkDocTemplateExist(request, audit);
	}

	@Override
	@POST
	@Path("/cancelDigitalSign")
	public CancelDigitalSignResponse cancelDigitalSign(CancelDigitalSignRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.cancelDigitalSign(request, audit);
	}

	@Override
	@POST
	@Path("/resendSignNotif")
	public ResendSignNotificationResponse resendSignNotification(ResendSignNotificationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.resendSignNotification(request, audit);
	}

	/**
	 * Download document masih untuk monitoring HO
	 */
	@Override
	@POST
	@Path("/downloadDocumentReportEmbed")
	public DocumentExcelReportResponse exportDocumentReportEmbed(ListInquiryDocumentEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.exportDocumentReportEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/s/userSignLink")
	public SignLinkResponse getUserSignLink(SignLinkRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.getUserSignLink(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/checkDocumentSendStatus")
	public CheckDocumentSendStatusResponse checkDocumentSendStatus(CheckDocumentSendStatusRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.checkDocSendStatus(request, audit);
	}

	@Override
	@POST
	@Path("/checkDocumentSendStatusEmbed")
	public CheckDocumentSendStatusResponse checkDocSendStatusEmbed(CheckDocumentSendStatusEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.checkDocSendStatusEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/getDocumentId")
	public GetDocumentIdResponse getDocumentId(GetDocumentIdRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.getDocumentId(request, audit);
	}

	@Override
	@POST
	@Path("/s/activationStatusByDocumentId")
	public ActivationStatusByDocumentIdResponse activationStatusByDocumentId(ActivationStatusByDocumentIdRequest request) throws IOException, ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.activationStatusByDocumentId(request, audit);
	}

	@Override
	@POST
	@Path("/activationStatusByDocumentIdEmbed")
	public ActivationStatusByDocumentIdResponse activationStatusByDocumentIdEmbed(ActivationStatusByDocumentIdRequest request) throws IOException, ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.activationStatusByDocumentIdEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/resumeSendDocument")
	public ResumeSendDocumentResponse resumeSendDocument(ResumeSendDocumentRequest request) throws IOException, ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.resumeSendDocument(request, audit);
	}

	@Override
	@POST
	@Path("/retryStamping")
	public RetryStampingResponse retryStamping(RetryStampingRequest request) throws IOException, ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.retryStamping(request, audit);
	}
	
	@Override
	@POST
	@Path("/retryStampingEmbed")
	public RetryStampingEmbedResponse retryStampingEmbed(RetryStampingEmbedRequest request) throws IOException, ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.retryStampingEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/insertStampingEmbed")
	public InsertDocumentStampingResponse insertDocumentStamping(InsertDocumentStampingRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.insertDocumentStamping(request, audit);
	}

	@Override
	@POST
	@Path("/retryLatestStampFromUploadEmbed")
	public 	RetryLatestStampFromUploadResponse retryLatestStampFromUploadEmbed(RetryLatestStampFromUploadEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.retryLatestStampFromUploadEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/s/insertManualSign")
	public InsertDocumentManualSignResponse insertDocumentManualSign(InsertDocumentManualSignRequest request) throws ParseException, IOException {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.insertDocumentManualSign(request, audit);
	}

	@Override
	@POST
	@Path("/stampEmeterai")
	public StampDocumentResponse stampDocument(StampDocumentRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader("x-api-key");
		return documentLogic.stampDocument(request, xApiKey, audit);
	}

	@Override
	@POST
	@Path("/s/retryLatestStampFromUpload")
	public RetryLatestStampFromUploadResponse retryLatestStampFromUpload(RetryLatestStampFromUploadRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.retryLatestStampFromUpload(request, audit);
	}

	@Override
	@POST
	@Path("/s/startStampingMeterai")
	public StartStampingMeteraiResponse startStampingMeterai(StartStampingMeteraiRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.startStampingMeterai(request, audit);
	}

	@Override
	@POST
	@Path("/s/resendNotifSign")
	public ResendNotifSignResponse resendNotifSign(ResendNotifSignRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.resendNotifSignResponse(request, audit);
	}
	
	@Override
	@POST
	@Path("/getFilterListDocumentTemplate")
	public DocumentTemplateListEmbedResponse getFilterListDocumentTemplate(DocumentTemplateListRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.getFilterListDocumentTemplate(request, audit);
	}
	
	@Override
	@POST
	@Path("/dummyClientUrlUpload")
	public DummyClientURLUploadResponse dummyClientUrlUpload(DummyClientURLUploadRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String token = httpRequest.getHeader("token");
		return documentLogic.dummyClientUrlUpload(request, token, audit);
	}

	@Override
	@POST
	@Path("/s/retryStampingNormal")
	public RetryStampingNormalResponse retryStampingNormal(RetryStampingNormalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.retryStampingNormal(request, audit);
	}

	@Override
	@POST
	@Path("/s/downloadStampedFile")
	public DownloadStampedFileFromDmsResponse downloadStampedDocumentFromDms(
			DownloadStampedFileFromDmsRequest request) throws IOException{
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.downloadStampedDocumentFromDms(request, audit);
	}
	
	@Override
	@POST
	@Path("/downloadStampedFileEmbed")
	public DownloadStampedFileFromDmsResponse downloadStampedDocumentFromDmsEmbed(
			DownloadStampedFileFromDmsRequest request) throws IOException{
		AuditContext audit = request.getAudit().toAuditContext();
		EmbedMsgBean embed = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		audit.setCallerId(embed.getEmail());
		
		String docId = commonLogic.decryptMessageToString(request.getDocumentId(), audit);
		request.setDocumentId(docId);
		return documentLogic.downloadStampedDocumentFromDms(request, audit);
	}

	@Override
	@POST
	@Path("/s/checkDocumentBeforeSigning")
	public CheckDocumentBeforeSigningResponse checkDocumentBeforeSigning(CheckDocumentBeforeSigningRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.checkDocumentBeforeSigning(request, audit);
	}

	@Override
	@POST
	@Path("/s/signConfirmDokumen")
	public SignConfirmDocumentResponse insertSignConfirmDocumentResponse(SignConfirmDocumentRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		
		// Set IP address from header x-real-ip
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xRealIp = httpRequest.getHeader("x-real-ip");
		if (StringUtils.isNotBlank(xRealIp)) {
			request.setIpAddress(xRealIp);
		}
		
		return signConfirmationLogic.signConfirmationDocument(request, audit);
	}

	@Override
	@POST
	@Path("/s/getDocumentSignDetails")
	public DocumentSignDetailsResponse getDocumentSignDetails(DocumentSignDetailsRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();
		return documentLogic.getDocumentSignDetails(request, auditContext);
	}
	
	@Override
	@POST
	@Path("/s/retryStampingMeterai")
	public RetryStampingMeteraiDocumentResponse retryStampingMeterai(RetryStampingRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();
		return documentLogic.retryStampingMeteraiDocument(request, auditContext);
	}

	@Override
	@POST
	@Path("/s/saveManualStampRequest")
	public InsertDocumentStampingResponse saveManualStampRequest(SaveManualStampRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();
		return documentLogic.saveManualStamp(request, auditContext);
	}

	@Override
	@POST
	@Path("/s/cancelDigitalSign")
	public MssResponseType cancelSignNormal(CancelSignNormalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentLogic.cancelSignNormalRequest(request, audit);
	}

	
	

}