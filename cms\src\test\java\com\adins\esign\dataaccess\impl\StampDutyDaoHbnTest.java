package com.adins.esign.dataaccess.impl;

import static org.junit.jupiter.api.Assertions.assertNotEquals;

import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.StampDutyDao;
import com.adins.esign.dataaccess.api.UserDao;
import com.adins.esign.model.custom.StampDutyBean;
import com.adins.esign.model.custom.StampDutyHistoryBean;
import com.adins.esign.util.MssTool;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class StampDutyDaoHbnTest {
	@Autowired UserDao userDao;
	@Autowired StampDutyDao sdDao;
	
	String tenantCode = "";
	String invoiceNo = "" ;
	String stampDutyStatus = "USED";
	String stampDutyNo = "" ;
	Date usedDateStart = MssTool.formatStringToDate("2021-10-10", GlobalVal.DATE_FORMAT) ;
	Date usedDateEnd = new Date()  ;
	int min = 1;
	int max = 100;
	long idStampDuty = 1;
	long idBalanceType = 34;

	@Test
	void getListStampDutyTest() {

		List<StampDutyBean> listSdTest = sdDao.getListStampDuty(tenantCode, invoiceNo, stampDutyStatus, stampDutyNo, null, null, null, usedDateStart, usedDateEnd, min, max, idBalanceType);
		assertNotEquals(0, listSdTest.size());
	}
	
	@Test
	void countListStampDutyTest() {
		int countTest = sdDao.countListStampDuty(tenantCode, invoiceNo, stampDutyStatus, stampDutyNo, null, null, null, usedDateStart, usedDateEnd, idBalanceType);
		assertNotEquals(0, countTest);
	}
	
	@Test
	void getListStampDutyDetailTest() {
		List<StampDutyHistoryBean> listDetail = sdDao.getListStampDutyDetail(tenantCode, idStampDuty);
		assertNotEquals(0, listDetail.size());
	}
}
