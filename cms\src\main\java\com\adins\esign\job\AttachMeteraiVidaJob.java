package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class AttachMeteraiVidaJob {
	private static final Logger LOG = LoggerFactory.getLogger(AttachMeteraiVidaJob.class);
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void runAttachMeteraiVida() {
		try {
			LOG.info("Attach meterai VIDA job started");
			AuditContext audit = new AuditContext("ATTACH METERAI VIDA JOB");
			schedulerLogic.attachMeteraiVida(audit);
			LOG.info("Attach meterai VIDA job finished");
		} catch (Exception e) {
			LOG.error("Attach meterai VIDA job error", e);
		}
	}
}
