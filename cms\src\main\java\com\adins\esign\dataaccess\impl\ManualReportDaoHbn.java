package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.ManualReportDao;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrManualReport;
import com.adins.esign.model.custom.ListManualReportBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.GetListManualReportRequest;


@Transactional
@Component
public class ManualReportDaoHbn extends BaseDaoHbn implements ManualReportDao {

	@Override
	public void insertTrManualReport(TrManualReport trManualReport) {
		trManualReport.setUsrCrt(MssTool.maskData(trManualReport.getUsrCrt()));
		this.managerDAO.insert(trManualReport);
	}
	@Override
	public void deleteTrManualReport(TrManualReport trManualReport) {
		this.managerDAO.delete(trManualReport);
	}
	
	@Override
	public TrManualReport getManualReportByTenantCodeAndReportTypeAndFileName (String tenantCode,String reportType, String fileName) {
		return this.managerDAO.selectOne(
				"from TrManualReport tr "
				+ "join fetch tr.msTenant t "
				+ "join fetch tr.msLov l"
				+ " where t.tenantCode =:tenantCode " 
				+ " and tr.fileName = :fileName " 
				+ " and l.code =:reportType"
				+ " and l.lovGroup = 'REPORT_TYPE' ",
				new Object[][] {{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)},{"reportType", reportType},{"fileName", fileName}});
				
		
	}
	
	@Override
	public TrManualReport getManualReportByTenantCodeAndFileName (String tenantCode, String fileName) {
		return this.managerDAO.selectOne(
				"from TrManualReport tr "
				+ "join fetch tr.msTenant t "
				+ " where t.tenantCode =:tenantCode " 
				+ " and tr.fileName = :fileName ",
				new Object[][] {{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)},{"fileName", fileName}});
				
		
	}
	
	@Override
	public List<ListManualReportBean> getListManualReport(GetListManualReportRequest request, int start, int end) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put("start", start);
		params.put("end", end);
		
		String conditionalParams = builConditionalParamListManualReport(request, params);
		
		query.append("select * from ( ")
			 .append(" select tenant_name as \"tenantName\", description as \"reportType\", filename as \"fileName\", to_char(report_date, 'DD Month YYYY') as \"reportDate\", ")
			 .append("  id_manual_report as \"manualReportId\", tenant_code as \"tenantCode\", row_number() over(order by id_manual_report) as no")
			 .append(" from tr_manual_report mr ")
			 .append(" join ms_lov l on l.id_lov = lov_report_type ")
			 .append(" join ms_tenant t on t.id_ms_tenant = mr.id_ms_tenant ")
			 .append("where 1=1 ")
			 .append(conditionalParams)
			 .append(" ) as a where a.no between :start and :end ");
		
		return managerDAO.selectForListString(ListManualReportBean.class, query.toString(), params, null);
	}
	
	private String builConditionalParamListManualReport(GetListManualReportRequest request, Map<String, Object> params) {
		StringBuilder builder = new StringBuilder();
		
		if (StringUtils.isNotBlank(request.getTenantCode())) {
			builder.append(" and tenant_code = :tenantCode ");
			params.put("tenantCode", request.getTenantCode());
		}
		
		if (StringUtils.isNotBlank(request.getReportType())) {
			builder.append(" and code = :reportType ");
			params.put("reportType", request.getReportType());
		}
		
		if (StringUtils.isNotBlank(request.getReportDateStart())) {
			builder.append(" and cast(report_date as date) >= cast( :reportDateStart as date) ")
					.append(" and cast(report_date as date) <= cast( :reportDateEnd as date) ");
			params.put("reportDateStart", request.getReportDateStart());
			params.put("reportDateEnd", request.getReportDateEnd());
		} else {
			builder.append(" and cast(report_date as date) >= date_trunc('MONTH', now()) - interval '1 day' ")
			.append(" and cast(report_date as date) <= now() ");
		}
		
		if (StringUtils.isNotBlank(request.getFileName())) {
			builder.append(" and filename like :filename ");
			params.put("filename", "%"+request.getFileName()+"%");
		}
		
		return builder.toString();
	}

	@Override
	public BigInteger countListManualReport(GetListManualReportRequest request) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		String conditionalParams = builConditionalParamListManualReport(request, params);
		
		query.append("select count(id_manual_report) ")
		 .append(" from tr_manual_report mr ")
		 .append(" join ms_lov l on l.id_lov = lov_report_type ")
		 .append(" join ms_tenant t on t.id_ms_tenant = mr.id_ms_tenant ")
		 .append("where 1=1 ")
		 .append(conditionalParams);
		
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

}
