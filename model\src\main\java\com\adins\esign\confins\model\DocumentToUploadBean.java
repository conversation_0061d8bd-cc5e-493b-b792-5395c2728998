package com.adins.esign.confins.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

public class DocumentToUploadBean implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@JsonProperty("FileName") @SerializedName("FileName") private String fileName;
	@JsonProperty("DocTypeTc") @SerializedName("DocTypeTc") private String docTypeTc;
	@JsonProperty("Content") @SerializedName("Content") private String content;
	@JsonProperty("DisplayName") @SerializedName("DisplayName") private String displayName;
	
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	public String getDocTypeTc() {
		return docTypeTc;
	}
	public void setDocTypeTc(String docTypeTc) {
		this.docTypeTc = docTypeTc;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public String getDisplayName() {
		return displayName;
	}
	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}
	
}
