package com.adins.esign.businesslogic.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageDeliveryException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.MessageDeliveryReportLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrMessageDeliveryReport;
import com.adins.esign.model.custom.MessageDeliveryReportBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.webservices.model.GetListDeliveryReportForMessageCheckingRequest;
import com.adins.esign.webservices.model.GetListDeliveryReportForMessageCheckingResponse;
import com.adins.esign.webservices.model.GetListMessageDeliveryReportRequest;
import com.adins.esign.webservices.model.GetListMessageDeliveryReportResponse;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.LovException;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.LovException.Reason;
import com.adins.exceptions.MessageDeliveryExepction;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.MessageDeliveryExepction.ReasonMessageDelivery;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericMessageDeliveryReportLogic extends BaseLogic implements MessageDeliveryReportLogic{

	private static final Logger LOG = LoggerFactory.getLogger(GenericMessageDeliveryReportLogic.class);
	
	@Autowired private CommonValidatorLogic commonValidatorLogic;

	
	@Override
	public GetListMessageDeliveryReportResponse getListMessageDeliveryReport(
			GetListMessageDeliveryReportRequest request, AuditContext audit) {
		
		GetListMessageDeliveryReportResponse response = new GetListMessageDeliveryReportResponse();
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());


		String messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST, new String[] {"Tenant code", request.getTenantCode()}, audit);

		commonValidatorLogic.validateNotNull(tenant,  messageValidation, StatusCode.TENANT_NOT_FOUND);
		
		MsUseroftenant userTenant = daoFactory.getUseroftenantDao().getUserTenantByLoginIdAndTenantCode(audit.getCallerId(), tenant.getTenantCode());
		
		if(StringUtils.isNotBlank(request.getDeliveryStatus()) && request.getDeliveryStatus().charAt(0) > '4') {
				throw new LovException(messageSource.getMessage("service.global.notvalid", new Object[] {"Delivery Status"}, this.retrieveLocaleAudit(audit)),
						Reason.CODE_INVALID);
			}
		
		messageValidation = getMessage("businesslogic.user.usertenantnotfound", new String[] { audit.getCallerId(), tenant.getTenantCode() }, audit);

		commonValidatorLogic.validateNotNull(userTenant,  messageValidation, StatusCode.USER_TENANT_NOT_FOUND);

		MsVendor vendor = null;

		if (StringUtils.isNotBlank(request.getvendorCode())) {
			vendor = daoFactory.getVendorDao().getVendorMGByVendorCode(StringUtils.upperCase(request.getvendorCode()));

			messageValidation = getMessage("businesslogic.vendor.vendorcodeinvalid", new Object[] { request.getvendorCode() }, audit);

			commonValidatorLogic.validateNotNull(vendor,  messageValidation, StatusCode.VENDOR_CODE_INVALID);
		}
		
		MsLov lov = new MsLov(); 
		
		if(StringUtils.isNotBlank(request.getMessageMedia())) {
			lov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_MESSAGE_MEDIA, request.getMessageMedia());

			messageValidation = getMessage("service.global.lovnotvalid", new Object[] {request.getMessageMedia()}, audit);

			commonValidatorLogic.validateNotNull(vendor,  messageValidation, StatusCode.LOV_CODE_INVALID);
		}
		
		Date dateStart = null;
		Date dateEnd = null;
		
		Date reqDateStart = null;
		Date reqDateEnd = null;
		
		if (!isDateRangeValid(request.getReportTimeStart(), request.getReportTimeEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { "list ReportTime" }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}
		
		if (!isDateRangeValid(request.getRequestTimeStart(), request.getRequestTimeEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { "list RequestTime" }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}
		
		if (StringUtils.isNotBlank(request.getReportTimeStart())) {
			dateStart = MssTool.formatStringToDate(request.getReportTimeStart() + GlobalVal.SOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		if (StringUtils.isNotBlank(request.getReportTimeEnd())) {
			dateEnd = MssTool.formatStringToDate(request.getReportTimeEnd() + GlobalVal.EOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		if (StringUtils.isNotBlank(request.getRequestTimeStart())) {
			reqDateStart = MssTool.formatStringToDate(request.getRequestTimeStart() + GlobalVal.SOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		if (StringUtils.isNotBlank(request.getRequestTimeEnd())) {
			reqDateEnd = MssTool.formatStringToDate(request.getRequestTimeEnd() + GlobalVal.SOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		validateFilterDate(dateStart, dateEnd, reqDateStart, reqDateEnd, audit);
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);

		int totalResult = daoFactory.getMessageDeliveryReportDao().countListMessageDelivery(request, min, max, dateStart, dateEnd, reqDateStart, reqDateEnd, lov);
		double totalPage = Math.ceil((double) totalResult / maxRow);

		
		List<Map<String, Object>> listMessageDeliveryReportbean = daoFactory.getMessageDeliveryReportDao().getListMessageDelivery(request, min, max, dateStart, dateEnd, reqDateStart, reqDateEnd, lov);
		
		Iterator<Map<String, Object>> itr = listMessageDeliveryReportbean.iterator();

		
		List<MessageDeliveryReportBean> listMessage = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			MessageDeliveryReportBean bean = new MessageDeliveryReportBean();

			String deliveryStatus = mapDeliveryStatus(map);

			bean.setVendorName((String) map.get("d0"));
			bean.setReportTime(map.get("d1").toString());
			bean.setRequestTime(map.get("d2") != null ? map.get("d2").toString() : "");
			bean.setRecipient((String) map.get("d3"));
			bean.setTrxNo((String) map.get("d4"));
			bean.setMessageMedia((String) map.get("d5"));
			bean.setDeliveryStatus((String) map.get("d6"));
			bean.setDeliveryStatusInformation(deliveryStatus);
			
			listMessage.add(bean);
		}
		
		response.setListMessageDeliveryReport(listMessage);
		response.setTotalPage((int) totalPage);
		response.setPage(request.getPage());
		response.setTotalResult(totalResult);
		return response;
	}
	
	private boolean isDateRangeValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);
		
		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return true;
		}
		if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
			try {
				sdf.parse(startDate);
			} catch (ParseException e) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
			try {
				sdf.parse(endDate);
			} catch (ParseException e) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		
		long dayCount = 0;
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			dayCount = (end.getTime() - start.getTime()) / (1000*60*60*24);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		LOG.info("Date range: {} hari", dayCount);
		return dayCount <= Long.valueOf(maxRangeDate);
	}

	private void validateFilterDate(Date dateStart, Date dateEnd, Date reqDateStart, Date reqDateEnd, AuditContext audit) {
		if(null == dateStart && null != dateEnd) {
			throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new Object[] {GlobalVal.CONST_REPORT_TIME_START}, this.retrieveLocaleAudit(audit)), 
				ReasonUser.PARAM_INVALID);
		} else if (null == reqDateStart && null != reqDateEnd) {
			throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new Object[] {GlobalVal.CONST_REQUEST_TIME_START}, this.retrieveLocaleAudit(audit)), 
				ReasonUser.PARAM_INVALID);
		} else if (null == dateEnd && null != dateStart) {
			throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new Object[] {GlobalVal.CONST_REPORT_TIME_END}, this.retrieveLocaleAudit(audit)), 
					ReasonUser.PARAM_INVALID);
		} else if (null == reqDateEnd && null != reqDateStart) { 
			throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new Object[] {GlobalVal.CONST_REQUEST_TIME_END}, this.retrieveLocaleAudit(audit)), 
					ReasonUser.PARAM_INVALID);
		} else if (null != dateStart && null != dateEnd && dateStart.after(dateEnd)) {
			throw new MessageDeliveryExepction(this.messageSource.getMessage(GlobalKey.MESSAGE_MESSAGE_DELIVERY_REPORT_INVALID_DATE, 
					new Object[] {GlobalVal.CONST_REPORT_TIME_END, GlobalVal.CONST_REPORT_TIME_START}, this.retrieveLocaleAudit(audit)),ReasonMessageDelivery.RANGE_DATE_INVALID);
		} else if (null != reqDateStart && null != reqDateEnd && reqDateStart.after(reqDateEnd)) {
			throw new MessageDeliveryExepction(this.messageSource.getMessage(GlobalKey.MESSAGE_MESSAGE_DELIVERY_REPORT_INVALID_DATE, 
					new Object[] {GlobalVal.CONST_REQUEST_TIME_END, GlobalVal.CONST_REQUEST_TIME_START}, this.retrieveLocaleAudit(audit)),ReasonMessageDelivery.RANGE_DATE_INVALID);
		}
	}

	private String mapDeliveryStatus(Map<String, Object> map) {

		String result = "";

		if (("0").equals(map.get("d6"))) {
			result = "Not Started";
		}else if (("1").equals(map.get("d6"))) {
			result = "Waiting";
		}else if (("2").equals(map.get("d6"))) {
			result = "Failed";
		}else if (("3").equals(map.get("d6"))) {
			result = "Delivered";
		}else if (("4").equals(map.get("d6"))) {
			result = "Read";
		}

		return result;
	}
	
	@Override
	public GetListDeliveryReportForMessageCheckingResponse getListDeliveryReportForMessageChecking (GetListDeliveryReportForMessageCheckingRequest request, AuditContext audit ) {
		GetListDeliveryReportForMessageCheckingResponse response = new GetListDeliveryReportForMessageCheckingResponse();
		String messageValidation =  "";
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"Phone No"}, audit);
		commonValidatorLogic.validateNotNull(request.getPhoneNo(), messageValidation, StatusCode.MANDATORY_PARAMETER);
		String phoneRegex = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_REGEX_PHONE_FORMAT);
		if (!request.getPhoneNo().matches(phoneRegex)) {
			throw new ParameterException(getMessage("businesslogic.user.invalidphonenoformat", null, audit), ReasonParam.INVALID_FORMAT);
		}
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"Message Media"}, audit);
		commonValidatorLogic.validateNotNull(request.getMessageMedia(), messageValidation, StatusCode.MANDATORY_PARAMETER);

		MsLov lov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_MESSAGE_MEDIA, request.getMessageMedia());
		messageValidation = getMessage("service.global.lovnotvalid", new Object[] {request.getMessageMedia()}, audit);
		commonValidatorLogic.validateNotNull(lov, messageValidation, StatusCode.LOV_CODE_INVALID);

		
		response = daoFactory.getMessageDeliveryReportDao().getListDeliveryReportForMessageChecking(request.getPhoneNo(), request.getMessageMedia());
		
		return response;
	}
	
	public void insertMessageDeliveryReport(MsTenant tenant, MsVendor vendor, String trxNo, String vendorTrxNo, String recipientDetail, NotificationType type, MsLov messageGateway,
			MsLov sendingPoint, AuditContext audit) {

		TrMessageDeliveryReport messageReport = new TrMessageDeliveryReport();
		messageReport.setMsTenant(tenant);
		messageReport.setMsVendor(vendor);
		messageReport.setRecipientDetail(recipientDetail);
		messageReport.setTrxNo(trxNo);
		messageReport.setVendorTrxNo(vendorTrxNo);
		messageReport.setDeliveryStatus("0");
		messageReport.setReportTime(new Date());
		messageReport.setRequestTime(new Date());

		MsLov messageMedia;
		if (NotificationType.WHATSAPP == type || NotificationType.WHATSAPP_HALOSIS == type) {
			messageMedia = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_MESSAGE_MEDIA, GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
		} else {
			messageMedia = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_MESSAGE_MEDIA, GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
		}

		MsLov credentialType = getCredentialTypeBasedOnType(tenant, type);

		messageReport.setMsLov(messageMedia);

		messageReport.setLovMessageGateway(messageGateway);

		messageReport.setLovCredentialType(credentialType);

		messageReport.setLovSendingPoint(sendingPoint);

		messageReport.setUsrCrt(audit.getCallerId());
		messageReport.setDtmCrt(new Date());

		daoFactory.getMessageDeliveryReportDao().insertMessageDeliveryReport(messageReport);
	}

	MsLov getCredentialTypeBasedOnType(MsTenant tenant, NotificationType type) {
		if ((NotificationType.WHATSAPP == type && hasAllTenantSettings(tenant,
				GlobalVal.LOV_CODE_TENANT_SETTING_WA_JATIS_USERNAME,
				GlobalVal.LOV_CODE_TENANT_SETTING_WA_JATIS_PASSWORD,
				GlobalVal.LOV_CODE_TENANT_SETTING_WA_JATIS_SENDER))
			||
			(NotificationType.WHATSAPP_HALOSIS == type && hasAllTenantSettings(tenant,
				GlobalVal.LOV_CODE_TENANT_SETTING_WA_HALOSIS_USERNAME,
				GlobalVal.LOV_CODE_TENANT_SETTING_WA_HALOSIS_PASSWORD,
				GlobalVal.LOV_CODE_TENANT_SETTING_WA_HALOSIS_SENDER))
			||
			(NotificationType.SMS_JATIS == type && hasAllTenantSettings(tenant,
					GlobalVal.LOV_CODE_TENANT_SETTING_SMS_JATIS_USERNAME,
					GlobalVal.LOV_CODE_TENANT_SETTING_SMS_JATIS_PASSWORD,
					GlobalVal.LOV_CODE_TENANT_SETTING_SMS_JATIS_SENDER))
			||
			(NotificationType.SMS_VFIRST == type && hasAllTenantSettings(tenant,
					GlobalVal.LOV_CODE_TENANT_SETTING_SMS_VFIRST_USERNAME,
					GlobalVal.LOV_CODE_TENANT_SETTING_SMS_VFIRST_PASSWORD,
					GlobalVal.LOV_CODE_TENANT_SETTING_SMS_VFIRST_SENDER))
		) {
			return daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_CREDENTIAL_TYPE, GlobalVal.CODE_LOV_CREDENTIAL_TYPE_TENANT);
		}
		return daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_CREDENTIAL_TYPE, GlobalVal.CODE_LOV_CREDENTIAL_TYPE_ESIGN);
	}

	private boolean hasAllTenantSettings(MsTenant tenant, String code1, String code2, String code3) {
		return null != daoFactory.getTenantSettingsDao().getTenantSettings(tenant, code1) && 
				null != daoFactory.getTenantSettingsDao().getTenantSettings(tenant, code2) && 
				null != daoFactory.getTenantSettingsDao().getTenantSettings(tenant, code3);
	}
}