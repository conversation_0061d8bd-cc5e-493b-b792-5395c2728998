package com.adins.esign.businesslogic.impl;

import java.util.Iterator;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.stringtemplate.v4.ST;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.model.MsMsgTemplate;

/**
 * https://github.com/antlr/stringtemplate4/blob/master/doc/cheatsheet.md
 * 
 * Contoh string template: 'Welcome to esign {user.fullname} ({user.loginId}) at {registerdate}'
 * parameters:
 * 	Map<String, Object> user = new HashMap<>();		
 * 	user.put("loginId", "administrator");
 *  user.put("fullname", "SYSTEM ADMINISTRATOR");
 *  
 *  templateParameters.put("user", user);
 *  templateParameters.put("registerdate", "21/04/2000");
 *  
 * Output: Welcome to esign SYSTEM ADMINISTRATOR (administrator) at 21/04/2000
 */
@Component
public class GenericMessageTemplateLogic extends BaseLogic implements MessageTemplateLogic {

	public static final Logger LOG = LoggerFactory.getLogger(GenericMessageTemplateLogic.class);		
	
	private char delimiterStartChar = '{';
	private char delimiterStopChar = '}';	
	
	@Transactional(readOnly = true)
	@Override
	public MsMsgTemplate getAndParseContent(String msgTemplateCode, Map<String, Object> templateParameters) {
		if (StringUtils.isBlank(msgTemplateCode)) {
			throw new IllegalArgumentException("Argument msgTemplateCode is required");
		}
				
		MsMsgTemplate template = this.daoFactory.getMsgTemplateDao().getTemplateByCode(msgTemplateCode);
		
		if (null == template) {
			LOG.info("Message template not found for code '{}'", msgTemplateCode);
			return null;
		}
		
		if (StringUtils.isBlank(template.getBody())) {
			LOG.info("Message template is empty for code '{}'", msgTemplateCode);
			return template;
		}
		
		MsMsgTemplate returnTemplate = new MsMsgTemplate(); //init new object bcoz dirty object committed if use same object
						
		ST templateBody = new ST(template.getBody(), delimiterStartChar, delimiterStopChar);
		this.setParamsToSt(templateParameters, templateBody);					
		String formattedContent = templateBody.render();
		returnTemplate.setBody(formattedContent);
		returnTemplate.setWaTemplateCode(template.getWaTemplateCode());
		returnTemplate.setWaHalosisTemplateCode(template.getWaHalosisTemplateCode());
					
		if (StringUtils.isNotBlank(template.getSubject())) {
			ST templateSubject = new ST(template.getSubject(), delimiterStartChar, delimiterStopChar);			
			this.setParamsToSt(templateParameters, templateSubject);
			String formattedSubject = templateSubject.render(); 
			returnTemplate.setSubject(formattedSubject);
		}		
		
		return returnTemplate;
	}
	
	private void setParamsToSt(Map<String, Object> templateParameters, ST template) {
		for (Iterator<String> iterator = templateParameters.keySet().iterator(); iterator.hasNext();) {
			String key = iterator.next();
			template.add(key, templateParameters.get(key));
		}
	}
}
