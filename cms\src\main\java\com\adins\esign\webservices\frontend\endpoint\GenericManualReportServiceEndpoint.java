package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.ManualReportLogic;
import com.adins.esign.webservices.frontend.api.ManualReportService;
import com.adins.esign.webservices.model.AddManualReportRequest;
import com.adins.esign.webservices.model.DeleteManualReportRequest;
import com.adins.esign.webservices.model.DownloadManualReportRequest;
import com.adins.esign.webservices.model.DownloadManualReportResponse;
import com.adins.esign.webservices.model.GetListManualReportRequest;
import com.adins.esign.webservices.model.GetListManualReportResponse;
import com.adins.esign.webservices.model.GetListReportRequest;
import com.adins.esign.webservices.model.GetListReportResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/manualReport")
@Api(value = "ManualReportServices")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericManualReportServiceEndpoint implements ManualReportService {

	@Autowired ManualReportLogic manualReportLogic;
	
	@Override
	@POST
	@Path("/s/getList")
	public GetListManualReportResponse getList(GetListManualReportRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return manualReportLogic.getList(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/saveManualReport")
	public MssResponseType saveManualReport (AddManualReportRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return manualReportLogic.saveManualReport(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/download")
	public DownloadManualReportResponse download(DownloadManualReportRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();
		return manualReportLogic.download(request, auditContext);
	}
	
	@Override
	@POST
	@Path("/s/listForDownload")
	public GetListReportResponse getListForDownload(GetListReportRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();	
		return manualReportLogic.getListForDownload(request, auditContext);
		
	}
	
	@Override
	@POST
	@Path("/s/deleteManualReport")
	public MssResponseType deleteManualReport(DeleteManualReportRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();	
		return manualReportLogic.deleteManualReport(request, auditContext);
		
	}

}
