package com.adins.esign.businesslogic.api;

import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.webservices.model.RegionListEmbedRequest;
import com.adins.esign.webservices.model.RegionListRequest;
import com.adins.esign.webservices.model.RegionListResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface RegionLogic {
	RegionListResponse getRegionList(RegionListRequest request, AuditContext audit);
	RegionListResponse getRegionListEmbed(RegionListEmbedRequest request, AuditContext audit);
	MsRegion insertUnregisteredRegion(String regionCode, String regionName, MsTenant tenant, AuditContext audit);
}
