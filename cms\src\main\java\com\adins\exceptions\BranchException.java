package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class BranchException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum Reason {
		TEMPLATE_MISMATCH,
		ERROR_PARSING,
		ERROR_GENERATE,
		ERROR_EXIST,
		UNKNOWN
	}

	private final Reason reason;

	public BranchException(Reason reason) {
		this.reason = reason;
	}

	public BranchException(String message, Reason reason) {
		super(message);
		this.reason = reason;
	}

	public BranchException(Throwable ex, Reason reason) {
		super(ex);
		this.reason = reason;
	}

	public BranchException(String message, Throwable ex, Reason reason) {
		super(message, ex);
		this.reason = reason;
	}

	public Reason getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
			case TEMPLATE_MISMATCH:
				return StatusCode.TEMPLATE_MISMATCH;
			case ERROR_PARSING:
				return StatusCode.ERROR_PARSING;
			case ERROR_GENERATE:
				return StatusCode.ERROR_GENERATE;
			case ERROR_EXIST:
				return StatusCode.ERROR_EXIST;
			default:
				return StatusCode.UNKNOWN;
			}
			
		}
		return StatusCode.UNKNOWN;
	}

}
