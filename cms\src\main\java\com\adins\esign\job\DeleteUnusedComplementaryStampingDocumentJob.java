package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.esign.businesslogic.api.SchedulerLogic;

@DisallowConcurrentExecution
@Component
public class DeleteUnusedComplementaryStampingDocumentJob extends BaseLogic {
	private static final Logger LOG = LoggerFactory.getLogger(DeleteUnusedComplementaryStampingDocumentJob.class);
	private static final String SCHEDULER = "SCHEDULER";

	@Autowired SchedulerLogic schedulerLogic;
	
	public void runDeleteDocument() {
		try {
			LOG.info("Job Delete Document Started");
			AuditContext auditContext = new AuditContext(SCHEDULER);
			schedulerLogic.deleteUnusedComplementaryStampingDocumentJob(auditContext);
			LOG.info("Job Delete Document Finished");
		} catch (Exception e) {
			LOG.error("Error on running DeleteUnusedComplementaryStampingDocument Job", e);
		}
	}
}
