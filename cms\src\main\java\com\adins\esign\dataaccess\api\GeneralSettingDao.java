package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.am.model.AmGeneralsetting;
import com.adins.esign.model.MsTenant;

public interface GeneralSettingDao {
	AmGeneralsetting getGsObjByCode(String gsCode);
	AmGeneralsetting getGsObjByCodeAndTenant(String gsCode, MsTenant tenant);
	AmGeneralsetting getGsObjById(long id);
	String getGsValueByCode(String gsCode);
	void insertGs(AmGeneralsetting insertedGs);
	void updateGs(AmGeneralsetting updGs);

	List<AmGeneralsetting> getListGsObjByCode(String gsCode);
}
