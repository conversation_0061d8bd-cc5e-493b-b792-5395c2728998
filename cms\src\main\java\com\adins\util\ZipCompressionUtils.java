package com.adins.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.adins.esign.util.MssTool;

public class ZipCompressionUtils {
	
	private static final Logger LOG = LoggerFactory.getLogger(ZipCompressionUtils.class);
	
	private ZipCompressionUtils() {
	}
	
	/**
	 * @param filename Also provide the file extention. Example: <code>sign_request_xxx.txt</code>
	 * @param data The plain text that is about to be zipped
	 * @return The zipped text file in byte array format
	 */
	public static final byte[] zipText(final String filename, final String data) {
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		try (ZipOutputStream zipOutputStream = new ZipOutputStream(baos)) {
			ZipEntry zipEntry = new ZipEntry(filename);
			zipOutputStream.putNextEntry(zipEntry);
			zipOutputStream.write(data.getBytes());
		} catch (Exception e) {
			LOG.error("Zip text failed", e);
		}
		
		return baos.toByteArray();
	}
	
	/**
	 * @param filename Also provide the file extention. Example: <code>selfie_photo.jpeg</code>
	 * @param data The base 64 string that is about to be zipped
	 * @return The zipped file in byte array format
	 */
	public static final byte[] zipBase64Data(final String filename, final String base64Data) {
		
		String finalBase64 = MssTool.cutImageStringPrefix(base64Data);
		
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		try (ZipOutputStream zipOutputStream = new ZipOutputStream(baos)) {
			ZipEntry zipEntry = new ZipEntry(filename);
			zipOutputStream.putNextEntry(zipEntry);
			zipOutputStream.write(Base64.getDecoder().decode(finalBase64));
		} catch (Exception e) {
			LOG.error("Zip base 64 data failed", e);
		}
		
		return baos.toByteArray();
	}
	
	/**
	 * @param zipBytes The zipped file in byte array format
	 * @return The unzipped plain text
	 */
	public static final String unzipText(final byte[] zipBytes) {
		
		ByteArrayInputStream is = new ByteArrayInputStream(zipBytes);
		
		try (ZipInputStream zipInputStream = new ZipInputStream(is)) {
			
			ZipEntry entry = null;
			
			while ((entry = zipInputStream.getNextEntry()) != null) {
				LOG.debug("Unzipping {}", entry.getName());
				ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
				byte[] buffer = new byte[1024];
				int length;
				while ((length = zipInputStream.read(buffer)) > 0) {
					byteArrayOutputStream.write(buffer, 0, length);
				}
				return byteArrayOutputStream.toString();
			}
			
			return null;
			
		} catch (Exception e) {
			LOG.error("Unzip text failed", e);
			return null;
		}
	}
	
	/**
	 * @param zipBytes The zipped file in byte array format
	 * @return The unzipped file in base 64 format
	 */
	public static final String unzipBase64Data(final byte[] zipBytes) {
		
		ByteArrayInputStream is = new ByteArrayInputStream(zipBytes);
		
		try (ZipInputStream zipInputStream = new ZipInputStream(is)) {
			
			ZipEntry entry = null;
			
			while ((entry = zipInputStream.getNextEntry()) != null) {
				LOG.debug("Unzipping {}", entry.getName());
				ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
				byte[] buffer = new byte[1024];
				int length;
				while ((length = zipInputStream.read(buffer)) > 0) {
					byteArrayOutputStream.write(buffer, 0, length);
				}
				
				byte[] originalData = byteArrayOutputStream.toByteArray();
				return Base64.getEncoder().encodeToString(originalData);
			}
			
			return null;
			
		} catch (Exception e) {
			LOG.error("Unzip text failed", e);
			return null;
		}
	}
	
}
