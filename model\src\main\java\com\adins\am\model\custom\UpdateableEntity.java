package com.adins.am.model.custom;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.apache.commons.lang3.StringUtils;

@MappedSuperclass
public abstract class UpdateableEntity extends CreateableEntity {

	protected String usrUpd;
	protected Date dtmUpd;

	@Column(name = "usr_upd", length = 36)
	public String getUsrUpd() {
		return this.usrUpd;
	}

	public void setUsrUpd(String usrUpd) {
		this.usrUpd = StringUtils.left(usrUpd, 36);
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "dtm_upd", length = 29)
	public Date getDtmUpd() {
		return this.dtmUpd;
	}

	public void setDtmUpd(Date dtmUpd) {
		this.dtmUpd = dtmUpd;
	}

}
