package com.adins.esign.businesslogic.impl;

import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;

import javax.transaction.Transactional;

import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.model.MsMsgTemplate;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class GenericMessageTemplateLogicTest {
//	@Autowired private MessageTemplateLogic messageTemplateLogic;
//	
//	@Test
//	void testTemplateCodeNull() {
//		Assertions.assertThrows(IllegalArgumentException.class, () -> {
//			this.messageTemplateLogic.getAndParseContent(null, null);
//		});
//	}
//	
//	@Test
//	void testTemplateCodeBlank() {
//		Assertions.assertThrows(IllegalArgumentException.class, () -> {
//			this.messageTemplateLogic.getAndParseContent("", null);
//		});
//	}
//	
//	@Test
//	void testTemplateCodeNotFound() throws ParseException {
//		final String templateCode = "RANDOM";
//		MsMsgTemplate template = this.messageTemplateLogic.getAndParseContent(templateCode, null);
//		Assertions.assertNull(template);
//	}
//	
//	@Test
//	void testTemplateCodeEmptyBody() throws ParseException {
//		final String templateCode = "DUMMY_EMPTY_BODY";
//		MsMsgTemplate template = this.messageTemplateLogic.getAndParseContent(templateCode, null);
//		Assertions.assertNotNull(template);
//	}
//	
//	@Test
//	void testTemplateCodeEmptySubject() throws ParseException {
//		final String templateCode = "DUMMY_EMPTY_SUBJECT";
//		Map<String, Object> user = new HashMap<>();		
//		user.put("loginId", "administrator");
//		user.put("fullname", "SYSTEM ADMINISTRATOR");
//		
//		Map<String, Object> templateParameters = new HashMap<>();
//		templateParameters.put("user", user);
//		
//		MsMsgTemplate template = this.messageTemplateLogic.getAndParseContent(templateCode, templateParameters);
//		Assertions.assertNull(template.getSubject());
//	}
//	
//	@Test
//	void testTemplateCodeDummyTemplate() throws ParseException {
//		final String templateCode = "DUMMY_TEMPLATE";
//		Map<String, Object> user = new HashMap<>();		
//		user.put("loginId", "administrator");
//		user.put("fullname", "SYSTEM ADMINISTRATOR");
//		user.put("dtmCrt", DateUtils.parseDate("04/21/2000 21:04:00", "MM/dd/yyyy HH:mm:ss"));
//		
//		Map<String, Object> templateParameters = new HashMap<>();
//		templateParameters.put("user", user);
//		
//		MsMsgTemplate template = this.messageTemplateLogic.getAndParseContent(templateCode, templateParameters);
//		Assertions.assertEquals(
//				"Welcome to esign SYSTEM ADMINISTRATOR (administrator)",
//				template.getSubject());
//	}
}
