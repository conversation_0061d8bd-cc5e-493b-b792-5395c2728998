package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendorRegisteredUserHistory;
import com.adins.esign.model.custom.EmailBean;

public interface VendorRegisteredUserDao {
	
	void insertVendorRegisteredUser(MsVendorRegisteredUser newVendorRegisteredUser);
	void insertVendorRegisteredUserNewTran(MsVendorRegisteredUser newVendorRegisteredUser);
	void updateVendorRegisteredUser(MsVendorRegisteredUser vRUser);
	void updateVendorRegisteredUserNewTran(MsVendorRegisteredUser vRUser);
	
	List<MsVendorRegisteredUser> getListVendorRegisteredUserByLoginId(String loginId);
	List<MsVendorRegisteredUser> getListVendorRegisteredUserBySignerRegisteredEmail(String email);
	List<MsVendorRegisteredUser> getListVendorRegisteredUserByPhone(String phone);
	List<MsVendorRegisteredUser> getListVendorRegisteredUserByIdMsUser(long idMsUser);
	
	List<MsVendorRegisteredUser> getListVendorUserByIdMsUserAndEmailService(Long idMsUser, String emailService);
	List<MsVendorRegisteredUser> getListVendorUserByPhone(String phone, String vendorCode);
	List<MsVendorRegisteredUser> getListVendorUserByEmail(String email, String vendorCode);
	
	/**
	 * <b>WARNING</b> - Should not be used for multi PSrE case
	 */
	MsVendorRegisteredUser getVendorRegisteredUserByLoginId(String loginId);
	
	/**
	 * <b>WARNING</b> - Should not be used for multi PSrE case
	 */
	MsVendorRegisteredUser getVendorRegisteredUserBySignerRegisteredEmail(String email);
	
	/**
	 * <b>WARNING</b> - Should not be used for multi PSrE case
	 */
	MsVendorRegisteredUser getVendorRegisteredUserByPhoneNumber(String phoneNum);
	
	MsVendorRegisteredUser getVendorRegisteredUserByIdMsVendorRegisteredUser(long vendorRegisteredUserId);
	
	MsVendorRegisteredUser getVendorRegisteredUserByLoginIdAndVendorCode(String loginId, String vendorCode);
	MsVendorRegisteredUser getEveryVendorRegisteredByLoginIdAndVendorCode(String loginId, String vendorCode);
	
	// Get latest ms_vendor_registered_user by phone / email / idNo
	MsVendorRegisteredUser getLatestVendorRegisteredUserByPhoneNumber(String phoneNum);
	MsVendorRegisteredUser getLatestVendorRegisteredUserBySignerRegisteredEmail(String email);
	MsVendorRegisteredUser getLatestVendorRegisteredUserByidNo(String idNo);
	
	// Get ms_vendor_registered_user by email / phone / email and vendor_code
	MsVendorRegisteredUser getVendorRegisteredUserByPhoneAndVendorCode(String phone, String vendorCode);
	MsVendorRegisteredUser getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(String email, String vendorCode);
	MsVendorRegisteredUser getVendorRegisteredUserByIdNoAndVendorCode(String idNo, String vendorCode);
	MsVendorRegisteredUser getActiveVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(String email, String vendorCode);

	
	MsVendorRegisteredUser getVendorRegisteredUserByIdMsUserAndVendorCode(long idMsUser, String vendorCode);
	MsVendorRegisteredUser getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(long idMsUser, String vendorCode);
	
	List<EmailBean> getUserRegisteredEmailByNik(String idNo);
	List<String> getUserRegisteredVendorsByNik(String idNo);
	List<String> getUserRegisteredVendorsByIdMsUser(Long idUser);
	
	String getVendorAccessTokenByVendorCodeAndidMsUser(String vendorCode, long idUser);
	MsVendorRegisteredUser getLatestVendorRegisteredUserBySignerRegisteredEmailandDtmUpd(String signerRegisteredEmail);
	MsVendorRegisteredUser getLatestVendorRegisteredUserByPhoneandDtmUpd(String phone);

	
}
