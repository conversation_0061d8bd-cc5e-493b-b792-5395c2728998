package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.webservices.frontend.api.TenantService;
import com.adins.esign.webservices.model.AddTenantRequest;
import com.adins.esign.webservices.model.AddTenantResponse;
import com.adins.esign.webservices.model.CheckLivenessFaceCompareServiceRequest;
import com.adins.esign.webservices.model.CheckLivenessFaceCompareServiceResponse;
import com.adins.esign.webservices.model.EditTenantRequest;
import com.adins.esign.webservices.model.EditTenantResponse;
import com.adins.esign.webservices.model.GetAutomaticStampingAfterSignRequest;
import com.adins.esign.webservices.model.GetAutomaticStampingAfterSignResponse;
import com.adins.esign.webservices.model.GetAvailableSendingPointInvitationRequest;
import com.adins.esign.webservices.model.GetAvailableSendingPointInvitationResponse;
import com.adins.esign.webservices.model.GetAvailableSendingPointRequest;
import com.adins.esign.webservices.model.GetAvailableSendingPointResponse;
import com.adins.esign.webservices.model.GetListTenantRequest;
import com.adins.esign.webservices.model.GetListTenantResponse;
import com.adins.esign.webservices.model.GetSmsDeliverySettingRequest;
import com.adins.esign.webservices.model.GetSmsDeliverySettingResponse;
import com.adins.esign.webservices.model.GetStatusEmailServiceTenantRequest;
import com.adins.esign.webservices.model.GetStatusEmailServiceTenantResponse;
import com.adins.esign.webservices.model.GetTenantRekonRequest;
import com.adins.esign.webservices.model.GetTenantRekonResponse;
import com.adins.esign.webservices.model.GetUploadUrlRequest;
import com.adins.esign.webservices.model.GetUploadUrlResponse;
import com.adins.esign.webservices.model.TenantDetailRequest;
import com.adins.esign.webservices.model.TenantDetailResponse;
import com.adins.esign.webservices.model.TenantSettingsEmbedRequest;
import com.adins.esign.webservices.model.TenantSettingsRequest;
import com.adins.esign.webservices.model.TenantSettingsResponse;
import com.adins.esign.webservices.model.TestTenantCallbackRequest;
import com.adins.esign.webservices.model.TestTenantCallbackResponse;
import com.adins.esign.webservices.model.TryTenantCallbackRequest;
import com.adins.esign.webservices.model.TryTenantCallbackResponse;
import com.adins.esign.webservices.model.UpdateDeliverySettingRequest;
import com.adins.esign.webservices.model.UpdateDeliverySettingResponse;
import com.adins.esign.webservices.model.UpdateTenantSettingsRequest;
import com.adins.esign.webservices.model.UpdateTenantSettingsResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/tenant")
@Api(value = "TenantService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericTenantServiceEndpoint implements TenantService {

	@Autowired private TenantLogic tenantLogic;
	
	@Override
	@POST
	@Path("/s/getTenantSettings")
	public TenantSettingsResponse getTenantSettings(TenantSettingsRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();
		return tenantLogic.getTenantSettings(request, auditContext);
	}

	@Override
	@POST
	@Path("/s/updateTenantSettings")
	public UpdateTenantSettingsResponse updateTenantSettings(UpdateTenantSettingsRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.updateTenantSettings(request, audit);
	}

	@Override
	@POST
	@Path("/getTenantSettingsEmbed")
	public TenantSettingsResponse getTenantSettingsEmbed(TenantSettingsEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getTenantSettingsEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/s/addTenant")
	public AddTenantResponse addTenant(AddTenantRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.addTenant(request, audit);
	}

	@Override
	@POST
	@Path("/s/editTenant")
	public EditTenantResponse editTenant(EditTenantRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.editTenant(request, audit);
	}

	@Override
	@POST
	@Path("/s/tenantDetail")
	public TenantDetailResponse getTenantDetail(TenantDetailRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getTenantDetail(request, audit);
	}

	@Override
	@POST
	@Path("/s/listTenant")
	public GetListTenantResponse getListTenant(GetListTenantRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getListTenantPaging(request, audit);
	}

	@Override
	@POST
	@Path("/s/getSmsDeliverySetting")
	public GetSmsDeliverySettingResponse getSmsDeliverySetting(GetSmsDeliverySettingRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();
		return tenantLogic.getSmsDeliverySettingDetail(request, auditContext);
	}
	
	@Override
	@POST
	@Path("/s/updateSmsDeliverySetting")
	public UpdateDeliverySettingResponse updateDeliverySetting(UpdateDeliverySettingRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.updateDeliverySetting(request, audit);
	}

	@Override
	@POST
	@Path("/getStatusEmailServiceTenant")
	public GetStatusEmailServiceTenantResponse getStatusEmailServiceTenant(GetStatusEmailServiceTenantRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getStatusEmailServiceTenant(request, audit);
	}

	@Override
	@POST
	@Path("/getAutomaticStampingAfterSign")
	public GetAutomaticStampingAfterSignResponse getAutomaticStampingAfterSign(
			GetAutomaticStampingAfterSignRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getAutomaticStampingAfterSign(request, audit);
	}

	@Override
	@POST
	@Path("/getUploadUrl")
	public GetUploadUrlResponse getUploadUrl(GetUploadUrlRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getUploadUrl(request, audit);
	}

	@Override
	@POST
	@Path("/s/getTenantRekon")
	public GetTenantRekonResponse getTenantRekon(GetTenantRekonRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getTenantRekon(request, audit);
	}

	@Override
	@POST
	@Path("/s/checkLivenessFaceCompareService")
	public CheckLivenessFaceCompareServiceResponse checkLivenessFaceCompareService(
			CheckLivenessFaceCompareServiceRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.checkLivenessFaceCompareService(request, audit);
	}

	@Override
	@POST
	@Path("/s/testCallback")
	public TestTenantCallbackResponse testCallback(TestTenantCallbackRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.testCallback(request, audit);
	}

	@Override
	@POST
	@Path("/s/tryCallback")
	public TryTenantCallbackResponse tryCallback(TryTenantCallbackRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.tryCallback(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/getAvailableSendingOptions")
	public GetAvailableSendingPointResponse getAvailableSendingPoint (GetAvailableSendingPointRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getAvailableSendingPoint(request, audit);
	}
	
	@Override
	@POST
	@Path("/getAvailableSendingOptionsInvitation")
	public GetAvailableSendingPointInvitationResponse getAvailableSendingPointInvitation (GetAvailableSendingPointInvitationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getAvailableSendingPointInvitation(request, audit);
	}
}
