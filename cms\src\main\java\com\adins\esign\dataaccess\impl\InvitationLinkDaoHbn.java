package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.InvitationLinkDao;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.custom.InvitationReportBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.model.custom.InvitationBean;

@Transactional
@Component
public class InvitationLinkDaoHbn extends BaseDaoHbn implements InvitationLinkDao{
	
	@Override
	public void updateInvitationLink(TrInvitationLink invitationLink) {
		invitationLink.setUsrUpd(MssTool.maskData(invitationLink.getUsrUpd()));
		this.managerDAO.update(invitationLink);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateInvitationLinkNewTran(TrInvitationLink invitationLink) {
		invitationLink.setUsrUpd(MssTool.maskData(invitationLink.getUsrUpd()));
		this.managerDAO.update(invitationLink);
	}
	
	@Override
	public TrInvitationLink getInvitationLinkByInvitationCode(String invitationCode) {
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "left join fetch il.msVendor mv "
				+ "left join fetch il.lovUserType lut "
				+ "left join fetch il.msOffice mo "
				+ "left join fetch il.msBusinessLine mbl "
				+ "where il.invitationCode = :invitationCode ", 
						new Object[][] {{TrInvitationLink.INVITATION_CODE_HBN, invitationCode}});
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrInvitationLink getInvitationLinkByInvitationCodeNewTran(String invitationCode) {
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "left join fetch mt.msEmailHosting eh "
				+ "left join fetch il.msVendor mv "
				+ "left join fetch il.lovUserType lut "
				+ "left join fetch il.msOffice mo "
				+ "left join fetch il.msBusinessLine mbl "
				+ "where il.invitationCode = :invitationCode ", 
						new Object[][] {{TrInvitationLink.INVITATION_CODE_HBN, invitationCode}});
	}

	@Override
	public void insertInvitationLink(TrInvitationLink invLink) {
		invLink.setUsrCrt(MssTool.maskData(invLink.getUsrCrt()));
		this.managerDAO.insert(invLink);
	}

	@Override
	public TrInvitationLink getInvitationLinkByRecieverDetail(String recieverDetail) {
		if (StringUtils.isBlank(recieverDetail)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "where il.receiverDetail = :receiverDetail", 
						new Object[][] {{TrInvitationLink.RECEIVER_DETAIL_HBM, StringUtils.upperCase(recieverDetail)}});
	}

	@Override
	public void deleteInvitationLink(TrInvitationLink invLink) {
		this.managerDAO.delete(invLink);
	}

	@Override
	public TrInvitationLink getInvitationByIdNo(String idNo) {
		if (StringUtils.isBlank(idNo)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "where il.idNo = :idNo", 
						new Object[][] {{TrInvitationLink.IDNO_HBM, StringUtils.upperCase(idNo)}});
	}
	
	@Override
	public TrInvitationLink getInvitationByIdNoV2(String idNo, String vendorCode) {
		if (StringUtils.isBlank(idNo)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "join fetch il.msVendor mv "
				+ "where il.idNo = :idNo and mv.vendorCode = :vendorCode", 
						new Object[][] {{TrInvitationLink.IDNO_HBM, StringUtils.upperCase(idNo)},
										{MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode)}});
	}

	@Override
	public TrInvitationLink getInvitationByPhone(String phone) {
		if (StringUtils.isBlank(phone)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "where il.phone = :phone", 
						new Object[][] {{TrInvitationLink.PHONE_HBM, StringUtils.upperCase(phone)}});
	}

	@Override
	public TrInvitationLink getInvitationByEmail(String email) {
		if (StringUtils.isBlank(email)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "where il.email = :email", 
						new Object[][] {{TrInvitationLink.EMAIL_HBM, StringUtils.upperCase(email)}});
	}
	
	private String constructParamTenant(Map<String, Object> params, String tenantCode) {
		StringBuilder query = new StringBuilder();
		if (StringUtils.isNotBlank(tenantCode)) {
			query.append(" and tenant_code LIKE :tenantCode ");
			params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		}
		
		return query.toString();
	}
	
	private String constructParamInvitation(Map<String, Object> params, String nama,
			String penerimaUndangan, Date tanggalPengirimanDari, Date tanggalPengirimanSampai, 
			String statusUndangan, String statusRegistrasi, String pengirimanMelalui) {
		StringBuilder query = new StringBuilder();
		
		if (StringUtils.isNotBlank(nama)) {
			query.append(" and il.full_name LIKE :nama ");
			params.put("nama", "%" + StringUtils.upperCase(nama) + "%");
		}
		
		if (StringUtils.isNotBlank(pengirimanMelalui)) {
			query.append(" and il.invitation_by LIKE :pengirimanMelalui ");
			params.put("pengirimanMelalui", pengirimanMelalui);
		}
		
		if (StringUtils.isNotBlank(penerimaUndangan)) {
			query.append(" and il.receiver_detail LIKE :penerimaUndangan ");
			params.put("penerimaUndangan", "%" + StringUtils.upperCase(penerimaUndangan) + "%");
		}
		
		if (StringUtils.isNotBlank(statusUndangan)) {
			query.append(" and il.is_active LIKE :statusUndangan ");
			params.put("statusUndangan", StringUtils.upperCase(statusUndangan));
		}
		
		if (StringUtils.isNotBlank(statusRegistrasi)) {
			query.append(" and msuser.is_registered LIKE :statusRegistrasi ");
			params.put("statusRegistrasi", StringUtils.upperCase(statusRegistrasi));
		}
		
		if(tanggalPengirimanDari != null && tanggalPengirimanSampai != null) {			
			query.append("and il.dtm_crt >= :tanggalPengirimanDari and il.dtm_crt <= :tanggalPengirimanSampai ");
			params.put("tanggalPengirimanDari", tanggalPengirimanDari);
			params.put("tanggalPengirimanSampai", tanggalPengirimanSampai);
		} else {
			 	 query.append("and il.dtm_crt >= date_trunc('MONTH', now()) and il.dtm_crt <= now() ");
			 
		}
		return query.toString();
	}

	@Override
	public List<InvitationBean> getListInvitation( int min, int max, Date tanggalPengirimanDari, Date tanggalPengirimanSampai, 
			String tenantCode, String nama, String penerimaUndangan, String pengirimanMelalui, String statusUndangan, String statusRegistrasi) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		
		String paramQueryTenant = this.constructParamTenant(params, tenantCode);		
		String paramQueryInvitation = this.constructParamInvitation(params, nama, penerimaUndangan, 
				tanggalPengirimanDari, tanggalPengirimanSampai, statusUndangan, statusRegistrasi, pengirimanMelalui);
		
		StringBuilder query = new StringBuilder();
		query.append(" with ilLink AS ")
		 .append(" ( ")
	 	 .append(" SELECT il.full_name AS \"nama\", il.invitation_by AS \"pengirimanMelalui\", il.receiver_detail AS \"penerimaUndangan\",  ")
	 	 .append(" TO_CHAR(il.dtm_crt, 'DD Mon YYYY HH24:MI:SS') AS \"tanggalPengiriman\", ")
	 	 .append(" CASE WHEN msuser.is_registered = '1' and dh.dtm_crt is not null and EXTRACT (epoch FROM (msuser.dtm_crt - dh.dtm_crt))/60 >=1 THEN TO_CHAR(msuser.dtm_upd, 'DD Mon YYYY') ")
	 	 	.append(" WHEN msuser.is_registered = '1' THEN TO_CHAR(msuser.dtm_crt, 'DD Mon YYYY HH24:MI:SS') ELSE null ")
	 	 	.append(" END as \"tanggalRegistrasi\", ")
	 	 .append(" CASE WHEN msuser.is_registered = '1' THEN 'DONE' ")
	 	 	.append(" ELSE 'NOT DONE'  ")
	 	 	.append(" END AS \"statusRegistrasi\", ")
	 	 .append(" CASE WHEN il.is_active = '1' THEN 'AKTIF' ")
	 	 	.append(" WHEN il.is_active = '0' THEN 'NON AKTIF'   ")
	 	 	.append(" END AS \"statusUndangan\",  ")
	 	 .append(" row_number() over (order by il.id_invitation_link) AS \"rowNum\" ")
	 	 .append(" FROM tr_invitation_link il ")
	 	 .append(" JOIN ms_tenant AS tent ON tent.id_ms_tenant = il.id_ms_tenant ")
	 	 .append(" LEFT JOIN LATERAL ( ")
	 	 	.append(" select mu.id_ms_user, vru.is_registered, vru.dtm_crt, vru.dtm_upd ")
	 	 	.append(" from am_msuser mu ")
	 	 	.append(" join ms_vendor_registered_user vru on mu.id_ms_user = vru.id_ms_user ")
	 	 	.append(" where mu.full_name = UPPER(il.full_name)  ")
	 	 	.append(" AND LENGTH(il.full_name)>0 ")
	 	 	.append(" AND DATE_PART('day', vru.dtm_crt - mu.dtm_crt) <=14 ")
	 	 	.append(" limit 1 ")
	 	 .append(" ) msuser on true ")
	 	 .append(" LEFT JOIN lateral( ")
	 	 	.append(" SELECT dh.dtm_crt ")
	 	 	.append(" FROM tr_document_d_sign dds ")
	 	 	.append(" join tr_document_d dd on dds.id_document_d = dd.id_document_d ")
	 	 	.append(" join tr_document_h dh on dd.id_document_h = dh.id_document_h ")
	 	 	.append(" WHERE dds.id_ms_user = msuser.id_ms_user ")
	 	 	.append(" ORDER BY dds.dtm_crt ASC LIMIT 1 ")
	 	 .append(" ) dh ON TRUE ")
	 	 .append(" WHERE 1 = 1 ")
	.append(paramQueryTenant)
	.append(paramQueryInvitation)
	.append(" order by il.dtm_crt ")
	.append(" ) ")
	.append(" select \"nama\", \"pengirimanMelalui\", \"penerimaUndangan\", \"tanggalPengiriman\", \"tanggalRegistrasi\", \"statusRegistrasi\", \"statusUndangan\" ")
	.append(" from ilLink ")
	.append(" WHERE \"rowNum\" between :min and :max  ");
		
		
		return this.managerDAO.selectForListString(InvitationBean.class, query.toString(), params, null);
	}

	@Override
	public Integer countListInvitation( String tenantCode, String nama, String pengirimanMelalui, String penerimaUndangan, 
			String statusUndangan, String statusRegistrasi, Date tanggalPengirimanDari, Date tanggalPengirimanSampai) {
		Map<String, Object> params = new HashMap<>();
		
		
		String paramQueryTenant = this.constructParamTenant(params, tenantCode);		
		String paramQueryInvitation = this.constructParamInvitation(params, nama, penerimaUndangan, tanggalPengirimanDari, 
				tanggalPengirimanSampai, statusUndangan, statusRegistrasi, pengirimanMelalui);
		
		StringBuilder query = new StringBuilder();
		query.append(" with ilLink AS ")
		 .append(" ( ")
	 	 .append(" SELECT il.full_name AS \"nama\", il.invitation_by AS \"pengirimanMelalui\", il.receiver_detail AS \"penerimaUndangan\",  ")
	 	 .append(" TO_CHAR(il.dtm_crt, 'DD Mon YYYY HH24:MI:SS') AS \"tanggalPengiriman\", ")
	 	 .append(" CASE WHEN msuser.is_registered = '1' and dh.dtm_crt is not null and EXTRACT (epoch FROM (msuser.dtm_crt - dh.dtm_crt))/60 >=1 THEN TO_CHAR(msuser.dtm_upd, 'DD Mon YYYY') ")
	 	 	.append(" WHEN msuser.is_registered = '1' THEN TO_CHAR(msuser.dtm_crt, 'DD Mon YYYY HH24:MI:SS') ELSE null ")
	 	 	.append(" END as \"tanggalRegistrasi\", ")
	 	 .append(" CASE WHEN msuser.is_registered = '1' THEN 'DONE' ")
	 	 	.append(" ELSE 'NOT DONE'  ")
	 	 	.append(" END AS \"statusRegistrasi\", ")
	 	 .append(" CASE WHEN il.is_active = '1' THEN 'AKTIF' ")
	 	 	.append(" WHEN il.is_active = '0' THEN 'NON AKTIF'   ")
	 	 	.append(" END AS \"statusUndangan\",  ")
	 	 .append(" row_number() over (order by il.id_invitation_link) AS \"rowNum\" ")
	 	 .append(" FROM tr_invitation_link il ")
	 	 .append(" JOIN ms_tenant AS tent ON tent.id_ms_tenant = il.id_ms_tenant ")
	 	 .append(" LEFT JOIN LATERAL ( ")
	 	 	.append(" select mu.id_ms_user, vru.is_registered, vru.dtm_crt, vru.dtm_upd ")
	 	 	.append(" from am_msuser mu ")
	 	 	.append(" join ms_vendor_registered_user vru on mu.id_ms_user = vru.id_ms_user ")
	 	 	.append(" where mu.full_name = UPPER(il.full_name)  ")
	 	 	.append(" AND LENGTH(il.full_name)>0 ")
	 	 	.append(" AND DATE_PART('day', vru.dtm_crt - mu.dtm_crt) <=14 ")
	 	 	.append(" limit 1 ")
	 	 .append(" ) msuser on true ")
	 	 .append(" LEFT JOIN lateral( ")
	 	 	.append(" SELECT dh.dtm_crt ")
	 	 	.append(" FROM tr_document_d_sign dds ")
	 	 	.append(" join tr_document_d dd on dds.id_document_d = dd.id_document_d ")
	 	 	.append(" join tr_document_h dh on dd.id_document_h = dh.id_document_h ")
	 	 	.append(" WHERE dds.id_ms_user = msuser.id_ms_user ")
	 	 	.append(" ORDER BY dds.dtm_crt ASC LIMIT 1 ")
	 	 .append(" ) dh ON TRUE ")
	 	 .append(" WHERE 1 = 1 ")
	.append(paramQueryTenant)
	.append(paramQueryInvitation)
	.append(" order by il.dtm_crt ")
	.append(" ) ")
	.append(" select COUNT(\"pengirimanMelalui\") ")
	.append(" from ilLink ");



		
		BigInteger totalData = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		
		return totalData.intValue();
	}

	@Override
	public List<InvitationReportBean> getListInvitationReport(Date tanggalPengirimanDari, Date tanggalPengirimanSampai,
			String tenantCode, String nama, String pengirimanMelalui, String penerimaUndangan, String statusUndangan,
			String statusRegistrasi) {
		Map<String, Object> params = new HashMap<>();

		String paramQueryTenant = this.constructParamTenant(params, tenantCode);		
		String paramQueryInvitation =this.constructParamInvitation(params, nama, penerimaUndangan, tanggalPengirimanDari, tanggalPengirimanSampai, 
				statusUndangan, statusRegistrasi, pengirimanMelalui);
		
		
		StringBuilder query = new StringBuilder();
		query.append(" with ilLink AS ")
		 .append(" ( ")
	 	 .append(" SELECT il.full_name AS \"nama\", il.invitation_by AS \"pengirimanMelalui\", il.receiver_detail AS \"penerimaUndangan\",  ")
	 	 .append(" TO_CHAR(il.dtm_crt, 'DD Mon YYYY HH24:MI:SS') AS \"tanggalPengiriman\", ")
	 	 .append(" CASE WHEN msuser.is_registered = '1' and dh.dtm_crt is not null and EXTRACT (epoch FROM (msuser.dtm_crt - dh.dtm_crt))/60 >=1 THEN TO_CHAR(msuser.dtm_upd, 'DD Mon YYYY') ")
	 	 	.append(" WHEN msuser.is_registered = '1' THEN TO_CHAR(msuser.dtm_crt, 'DD Mon YYYY HH24:MI:SS') ELSE null ")
	 	 	.append(" END as \"tanggalRegistrasi\", ")
	 	 .append(" CASE WHEN msuser.is_registered = '1' THEN 'DONE' ")
	 	 	.append(" ELSE 'NOT DONE'  ")
	 	 	.append(" END AS \"statusRegistrasi\", ")
	 	 .append(" CASE WHEN il.is_active = '1' THEN 'AKTIF' ")
	 	 	.append(" WHEN il.is_active = '0' THEN 'NON AKTIF'   ")
	 	 	.append(" END AS \"statusUndangan\",  ")
	 	 .append(" row_number() over (order by il.id_invitation_link) AS \"rowNum\" ")
	 	 .append(" FROM tr_invitation_link il ")
	 	 .append(" JOIN ms_tenant AS tent ON tent.id_ms_tenant = il.id_ms_tenant ")
	 	 .append(" LEFT JOIN LATERAL ( ")
	 	 	.append(" select mu.id_ms_user, vru.is_registered, vru.dtm_crt, vru.dtm_upd ")
	 	 	.append(" from am_msuser mu ")
	 	 	.append(" join ms_vendor_registered_user vru on mu.id_ms_user = vru.id_ms_user ")
	 	 	.append(" where mu.full_name = UPPER(il.full_name)  ")
	 	 	.append(" AND LENGTH(il.full_name)>0 ")
	 	 	.append(" AND DATE_PART('day', vru.dtm_crt - mu.dtm_crt) <=14 ")
	 	 	.append(" limit 1 ")
	 	 .append(" ) msuser on true ")
	 	 .append(" LEFT JOIN lateral( ")
	 	 	.append(" SELECT dh.dtm_crt ")
	 	 	.append(" FROM tr_document_d_sign dds ")
	 	 	.append(" join tr_document_d dd on dds.id_document_d = dd.id_document_d ")
	 	 	.append(" join tr_document_h dh on dd.id_document_h = dh.id_document_h ")
	 	 	.append(" WHERE dds.id_ms_user = msuser.id_ms_user ")
	 	 	.append(" ORDER BY dds.dtm_crt ASC LIMIT 1 ")
	 	 .append(" ) dh ON TRUE ")
	 	 .append(" WHERE 1 = 1 ")
	.append(paramQueryTenant)
	.append(paramQueryInvitation)
	.append(" order by il.dtm_crt ")
	.append(" ) ")
	.append(" select \"nama\", \"pengirimanMelalui\", \"penerimaUndangan\", \"tanggalPengiriman\", \"tanggalRegistrasi\", \"statusRegistrasi\", \"statusUndangan\" ")
	.append(" from ilLink ");
	
	
		return this.managerDAO.selectForListString(InvitationReportBean.class, query.toString(), params, null);
	}

	@Override
	public TrInvitationLink getInvLinkByReceiverDetailAndVendorCode(String receiverDetail, String vendorCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("receiverDetail", receiverDetail);
		params.put("vendorCode", vendorCode);
		
		return this.managerDAO.selectOne("from TrInvitationLink il "
										 + "join fetch il.msVendor v "
										 + "where v.vendorCode = :vendorCode and il.receiverDetail = :receiverDetail ", params);
	}
	
	@Override
	public TrInvitationLink getInvitationLinkByRecieverDetailAndIdMsVendor(String recieverDetail, Long idMsVendor) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrInvitationLink.RECEIVER_DETAIL_HBM, StringUtils.upperCase(recieverDetail));
		params.put(MsVendor.ID_VENDOR_HBM, idMsVendor);
		if (StringUtils.isBlank(recieverDetail)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "join fetch il.msVendor mv "
				+ "where il.receiverDetail = :receiverDetail "
				+ "and mv.idMsVendor = :idMsVendor", 
						params);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrInvitationLink getInvitationLinkByRecieverDetailAndIdMsVendorNewTrx(String recieverDetail, Long idMsVendor) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrInvitationLink.RECEIVER_DETAIL_HBM, StringUtils.upperCase(recieverDetail));
		params.put(MsVendor.ID_VENDOR_HBM, idMsVendor);
		if (StringUtils.isBlank(recieverDetail)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "join fetch il.msVendor mv "
				+ "where il.receiverDetail = :receiverDetail "
				+ "and mv.idMsVendor = :idMsVendor", 
						params);
	}

	@Override
	public TrInvitationLink getInvitationByPhoneAndVendorCode(String phone, String vendorCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrInvitationLink.RECEIVER_DETAIL_HBM, phone);
		params.put(MsVendor.VENDOR_CODE_HBM, vendorCode);
		if (StringUtils.isBlank(phone)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "join fetch il.msVendor mv "
				+ "left join fetch il.amMsuser mu "
				+ "where (il.phone = :receiverDetail or il.receiverDetail = :receiverDetail) "
				+ "and mv.vendorCode = :vendorCode", 
						params);
	}

	@Override
	public TrInvitationLink getInvitationByEmailAndVendorCode(String email, String vendorCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrInvitationLink.RECEIVER_DETAIL_HBM, StringUtils.upperCase(email));
		params.put(MsVendor.VENDOR_CODE_HBM, vendorCode);
		if (StringUtils.isBlank(email)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "join fetch il.msVendor mv "
				+ "left join fetch il.amMsuser mu "
				+ "where (il.email = :receiverDetail or il.receiverDetail = :receiverDetail) "
				+ "and mv.vendorCode = :vendorCode", 
						params);
	}

	@Override
	public TrInvitationLink getInvitationByIdNoAndVendorCode(String idNo, String vendorCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrInvitationLink.RECEIVER_DETAIL_HBM, idNo);
		params.put(MsVendor.VENDOR_CODE_HBM, vendorCode);
		if (StringUtils.isBlank(idNo)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "join fetch il.msVendor mv "
				+ "left join fetch il.amMsuser mu "
				+ "where il.idNo = :receiverDetail "
				+ "and mv.vendorCode = :vendorCode", 
						params);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrInvitationLink> getListInvitationLinkByRecieverDetail(String recieverDetail) {
		if (StringUtils.isBlank(recieverDetail)) {
			return Collections.emptyList();
		}

		Object[][] queryParams = {{TrInvitationLink.RECEIVER_DETAIL_HBM, StringUtils.upperCase(recieverDetail)}};
		return (List<TrInvitationLink>) this.managerDAO.list(" from TrInvitationLink il "
				+ " join fetch il.msTenant mt "
				+ " join fetch il.msVendor mv"
				+ " where il.receiverDetail = :receiverDetail ", queryParams).get(GlobalKey.MAP_RESULT_LIST);
		
	}
	
	
	@SuppressWarnings("unchecked")
	@Override
	public List<TrInvitationLink> getListInvitationByIdNo(String idNo) {
		if (StringUtils.isBlank(idNo)) {
			return Collections.emptyList();
		}
		
		Object[][] queryParams = {{TrInvitationLink.IDNO_HBM, StringUtils.upperCase(idNo)}};
		return (List<TrInvitationLink>) this.managerDAO.list(" from TrInvitationLink il "
				+ " join fetch il.msTenant mt "
				+ " join fetch il.msVendor mv"
				+ " where il.idNo = :idNo ", queryParams).get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<TrInvitationLink> getListInvitationByPhone(String phone) {
		if (StringUtils.isBlank(phone)) {
			return Collections.emptyList();
		}
		
		Object[][] queryParams = {{TrInvitationLink.PHONE_HBM, StringUtils.upperCase(phone)}};
		return (List<TrInvitationLink>) this.managerDAO.list(" from TrInvitationLink il "
				+ " join fetch il.msTenant mt "
				+ " join fetch il.msVendor mv"
				+ " where il.phone = :phone or il.receiverDetail = :phone ", queryParams).get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<TrInvitationLink> getListInvitationByEmail(String email) {
		if (StringUtils.isBlank(email)) {
			return Collections.emptyList();
		}
		
		Object[][] queryParams = {{TrInvitationLink.EMAIL_HBM, StringUtils.upperCase(email)}};
		return (List<TrInvitationLink>) this.managerDAO.list(" from TrInvitationLink il "
				+ " join fetch il.msTenant mt "
				+ " join fetch il.msVendor mv"
				+ " where il.email = :email or il.receiverDetail = :email ", queryParams).get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@Override
	public TrInvitationLink getInvitationLinkByRecieverDetailV2(String recieverDetail, String vendorCode) {
		if (StringUtils.isBlank(recieverDetail)) {
			return null;
		}
		Map<String, Object> params = new HashMap<>();
		params.put(TrInvitationLink.RECEIVER_DETAIL_HBM, StringUtils.upperCase(recieverDetail));
		params.put(MsVendor.VENDOR_CODE_HBM, vendorCode);
		
		return this.managerDAO.selectOne(
				" from TrInvitationLink il "
				+ " join fetch il.msTenant mt "
				+ " join fetch il.msVendor mv "
				+ " where il.receiverDetail = :receiverDetail "
				+ " and mv.vendorCode = :vendorCode ",
				params);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrInvitationLink> getListInvitationLinkByRecieverDetail(String recieverDetail, String tenantCode) {
		if (StringUtils.isBlank(recieverDetail)) {
			return null;
		}

		Map<String, Object> params = new HashMap<>();
		params.put(TrInvitationLink.RECEIVER_DETAIL_HBM, StringUtils.upperCase(recieverDetail));
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		
		return (List<TrInvitationLink>) this.managerDAO.list(
				" from TrInvitationLink il "
				+ " join fetch il.msTenant mt "
				+ " join fetch il.msVendor mv"
				+ " where il.receiverDetail = :receiverDetail "
				+ "and mt.tenantCode = :tenantCode ", params).get(GlobalKey.MAP_RESULT_LIST);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrInvitationLink> getListInvitationByIdNo(String idNo, String tenantCode) {
		if (StringUtils.isBlank(idNo)) {
			return null;
		}
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrInvitationLink.IDNO_HBM, StringUtils.upperCase(idNo));
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		
		return (List<TrInvitationLink>) this.managerDAO.list(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "join fetch il.msVendor mv "
				+ "where il.idNo = :idNo "
				+ "and mt.tenantCode = :tenantCode ", params).get(GlobalKey.MAP_RESULT_LIST);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrInvitationLink> getListInvitationByPhone(String phone, String tenantCode) {
		if (StringUtils.isBlank(phone)) {
			return null;
		}
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrInvitationLink.PHONE_HBM, StringUtils.upperCase(phone));
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		
		return (List<TrInvitationLink>) this.managerDAO.list(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "join fetch il.msVendor mv "
				+ "where il.phone = :phone "
				+ "and mt.tenantCode = :tenantCode ", params).get(GlobalKey.MAP_RESULT_LIST);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrInvitationLink> getListInvitationByEmail(String email, String tenantCode) {
		if (StringUtils.isBlank(email)) {
			return null;
		}
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrInvitationLink.EMAIL_HBM, StringUtils.upperCase(email));
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		
		return (List<TrInvitationLink>) this.managerDAO.list(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "join fetch il.msVendor mv "
				+ "where il.email = :email "
				+ "and mt.tenantCode = :tenantCode ", params).get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public List<TrInvitationLink> getListInvitationByIdNoOtherVendor(String idNo, String vendorCode) {
		if (StringUtils.isBlank(idNo)) {
			return Collections.emptyList();
		}

		Object[][] queryParams = {{TrInvitationLink.IDNO_HBM, idNo}, 
				{"vendorCode", vendorCode}};
		
		StringBuilder query = new StringBuilder()
								.append("select distinct il.id_invitation_link ")
								.append("from tr_invitation_link il ")
								.append("join ms_vendor v on v.id_ms_vendor = il.id_ms_vendor ")
								.append("where id_no = :idNo and vendor_code != :vendorCode ");
		
		List<Map<String, Object>> idInvLinks = this.managerDAO.selectAllNativeString(query.toString(), queryParams);
		
		List<TrInvitationLink> invLinks = new ArrayList<>();
		Iterator<Map<String, Object>> itr = idInvLinks.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			BigInteger id = (BigInteger) map.get("d0");
			TrInvitationLink invLink = getInvitationLinkByIdInvitationLink(id);
			invLinks.add(invLink);
		}
		
		return invLinks;
	}

	@Override
	public List<TrInvitationLink> getListInvitationByPhoneOtherVendor(String phone, String vendorCode) {
		if (StringUtils.isBlank(phone)) {
			return Collections.emptyList();
		}

		Object[][] queryParams = {{TrInvitationLink.PHONE_HBM, phone}, 
				{MsVendor.VENDOR_CODE_HBM, vendorCode}};
		
		StringBuilder query = new StringBuilder()
				.append("select distinct il.id_invitation_link ")
				.append("from tr_invitation_link il ")
				.append("join ms_vendor v on v.id_ms_vendor = il.id_ms_vendor ")
				.append("where (phone = :phone or receiver_detail = :phone) and vendor_code != :vendorCode and il.is_active = '1' ");

		List<Map<String, Object>> idInvLinks = this.managerDAO.selectAllNativeString(query.toString(), queryParams);

		List<TrInvitationLink> invLinks = new ArrayList<>();
		Iterator<Map<String, Object>> itr = idInvLinks.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			BigInteger id = (BigInteger) map.get("d0");
			TrInvitationLink invLink = getInvitationLinkByIdInvitationLink(id);
			invLinks.add(invLink);
		}	

		return invLinks;
	}

	@Override
	public List<TrInvitationLink> getListInvitationByEmailOtherVendor(String email, String vendorCode) {
		if (StringUtils.isBlank(email)) {
			return Collections.emptyList();
		}

		Object[][] queryParams = {{TrInvitationLink.EMAIL_HBM, email}, 
				{MsVendor.VENDOR_CODE_HBM, vendorCode}};
		
		StringBuilder query = new StringBuilder()
				.append("select distinct il.id_invitation_link ")
				.append("from tr_invitation_link il ")
				.append("join ms_vendor v on v.id_ms_vendor = il.id_ms_vendor ")
				.append("where (email = :email or receiver_detail = :email) and vendor_code != :vendorCode and il.is_active = '1' ");

		List<Map<String, Object>> idInvLinks = this.managerDAO.selectAllNativeString(query.toString(), queryParams);

		List<TrInvitationLink> invLinks = new ArrayList<>();
		Iterator<Map<String, Object>> itr = idInvLinks.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			BigInteger id = (BigInteger) map.get("d0");
			TrInvitationLink invLink = getInvitationLinkByIdInvitationLink(id);
			invLinks.add(invLink);
		}	

		return invLinks;
	}
	
	private TrInvitationLink getInvitationLinkByIdInvitationLink(BigInteger id) {
		return this.managerDAO.selectOne(
				"from TrInvitationLink il " 
				+ "where il.idInvitationLink = :idInvitationLink ",
				new Object[][] {{GlobalVal.CONST_OBJECT_ID_INVITATION_LINK, id.longValue()}});
	}

	@Override
	public TrInvitationLink getLatestInvitationLinkByReceiverDetail(String recieverDetail) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrInvitationLink.RECEIVER_DETAIL_HBM, StringUtils.upperCase(recieverDetail));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_invitation_link ")
			.append("from tr_invitation_link ")
			.append("where receiver_detail = :receiverDetail ")
			.append("order by id_invitation_link desc limit 1 ");
		
		BigInteger idInvitationLink = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idInvitationLink) {
			return null;
		}
		
		return managerDAO.selectOne(
				"from TrInvitationLink il "
				+ "join fetch il.msTenant mt "
				+ "where il.idInvitationLink = :idInvitationLink ", new Object[][] {{GlobalVal.CONST_OBJECT_ID_INVITATION_LINK, idInvitationLink.longValue()}});
	}

	

	@Override
	public TrInvitationLink getInvitationLinkByPhoneAndVendorCodeOtherId(String phone, String vendorCode, Long idInvitationLink) {
		
		TrInvitationLink invLink = new TrInvitationLink();

		Object[][] queryParams = {{TrInvitationLink.PHONE_HBM, phone}, 
				{MsVendor.VENDOR_CODE_HBM, vendorCode},{GlobalVal.CONST_OBJECT_ID_INVITATION_LINK,idInvitationLink}};
		
		StringBuilder query = new StringBuilder()
				.append(" select il.id_invitation_link ")
				.append(" from tr_invitation_link il ")
				.append(" join ms_vendor v on v.id_ms_vendor = il.id_ms_vendor ")
				.append(" where (phone = :phone or receiver_detail = :phone) and vendor_code = :vendorCode and id_invitation_link <> :idInvitationLink ")
				.append(" order by il.dtm_crt asc limit 1 ");
		
		BigInteger idInvLink = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), queryParams);
		if (null == idInvLink) {
			return null;
		}
		
		invLink = getInvitationLinkByIdInvitationLink(idInvLink);
		
		return invLink;
		
	}

	@Override
	public TrInvitationLink getInvitationLinkByEmailAndVendorCodeOtherId(String email, String vendorCode, Long idInvitationLink) {
		
		TrInvitationLink invLink = new TrInvitationLink();

		String emailUser = email == null ? "" : email;
		Object[][] queryParams = {{TrInvitationLink.EMAIL_HBM, emailUser}, 
				{MsVendor.VENDOR_CODE_HBM, vendorCode},{"idInvitationLink",idInvitationLink}};

		
		StringBuilder query = new StringBuilder()
				.append(" select il.id_invitation_link ")
				.append(" from tr_invitation_link il ")
				.append(" join ms_vendor v on v.id_ms_vendor = il.id_ms_vendor ")
				.append(" where (email = :email or receiver_detail = :email) and vendor_code = :vendorCode and id_invitation_link <> :idInvitationLink ")
				.append(" order by il.dtm_crt asc limit 1 ");
		
		BigInteger idInvLink = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), queryParams);
		if (null == idInvLink) {
			return null;
		
		}
		invLink = getInvitationLinkByIdInvitationLink(idInvLink);
		
		return invLink;
		
	}
}
