package com.adins.esign.businesslogic.api.interfacing;

import java.io.IOException;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.<PERSON>Lov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.custom.TknajBulkSignResponse;
import com.adins.esign.model.custom.TknajDownloadDocResponse;
import com.adins.esign.model.custom.TknajRegisterCekResponse;
import com.adins.esign.model.custom.TknajUplDocResponse;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.tekenaja.TekenAjaLocationBean;
import com.adins.esign.webservices.model.DocumentConfinsRequestBean;
import com.adins.esign.webservices.model.InsertDocumentManualSignRequest;
import com.adins.esign.webservices.model.TekenAjaSignBulkRequest;
import com.adins.esign.webservices.model.TekenAjaSignRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.tekenaja.DownloadTekenAjaLinkResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaDownloadCertificateResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaHashSignRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaHashSignResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaHashSignSentOtpResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterApiResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterRequest;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface TekenAjaLogic {
	// Register
	TknajRegisterCekResponse registerCek(String nik, String action, MsVendor vendor, MsTenant tenant, AuditContext audit);
	TekenAjaRegisterApiResponse registerUser(TekenAjaRegisterRequest request, AuditContext audit);
	TekenAjaRegisterApiResponse registerUser(UserBean userData, MsVendor vendor, MsTenant tenant, AuditContext audit) throws IOException;
	AmMsuser insertRegisteredUser(UserBean userData, MsLov lovUserType, MsVendor vendor, MsTenant tenant, boolean isActivated, boolean externalActivation, AuditContext audit);
	
	// Document
	TknajUplDocResponse uploadDoc(DocumentConfinsRequestBean request, InsertDocumentManualSignRequest manualSignReq, String documentId, MsVendor vendor, MsTenant tenant, TrDocumentD docD, TrDocumentH docH, AuditContext audit) throws IOException;
	TknajDownloadDocResponse downloadDoc(String psreDocumentId, MsVendor vendor, MsTenant tenant, AuditContext audit) throws IOException;
	ViewDocumentResponse getPdfFromLinkTekenAja(DownloadTekenAjaLinkResponse responselink );
	
	// Province, City
	TekenAjaLocationBean getProvinceList(String vendorCode) throws IOException;
	TekenAjaLocationBean getDistrictList(String provinceKey, String vendorCode) throws IOException;
	TekenAjaLocationBean getSubDistrictList(String provinceKey, String districtKey, String vendorCode) throws IOException;
	
	// Signing
	TknajBulkSignResponse tekenAjaSign(TekenAjaSignRequest request, AuditContext audit) throws IOException;
	TknajBulkSignResponse tekenAjaSignBulk(TekenAjaSignBulkRequest request, AuditContext audit) throws IOException;
	
	/**
	 * @param gender "M" or "F"
	 * @exception TekenajaException thrown when gender is not "M" or "F"
	 */
	String formatGenderTekenAjaFormat(String gender, AuditContext audit);
	String buildRegisterErrorMessage(TekenAjaRegisterApiResponse registerResponse);
	String buildRegisterCekErrorMessage(TknajRegisterCekResponse response);
	TekenAjaDownloadCertificateResponse downloadCertificate (MsVendorRegisteredUser vRUser,MsTenant tenant, AuditContext audit);

	TekenAjaHashSignSentOtpResponse tekenAjaHashSignSendOtp(String identifier, String apikey, AuditContext audit);
	TekenAjaHashSignResponse tekenAjaHashSign(TekenAjaHashSignRequest request, AuditContext audit);
	
}
