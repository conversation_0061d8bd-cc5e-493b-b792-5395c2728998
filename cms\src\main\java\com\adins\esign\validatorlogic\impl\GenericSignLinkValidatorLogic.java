package com.adins.esign.validatorlogic.impl;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.model.TrSignLinkRequest;
import com.adins.esign.validatorlogic.api.SignLinkValidatorLogic;
import com.adins.exceptions.EmbedWebViewException;
import com.adins.exceptions.EmbedWebViewException.ReasonEmbedWebView;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericSignLinkValidatorLogic extends BaseLogic implements SignLinkValidatorLogic {
	
	@Autowired private CommonLogic commonLogic;

	private String decryptSignLinkCode(String encryptedCode, AuditContext audit) {
		try {
			return commonLogic.decryptMessageToString(encryptedCode, audit);
		} catch (Exception e) {
			throw new EmbedWebViewException(getMessage("businesslogic.embedwebview.decrypterror",
					null, audit), ReasonEmbedWebView.DECRYPT_ERROR);
		}
	}

	@Override
	public List<TrSignLinkRequest> validateGetSignLinkRequestsByEncryptedMsg(String encryptedMsg, AuditContext audit) {
		String signLinkCode = decryptSignLinkCode(encryptedMsg, audit);
		List<TrSignLinkRequest> requests = daoFactory.getSignLinkRequestDao().getSignLinkRequestsBySignLinkCode(signLinkCode);
		if (CollectionUtils.isEmpty(requests)) {
			throw new EmbedWebViewException(getMessage("businesslogic.embedwebview.signlinkrequestnotfound",
					null, audit), ReasonEmbedWebView.SIGN_LINK_REQUEST_NOT_FOUND);
		}
		
		return requests;
	}

}
