package com.adins.esign.webservices.frontend.api;

import java.text.ParseException;

import com.adins.esign.webservices.model.BalanceEmbedRequest;
import com.adins.esign.webservices.model.BalanceRequest;
import com.adins.esign.webservices.model.BalanceResponse;
import com.adins.esign.webservices.model.CheckThresholdRequest;
import com.adins.esign.webservices.model.CheckThresholdResponse;
import com.adins.esign.webservices.model.DownloadListBalanceMutationEmbedRequest;
import com.adins.esign.webservices.model.DownloadListBalanceMutationRequest;
import com.adins.esign.webservices.model.DownloadListBalanceMutationResponse;
import com.adins.esign.webservices.model.ExtendTopUpBalanceRequest;
import com.adins.esign.webservices.model.GetListBalanceMutationEmbedRequest;
import com.adins.esign.webservices.model.GetListTopupBalanceRequest;
import com.adins.esign.webservices.model.GetListTopupBalanceResponse;
import com.adins.esign.webservices.model.ListBalanceHistoryRequest;
import com.adins.esign.webservices.model.ListBalanceHistoryResponse;
import com.adins.esign.webservices.model.ListBalanceTenantRequest;
import com.adins.esign.webservices.model.ListBalanceTenantResponse;
import com.adins.esign.webservices.model.ListBalanceTypeByVendorAndTenantRequest;
import com.adins.esign.webservices.model.ListBalanceTypeByVendorAndTenantResponse;
import com.adins.esign.webservices.model.ListBalanceVendoroftenantRequest;
import com.adins.esign.webservices.model.ListBalanceVendoroftenantResponse;
import com.adins.esign.webservices.model.SaldoConfigurationRequest;
import com.adins.esign.webservices.model.SaldoConfigurationResponse;
import com.adins.esign.webservices.model.SignBalanceAvailabilityRequest;
import com.adins.esign.webservices.model.SignBalanceAvailabilityResponse;
import com.adins.esign.webservices.model.UpdateBalanceTenantRequest;
import com.adins.esign.webservices.model.UpdateBalanceTenantResponse;
import com.adins.esign.webservices.model.UpdateRefNumberRequest;
import com.adins.esign.webservices.model.UpdateRefNumberResponse;
import com.adins.framework.service.base.model.MssResponseType;

public interface SaldoService {
	//Balance 
	BalanceResponse getBalance(BalanceRequest request);
	BalanceResponse getBalanceEmbed(BalanceEmbedRequest request);
	BalanceResponse getBalanceAllTenant(BalanceRequest request);
	
	ListBalanceHistoryResponse getListBalanceHistory(ListBalanceHistoryRequest request) throws ParseException;
	SaldoConfigurationResponse configureSaldo(SaldoConfigurationRequest request) throws ParseException;
	ListBalanceTenantResponse getListBalanceTenant(ListBalanceTenantRequest request);
	UpdateBalanceTenantResponse updateBalanceTenant(UpdateBalanceTenantRequest request);
	ListBalanceVendoroftenantResponse getListBalanceVendoroftenant(ListBalanceVendoroftenantRequest request);
	CheckThresholdResponse checkThreshold(CheckThresholdRequest request) throws ParseException;
	ListBalanceHistoryResponse getListBalanceMutationEmbed(GetListBalanceMutationEmbedRequest request) throws ParseException;
	ListBalanceHistoryResponse getListBalanceMutationAllTenant(ListBalanceHistoryRequest request) throws ParseException;
	

	//export excel balance mutation
	DownloadListBalanceMutationResponse downloadListBalanceMutation(DownloadListBalanceMutationRequest request);
	DownloadListBalanceMutationResponse downloadListBalanceMutationEmbed(DownloadListBalanceMutationEmbedRequest request);
	DownloadListBalanceMutationResponse downloadListBalanceMutationAllTenant(DownloadListBalanceMutationRequest request);
	
	ListBalanceTypeByVendorAndTenantResponse getListBalanceTypeByVendorAndTenant(ListBalanceTypeByVendorAndTenantRequest request);
	
	SignBalanceAvailabilityResponse signBalanceAvailability(SignBalanceAvailabilityRequest request);
	
	GetListTopupBalanceResponse getListTopupBalance(GetListTopupBalanceRequest request);
	
	MssResponseType updateRefNumber(UpdateRefNumberRequest request);
	
	MssResponseType extendTopupBalance(ExtendTopUpBalanceRequest request);
}
