package com.adins.esign.webservices.confins.api;

import com.adins.esign.webservices.model.CancelAgreementRequest;
import com.adins.esign.webservices.model.CancelAgreementResponse;
import com.adins.esign.webservices.model.DocumentConfinsRequest;
import com.adins.esign.webservices.model.DocumentConfinsResponse;

public interface ConfinsDocumentService {
	DocumentConfinsResponse send(DocumentConfinsRequest documentConfinsRequest) throws Exception;
	CancelAgreementResponse cancelAgreement(CancelAgreementRequest request);
}
