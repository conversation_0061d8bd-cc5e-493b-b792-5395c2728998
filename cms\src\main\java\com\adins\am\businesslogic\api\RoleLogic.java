package com.adins.am.businesslogic.api;

import java.util.List;

import javax.annotation.security.RolesAllowed;

import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsTenant;
import com.adins.esign.webservices.model.AddMenuOfRoleRequest;
import com.adins.esign.webservices.model.AddRoleManagementRequest;
import com.adins.esign.webservices.model.AddRoleRequest;
import com.adins.esign.webservices.model.EditRoleManagementRequest;
import com.adins.esign.webservices.model.GetListRoleManagementRequest;
import com.adins.esign.webservices.model.GetListRoleManagementResponse;
import com.adins.esign.webservices.model.GetListRoleRequest;
import com.adins.esign.webservices.model.GetListRoleResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface RoleLogic {
	// AmMsrole
	List<AmMsrole> getListRoleByIdUser(long idUser, AuditContext callerId);
	List<AmMsrole> getListRoleByIdUserTenantCode(long idUser, String tenantCode, AuditContext callerId);
	AmMsrole insertRole(String roleCode, String roleName, MsTenant tenant, AuditContext audit);
	
	// AmMemberofrole
	void insertMemberofrole(AmMsrole role, AmMsuser user, AuditContext audit);
	
	@RolesAllowed("ROLE_USER_MANAGEMENT")
	MssResponseType addRole(AddRoleRequest request, AuditContext audit);
	@RolesAllowed("ROLE_USER_MANAGEMENT")
	MssResponseType addMenuOfRole(AddMenuOfRoleRequest request, AuditContext audit);
	GetListRoleResponse getListRole(GetListRoleRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_ROLE_MANAGEMENT")
	GetListRoleManagementResponse getListRoleManagement(GetListRoleManagementRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_ROLE_MANAGEMENT")
	MssResponseType addRoleManagement(AddRoleManagementRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_ROLE_MANAGEMENT")
	MssResponseType editRoleManagement(EditRoleManagementRequest request, AuditContext audit);
}