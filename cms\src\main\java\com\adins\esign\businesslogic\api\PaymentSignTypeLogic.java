package com.adins.esign.businesslogic.api;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.webservices.model.GetListPaymentTypeRequest;
import com.adins.esign.webservices.model.PaymentSignTypeListRequest;
import com.adins.esign.webservices.model.PaymentSignTypeListResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface PaymentSignTypeLogic {
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isValidTenant(#request.tenantCode, authentication) and @esignSecurityServices.isRoleTenant('ROLE_DOC_TEMPLATE', #request.tenantCode, authentication)")
	PaymentSignTypeListResponse getPaymentSignTypeList(PaymentSignTypeListRequest request, AuditContext audit);
	PaymentSignTypeListResponse getListPaymentType(GetListPaymentTypeRequest  request, AuditContext audit);
}