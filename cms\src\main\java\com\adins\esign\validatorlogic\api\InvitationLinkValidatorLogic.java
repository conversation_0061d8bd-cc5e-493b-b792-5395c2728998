package com.adins.esign.validatorlogic.api;

import com.adins.esign.model.TrInvitationLink;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface InvitationLinkValidatorLogic {
	TrInvitationLink validateGetInvitationLink(String invitationCode, AuditContext audit);
	TrInvitationLink validateGetInvitationLinkNewTran(String invitationCode, AuditContext audit);
	String decryptInvitationCode(String encryptedCode, AuditContext audit);
	boolean validateAutofillInvitationRegisteredUser(TrInvitationLink invitationLink);
	/**
	 * Notes from Axel:<br><br>
	 * <b>WARNING:</b><br>
	 * Will deactivate tr_invitation_link if expired by using <code>Propagion.REQUIRES_NEW</code> transaction.<br>
	 * Be careful if your method will update or have updated the tr_invitation_link using normal transaction.
	 */
	void validateInvitationExpiredDate(TrInvitationLink invLink, AuditContext audit);
}
