package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class PaymentSignTypeException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonPaymentSignType {
		TENANT_NOT_FOUND,
		TENANT_CODE_EMPTY,
		USER_NOT_FOUND,
		INVALID_ROLE,
		BALANCE_NULL
	}
	
	private final ReasonPaymentSignType reason;
	
	public PaymentSignTypeException(ReasonPaymentSignType reason) {
		this.reason = reason;
	}
	
	public PaymentSignTypeException(String message, ReasonPaymentSignType reason) {
		super(message);
		this.reason = reason;
	}
	
	public PaymentSignTypeException(Throwable ex, ReasonPaymentSignType reason) {
		super(ex);
		this.reason = reason;
	}
	
	public PaymentSignTypeException(String message, Throwable ex, ReasonPaymentSignType reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	public ReasonPaymentSignType getReason() {
		return reason;
	}
	
	@Override
	public int getErrorCode() {
		if (null != this.reason) {
			switch (reason) {
			case TENANT_CODE_EMPTY:
				return StatusCode.PAYMENT_SIGN_TYPE_EMPTY_TENANT_CODE;
			case TENANT_NOT_FOUND:
				return StatusCode.PAYMENT_SIGN_TYPE_TENANT_NOT_FOUND;
			case USER_NOT_FOUND:
				return StatusCode.PAYMENT_SIGN_TYPE_USER_NOT_FOUND;
			case INVALID_ROLE:
				return StatusCode.PAYMENT_SIGN_TYPE_INVALID_ROLE;
			case BALANCE_NULL:
				return StatusCode.PAYMENT_SIGN_TYPE_BALANCE_NULL;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}

}
