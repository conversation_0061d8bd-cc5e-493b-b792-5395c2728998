package com.adins.esign.validatorlogic.api;

import java.util.List;

import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface BalanceValidatorLogic {
	void validateBalanceAvailability(String balanceTypeCode, MsTenant tenant, MsVendor vendor, AuditContext audit);
	void validateBalanceAvailabilityWithAmount(String balanceTypeCode, MsTenant tenant, MsVendor vendor, int amount, AuditContext audit);
	void validateBalanceMsUserOfTenant(String balanceTypeCode, MsTenant tenant, MsVendor vendor, AuditContext audit);
	void validateWhatsAppNotifBalanceAvailability(MsTenant tenant, String phone, AuditContext audit);
	void validateWhatsAppNotifBalanceAvailabilityWithAmount(MsTenant tenant, List<String> phones, AuditContext audit);
}
