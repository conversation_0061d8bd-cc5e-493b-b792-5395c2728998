package com.adins.esign.validatorlogic.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsBalancevendoroftenant;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.webservices.model.BalanceRequest;
import com.adins.exceptions.SaldoException;
import com.adins.exceptions.SaldoException.ReasonSaldo;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericBalanceValidatorLogic extends BaseLogic implements BalanceValidatorLogic {

	@Autowired private SaldoLogic saldoLogic;

	@Override
	public void validateBalanceAvailability(String balanceTypeCode, MsTenant tenant, MsVendor vendor, AuditContext audit) {

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceTypeCode);

		MsBalancevendoroftenant balancevendoroftenant = daoFactory.getBalanceVendoroftenantDao().getBalanceVendorOfTenantByBalanceTypeAndTenant(balanceTypeCode, tenant.getTenantCode(), vendor.getVendorCode());
		if (null == balancevendoroftenant) {
			throw new SaldoException(
					getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_CONFIGURED,
							new String[] { balanceType.getDescription(), tenant.getTenantName() }, audit), ReasonSaldo.BALANCE_NOT_CONFIGURED);
		}

		BalanceRequest request = new BalanceRequest();
		request.setBalanceType(balanceType.getCode());
		request.setTenantCode(tenant.getTenantCode());
		request.setVendorCode(vendor.getVendorCode());

		int currentBalance = 0;
		try {
			currentBalance = saldoLogic.getBalanceNotSecure(request, audit).getListBalance().get(0).getCurrentBalance().intValue();
		} catch (Exception e) {
			throw new SaldoException(
					getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_CONFIGURED,
							new String[] { balanceType.getDescription(), tenant.getTenantName() }, audit),
					e, ReasonSaldo.BALANCE_NOT_CONFIGURED);
		}

		if (currentBalance <= 0) {
			throw new SaldoException(getMessage("businesslogic.saldo.balancenotenough",
					new String[] { balanceType.getDescription() }, audit), ReasonSaldo.BALANCE_NOT_ENOUGH);
		}
	}

	@Override
	public void validateBalanceAvailabilityWithAmount(String balanceTypeCode, MsTenant tenant, MsVendor vendor, int amount, AuditContext audit) {
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceTypeCode);

		BalanceRequest request = new BalanceRequest();
		request.setBalanceType(balanceType.getCode());
		request.setTenantCode(tenant.getTenantCode());
		request.setVendorCode(vendor.getVendorCode());

		int currentBalance = 0;
		try {
			currentBalance = saldoLogic.getBalanceNotSecure(request, audit).getListBalance().get(0).getCurrentBalance().intValue();
		} catch (Exception e) {
			throw new SaldoException(
					getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_CONFIGURED,
							new String[] { balanceType.getDescription(), tenant.getTenantName() }, audit),
					e, ReasonSaldo.BALANCE_NOT_CONFIGURED);
		}

		if (currentBalance < amount) {
			throw new SaldoException(getMessage("businesslogic.saldo.balancenotenough",
					new String[] { balanceType.getDescription() }, audit), ReasonSaldo.BALANCE_NOT_ENOUGH);
		}

	}

	@Override
	public void validateBalanceMsUserOfTenant(String balanceTypeCode, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		MsBalancevendoroftenant balanceVendorOfTenant = daoFactory.getBalanceVendoroftenantDao()
				.getBalanceVendorOfTenantByBalanceTypeAndTenant(balanceTypeCode, tenant.getTenantCode(),
						vendor.getVendorCode());

		if (null == balanceVendorOfTenant) {
			throw new SaldoException(getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_CONFIGURED,
					new String[] { balanceTypeCode, tenant.getTenantName() }, audit), ReasonSaldo.BALANCE_NOT_CONFIGURED);
		}
	}

	@Override
	public void validateWhatsAppNotifBalanceAvailability(MsTenant tenant, String phone, AuditContext audit) {
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA, tenant, vendor, audit);
	}

	@Override
	public void validateWhatsAppNotifBalanceAvailabilityWithAmount(MsTenant tenant, List<String> phones, AuditContext audit) {
		int toCut = 0;

		toCut += phones.size();

		if (toCut == 0) {
			return;
		}

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		validateBalanceAvailabilityWithAmount(GlobalVal.CODE_LOV_BALANCE_TYPE_WA, tenant, vendor, toCut, audit);
	}

}
