<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd        
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">	
	
	<bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
    	<property name="locations" value="classpath:application.properties"/>
	</bean>	
	
	<bean id="primaryMailSender" name="primaryMailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
		<property name="protocol" value="smtp" />
		<property name="host" value="${spring.mail.host}" />
		<property name="port" value="${spring.mail.port}" />
		<property name="username" value="${spring.mail.username}" />
		<property name="password" value="${spring.mail.password}" />
		<property name="javaMailProperties">
			<props>
				<prop key="mail.smtp.auth">true</prop>
				<prop key="mail.smtp.starttls.enable">true</prop>
				<prop key="mail.smtp.connectiontimeout">5000</prop>
				<prop key="mail.smtp.timeout">5000</prop>
				<prop key="mail.smtp.writetimeout">5000</prop>
			</props>
		</property>
	</bean>
	
	<bean id="backupMailSender" name="backupMailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
		<property name="protocol" value="smtp" />
		<property name="host" value="${spring.mail.backup.host}" />
		<property name="port" value="${spring.mail.backup.port}" />
		<property name="username" value="${spring.mail.backup.username}" />
		<property name="password" value="${spring.mail.backup.password}" />
		<property name="javaMailProperties">
			<props>
				<prop key="mail.smtp.ssl.enable">true</prop>
				<prop key="mail.smtp.auth">true</prop>
				<prop key="mail.smtp.starttls.enable">true</prop>
				<prop key="mail.smtp.connectiontimeout">5000</prop>
				<prop key="mail.smtp.timeout">5000</prop>
				<prop key="mail.smtp.writetimeout">5000</prop>
			</props>
		</property>
	</bean>
	
	<bean class="com.adins.esign.businesslogic.impl.SpringEmailSenderLogic" />    	
	
</beans>