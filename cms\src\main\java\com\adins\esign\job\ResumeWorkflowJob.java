package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class ResumeWorkflowJob extends BaseLogic {
	private static final Logger LOG = LoggerFactory.getLogger(ResumeWorkflowJob.class);
	
	@Autowired SchedulerLogic schedulerLogic;

	public void runResumeWorkflow() {
		try {
			LOG.info("Job Resume Workflow Started");
			AuditContext auditContext = new AuditContext(GlobalVal.SCHEDULER_WORKFLOW);
			schedulerLogic.resumeWorkflow(auditContext);
			LOG.info("Job Resume Workflow Finished");
		} catch (Exception e) {
			LOG.error("Error on running Job Resume Workflow", e);
		}
	}
}
