package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.dataaccess.api.SignLinkRequestDao;
import com.adins.esign.model.TrSignLinkRequest;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class SignLinkRequestDaoHbn extends BaseDaoHbn implements SignLinkRequestDao {

	@Override
	public void insertSignLinkRequest(TrSignLinkRequest signLinkRequest) {
		signLinkRequest.setUsrCrt(MssTool.maskData(signLinkRequest.getUsrCrt()));
		managerDAO.insert(signLinkRequest);
	}

	@Override
	public void updateSignLinkRequest(TrSignLinkRequest signLinkRequest) {
		signLinkRequest.setUsrUpd(MssTool.maskData(signLinkRequest.getUsrUpd()));
		managerDAO.update(signLinkRequest);
	}

	@Override
	public void deleteSignLinkRequest(TrSignLinkRequest signLinkRequest) {
		managerDAO.delete(signLinkRequest);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrSignLinkRequest> getSignLinkRequestsBySignLinkCode(String code) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrSignLinkRequest.SIGN_LINK_CODE_HBM, code);
		
		return (List<TrSignLinkRequest>) managerDAO.list(
				"from TrSignLinkRequest slr "
				+ "join fetch slr.trDocumentD dd "
				+ "join fetch dd.msTenant mt "
				+ "join fetch dd.msVendor mv "
				+ "join fetch slr.amMsuser mu "
				+ "where slr.signLinkCode = :signLinkCode ", params).get(GlobalKey.MAP_RESULT_LIST);
	}

}
