package com.adins.esign.webservices.embed.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.dom4j.DocumentException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.NoSuchMessageException;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.FeedbackLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.webservices.embed.api.FeedbackEmbedService;
import com.adins.esign.webservices.model.FeedbackResponse;
import com.adins.esign.webservices.model.embed.FeedbackHybridEmbedRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

import io.swagger.annotations.Api;

@Component
@Path("/embed/feedback")
@Api(value = "FeedbackEmbedService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericFeedbackEmbedServiceEndpoint implements FeedbackEmbedService{
	
	@Autowired FeedbackLogic feedbackLogic;

	@Override
	@POST
	@Path("/save")
	public FeedbackResponse saveFeedback(FeedbackHybridEmbedRequest request) throws NoSuchMessageException, DocumentException{
		FeedbackResponse response = new FeedbackResponse();
		
		AuditContext audit = request.getAudit().toAuditContext();
		feedbackLogic.insertFeedbackHybridEmbed(request, audit);
		
		Status status = new Status();
		status.setCode(0);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}
	
}
