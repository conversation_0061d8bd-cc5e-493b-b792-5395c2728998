package com.adins.esign.webservices.frontend.api;

import com.adins.esign.webservices.model.DownloadAuditTrailExcelRequest;
import com.adins.esign.webservices.model.DownloadAuditTrailExcelResponse;
import com.adins.esign.webservices.model.InquiryAuditTrailSignProcessRequest;
import com.adins.esign.webservices.model.InquiryAuditTrailSignProcessResponse;
import com.adins.esign.webservices.model.SigningProcessAuditTrailRelatedDocumentRequest;
import com.adins.esign.webservices.model.SigningProcessAuditTrailRelatedDocumentResponse;

public interface SigningProcessAuditTrailService {
	InquiryAuditTrailSignProcessResponse getInquriyAuditTrailSignProcess (InquiryAuditTrailSignProcessRequest request );
	SigningProcessAuditTrailRelatedDocumentResponse getAuditTrailSignProcessDetail (SigningProcessAuditTrailRelatedDocumentRequest request );
	DownloadAuditTrailExcelResponse downloadAuditTrailSignProcess (DownloadAuditTrailExcelRequest request );

}
