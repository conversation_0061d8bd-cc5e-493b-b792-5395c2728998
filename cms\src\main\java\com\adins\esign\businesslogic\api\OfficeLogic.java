package com.adins.esign.businesslogic.api;

import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.webservices.model.OfficeListEmbedRequest;
import com.adins.esign.webservices.model.OfficeListRequest;
import com.adins.esign.webservices.model.OfficeListResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface OfficeLogic {
	OfficeListResponse getOfficeList(OfficeListRequest request, AuditContext audit);
	OfficeListResponse getOfficeListEmbed(OfficeListEmbedRequest request, AuditContext audit);
	MsOffice insertOffice(String officeName, String officeCode, MsTenant tenant, AuditContext audit);
	MsOffice insertUnregisteredOffice(String officeName, String officeCode, MsRegion region, MsTenant tenant, AuditContext audit);
	MsOffice getOfficeByCodeAndTenant(String officeCode, boolean checkOfficeExistence, MsTenant tenant, AuditContext audit);
}
