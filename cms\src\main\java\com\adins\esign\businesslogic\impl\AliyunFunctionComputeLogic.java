package com.adins.esign.businesslogic.impl;

import java.net.HttpURLConnection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.FunctionComputeLogic;
import com.aliyuncs.fc.client.FunctionComputeClient;
import com.aliyuncs.fc.constants.Const;
import com.aliyuncs.fc.request.InvokeFunctionRequest;
import com.aliyuncs.fc.response.InvokeFunctionResponse;

@Component
public class AliyunFunctionComputeLogic implements FunctionComputeLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(AliyunFunctionComputeLogic.class);
	
	@Autowired private FunctionComputeClient functionComputeClient;
	
	@Value("${spring.cloud.alicloud.fc.service}") private String fcServiceName;
	
	/*
	 *  If there is new function to be called, add the function name to this enumerate
	 *  
	 *  Function naming convention:
	 *  1. Use a lowercase single letter, word, or words.
	 *  2. Separate words with underscores.
	 *  
	 *  Examples: function, my_function, my_second_function
	 */
	public enum FunctionName {
		TEST_ASYNC("test_async_invocation"),
		RECONCILE_OTP_DIGISIGN("recon_otp_digisign"),
		SIGN_VIDA_DOCUMENT("sign_vida_document"),
		SIGN_PRIVY_DOCUMENT("sign_privy_document"),
		UPDATE_PSRE_ID("update_psre_id"),
		REGISTRATION_LIVENESS("registration_liveness"),
		CHECK_REGISTER_STATUS("check_register_status"),
		EXECUTE_CLIENT_CALLBACK("execute_client_callback"),
		STAMP_PRIVY_JOB("stamp_privy_job"),
		STAMP_VIDA_JOB("stamp_vida_job"),
		PROCESS_AUTOSIGN_DATA_SCHEDULER("process_autosign_data_scheduler"),
		PROCESS_AUTOSIGN_DATA("process_autosign_data"),
		STAMP_PAJAKKU_JOB("stamping_pajakku_job");
		
		private final String name;
		
		private FunctionName(String name) {
			this.name = name;
		}
		
		@Override
		public String toString() {
			return this.name;
		}
	}
	
	private void invokeAsyncFuctionCompute(FunctionName functionName, String payload) {
		if (null == functionName) {
			LOG.warn("Asynchronous invocation not accepted (Function name is null)");
			return;
		}
		
		// Prepare request
		InvokeFunctionRequest invReq = new InvokeFunctionRequest(fcServiceName, functionName.toString());
		invReq.setInvocationType(Const.INVOCATION_TYPE_ASYNC);
		invReq.setPayload(payload.getBytes());
		
		// Invoke function
		InvokeFunctionResponse invRes = functionComputeClient.invokeFunction(invReq);
		if (HttpURLConnection.HTTP_ACCEPTED == invRes.getStatus()) {
			LOG.info("Asynchronous invocation accepted, request ID: {}", invRes.getRequestId());
		} else {
			LOG.warn("Asynchronous invocation failed");
		}
	}

	@Override
	public void invokeAsynchronousTest(String payload) {
		invokeAsyncFuctionCompute(FunctionName.TEST_ASYNC, payload);
	}

	@Override
	public void invokeReconcileOtpDigisign(long idJobResult) {
		String payload = String.valueOf(idJobResult);
		invokeAsyncFuctionCompute(FunctionName.RECONCILE_OTP_DIGISIGN, payload);
	}

	@Override
	public void invokeSignVidaDocument(long idSignRequest) {
		String payload = String.valueOf(idSignRequest);
		invokeAsyncFuctionCompute(FunctionName.SIGN_VIDA_DOCUMENT, payload);
	}

	@Override
	public void invokeSignPrivyDocument(long idSignRequest) {
		String payload = String.valueOf(idSignRequest);
		invokeAsyncFuctionCompute(FunctionName.SIGN_PRIVY_DOCUMENT, payload);
	}

	@Override
	public void invokeUpdatePsreId(long idJobUpdatePsreId) {
		String payload = String.valueOf(idJobUpdatePsreId);
		invokeAsyncFuctionCompute(FunctionName.UPDATE_PSRE_ID, payload);
		
	}

	@Override
	public void invokeRegistrationLiveness(String trxNo) {
		invokeAsyncFuctionCompute(FunctionName.REGISTRATION_LIVENESS, trxNo);
	}

	@Override
	public void invokeCheckRegisterStatus(long idJobCheckRegisterStatus) {
		String payload = String.valueOf(idJobCheckRegisterStatus);
		invokeAsyncFuctionCompute(FunctionName.CHECK_REGISTER_STATUS, payload);
		
	}

	@Override
	public void invokeClientCallback(long idClientCallbackRequest) {
		String payload = String.valueOf(idClientCallbackRequest);
		invokeAsyncFuctionCompute(FunctionName.EXECUTE_CLIENT_CALLBACK, payload);
	}

	@Override
	public void invokeAttachMeteraiPrivy(long idDocumentH) {
		String payload = String.valueOf(idDocumentH);
		invokeAsyncFuctionCompute(FunctionName.STAMP_PRIVY_JOB, payload);
	}
	
	@Override
	public void invokeProcessAutosignData(long idProcessAutosignBmH) {
		String payload = String.valueOf(idProcessAutosignBmH);
		invokeAsyncFuctionCompute(FunctionName.PROCESS_AUTOSIGN_DATA, payload);	
	}

	@Override
	public void invokeAttachMeteraiVida(long idDocumentH) {
		String payload = String.valueOf(idDocumentH);
		invokeAsyncFuctionCompute(FunctionName.STAMP_VIDA_JOB, payload);
	}

	@Override
	public void invokeAttachMeteraiPajakku(long idDocumentH) {
		String payload = String.valueOf(idDocumentH);
		invokeAsyncFuctionCompute(FunctionName.STAMP_PAJAKKU_JOB, payload);
	}
}
