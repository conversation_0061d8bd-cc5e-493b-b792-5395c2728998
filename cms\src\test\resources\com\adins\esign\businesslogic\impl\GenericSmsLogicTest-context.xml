<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd        
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">	

	<bean id="hikariConfig" class="com.zaxxer.hikari.HikariConfig">
	    <property name="driverClassName" value="${spring.datasource.driver-class-name}" />	    
		<property name="jdbcUrl" value="${spring.datasource.url}" />
		<property name="username" value="${spring.datasource.username}" />
		<property name="password" value="${spring.datasource.password}" />
	    <property name="minimumIdle" value="${spring.datasource.hikari.minimum-idle}" />
	    <property name="maximumPoolSize" value="${spring.datasource.hikari.maximum-pool-size}" />
	    <property name="connectionTestQuery" value="${spring.datasource.hikari.connection-test-query}" />
	</bean>
	
	<bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
	    <constructor-arg ref="hikariConfig" />
	</bean>

	<bean id="hibernateSessionFactoryBean" class="org.springframework.orm.hibernate5.LocalSessionFactoryBean">
		<property name="dataSource" ref="dataSource"/>
        <property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">org.hibernate.dialect.PostgreSQLDialect</prop>
                <prop key="hibernate.show_sql">false</prop>
                <prop key="hibernate.format_sql">false</prop>
                <prop key="hibernate.jdbc.use_streams_for_binary">true</prop>
            </props>
        </property>
		<property name="packagesToScan">
			<list>
				<value>com.adins.am.model</value>
				<value>com.adins.esign.model</value>
			</list>
		</property>
    </bean>
        	
	<bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
		<property name="basenames">
		  <list>
			<value>classpath:com/adins/esign/messages</value>
		  </list>
		</property>
    	<property name="cacheSeconds" value="-1"/>
	</bean>
    
	<bean id="transactionManager" class="org.springframework.orm.hibernate5.HibernateTransactionManager">
	    <property name="sessionFactory" ref="hibernateSessionFactoryBean" />
	</bean>
	
    <bean id="globalManagerDAO" class="com.adins.framework.persistence.dao.hibernate.manager.global.GlobalManagerDao">
		<property name="sessionFactory" ref="hibernateSessionFactoryBean"/>
    </bean>
    
    <context:component-scan base-package="com.adins.esign.dataaccess" />
    
    <bean id="auditlogLogic" class="com.adins.am.businesslogic.impl.AuditlogLogic" />
    
    <bean id="auditTrail" class="com.adins.framework.persistence.dao.hibernate.interceptor.audit.GenericAuditLog" scope="singleton">
    	<property name="sessionFactory" ref="hibernateSessionFactoryBean"/>
    	<property name="auditManager" ref="auditlogLogic"/>
    </bean>	
	
	<bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
    	<property name="locations" value="classpath:application.properties"/>
	</bean>    
	
	<bean class="com.adins.esign.businesslogic.impl.GenericSmsLogic" />
</beans>