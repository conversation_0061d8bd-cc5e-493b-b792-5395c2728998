package com.adins.esign.webservices.frontend.api;

import com.adins.esign.webservices.model.GetDataUserManagementViewRequest;
import com.adins.esign.webservices.model.GetDataUserManagementViewResponse;
import com.adins.esign.webservices.model.GetListDataPenggunaRequest;
import com.adins.esign.webservices.model.GetListDataPenggunaResponse;
import com.adins.esign.webservices.model.GetListUserManagementRequest;
import com.adins.esign.webservices.model.GetListUserManagementResponse;
import com.adins.esign.webservices.model.InsertUserManagementResponse;
import com.adins.esign.webservices.model.InsestUserManagementRequest;
import com.adins.esign.webservices.model.UpdateUserManagementRequest;
import com.adins.esign.webservices.model.UpdateUserManagementResponse;

public interface UserManagementService {
	GetListUserManagementResponse getListUserManagement(GetListUserManagementRequest request);
	InsertUserManagementResponse insertUserManagement(InsestUserManagementRequest request);
	
	GetListDataPenggunaResponse getListDataPengguna(GetListDataPenggunaRequest request);
	
	GetDataUserManagementViewResponse getDataUserManagementView(GetDataUserManagementViewRequest request);
	UpdateUserManagementResponse updateUserManagement(UpdateUserManagementRequest request);
	}
