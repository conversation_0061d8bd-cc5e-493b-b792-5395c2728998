package com.adins.esign.dataaccess.impl;

import java.util.List;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.dataaccess.api.MenuDao;
import com.adins.esign.model.custom.MenuUserBean;
import com.google.gson.Gson;

@Disabled
@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class MenuDaoHbnTest {
	@Autowired MenuDao menuDao;
	
	@Test
	void getMenuByRoleAndTenantTest() {
		List<MenuUserBean> resultList = this.menuDao.getMenuByRoleAndTenant("ADMCLIENT", "WOM");
		Gson gson = new Gson();
		System.out.println(gson.toJson(resultList));;
	}
}
