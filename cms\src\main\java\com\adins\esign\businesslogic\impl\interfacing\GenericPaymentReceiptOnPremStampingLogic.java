package com.adins.esign.businesslogic.impl.interfacing;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonStampingLogic;
import com.adins.esign.businesslogic.api.FileAccessLogic;
import com.adins.esign.businesslogic.api.interfacing.PaymentReceiptOnPremStampingLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.MediaType;
import com.adins.esign.constants.enums.StampingDocumentType;
import com.adins.esign.constants.enums.StampingErrorDetail;
import com.adins.esign.constants.enums.StampingErrorLocation;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsPeruriDocType;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDStampduty;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.AttachEmeteraiErrorDetail;
import com.adins.esign.model.custom.EmeteraiPajakkuLoginRequestBean;
import com.adins.esign.model.custom.EmeteraiPajakkuLoginResponseBean;
import com.adins.esign.model.custom.IncrementAgreementStampingErrorCountBean;
import com.adins.esign.model.custom.PaymentReceiptConfinsSyncBean;
import com.adins.esign.model.custom.PaymentReceiptConfinsSyncRequest;
import com.adins.esign.model.custom.PaymentReceiptConfinsSyncResponse;
import com.adins.esign.model.custom.SignLocationBean;
import com.adins.esign.model.custom.UploadDocPajakkuResponseBean;
import com.adins.esign.model.custom.UploadPaymentReceiptToDmsResponse;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.PaymentReceiptValidatorLogic;
import com.adins.esign.webservices.model.GenerateEmeteraiPajakkuRequest;
import com.adins.esign.webservices.model.GenerateEmeteraiPajakkuResponse;
import com.adins.esign.webservices.model.StampingEmeteraiPajakkuResponse;
import com.adins.esign.webservices.model.StampingOnPremEmeteraiPajakkuRequest;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptRequest;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptResponse;
import com.adins.esign.webservices.model.confins.UploadStampedDocumentResponse;
import com.adins.esign.webservices.model.confins.UploadStampedPaymenReceiptRequest;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.EmeteraiException;
import com.adins.exceptions.EmeteraiOnPremException;
import com.adins.exceptions.StampingException;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.EmeteraiException.ReasonEmeterai;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.google.gson.Gson;

import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

@Component
public class GenericPaymentReceiptOnPremStampingLogic extends BaseLogic implements PaymentReceiptOnPremStampingLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericPaymentReceiptOnPremStampingLogic.class);
	
	public static final String DOC_TRX_ID_DELIMITER = ";";
	
	private static final String BEARER	= "Bearer ";
	
	// Peruri URL
	@Value("${e-meterai.pajakku.login}") private String urlLogin;
	@Value("${emeterai.pajakku.generate.uri}") private String urlGenerate;
	@Value("${emeterai.onprem.stamp.url}") private String urlStamp;
	
	// Peruri API parameter
	@Value("${emeterai.onprem.paymentreceipt.processnumber}") private String processNumber;
	@Value("${emeterai.pajakku.folder}") private String folder;
	@Value("${emeterai.pajakku.certificatelevel}") private String certifLevel;
	@Value("${emeterai.pajakku.profilename}") private String profileName;
	
	@Autowired private Gson gson;
	
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private CommonStampingLogic commonStampingLogic;
	@Autowired private FileAccessLogic fileAccessLogic;
	@Autowired private PaymentReceiptValidatorLogic paymentReceiptValidatorLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	private EmeteraiPajakkuLoginResponseBean loginPeruri(TrDocumentH documentH, long connectTimeout, long readTimeout, boolean throwMaxError, AuditContext audit) {
		String jsonRequest = null;
		String jsonResponse = null;
		
		try {
			String username = commonStampingLogic.getAccountUsername(documentH, audit);
			String password = commonStampingLogic.getAccountPassword(documentH, audit);
			
			if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
				LOG.error("Kontrak {}, Peruri login credential is empty", documentH.getRefNumber());
				
				String message = getMessage("businesslogic.emeterai.emptylogincredential", null, audit);
				
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, null, message, null, null, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.LOGIN, StampingErrorDetail.VALIDATION, throwMaxError, audit);
				
				EmeteraiPajakkuLoginResponseBean response = new EmeteraiPajakkuLoginResponseBean();
				response.setStatusCode(GlobalVal.PERURI_ERROR_CODE);
				response.setMessage(message);
				return response;
			}
			
			// Prepare header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(MediaType.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			WebClient client = WebClient.create(urlLogin).headers(mapHeader);
			MssTool.setWebClientConnReadTimeout(client, connectTimeout, readTimeout);
			
			// Prepare request body
			EmeteraiPajakkuLoginRequestBean request = new EmeteraiPajakkuLoginRequestBean();
			request.setUser(username);
			request.setPassword(password);
			jsonRequest = gson.toJson(request);
			
			EmeteraiPajakkuLoginRequestBean dummyRequestBean = EmeteraiPajakkuLoginRequestBean.createMaskedInstance(request);
			String logJsonRequest = gson.toJson(dummyRequestBean);
			LOG.info("Kontrak {}, Login Peruri request: {}", documentH.getRefNumber(), logJsonRequest);
			
			// Get response
			Response clientResponse = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) clientResponse.getEntity());
			jsonResponse = IOUtils.toString(isReader);
			LOG.info("Kontrak {}, Login Peruri response: {}", documentH.getRefNumber(), jsonResponse);
			
			EmeteraiPajakkuLoginResponseBean response = gson.fromJson(jsonResponse, EmeteraiPajakkuLoginResponseBean.class);
			response.setJsonRequest(jsonRequest);
			response.setJsonResponse(jsonResponse);
			
			if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {			
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, null, jsonResponse, null, null, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.LOGIN, StampingErrorDetail.FAIL_RESPONSE, throwMaxError, audit);
			}
			
			return response;
			
		} catch (StampingException e) {
			throw e;
		} catch (Exception e) {
			
			LOG.error("Kontrak {}, Login Peruri exception: {}", documentH.getRefNumber(), e.getLocalizedMessage(), e);
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, null, e.getLocalizedMessage(), e, null, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.LOGIN, StampingErrorDetail.EXCEPTION, throwMaxError, audit);
			
			EmeteraiPajakkuLoginResponseBean response = new EmeteraiPajakkuLoginResponseBean();
			response.setStatusCode(GlobalVal.PERURI_ERROR_CODE);
			response.setErrorMsg(e.getLocalizedMessage());
			response.setErrorMessage(e.getLocalizedMessage());
			response.setException(e);
			response.setJsonRequest(jsonRequest);
			response.setJsonResponse(jsonResponse);
			return response;
		}
	}
	
	private UploadDocPajakkuResponseBean uploadDocumentForStamping(TrDocumentD document, TrDocumentDStampduty docSdt, boolean throwMaxError, AuditContext audit) {
		try {
			// Set stamping start time
			if (null == docSdt.getStartStampProcess()) {
				docSdt.setStartStampProcess(new Date());
				daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(docSdt);
			}
			
			// Delete hasil stamping, supaya setelah STM_SDT bisa dicek apakah hasil stamping sudah masuk
			fileAccessLogic.deleteStampedDocument(document);
			
			String base64Document = commonStampingLogic.getPaymentReceiptToUpload(document, audit);
			byte[] fileContent = Base64.getDecoder().decode(base64Document);
			fileAccessLogic.storeBaseStampDocument(fileContent, document);
			commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_GEN_SDT, audit);
			
			UploadDocPajakkuResponseBean response = new UploadDocPajakkuResponseBean();
			response.setStatusCode(GlobalVal.PERURI_SUCCESS_CODE);
			return response;
			
		} catch (StampingException e) {
			throw e;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Upload document for stamping exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e);
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, null, null);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.UPL_DOC, StampingErrorDetail.EXCEPTION, throwMaxError, audit);
			
			UploadDocPajakkuResponseBean response = new UploadDocPajakkuResponseBean();
			response.setStatusCode(GlobalVal.PERURI_ERROR_CODE);
			response.setErrorMessage(response.getMessage());
			return response;
		}
	}
	
	private GenerateEmeteraiPajakkuResponse generateEmeterai(TrDocumentD document, TrDocumentDStampduty docSdt, String token, long connectTimeout, long readTimeout, boolean throwMaxError, AuditContext audit) {
		String jsonRequest = null;
		String jsonResponse = null;
		
		try {
			
			int sdtNeeded = document.getTotalMaterai() - document.getTotalStamping();
			int availableSdt = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
			
			LOG.info("Kontrak {}, Document {}, Need SDT: {}, Available SDT: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), sdtNeeded, availableSdt);
			if (availableSdt >= sdtNeeded) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_STM_SDT, audit);
				GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
				response.setMessage("Stamp duty already available");
				response.setStatusCode(GlobalVal.PERURI_SUCCESS_CODE);
				return response;
			}
			
			boolean enoughBalance = commonStampingLogic.enoughSdtBalance(document, sdtNeeded, audit);
			if (!enoughBalance) {
				String message = "Not enough balance";
				LOG.error("Kontrak {}, Dokumen {}, Generate meterai validation: Not enough balance", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, message, null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.GEN_SDT, StampingErrorDetail.VALIDATION, throwMaxError, audit);
				
				GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
				response.setMessage(message);
				response.setStatusCode(GlobalVal.PERURI_ERROR_CODE);
				response.setErrorMsg(message);
				return response;
			}
			
			String nilaiMeteraiLunas = commonStampingLogic.getStampDutyFee(audit);
			GenerateEmeteraiPajakkuRequest request = new GenerateEmeteraiPajakkuRequest();
			request.setUpload(false);
			request.setNamadoc(commonStampingLogic.getNamaDocForGenerate(document, audit));
			request.setNamafile(document.getDocumentId() + ".pdf");
			request.setNilaidoc(nilaiMeteraiLunas);
			request.setSnOnly(false);
			request.setNodoc(commonStampingLogic.getNoDocForGenerate(document, audit));
			request.setTgldoc(commonStampingLogic.getTglDocForGenerate(document, audit));
			
			if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && "1".equals(document.getTrDocumentH().getIsPostpaidStampduty()) && StringUtils.isNotBlank(document.getDocumentName())) {
				request.setNamejidentitas(document.getMsLovIdType().getCode());
				request.setNoidentitas(document.getIdNo());
				request.setNamedipungut(document.getIdName());
			}
			
			jsonRequest = gson.toJson(request);
			LOG.info("Kontrak {}, Dokumen {}, Generate meterai request: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonRequest);
			
			// Prepare API Header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(MediaType.AUTHORIZATION, BEARER + token);
			mapHeader.add(MediaType.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			WebClient client = WebClient.create(urlGenerate).headers(mapHeader);
			MssTool.setWebClientConnReadTimeout(client, connectTimeout, readTimeout);
			
			// Post API
			Response response = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			jsonResponse = IOUtils.toString(isReader);
			
			LOG.info("Kontrak {}, Dokumen {}, Generate meterai response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonResponse);
			GenerateEmeteraiPajakkuResponse generateResponse = gson.fromJson(jsonResponse, GenerateEmeteraiPajakkuResponse.class);
			if (!GlobalVal.PERURI_SUCCESS_CODE.equals(generateResponse.getStatusCode())) {
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, jsonResponse, null, jsonRequest, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.GEN_SDT, StampingErrorDetail.FAIL_RESPONSE, throwMaxError, audit);
				return generateResponse;
			}
			
			// Save QR to local file
			byte[] qrContent = Base64.getDecoder().decode(generateResponse.getResult().getImage());
			fileAccessLogic.storeStampQr(qrContent, generateResponse.getResult().getSn());
			
			MsTenant tenant = document.getMsTenant();
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
			
			String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_AVAILABLE);
			MsLov balanceType = null;
			MsLov trxType = null;
			if ("1".equals(document.getTrDocumentH().getIsPostpaidStampduty())) {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT_POSTPAID);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT_POSTPAID);
			} else {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT);
			}
			
			TrStampDuty sdt = new TrStampDuty();
			sdt.setTrxNo(trxNo);
			sdt.setStampDutyNo(generateResponse.getResult().getSn());
			sdt.setStampQr(generateResponse.getResult().getSn() + ".png");
			sdt.setMsLov(sdtStatus);
			sdt.setUsrCrt(audit.getCallerId());
			sdt.setDtmCrt(new Date());
			sdt.setMsTenant(tenant);
			sdt.setMsVendor(vendor);
			sdt.setStampDutyFee(Integer.valueOf(nilaiMeteraiLunas));
			daoFactory.getStampDutyDao().insertTrStampDutyNewTran(sdt);
			LOG.info("Kontrak {}, stamp duty with SN {} inserted.", document.getTrDocumentH().getRefNumber(), sdt.getStampDutyNo());
			
			docSdt.setTrStampDuty(sdt);
			docSdt.setUsrUpd(audit.getCallerId());
			docSdt.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(docSdt);
			
			String notes = commonStampingLogic.getBalanceMutationNotesForGenerate(docSdt, audit);
			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(trxNo);
			mutation.setTrxDate(new Date());
			mutation.setRefNo(document.getTrDocumentH().getRefNumber());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setTrDocumentD(document);
			mutation.setTrDocumentH(document.getTrDocumentH());
			mutation.setNotes(notes);
			mutation.setTrStampDuty(sdt);
			mutation.setUsrCrt(audit.getCallerId());
			mutation.setDtmCrt(new Date());
			mutation.setMsOffice(document.getTrDocumentH().getMsOffice());
			mutation.setMsBusinessLine(document.getTrDocumentH().getMsBusinessLine());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
			LOG.info("Kontrak {}, balance mutation with stamp duty SN {} inserted.", document.getTrDocumentH().getRefNumber(), sdt.getStampDutyNo());
			
			commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_STM_SDT, audit);
			return generateResponse;
			
		} catch (StampingException e) {
			throw e;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Generate meterai exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage());
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e , jsonRequest, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.GEN_SDT, StampingErrorDetail.EXCEPTION, throwMaxError, audit);
			
			GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
			response.setMessage(e.getLocalizedMessage());
			response.setStatusCode(GlobalVal.PERURI_ERROR_CODE);
			response.setErrorMsg(e.getLocalizedMessage());
			return response;
		}
	}
	
	private StampingEmeteraiPajakkuResponse stampEmeterai(TrDocumentD document, TrDocumentDStampduty docSdt, String token, long connectTimeout, long readTimeout, boolean throwMaxError, AuditContext audit) {
		String jsonRequest = null;
		String jsonResponse = null;
		
		try {
			
			int sdtAvailable = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
			LOG.info("Kontrak {}, Dokumen {}, available SDT qty: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), sdtAvailable);
			if (0 == sdtAvailable) {
				String message = "No stamp duty for " + document.getDocumentId();
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, message, null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.STM_SDT, StampingErrorDetail.VALIDATION, throwMaxError, audit);
				
				StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
				response.setErrorCode(GlobalVal.PERURI_ERROR_CODE);
				response.setErrorMessage(message);
				response.setErrorMsg(message);
				return response;
			}
			
			TrStampDuty sdt = docSdt.getTrStampDuty();
			if (GlobalVal.CODE_LOV_SDT_GO_LIVE.equals(sdt.getMsLov().getCode())) {
				String message = "SDT with number " + sdt.getStampDutyNo() + " cannot be used.";
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, message, null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.STM_SDT, StampingErrorDetail.VALIDATION, throwMaxError, audit);
				
				StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
				response.setErrorCode(GlobalVal.PERURI_ERROR_CODE);
				response.setErrorMessage(message);
				response.setErrorMsg(message);
				return response;
			}
			
			SignLocationBean coordinate = gson.fromJson(docSdt.getSignLocation(), SignLocationBean.class);
			StampingOnPremEmeteraiPajakkuRequest request = new StampingOnPremEmeteraiPajakkuRequest();
			request.setCertificatelevel(certifLevel);
			request.setDest(commonStampingLogic.getOnPremStampDestination(document, audit));
			request.setDocpass(StringUtils.EMPTY);
			request.setJwToken(token);
			request.setLocation(document.getTrDocumentH().getMsOffice().getOfficeName());
			request.setProfileName(profileName);
			request.setReason(commonStampingLogic.getReasonForStamp(document, audit));
			request.setRefToken(sdt.getStampDutyNo());
			request.setSpesimenPath(commonStampingLogic.getOnPremSpecimenPath(docSdt, audit));
			request.setSrc(commonStampingLogic.getOnPremSource(document, audit));
			request.setRetryFlag("1");
			request.setVisLLX(Double.valueOf(coordinate.getLlx()));
			request.setVisLLY(Double.valueOf(coordinate.getLly()));
			request.setVisURX(Double.valueOf(coordinate.getUrx()));
			request.setVisURY(Double.valueOf(coordinate.getUry()));
			request.setVisSignaturePage(docSdt.getSignPage());
			
			// Prepare API Header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(MediaType.AUTHORIZATION, BEARER + token);
			mapHeader.add(MediaType.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			WebClient client = WebClient.create(urlStamp).headers(mapHeader);
			MssTool.setWebClientConnReadTimeout(client, connectTimeout, readTimeout);
			
			// Prepare API request body
			jsonRequest = gson.toJson(request);
			LOG.info("Kontrak {}, Dokumen {}, Stamping meterai request: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonRequest);
			
			// Get API response
			Response response = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			jsonResponse = IOUtils.toString(isReader);
			LOG.info("Kontrak {}, Dokumen {}, Stamping meterai response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonResponse);
			
			StampingEmeteraiPajakkuResponse stampResponse = gson.fromJson(jsonResponse, StampingEmeteraiPajakkuResponse.class);
			if (GlobalVal.PERURI_SUCCESS_CODE.equals(stampResponse.getErrorCode())) {
				// Update SDT Status to GO LIVE
				MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_GO_LIVE);
				sdt.setMsLov(sdtStatus);
				sdt.setUsrUpd(audit.getCallerId());
				sdt.setDtmUpd(new Date());
				daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
				
				// Update tr_document_d_stampduty, fill id_stamp_duty and stamp date
				docSdt.setTrStampDuty(sdt);
				docSdt.setStampingDate(new Date());
				docSdt.setUsrUpd(audit.getCallerId());
				docSdt.setDtmUpd(new Date());
				daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(docSdt);
				
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_OSS, audit);
				return stampResponse;
			}
			
			StampingEmeteraiPajakkuResponse retryResponse = retryCallStampApi(document, jsonRequest, token, connectTimeout, readTimeout, audit);
			if (GlobalVal.PERURI_SUCCESS_CODE.equals(retryResponse.getErrorCode())) {
				// Update SDT Status to GO LIVE
				MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_GO_LIVE);
				sdt.setMsLov(sdtStatus);
				sdt.setUsrUpd(audit.getCallerId());
				sdt.setDtmUpd(new Date());
				daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
				
				// Update tr_document_d_stampduty, fill id_stamp_duty and stamp date
				docSdt.setTrStampDuty(sdt);
				docSdt.setStampingDate(new Date());
				docSdt.setUsrUpd(audit.getCallerId());
				docSdt.setDtmUpd(new Date());
				daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(docSdt);
				
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_OSS, audit);
				return stampResponse;
			}
			
			// Update status tr_stamp_duty ke FAILED
			MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_STAMP_FAILED);
			sdt.setMsLov(sdtStatus);
			sdt.setErrorMessage(retryResponse.getErrorMessage());
			sdt.setUsrUpd(audit.getCallerId());
			sdt.setDtmUpd(new Date());
			daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
						
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, retryResponse.getErrorMessage(), null, jsonRequest, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.STM_SDT, StampingErrorDetail.FAIL_RESPONSE, throwMaxError, audit);
			return retryResponse;
		} catch (StampingException e) {
			throw e;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Stamping meterai exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e);
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, jsonRequest, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.STM_SDT, StampingErrorDetail.EXCEPTION, throwMaxError, audit);
			
			StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
			response.setErrorCode(GlobalVal.PERURI_ERROR_CODE);
			response.setErrorMessage(e.getLocalizedMessage());
			response.setErrorMsg(e.getLocalizedMessage());
			return response;
		}
	}
	
	private StampingEmeteraiPajakkuResponse retryCallStampApi(TrDocumentD document, String jsonRequest, String token, long connectTimeout, long readTimeout, AuditContext audit) throws IOException {
		int retryAttemps = commonStampingLogic.getStampingRetryAttempts(audit);
		int retryCount = 0;
		
		StampingEmeteraiPajakkuResponse stampResponse = new StampingEmeteraiPajakkuResponse();
		while (retryCount < retryAttemps) {
			// Prepare API Header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(MediaType.AUTHORIZATION, BEARER + token);
			mapHeader.add(MediaType.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			WebClient client = WebClient.create(urlStamp).headers(mapHeader);
			MssTool.setWebClientConnReadTimeout(client, connectTimeout, readTimeout);
						
			// Prepare API request body
			LOG.info("Kontrak {}, Dokumen {}, Stamping meterai request: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonRequest);
			
			// Get API response
			Response response = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String jsonResponse = IOUtils.toString(isReader);
			LOG.info("Kontrak {}, Dokumen {}, Stamping meterai response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonResponse);
			
			stampResponse = gson.fromJson(jsonResponse, StampingEmeteraiPajakkuResponse.class);
			if (GlobalVal.PERURI_SUCCESS_CODE.equals(stampResponse.getErrorCode())) {
				return stampResponse;
			}
			retryCount++;
		}
		
		return stampResponse;
	}
	
	private AttachEmeteraiErrorDetail storeStampedDocumentToOss(TrDocumentD document, boolean throwMaxError, AuditContext audit) {
		try {
			boolean isExists = false;
			int attempCount = commonStampingLogic.getFileCheckAttempts(audit);
			long delayMs = commonStampingLogic.getFileCheckDelay(audit);
		
			LOG.info("Kontrak {}, Dokumen {}, Upload to OSS", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
			while (!isExists) {
				if (attempCount <= 0) {
					LOG.error("Kontrak {}, Dokumen {}, Max check count reached", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
					throw new EmeteraiOnPremException(getMessage("businesslogic.emeterai.onprem.filecheckfailed", null, audit));
				}
				
				Thread.sleep(delayMs);
				LOG.info("Kontrak {}, Dokumen {}, {} attempts left to check stamped document", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), attempCount);
				isExists = fileAccessLogic.isStampedDocumentExists(document);
				attempCount -= 1;
			}
				
			byte[] documentByteArray = fileAccessLogic.getStampedDocument(document);
			if (null == documentByteArray) {
				throw new EmeteraiOnPremException(getMessage("businesslogic.emeterai.onprem.filecheckfailed", null, audit));
			}
			
			cloudStorageLogic.storeStampedDocument(document, documentByteArray);
			
			// Update documentD
			short stamped = document.getTotalStamping();
			stamped += 1;
			document.setTotalStamping(stamped);
			daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
			
			LOG.info("Kontrak {}, Dokumen {}, stamp process: {}/{}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), document.getTotalStamping(), document.getTotalMaterai() );
			if (document.getTotalMaterai().equals(document.getTotalStamping())) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_SDT_FIN, audit);
			} else {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
			}
			
			AttachEmeteraiErrorDetail response = new AttachEmeteraiErrorDetail();
			response.setErrorCode(GlobalVal.PERURI_SUCCESS_CODE);
			return response;
		} catch (InterruptedException e) {
			
			LOG.error("Kontrak {}, Dokumen {}, Upload to OSS exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e);
			LOG.info("Kontrak {}, Dokumen {}, interrupting current thread", document.getTrDocumentH().getRefNumber(), document.getDocumentId());
			
			Thread.currentThread().interrupt();
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, null, null);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.UPL_OSS, StampingErrorDetail.EXCEPTION, throwMaxError, audit);
			
			AttachEmeteraiErrorDetail response = new AttachEmeteraiErrorDetail();
			response.setErrorCode(GlobalVal.PERURI_ERROR_CODE);
			response.setMessage(e.getLocalizedMessage());
			return response;
			
		} catch (Exception e) {
			
			LOG.error("Kontrak {}, Dokumen {}, Upload to OSS exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e);
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, null, null);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.UPL_OSS, StampingErrorDetail.EXCEPTION, throwMaxError, audit);
			
			AttachEmeteraiErrorDetail response = new AttachEmeteraiErrorDetail();
			response.setErrorCode(GlobalVal.PERURI_ERROR_CODE);
			response.setMessage(e.getLocalizedMessage());
			return response;
		}
		
	}
	
	
	
	private void syncEmeteraiLogToConfins(TrDocumentH docH, AuditContext audit) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_TIME_FORMAT_SEC_WITHSLASH);
			
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocumentHeaderIdNewTran(docH.getIdDocumentH());
			List<TrDocumentDStampduty> docDsdts = daoFactory.getDocumentDao().getDocumentStampDutyByIdDocumentDNewTran(docD.getIdDocumentD());
			
			List<PaymentReceiptConfinsSyncBean> beans = new ArrayList<>();
			List<Map<String, Object>> sdts = daoFactory.getStampDutyDao().getIdStampDutyForDocumentNewTran(docD);
			
			for (Map<String, Object> sdtId : sdts) {
				BigInteger id = (BigInteger) sdtId.get("d0");
				TrStampDuty sdt = daoFactory.getStampDutyDao().getStampDutyById(id.longValue());
				TrDocumentDStampduty docDSdt = new TrDocumentDStampduty();
				for (TrDocumentDStampduty temp : docDsdts) {
					if (temp.getStartStampProcess().compareTo(sdt.getDtmCrt()) < 0 && temp.getStampingDate().compareTo(sdt.getDtmCrt()) > 0) {
						docDSdt = temp;
						break;
					}
				}
				
				String stampingStat = GlobalVal.CODE_LOV_SDT_GO_LIVE.equals(sdt.getMsLov().getCode()) ? "Success" : "Failed";
				String peruriTrxNo = StringUtils.isBlank(sdt.getTransactionId()) ? "ONPREMISE" : sdt.getTransactionId();
				String docName = StringUtils.isBlank(sdt.getDocumentNameSdt()) ? "ONPREMISE" : sdt.getDocumentNameSdt();
				
				PaymentReceiptConfinsSyncBean bean = new PaymentReceiptConfinsSyncBean();
				bean.setDocName(docName);
				bean.setEndStampingDt(sdf.format(docDSdt.getStampingDate()));
				bean.setErrMsg(sdt.getErrorMessage());
				bean.setPayRcvDTrxId(docD.getTenantTransactionId());
				bean.setPeruriTrxNo(peruriTrxNo);
				bean.setReqDt(sdf.format(docD.getDtmCrt()));
				bean.setSnCreatedDt(sdf.format(sdt.getDtmCrt()));
				bean.setSnEMeterai(sdt.getStampDutyNo());
				bean.setStampingStat(stampingStat);
				bean.setStartStampingDt(sdf.format(docDSdt.getStartStampProcess()));
				beans.add(bean);
			}
			
			// Set API request body
			PaymentReceiptConfinsSyncRequest request = new PaymentReceiptConfinsSyncRequest();
			request.setRequestLogPeruri(beans);
			String jsonRequest = gson.toJson(request);
			LOG.info("Kontrak {}, sync data to CONFINS request: {}", docH.getRefNumber(), jsonRequest);
			
			// Set API header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			
			// Set API URL
			// String url = daoFactory.getCommonDao().getGeneralSettingByTenant("USE_STAMPING_SYNC_URL", docH.getMsTenant()).getGsValue();
			MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(docH.getMsTenant(), GlobalVal.LOV_CODE_TENANT_SETTING_USE_STAMPING_SYNC_URL);
			
			commonValidatorLogic.validateNotNull(tenantSettings, "stamping sync url", audit);


			String url = tenantSettings.getSettingValue();
			

			WebClient client = WebClient.create(url).headers(mapHeader);
			MssTool.trustAllSslCertificate(client);
			
			// Process API response
			Response response = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String result = IOUtils.toString(isReader);
			LOG.info("Kontrak {}, sync data to CONFINS response: {}", docH.getRefNumber(), result);
			
			Status status = gson.fromJson(result, Status.class);
			if (200 == status.getCode()) {
				LOG.info("Kontrak {}, sync data to CONFINS successful", docH.getRefNumber());
				docH.setCallbackProcess((short) 13);
				docH.setUsrUpd(audit.getCallerId());
				docH.setDtmUpd(new Date());
				daoFactory.getDocumentDao().updateDocumentHNewTran(docH);
			}
			
		} catch (Exception e) {
			LOG.error("Kontrak {}, sync data to CONFINS exception: {}", docH.getRefNumber(), e.getLocalizedMessage(), e);
		}
		
	}

	@Override
	public PaymentReceiptConfinsSyncResponse syncEmeteraiLogsToConfins(AuditContext audit) throws IOException {
		List<TrDocumentH> lDocH = daoFactory.getDocumentDao().getListDocumentHeaderWithoutUserByCallbackProcessNewTrx((short) 11);
		if (CollectionUtils.isEmpty(lDocH)) {
			LOG.info("No Data to Sync to Confins");
		} 
		
		LOG.info("{} Documents to Sync to Confins", lDocH.size());
		for (TrDocumentH docH : lDocH) {
			syncEmeteraiLogToConfins(docH, audit);
		}
		return new PaymentReceiptConfinsSyncResponse();
	}
	
	private void uploadPaymentReceiptToDms(TrDocumentH docH, AuditContext audit) {
		try {
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocumentHeaderIdNewTran(docH.getIdDocumentH());
			MsTenant tenant = docD.getMsTenant();
			String documentDate = MssTool.formatDateToStringIn(docD.getRequestDate(), "yyyy/MM/dd");
			// String dmsUsername = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, AmGlobalKey.GENERALSETTING_DMS_USERNAME).getSettingValue();

			MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_DMS_USERNAME);

			commonValidatorLogic.validateNotNull(tenantSettings, "DMS Username", audit);

			String dmsUsername = tenantSettings.getSettingValue();

			if (StringUtils.isBlank(dmsUsername)) {
				throw new StampingException(String.format("Tenant Setting %1s tidak ditemukan untuk tenant %2s", AmGlobalKey.GENERALSETTING_DMS_USERNAME, tenant.getTenantCode()));
			}
			String uplDmsIntegrationValue = Base64.getEncoder().encodeToString(dmsUsername.getBytes());
			
			UploadStampedPaymenReceiptRequest request = new UploadStampedPaymenReceiptRequest();
			if (docH.getRefNumber().contains("-")) {
				String docHSplit = docH.getRefNumber().split("-")[0];
				request.setAgreementNo(docHSplit);
			} else if (!docH.getRefNumber().contains("-")) {
				request.setAgreementNo(docH.getRefNumber());
			}
			
			request.setUsername(uplDmsIntegrationValue);
			request.setContent("[String Base64]");
			request.setDokumenDate(documentDate);
			request.setDokumenPeruri(docD.getMsPeruriDocType().getDocName());
			request.setFilename(docD.getDocumentName() + ".pdf");
			request.setPaymentHistoryId(docD.getTenantTransactionId());
			String json = gson.toJson(request);
			LOG.info("Upload payment receipt to DMS request: {}", json);
			
			String base64Document = commonStampingLogic.getStampedDocumentFromOss(docD, audit);
			request.setContent(base64Document);
			
			String url = docH.getUrlUpload();
			if (StringUtils.isEmpty(url)) {
				throw new EmeteraiException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
						new String[] {"Upload URL"}, audit), ReasonEmeterai.UPLOAD_DOC_EXCEPTION);
			}
			
			OkHttpClient okHClient = MssTool.getUnsafeOkHttpClient();
			
			// Prepare header
			Map<String, String> header = new HashMap<>();
			header.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			
			header.put("Integration", uplDmsIntegrationValue);
			Headers headers = Headers.of(header);
			LOG.info("Kontrak {}, Dokumen {}, Upload payment receipt to DMS request header: {}", docH.getRefNumber(), docD.getDocumentId(), header);
			LOG.info("Kontrak {}, Dokumen {}, Upload payment receipt to DMS with URL: {}", docH.getRefNumber(), docD.getDocumentId(), url);
			
			String jsonBody = gson.toJson(request);
			RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_JSON), jsonBody);
			
			Request okHRequest = new Request.Builder()
					.headers(headers)
					.url(url)
					.post(body).build();
			
			okhttp3.Response okHResponse = okHClient.newCall(okHRequest).execute();
			String result = okHResponse.body().string();
			LOG.info("Kontrak {}, Dokumen {}, Upload document to DMS response: {}", docH.getRefNumber(), docD.getDocumentId(), result);
			
			UploadStampedDocumentResponse uploadResponse = gson.fromJson(result, UploadStampedDocumentResponse.class);
			if ("200".equals(uploadResponse.getStatusCode())) {
				LOG.info("Upload to DMS for {} Successful", docH.getRefNumber());
				docH.setCallbackProcess((short) 14);
				docH.setUsrUpd(audit.getCallerId());
				docH.setDtmUpd(new Date());
				daoFactory.getDocumentDao().updateDocumentHNewTran(docH);
			}
			
		} catch (Exception e) {
			LOG.error("Kontrak {}, Upload document to DMS exception: {}", docH.getRefNumber(), e.getLocalizedMessage(), e);
		}
		
	}

	@Override
	public UploadPaymentReceiptToDmsResponse uploadPaymentReceiptsToDms(AuditContext audit) {
		List<TrDocumentH> lDocH = daoFactory.getDocumentDao().getListDocumentHeaderWithoutUserByCallbackProcessNewTrx((short) 13);
		if (CollectionUtils.isEmpty(lDocH)) {
			LOG.info("No payment receipt to upload to DMS");
			return new UploadPaymentReceiptToDmsResponse();
		}
		
		LOG.info("{} payment receipt(s) to upload to DMS", lDocH.size());
		
		for (TrDocumentH docH : lDocH) {
			uploadPaymentReceiptToDms(docH, audit);
		}
		
		return new UploadPaymentReceiptToDmsResponse();
	}

	@Override
	public void deleteStampingBaseFromOss(AuditContext audit) {
		List<TrDocumentH> lDocH = daoFactory.getDocumentDao().getListDocumentHeaderWithoutUserByCallbackProcessNewTrx((short) 14);
		if (lDocH.isEmpty()) {
			LOG.info("No document to delete from stamping_document/base/ OSS");
			return;
		}
		
		LOG.info("{} document(s) to delete from stamping_document/base/ OSS", lDocH.size());
		for (TrDocumentH docH : lDocH) {
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocumentHeaderIdNewTran(docH.getIdDocumentH());
			cloudStorageLogic.deleteStampingPaymentReceipt(docD);
			
			docH.setCallbackProcess((short) 901);
			docH.setUsrUpd(audit.getCallerId());
			docH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHNewTran(docH);
		}
	}
	
	private String doAndRetryLoginPeruri(TrDocumentH documentH, long connectTimeout, long readTimeout, boolean throwMaxError, AuditContext audit) {
		EmeteraiPajakkuLoginResponseBean loginResponse = loginPeruri(documentH, connectTimeout, readTimeout, throwMaxError, audit);
		if (GlobalVal.PERURI_SUCCESS_CODE.equals(loginResponse.getStatusCode())) {
			return loginResponse.getToken();
		}
		return doAndRetryLoginPeruri(documentH, connectTimeout, readTimeout, throwMaxError, audit);
	}
	
	private InsertStampingPaymentReceiptResponse doAndRetryStampPaymentReceipt(TrDocumentH documentH, String token, long connectTimeout, long readTimeout, boolean returnDocument, boolean throwMaxError, AuditContext audit) {
		List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
		TrDocumentD document = documents.get(0);
		List<TrDocumentDStampduty> docSdts = daoFactory.getDocumentDao().getDocumentStampDutyByIdDocumentDNewTran(document.getIdDocumentD());
		
		int currentLoop = document.getTotalStamping();
		for (int i = currentLoop; i < document.getTotalMaterai(); i++) {
			TrDocumentDStampduty docSdt = docSdts.get(i);
			if (GlobalVal.STEP_ATTACH_METERAI_NOT_STR.equals(document.getSdtProcess())) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
			}
			
			if (GlobalVal.STEP_ATTACH_METERAI_UPL_DOC.equals(document.getSdtProcess())) {
				UploadDocPajakkuResponseBean response = uploadDocumentForStamping(document, docSdt, throwMaxError, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
					return doAndRetryStampPaymentReceipt(documentH, token, connectTimeout, readTimeout, returnDocument, throwMaxError, audit);
				}
			}
			if (GlobalVal.STEP_ATTACH_METERAI_GEN_SDT.equals(document.getSdtProcess())) {
				GenerateEmeteraiPajakkuResponse response = generateEmeterai(document, docSdt, token, connectTimeout, readTimeout, throwMaxError, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
					document.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_UPL_DOC);
					document.setUsrUpd(audit.getCallerId());
					document.setDtmUpd(new Date());
					daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
					
					return doAndRetryStampPaymentReceipt(documentH, token, connectTimeout, readTimeout, returnDocument, throwMaxError, audit);
				}
			}
			if (GlobalVal.STEP_ATTACH_METERAI_STM_SDT.equals(document.getSdtProcess())) {
				StampingEmeteraiPajakkuResponse response = stampEmeterai(document, docSdt, token, connectTimeout, readTimeout, throwMaxError, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getErrorCode())) {
					document.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_UPL_DOC);
					document.setUsrUpd(audit.getCallerId());
					document.setDtmUpd(new Date());
					daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
					
					TrStampDuty sdt = docSdt.getTrStampDuty();
					MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_STAMP_FAILED);
					sdt.setMsLov(sdtStatus);
					sdt.setErrorMessage(response.getErrorMessage());
					sdt.setUsrUpd(audit.getCallerId());
					sdt.setDtmUpd(new Date());
					daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
					
					return doAndRetryStampPaymentReceipt(documentH, token, connectTimeout, readTimeout, returnDocument, throwMaxError, audit);
				}
			}
			
			if (GlobalVal.STEP_ATTACH_METERAI_UPL_OSS.equals(document.getSdtProcess())) {
				AttachEmeteraiErrorDetail response = storeStampedDocumentToOss(document, throwMaxError, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getErrorCode())) {
					return doAndRetryStampPaymentReceipt(documentH, token, connectTimeout, readTimeout, returnDocument, throwMaxError, audit);
				}
			}
		}
		
		if (GlobalVal.STEP_ATTACH_METERAI_SDT_FIN.equals(document.getSdtProcess())) {
			commonStampingLogic.updateDocumentHMeteraiProcess(documentH, GlobalVal.ON_PREM_PR_STAMP_SUCCESS, audit);
			
			documentH.setCallbackProcess((short) 11);
			documentH.setUsrUpd(audit.getCallerId());
			documentH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
			
			InsertStampingPaymentReceiptResponse response = new InsertStampingPaymentReceiptResponse();
			if (returnDocument) {
				response.setStampedDocument(commonStampingLogic.getStampedDocumentFromOss(document, audit));
			}
			return response;
		}
		
		String message = "Should not reach this process. Please check the data";
		IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, document, message, null, null, null);
		commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.FINAL_VAL, StampingErrorDetail.VALIDATION, throwMaxError, audit);
		return doAndRetryStampPaymentReceipt(documentH, token, connectTimeout, readTimeout, returnDocument, throwMaxError, audit);
	}
	
	private InsertStampingPaymentReceiptResponse stampPaymentReceipt(TrDocumentH documentH, boolean returnDocument, AuditContext audit) {
		long connectTimeout = commonStampingLogic.getStampingConnectionTimeout(StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, audit);
		long readTimeout = commonStampingLogic.getStampingReadTimeout(StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, audit);
		
		String peruriLoginToken = doAndRetryLoginPeruri(documentH, connectTimeout, readTimeout, true, audit);
		return doAndRetryStampPaymentReceipt(documentH, peruriLoginToken, connectTimeout, readTimeout, returnDocument, true, audit);
	}

	@Override
	public InsertStampingPaymentReceiptResponse insertStampingPaymentReceipt(InsertStampingPaymentReceiptRequest request, AuditContext audit) {
		paymentReceiptValidatorLogic.validateInsertPaymentReceipt(request, audit);
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCodeNewTrx(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(getMessage("businesslogic.paymentsigntype.tenantnotfound", null, audit), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		Date now = new Date();
		MsLov lovIdType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_ID_TYPE, request.getIdType());
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_PERURI);
		
		MsRegion region = daoFactory.getRegionDao().getRegionByCodeAndTenantNewTrx(request.getRegionCode(), tenant.getTenantCode());
		if (null == region && StringUtils.isNotBlank(request.getRegionCode())) {
			region = new MsRegion();
			region.setDtmCrt(now);
			region.setUsrCrt(audit.getCallerId());
			region.setMsTenant(tenant);
			region.setRegionCode(StringUtils.upperCase(request.getRegionCode()));
			region.setRegionName(request.getRegionName());
			daoFactory.getRegionDao().insertRegionNewTrx(region);
		}
		
		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(request.getOfficeCode(), tenant.getTenantCode());
		if (null == office) {
			office = new MsOffice();
			office.setUsrCrt(audit.getCallerId());
			office.setDtmCrt(now);
			office.setOfficeCode(StringUtils.upperCase(request.getOfficeCode()));
			office.setOfficeName(request.getOfficeName());
			office.setIsActive("1");
			office.setMsTenant(tenant);
			if (null != region) {
				office.setMsRegion(region);
			}
			daoFactory.getOfficeDao().insertOfficeNewTrx(office);
		}
		
		MsDocTemplate docTemplate = daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(request.getDocumentTemplateCode(), tenant.getTenantCode());
		if (null == docTemplate) {
			String[] errParams = {"Document Template Code " + request.getDocumentTemplateCode()};
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, errParams, audit), ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
		}
		
		MsPeruriDocType peruriDocType = daoFactory.getPeruriDocTypeDao().getPeruriDocTypeByDocId(request.getPeruriDocTypeId());
		if (null == peruriDocType) {
			String[] errParams = {"Peruri Document Type " + request.getPeruriDocTypeId()};
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, errParams, audit), ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
		}
		
		MsLov docType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE, "TRX");
		MsLov signStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS, GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED);
		
		List<MsDocTemplateSignLoc> sdtLocs = daoFactory.getDocumentDao().getListSignLocation(request.getDocumentTemplateCode(), GlobalVal.CODE_LOV_SIGN_TYPE_SDT, tenant.getTenantCode());
		if (CollectionUtils.isEmpty(sdtLocs)) {
			throw new DocumentException(getMessage("businesslogic.document.templatemusthavesdt", null, audit), ReasonDocument.TEMPLATE_NO_SDT);
		}
		
		TrDocumentH docH = new TrDocumentH();
		docH.setUsrCrt(audit.getCallerId());
		docH.setDtmCrt(now);
		docH.setRefNumber(request.getDocumentNumber());
		docH.setTotalDocument((short) 1);
		docH.setMsOffice(office);
		docH.setMsTenant(tenant);
		docH.setMsLov(docType);
		docH.setUrlUpload(tenant.getUploadUrl());
		docH.setIsActive("1");
		docH.setProsesMaterai((short) 320);
		docH.setIsManualUpload("1");
		docH.setIsPostpaidStampduty("1");
		daoFactory.getDocumentDao().insertDocumentHeaderNewTrx(docH);
		
		TrDocumentD docD = new TrDocumentD();
		docD.setUsrCrt(audit.getCallerId());
		docD.setDtmCrt(now);
		docD.setDocumentId(daoFactory.getDocumentDao().generateDocumentId());
		docD.setMsLovByLovSignStatus(signStatus);
		docD.setMsDocTemplate(docTemplate);
		docD.setTrDocumentH(docH);
		docD.setRequestDate(MssTool.formatStringToDate(request.getDocDate(), GlobalVal.DATE_FORMAT));
		docD.setMsTenant(tenant);
		docD.setMsVendor(vendor);
		docD.setTotalMaterai((short) sdtLocs.size());
		docD.setDocumentName(request.getDocName());
		docD.setMsPeruriDocType(peruriDocType);
		docD.setDocumentNominal(Double.parseDouble(request.getDocNominal()));
		docD.setMsLovIdType(lovIdType);
		docD.setIdName(request.getTaxOwedsName());
		docD.setIdNo(request.getIdNo());
		docD.setTenantTransactionId(request.getDocumentTransactionId());
		docD.setTotalStamping((short) 0);
		docD.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_NOT_STR);
		daoFactory.getDocumentDao().insertDocumentDetailNewTrx(docD);
		
		short seq = 0;
		for (MsDocTemplateSignLoc sdt : sdtLocs) {
			TrDocumentDStampduty docDSdt = new TrDocumentDStampduty();
			docDSdt.setUsrCrt(audit.getCallerId());
			docDSdt.setDtmCrt(now);
			docDSdt.setSignLocation(sdt.getSignLocation());
			docDSdt.setSignPage(sdt.getSignPage());
			docDSdt.setSeqNo(seq);
			docDSdt.setTransform(sdt.getTransform());
			docDSdt.setTrDocumentD(docD);
			daoFactory.getDocumentDao().insertDocumentDetailSdtNewTrx(docDSdt);
			seq++;
		}
		
		// Store document to OSS
		byte[] documentByteArray = Base64.getDecoder().decode(request.getDocumentFile());
		cloudStorageLogic.storeStampingPaymentReceipt(docD, documentByteArray);
		
		boolean returnDocument = "1".equals(request.getReturnStampResult());
		return stampPaymentReceipt(docH, returnDocument, audit);
	}
	
	private void retryStampPaymentReceipt(TrDocumentH documentH, AuditContext audit) {
		commonStampingLogic.updateDocumentHMeteraiProcess(documentH, GlobalVal.ON_PREM_PR_STAMP_IN_PROGRESS, audit);
		
		long connectTimeout = commonStampingLogic.getStampingConnectionTimeout(StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, audit);
		long readTimeout = commonStampingLogic.getStampingReadTimeout(StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, audit);
		boolean throwMaxError = false;
		
		EmeteraiPajakkuLoginResponseBean loginResponse = loginPeruri(documentH, connectTimeout, readTimeout, throwMaxError, audit);
		if (!GlobalVal.PERURI_SUCCESS_CODE.equals(loginResponse.getStatusCode())) {
			return;
		}
		
		String token = loginResponse.getToken();
		
		List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
		TrDocumentD document = documents.get(0);
		LOG.info("Kontrak {}, Dokumen {}, Total Meterai: {}, Total Stamping: {}", documentH.getRefNumber(), document.getDocumentId(), document.getTotalMaterai(), document.getTotalStamping());
		List<TrDocumentDStampduty> docSdts = daoFactory.getDocumentDao().getDocumentStampDutyByIdDocumentDNewTran(document.getIdDocumentD());
		
		int currentLoop = document.getTotalStamping();
		for (int i = currentLoop; i < document.getTotalMaterai(); i++) {
			TrDocumentDStampduty docSdt = docSdts.get(i);
			if (GlobalVal.STEP_ATTACH_METERAI_NOT_STR.equals(document.getSdtProcess())) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
			}
			if (GlobalVal.STEP_ATTACH_METERAI_UPL_DOC.equals(document.getSdtProcess())) {
				UploadDocPajakkuResponseBean response = uploadDocumentForStamping(document, docSdt, throwMaxError, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
					return;
				}
			}
			if (GlobalVal.STEP_ATTACH_METERAI_GEN_SDT.equals(document.getSdtProcess())) {
				GenerateEmeteraiPajakkuResponse response = generateEmeterai(document, docSdt, token, connectTimeout, readTimeout, throwMaxError, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
					commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
					return;
				}
			}
			if (GlobalVal.STEP_ATTACH_METERAI_STM_SDT.equals(document.getSdtProcess())) {
				StampingEmeteraiPajakkuResponse response = stampEmeterai(document, docSdt, token, connectTimeout, readTimeout, throwMaxError, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getErrorCode())) {
					
					commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
					
					TrStampDuty sdt = docSdt.getTrStampDuty();
					MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_STAMP_FAILED);
					sdt.setMsLov(sdtStatus);
					sdt.setErrorMessage(response.getErrorMessage());
					sdt.setUsrUpd(audit.getCallerId());
					sdt.setDtmUpd(new Date());
					daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
					
					return;
				}
			}
			
			if (GlobalVal.STEP_ATTACH_METERAI_UPL_OSS.equals(document.getSdtProcess())) {
				AttachEmeteraiErrorDetail response = storeStampedDocumentToOss(document, throwMaxError, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getErrorCode())) {
					return;
				}
			}
		}
		
		if (GlobalVal.STEP_ATTACH_METERAI_SDT_FIN.equals(document.getSdtProcess())) {
			commonStampingLogic.updateDocumentHMeteraiProcess(documentH, GlobalVal.ON_PREM_PR_STAMP_SUCCESS, audit);
			
			documentH.setCallbackProcess((short) 11);
			documentH.setUsrUpd(audit.getCallerId());
			documentH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
			
			return;
		}
		
		String message = "Should not reach this process. Please check the data";
		IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, document, message, null, null, null);
		LOG.info("Job On-Premise payment receipt stamping");
		commonStampingLogic.incrementAgreementStampingErrorCount(bean, processNumber, StampingDocumentType.PAYMENT_RECEIPT_ON_PREM, StampingErrorLocation.FINAL_VAL, StampingErrorDetail.VALIDATION, throwMaxError, audit);
	}

	@Override
	public void retryStampingAllPaymentReceipt(AuditContext audit) {
		Short meteraiProcess = Short.valueOf(GlobalVal.ON_PREM_PR_STAMP_IN_QUEUE);
		List<TrDocumentH> documentHs = daoFactory.getDocumentDao().getListDocumentHeaderByProsesMeteraiNewTran(meteraiProcess);
		for (int i = 0; i < documentHs.size(); i++) {
			retryStampPaymentReceipt(documentHs.get(i), audit);
			LOG.info("Job On-Premise payment receipt stamping, done processing {} out of {} contract(s)", i+1, documentHs.size());
		}
		
	}

}
