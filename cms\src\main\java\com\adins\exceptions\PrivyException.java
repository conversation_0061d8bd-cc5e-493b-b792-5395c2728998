package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class PrivyException extends AdInsException {
	
	private static final long serialVersionUID = 1L;
	
	public PrivyException(String message) {
		super(message);
	}
	
	public PrivyException(String message, Throwable ex) {
		super(message, ex);
	}

	@Override
	public int getErrorCode() {
		return StatusCode.PRIVY_ERROR;
	}

}
