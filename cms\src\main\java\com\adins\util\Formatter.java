package com.adins.util;

import java.text.*;
import java.util.*;

/**
 * Formatter is a helper class for conversion of numeric and date types to/from String type.
 * The class internally use ThreadLocal object for returning the same number/date formatter instance
 * for the same thread and number/date Sformat <p>
 * Example:<br>
 * <code><pre>
 * DateFormat formatter = Formatter.getDateFormat(Formatter.DFORMAT_DMY); <br>
 * for (int i=0; i < xxx; i++) { <br>
 *    // use 'formatter' <br>
 * } <br>
 * </pre></code>
 * Inner class NumberFormatType is used for specifying number format
 * <p>Copyright: Copyright (c) 2004</p>
 * <p>Company: AdIns</p>
 * <AUTHOR>
 * @version 1.0
 */
public class Formatter {
  public static final String DFORMAT_YMD = "yyyyMMdd";
  public static final String DFORMAT_DMY = "ddMMyyyy";

  public static final NumberFormatType NFORMAT_ID_0  = new NumberFormatType('.', ',', 0);
  public static final NumberFormatType NFORMAT_ID_2  = new NumberFormatType('.', ',', 2);
  public static final NumberFormatType NFORMAT_US_0  = new NumberFormatType(',', '.', 0);
  public static final NumberFormatType NFORMAT_US_2  = new NumberFormatType(',', '.', 2);
  public static final NumberFormatType NFORMAT_PLAIN = new NumberFormatType(',', '.', 0, false);

  private static ThreadLocal<HashMap<NumberFormatType, DecimalFormat>> numberFormatPool = new ThreadLocal<>();
  private static ThreadLocal<HashMap<String, DateFormat>> dateFormatPool = new ThreadLocal<>();

  /*********************************************************/
  /**
   * Inner static class for specifying number format
   * <p>Copyright: Copyright (c) 2004</p>
   * <p>Company: AdIns</p>
   * <AUTHOR> Gunardi
   * @version 1.0
   */
  public static class NumberFormatType {
     private int decimal;
     private char thousandSeparator;
     private char decimalSeparator;
     private boolean showThousandSeparator;

     /**
      * Create instance of NumberFormatType with specified parameters and displays thousand separator
      * @param thousandSeparator char
      * @param decimalSeparator char
      * @param decimal int
      */
     public NumberFormatType(char thousandSeparator, char decimalSeparator, int decimal) {
        this(thousandSeparator, decimalSeparator, decimal, true);
     }
     /**
      * Create instance of NumberFormatType with specified parameters
      * @param thousandSeparator char
      * @param decimalSeparator char
      * @param decimal int
      * @param showThousandSeparator boolean
      */
     public NumberFormatType(char thousandSeparator, char decimalSeparator, int decimal, boolean showThousandSeparator) {
        this.decimal = decimal;
        this.thousandSeparator = thousandSeparator;
        this.decimalSeparator = decimalSeparator;
        this.showThousandSeparator = showThousandSeparator;
     }

     public boolean equals(Object object) {
        if (!(object instanceof NumberFormatType)) {
           return false;
        }
        if (this == object) { // optimization
           return true;
        }
        NumberFormatType other = (NumberFormatType) object;
        return other.thousandSeparator == this.thousandSeparator &&
            other.decimalSeparator == this.decimalSeparator &&
            other.showThousandSeparator == this.showThousandSeparator &&
            other.decimal == this.decimal;
     }

     public int hashCode() {
        int result = 5; // arbitrary
        result = 7*result + this.thousandSeparator;
        result = 7*result + this.decimalSeparator;
        result = 7*result + (this.showThousandSeparator ? 1 : 0);
        result = 7*result + this.decimal;
        return result;
     }
  }
  /*********************************************************/



  private Formatter() {}

  /**
   * Returns NumberFormat instance for the specified argument 'type'.
   * NumberFormat instance is cache per thread (using ThreadLocal) and per format,
   * so the same thread requesting the same formatType will get the same NumberFormat instance.
   * @param type String
   * @return NumberFormat
   */
  public static NumberFormat getNumberFormat(NumberFormatType formatType) {
    NumberFormat result = null;
    HashMap<NumberFormatType, DecimalFormat> numberFormats = numberFormatPool.get();
    if (numberFormats == null) {
      numberFormats = new HashMap<>();
      numberFormatPool.set(numberFormats);
    }
    else {
       result = numberFormats.get(formatType);
    }
    if (result == null) {
      DecimalFormatSymbols symbols = new DecimalFormatSymbols();
      symbols.setGroupingSeparator(formatType.thousandSeparator);
      symbols.setDecimalSeparator(formatType.decimalSeparator);
      DecimalFormat dFormat = new DecimalFormat("###,###,###,###,###,###.##", symbols);
      dFormat.setMinimumFractionDigits(formatType.decimal);
      dFormat.setMaximumFractionDigits(formatType.decimal);
      dFormat.setGroupingSize(formatType.showThousandSeparator ? 3 : 0);
      dFormat.setDecimalSeparatorAlwaysShown(formatType.decimal > 0);
      numberFormats.put(formatType, dFormat);
      result = dFormat;
    }
    return result;
  }

  /**
   * Returns (Simple)DateFormat instance for the specified format argument
   * DateFormat instance is cache per thread (using ThreadLocal) and per format,
   * so the same thread requesting the same date format will get the same DateFormat instance.
   * @param format String
   * @return DateFormat
   */
  public static DateFormat getDateFormat(String format) {
    DateFormat result = null;
    HashMap<String, DateFormat> dateFormats =  dateFormatPool.get();
    if (dateFormats == null) {
      dateFormats = new HashMap<>();
      dateFormatPool.set(dateFormats);
    }
    else {
       result = dateFormats.get(format);
    }
    if (result == null) {
      result = new SimpleDateFormat(format);
      dateFormats.put(format, result);
    }
    return result;
  }

  /**
   * Format specified java.util.Date date using format
   * @param date Date
   * @param format String
   * @return String
   */
  public static String formatDate(Date date, String format) {
     return getDateFormat(format).format(date);
  }

  /**
   * Format specified java.util.Date dt using formatter to text String
   * @param dt Date
   * @param formatter DateFormat
   * @return String
   */
  public static String formatDate(Date dt, DateFormat formatter) {
    return formatter.format(dt);
  }



  /**
   * Parse specified String dateStr using format
   * @param dateStr String
   * @param format String
   * @throws ParseException
   * @return Date
   */
  public static java.util.Date parseDate(String dateStr, String format) throws ParseException {
     return getDateFormat(format).parse(dateStr);
  }


  /**
   * Parse specified String dateStr using formatter to java.util.Date
   * @param dateStr String
   * @param formatter DateFormat
   * @throws ParseException
   * @return Date
   */
  public static Date parseDate(String dateStr, DateFormat formatter) throws ParseException  {
    return formatter.parse(dateStr);
  }

  /**
   * Change date format using formatter to java.util.Date
   * @param dt Date
   * @param format String
   * @throws ParseException
   * @return Date
   */
  public static Date changeDateFormat(Date dt,  String format) throws ParseException  {
	  return getDateFormat(format).parse(getDateFormat(format).format(dt));
  }

}
