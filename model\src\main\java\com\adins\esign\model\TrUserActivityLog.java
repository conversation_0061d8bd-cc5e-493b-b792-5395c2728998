package com.adins.esign.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.CreateableEntity;

@Entity
@Table(name = "tr_user_activity_log")
public class TrUserActivityLog extends CreateableEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
	
	private long idUserActivityLog;
	private AmMsuser idMsuser;
	private MsTenant idMsTenant;
	private AmMsrole idMsRole;
    private String loginId;
	private MsLov lovUserActivityLog ;

    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_user_activity_log", unique = true, nullable = false)
	public long getIdUserActivityLog() {
		return idUserActivityLog;
	}
	public void setIdUserActivityLog(long idUserActivityLog) {
		this.idUserActivityLog = idUserActivityLog;
	}

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getIdMsuser() {
		return idMsuser;
	}
	public void setIdMsuser(AmMsuser idMsuser) {
		this.idMsuser = idMsuser;
	}

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getIdMsTenant() {
		return idMsTenant;
	}
	public void setIdMsTenant(MsTenant idMsTenant) {
		this.idMsTenant = idMsTenant;
	}

    @ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_role", nullable = false)
	public AmMsrole getIdMsRole() {
		return idMsRole;
	}
	public void setIdMsRole(AmMsrole idMsRole) {
		this.idMsRole = idMsRole;
	}

    @Column(name = "login_id", length = 80, nullable = false)
	public String getLoginId() {
		return loginId;
	}
	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_user_activity_log", nullable = false)
	public MsLov getLovUserActivityLog() {
		return lovUserActivityLog;
	}
	public void setLovUserActivityLog(MsLov lovUserActivityLog) {
		this.lovUserActivityLog = lovUserActivityLog;
	}
}
