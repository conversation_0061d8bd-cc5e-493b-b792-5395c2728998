package com.adins.esign.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.FeedbackDao;
import com.adins.esign.model.TrFeedback;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class FeedbackDaoHbn extends BaseDaoHbn implements FeedbackDao{

	@Override
	public void insertFeedback(TrFeedback feedback) {
		feedback.setUsrCrt(MssTool.maskData(feedback.getUsrCrt()));
		this.managerDAO.insert(feedback);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertFeedbackNewTrx(TrFeedback feedback) {
		feedback.setUsrCrt(MssTool.maskData(feedback.getUsrCrt()));
		this.managerDAO.insert(feedback);
	}

}
