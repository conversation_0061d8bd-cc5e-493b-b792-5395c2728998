package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class FormException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum Reason {
		INVALID_IDENTIFIER, 
		INVALID_SCRIPT,
		UNKNOWN
	}

	private final Reason reason;

	public FormException(Reason reason) {
		this.reason = reason;
	}

	public FormException(String message, Reason reason) {
		super(message);
		this.reason = reason;
	}

	public FormException(Throwable ex, Reason reason) {
		super(ex);
		this.reason = reason;
	}

	public FormException(String message, Throwable ex, Reason reason) {
		super(message, ex);
		this.reason = reason;
	}

	public Reason getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
			case INVALID_IDENTIFIER:
				return StatusCode.INVALID_IDENTIFIER;
			case INVALID_SCRIPT:
				return StatusCode.INVALID_SCRIPT;
			case UNKNOWN:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}
}
