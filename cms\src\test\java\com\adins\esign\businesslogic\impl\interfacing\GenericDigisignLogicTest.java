package com.adins.esign.businesslogic.impl.interfacing;

import java.io.UnsupportedEncodingException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;

@Disabled("temp disabled")
@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class GenericDigisignLogicTest {
	
	@Autowired private DigisignLogic digisignLogic;
	private String encryptedHtml = "0pbE1wAmozAohhkoojVVpzFxPDk5BH9qnMGO3uqTGJNJ0umvrhiETGF%2FQH2EtKy0QIcL9j3DTj%2By%0AyCQbqej1zQwbLrLneG56B04Vc1jbqtzSh6R3FW4894%2FmFIMibWMzZCoAm%2FiPPNZ2b7vH6qDhOMK1%0AHlzvrZACIRjrmPPyuo2sj5cKtyLxKii%2F6jeTcWDr4NGgGdW0PmvYxAKb1xjOw4fucLAEEsZm4cnp%0A412tjx8%3D";
	//expected Key=K9bu6w89raldAWTj	
	
	@Test
	void testDigiMsg() throws UnsupportedEncodingException {
		String plain = digisignLogic.decryptMessage(encryptedHtml, "WOM");
		Assertions.assertEquals("{\"document_id\":\"MT40300000167589_B\",\"status\":\"0\",\"status_document\":\"waiting\",\"result\":\"00\",\"notif\":\"Proses tanda tangan berhasil!\",\"email_user\":\"<EMAIL>\"}",
				plain);
	}
}
