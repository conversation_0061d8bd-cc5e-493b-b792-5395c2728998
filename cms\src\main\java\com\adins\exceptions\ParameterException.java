package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class ParameterException extends AdInsException {
	private static final long serialVersionUID = 1L;
	
	public enum ReasonParam {
		MANDATORY_PARAM,
		INCORRENT_DATE_FORMAT,
		INVALID_DATE_RANGE,
		MUST_BE_NUMERIC,
		INVALID_FORMAT,
		INVALID_NIK_LENGTH,
		NIK_MUST_BE_NUMERIC,
		INVALID_PASSWORD_LENGTH,
		INVALID_PASSWORD_COMPLEXITY,
		INVALID_GENEDER_FORMAT,
		INVALID_CONDITION,
		INVALID_LENGTH,
		UNKNOWN,
		DATA_NOT_FOUND,
		ALREADY_EXISTED
	}
	
	private final ReasonParam reason;
	
	public ParameterException(String message, ReasonParam reason) {
		super(message);
		this.reason = reason;
	}

	@Override
	public int getErrorCode() {
		if (reason != null) {
			switch (reason) {
			case MANDATORY_PARAM:
				return StatusCode.MANDATORY_PARAMETER;
			case INCORRENT_DATE_FORMAT:
				return StatusCode.INVALID_DATE_FORMAT;
			case INVALID_DATE_RANGE:
				return StatusCode.INVALID_DATE_RANGE;
			case MUST_BE_NUMERIC:
				return StatusCode.MUST_BE_NUMERIC;
			case INVALID_FORMAT:
				return StatusCode.INVALID_FORMAT;
			case INVALID_NIK_LENGTH:
				return StatusCode.INVALID_NIK_LENGTH;
			case NIK_MUST_BE_NUMERIC:
				return StatusCode.NIK_IS_NOT_NUMBER;
			case INVALID_PASSWORD_LENGTH:
				return StatusCode.INVALID_PASSWORD_LENGTH;
			case INVALID_PASSWORD_COMPLEXITY:
				return StatusCode.INVALID_PASSWORD_COMPLEXITY;
			case INVALID_GENEDER_FORMAT:
				return StatusCode.INVALID_GENDER_CODE;
			case INVALID_CONDITION:
				return StatusCode.INVALID_CONDITION;
			case INVALID_LENGTH:
				return StatusCode.INVALID_LENGTH;
			case DATA_NOT_FOUND:
				return StatusCode.DATA_NOT_FOUND;
			case ALREADY_EXISTED:
				return StatusCode.ALREADY_EXISTED;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}
}
