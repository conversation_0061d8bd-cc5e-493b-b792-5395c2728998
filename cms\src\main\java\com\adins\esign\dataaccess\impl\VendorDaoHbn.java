package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.VendorDao;
import com.adins.esign.model.MsBalancevendoroftenant;
import com.adins.esign.model.<PERSON>Lov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.custom.ListPsrePriorityBean;
import com.adins.esign.model.custom.VendorTenantBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.GetListPSrESettingRequest;

@Transactional
@Component
public class VendorDaoHbn extends BaseDaoHbn implements VendorDao {
	
	private static final String VENDOR_TYPE = "vendorType";
	
	@Override
	public void updateVendor(MsVendor updVendor) {
		updVendor.setUsrUpd(MssTool.maskData(updVendor.getUsrUpd()));
		this.managerDAO.update(updVendor);
	}
	
	@Override
	public MsVendor getVendorDataByIdMsUser(long idMsUser, String vendorCode) {
		Object[][] params = new Object[][] {{AmMsuser.ID_MS_USER_HBM, idMsUser}, {MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode)}} ;
		
		StringBuilder query = new StringBuilder()
				.append("select id_ms_vendor_registered_user ")
				.append("from ms_vendor_registered_user  vru ")
				.append("join am_msuser mu on vru.id_ms_user = mu.id_ms_user ")
				.append("join ms_vendor v on vru.id_ms_vendor = v.id_ms_vendor ")
				.append("where vru.is_active = '1' and v.is_active = '1' and mu.id_ms_user = :idMsUser and v.vendor_code = :vendorCode ");
		
		BigInteger idMsVendorRegisteredUser = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		
		if (null == idMsVendorRegisteredUser) {
			return null;
		} else {
			long vendorRegisteredUserId = idMsVendorRegisteredUser.longValue();
			MsVendorRegisteredUser vendorRegisteredUser = this.managerDAO.selectOne(MsVendorRegisteredUser.class,
					new Object[][] {{ Restrictions.eq("idMsVendorRegisteredUser", vendorRegisteredUserId) }});
			return vendorRegisteredUser.getMsVendor();
		}
	}
	
	@Override
	public MsVendor getVendorPSrEByVendorCode(String vendorCode) {
		return this.managerDAO.selectOne(
				"from MsVendor mv "
				+ "join fetch mv.msLovVendorType mlvt "
				+ "where mv.vendorCode = :vendorCode and mlvt.lovGroup = 'VENDOR_TYPE' and mlvt.code ='PSRE' ", 
	new Object[][] {{ MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode) }});
	}
	
	@Override
	public MsVendor getVendorByCode(String vendorCode) {
		if (StringUtils.isBlank(vendorCode)) {
			return null;
		}
		
		Object[][] queryParams = {
				{Restrictions.eq(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode))}
		};
		return this.managerDAO.selectOne(MsVendor.class, queryParams);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsVendor getVendorByCodeNewTrx(String vendorCode) {
		if (StringUtils.isBlank(vendorCode)) {
			return null;
		}
		
		Object[][] queryParams = {
				{Restrictions.eq(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode))}
		};
		return this.managerDAO.selectOne(MsVendor.class, queryParams);
	}
	
	@Override
	public MsVendorRegisteredUser getVendorNotActiveRegisteredUserByLoginId(String loginId) {
		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser vuser "
				+ "join fetch vuser.msVendor mv "
				+ "join fetch vuser.amMsuser mu "
				+ "where mv.isActive ='0' and  mu.loginId = :loginId ", 
		new Object[][] {{ AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId) }});
	}
	
	@Override
	public MsVendoroftenant getVendoroftenantByTenantId(long idTenant) {
		return this.managerDAO.selectOne(MsVendoroftenant.class,
				new Object[][] {{ Restrictions.eq("msTenant.idMsTenant", idTenant) }});
	}
	
	
	@Override
	public MsVendoroftenant getVendoroftenant(MsTenant tenant, MsVendor vendor) {
		Object[][] params = {{Restrictions.eq("msTenant", tenant)},{Restrictions.eq("msVendor", vendor)}};
		
		return this.managerDAO.selectOne(MsVendoroftenant.class, params);
	}

	@Override
	public MsVendoroftenant getVendorTenantByCode(String tenantCode, String vendorCode) {
		return this.managerDAO.selectOne(
					"from MsVendoroftenant vot "
					+ "join fetch vot.msVendor mv "
					+ "join fetch vot.msTenant mt "
					+ "where mt.tenantCode = :tenantCode and mv.vendorCode = :vendorCode and mv.isActive ='1' ", 
		new Object[][] {{ MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode) },
						{ MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode) }});
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<MsVendoroftenant> getListVendoroftenant() {
			Map<String, Object> mapResultList = this.managerDAO.list(
					"from MsVendoroftenant vot "
					+ "join fetch vot.msVendor mv "
					+ "join fetch mv.msLov ml "
					+ "join fetch vot.msTenant mt ", 
					new Object[][] {});
			return (List<MsVendoroftenant>) mapResultList.get(AmGlobalKey.MAP_RESULT_LIST);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<MsVendoroftenant> getListVendoroftenantByTenantCode(String tenantCode) {
		Map<String, Object> mapResultList = this.managerDAO.list(
				"from MsVendoroftenant vot "
				+ "join fetch vot.msVendor mv "
				+ "join fetch vot.msTenant mt "
				+ "where mt.tenantCode = :tenantCode "
				+ "order by vot.defaultVendor asc ",
				new Object[][] {{ MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode) }});
		return (List<MsVendoroftenant>) mapResultList.get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public List<Map<String, Object>> getVendorList(String vendorTypeCode, String tenantCode, String vendorCode, String vendorCodeExclude) {
		Map<String, Object> params = new HashMap<>();
		String conditionalParamVendorTypeTenantCode = this.constructParamVendorTypeTenantcode
				(params, vendorTypeCode, tenantCode, vendorCode, vendorCodeExclude);
		StringBuilder query = new StringBuilder();
		
		query
			.append("select vendor_code, vendor_name ")
			.append("from ms_vendor mv ")
			.append("join ms_lov ml on mv.lov_vendor_type = ml.id_lov ")
			.append("join ms_vendoroftenant vot on mv.id_ms_vendor = vot.id_ms_vendor ")
			.append("join ms_tenant mt on mt.id_ms_tenant = vot.id_ms_tenant ")
			.append("where 1 = 1 ")
			.append("and mv.is_active = '1' ")
			.append(conditionalParamVendorTypeTenantCode)
			.append("order by vot.default_vendor asc nulls last ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	private String constructParamVendorTypeTenantcode(Map<String, Object> params, String vendorTypeCode, String tenantCode, 
			String vendorCode, String vendorCodeExclude) {
		StringBuilder queryParamSearch = new StringBuilder();
		if (Strings.isNotBlank(vendorTypeCode)) {
			params.put("vendorTypeCode", StringUtils.upperCase(vendorTypeCode));
			queryParamSearch.append(" and ml.code = :vendorTypeCode ");
		}
		
		if (Strings.isNotBlank(tenantCode)) {
			params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
			queryParamSearch.append(" and mt.tenant_code = :tenantCode ");
		}
		
		if (Strings.isNotBlank(vendorCode)) {
			params.put(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode));
			queryParamSearch.append(" and mv.vendor_code = :vendorCode ");
		}
		
		if (Strings.isNotBlank(vendorCodeExclude)) { //exclude vendor tertentu
			params.put("vendorCodeExc", StringUtils.upperCase(vendorCodeExclude));
			queryParamSearch.append(" and mv.vendor_code != :vendorCodeExc ");
		}
		
		return queryParamSearch.toString();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<MsBalancevendoroftenant> getListMsBalancevendoroftenant() {
		Map<String, Object> mapResultList = this.managerDAO.list(
				"from MsBalancevendoroftenant bvot "
				+ "join fetch bvot.msVendor mv "
				+ "join fetch mv.msLovVendorType mlv "
				+ "join fetch bvot.msTenant mt "
				+ "join fetch bvot.msLov mlbvot ", 
				new Object[][] {});
		return (List<MsBalancevendoroftenant>) mapResultList.get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public void insertVendoroftenant(MsVendoroftenant vot) {
		vot.setUsrCrt(MssTool.maskData(vot.getUsrCrt()));
		this.managerDAO.insert(vot);
	}

	@Override
	public List<VendorTenantBean> getListVendorTenantFromBalanceVendorOfTenant(String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		StringBuilder query = new StringBuilder();
		query.append(" select distinct v.vendor_code as \"vendorCode\", t.tenant_code as \"tenantCode\" ")
			 .append(" from ms_balancevendoroftenant bvot ")
			 .append(" join lateral ( select t.tenant_code ")
			 				.append(" from ms_tenant t ")
			 				.append(" where t.id_ms_tenant = bvot.id_ms_tenant and t.tenant_code = :tenantCode ) t on true ")
			 .append(" join lateral ( select v.vendor_code ")
				 			.append(" from ms_vendor v ")
				 			.append(" where v.id_ms_vendor = bvot.id_ms_vendor ) v on true ");
							
		return this.managerDAO.selectForListString(VendorTenantBean.class, query.toString(), params, null);
	}

	@Override
	public void deleteVendoroftenant(MsVendoroftenant vot) {
		this.managerDAO.delete(vot);
	}

	@Override
	public List<Map<String, Object>> getListVendorByTenantAndBalanceType(String tenantCode, String balanceType) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		params.put(MsLov.CODE_HBM, StringUtils.upperCase(balanceType));
		
		StringBuilder query = new StringBuilder();
		query.append(" select v.vendor_code, v.vendor_name ")
			 .append(" from ms_vendor v ")
			 .append(" join ms_balancevendoroftenant bvot on bvot.id_ms_vendor = v.id_ms_vendor ")
			 .append(" join ms_tenant t on t.id_ms_tenant = bvot.id_ms_tenant ")
			 .append(" join ms_lov l on l.id_lov = bvot.lov_balance_type ")
			 .append(" where t.tenant_code = :tenantCode and l.code = :code ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getAllVendorList() {
		StringBuilder query = new StringBuilder();
		query
		.append("select vendor_code, vendor_name ")
		.append("from ms_vendor mv ")
		.append("where 1 = 1 ");
	
		return this.managerDAO.selectAllNativeString(query.toString(), null);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<MsBalancevendoroftenant> getListBalanceByVendorTenant(String tenantCode, String vendorCode) {
		Object[][] param = new Object[][] {{MsTenant.TENANT_CODE_HBM, tenantCode}, {MsVendor.VENDOR_CODE_HBM, vendorCode}};

		Map<String, Object> mapResultList = this.managerDAO.list(
				"from MsBalancevendoroftenant bvot "
				+ "join fetch bvot.msVendor mv "
				+ "join fetch bvot.msTenant mt "
				+ "join fetch bvot.msLov balType "
				+ "where mt.tenantCode = :tenantCode and mv.vendorCode = :vendorCode "
				+ "and balType.isActive = '1' "
				+ "and mv.isActive ='1' "
				+ "and mt.isActive='1' "
				+ "and (bvot.isHidden is null or bvot.isHidden = '0') "
			, param);
		return (List<MsBalancevendoroftenant>) mapResultList.get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsVendor getDefaultOperatingPsreVendorOfTenant(String tenantCode) {
		if (StringUtils.isBlank(tenantCode)) {
			return null;
		}
		
		Map<String, Object> params = new HashMap<>();
		params.put(VENDOR_TYPE, GlobalVal.CODE_LOV_VENDOR_TYPE_PSRE);
		params.put(MsVendor.IS_OPERATING_HBM, "1");
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select mv.id_ms_vendor from ms_vendoroftenant vot ")
			.append("join ms_tenant mt on vot.id_ms_tenant = mt.id_ms_tenant ")
			.append("join ms_vendor mv on vot.id_ms_vendor = mv.id_ms_vendor ")
			.append("join ms_lov lvt on mv.lov_vendor_type =lvt.id_lov ")
			.append("where mv.is_operating = :isOperating ")
			.append("and lvt.code = :vendorType ")
			.append("and mt.tenant_code = :tenantCode ")
			.append("order by vot.default_vendor asc limit 1 ");
		
		BigInteger idMsVendor = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsVendor) {
			if (StringUtils.isBlank(tenantCode)) {
				return null;
			}
			
			params = new HashMap<>();
			params.put(VENDOR_TYPE, GlobalVal.CODE_LOV_VENDOR_TYPE_PSRE);
			params.put(MsVendoroftenant.DEFAULT_VENDOR_HBM, "1");
			params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
			
			query = new StringBuilder();
			query
				.append("select mv.id_ms_vendor from ms_vendoroftenant vot ")
				.append("join ms_tenant mt on vot.id_ms_tenant = mt.id_ms_tenant ")
				.append("join ms_vendor mv on vot.id_ms_vendor = mv.id_ms_vendor ")
				.append("join ms_lov lvt on mv.lov_vendor_type =lvt.id_lov ")
				.append("where vot.default_vendor = :defaultVendor ")
				.append("and lvt.code = :vendorType ")
				.append("and mt.tenant_code = :tenantCode ")
				.append("order by vot.dtm_crt asc limit 1 ");
			
			idMsVendor = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
			if (null == idMsVendor) {
				return null;
			}
			
			return this.managerDAO.selectOne(MsVendor.class, new Object[][] {{ Restrictions.eq(MsVendor.ID_VENDOR_HBM, idMsVendor.longValue()) }});
		}
		
		return this.managerDAO.selectOne(MsVendor.class, new Object[][] {{ Restrictions.eq(MsVendor.ID_VENDOR_HBM, idMsVendor.longValue()) }});
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<MsVendoroftenant> getListVendoroftenantByVendorCode(String vendorCode) {
		Map<String, Object> mapResultList = this.managerDAO.list(
				"from MsVendoroftenant vot "
				+ "join fetch vot.msVendor mv "
				+ "join fetch vot.msTenant mt "
				+ "where mv.vendorCode = :vendorCode ",
				new Object[][] {{ MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode) }});
		return (List<MsVendoroftenant>) mapResultList.get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public MsVendor getVendorByIdMsVendor(long idMsVendor) {
		Object[][] queryParams = {
				{Restrictions.eq(MsVendor.ID_VENDOR_HBM, idMsVendor)}
		};
		return this.managerDAO.selectOne(MsVendor.class, queryParams);
	}
	
	//FIX SONAR
	
	@Override
	public String getHighestPriorityVendorCodeAvailable(String tenantCode, List<String> excludedVendors) {
		StringBuilder vendorsToExclude = new StringBuilder();
		if (CollectionUtils.isNotEmpty(excludedVendors)) {
			vendorsToExclude.append("and vendor_code not in ( ");
			Iterator<String> itr = excludedVendors.iterator();
			while (itr.hasNext()) {
				String vendor = itr.next();
				if (itr.hasNext()) {
					vendorsToExclude.append("\'"+vendor+"\', ");
				} else {
					vendorsToExclude.append("\'"+vendor+"\' ) ");
				}
			}
		}
		
		Object[][] params = new Object[][] {{GlobalVal.CONST_TENANT_CODE, tenantCode}};
		
		StringBuilder query = new StringBuilder();
		query.append("select v.vendor_code as \"vendorCode\" from ms_vendoroftenant vot ")
			 .append("join ms_vendor v on v.id_ms_vendor = vot.id_ms_vendor ")
			 .append("join ms_tenant t on t.id_ms_tenant = vot.id_ms_tenant ")
			 .append("join ms_lov l on l.id_lov = v.lov_vendor_type ")
			 .append("where l.code = 'PSRE' and t.tenant_code = :tenantCode and is_operating = '1' ")
			 .append(vendorsToExclude.toString())
			 .append("order by default_vendor asc limit 1 ");
		
		String operatingVendor = (String) this.managerDAO.selectOneNativeString(query.toString(), params);
		
		if (StringUtils.isEmpty(operatingVendor)) {
			StringBuilder queryNonOperating = new StringBuilder();
			queryNonOperating.append("select v.vendor_code as \"vendorCode\" from ms_vendoroftenant vot ")
				 .append("join ms_vendor v on v.id_ms_vendor = vot.id_ms_vendor ")
				 .append("join ms_tenant t on t.id_ms_tenant = vot.id_ms_tenant ")
				 .append("join ms_lov l on l.id_lov = v.lov_vendor_type ")
				 .append("where l.code = 'PSRE' and t.tenant_code = :tenantCode ")
				 .append(vendorsToExclude.toString())
				 .append("order by default_vendor asc limit 1 ");
			
			return (String) this.managerDAO.selectOneNativeString(queryNonOperating.toString(), params);
		}
		
		return operatingVendor;
	}

	
	@Override
	public MsBalancevendoroftenant getBalanceVendorOfTenant(String vendorCode, String tenantCode, String balanceType) {
		return this.managerDAO.selectOne(
				"from MsBalancevendoroftenant bvot "
				+ "join fetch bvot.msVendor mv "
				+ "join fetch bvot.msTenant mt "
				+ "join fetch bvot.msLov l "
				+ "where mt.tenantCode = :tenantCode and mv.vendorCode = :vendorCode and mv.isActive ='1' "
				+ "and l.code = :balanceType ", 
				new Object[][] {{ MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode) },
								{ MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode) },
								{ "balanceType", StringUtils.upperCase(balanceType)}});
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsVendor getMainDefaultPsreVendorOfTenant(String tenantCode) {
		if (StringUtils.isBlank(tenantCode)) {
			return null;
		}
		
		Map<String, Object> params = new HashMap<>();
		params.put(VENDOR_TYPE, GlobalVal.CODE_LOV_VENDOR_TYPE_PSRE);
		params.put(MsVendoroftenant.DEFAULT_VENDOR_HBM, "1");
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select mv.id_ms_vendor from ms_vendoroftenant vot ")
			.append("join ms_tenant mt on vot.id_ms_tenant = mt.id_ms_tenant ")
			.append("join ms_vendor mv on vot.id_ms_vendor = mv.id_ms_vendor ")
			.append("join ms_lov lvt on mv.lov_vendor_type =lvt.id_lov ")
			.append("where vot.default_vendor = :defaultVendor ")
			.append("and lvt.code = :vendorType ")
			.append("and mt.tenant_code = :tenantCode ")
			.append("order by vot.dtm_crt asc limit 1 ");
		
		BigInteger idMsVendor = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsVendor) {
			return null;
		}
		
		return this.managerDAO.selectOne(MsVendor.class, new Object[][] {{ Restrictions.eq(MsVendor.ID_VENDOR_HBM, idMsVendor.longValue()) }});
	}

	@Override
	public MsVendor getHighestPriorityVendorAvailable(String tenantCode, List<String> excludedVendorCodes) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		params.put("excludedVendors", excludedVendorCodes);
		params.put(MsLov.LOV_GROUP_HBM, "VENDOR_TYPE");
		params.put(MsLov.CODE_HBM, "PSRE");
		params.put(MsVendor.IS_OPERATING_HBM, "1");
		
		StringBuilder query = new StringBuilder();
		query
			.append("select mv.id_ms_vendor ")
			.append("from ms_vendoroftenant vot ")
			.append("join ms_vendor mv on vot.id_ms_vendor = mv.id_ms_vendor ")
			.append("join ms_lov lvt on mv.lov_vendor_type = lvt.id_lov ")
			.append("join ms_tenant mt on vot.id_ms_tenant = mt.id_ms_tenant ")
			.append("where mt.tenant_code = :tenantCode ")
			.append("and lvt.lov_group = :lovGroup and code = :code ")
			.append("and mv.is_operating = :isOperating ")
			.append("and mv.vendor_code not in :excludedVendors ")
			.append("order by default_vendor asc limit 1 ");
		
		BigInteger idMsVendor = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsVendor) {
			return null;
		}
		
		return getVendorByIdMsVendor(idMsVendor.longValue());
	}

	@Override
	public List<Map<String, Object>> getVendorByIdNo(String idNo) {
		if (StringUtils.isBlank(idNo)) {
			return null;
		}
		Object[][] params = new Object[][] {{AmMsuser.HASHED_IDNO_HBM, MssTool.getHashedString(idNo)}};

		StringBuilder query = new StringBuilder();
		query.append("SELECT mv.vendor_code FROM ms_vendor_registered_user mu JOIN am_msuser u"
				+ " ON (mu.id_ms_user = u.id_ms_user) "
				+ "JOIN ms_vendor mv ON (mu.id_ms_vendor = mv.id_ms_vendor)  WHERE hashed_id_no = :hashedIdNo");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getListVendorByIdMsUser(long idMsUser) {
		
		Object[][] params = new Object[][] {{AmMsuser.ID_MS_USER_HBM,idMsUser}};
		
		StringBuilder query = new StringBuilder();
		query.append("select DISTINCT mv.vendor_code, mv.vendor_name " + 
				"from am_msuser mu " + 
				"join ms_useroftenant uot on mu.id_ms_user = uot.id_ms_user " + 
				"join ms_vendoroftenant vot on uot.id_ms_tenant = vot.id_ms_tenant " + 
				"join ms_vendor mv on vot.id_ms_vendor = mv.id_ms_vendor " + 
				"join ms_lov ml on mv.lov_vendor_type = ml.id_lov " + 
				"where ml.lov_group = 'VENDOR_TYPE' and ml.code = 'PSRE' " + 
				"and mu.id_ms_user = :idMsUser");
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public String getVendorCodeByIdDocumentH(long idDocumentH) {
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		
		params.put("idDocumentH", idDocumentH);
		
		query.append("select distinct(vendor_code) from ms_vendor join tr_document_d on tr_document_d.id_ms_vendor = ms_vendor.id_ms_vendor \r\n" + 
				"where id_document_h = :idDocumentH");
		
		return (String) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Map<String, Object>> getListPSrESetting(GetListPSrESettingRequest request, int min, int max) {
		
		Map<String, Object> params = new HashMap<>();
		StringBuilder query = new StringBuilder();
		params.put("min", min);
		params.put("max", max);
		params.put(MsLov.CODE_HBM, "PSRE");
		
		query
			.append("with psresetting as (select mv.vendor_code as vendorcode, mv.vendor_name as vendorname, mv.is_active as isactive, mv.is_operating as isoperating, ml2.description as description, ml2.code as code, row_number() over (order by mv.vendor_name) as rownum ")
			.append("from ms_vendor mv ")
			.append("join ms_lov ml on mv.lov_vendor_type = ml.id_lov ")
			.append("left Join ms_lov ml2 on mv.lov_vendor_sign_payment_type = ml2.id_lov ")
			.append("where ml.code = :code ");
		
		if (StringUtils.isNotBlank(request.getVendorName())) {
			params.put("vendorName", "%"+StringUtils.upperCase(request.getVendorName())+"%");
			query.append("and upper(mv.vendor_name) like :vendorName ");
		}
		
		if (StringUtils.isNotBlank(request.getVendorCode())) {
			params.put(GlobalVal.CONST_VENDOR_CODE, "%"+StringUtils.upperCase(request.getVendorCode())+"%");
			query.append("and upper(mv.vendor_code) like :vendorCode ");
		}
		
		if (StringUtils.isNotBlank(request.getStatus())) {
			params.put("isActive", StringUtils.upperCase(request.getStatus()));
			query.append("and mv.is_active = :isActive ");
		}
		
		if (StringUtils.isNotBlank(request.getStatusOperating())) {
			params.put(MsVendor.IS_OPERATING_HBM, StringUtils.upperCase(request.getStatusOperating()));
			query.append("and mv.is_operating = :isOperating ");
		}
		
		query.append(") select vendorcode, vendorname, isactive, isoperating, description, code from psresetting where rownum between :min and :max");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public int countGetListPSrESetting(GetListPSrESettingRequest request) {
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		params.put(MsLov.CODE_HBM, "PSRE");
		query
			.append("select count (*) ")
			.append("from ms_vendor mv ")
			.append("join ms_lov ml on mv.lov_vendor_type = ml.id_lov ")
			.append("left Join ms_lov ml2 on mv.lov_vendor_sign_payment_type = ml2.id_lov ")
			.append("where ml.code = :code ");
	
		if (StringUtils.isNotBlank(request.getVendorName())) {
			params.put("vendorName", "%"+StringUtils.upperCase(request.getVendorName())+"%");
			query.append("and upper(mv.vendor_name) like :vendorName ");
		}
		
		if (StringUtils.isNotBlank(request.getVendorCode())) {
			params.put(GlobalVal.CONST_VENDOR_CODE, "%"+StringUtils.upperCase(request.getVendorCode())+"%");
			query.append("and upper(mv.vendor_code) like :vendorCode ");
		}
		
		if (StringUtils.isNotBlank(request.getStatus())) {
			params.put("isActive", StringUtils.upperCase(request.getStatus()));
			query.append("and mv.is_active = :isActive ");
		}
		
		if (StringUtils.isNotBlank(request.getStatusOperating())) {
			params.put(MsVendor.IS_OPERATING_HBM, StringUtils.upperCase(request.getStatusOperating()));
			query.append("and mv.is_operating = :isOperating ");
		}
		
		List<Map<String, Object>> total = this.getManagerDAO().selectAllNativeString(query.toString(), params);
		
		String totalString = total.get(0).toString();
		int start = total.get(0).toString().indexOf("=");
		int end = total.get(0).toString().indexOf("}");
		String subString = totalString.substring(start + 1, end);

		return Integer.parseInt(subString);
	}

	@Override
	public List<ListPsrePriorityBean> getListPsrePriority(String tenantCode) {
		
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(GlobalVal.CONST_TENANT_CODE, StringUtils.upperCase(tenantCode));
		params.put("lovCode", GlobalVal.CODE_LOV_VENDOR_TYPE_PSRE);
		
		query.append("select ms_vendor.vendor_code as \"vendorCode\", ms_vendor.vendor_name as \"vendorName\", default_vendor as \"priority\" " + 
				"from ms_vendoroftenant join ms_tenant on ms_vendoroftenant.id_ms_tenant = ms_tenant.id_ms_tenant join ms_vendor ON ms_vendoroftenant.id_ms_vendor = ms_vendor.id_ms_vendor " +
				"join ms_lov on ms_vendor.lov_vendor_type = ms_lov.id_lov " + 
				"where ms_tenant.tenant_code = :tenantCode and ms_lov.code = :lovCode and default_vendor is not null order by default_vendor asc ");
		
		
		return this.managerDAO.selectForListString(ListPsrePriorityBean.class, query.toString(), params, null);
	}

	@Override
	public void updateVendoroftenant(MsVendoroftenant updVot) {
		updVot.setUsrCrt(MssTool.maskData(updVot.getUsrCrt()));
		this.managerDAO.update(updVot);
	}

	@Override
	public MsVendor getVendorMGByVendorCode(String vendorCode)  {
		

		return this.managerDAO.selectOne(
				"from MsVendor mv "
				+ "join fetch mv.msLovVendorType mlvt "
				+ "where mv.vendorCode = :vendorCode and mlvt.lovGroup = 'VENDOR_TYPE' and mlvt.code ='MG' ", 
	new Object[][] {{ MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode) }});
	}

	@Override
	public MsVendor getVendorByTenantCodeAndVendorCode(String vendorCode, String tenantCode) {
	    Map<String, Object> params = new HashMap<>();
	    params.put(GlobalVal.CONST_TENANT_CODE, StringUtils.upperCase(tenantCode));
	    params.put(GlobalVal.CONST_VENDOR_CODE, StringUtils.upperCase(vendorCode));
	    
	    StringBuilder query = new StringBuilder()
				.append("select id_ms_vendoroftenant ")
				.append("from ms_vendoroftenant mvt ")
				.append("join ms_vendor mv on mv.id_ms_vendor = mvt.id_ms_vendor ")
				.append("join ms_tenant mt on mt.id_ms_tenant = mvt.id_ms_tenant ")
				.append("where mv.is_active = '1' and mt.tenant_code = :tenantCode and mv.vendor_code = :vendorCode ");

		BigInteger idMsVendorOfTenant = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		
		if (null == idMsVendorOfTenant) {
			return null;
		} else {
			long idVendorOfTenant = idMsVendorOfTenant.longValue();
			MsVendoroftenant vendorOfTenant = this.managerDAO.selectOne(MsVendoroftenant.class,
					new Object[][] {{ Restrictions.eq("idMsVendoroftenant", idVendorOfTenant) }});
			return vendorOfTenant.getMsVendor();
		}

	}


}