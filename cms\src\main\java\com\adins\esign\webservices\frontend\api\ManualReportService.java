package com.adins.esign.webservices.frontend.api;

import com.adins.esign.webservices.model.AddManualReportRequest;
import com.adins.esign.webservices.model.DeleteManualReportRequest;
import com.adins.esign.webservices.model.DownloadManualReportRequest;
import com.adins.esign.webservices.model.DownloadManualReportResponse;
import com.adins.esign.webservices.model.GetListManualReportRequest;
import com.adins.esign.webservices.model.GetListManualReportResponse;

import com.adins.framework.service.base.model.MssResponseType;
import com.adins.esign.webservices.model.GetListReportRequest;
import com.adins.esign.webservices.model.GetListReportResponse;

public interface ManualReportService {
	GetListManualReportResponse getList(GetListManualReportRequest request);
	MssResponseType saveManualReport (AddManualReportRequest request); 
	DownloadManualReportResponse download(DownloadManualReportRequest request);
	GetListReportResponse getListForDownload(GetListReportRequest request);
	MssResponseType deleteManualReport(DeleteManualReportRequest request);
}
