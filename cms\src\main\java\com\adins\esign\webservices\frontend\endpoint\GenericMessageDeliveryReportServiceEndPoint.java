package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.MessageDeliveryReportLogic;
import com.adins.esign.webservices.frontend.api.MessageDeliveryReportService;
import com.adins.esign.webservices.model.GetListDeliveryReportForMessageCheckingRequest;
import com.adins.esign.webservices.model.GetListDeliveryReportForMessageCheckingResponse;
import com.adins.esign.webservices.model.GetListMessageDeliveryReportRequest;
import com.adins.esign.webservices.model.GetListMessageDeliveryReportResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/messageDelivery")
@Api(value = "MessageDeliveryReportService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericMessageDeliveryReportServiceEndPoint implements MessageDeliveryReportService {
	@Autowired MessageDeliveryReportLogic messageDeliveryReportLogic;

	@Override
	@POST
	@Path("/s/listMessageDelivery")
	public GetListMessageDeliveryReportResponse listMessageDeliveryReport(GetListMessageDeliveryReportRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return messageDeliveryReportLogic.getListMessageDeliveryReport(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/listDeliveryReportForMessageChecking")
	public GetListDeliveryReportForMessageCheckingResponse listDeliveryReportForMessageChecking(GetListDeliveryReportForMessageCheckingRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return messageDeliveryReportLogic.getListDeliveryReportForMessageChecking(request, audit);
	}
}
