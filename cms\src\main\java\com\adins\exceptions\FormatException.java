package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class FormatException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonFormat {
		INVALID_DATE_FORMAT,
		INVALID_FORMAT,
		UNKNOWN
	}
	
	private final ReasonFormat reason;
	
	public FormatException(ReasonFormat reason) {
		this.reason = reason;
	}
	
	public FormatException(String message, ReasonFormat reason) {
		super(message);
		this.reason = reason;
	}
	
	public FormatException(Throwable ex, ReasonFormat reason) {
		super(ex);
		this.reason = reason;
	}
	
	public FormatException(String message, Throwable ex, ReasonFormat reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	public ReasonFormat getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (null != this.reason) {
			switch (reason) {
			case INVALID_DATE_FORMAT:
				return StatusCode.INVALID_FORMAT;
			case INVALID_FORMAT:
				return StatusCode.INVALID_FORMAT;
			case UNKNOWN:
				return StatusCode.UNKNOWN;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}
	
	

}
