package com.adins.esign.businesslogic.api.interfacing;

import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface WhatsAppHalosisLogic {
	/**
	 * Send WhatsApp asynchronously.<br>
	 * Insert balance mutation with <code>Propagation.REQUIRES_NEW</code> transaction.<br>
	 * If error occurred with database session, may be needed to get table object with <code>Propagation.REQUIRES_NEW</code> transaction.
	 */
	void sendMessage(HalosisSendWhatsAppRequestBean request, AuditContext audit);
	
	void sendMessage(HalosisSendWhatsAppRequestBean request, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit);

	
	/**
	 * Send WhatsApp synchronously.<br>
	 * Insert balance mutation with default transaction. (commit when API response is returned)<br>
	 */
	boolean sendSynchronousMessage(HalosisSendWhatsAppRequestBean request, AuditContext audit);
	
	boolean sendSynchronousMessage(HalosisSendWhatsAppRequestBean request, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit);
}
