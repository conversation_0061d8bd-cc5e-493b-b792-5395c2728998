package com.adins.esign.businesslogic.impl.interfacing;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.mail.MessagingException;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.ContentDisposition;
import org.apache.cxf.jaxrs.ext.multipart.MultipartBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.businesslogic.api.interfacing.EmeteraiRkgLogic;
import com.adins.esign.confins.model.DocumentToUploadBean;
import com.adins.esign.confins.model.UploadToCoreBean;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.ActivationDocumentBean;
import com.adins.esign.model.custom.CreateOrderEmeteraiRkgRequestBean;
import com.adins.esign.model.custom.CreateOrderEmeteraiRkgResponseBean;
import com.adins.esign.model.custom.DataReturnBean;
import com.adins.esign.model.custom.DownloadStampedDocResponse;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.EmeteraiRkgResponseHeaderBean;
import com.adins.esign.model.custom.SignLocationBean;
import com.adins.esign.model.custom.UploadDocumentEmeteraiRkgResponseBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.BalanceRequest;
import com.adins.esign.webservices.model.BalanceResponse;
import com.adins.esign.webservices.model.DownloadStampDocumentRkgRequest;
import com.adins.esign.webservices.model.DownloadStampDocumentRkgResponse;
import com.adins.esign.webservices.model.GenerateEmeteraiRkgRequest;
import com.adins.esign.webservices.model.GenerateEmeteraiRkgResponse;
import com.adins.esign.webservices.model.StampingEmeteraiRkgRequest;
import com.adins.esign.webservices.model.StampingEmeteraiRkgResponse;
import com.adins.esign.webservices.model.SubmitDocumentRkgRequest;
import com.adins.esign.webservices.model.SubmitDocumentRkgResponse;
import com.adins.esign.webservices.model.UpdateParamsRkgRequest;
import com.adins.esign.webservices.model.UpdateParamsRkgResponse;
import com.adins.esign.webservices.model.UpdateStampDutyStatusResponse;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.exceptions.EmailException;
import com.adins.exceptions.EmailException.ReasonEmail;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.google.gson.Gson;

@Transactional
@Component
public class GenericEmeteraiRkgLogic extends BaseLogic implements EmeteraiRkgLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericEmeteraiRkgLogic.class);
	
    private String headerAuth = javax.ws.rs.core.HttpHeaders.AUTHORIZATION;
    private String headerContentType = javax.ws.rs.core.HttpHeaders.CONTENT_TYPE;
    private String headerJson = MediaType.APPLICATION_JSON;
    private String headerPdf = org.springframework.http.MediaType.APPLICATION_PDF_VALUE;
    private String headerMultipart = javax.ws.rs.core.MediaType.MULTIPART_FORM_DATA;
    private String headerTextPlain = MediaType.TEXT_PLAIN;
    
    private static final String CONST_BEARER = "Bearer ";

	@Value("${emeterai.rkg.auth.token}") private String authToken;
	@Value("${emeterai.rkg.uri}") private String urlEmeterai;
	@Value("${emeterai.rkg.uploaddocument.uri}") private String urlUploadDocument;
	@Value("${emeterai.rkg.createorder.uri}") private String urlCreateOrder;
	@Value("${emeterai.rkg.stampingorder.uri}") private String urlStampingOrder;
	@Value("${emeterai.rkg.download.uri}") private String urlDownload;
	@Value("${emeterai.rkg.params.uri}") private String urlUpdateParams;
	@Value("${emeterai.rkg.submit.uri}") private String urlSubmit;
	@Value("${emeterai.rkg.generate.uri}") private String urlGenerate;
	@Value("${emeterai.rkg.complete.uri}") private String urlComplete;
	
	@Value("${spring.mail.username}") private String fromEmailAddr;
	
	@Autowired private Gson gson;
	@Autowired private DigisignLogic digisignLogic;
	@Autowired private DocumentLogic documentLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private MessageTemplateLogic messageTemplateLogic;
	@Autowired private EmailSenderLogic emailSenderLogic;
    
	@Override
	public UpdateStampDutyStatusResponse attachMeteraiRkg(TrDocumentH documentH, AuditContext audit)
			throws IOException {
		this.updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_PROCESS, null, audit);
		
		List<TrDocumentD> listDocumentD = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderId(documentH.getIdDocumentH());
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(listDocumentD.get(0).getMsTenant(), listDocumentD.get(0).getMsVendor());
		
		boolean isAllDocumentFullyStamped = true;
		
		for (TrDocumentD documentD: listDocumentD) {
			if (null == documentD.getTotalMaterai() || null == documentD.getTotalStamping()) {
				continue;
			}
			
			if (0 == documentD.getTotalMaterai()) {
				this.updateStepAttachMeterai(documentD, GlobalVal.STEP_ATTACH_METERAI_NOT_SDT, audit);
				continue;
			}
			
			if (documentD.getTotalStamping() < documentD.getTotalMaterai()) {
				isAllDocumentFullyStamped = false;
				int count = documentD.getTotalStamping();
				boolean isError = false;
				
				while(count < documentD.getTotalMaterai() && !isError) {
					String stepMeterai = documentD.getSdtProcess();
					
					try {
						switch (stepMeterai) {
							case GlobalVal.STEP_ATTACH_METERAI_NOT_STR:
							case GlobalVal.STEP_ATTACH_METERAI_SDT_STR:
							case GlobalVal.STEP_ATTACH_METERAI_UPL_DOC:
								UploadDocumentEmeteraiRkgResponseBean uploadDocBean = this.uploadDocumentEmeteraiRkg(documentD, vot, audit);
								if (!uploadDocBean.getMeta().getSuccess()) {
									isError = true;
								}
								break;
								
							case GlobalVal.STEP_ATTACH_METERAI_CRT_ORD:
								CreateOrderEmeteraiRkgResponseBean createOrderBean = this.createOrderEmeteraiRkg(documentD, audit);
								if (!createOrderBean.getMeta().getSuccess()) {
									isError = true;
								}
								break;
								
							case GlobalVal.STEP_ATTACH_METERAI_UPD_DOC:
								UpdateParamsRkgResponse updateparamsBean = this.updateParams(documentD, count, audit);
								if (!updateparamsBean.getMeta().getSuccess()) {
									isError = true;
									break;
								}
								SubmitDocumentRkgResponse submitDocumentBean = this.submitDocument(documentD, count, audit);
								if (!submitDocumentBean.getMeta().getSuccess()) {
									isError = true;
								}
								break;
								
							case GlobalVal.STEP_ATTACH_METERAI_GEN_SDT:
								GenerateEmeteraiRkgResponse genEmeteraiBean = this.generateEmeterai(documentH, documentD, count, audit);
								if (!genEmeteraiBean.getMeta().getSuccess()) {
									isError = true;
								}
								break;
								
							case GlobalVal.STEP_ATTACH_METERAI_STM_SDT:
								StampingEmeteraiRkgResponse stampingEmeteraiBean = this.stampingEmeterai(documentH, documentD, count, audit);
								if (!stampingEmeteraiBean.getMeta().getSuccess()) {
									isError = true;
								}
								break;
								
							default:
								break;
						}
						
						if (isError) {
							this.updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_ERROR, null, audit);
							return new UpdateStampDutyStatusResponse();
						}
						
					} catch (Exception e) {
						this.updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_ERROR, e, audit);
						return new UpdateStampDutyStatusResponse();
					}
					
					count++;
				}
			}
		}
		
		if (isAllDocumentFullyStamped) {
			try {
				Status status = this.uploadFullySignedDocument(documentH, audit);
				if (200 != status.getCode()) {
					this.updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_ERROR, null, audit);
				}
							
				return new UpdateStampDutyStatusResponse();
			} catch (Exception e) {
				this.updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_ERROR, e, audit);
				return new UpdateStampDutyStatusResponse();
			}
		}
		
		return this.attachMeteraiRkg(documentH, audit);
	}
	
	private UploadDocumentEmeteraiRkgResponseBean uploadDocumentEmeteraiRkg(TrDocumentD documentD, MsVendoroftenant vot, 
			AuditContext audit) throws IOException {
		UploadDocumentEmeteraiRkgResponseBean response = new UploadDocumentEmeteraiRkgResponseBean();
		EmeteraiRkgResponseHeaderBean meta = new EmeteraiRkgResponseHeaderBean();
		this.updateStepAttachMeterai(documentD, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
		String pdfBase64 = "";
		String transactionId = documentD.getTransactionId();
		String[] transactionIds = {};
		if (StringUtils.isNotBlank(transactionId)) {
			transactionIds = transactionId.split(";");
		}
		
		if(transactionIds.length >= documentD.getTotalMaterai()) {
			meta.setSuccess(true);
			response.setMeta(meta);
			return response;
		}
		
		if (transactionIds.length > 0) {
			DownloadStampedDocResponse downloadStampedDocResponse = this.downloadStampedDocument(transactionId, audit);
			
			if (!GlobalVal.STATUS_MCP_SUCCESS.equals(downloadStampedDocResponse.getResponseCode())) {
				meta.setSuccess(false);
				response.setMeta(meta);
				return response;
			}
			pdfBase64 = downloadStampedDocResponse.getDataReturn().getPdfFile();
			
		} else {
			ViewDocumentRequest viewDocRequest = new ViewDocumentRequest();
			viewDocRequest.setDocumentId(documentD.getDocumentId());
			ViewDocumentResponse viewDocResponse = new ViewDocumentResponse();
			try {
				viewDocResponse = digisignLogic.getDocumentFileDigisign(documentD, vot.getToken(), audit);
			} catch (Exception e) {
				meta.setSuccess(false);
				response.setMeta(meta);
				return response;
			}
			pdfBase64 = viewDocResponse.getPdfBase64();
		}
		
		if (StringUtils.isNotBlank(pdfBase64)) {
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(headerContentType, headerMultipart);
			mapHeader.add(headerAuth, CONST_BEARER + authToken);
			
			WebClient client = WebClient.create(urlEmeterai + urlUploadDocument).headers(mapHeader);
			
			List<Attachment> atts = new LinkedList<>();
			
			String fileName = documentD.getDocumentId() + ".pdf";
			ContentDisposition cdFile = new ContentDisposition("form-data; name=\"file\"; filename=\""+fileName+"\"");
			byte[] dataImagePhoto = Base64.getDecoder().decode(pdfBase64);
			atts.add(new Attachment("file", new ByteArrayInputStream(dataImagePhoto), cdFile));
			
			ContentDisposition cdDesc = new ContentDisposition("form-data; name=\"description\"");
			atts.add(new Attachment("description", new ByteArrayInputStream(documentD.getMsDocTemplate().getDocTemplateName().getBytes()), 
					cdDesc));
			
			MultipartBody body = new MultipartBody(atts);
			Response clientResponse = null;
			try {
				clientResponse = client.post(body);
			} catch (Exception e) {
				meta.setSuccess(false);
				response.setMeta(meta);
				return response;
			}
			
			String result = StringUtils.EMPTY;
			try {
				InputStreamReader isReader = new InputStreamReader((InputStream) clientResponse.getEntity());
				result = IOUtils.toString(isReader);
			} catch (IOException e) {
				meta.setSuccess(false);
				response.setMeta(meta);
				LOG.error("Error on upload document e-meterai RKG result");
				return response;
			}
			LOG.info("Upload document e-meterai RKG result: {}", result);
			
			try {
				response = gson.fromJson(result, UploadDocumentEmeteraiRkgResponseBean.class);
			} catch (Exception e) {
				meta.setSuccess(false);
				response.setMeta(meta);
				return response;
			}
			
			if (response.getMeta().getSuccess()) {
				String updatedDocumentIdSdt;
				if (StringUtils.isNotBlank(documentD.getDocumentIdSdt())) {
					updatedDocumentIdSdt = documentD.getDocumentIdSdt() + ";" + response.getData().getDocumentId();
				} else {
					updatedDocumentIdSdt = response.getData().getDocumentId();
				}
				LOG.info("Updated Document Id Sdt {}", updatedDocumentIdSdt);
				
				documentD.setUsrUpd(audit.getCallerId());
				documentD.setDtmUpd(new Date());
				documentD.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_CRT_ORD);
				documentD.setDocumentIdSdt(updatedDocumentIdSdt);
				daoFactory.getDocumentDao().updateDocumentDetail(documentD);
			}
			
		}
		
		return response;
	}
	
	private CreateOrderEmeteraiRkgResponseBean createOrderEmeteraiRkg(TrDocumentD documentD, AuditContext audit) {
		CreateOrderEmeteraiRkgResponseBean response = new CreateOrderEmeteraiRkgResponseBean();
		EmeteraiRkgResponseHeaderBean meta = new EmeteraiRkgResponseHeaderBean();
		
		String documentIdSdt = documentD.getDocumentIdSdt();
		String[] documentIdSdts = {};
		if (StringUtils.isNotBlank(documentIdSdt)) {
			documentIdSdts = documentIdSdt.split(";");
		}
		
		String transactionId = documentD.getTransactionId();
		String[] transactionIds = {};
		if (StringUtils.isNotBlank(transactionId)) {
			transactionIds = transactionId.split(";");
		}
		
		if(transactionIds.length >= documentIdSdts.length) {
			meta.setSuccess(true);
			response.setMeta(meta);
			return response;
		}
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerContentType, headerJson);
		mapHeader.add(headerAuth, CONST_BEARER + authToken);
		WebClient client = WebClient.create(urlEmeterai + urlCreateOrder).headers(mapHeader);
		
		CreateOrderEmeteraiRkgRequestBean request = new CreateOrderEmeteraiRkgRequestBean();
		request.setDocumentId(documentIdSdts[transactionIds.length]);
		
		String jsonRequest = gson.toJson(request);
		LOG.info("JSON request create order e-meterai RKG : {}", jsonRequest);
		Response clientResponse = null;
		try {
			clientResponse = client.post(jsonRequest);
		} catch (Exception e) {
			meta.setSuccess(false);
			response.setMeta(meta);
			return response;
		}
		
		String result = StringUtils.EMPTY;
		try {
			InputStreamReader isReader = new InputStreamReader((InputStream) clientResponse.getEntity());
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			meta.setSuccess(false);
			response.setMeta(meta);
			LOG.error("Error on create order e-meterai RKG result");
			return response;
		}
		LOG.info("Create order e-meterai RKG result: {}", result);
		
		try {
			response = gson.fromJson(result, CreateOrderEmeteraiRkgResponseBean.class);
		} catch (Exception e) {
			meta.setSuccess(false);
			response.setMeta(meta);
			return response;
		}
		
		if (response.getMeta().getSuccess()) {
			String updatedTransactionId;
			if (StringUtils.isNotBlank(transactionId)) {
				updatedTransactionId = transactionId + ";" + response.getData().getStampingOrderId();
			} else {
				updatedTransactionId = response.getData().getStampingOrderId();
			}
			LOG.info("Updated Transaction Id {}", updatedTransactionId);
			
			documentD.setUsrUpd(audit.getCallerId());
			documentD.setDtmUpd(new Date());
			documentD.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_UPD_DOC);
			documentD.setTransactionId(updatedTransactionId);
			daoFactory.getDocumentDao().updateDocumentDetail(documentD);
		}
		
		return response;
	}
	
	private Status uploadFullySignedDocument(TrDocumentH docH, AuditContext audit) throws IOException {
		UploadToCoreBean bean = new UploadToCoreBean();
		try {
			bean = prepareUploadToCoreData(docH, audit);
		} catch (Exception e) {
			this.updateStatusProcessMeterai(docH, GlobalVal.STATUS_ATTACH_METERAI_ERROR, e, audit);
			throw e;
		}
		
		Status status = documentLogic.callUrlUpload(docH.getUrlUpload(), bean);
		
		if (status.getCode() == 200) {
			this.updateStatusProcessMeterai(docH, GlobalVal.STATUS_ATTACH_METERAI_FINISHED, null, audit);
		}
		
		return status;
	}
	
	private DownloadStampedDocResponse downloadStampedDocument(String rawStampingOrder, AuditContext audit) throws IOException {
		DownloadStampedDocResponse responseResult = new DownloadStampedDocResponse();
		String[] stampingOrderArray = rawStampingOrder.split(";");
		int lastSOindex = stampingOrderArray.length - 1;
		
		DownloadStampDocumentRkgRequest rkgRequest = new DownloadStampDocumentRkgRequest();
		rkgRequest.setTeraOrderId(stampingOrderArray[lastSOindex]);
		String rkgRequestString = gson.toJson(rkgRequest);
		LOG.info("JSON request download document e-meterai : {}", rkgRequestString);
		
		String url = urlEmeterai + urlStampingOrder + "/" + stampingOrderArray[lastSOindex] + urlDownload;
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerContentType, headerTextPlain);
		mapHeader.add(headerAuth, CONST_BEARER + authToken);
		WebClient client = WebClient.create(url).headers(mapHeader);
		
		Response response = null;
		try {
			response = client.get();
		} catch (Exception e) {
			responseResult.setResponseCode("200");
			return responseResult;
		}
		
		MultivaluedMap<String, Object> responseHeader = response.getHeaders();
		String responseContentType = responseHeader.get(headerContentType).toString();
		responseContentType = responseContentType.substring(1, responseContentType.length() - 1);
		
		if (headerPdf.equalsIgnoreCase(responseContentType)) {
			InputStream is = (InputStream) response.getEntity();
			String base64String;
			try {
				byte[] pdfBytes = org.apache.commons.io.IOUtils.toByteArray(is);
				base64String = Base64.getEncoder().encodeToString(pdfBytes);
				responseResult.setResponseCode(GlobalVal.STATUS_MCP_SUCCESS);
				DataReturnBean bean = new DataReturnBean();
				bean.setPdfFile(base64String);
				responseResult.setDataReturn(bean);
			} catch (Exception e) {
				responseResult.setResponseCode("200");
				return responseResult;
			}
		} else {
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			
			String result = StringUtils.EMPTY;
			try {
				result = IOUtils.toString(isReader);
			} catch (IOException e) {
				responseResult.setMessage(responseResult.getMessage());
				responseResult.setResponseCode("200");
				return responseResult;
			}
			LOG.info("JSON result download document e-meterai : {}", result);
			
			try {
				DownloadStampDocumentRkgResponse rkgResponse = gson.fromJson(result, DownloadStampDocumentRkgResponse.class);
				responseResult.setMessage(rkgResponse.getError());
				responseResult.setResponseCode("200");
			} catch (Exception e) {
				responseResult.setMessage(responseResult.getMessage());
				responseResult.setResponseCode("200");
				return responseResult;
			}
		}
		
		if (!GlobalVal.STATUS_MCP_SUCCESS.equals(responseResult.getResponseCode())) {
			responseResult.setMessage(responseResult.getMessage());
			responseResult.setResponseCode(responseResult.getResponseCode());
			return responseResult;
		}
		
		return responseResult;
	}
	
	private UploadToCoreBean prepareUploadToCoreData(TrDocumentH docH, AuditContext audit) throws IOException {
		List<DocumentToUploadBean> listUpload = new ArrayList<>();
		List<ActivationDocumentBean> listDocD = daoFactory.getDocumentDao().getActivationDocumentByDocHAndUser(docH, null);
		for (ActivationDocumentBean bean : listDocD) {
			DocumentToUploadBean uploadBean = new DocumentToUploadBean();
			uploadBean.setDocTypeTc(bean.getDocumentTemplateCode());
			uploadBean.setDisplayName(bean.getDocTemplateName());
			
			if (bean.getTotalMeterai() > 0) {
				DownloadStampedDocResponse response = this.downloadStampedDocument(bean.getTransactionIds(), audit);
				uploadBean.setContent(response.getDataReturn().getPdfFile());
			} else {
				TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(bean.getDocumentId());
				ViewDocumentResponse response = digisignLogic.getDocumentFile(document, audit);
				uploadBean.setContent(response.getPdfBase64());
			}
			
			String filename = GlobalVal.PREFIX_DOCUMENT_FILE_NAME + bean.getDocumentId() + ".pdf";
			uploadBean.setFileName(filename);
			
			listUpload.add(uploadBean);
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(bean.getDocumentId());
			this.updateStepAttachMeterai(docD, GlobalVal.STEP_ATTACH_METERAI_UPL_CON, audit);
		}
		
		UploadToCoreBean bean = new UploadToCoreBean();
		bean.setDocumentObjs(listUpload);
		bean.setRefNo(docH.getRefNumber());
		
		return bean;
	}
	
	private String updateStatusProcessMeterai(TrDocumentH documentH, String statusAttachMeterai, Exception e, AuditContext audit) {
		if (GlobalVal.STATUS_ATTACH_METERAI_ERROR.equals(statusAttachMeterai))
			LOG.error("Error on attach meterai");
		if (null != e) {
			LOG.error(e.getLocalizedMessage());
			e.printStackTrace();
		}

		documentH.setProsesMaterai(new Short(statusAttachMeterai));
		documentH.setDtmUpd(new Date());
		documentH.setUsrUpd(audit.getCallerId());
		daoFactory.getDocumentDao().updateDocumentH(documentH);

		return statusAttachMeterai;
	}
	
	private String updateStepAttachMeterai(TrDocumentD documentD, String step, AuditContext audit) {
		documentD.setDtmUpd(new Date());
		documentD.setUsrUpd(audit.getCallerId());
		documentD.setSdtProcess(step);
		daoFactory.getDocumentDao().updateDocumentDetail(documentD);
		
		return step;
	}
	
	private UpdateParamsRkgResponse updateParams(TrDocumentD document, Integer currentLoop, AuditContext audit) {
		
		UpdateParamsRkgResponse response = new UpdateParamsRkgResponse();
		EmeteraiRkgResponseHeaderBean meta = new EmeteraiRkgResponseHeaderBean();
		
		if (StringUtils.isBlank(document.getDocumentIdSdt())) {
			meta.setSuccess(false);
			meta.setMessage("RKG Document ID is empty");
			response.setMeta(meta);
			return response;
		}
		
		String documentDate = MssTool.formatDateToStringIn(document.getRequestDate(), GlobalVal.RKG_DATE_FORMAT);
		MsOffice office = document.getTrDocumentH().getMsOffice();
		List<MsDocTemplateSignLoc> sdtLocs = daoFactory.getDocumentDao()
				.getListSignLocationByTemplateCodeAndLovSignType(document.getMsDocTemplate().getDocTemplateCode(), GlobalVal.CODE_LOV_SIGN_TYPE_SDT);
		
		MsDocTemplateSignLoc sdtLoc = sdtLocs.get(currentLoop);
		SignLocationBean coordinate = gson.fromJson(sdtLoc.getSignLocation(), SignLocationBean.class);
		
		UpdateParamsRkgRequest request = new UpdateParamsRkgRequest();
		request.setCertificateLevel(GlobalVal.RKG_CERTIF_LVL_NOT_CERTIFIED);
		request.setDocumentType(GlobalVal.RKG_DOC_TYPE_PERJANJIAN);
		request.setDocumentDate(documentDate);
		request.setDocumentNo(document.getDocumentId());
		request.setDocumentValue("10000");
		request.setVisualSignPage(sdtLoc.getSignPage());
		request.setLocation(office.getOfficeName());
		request.setReason(GlobalVal.RKG_REASON_APPROVAL);
		request.setDocumentPassword("");
		request.setVisualLlx(Double.valueOf(coordinate.getLlx()));
		request.setVisualLly(Double.valueOf(coordinate.getLly()));
		request.setVisualUrx(Double.valueOf(coordinate.getUrx()));
		request.setVisualUry(Double.valueOf(coordinate.getUry()));
		
		String[] transactionIds = document.getTransactionId().split(";");
		
		return updateParamsRkg(request, transactionIds[currentLoop]);
	}
	
	private UpdateParamsRkgResponse updateParamsRkg(UpdateParamsRkgRequest request, String rkgDocumentId) {
		
		UpdateParamsRkgResponse updateResponse = new UpdateParamsRkgResponse();
		EmeteraiRkgResponseHeaderBean meta = new EmeteraiRkgResponseHeaderBean();
		
		String url = urlEmeterai + urlStampingOrder + "/" + rkgDocumentId + urlUpdateParams;
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerContentType, headerJson);
		mapHeader.add(headerAuth, CONST_BEARER + authToken);
		WebClient client = WebClient.create(url).headers(mapHeader);
		
		String updateParamsRequestJson = gson.toJson(request);
		LOG.info("RKG Update Params request: {}", updateParamsRequestJson);
		
		Response response = null;
		try {
			response = client.put(updateParamsRequestJson);
		} catch (Exception e) {
			meta.setSuccess(false);
			updateResponse.setMeta(meta);
			return updateResponse;
		}
		
		String result = StringUtils.EMPTY;
		try {
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			meta.setSuccess(false);
			updateResponse.setMeta(meta);
			LOG.error("Error on update params e-meterai RKG result");
			return updateResponse;
		}
		
		LOG.info("RKG Update Params Response: {}", result);
		updateResponse = gson.fromJson(result, UpdateParamsRkgResponse.class);
		
		return updateResponse;
	}
	
	private SubmitDocumentRkgResponse submitDocument(TrDocumentD document, int currentLoop, AuditContext audit) {
		
		SubmitDocumentRkgResponse submitResponse = new SubmitDocumentRkgResponse();
		EmeteraiRkgResponseHeaderBean meta = new EmeteraiRkgResponseHeaderBean();
		
		if (StringUtils.isBlank(document.getDocumentIdSdt())) {
			meta.setSuccess(false);
			submitResponse.setMeta(meta);
			return submitResponse;
		}
		
		String[] transactionIds = document.getTransactionId().split(";");
		
		SubmitDocumentRkgResponse response = this.submitDocumentRkg(transactionIds[currentLoop]);
		if (response.getMeta().getSuccess()) {
			this.updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_GEN_SDT, audit);
		}
		return response;
	}

	private SubmitDocumentRkgResponse submitDocumentRkg(String rkgDocumentId) {
		SubmitDocumentRkgResponse submitResponse = new SubmitDocumentRkgResponse();
		EmeteraiRkgResponseHeaderBean meta = new EmeteraiRkgResponseHeaderBean();
		
		String url = urlEmeterai + urlStampingOrder + "/" + rkgDocumentId + urlSubmit;
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerAuth, CONST_BEARER + authToken);
		WebClient client = WebClient.create(url).headers(mapHeader);
		
		
		SubmitDocumentRkgRequest request = new SubmitDocumentRkgRequest();
		request.setTeraOrderId(rkgDocumentId);
		String requestJson = gson.toJson(request);
		LOG.info("RKG Submit Document Request: {}", requestJson);
		
		Response response = null;
		try {
			response = client.put(requestJson);
		} catch (Exception e) {
			meta.setSuccess(false);
			submitResponse.setMeta(meta);
			return submitResponse;
		}
		
		String result = StringUtils.EMPTY;
		try {
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			meta.setSuccess(false);
			submitResponse.setMeta(meta);
			LOG.error("Error on getting submit document RKG result");
			return submitResponse;
		}
		
		LOG.info("RKG Submit Document Response: {}", result);
		submitResponse = gson.fromJson(result, SubmitDocumentRkgResponse.class);
		
		return submitResponse;
	}
	
	private GenerateEmeteraiRkgResponse generateEmeterai(TrDocumentH documentH, TrDocumentD document, int currentLoop, AuditContext audit) {
		GenerateEmeteraiRkgResponse response = new GenerateEmeteraiRkgResponse();
		EmeteraiRkgResponseHeaderBean meta = new EmeteraiRkgResponseHeaderBean();
		
		if (StringUtils.isBlank(document.getDocumentIdSdt())) {
			meta.setSuccess(false);
			meta.setMessage("Empty Document Id Sdt");
			response.setMeta(meta);
			return response;
		}
		
		MsTenant tenant = document.getMsTenant();
		MsVendor vendorSdt = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		
		BalanceRequest balanceRequest = new BalanceRequest();
		balanceRequest.setTenantCode(tenant.getTenantCode());
		balanceRequest.setVendorCode(vendorSdt.getVendorCode());
		balanceRequest.setBalanceType(GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		
		String[] transactionIds = document.getTransactionId().split(";");
		int availableSdt = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
		
		BalanceResponse balanceResponse = saldoLogic.getBalanceNotSecure(balanceRequest, audit);
		
		int sdtBalance = balanceResponse.getListBalance().get(0).getCurrentBalance().intValue();
		int sdtNeeded = transactionIds.length - availableSdt;
		if (sdtBalance < sdtNeeded) {
			LOG.warn("Insufficient balance to generate E-Meterai");
			
			String[] recipient = tenant.getEmailReminderDest().split(",");
			
			this.sendInsufficientSdtBalanceEmail("generate e-Meterai", documentH.getRefNumber(), document.getMsDocTemplate().getDocTemplateName(),
					balanceType.getDescription(), sdtBalance, recipient);
			
			documentH.setProsesMaterai((short) 2);
			documentH.setUsrUpd(audit.getCallerId());
			documentH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentH(documentH);
			
			meta.setSuccess(false);
			response.setMeta(meta);
			
			return response;
		}
		
		response = this.generateEmeteraiRkg(transactionIds[currentLoop]);
		if (!response.getMeta().getSuccess()) {
			return response;
		}
		
		this.updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_STM_SDT, audit);
		
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_AVAILABLE);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT);
		
		// Stamp duty fee hardcoded 10.000
		TrStampDuty sdt = new TrStampDuty();
		sdt.setStampDutyNo(response.getData().getSerialNumber());
		sdt.setTrxNo(trxNo);
		sdt.setMsLov(sdtStatus);
		sdt.setMsTenant(tenant);
		sdt.setMsVendor(vendorSdt);
		sdt.setStampDutyFee(10000);
		sdt.setUsrCrt(audit.getCallerId());
		sdt.setDtmCrt(new Date());
		daoFactory.getStampDutyDao().insertTrStampDuty(sdt);
		
		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(trxNo);
		mutation.setTrxDate(new Date());
		mutation.setRefNo(documentH.getRefNumber());
		mutation.setQty(0);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsTenant(tenant);
		mutation.setMsVendor(vendorSdt);
		mutation.setTrDocumentD(document);
		mutation.setTrDocumentH(documentH);
		mutation.setNotes(sdt.getStampDutyNo());
		mutation.setTrStampDuty(sdt);
		mutation.setUsrCrt(audit.getCallerId());
		mutation.setDtmCrt(new Date());
		daoFactory.getBalanceMutationDao().insertTrBalanceMutation(mutation);
		
		return response;
	}
	
	private GenerateEmeteraiRkgResponse generateEmeteraiRkg(String rkgDocumentId) {
		GenerateEmeteraiRkgResponse generateResponse = new GenerateEmeteraiRkgResponse();
		EmeteraiRkgResponseHeaderBean meta = new EmeteraiRkgResponseHeaderBean();
		
		String url = urlEmeterai + urlStampingOrder + "/" + rkgDocumentId + urlGenerate;
		GenerateEmeteraiRkgRequest request = new GenerateEmeteraiRkgRequest();
		request.setTeraOrderId(rkgDocumentId);
		String jsonRequest = gson.toJson(request);
		LOG.info("RKG Generate Emeterai Request: {}", jsonRequest);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerAuth, CONST_BEARER + authToken);
		WebClient client = WebClient.create(url).headers(mapHeader);
		
		Response response = null;
		try {
			response = client.put(jsonRequest);
		} catch (Exception e) {
			meta.setSuccess(false);
			generateResponse.setMeta(meta);
			return generateResponse;
		}
		
		String result = StringUtils.EMPTY;
		try {
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			meta.setSuccess(false);
			generateResponse.setMeta(meta);
			LOG.error("Error on getting generate e-Meterai RKG result");
			return generateResponse;
		}
		
		LOG.info("RKG Generate E-Meterai Response: {}", result);
		generateResponse = gson.fromJson(result, GenerateEmeteraiRkgResponse.class);
		return generateResponse;
	}
	
	private StampingEmeteraiRkgResponse stampingEmeterai(TrDocumentH documentH, TrDocumentD document, int currentLoop, AuditContext audit) {
		StampingEmeteraiRkgResponse response = new StampingEmeteraiRkgResponse();
		EmeteraiRkgResponseHeaderBean meta = new EmeteraiRkgResponseHeaderBean();
		
		if (StringUtils.isBlank(document.getDocumentIdSdt())) {
			meta.setSuccess(false);
			meta.setMessage("Empty Document Id Sdt");
			response.setMeta(meta);
			return response;
		}
		
		List<Map<String, Object>> stampDutyIds = daoFactory.getStampDutyDao().getIdStampDutyForDocument(document.getIdDocumentD());
		BigInteger idStampDuty = (BigInteger) stampDutyIds.get(currentLoop).get("d0");
		TrStampDuty sdt = daoFactory.getStampDutyDao().getStampDutyById(idStampDuty.longValue());
		if (!GlobalVal.CODE_LOV_SDT_AVAILABLE.equals(sdt.getMsLov().getCode())) {
			LOG.info("SDT with number {} is not available.", sdt.getStampDutyNo());
			meta.setSuccess(false);
			meta.setMessage("SDT with number " + sdt.getStampDutyNo() + " is not available.");
			response.setMeta(meta);
			return response;
		}
		
		String[] transactionIds = document.getTransactionId().split(";");
		response = this.stampingEmeteraiRkg(transactionIds[currentLoop]);
		if (!response.getMeta().getSuccess()) {
			return response;
		}
		
		short totalStamped = document.getTotalStamping();
		totalStamped += 1;
		document.setTotalStamping(totalStamped);
		
		if (document.getTotalMaterai().equals(totalStamped)) {
			this.updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_UPL_CON, audit);
		} else {
			this.updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
		}
		
		MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_GO_LIVE);
		sdt.setMsLov(sdtStatus);
		sdt.setUsrUpd(audit.getCallerId());
		sdt.setDtmUpd(new Date());
		daoFactory.getStampDutyDao().updateNativeStringTrStampDuty(sdt);
		
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT);
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		MsVendor sdtVendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		
		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(trxNo);
		mutation.setTrxDate(new Date());
		mutation.setRefNo(documentH.getRefNumber());
		mutation.setQty(-1);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsTenant(document.getMsTenant());
		mutation.setMsVendor(sdtVendor);
		mutation.setTrDocumentD(document);
		mutation.setTrDocumentH(documentH);
		mutation.setNotes(sdt.getStampDutyNo());
		mutation.setTrStampDuty(sdt);
		mutation.setUsrCrt(audit.getCallerId());
		mutation.setDtmCrt(new Date());
		daoFactory.getBalanceMutationDao().insertTrBalanceMutation(mutation);
		
		return response;
	}
	
	private StampingEmeteraiRkgResponse stampingEmeteraiRkg(String teraOrderId) {
		StampingEmeteraiRkgResponse stampingResponse = new StampingEmeteraiRkgResponse();
		EmeteraiRkgResponseHeaderBean meta = new EmeteraiRkgResponseHeaderBean();
		
		String url = urlEmeterai + urlStampingOrder + "/" + teraOrderId + urlComplete;
		StampingEmeteraiRkgRequest request = new StampingEmeteraiRkgRequest();
		request.setTeraOrderId(teraOrderId);
		String jsonRequest = gson.toJson(request);
		LOG.info("RKG Stamping Emeterai Request: {}", jsonRequest);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerAuth, CONST_BEARER + authToken);
		WebClient client = WebClient.create(url).headers(mapHeader);
		
		Response response = null;
		try {
			response = client.put(jsonRequest);
		} catch (Exception e) {
			meta.setSuccess(false);
			stampingResponse.setMeta(meta);
			return stampingResponse;
		}
		
		String result = StringUtils.EMPTY;
		try {
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			meta.setSuccess(false);
			stampingResponse.setMeta(meta);
			LOG.error("Error on getting Stamping Emeterai RKG result");
			return stampingResponse;
		}
		
		LOG.info("RKG Stamping Emeterai Response: {}", result);
		stampingResponse = gson.fromJson(result, StampingEmeteraiRkgResponse.class);
		return stampingResponse;
	}
	
	private void sendInsufficientSdtBalanceEmail(String action, String refNo, String documentName, String balanceType, Integer saldo, String[] emailDest) {
		Map<String, Object> reminder = new HashMap<>();
		reminder.put("title", action);
		reminder.put("action", action);
		reminder.put("refNo", refNo);
		reminder.put("documentName", documentName);
		reminder.put("balanceType", balanceType);
		reminder.put("saldo", saldo);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("reminder", reminder);

		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INSUFFICIENT_BAL, templateParameters);
		String[] recipient = emailDest;

		EmailInformationBean emailBean = new EmailInformationBean();
		emailBean.setFrom(fromEmailAddr);
		emailBean.setTo(recipient);
		emailBean.setBodyMessage(template.getBody());
		emailBean.setSubject(template.getSubject());

		try {
			emailSenderLogic.sendEmail(emailBean, null);
		} catch (MessagingException e) {
			throw new EmailException(ReasonEmail.SEND_EMAIL_ERROR);
		}
	}
}
