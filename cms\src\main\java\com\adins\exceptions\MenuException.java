package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class MenuException extends AdInsException {
	private static final long serialVersionUID = 1L;
	
	public enum ReasonMenu {
		NOT_ACTIVE,
		EMPTY_PATH,
		NOT_MANAGEABLE
	}
	
	private final ReasonMenu reason;

	public MenuException(ReasonMenu reason) {
		this.reason = reason;
	}

	public MenuException(String message, ReasonMenu reason) {
		super(message);
		this.reason = reason;
	}

	public MenuException(Throwable ex, ReasonMenu reason) {
		super(ex);
		this.reason = reason;
	}

	public MenuException(String message, Throwable ex, ReasonMenu reason) {
		super(message, ex);
		this.reason = reason;
	}

	public ReasonMenu getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
				case NOT_ACTIVE:
					return StatusCode.MENU_NOT_ACTIVE;
				case NOT_MANAGEABLE:
					return StatusCode.MENU_NOT_MANAGEABLE;
				case EMPTY_PATH:
					return StatusCode.PATH_EMPTY;
				default:
					return StatusCode.UNKNOWN;
			}
			
		}
		
		return StatusCode.UNKNOWN;
	}

}
