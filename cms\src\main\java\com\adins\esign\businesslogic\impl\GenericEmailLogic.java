package com.adins.esign.businesslogic.impl;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Properties;

import javax.mail.Address;
import javax.mail.Flags;
import javax.mail.Flags.Flag;
import javax.mail.Folder;
import javax.mail.Message;
import javax.mail.Message.RecipientType;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.NoSuchProviderException;
import javax.mail.Part;
import javax.mail.Session;
import javax.mail.Store;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.EmailLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsEmailHosting;
import com.adins.esign.model.MsEmailPattern;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.DeleteEmailParam;
import com.adins.esign.model.custom.EmailBean;
import com.adins.esign.model.custom.ExpungeEmailResultBean;
import com.adins.esign.model.custom.ReadEmailBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.CreateSingleEmailRequest;
import com.adins.esign.webservices.model.CreateSingleEmailResponse;
import com.adins.exceptions.EmailException;
import com.adins.exceptions.EmailException.ReasonEmail;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericEmailLogic extends BaseLogic implements EmailLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericEmailLogic.class);
	
	@Value("${email.port}") private String emailPort;
	
	@Autowired private UserValidatorLogic userValidatorLogic;
	
	// Socket connection timeout value in milliseconds. Default is infinite timeout.
	private static final int IMAP_CONNECTION_TIMEOUT = 10_000;
	// Socket read timeout value in milliseconds. Default is infinite timeout.
	private static final int IMAP_READ_TIMEOUT = 10_000;
	// Socket write timeout value in milliseconds. Default is infinite timeout.
	private static final int IMAP_WRITE_TIMEOUT = 10_000;
	
	private int getImapConnectTimeout() {
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_IMAP_CONN_TIMEOUT);
		if (null == gs) {
			return IMAP_CONNECTION_TIMEOUT;
		}
		try {
			return Integer.valueOf(gs.getGsValue());
		} catch (Exception e) {
			return IMAP_CONNECTION_TIMEOUT;
		}
	}
	
	private int getImapReadTimeout() {
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_IMAP_READ_TIMEOUT);
		if (null == gs) {
			return IMAP_READ_TIMEOUT;
		}
		try {
			return Integer.valueOf(gs.getGsValue());
		} catch (Exception e) {
			return IMAP_READ_TIMEOUT;
		}
	}
	
	private int getImapWriteTimeout() {
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_IMAP_WRITE_TIMEOUT);
		if (null == gs) {
			return IMAP_WRITE_TIMEOUT;
		}
		try {
			return Integer.valueOf(gs.getGsValue());
		} catch (Exception e) {
			return IMAP_WRITE_TIMEOUT;
		}
	}
	
	private void addCpanelEmailAndForwarder(MsEmailHosting emailHosting, String address, String fullAddress) throws IOException {
		StringBuilder createEmailUri = new StringBuilder();
		createEmailUri.append("https://" + emailHosting.getEmailHostingDomain() + ":" + emailHosting.getAccessPort())
					  .append("/execute/Email/add_pop")
					  .append("?email=" + address)
					  .append("&password=" + emailHosting.getDefaultEmailPassword());
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		String authorizationHeader = Base64.getEncoder().encodeToString((emailHosting.getCpanelUsername() + ":" + emailHosting.getCpanelPassword()).getBytes());
		mapHeader.add(HttpHeaders.AUTHORIZATION, "Basic " + authorizationHeader);
		WebClient client = WebClient.create(createEmailUri.toString()).headers(mapHeader);
		Response response = client.post(null);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String result = IOUtils.toString(isReader);
		LOG.info("Create email account response: {}", result);
		
		addEmailAccountForwarder(fullAddress, emailHosting, mapHeader);
	}
	
	@Override
	public String createEmail(SignerBean bean, MsTenant tenant) throws ParseException, IOException {
		MsEmailHosting emailHosting = tenant.getMsEmailHosting();
		List<EmailBean> emailBeans = daoFactory.getVendorRegisteredUserDao().getUserRegisteredEmailByNik(bean.getIdNo());
		if(!emailBeans.isEmpty()) {
			for(EmailBean eBean : emailBeans) {
				if("1".equals(eBean.getEmailService()) && eBean.getEmail().contains(StringUtils.upperCase(emailHosting.getEmailHostingDomain()))) {
					bean.setIdEmailHosting(emailHosting.getIdEmailHosting());
					return eBean.getEmail();
				}
			}
		}
		
		String address = this.createEmailAddress(bean, emailHosting.getEmailHostingDomain());
		String fullAddress = address + "@" + emailHosting.getEmailHostingDomain();
		bean.setIdEmailHosting(emailHosting.getIdEmailHosting());
		
		try {
			addCpanelEmailAndForwarder(emailHosting, address, fullAddress);
		} catch (Exception e) {
			LOG.error("Failed to add cPanel email address and forwarder with error: {}", e.getLocalizedMessage(), e);
		}
		return fullAddress;
	}
	
	private void addEmailAccountForwarder(String emailAddress, MsEmailHosting emailHosting, MultivaluedMap<String, String> mapHeader) throws IOException {
		StringBuilder addForwarderUri = new StringBuilder();
		addForwarderUri.append("https://" + emailHosting.getEmailHostingDomain() + ":" + emailHosting.getAccessPort())
					   .append("/execute/Email/add_forwarder")
					   .append("?domain=" + emailHosting.getEmailHostingDomain())
					   .append("&email=" + emailAddress)
					   .append("&fwdopt=fwd")
					   .append("&fwdemail=" + emailHosting.getDefaultEmailInbox());
		
		WebClient client = WebClient.create(addForwarderUri.toString()).headers(mapHeader);
		Response response = client.post(null);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String result = IOUtils.toString(isReader);
		LOG.info("Add cPanel forwarder response: {}", result);
	}
	
	private String createEmailAddress(SignerBean bean, String domain) throws ParseException {
		String regex = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_REGEX_NAME_CLEANSING);
		String name = bean.getUserName();
		name = StringUtils.normalizeSpace(name);
		name = name.replaceAll(regex, "");
		name = StringUtils.normalizeSpace(name);
		bean.setUserName(name);
		String[] separated = name.split(" ");
		StringBuilder address = new StringBuilder();
		for (int i = 0; i < separated.length; i++) {
			address.append(StringUtils.lowerCase(separated[i]));
			if(i != separated.length - 1){
				address.append(".");
			}
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		SimpleDateFormat sdfEmail = new SimpleDateFormat(GlobalVal.DATE_FORMAT_EMAIL);
		
		int seq = 1;
		
		Date dob = sdf.parse(bean.getUserDob());
		String dobEmail = sdfEmail.format(dob);
		int addressLength = address.toString().length();
		
		String email = address.toString() + "@" + domain;
		
		AuditContext audit = new AuditContext(name);
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		
		while (null != user) {
			if (!email.contains(dobEmail)) {
				address.append(dobEmail);
			} else {
				if (Character.compare(address.charAt(addressLength-2), '_') != 0) {
					address.append("_" + seq);
					seq++;
				} else {
					address.deleteCharAt(addressLength-1);
					address.append(seq);
					seq++;
				}
			}
			
			addressLength = address.toString().length();
			email = address.toString() + "@" + domain;
			user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		}
		return address.toString();
	}

	@Override
	public List<ReadEmailBean> readEmail(String emailAddress, String password) {
		try {
			String[] emailArr = emailAddress.split("@");
			String host = emailArr[1].contains("gmail") ? "mail.google.com" : "mail." + emailArr[1];
			// Secara umum, valuenya 993. Port untuk read email pasti ambil dari application.properties
			String port = emailPort;
			return getEmails("imap", host, port, emailAddress, password);
		} catch (Exception e) {
			LOG.error("Error when reading email from {}", emailAddress, e);
			return new ArrayList<>();
		}
		
	}
	
	private static Properties getServerProperties(String protocol, String host, String port, int connTimeout, int readTimeout, int writeTimeout) {
		
		String connTimeoutKey = String.format("mail.%s.connectiontimeout", protocol);
		String readTimeoutKey = String.format("mail.%s.timeout", protocol);
		String writeTimeoutKey = String.format("mail.%s.writetimeout", protocol);
		
		Properties properties = new Properties();
		properties.put(String.format("mail.%s.host", protocol), host);
		properties.put(String.format("mail.%s.port", protocol), port);
		properties.put(connTimeoutKey, connTimeout);
		properties.put(readTimeoutKey, readTimeout);
		properties.put(writeTimeoutKey, writeTimeout);
		properties.setProperty(String.format("mail.%s.socketFactory.class", protocol), "javax.net.ssl.SSLSocketFactory");
		properties.setProperty(String.format("mail.%s.socketFactory.fallback", protocol), "false");
		properties.setProperty(String.format("mail.%s.socketFactory.port", protocol), String.valueOf(port));
		
		LOG.debug("Setting server properties for {}", host);
		return properties;
	}
	
	public List<ReadEmailBean> getEmails(String protocol, String host, String port, String userName, String password) throws IOException {
		int connTimeout = getImapConnectTimeout();
		int readTimeout = getImapReadTimeout();
		int writeTimeout = getImapWriteTimeout();

		Properties properties = getServerProperties(protocol, host, port, connTimeout, readTimeout, writeTimeout);
		LOG.debug("Connecting to: {}", host);
		Session session = Session.getInstance(properties);
		LOG.debug("Connected to: {}", host);

		List<ReadEmailBean> listReadEmail = new ArrayList<>();
		try {
			Store store = session.getStore(protocol);
			store.connect(userName, password);

			Folder inbox = store.getFolder("INBOX");
			inbox.open(Folder.READ_WRITE);

			int unreadCount = inbox.getUnreadMessageCount();
			int count = inbox.getMessageCount();
			LOG.debug("You have {} unread emails in your inbox.", unreadCount);
			Message[] messages = inbox.getMessages(1, count);
			for (int i = 0; i < messages.length; i++) {
				Message message = messages[i];
				if (!message.getFlags().contains(Flag.SEEN)) {
					//message.setFlag(Flag.SEEN, true);
					String toemail = parseAddresses(message.getRecipients(RecipientType.TO));
					String subject = message.getSubject();
					String from = parseAddresses(message.getFrom());
	
					String contentType = message.getContentType();
					String messageContent = "";
	
					if (contentType.contains("multipart")) {
						Object content = message.getContent();
						if (content != null) {
							messageContent = content.toString();
						}
	
						// content may contain attachments
						Multipart multiPart = (Multipart) message.getContent();
						int numberOfParts = multiPart.getCount();
						for (int partCount = 0; partCount < numberOfParts; partCount++) {
							MimeBodyPart part = (MimeBodyPart) multiPart.getBodyPart(partCount);
							if (!Part.ATTACHMENT.equalsIgnoreCase(part.getDisposition())) {
								// this part for the message content
								if (!part.getContent().toString().startsWith("com.sun.mail.util.BASE64DecoderStream"))
									messageContent = part.getContent().toString();
								if (messageContent.contains("MimeMultipart")) {
									Multipart multipart2 = (Multipart) part.getContent();
									int parts = multipart2.getCount();
									for (int x = 0; x < parts; x++) {
										MimeBodyPart part2 = (MimeBodyPart) multipart2.getBodyPart(x);
										if (!Part.ATTACHMENT.equalsIgnoreCase(part2.getDisposition()) && 
												!part2.getContent().toString().startsWith("com.sun.mail.util.BASE64DecoderStream")) {
											messageContent = part2.getContent().toString();
										}
									}
								}
							}
						}
						
					} else if (contentType.contains("text/plain") || contentType.contains("text/html")) {
						Object content = message.getContent();
						if (content != null) {
							messageContent = content.toString();
						}
					}
					
					ReadEmailBean bean = new ReadEmailBean();
					bean.setSender(from);
					bean.setSubject(subject);
					bean.setRecipient(toemail);
					bean.setContent(messageContent);
					listReadEmail.add(bean);
				}
			}
			
			inbox.close();
			store.close();
		} catch (NoSuchProviderException e) {
			LOG.error("Get email: No provider for protocol {}: {}", protocol, e.getLocalizedMessage(), e);
		} catch (MessagingException e) {
			LOG.error("Get email: Could not connect to message store: {}", e.getLocalizedMessage(), e);
		} catch (Exception e) {
			LOG.error("Get email: Error when getting email list: {}", e.getLocalizedMessage(), e);
		}
		
		return listReadEmail;
	}

	private String parseAddresses(Address[] address) {

		StringBuilder listOfAddress = new StringBuilder();
		if ((address == null) || (address.length < 1))
			return null;
		if (!(address[0] instanceof InternetAddress))
			return null;

		for (int i = 0; i < address.length; i++) {
			InternetAddress internetAddress = (InternetAddress) address[0];
			listOfAddress.append(internetAddress.getAddress());
			if (i != address.length - 1) {
				listOfAddress.append(",");
			}
		}
		return listOfAddress.toString();
	}

	/**
	 * This method is use to check whether user entered credentials are correct or
	 * not. If connection established then credentials are correct otherwise not.
	 * 
	 * @param protocol
	 * @param host
	 * @param port
	 * @param userName
	 * @param password
	 * @return
	 */

	public String getConnectedStatus(String protocol, String host, String port, String userName, String password) {
		int connTimeout = getImapConnectTimeout();
		int readTimeout = getImapReadTimeout();
		int writeTimeout = getImapWriteTimeout();
		
		Properties properties = getServerProperties(protocol, host, port, connTimeout, readTimeout, writeTimeout);
		Session session = Session.getInstance(properties);
		String isconnected = "";
		try {
			// ---- Start Connection Establishment----------
			Store store = session.getStore(protocol);
			LOG.debug("Connecting to email....");
			store.connect(userName, password);
			isconnected = "Connected_to_IMAP";
			LOG.debug("Is Connected: {}", isconnected);
			LOG.debug("Connected to : {}", protocol);
		} catch (NoSuchProviderException e) {
			String msg = "No provider for protocol: " + protocol;
			LOG.debug(msg);
			return msg;
		} catch (MessagingException ex) {
			String msg = this.messageSource.getMessage("businesslogic.email.couldnotconnecttostore", null, this.retrieveDefaultLocale());
			LOG.debug(msg);
			return msg;
		}
		return isconnected;
	}

	/**
	 * This method is use to handle MIME message. a message with an attachment is
	 * represented in MIME as a multipart message. In the simple case, the results
	 * of the Message object's getContent method will be a MimeMultipart object. The
	 * first body part of the multipart object wil be the main text of the message.
	 * The other body parts will be attachments.
	 * 
	 * @param p
	 * @return
	 * @throws MessagingException
	 * @throws IOException
	 */

	public static String getText(Part p) throws MessagingException, IOException {
		if (p.isMimeType("text/*")) {
			return (String) p.getContent();
		}

		if (p.isMimeType("multipart/alternative")) {
			// prefer html text over plain text
			Multipart mp = (Multipart) p.getContent();
			String text = null;
			for (int i = 0; i < mp.getCount(); i++) {
				Part bp = mp.getBodyPart(i);
				if (bp.isMimeType("text/plain")) {
					if (text == null){
						text = getText(bp);
					}
				} else if (bp.isMimeType("text/html")) {
					String s = getText(bp);
					if (s != null){
						return s;
					}
				} else {
					return getText(bp);
				}
			}
			return text;
		} else if (p.isMimeType("multipart/*")) {
			Multipart mp = (Multipart) p.getContent();
			for (int i = 0; i < mp.getCount(); i++) {
				String s = getText(mp.getBodyPart(i));
				if (s != null){
					return s;
				}
			}
		}

		return null;
	}

	@Override
	public ExpungeEmailResultBean deleteEmail(String emailAddress, String password, DeleteEmailParam param) throws IOException {
		String[] emailArr = emailAddress.split("@");
		String host = "mail." + emailArr[1];
		// Secara umum, valuenya 993. Port untuk delete email pasti ambil dari application.properties
		String port = emailPort;
		return expungeEmails("imap", host, port, emailAddress, password, param);
	}
	
	private ExpungeEmailResultBean expungeEmails(String protocol, String host, String port, String userName, String password, DeleteEmailParam param) {
		
		LOG.debug("Expunge emails in {}", host);
		
		int connTimeout = getImapConnectTimeout();
		int readTimeout = getImapReadTimeout();
		int writeTimeout = getImapWriteTimeout();
		
		Properties properties = getServerProperties(protocol, host, port, connTimeout, readTimeout, writeTimeout);
		Session session = Session.getInstance(properties);
		LOG.debug("Expunge emails, connected to: {}", host);
		
		int deleteCount = 0;
		ExpungeEmailResultBean resultBean = new ExpungeEmailResultBean();
		
		try {
			Store store = session.getStore(protocol);
			store.connect(userName, password);

			Folder inbox = store.getFolder("INBOX");
			inbox.open(Folder.READ_WRITE);

			int count = inbox.getMessageCount();
			LOG.info("Deleting mails in {}", userName);
			LOG.debug("You have {} emails in your inbox.", count);
			Message[] messages = inbox.getMessages(1, count);
			for (int i = 0; i < messages.length; i++) {
				Message message = messages[i];
				if (param.getDeleteType().equalsIgnoreCase("ALL") || this.isDeleteParamRead(param, message.getFlags())) {
					message.setFlag(Flag.DELETED, true);
					deleteCount++;
				} else if (param.getDeleteType().equalsIgnoreCase("DATE")) {
					Date threshold = DateUtils.addDays(new Date(), -param.getDay());
					if (null == message.getSentDate()) {
						LOG.info("{} has mail with subject {} and sent date null", userName, message.getSubject());
						resultBean.setErrorMessage(userName + " has mail " + message.getSubject() + " with sent date null");
					} else if (null != message.getSentDate() && message.getSentDate().before(threshold)) {
						message.setFlag(Flag.DELETED, true);
						deleteCount++;
					}
				}
			}
			
			inbox.expunge(); //DELETE PERMANENTLY
			inbox.close(true); //true supaya yg ter-flag DELETED dihapus
			store.close();
		} catch (NoSuchProviderException ex) {
			LOG.error("Expunge email: No provider for protocol: {}", protocol, ex);
		} catch (MessagingException ex) {
			LOG.error("Expunge email: Could not connect to the message store: {}", ex.getLocalizedMessage());
		}
		
		resultBean.setTotalData(deleteCount);
		return resultBean;
	}
	
	private boolean isDeleteParamRead(DeleteEmailParam param, Flags flags) {
		return param.getDeleteType().equalsIgnoreCase("READ") && flags.contains(Flag.SEEN);
	}
	
	@Override
	public String parseEmailContent(ReadEmailBean emailBean, MsEmailPattern emailPattern) {
		String subject = emailBean.getSubject();
		String emailContentHtml = emailBean.getContent();
		Integer index = Integer.valueOf(emailPattern.getElementIndex());
		
		LOG.info("Parse email body with subject {} with body length = {}", subject, emailContentHtml.length());
		
		Document doc = Jsoup.parse(emailContentHtml).body().ownerDocument();
		Elements elements = doc.select(emailPattern.getCssQuery());
		
		String result = StringUtils.EMPTY;
		if (GlobalVal.READ_EMAIL_TYPE_LINK.equals(emailPattern.getDataType())) {
			result = elements.get(index).attr("href");
		} else if (GlobalVal.READ_EMAIL_TYPE_TEXT.equals(emailPattern.getDataType())) {
			result = elements.get(index).text();
		}
		
		LOG.info("Parse email body with subject {} result: {}", emailBean.getSubject(), result);
		return result;
	}

	@Override
	public CreateSingleEmailResponse createEmail(CreateSingleEmailRequest request, AuditContext audit) {
		try {
			SignerBean bean = new SignerBean();
			bean.setUserName(request.getName());
			bean.setUserDob(request.getDob());
			bean.setIdNo(request.getNik());
			String emailAddress = createEmail(bean, request.getTenant());
			
			CreateSingleEmailResponse response = new CreateSingleEmailResponse();
			response.setEmail(emailAddress);
			response.setIdEmailHosting(bean.getIdEmailHosting());
			return response;
		} catch (Exception e) {
			LOG.error("Create email error", e);
			String message = "Error when creating email: " + e.getLocalizedMessage();
			throw new EmailException(message, ReasonEmail.CREATE_EMAIL_ERROR);
		}
		
	}

}
