package com.adins.esign.businesslogic.api;

import java.io.IOException;
import java.util.List;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.am.model.AmMsuser;
import com.adins.esign.constants.enums.NotificationSendingPoint;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.custom.FaceMatchLivenessRequest;
import com.adins.esign.webservices.model.AddTenantRequest;
import com.adins.esign.webservices.model.AddTenantResponse;
import com.adins.esign.webservices.model.CheckLivenessFaceCompareServiceRequest;
import com.adins.esign.webservices.model.CheckLivenessFaceCompareServiceResponse;
import com.adins.esign.webservices.model.EditTenantRequest;
import com.adins.esign.webservices.model.EditTenantResponse;
import com.adins.esign.webservices.model.FaceVerifyRequest;
import com.adins.esign.webservices.model.FaceVerifyResponse;
import com.adins.esign.webservices.model.GetAutomaticStampingAfterSignRequest;
import com.adins.esign.webservices.model.GetAutomaticStampingAfterSignResponse;
import com.adins.esign.webservices.model.GetAvailableSendingPointInvitationRequest;
import com.adins.esign.webservices.model.GetAvailableSendingPointInvitationResponse;
import com.adins.esign.webservices.model.GetAvailableSendingPointRequest;
import com.adins.esign.webservices.model.GetAvailableSendingPointResponse;
import com.adins.esign.webservices.model.GetListTenantRequest;
import com.adins.esign.webservices.model.GetListTenantResponse;
import com.adins.esign.webservices.model.GetSmsDeliverySettingRequest;
import com.adins.esign.webservices.model.GetSmsDeliverySettingResponse;
import com.adins.esign.webservices.model.GetStatusEmailServiceTenantRequest;
import com.adins.esign.webservices.model.GetStatusEmailServiceTenantResponse;
import com.adins.esign.webservices.model.GetStatusStampingOtomatisTenantRequest;
import com.adins.esign.webservices.model.GetStatusStampingOtomatisTenantResponse;
import com.adins.esign.webservices.model.GetTenantRekonRequest;
import com.adins.esign.webservices.model.GetTenantRekonResponse;
import com.adins.esign.webservices.model.GetUploadUrlRequest;
import com.adins.esign.webservices.model.GetUploadUrlResponse;
import com.adins.esign.webservices.model.LivenessEmbedRequest;
import com.adins.esign.webservices.model.LivenessRequest;
import com.adins.esign.webservices.model.LivenessResponse;
import com.adins.esign.webservices.model.TenantDetailRequest;
import com.adins.esign.webservices.model.TenantDetailResponse;
import com.adins.esign.webservices.model.TenantSettingsEmbedRequest;
import com.adins.esign.webservices.model.TenantSettingsRequest;
import com.adins.esign.webservices.model.TenantSettingsResponse;
import com.adins.esign.webservices.model.TestTenantCallbackRequest;
import com.adins.esign.webservices.model.TestTenantCallbackResponse;
import com.adins.esign.webservices.model.TryTenantCallbackRequest;
import com.adins.esign.webservices.model.TryTenantCallbackResponse;
import com.adins.esign.webservices.model.UpdateDeliverySettingRequest;
import com.adins.esign.webservices.model.UpdateDeliverySettingResponse;
import com.adins.esign.webservices.model.UpdateTenantSettingsRequest;
import com.adins.esign.webservices.model.UpdateTenantSettingsResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface TenantLogic {
	MsTenant getTenantByCode (String tenantCode, AuditContext audit);
	MsTenant getTenantById(long idMsTenant, AuditContext audit);
	MsTenant getTenantFromXApiKey(String xApiKey, AuditContext audit);
	List<MsTenant> getListTenant (AuditContext audit);
	List<MsTenant> getListTenantByUser(AmMsuser user, AuditContext audit);
	
	void insertUseroftenant(AmMsuser user, MsTenant tenant, AuditContext audit);
	MsUseroftenant getUseroftenant(AmMsuser user, MsTenant tenant, AuditContext audit);
	MsUseroftenant getUseroftenant(String loginId, String tenantCode, AuditContext audit);
	GetListTenantResponse getListTenantCodeAndName(GetListTenantRequest request, AuditContext audit);
	String checkNotificationType(String tenantCode, String loginId, String vendorCode, AuditContext audit);
	GetStatusStampingOtomatisTenantResponse getStatusStampingOtomatisTenant(GetStatusStampingOtomatisTenantRequest request, AuditContext audit);
	GetAvailableSendingPointResponse getAvailableSendingPoint (GetAvailableSendingPointRequest request, AuditContext audit);
	GetAvailableSendingPointInvitationResponse getAvailableSendingPointInvitation (GetAvailableSendingPointInvitationRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_INQUIRY","ROLE_DASHBOARD","ROLE_INBOX"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	LivenessResponse getTenantLiveness(LivenessRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	LivenessResponse getTenantLivenessEmbed(LivenessEmbedRequest request, AuditContext audit);
	FaceVerifyResponse getLivenessCheck(FaceVerifyRequest request, AuditContext audit) throws IOException;
	FaceVerifyResponse getLivenessCheckNodeflux(FaceMatchLivenessRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	TenantSettingsResponse getTenantSettingsEmbed(TenantSettingsEmbedRequest request, AuditContext audit);
	TenantSettingsResponse getTenantSettings(TenantSettingsRequest request, AuditContext audit);
	TenantSettingsResponse getTenantSettingsWithoutSecurity(TenantSettingsRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_TENANT_SETTING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	UpdateTenantSettingsResponse updateTenantSettings(UpdateTenantSettingsRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_TENANT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	AddTenantResponse addTenant(AddTenantRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_TENANT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	EditTenantResponse editTenant(EditTenantRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_TENANT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	TenantDetailResponse getTenantDetail(TenantDetailRequest request, AuditContext audit);
	GetListTenantResponse getListTenantPaging(GetListTenantRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_LINK_SETTING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	UpdateDeliverySettingResponse updateDeliverySetting(UpdateDeliverySettingRequest request, AuditContext audit);
	GetSmsDeliverySettingResponse getSmsDeliverySettingDetail(GetSmsDeliverySettingRequest request, AuditContext audit);
	GetAutomaticStampingAfterSignResponse getAutomaticStampingAfterSign(GetAutomaticStampingAfterSignRequest request, AuditContext audit);
	GetUploadUrlResponse getUploadUrl(GetUploadUrlRequest request, AuditContext audit);

	
	GetStatusEmailServiceTenantResponse getStatusEmailServiceTenant(GetStatusEmailServiceTenantRequest request, AuditContext audit);
	
	GetTenantRekonResponse getTenantRekon(GetTenantRekonRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_INQUIRY","ROLE_DASHBOARD","ROLE_INBOX"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	CheckLivenessFaceCompareServiceResponse checkLivenessFaceCompareService(CheckLivenessFaceCompareServiceRequest request, AuditContext audit);
	
	CheckLivenessFaceCompareServiceResponse checkLivenessFaceCompareServiceFullApi(CheckLivenessFaceCompareServiceRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_TENANT_SETTING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	TestTenantCallbackResponse testCallback(TestTenantCallbackRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_TENANT_SETTING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	TryTenantCallbackResponse tryCallback(TryTenantCallbackRequest request, AuditContext audit);
	
	/**
	 * @param tenant
	 * @param sendingPoint
	 * @param emailService "1", "0", or null. Any other string will be considered as "0".
	 */
	NotificationType getNotificationType(MsTenant tenant, NotificationSendingPoint sendingPoint, String emailService);
}
