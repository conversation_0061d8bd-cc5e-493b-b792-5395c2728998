package com.adins.esign.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.JobUpdatePsreIdDao;
import com.adins.esign.model.TrJobUpdatePsreId;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class JobUpdatePsreIdDaoHbn extends BaseDaoHbn implements JobUpdatePsreIdDao {

	@Override
	public void insertJobUpdatePsreId(TrJobUpdatePsreId jobUpdatePsreId) {
		jobUpdatePsreId.setUsrCrt(MssTool.maskData(jobUpdatePsreId.getUsrCrt()));
		managerDAO.insert(jobUpdatePsreId);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertJobUpdatePsreIdNewTran(TrJobUpdatePsreId jobUpdatePsreId) {
		jobUpdatePsreId.setUsrCrt(MssTool.maskData(jobUpdatePsreId.getUsrCrt()));
		managerDAO.insert(jobUpdatePsreId);
	}

	@Override
	public void updateJobUpdatePsreId(TrJobUpdatePsreId jobUpdatePsreId) {
		jobUpdatePsreId.setUsrUpd(MssTool.maskData(jobUpdatePsreId.getUsrUpd()));
		managerDAO.update(jobUpdatePsreId);
	}	

}
