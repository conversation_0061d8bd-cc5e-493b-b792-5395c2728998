package com.adins.am.businesslogic.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmAuditlog;
import com.adins.framework.persistence.dao.api.IAuditable;
import com.adins.framework.persistence.dao.model.AuditContext;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class AuditlogLogicTest extends BaseLogic {
	@Autowired private IAuditable audit;
	
	private AuditContext auditContext = new AuditContext();
	
	private String activity;
	private String tableName;
	private String fieldName;
	private String oldValue;
	private String newValue;
	private String keyValue;
	
	@BeforeEach
	public void setUp()	{
		activity = "JUNIT";
		tableName = "JUNIT_TABLE";
		fieldName = "JUNIT_NAME";
		oldValue = "JUNIT_OLD_VALUE";
		newValue = "JUNIT_NEW_VALUE";
		keyValue = "JUNIT";
		
		Map<String, Object> parameters = new HashMap<String, Object>();
		parameters.put(AuditContext.KEY_PARAMS_APPLICATIONNAME, "JUNIT Application");
		parameters.put(AuditContext.KEY_PARAMS_SUBSYSTEMNAME, "JUNIT Subsystem");
		parameters.put(AuditContext.KEY_PARAMS_SCREENID, "JUNIT Screen Id");
		parameters.put(AuditContext.KEY_PARAMS_TERMINALADDRESS, "JUNIT Terminal Address");
		parameters.put(AuditContext.KEY_PARAMS_TERMINALID, "JUNIT Terminal Id");
		parameters.put(AuditContext.KEY_PARAMS_TERMINALUSERID, "JUNIT Terminal User Id");
		auditContext.setParameters(parameters);
	}
	
	@Test
	@Rollback(true)
	void insertAuditLogWithAuditContextTest() {
		AuditContext.setContext(auditContext);
		audit.insertAuditLog(activity, tableName, fieldName, oldValue, newValue, keyValue);
		AmAuditlog auditLog = daoFactory.getLogDao().getAuditLogByActivity(activity);
		assertEquals(auditLog.getActivity(), activity);
	}
	
	@Test
	@Rollback(true)
	void insertAuditLogWithoutAuditContextTest() {
		audit.insertAuditLog(activity, tableName, fieldName, oldValue, newValue, keyValue);
		AmAuditlog auditLog = daoFactory.getLogDao().getAuditLogByActivity(activity);
		assertEquals(auditLog.getActivity(), activity);
	}

}
