package com.adins.esign.businesslogic.api;

import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.webservices.model.DownloadAuditTrailExcelRequest;
import com.adins.esign.webservices.model.DownloadAuditTrailExcelResponse;
import com.adins.esign.webservices.model.InquiryAuditTrailSignProcessRequest;
import com.adins.esign.webservices.model.InquiryAuditTrailSignProcessResponse;
import com.adins.esign.webservices.model.SigningProcessAuditTrailRelatedDocumentRequest;
import com.adins.esign.webservices.model.SigningProcessAuditTrailRelatedDocumentResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface SigningProcessAuditTrailLogic {
	
	/**
	 * Save request and response body in OSS folder audit_log. Log will be saved in zip format.
	 * 
	 * @param trail TrSigningProcessAuditTrail object
	 * @param subfolderName The value of x in audit_log/{x}/{id_signing_process_audit_trail}.zip
	 * @param requestBody
	 * @param responseBody
	 * @param appendExistingLog Set <code>true</code> to append existing log file. Set <code>false</code> to overwrite existing log file.
	 * @param audit
	 */
	void logProcessRequestResponse(TrSigningProcessAuditTrail trail, String subfolderName, String requestBody, String responseBody, boolean appendExistingLog, AuditContext audit);
	InquiryAuditTrailSignProcessResponse getInquiryAuditTrail(InquiryAuditTrailSignProcessRequest request, AuditContext audit);
	SigningProcessAuditTrailRelatedDocumentResponse getSigningProcessAuditTrailRelatedDocument(SigningProcessAuditTrailRelatedDocumentRequest request, AuditContext audit);
	DownloadAuditTrailExcelResponse downloadAuditTrailSignProcess(DownloadAuditTrailExcelRequest request, AuditContext audit);


}
