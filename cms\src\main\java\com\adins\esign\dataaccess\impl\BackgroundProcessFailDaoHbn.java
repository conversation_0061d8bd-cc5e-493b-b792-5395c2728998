package com.adins.esign.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.BackgroundProcessFailDao;
import com.adins.esign.model.TrBackgroundProcessFail;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class BackgroundProcessFailDaoHbn extends BaseDaoHbn implements BackgroundProcessFailDao {

	@Override
	public void insertTrBackgroundProcessFail(TrBackgroundProcessFail backgroundProcessFail) {
		backgroundProcessFail.setUsrCrt(MssTool.maskData(backgroundProcessFail.getUsrCrt()));
		this.managerDAO.insert(backgroundProcessFail);
	}

}
