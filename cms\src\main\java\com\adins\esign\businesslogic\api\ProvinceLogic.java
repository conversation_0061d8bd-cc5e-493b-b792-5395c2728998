package com.adins.esign.businesslogic.api;

import com.adins.esign.webservices.model.GetProvinceByInvitationRequest;
import com.adins.esign.webservices.model.GetProvinceEmbedRequest;
import com.adins.esign.webservices.model.GetProvinceRequest;
import com.adins.esign.webservices.model.GetProvinceResponse;
import com.adins.esign.webservices.model.external.GetProvinceListExternalRequest;
import com.adins.esign.webservices.model.external.GetProvinceListExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface ProvinceLogic {
	GetProvinceResponse getProvinceList(GetProvinceRequest request, AuditContext audit);
	GetProvinceResponse getProvinceListEmbed(GetProvinceEmbedRequest request, AuditContext audit);
	GetProvinceResponse getProvinceListInvReg(GetProvinceByInvitationRequest request, AuditContext audit);
	GetProvinceListExternalResponse getProvinceListExternal(GetProvinceListExternalRequest request, String apiKey, AuditContext audit);

}
