package com.adins.esign.dataaccess.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.DailyRecapDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceDailyRecap;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class DailyRecapDaoHbn extends BaseDaoHbn implements DailyRecapDao {
	
	@Override
	public void insertTrBalanceDailyRecap(TrBalanceDailyRecap dailyRecap) {
		dailyRecap.setUsrCrt(MssTool.maskData(dailyRecap.getUsrCrt()));
		this.managerDAO.insert(dailyRecap);
	}
	
	@Override
	public int countQtyDailyRecap(String dateRecap, MsLov balanceType, MsTenant tenant, MsVendor vendor) {
		StringBuilder query = new StringBuilder();
		query.append(" SELECT SUM(COALESCE(recap_total_balance,0)+ COALESCE(number_of_use,0)) AS Saldo ")
				.append(" FROM ms_lov ")
				.append(" LEFT JOIN LATERAL ( ")
					.append(" SELECT recap_total_balance, recap_date, id_ms_tenant, id_ms_vendor ")
					.append(" FROM tr_balance_daily_recap bdc ")
					.append(" WHERE bdc.lov_balance_type = ms_lov.id_lov ")
						.append(" AND bdc.recap_date < TO_DATE(:dateRecap ,'"+GlobalVal.DATE_FORMAT+"')")
						.append(" AND id_ms_tenant = :idMsTenant ")
						.append(" AND id_ms_vendor = :idMsVendor ")
					.append(" ORDER BY bdc.recap_date DESC LIMIT 1 ")
				.append(" ) bdc ON TRUE ")
				.append(" LEFT JOIN LATERAL ( ")
					.append(" SELECT SUM(COALESCE(bm.qty,0)) AS number_of_use ")
					.append(" FROM tr_balance_mutation bm ")
					.append(" JOIN ms_lov mlov ON mlov.id_lov = lov_trx_type ")
					.append(" WHERE bm.lov_balance_type = ms_lov.id_lov ")
						.append(" AND DATE(bm.trx_date)>bdc.recap_date AND DATE(bm.trx_date)<=TO_DATE(:dateRecap ,'"+GlobalVal.DATE_FORMAT+"')")						
						.append(" AND bm.id_ms_tenant=bdc.id_ms_tenant ")
						.append(" AND bm.id_ms_vendor=bdc.id_ms_vendor ")
				.append(" ) bm ON TRUE ")
				.append(" WHERE lov_group= :lovGroupBalanceType ")
				.append(" AND code= :codeLovBalanceType ");
		
		Object[][] queryParams = { 
				{ "dateRecap", dateRecap},
				{ MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant()},
				{ MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor()},
				{ "lovGroupBalanceType", StringUtils.upperCase(balanceType.getLovGroup())},
				{ "codeLovBalanceType", StringUtils.upperCase(balanceType.getCode())}
		};
		return ((BigDecimal) this.managerDAO.selectOneNativeString(query.toString(), queryParams)).intValue();
	}
	
	

	@Override
	public TrBalanceDailyRecap getDailyRecap(String dateRecap, MsLov balanceType, MsTenant tenant, MsVendor vendor) {
		TrBalanceDailyRecap dailyRecap = new TrBalanceDailyRecap();

		StringBuilder query = new StringBuilder();
		query.append(" SELECT id_balance_daily_recap, recap_date, recap_total_balance ")
				.append(" FROM tr_balance_daily_recap bdc ")
				.append(" WHERE bdc.lov_balance_type = :idLov ")
					.append(" AND bdc.recap_date = TO_DATE(:dateRecap ,'"+GlobalVal.DATE_FORMAT+"') ") //check yesterday recap
					.append(" AND id_ms_tenant = :idMsTenant ")
					.append(" AND id_ms_vendor = :idMsVendor ");
				
		Object[][] queryParams = { 
				{ "dateRecap", dateRecap},
				{ MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant()},
				{ MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor()},
				{ MsLov.ID_LOV_HBM, balanceType.getIdLov()}
		};

		List<Map<String,Object>> resultDailyRecap = this.managerDAO.selectAllNativeString(query.toString(), queryParams);
		if(!resultDailyRecap.isEmpty()) {
			for (Map<String,Object> dailyRecapData : resultDailyRecap) {
				dailyRecap.setIdBalanceDailyRecap(((BigInteger) dailyRecapData.get("d0")).longValue());		
				dailyRecap.setRecapDate((Date) dailyRecapData.get("d1"));
				dailyRecap.setRecapTotalBalance((Integer) dailyRecapData.get("d2"));
				dailyRecap.setMsLov(balanceType);
				dailyRecap.setMsTenant(tenant);
				dailyRecap.setMsVendor(vendor);

			}
		}
		return dailyRecap;
	}
	
}
