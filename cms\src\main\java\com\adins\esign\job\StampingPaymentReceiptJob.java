package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.interfacing.PaymentReceiptStampingLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class StampingPaymentReceiptJob extends BaseLogic {
	private static final Logger LOG = LoggerFactory.getLogger(StampingPaymentReceiptJob.class);
	
	@Autowired private PaymentReceiptStampingLogic paymentReceiptStampingLogic;
	
	public void runStampingPaymentReceipt() {
		try {
			LOG.info("Job Stamping Payment Receipt Started");
			AuditContext audit = new AuditContext("STAMPING SCHEDULER");
			paymentReceiptStampingLogic.retryStampingAllPaymentReceipt(audit);
			LOG.info("Job Stamping Payment Receipt Finished");
		} catch (Exception e) {
			LOG.error("Error on running StampingPaymentReceiptJob", e);
		}
	}
}
