package com.adins.esign.constants;

public class TekenAjaConstant {
	protected TekenAjaConstant() {
		throw new IllegalStateException("TekenAjaConstants class shall not be instantiated!");
	}
	
	// Register check action
	public static final String REGCEK_ACTION_CHECK_NIK	 = "check_nik";
	public static final String REGCEK_ACTION_RESEND_EMAIL	 = "resend_email";
	
	// Response status
	public static final String RESPONSE_STATUS_OK = "OK";
	public static final String RESPONSE_STATUS_ERROR = "ERROR";
	
	// Register response code
	public static final String REGISTER_RESPONSE_CODE_USER_EXISTS = "USER_EXISTS";
	public static final String REGISTER_RESPONSE_CODE_NIK_EMAIL_EXIST = "NIK_EMAIL_EXIST";
	
	// Register response message
	public static final String REGISTER_RESPONSE_MSG_USER_EXISTS = "Pengguna telah terdaftar";
	public static final String REGISTER_RESPONSE_MSG_NIK_EMAIL_EXIST = "NIK atau Email sudah terdaftar";
	
	// NIK check response code
	public static final String NIK_CHECK_RESPONSE_CODE_EXISTS_VERIFIED = "USER_EXISTS_VERIFIED";
	public static final String NIK_CHECK_RESPONSE_CODE_EXISTS_UNVERIFIED = "USER_EXISTS_UNVERIFIED";
	public static final String NIK_CHECK_RESPONSE_CODE_EXISTS_CERT_EXPIRED = "USER_EXISTS_CERTIFICATE_EXPIRED";
	
	// NIK check response message
	public static final String NIK_CHECK_RESPONSE_MSG_EXISTS_VERIFIED = "Pengguna telah terdaftar dan Aktif.";
	public static final String NIK_CHECK_RESPONSE_MSG_EXISTS_UNVERIFIED = "Pengguna sudah terdaftar tapi belum verifikasi email.";
	public static final String NIK_CHECK_RESPONSE_MSG_EXISTS_CERT_EXPIRED = "Pengguna sudah terdaftar tapi sertifikat sudah kadaluwarsa.";
}