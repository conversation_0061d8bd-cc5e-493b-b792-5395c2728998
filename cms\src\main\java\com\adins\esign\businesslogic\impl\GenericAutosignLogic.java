package com.adins.esign.businesslogic.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.AutosignLogic;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.FunctionComputeLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrProcessAutosignBmH;
import com.adins.esign.model.custom.ImportProcessAutosignBmBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.model.custom.DetailImportAutosignBmBean;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.esign.webservices.model.DetailImportAutosignBmRequest;
import com.adins.esign.webservices.model.DetailImportAutosignBmResponse;
import com.adins.esign.webservices.model.DownloadTemplateExcelImportAutosignBmRequest;
import com.adins.esign.webservices.model.DownloadTemplateExcelImportAutosignBmResponse;
import com.adins.esign.webservices.model.ImportAutosignBmDataRequest;
import com.adins.esign.webservices.model.ImportAutosignBmDataResponse;
import com.adins.esign.webservices.model.InquiryImportAutosignBmRequest;
import com.adins.esign.webservices.model.InquiryImportAutosignBmResponse;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.exceptions.AutosignException;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.UserException;
import com.adins.exceptions.AutosignException.ReasonAutosign;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Component
public class GenericAutosignLogic extends BaseLogic implements AutosignLogic {

	private static final Logger LOG = LoggerFactory.getLogger(GenericUserLogic.class);
	
	@Autowired private VendorValidatorLogic vendorValidatorLogic;
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private FunctionComputeLogic functionComputeLogic;
	
	@Override
	public ImportAutosignBmDataResponse importDataBmAutosign(ImportAutosignBmDataRequest request, AuditContext audit) {
		boolean checkExistence = true;
		
		byte[] decodedBytes;
		
		String messageValidation = "";
		
		messageValidation = getMessage(GlobalKey.MESSAGE_AUTOSIGN_FILENAME_EMPTY, null, audit);
		commonValidatorLogic.validateNotNull(request.getFileName(), messageValidation, StatusCode.EMPTYFILENAME);
		
		commonValidatorLogic.validateNotNull(request.getTenantCode(), "tenant Code", audit);
		
		commonValidatorLogic.validateNotNull(request.getPsreCode(), "psre Code" , audit);
		
		messageValidation = getMessage(GlobalKey.MESSAGE_AUTOSIGN_BASE64_EMPTY, null, audit);
		commonValidatorLogic.validateNotNull(request.getExcelBase64(), messageValidation, StatusCode.EMPTYBASE64);
		
		messageValidation = getMessage(GlobalKey.MESSAGE_AUTOSIGN_EXECUTE_TIME_EMPTY, null, audit);
		commonValidatorLogic.validateNotNull(request.getExecuteTime(),  messageValidation, StatusCode.EMPTYEXECUTIONTIME);
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), checkExistence, audit);
		
		MsVendor vendor = vendorValidatorLogic.validateVendorOfTenant(request.getPsreCode(), request.getTenantCode(), checkExistence, audit);
				
		if(!isValidExecuteTime(request.getExecuteTime().toUpperCase())) {
			throw new AutosignException(this.messageSource.getMessage("businesslogic.autosign.executiondatenotvalid", null, this.retrieveLocaleAudit(audit)), ReasonAutosign.EXECUTIONDATENOTVALID);
		}
		
		try {
	        decodedBytes = Base64.getDecoder().decode(request.getExcelBase64());
		} catch (Exception e) {
			throw new AutosignException(this.messageSource.getMessage("businesslogic.autosign.base64notvalid", null, this.retrieveLocaleAudit(audit)), ReasonAutosign.BASE64NOTVALID);
		}
		
		TrProcessAutosignBmH trProcessExcelBmH = new TrProcessAutosignBmH();
		
		trProcessExcelBmH.setTrxDate(new Date());
		trProcessExcelBmH.setMsTenant(tenant);
		trProcessExcelBmH.setMsVendor(vendor);
		trProcessExcelBmH.setFileName(request.getFileName());
		trProcessExcelBmH.setStatus("0");
		trProcessExcelBmH.setDtmCrt(new Date());
		trProcessExcelBmH.setExecuteType(request.getExecuteTime().toUpperCase());
		trProcessExcelBmH.setUsrCrt(audit.getCallerId());
		
		daoFactory.getProcessExcelBmDao().insertTrProcessExcelBm(trProcessExcelBmH);
		
		cloudStorageLogic.storeAutosignExcel(trProcessExcelBmH.getTrxDate(), tenant.getTenantCode(), vendor.getVendorCode(), request.getFileName(), trProcessExcelBmH.getIdProcessAutosignBmH(), decodedBytes);
		
		if ("NOW".equals(request.getExecuteTime().toUpperCase())) {
			functionComputeLogic.invokeProcessAutosignData(trProcessExcelBmH.getIdProcessAutosignBmH());
		}
		
		ImportAutosignBmDataResponse response = new ImportAutosignBmDataResponse();
		Status status = new Status();
		status.setCode(0);
		response.setStatus(status);

		return response;
	}
	
	public static boolean isValidExecuteTime(String executeTime) {
        return "NOW".equals(executeTime) || "NEXT DAY".equals(executeTime);
    }

	@Override
	public InquiryImportAutosignBmResponse inquiryImportAutosignBm(InquiryImportAutosignBmRequest request, AuditContext audit) {
		
		commonValidatorLogic.validateNotNull(request.getTenantCode(), "Tenant Code", audit);
		tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		if (!isValidStatus(request.getStatusImport().toUpperCase()) &&  !request.getStatusImport().isEmpty()) {
			throw new AutosignException(getMessage(GlobalKey.MESSAGE_AUTOSIGN_INVALID_STATUS, null, audit), ReasonAutosign.INVALID_STATUS_IMPORT);
		}
	
		Date dateStart = null;
		Date dateEnd = null;
		
		if (!isDateRangeValid(request.getImportTimeStart(), request.getImportTimeEnd(), audit)) {
			throw new AutosignException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
					new Object[] { "Import Time" }, audit), ReasonAutosign.INVALID_IMPORT_AUTOSIGN_DATE_RANGE);
		}
		
		if (StringUtils.isNotBlank(request.getImportTimeStart())) {
			dateStart = MssTool.formatStringToDate(request.getImportTimeStart() + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		if (StringUtils.isNotBlank(request.getImportTimeEnd())) {
			dateEnd = MssTool.formatStringToDate(request.getImportTimeEnd() + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		if(null == dateStart && null != dateEnd) {
			throw new UserException(getMessage("businesslogic.global.mandatory", new Object[] {"Import Time Start"}, audit), ReasonUser.PARAM_INVALID);
		}
		
		if (null == dateEnd && null != dateStart) {
			throw new UserException(getMessage("businesslogic.global.mandatory", new Object[] {"Import Time End"}, audit), ReasonUser.PARAM_INVALID);
		}
		
		if (null != dateStart && null != dateEnd && dateStart.after(dateEnd)) {
			throw new UserException(getMessage("businesslogic.message.datestartgreaterthandateend", new Object[] {"Import Time End", "Import Time Start"}, audit), ReasonUser.PARAM_INVALID);
		}
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		
		int totalResult = daoFactory.getProcessExcelBmDao().countListImportProcessAutosignBm(request, min, max, dateStart, dateEnd);
		double totalPage = Math.ceil((double) totalResult / maxRow);
		
		List<Map<String, Object>> getListImportProcessAutosignBm = daoFactory.getProcessExcelBmDao().getListImportProcessAutosignBm(request, min, max, dateStart, dateEnd);
		Iterator<Map<String, Object>> itr = getListImportProcessAutosignBm.iterator();
		List<ImportProcessAutosignBmBean> listImportProcessAutosignBm = new ArrayList<>();
		
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			ImportProcessAutosignBmBean bean = new ImportProcessAutosignBmBean();
			bean.setIdProcessAutosignH(map.get("d0").toString());
			bean.setRequestImportTime((String) map.get("d1"));
			bean.setExecuteType((String) map.get("d2"));
			bean.setStatusImport((String) map.get("d3"));
			bean.setFileName((String) map.get("d4"));
			bean.setTotal((String) map.get("d5"));
		
			listImportProcessAutosignBm.add(bean);
		}
		
		InquiryImportAutosignBmResponse response = new InquiryImportAutosignBmResponse();
		response.setImportProcessAutosignBmBean(listImportProcessAutosignBm);
		response.setTotalPage((int) totalPage);
		response.setPage(request.getPage());
		response.setTotalResult(totalResult);
		return response;
	}
	
	public static boolean isValidStatus(String status) {
        return "WAITING".equals(status) || "IN PROCESS".equals(status) || "DONE".equals(status);
    }
	
	private boolean isDateRangeValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);
		
		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return true;
		}
		if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
			try {
				sdf.parse(startDate);
			} catch (ParseException e) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
			try {
				sdf.parse(endDate);
			} catch (ParseException e) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		
		long dayCount = 0;
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			dayCount = (end.getTime() - start.getTime()) / (1000*60*60*24);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		LOG.info("Date range: {} hari", dayCount);
		return dayCount <= Long.valueOf(maxRangeDate);
	}
	
	@Override
	public DetailImportAutosignBmResponse detailImportAutosignBm(DetailImportAutosignBmRequest request,
			AuditContext audit) {
		tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		List<DetailImportAutosignBmBean> details;
		
		TrProcessAutosignBmH header = daoFactory.getProcessExcelBmDao().getProcessAutosignBmHeader(request.getTenantCode(), request.getRequestDate(), request.getFileName());
		if (null == header) {
			throw new AutosignException(getMessage("bussinesslogic.autosign.headernotfound", null, audit), ReasonAutosign.HEADER_NOT_FOUND);
		}
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);

		int totalResult = daoFactory.getProcessExcelBmDao().countListOfProcessAutosignBmDetail(header.getIdProcessAutosignBmH());
		double totalPage = Math.ceil((double) totalResult / maxRow);
		
		details = daoFactory.getProcessExcelBmDao().getListOfProcessAutosignBmDetail(header.getIdProcessAutosignBmH(), min, max);
		
		DetailImportAutosignBmResponse response = new DetailImportAutosignBmResponse();
		response.setDetails(details);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalResult);
		
		Status status = new Status();
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		
		response.setStatus(status);
		return response;
	}

	@Override
	public DownloadTemplateExcelImportAutosignBmResponse downloadTemplateExcelImportAutosignBm(
			DownloadTemplateExcelImportAutosignBmRequest request, AuditContext audit) {
		
		byte[] templateExcel = cloudStorageLogic.getTemplateExcelAutosignBm();

		if (null == templateExcel) {
		    throw new AutosignException(getMessage(GlobalKey.MESSAGE_AUTOSIGN_EMPTY_TEMPLATE_FILE, null, audit), ReasonAutosign.EMPTY_TEMPLATE_FILE);
		}
		
		String base64Document = Base64.getEncoder().encodeToString(templateExcel);
		
		DownloadTemplateExcelImportAutosignBmResponse response = new DownloadTemplateExcelImportAutosignBmResponse();
		
		response.setFilename("templateImportBm.xlsx");
		response.setExcelBase64(base64Document);
		
		
		return response;
	}
}
