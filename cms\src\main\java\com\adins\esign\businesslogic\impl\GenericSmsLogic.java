package com.adins.esign.businesslogic.impl;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.SocketTimeoutException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.transport.http.HTTPConduit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SmsLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.model.custom.AddressSmsVfirstBean;
import com.adins.esign.model.custom.GenerateTokenVfirstResponse;
import com.adins.esign.model.custom.SendSmsVfirstRequest;
import com.adins.esign.model.custom.SendSmsVfirstResponse;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.SmsVfirstBean;
import com.adins.esign.model.custom.UserVfirstBean;
import com.adins.esign.model.custom.vfirst.VFirstSmsProperties;
import com.adins.esign.model.custom.vfirst.VFirstSmsTokenBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.SendSmsResponse;
import com.adins.esign.webservices.model.SendSmsValueFirstRequestBean;
import com.adins.exceptions.SmsException;
import com.adins.exceptions.SmsException.ReasonSms;
import com.adins.exceptions.StatusCode;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.google.gson.Gson;

@Transactional
@Component
public class GenericSmsLogic extends BaseLogic implements SmsLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericSmsLogic.class);
	
	@Autowired private Gson gson;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	
	@Value("${sms.vFirstVersion}") private String smsVFirstVersion;
	@Value("${sms.vFirstFrom}") private String smsVFirstFrom;
	@Value("${sms.vFirstTag}") private String smsVFirstTag;
	
	@Value("${vfirst.generatetoken.url}") private String generateTokenUrl;
	@Value("${vfirst.deletetoken.url}") private String deleteTokenUrl;
	@Value("${vfirst.sendsms.url}") private String sendSmsUrl;
	@Value("${vfirst.username.json}") private String username;
	@Value("${vfirst.pass.json}") private String pass;
	
	public static final String VFIRST_QUERYSTRING_FORMAT = "?username=%1$s&password=%2$s&to=%3$s&from=%4$s&text=%5$s";
	
	private static Map<String, VFirstSmsTokenBean> vFirstTokens = new HashMap<>();
	
	@Override
	public SendSmsResponse sendSms(SendSmsValueFirstRequestBean requestBean) {
		SendSmsResponse response = new SendSmsResponse();
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		VFirstSmsProperties tenantSmsProperties = getTenantSmsProperties(requestBean.getTenant());
		
		try {
			String token = this.getTenantSmsToken(requestBean.getTenant(), tenantSmsProperties);
			response = this.sendSmsVfirst(tenantSmsProperties, requestBean, token, nextTrxNo);
		} catch (Exception e) {
			String[] msgArr = e.getMessage().split("@");
			LOG.error("Send SMS Error: {}", msgArr[0], e);
			response.setGuid(msgArr.length > 1 ? msgArr[1] : null);
			response.setErrorMsg(msgArr[0]);
			response.setTrxNo(nextTrxNo);
			if (msgArr[0].equals(messageSource.getMessage("vfirst.28681", null, this.retrieveDefaultLocale()))) {
				response.setErrorCode("28681");
			} else if (msgArr[0].equals(messageSource.getMessage("vfirst.28682", null, this.retrieveDefaultLocale()))) {
				response.setErrorCode("28682");
			} else {
				response.setErrorCode("408");
			}
			return response;
		}
		return response;
	}
	
	
	@Override
	public SendSmsResponse sendSms(SendSmsValueFirstRequestBean requestBean,SigningProcessAuditTrailBean auditTrailBean) {
		SendSmsResponse response = new SendSmsResponse();
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		VFirstSmsProperties tenantSmsProperties = getTenantSmsProperties(requestBean.getTenant());
		MsLov lovNotificationMedia = new MsLov();
		MsLov lovNotificationVendor = new MsLov();
		lovNotificationMedia = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_MESSAGE_MEDIA, GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
		lovNotificationVendor = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);
		
		try {

			String token = this.getTenantSmsToken(requestBean.getTenant(), tenantSmsProperties);
			response = this.sendSmsVfirst(tenantSmsProperties, requestBean, token, nextTrxNo);
			
			TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
			auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
			auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
			auditTrail.setEmail(auditTrailBean.getEmail());
			auditTrail.setAmMsUser(auditTrailBean.getUser());
			auditTrail.setMsTenant(auditTrailBean.getTenant());
			auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
			auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
			auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
			auditTrail.setOtpCode(auditTrailBean.getOtpCode());
			auditTrail.setResultStatus("1");
			auditTrail.setDtmCrt(new Date());
			auditTrail.setUsrCrt(auditTrailBean.getEmail());
			auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
			auditTrail.setNotes(auditTrailBean.getNotes());
			auditTrail.setNotificationMedia(lovNotificationMedia.getDescription());
			auditTrail.setNotificationVendor(lovNotificationVendor.getDescription());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			
			if (auditTrailBean.getDocumentDs() != null ) {
				for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
					TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
					auditTrailDetail.setDtmCrt(new Date());
					auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
					auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
					auditTrailDetail.setTrDocumentD(docD);
					daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);;
				}
			}
			
			
		} catch (Exception e) {
			TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
			auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
			auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
			auditTrail.setEmail(auditTrailBean.getEmail());
			auditTrail.setAmMsUser(auditTrailBean.getUser());
			auditTrail.setMsTenant(auditTrailBean.getTenant());
			auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
			auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
			auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
			auditTrail.setOtpCode(auditTrailBean.getOtpCode());
			auditTrail.setResultStatus("0");
			auditTrail.setDtmCrt(new Date());
			auditTrail.setUsrCrt(auditTrailBean.getEmail());
			auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
			auditTrail.setNotes(auditTrailBean.getNotes());
			auditTrail.setNotificationMedia(lovNotificationMedia.getDescription());
			auditTrail.setNotificationVendor(lovNotificationVendor.getDescription());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			
			if (auditTrailBean.getDocumentDs() != null ) {
				for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
					TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
					auditTrailDetail.setDtmCrt(new Date());
					auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
					auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
					auditTrailDetail.setTrDocumentD(docD);
					daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);;
				}
			}
			
			
			String[] msgArr = e.getMessage().split("@");
			LOG.error("Send SMS Error: {}", msgArr[0], e);
			response.setGuid(msgArr.length > 1 ? msgArr[1] : null);
			response.setErrorMsg(msgArr[0]);
			response.setTrxNo(nextTrxNo);
			if (msgArr[0].equals(messageSource.getMessage("vfirst.28681", null, this.retrieveDefaultLocale()))) {
				response.setErrorCode("28681");
			} else if (msgArr[0].equals(messageSource.getMessage("vfirst.28682", null, this.retrieveDefaultLocale()))) {
				response.setErrorCode("28682");
			} else {
				response.setErrorCode("408");
			}
			return response;
		}
		return response;
	}
	
	
	private SendSmsResponse sendSmsVfirst(VFirstSmsProperties smsProperties, SendSmsValueFirstRequestBean requestBean, String token, long nextTrxNo) throws IOException {
		SendSmsResponse response = new SendSmsResponse();
		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		
		SendSmsVfirstRequest request = new SendSmsVfirstRequest();
		request.setVer(smsVFirstVersion);
		UserVfirstBean user = new UserVfirstBean();
		request.setUser(user);
		
		List<SmsVfirstBean> listSms = new ArrayList<>();
		SmsVfirstBean smsBean = new SmsVfirstBean();
		smsBean.setCoding("1");
		smsBean.setUdh("0");
		smsBean.setText(requestBean.getMsg());
		smsBean.setProperty("0");
		smsBean.setId(String.valueOf(nextTrxNo));
		response.setTrxNo(nextTrxNo);
		
		List<AddressSmsVfirstBean> listAddress = new ArrayList<>();
		
		AddressSmsVfirstBean address = new AddressSmsVfirstBean();
		address.setFrom(smsProperties.getSender());
		address.setTag(smsVFirstTag + " " + requestBean.getTenant().getTenantCode());
		String to = Character.compare(requestBean.getPhoneNo().charAt(0), '0') == 0 ? "62" + requestBean.getPhoneNo().substring(1) : requestBean.getPhoneNo();
		address.setTo(to);
		address.setSeq("1");
		listAddress.add(address);
		
		smsBean.setAddress(listAddress);
		
		listSms.add(smsBean);
		
		request.setSms(listSms);
		
		String body = gson.toJson(request);
		LOG.info("Send {} SMS request: {}", requestBean.getTenant().getTenantCode(), body);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.AUTHORIZATION, "Bearer " + token);
		mapHeader.add(HttpHeaders.CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);

		String result = null;
		InputStreamReader isReader = null;
		try {
			WebClient client = WebClient.create(sendSmsUrl).headers(mapHeader);
			Response endpointResponse = client.post(body);
			isReader = new InputStreamReader((InputStream) endpointResponse.getEntity());
			result = IOUtils.toString(isReader);
			LOG.info("Send {} SMS response: {}", requestBean.getTenant().getTenantCode(), result);
		} catch (Exception e) {
			LOG.error("Send SMS VFirst exception: {}", e.getMessage(), e);
			String message = "vfirst.408";
			throw new SmsException(message, ReasonSms.FAIL_SEND_SMS);
		}

		SendSmsVfirstResponse vfirstResponse = gson.fromJson(result, SendSmsVfirstResponse.class);
		response.setGuid(vfirstResponse.getMessageack().getGuid().getGuid());
		if (vfirstResponse.getMessageack().getErr() != null
				|| vfirstResponse.getMessageack().getGuid().getError() != null) {
			String message = vfirstResponse.getMessageack().getErr() != null
					? vfirstResponse.getMessageack().getErr().getDesc()
					: String.valueOf(vfirstResponse.getMessageack().getGuid().getError().getCode());
			if (StringUtils.isNumeric(message) && !message.equals("0")) {
				response.setErrorCode(message);
				message = messageSource.getMessage("vfirst." + message, null, this.retrieveDefaultLocale()) + "@"
						+ response.getGuid();
			}
			throw new SmsException(message, ReasonSms.FAIL_SEND_SMS);
		}
		
		return response;
	}
	
	private String getTenantSmsToken(MsTenant tenant, VFirstSmsProperties tenantProperties) throws IOException {
		
		VFirstSmsTokenBean tenantToken = vFirstTokens.get(tenant.getTenantCode());
		// No tenant token in memory
		if (null == tenantToken) {
			return generateNewTenantToken(tenant, tenantProperties);
		}
		
		boolean expired = isTenantTokenExpired(tenant);
		// Tenant token existed in memory but expired
		if (expired) {
			return generateNewTenantToken(tenant, tenantProperties);
		}
		
		// Tenant token existed in memory and is not expired
		LOG.info("Tenant {}, SMS token existed. Using token: {}", tenant.getTenantCode(), tenantToken.getToken());
		return tenantToken.getToken();
	}
	
	private String generateNewTenantToken(MsTenant tenant, VFirstSmsProperties tenantSmsProperties) throws IOException {
		GenerateTokenVfirstResponse response = generateToken(tenant, tenantSmsProperties);
		String token = response.getToken();
		
		VFirstSmsTokenBean tokenBean = new VFirstSmsTokenBean();
		tokenBean.setToken(token);
		tokenBean.setExpiredDate(response.getExpiryDate());
		
		LOG.info("Tenant {}, Saving new SMS token: {}", tenant.getTenantCode(), token);
		vFirstTokens.put(tenant.getTenantCode(), tokenBean);
		return token;
	}
	
	private boolean isTenantTokenExpired(MsTenant tenant) {
		VFirstSmsTokenBean tenantToken = vFirstTokens.get(tenant.getTenantCode());
		if (null == tenantToken) {
			return true;
		}
		
		if (StringUtils.isBlank(tenantToken.getExpiredDate())) {
			return true;
		}
		
		// Cek apakah expirednya sudah selisih kurang dari 2 hari dari waktu request SMS
		LocalDate localDate = LocalDate.now();
		String lexpiredDate = tenantToken.getExpiredDate().substring(0, 10);
		
		LocalDate expiredDate = LocalDate.parse(lexpiredDate);
		
		Duration diff = Duration.between(localDate.atStartOfDay(), expiredDate.atStartOfDay());
		long diffDays = diff.toDays();
		
		String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_TOKEN_DATE_DIFF_SMS);
		long dateDiff = Long.parseLong(gsValue);
		return diffDays < dateDiff;
	}
	
	private GenerateTokenVfirstResponse generateToken(MsTenant tenant, VFirstSmsProperties tenantSmsProperties) throws IOException {
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.AUTHORIZATION, HttpHeaders.buildBasicAuthorization(tenantSmsProperties.getUsername(), tenantSmsProperties.getPassword()));
		
		WebClient client = WebClient.create(generateTokenUrl).headers(mapHeader);
		Response response = client.post(null);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		
		String result = IOUtils.toString(isReader);
		LOG.info("Tenant {}, Generate VFirst token result: {}", tenant.getTenantCode(), result);
		GenerateTokenVfirstResponse responseBean = gson.fromJson(result, GenerateTokenVfirstResponse.class);
		
		String dateString = responseBean.getExpiryDate();
		String indoDateString = this.convertToGmt(dateString);
	      
		if (StringUtils.isNotBlank(responseBean.getToken()) || StringUtils.isNotBlank(responseBean.getExpiryDate())) {
			responseBean.setExpiryDate(indoDateString);
			return responseBean;
		}
		
		throw new SmsException(result, ReasonSms.FAIL_GENERATE_TOKEN);
		
	}
	
	private String convertToGmt(String dateString) {
	    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	    Date date = null;
	    try {
			date = dateFormat.parse(dateString);
		} catch (ParseException e) {
			LOG.info("convertToGmt failed");
		}
	    
	    Calendar calendar = Calendar.getInstance();
	    calendar.setTime(date);
	    
	    calendar.add(Calendar.MINUTE, 30);
	    calendar.add(Calendar.HOUR, 1);

	    Date indoDate = calendar.getTime();
	    
	    return dateFormat.format(indoDate);
	}
	
	private VFirstSmsProperties getTenantSmsProperties(MsTenant tenant) {
		MsTenantSettings usernameSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "SMS_VFRIST_USERNAME");
		if (null == usernameSettings) {
			return getApplicationSmsProperties(tenant);
		}
		
		MsTenantSettings passwordSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "SMS_VFRIST_PASSWORD");
		if (null == passwordSettings) {
			return getApplicationSmsProperties(tenant);
		}
		
		MsTenantSettings senderSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "SMS_VFRIST_SENDER");
		if (null == senderSettings) {
			return getApplicationSmsProperties(tenant);
		}
		
		LOG.debug("Tenant {} uses VFirst SMS properties from tenant settings", tenant.getTenantCode());
		return new VFirstSmsProperties(usernameSettings.getSettingValue(), passwordSettings.getSettingValue(), senderSettings.getSettingValue());
	}
	
	private VFirstSmsProperties getApplicationSmsProperties(MsTenant tenant) {
		LOG.debug("Tenant {} uses VFirst SMS properties from application", tenant.getTenantCode());
		return new VFirstSmsProperties(username, pass, smsVFirstFrom);
	}
	
	
}
