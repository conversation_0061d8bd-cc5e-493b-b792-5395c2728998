package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class ReadEmailJob {
	private static final Logger LOG = LoggerFactory.getLogger(ReadEmailJob.class);
	private static final String SCHEDULER = "SCHEDULER";
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void runReadEmail() {
		try {
			LOG.info("Job Read Email started");
			AuditContext audit = new AuditContext(SCHEDULER);
			schedulerLogic.readEmail(audit);
			LOG.info("Job Read Email finished");
		} catch (Exception e) {
			LOG.error("Error on running Read Email Job", e);
		}
	}
}
