package com.adins.logback;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;

public class SubsystemFilter extends Filter<ILoggingEvent> {

	@Override
	public FilterReply decide(ILoggingEvent event) {
		if(event.getMDCPropertyMap().get("subsystemContext") != null) {
			return FilterReply.ACCEPT;
		}
		return FilterReply.DENY;
	}

}
