package com.adins.esign.businesslogic.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.mail.MessagingException;
import javax.persistence.EntityNotFoundException;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.utils.ExceptionUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.NoSuchMessageException;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.ExcelLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.job.QueuePublisher;
import com.adins.esign.model.MsBalancevendoroftenant;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrBackgroundProcessFail;
import com.adins.esign.model.TrBalanceDailyRecap;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrBalanceTopUp;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrSchedulerJob;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.BalanceBean;
import com.adins.esign.model.custom.BalanceCheckQueueBean;
import com.adins.esign.model.custom.BalanceMutationBean;
import com.adins.esign.model.custom.BalanceMutationExternalBean;
import com.adins.esign.model.custom.BalanceVendoroftenantBean;
import com.adins.esign.model.custom.EmailAttachmentBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.ListBalanceBean;
import com.adins.esign.model.custom.ListTopupBalanceBean;
import com.adins.esign.model.custom.VendorTenantBean;
import com.adins.esign.model.custom.BalanceTenantBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.AnnotationValidatorLogic;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.esign.webservices.model.AddBalanceTypeRequest;
import com.adins.esign.webservices.model.AddBalanceTypeResponse;
import com.adins.esign.webservices.model.BalanceEmbedRequest;
import com.adins.esign.webservices.model.BalanceRequest;
import com.adins.esign.webservices.model.BalanceResponse;
import com.adins.esign.webservices.model.CheckThresholdRequest;
import com.adins.esign.webservices.model.CheckThresholdResponse;
import com.adins.esign.webservices.model.DownloadListBalanceMutationEmbedRequest;
import com.adins.esign.webservices.model.DownloadListBalanceMutationRequest;
import com.adins.esign.webservices.model.DownloadListBalanceMutationResponse;
import com.adins.esign.webservices.model.ExtendTopUpBalanceRequest;
import com.adins.esign.webservices.model.GetListBalanceTypeExternalRequest;
import com.adins.esign.webservices.model.GetListBalanceTypeExternalResponse;
import com.adins.esign.webservices.model.GetListTopupBalanceRequest;
import com.adins.esign.webservices.model.GetListTopupBalanceResponse;
import com.adins.esign.webservices.model.GetListBalanceMutationEmbedRequest;
import com.adins.esign.webservices.model.GetListBalanceMutationExternalRequest;
import com.adins.esign.webservices.model.GetListBalanceMutationExternalResponse;
import com.adins.esign.webservices.model.ListBalanceHistoryRequest;
import com.adins.esign.webservices.model.ListBalanceHistoryResponse;
import com.adins.esign.webservices.model.ListBalanceTenantRequest;
import com.adins.esign.webservices.model.ListBalanceTenantResponse;
import com.adins.esign.webservices.model.ListBalanceTypeByVendorAndTenantRequest;
import com.adins.esign.webservices.model.ListBalanceTypeByVendorAndTenantResponse;
import com.adins.esign.webservices.model.ListBalanceVendoroftenantRequest;
import com.adins.esign.webservices.model.ListBalanceVendoroftenantResponse;
import com.adins.esign.webservices.model.LovListRequest;
import com.adins.esign.webservices.model.SaldoConfigurationRequest;
import com.adins.esign.webservices.model.SaldoConfigurationResponse;
import com.adins.esign.webservices.model.SignBalanceAvailabilityRequest;
import com.adins.esign.webservices.model.SignBalanceAvailabilityResponse;
import com.adins.esign.webservices.model.UpdateBalanceTenantRequest;
import com.adins.esign.webservices.model.UpdateBalanceTenantResponse;
import com.adins.esign.webservices.model.UpdateRefNumberRequest;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.EmbedMsgException;
import com.adins.exceptions.FormatException;
import com.adins.exceptions.LovException;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.SaldoException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.LovException.Reason;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.exceptions.PaymentSignTypeException.ReasonPaymentSignType;
import com.adins.exceptions.PaymentSignTypeException;
import com.adins.exceptions.SaldoException.ReasonSaldo;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.EmbedMsgException.ReasonEmbedMsg;
import com.adins.exceptions.FormatException.ReasonFormat;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
@Transactional
public class GenericSaldoLogic extends BaseLogic implements SaldoLogic{
	private static final Logger LOG = LoggerFactory.getLogger(GenericSaldoLogic.class);
	
	// Business logic
	@Autowired private CommonLogic commonLogic;
	@Autowired private ExcelLogic excelLogic;
	@Autowired private SchedulerLogic schedulerLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private VendorLogic vendorLogic;
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private MessageTemplateLogic msgTemplateLogic;
	
	// Validator
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private VendorValidatorLogic vendorValidatorLogic;
	@Autowired private BalanceValidatorLogic balanceValidatorLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	@Autowired private AnnotationValidatorLogic annotationValidatorLogic;
	
	private static final String PARAM_BAL_TYPE_CODE = "balanceTypeCode";
	private static final String PARAM_TRX_DATE = "Transaction Date";
	private static final String PARAM_BAL_EXP_DATE = "Balance Expired Date";
	private static final String PARAM_BAL_PRICE = "Balance Price";
	private static final String PARAM_NOTES = "notes";
	
	private static final String CONST_VENDOR_CODE = "Vendor Code";
	
	private static final String BALMUT_FILENAME_PREFIX = "BalanceMutation";
	
	@Override
	public BalanceResponse getBalance(BalanceRequest request, AuditContext audit) {
		MsTenant tenant = tenantLogic.getTenantByCode(request.getTenantCode(), audit);
		MsVendor vendor = vendorLogic.getVendorByCode(request.getVendorCode(), audit);
		
		this.tenantVendorValidation(request.getTenantCode(), request.getVendorCode(), tenant, vendor, audit);

		return this.getBalanceByVendorAndTenant(request.getBalanceType(), tenant, vendor, audit);

	}
	
	@Override
	public BalanceResponse getBalanceEmbed(BalanceEmbedRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getMsg())) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		if (null == msgBean) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_INVALID, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		if (null == tenant) {
			if (StringUtils.isBlank(msgBean.getTenantCode())) {
				throw new TenantException(messageSource.getMessage("businesslogic.tenant.tenantcodeempty"
						, null, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
			} else {
				throw new TenantException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_TENANT_NOT_EXIST
						, new Object[] {msgBean.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
			}
		}
		MsVendor vendor = vendorLogic.getVendorByCode(request.getVendorCode(), audit);
		
		this.tenantVendorValidation(msgBean.getTenantCode(), request.getVendorCode(), tenant, vendor, audit);

		return this.getBalanceByVendorAndTenant(request.getBalanceType(), tenant, vendor, audit);

	}
	
	private void tenantVendorValidation(String tenantCode, String vendorCode, MsTenant tenant, MsVendor vendor, 
			AuditContext audit) {
		if (StringUtils.isBlank(tenantCode) || null == tenant) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {MsTenant.TENANT_CODE_HBM}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_CODE_EMPTY);
		}
		if (StringUtils.isBlank(vendorCode) || null == vendor) {
			throw new VendorException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {MsVendor.VENDOR_CODE_HBM}, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_CODE_INVALID);
		}
	}
	
	private BalanceResponse getBalanceByVendorAndTenant(String balanceTypeCode, MsTenant tenant, MsVendor vendor,
			AuditContext audit) {
		BalanceResponse response = new BalanceResponse();
		List<BalanceBean> listBalanceBean = new ArrayList<>();

		if(StringUtils.isBlank(balanceTypeCode)) {
			List<MsBalancevendoroftenant> listBalance = daoFactory.getVendorDao().getListBalanceByVendorTenant
					(tenant.getTenantCode(), vendor.getVendorCode());
			for (MsBalancevendoroftenant bvot : listBalance) {
				MsLov balanceTypeLov = bvot.getMsLov();
				BalanceBean balanceBean = new BalanceBean();

				this.setResponseBalanceByType(balanceBean, bvot.getMsTenant(), bvot.getMsVendor(), balanceTypeLov);
				
				listBalanceBean.add(balanceBean);
			}
		}
		else {
			MsLov balanceTypeLov = commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceTypeCode, audit);
			BalanceBean balanceBean = new BalanceBean();
			
			this.setResponseBalanceByType(balanceBean, tenant, vendor, balanceTypeLov);
			
			listBalanceBean.add(balanceBean);
		}
		response.setListBalance(listBalanceBean);
		return response;
	}
	
	private void setResponseBalanceByType(BalanceBean balanceBean, MsTenant tenant, MsVendor vendor,
			MsLov balanceTypeLov) {
		BigInteger balance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenant
				(tenant, vendor, balanceTypeLov);
		
		balanceBean.setLovGroup(balanceTypeLov.getLovGroup());
		balanceBean.setCode(balanceTypeLov.getCode());
		balanceBean.setDescription(balanceTypeLov.getDescription());
		balanceBean.setCurrentBalance(balance);	
	}

	@Override
	public ListBalanceHistoryResponse getListBalanceMutation(ListBalanceHistoryRequest request, AuditContext audit) throws ParseException {
		MsTenant tenant = tenantLogic.getTenantByCode(request.getTenantCode(), audit);
		MsVendor vendor = vendorLogic.getVendorByCode(request.getVendorCode(), audit);
		this.tenantVendorValidation(request.getTenantCode(), request.getVendorCode(), tenant, vendor, audit);
	
		if (StringUtils.isBlank(request.getBalanceType())) {
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_PLEASE_CHOOSE_BALANCE_TYPE, null, this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}
		MsLov balanceTypeLov = commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, request.getBalanceType(), audit);
		
		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);
		//penjagaan batas max hari
		if (!isDateRangeValid(request.getTransactionDateStart(), request.getTransactionDateEnd(), audit)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE_GENERAL_SETTING,
					new Object[] {GlobalVal.CONST_TRANSAKSI,maxRangeDate}, this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_DATE_RANGE);
		}
		
		if (StringUtils.isNotBlank(request.getOfficeCode())) {
			MsOffice tenantOffice = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(request.getOfficeCode(), request.getTenantCode());
			if (tenantOffice == null) {
				throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_BRANCH_NOT_EXIST, null, this.retrieveLocaleAudit(audit)), ReasonDocument.BRANCH_NOT_EXIST);
			}
		}
		
		Date dateStart = MssTool.formatStringToDate(request.getTransactionDateStart(), GlobalVal.DATE_FORMAT);

		Date dateEnd = MssTool.formatStringToDate(request.getTransactionDateEnd(), GlobalVal.DATE_FORMAT);
		
		ListBalanceHistoryResponse response = new ListBalanceHistoryResponse();

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		
		List<BalanceMutationBean> result = daoFactory.getBalanceMutationDao().getListBalanceMutation(tenant, vendor, balanceTypeLov, 
												request.getTransactionType(), dateStart, dateEnd, request.getDocumentType(), request.getReferenceNo(), request.getDocumentName(),
												min, max, request.getOfficeCode(), request.getStatus());
		
		int totalResult = daoFactory.getBalanceMutationDao().countBalanceMutation(tenant, vendor, balanceTypeLov,
												request.getTransactionType(), dateStart, dateEnd, request.getDocumentType(), request.getReferenceNo(), request.getDocumentName(), request.getOfficeCode(),
												request.getStatus());
		double totalPage =  Math.ceil((double) totalResult / maxRow);
		
		response.setListMutation(result);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalResult);
		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		
		return response;
	}
	
	private boolean isDateRangeValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		sdf.setLenient(false);

		String maxRangeDate = daoFactory.getGeneralSettingDao()
				.getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);

		// If both dates are blank, return true
		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return true;
		}

		// Validate startDate if provided
		if (StringUtils.isNotBlank(startDate)) {
			isValidDateFormat(startDate, audit);
			parseDate(startDate, sdf, audit);
		}

		// Validate endDate if provided
		if (StringUtils.isNotBlank(endDate)) {
			isValidDateFormat(endDate, audit);
			parseDate(endDate, sdf, audit);
		}

		if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
			return true;
		}

		// If both dates are provided, check the date range
		long dayCount = 0;
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			dayCount = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}

		if (dayCount < 0) {
			throw new CommonException(this.messageSource.getMessage("businesslogic.error.invaliddatevalue",
					null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_RANGE);
		}

		LOG.info("Date range: {} hari", dayCount);
		return dayCount <= Long.parseLong(maxRangeDate);
	}

	private void isValidDateFormat(String date, AuditContext audit) {
		if (!date.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
			throw new CommonException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH, null,
							retrieveLocaleAudit(audit)),
					ReasonCommon.INVALID_DATE_FORMAT);
		}
	}

	private Date parseDate(String date, SimpleDateFormat sdf, AuditContext audit) {
		try {
			return sdf.parse(date);
		} catch (ParseException e) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
							retrieveLocaleAudit(audit)),
					ReasonDocument.READ_WRITE_ERROR);
		}
	}
	
	private void isDateCompareValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		String validateMessage = "";
		if (StringUtils.isBlank(startDate)) {
			validateMessage = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] { PARAM_TRX_DATE }, audit);
			commonValidatorLogic.validateNotNull(startDate, validateMessage, StatusCode.MANDATORY_PARAMETER);
		}
		if (StringUtils.isBlank(endDate)) {
			validateMessage = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] { PARAM_BAL_EXP_DATE }, audit);
			commonValidatorLogic.validateNotNull(endDate, validateMessage, StatusCode.MANDATORY_PARAMETER);
		}
		
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			if (start.after(end)) {
				throw new CommonException(this.messageSource.getMessage("businesslogic.global.datecompareinvalid",
						 new String[] { PARAM_TRX_DATE,PARAM_BAL_EXP_DATE }, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_RANGE);
			}
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		
	}
	
	@Override
	public SaldoConfigurationResponse configureSaldo(SaldoConfigurationRequest request, AuditContext audit) throws ParseException, IllegalAccessException, NoSuchMessageException {
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(audit.getCallerId());
		BigDecimal balancePrice = new BigDecimal("0.0");
		String validateMessage = "";
		
		validateMessage = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] { PARAM_BAL_PRICE }, audit);
		commonValidatorLogic.validateNotNull(request.getBalancePrice(), validateMessage, StatusCode.MANDATORY_PARAMETER);
		
		validateMessage = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] { PARAM_BAL_EXP_DATE }, audit);
		commonValidatorLogic.validateNotNull(request.getBalanceExpiredDate(), validateMessage, StatusCode.MANDATORY_PARAMETER);
		
		
		try {
			balancePrice = BigDecimal.valueOf(request.getBalancePrice());
		} catch (Exception e) {
			throw new FormatException(getMessage("service.global.notvalidnumeric", new Object[] {PARAM_BAL_PRICE}, audit), ReasonFormat.INVALID_FORMAT);
		}
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		Date balanceExpiredDate = sdf.parse(request.getBalanceExpiredDate());
		
		SaldoConfigurationResponse response = new SaldoConfigurationResponse();
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, "T" + request.getBalanceTypeCode());
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, request.getBalanceTypeCode());
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		Date trxDate = sdf.parse(request.getTrxDate());
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		BigInteger currentBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenant(tenant, vendor, balanceType);

		if (request.getQty()<= 0 ) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MUSTGREATERTHAN, new Object[] {"Quantity", "0"},audit), ReasonUser.INVALID_FORMAT);
		}
		
		if (balancePrice.compareTo(BigDecimal.ZERO) <= 0 ) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MUSTGREATERTHAN, new Object[] {PARAM_BAL_PRICE, "0"},audit), ReasonUser.INVALID_FORMAT);
		}
		
		isDateCompareValid(request.getTrxDate(), request.getBalanceExpiredDate(), audit);

		this.prepareInsertBalanceMutation(request, trxType, balanceType, tenant, vendor, audit);
		
		insertBalanceMutationTopUp(null, null, null, balanceType, trxType, tenant, vendor,
				trxDate, request.getRefNo(), request.getQty(), String.valueOf(nextTrxNo), user, request.getNotes(), null, audit,balanceExpiredDate,balancePrice);
		
		sendEmailTopup(audit, tenant, balanceType.getDescription(), request.getRefNo(), request.getNotes(), request.getQty(), currentBalance);
		
		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		
		return response;
	}
	
	private void sendEmailTopup(AuditContext audit, MsTenant tenant, String balanceName, String refNo, String notes, int qty, BigInteger currentBalance) {		
		Map<String, Object> topupMap = new HashMap<>();
        topupMap.put("tenantName", tenant.getTenantName());
        topupMap.put("balance", balanceName);
        topupMap.put("refNo", refNo);
        topupMap.put(PARAM_NOTES, notes);
        topupMap.put("qty", qty);
        topupMap.put("before", currentBalance);
        topupMap.put("after", currentBalance.add(BigInteger.valueOf(qty)));

        Map<String, Object> templateParam = new HashMap<>();
        templateParam.put("topup", topupMap);

        MsMsgTemplate template = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_TOPUP_SUCCESS, templateParam);
        
        String[] recipient = tenant.getEmailReminderDest().split(",");
        
        AmGeneralsetting picOperation = daoFactory.getGeneralSettingDao().getGsObjByCode("PIC_OPERATION_ADINS");
        
        String[] recipientOperation = picOperation.getGsValue().split(";");

        EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setSubject(template.getSubject());
		emailInfo.setBodyMessage(template.getBody());
		emailInfo.setTo(recipient);
		emailInfo.setCc(recipientOperation);
		try {
			emailSenderLogic.sendEmailWithoutAsync(emailInfo, null);
		} catch (Exception e) {
			TrBackgroundProcessFail processFail = new TrBackgroundProcessFail();
			processFail.setProcessName(template.getSubject());
			processFail.setProcessLocation("Top Up Saldo");
			processFail.setStackTrace(Arrays.toString(e.getStackTrace()));
			processFail.setProcessStatus("0");
			processFail.setUsrCrt(audit.getCallerId());
			processFail.setDtmCrt(new Date());
			
			daoFactory.getBackgroundProcessFailDao().insertTrBackgroundProcessFail(processFail);
			
			EmailAttachmentBean[] attachments = null;
			if (null != e) {
				byte[] stackTraceFile = buildStackTraceTextFile(e);
				String filename = buildStackTraceFileName(processFail.getProcessLocation());
				EmailAttachmentBean attachment = new EmailAttachmentBean(stackTraceFile, filename);
				attachments = new EmailAttachmentBean[] {attachment};
			}
			
			StringBuilder sb = new StringBuilder();
			sb.append("<tr>");
			sb.append("<td style=\"font-family:Arial, Helvetica, sans-serif; color:#222; background-color:#fff;\">");
			sb.append("<ul style=\"list-style-type:disc; padding-left: 20px; margin: 0;\">");

			sb.append("<li style=\"margin-bottom:8px;\">");
			sb.append("<b><span style=\"display:inline-block; width:130px;\">Service Name</span></b>: ").append(balanceName).append("</li>");

			sb.append("<li style=\"margin-bottom:8px;\">");
			sb.append("<b><span style=\"display:inline-block; width:130px;\">Invoice No</span></b>: ").append(refNo).append("</li>");

			sb.append("<li style=\"margin-bottom:8px;\">");
			sb.append("<b><span style=\"display:inline-block; width:130px;\">Catatan</span></b>: ").append(notes).append("</li>");

			sb.append("<li style=\"margin-bottom:8px;\">");
			sb.append("<b><span style=\"display:inline-block; width:130px;\">Jumlah</span></b>: ").append(qty).append("</li>");

			sb.append("<li style=\"margin-bottom:8px;\">");
			sb.append("<b><span style=\"display:inline-block; width:130px;\">Saldo Sebelum</span></b>: ").append(currentBalance).append("</li>");

			sb.append("<li style=\"margin-bottom:8px;\">");
			sb.append("<b><span style=\"display:inline-block; width:130px;\">Saldo Sesudah</span></b>: ").append(currentBalance.add(BigInteger.valueOf(qty))).append("</li>");

			sb.append("</ul>");
			sb.append("</td>");
			sb.append("</tr>");
			
	        Map<String, Object> templateParameter = new HashMap<>();
	        templateParameter.put("templateSubject", template.getSubject());
	        templateParameter.put("module", sb.toString());
	        templateParameter.put(PARAM_NOTES, "Gagal saat Top Up Saldo untuk " + tenant.getTenantName());

	        MsMsgTemplate templateError = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_SEND_EMAIL_FAIL, templateParameter);

	        EmailInformationBean emailInfoError = new EmailInformationBean();
			emailInfoError.setSubject(templateError.getSubject());
			emailInfoError.setBodyMessage(templateError.getBody());
			emailInfoError.setTo(recipientOperation);
			
			try {
				emailSenderLogic.sendEmail(emailInfoError, attachments);
			} catch (MessagingException e1) {
				LOG.error("Send email failed", e);
			}
		}
	}
	
	private String buildStackTraceFileName(String errorLocation) {
		String currentTime = MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_TIME_FORMAT_SEQ);
		StringBuilder filename = new StringBuilder()
				.append(StringUtils.upperCase(errorLocation)).append("_")
				.append(currentTime)
				.append(".txt");
		return filename.toString();
	}
	
	private byte[] buildStackTraceTextFile(Exception e) {
		String stackTrace = ExceptionUtils.getStackTrace(e);
		String base64 = Base64.getEncoder().encodeToString(stackTrace.getBytes());
		return Base64.getDecoder().decode(base64);
	}
	
	private void prepareInsertBalanceMutation(SaldoConfigurationRequest request, MsLov trxType, MsLov balanceType, MsTenant tenant, MsVendor vendor, AuditContext audit) throws IllegalAccessException, NoSuchMessageException {
		if (trxType == null || balanceType == null) {
			throw new LovException(this.messageSource.getMessage("service.global.lovnotvalid", new Object[] {request.getBalanceTypeCode()}, 
						this.retrieveLocaleAudit(audit)), Reason.ERROR_EXIST);
		}
		
		if (tenant == null) {
			throw new EntityNotFoundException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_TENANT_NOT_EXIST, 
						new Object[] {request.getTenantCode()}, this.retrieveLocaleAudit(audit)));
		}
		
		if (vendor == null) {
			throw new EntityNotFoundException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_VENDOR_NOT_EXIST, 
					new Object[] {request.getVendorCode()}, this.retrieveLocaleAudit(audit)));
		}
		if (vendor.getMsLovVendorType().getCode().equals("E-MATERAI")) {
			throw new IllegalAccessException(this.messageSource.getMessage("businesslogic.saldo.vendortypeinvalid", 
					null, this.retrieveLocaleAudit(audit)));
		}
	}
	
	@Override
	public void insertBalanceMutation(TrStampDuty stampDuty, TrDocumentH docH, TrDocumentD docD, MsLov balanceType, 
		MsLov trxType, MsTenant tenant, MsVendor vendor, Date trxDate, String refNo,
		int qty, String trxNo, AmMsuser user, String notes, String vendorTrxNo, MsOffice office, MsBusinessLine businessLine, AuditContext audit){

		TrBalanceMutation bm = new TrBalanceMutation();
		bm.setTrStampDuty(stampDuty);
		bm.setTrDocumentH(docH);
		bm.setTrDocumentD(docD);
		bm.setMsLovByLovBalanceType(balanceType);
		bm.setMsLovByLovTrxType(trxType);
		bm.setMsTenant(tenant);
		bm.setMsVendor(vendor);
		bm.setTrxDate(trxDate);
		bm.setRefNo(refNo);
		bm.setQty(qty);
		bm.setTrxNo(trxNo);
		bm.setVendorTrxNo(vendorTrxNo);
		bm.setMsOffice(office);
		bm.setMsBusinessLine(businessLine);

		if(stampDuty != null) {
			bm.setNotes(stampDuty.getStampDutyNo()); //No Materai
			bm.setAmMsuser(null); //Trx By diisi null, karena dilakukan oleh system
		}
		else {
			bm.setNotes(StringUtils.left(notes, 200));
			bm.setAmMsuser(user);
		}

		if (GlobalVal.CODE_LOV_BALANCE_TYPE_OTP.equals(balanceType.getCode()) && null != user) {
			bm.setUsrCrt(user.getLoginId());
		} else {
			bm.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		}
		
		bm.setDtmCrt(new Date());
		
		daoFactory.getBalanceMutationDao().insertTrBalanceMutation(bm);
				
		QueuePublisher.queueBalanceCheck(new BalanceCheckQueueBean(tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode()));		
	}
	
	@Override
	public void insertBalanceMutation(TrStampDuty stampDuty, TrDocumentH docH, TrDocumentD docD, MsLov balanceType, 
		MsLov trxType, MsTenant tenant, MsVendor vendor, Date trxDate, String refNo,
		int qty, String trxNo, AmMsuser user, String notes, String vendorTrxNo, AuditContext audit){

		TrBalanceMutation bm = new TrBalanceMutation();
		bm.setTrStampDuty(stampDuty);
		bm.setTrDocumentH(docH);
		bm.setTrDocumentD(docD);
		bm.setMsLovByLovBalanceType(balanceType);
		bm.setMsLovByLovTrxType(trxType);
		bm.setMsTenant(tenant);
		bm.setMsVendor(vendor);
		bm.setTrxDate(trxDate);
		bm.setRefNo(refNo);
		bm.setQty(qty);
		bm.setTrxNo(trxNo);
		bm.setVendorTrxNo(vendorTrxNo);
		
		if (null != docH) {
			bm.setMsBusinessLine(docH.getMsBusinessLine());
			bm.setMsOffice(docH.getMsOffice());
		}

		if(stampDuty != null) {
			bm.setNotes(stampDuty.getStampDutyNo()); //No Materai
			bm.setAmMsuser(null); //Trx By diisi null, karena dilakukan oleh system
		}
		else {
			bm.setNotes(StringUtils.left(notes, 200));
			bm.setAmMsuser(user);
		}

		if (GlobalVal.CODE_LOV_BALANCE_TYPE_OTP.equals(balanceType.getCode()) && null != user) {
			bm.setUsrCrt(user.getLoginId());
		} else {
			bm.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		}
		
		bm.setDtmCrt(new Date());
		
		daoFactory.getBalanceMutationDao().insertTrBalanceMutation(bm);
				
		QueuePublisher.queueBalanceCheck(new BalanceCheckQueueBean(tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode()));		
	}

	
	private void insertBalanceMutationTopUp(TrStampDuty stampDuty, TrDocumentH docH, TrDocumentD docD, MsLov balanceType, 
		MsLov trxType, MsTenant tenant, MsVendor vendor, Date trxDate, String refNo,
		int qty, String trxNo, AmMsuser user, String notes, String vendorTrxNo, AuditContext audit,Date expiredDate, BigDecimal balancePricePerQty){

		TrBalanceMutation bm = new TrBalanceMutation();
		bm.setTrStampDuty(stampDuty);
		bm.setTrDocumentH(docH);
		bm.setTrDocumentD(docD);
		bm.setMsLovByLovBalanceType(balanceType);
		bm.setMsLovByLovTrxType(trxType);
		bm.setMsTenant(tenant);
		bm.setMsVendor(vendor);
		bm.setTrxDate(trxDate);
		bm.setRefNo(refNo);
		bm.setQty(qty);
		bm.setTrxNo(trxNo);
		bm.setVendorTrxNo(vendorTrxNo);
		
		if (null != docH) {
			bm.setMsBusinessLine(docH.getMsBusinessLine());
			bm.setMsOffice(docH.getMsOffice());
		}

		if(stampDuty != null) {
			bm.setNotes(stampDuty.getStampDutyNo()); //No Materai
			bm.setAmMsuser(null); //Trx By diisi null, karena dilakukan oleh system
		}
		else {
			bm.setNotes(StringUtils.left(notes, 200));
			bm.setAmMsuser(user);
		}

		if (GlobalVal.CODE_LOV_BALANCE_TYPE_OTP.equals(balanceType.getCode()) && null != user) {
			bm.setUsrCrt(user.getLoginId());
		} else {
			bm.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		}
		
		bm.setDtmCrt(new Date());
		
		daoFactory.getBalanceMutationDao().insertTrBalanceMutation(bm);
		
		TrBalanceTopUp trBalanceTopUp = new TrBalanceTopUp();
		trBalanceTopUp.setTrBalanceMutation(bm);
		trBalanceTopUp.setExpiredDate(expiredDate);
		trBalanceTopUp.setIsUsed("0");
		trBalanceTopUp.setBalancePricePerQty(balancePricePerQty);
		trBalanceTopUp.setUsrCrt(bm.getUsrCrt());
		trBalanceTopUp.setDtmCrt(bm.getDtmCrt());
		daoFactory.getBalanceTopUpDao().insertTrBalanceTopUp(trBalanceTopUp);
		
		QueuePublisher.queueBalanceCheck(new BalanceCheckQueueBean(tenant.getTenantCode(), vendor.getVendorCode(), balanceType.getCode()));		
	}
	
	@Override
	public void insertBalanceMutationNewSesh(TrStampDuty stampDuty, TrDocumentH docH, TrDocumentD docD, String balanceType, 
		String trxType, MsTenant tenant, MsVendor vendor, Date trxDate, String refNo,
		int qty, String trxNo, AmMsuser user, String notes,  AuditContext audit){

		TrBalanceMutation bm = new TrBalanceMutation();
		bm.setTrStampDuty(stampDuty);
		bm.setTrDocumentH(docH);
		bm.setTrDocumentD(docD);
		
		MsLov balType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceType);
		bm.setMsLovByLovBalanceType(balType);
		
		MsLov txType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, trxType);
		bm.setMsLovByLovTrxType(txType);
		bm.setMsTenant(tenant);
		bm.setMsVendor(vendor);
		bm.setTrxDate(trxDate);
		bm.setRefNo(refNo);
		bm.setQty(qty);
		bm.setTrxNo(trxNo);

		if(stampDuty != null) {
			bm.setNotes(stampDuty.getStampDutyNo()); //No Materai
			bm.setAmMsuser(null); //Trx By diisi null, karena dilakukan oleh system
		}
		else {
			bm.setNotes(StringUtils.left(notes, 200));
			bm.setAmMsuser(user);
		}

		if (GlobalVal.CODE_LOV_BALANCE_TYPE_OTP.equals(balType.getCode()) && null != user) {
			bm.setUsrCrt(user.getLoginId());
		} else {
			bm.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
		}
		
		bm.setDtmCrt(new Date());
		
		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(bm);
				
		QueuePublisher.queueBalanceCheck(new BalanceCheckQueueBean(tenant.getTenantCode(), vendor.getVendorCode(), balType.getCode()));		
	}
	
	private void createListBalanceMutationSheet(XSSFWorkbook workbook, XSSFCellStyle styleBoldText, List<BalanceMutationBean> listBalanceMutation, String refNoLabel) {
		XSSFSheet mainSheet = workbook.createSheet("Search Result");
		mainSheet.setColumnWidth(0, 5000);
		mainSheet.setColumnWidth(1, 5000);
		mainSheet.setColumnWidth(2, 5000);
		mainSheet.setColumnWidth(3, 5000);
		mainSheet.setColumnWidth(4, 5000);
		mainSheet.setColumnWidth(5, 5000);
		mainSheet.setColumnWidth(6, 5000);
		mainSheet.setColumnWidth(7, 5000);
		
		XSSFRow rowOne = mainSheet.createRow(0);
		XSSFCell cellA1 = rowOne.createCell(0);
		XSSFCell cellB1 = rowOne.createCell(1);
		XSSFCell cellC1 = rowOne.createCell(2);
		XSSFCell cellD1 = rowOne.createCell(3);
		XSSFCell cellE1 = rowOne.createCell(4);
		XSSFCell cellF1 = rowOne.createCell(5);
		XSSFCell cellG1 = rowOne.createCell(6);
		XSSFCell cellH1 = rowOne.createCell(7);
		XSSFCell cellI1 = rowOne.createCell(8);
		XSSFCell cellJ1 = rowOne.createCell(9);
		XSSFCell cellL1 = rowOne.createCell(10);
		XSSFCell cellM1 = rowOne.createCell(11);
		XSSFCell cellN1 = rowOne.createCell(12);

		cellA1.setCellValue("Transaction No");
		cellB1.setCellValue(PARAM_TRX_DATE);
		cellC1.setCellValue("Transation Type");
		cellD1.setCellValue("Transaction By");
		cellE1.setCellValue(refNoLabel);
		cellF1.setCellValue("Document Type");
		cellG1.setCellValue("Document Name");
		cellH1.setCellValue("Notes");
		cellI1.setCellValue("Qty");
		cellJ1.setCellValue("Balance");
		cellL1.setCellValue("Office");
		cellM1.setCellValue("Region");
		cellN1.setCellValue("Business Line");
		cellA1.setCellStyle(styleBoldText);
		cellB1.setCellStyle(styleBoldText);
		cellC1.setCellStyle(styleBoldText);
		cellD1.setCellStyle(styleBoldText);
		cellE1.setCellStyle(styleBoldText);
		cellF1.setCellStyle(styleBoldText);
		cellG1.setCellStyle(styleBoldText);
		cellH1.setCellStyle(styleBoldText);
		cellI1.setCellStyle(styleBoldText);
		cellJ1.setCellStyle(styleBoldText);
		cellL1.setCellStyle(styleBoldText);
		cellM1.setCellStyle(styleBoldText);
		cellN1.setCellStyle(styleBoldText);
		
		for (int i = 1 ; i < listBalanceMutation.size() + 1; i++) {
			XSSFRow row = mainSheet.createRow(i);
			XSSFCell cellOne = row.createCell(0);
			XSSFCell cellTwo = row.createCell(1);
			XSSFCell cellThree = row.createCell(2);
			XSSFCell cellFour = row.createCell(3);
			XSSFCell cellFive = row.createCell(4);
			XSSFCell cellSix = row.createCell(5);
			XSSFCell cellSeven = row.createCell(6);
			XSSFCell cellEight = row.createCell(7);
			XSSFCell cellNine = row.createCell(8);
			XSSFCell cellTen = row.createCell(9);
			XSSFCell cellEleven = row.createCell(10);
			XSSFCell cellTwelve = row.createCell(11);
			XSSFCell cellThirteen = row.createCell(12);
			
			BalanceMutationBean bean = listBalanceMutation.get(i-1);
			cellOne.setCellValue(bean.getTransactionNo());
			cellTwo.setCellValue(bean.getTransactionDate());
			cellThree.setCellValue(bean.getTransactionType());
			cellFour.setCellValue(bean.getCustomerName());
			cellFive.setCellValue(bean.getRefNumber());
			cellSix.setCellValue(bean.getDocumentType());
			cellSeven.setCellValue(bean.getDocumentName());
			cellEight.setCellValue(bean.getNotes());
			cellNine.setCellValue(bean.getQty());
			cellTen.setCellValue(bean.getBalance());
			cellEleven.setCellValue(bean.getOfficeName());
			cellTwelve.setCellValue(bean.getRegion());
			cellThirteen.setCellValue(bean.getBusinessLine());
		}
		
	}

	@Override
	public DownloadListBalanceMutationResponse downloadListBalanceMutation(DownloadListBalanceMutationRequest req, AuditContext audit) throws ParseException {
		MsTenant tenant = tenantLogic.getTenantByCode(req.getTenantCode(), audit);
		MsVendor vendor = vendorLogic.getVendorByCode(req.getVendorCode(), audit);
		this.tenantVendorValidation(req.getTenantCode(), req.getVendorCode(), tenant, vendor, audit);
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, req.getBalanceType());

		String messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_SALDO_PLEASE_CHOOSE_BALANCE_TYPE, null, audit);

		commonValidatorLogic.validateNotNull(balanceType, messageValidation, StatusCode.SALDO_BALANCE_TYPE_CODE_EMPTY);
		
		DownloadListBalanceMutationResponse response = new DownloadListBalanceMutationResponse();
		
		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);
		//penjagaan batas 30 hari
		if (!isDateRangeValid(req.getTransactionDateStart(), req.getTransactionDateEnd(), audit)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE_GENERAL_SETTING,
					new Object[] {GlobalVal.CONST_TRANSAKSI,maxRangeDate}, this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_DATE_RANGE);
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		Date dateStart = null;
		Date dateEnd = null;
		if(!StringUtils.isBlank(req.getTransactionDateStart())) {
			dateStart = sdf.parse(req.getTransactionDateStart());
		}
		
		if(!StringUtils.isBlank(req.getTransactionDateEnd())) {
			dateEnd = sdf.parse(req.getTransactionDateEnd());
		}
		
		if (StringUtils.isNotBlank(req.getOfficeCode())) {
			MsOffice tenantOffice = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(req.getOfficeCode(), req.getTenantCode());
			
			messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_BRANCH_NOT_EXIST, null, audit);

			commonValidatorLogic.validateNotNull(tenantOffice, maxRangeDate, StatusCode.BRANCH_NOT_EXIST);
		}
		
		List<BalanceMutationBean> listBalanceMutation = daoFactory.getBalanceMutationDao().getListBalanceMutation(tenant, vendor, balanceType, req.getTransactionType(), 
																		dateStart, dateEnd, req.getDocumentType(), req.getReferenceNo(), req.getDocumentName(), 0, 0, req.getOfficeCode(),
																		req.getStatus());
		
		StringBuilder name = new StringBuilder();
		name.append(BALMUT_FILENAME_PREFIX);
		if(StringUtils.isNotBlank(req.getTenantCode())) {
			name.append("_" + DownloadListBalanceMutationRequest.TENANT_CODE + req.getTenantCode());
		}
		if(StringUtils.isNotBlank(req.getVendorCode())) {
			name.append("_" + DownloadListBalanceMutationRequest.VENDOR_CODE + req.getVendorCode());
		}
		if(StringUtils.isNotBlank(req.getBalanceType())) {
			name.append("_" + DownloadListBalanceMutationRequest.BALANCE_TYPE + req.getBalanceType());
		}
		if(StringUtils.isNotBlank(req.getDocumentName())) {
			name.append("_" + DownloadListBalanceMutationRequest.DOC_NAME + req.getDocumentName());
		}
		if(StringUtils.isNotBlank(req.getDocumentType())) {
			name.append("_" + DownloadListBalanceMutationRequest.DOC_TYPE + req.getDocumentType());
		}
		if(StringUtils.isNotBlank(req.getReferenceNo())) {
			name.append("_" + DownloadListBalanceMutationRequest.REF_NO + req.getReferenceNo());
		}
		if(StringUtils.isNotBlank(req.getTransactionDateStart())) {
			name.append("_" + DownloadListBalanceMutationRequest.TRX_DATE_START + req.getTransactionDateStart());
		}
		if(StringUtils.isNotBlank(req.getTransactionDateEnd())) {
			name.append("_" + DownloadListBalanceMutationRequest.TRX_DATE_END + req.getTransactionDateEnd());
		}
		if(StringUtils.isNotBlank(req.getTransactionType())) {
			name.append("_" + DownloadListBalanceMutationRequest.TRX_TYPE + req.getTransactionType());
		}		
		name.append(GlobalVal.FILE_EXTENTION_XLSX);
		response.setFilename(name.toString());
		
		byte[] excel = null;
		try {
			excel = this.excelLogic.generate((workbook, styleBoldText) -> 
				this.createListBalanceMutationSheet(workbook, styleBoldText, listBalanceMutation, tenant.getRefNumberLabel())
			);
		} catch (IOException e) {
			LOG.error("Download list balance mutation error", e);
			Status status = new Status();
			status.setCode(200);
			status.setMessage(e.getMessage());
		}
		String base64 = Base64.getEncoder().encodeToString(excel);
		response.setExcelBase64(base64);

		return response;
	}
	
	@Override
	public DownloadListBalanceMutationResponse downloadListBalanceMutationEmbed(DownloadListBalanceMutationEmbedRequest req, AuditContext audit) throws ParseException {
		DownloadListBalanceMutationResponse response = new DownloadListBalanceMutationResponse();
		Status status = new Status();
		EmbedMsgBean msgBean  = commonLogic.decryptMessageToClass(req.getMsg(), audit, EmbedMsgBean.class);
		String vendorCode = commonLogic.decryptMessageToString(req.getVendorCode(), audit);
		String balType = commonLogic.decryptMessageToString(req.getBalanceType(), audit);
		
		if (null == balType || null == vendorCode || null == msgBean) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_INVALID, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		if (null == tenant) {
			if (StringUtils.isBlank(msgBean.getTenantCode())) {
				throw new TenantException(messageSource.getMessage("businesslogic.tenant.tenantcodeempty"
						, null, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
			} else {
				throw new TenantException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_TENANT_NOT_EXIST
						, new Object[] {msgBean.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
			}
		}
		
		MsVendor vendor = vendorLogic.getVendorByCode(vendorCode, audit);
		this.tenantVendorValidation(msgBean.getTenantCode(), vendorCode, tenant, vendor, audit);
		
	    MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, balType);
		if(balanceType == null) {
			throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_PLEASE_CHOOSE_BALANCE_TYPE
					, null, this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_TYPE_CODE_EMPTY);
		}
		
		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);
		//penjagaan batas max hari
		if (!isDateRangeValid(req.getTransactionDateStart(), req.getTransactionDateEnd(), audit)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE_GENERAL_SETTING,
					new Object[] {GlobalVal.CONST_TRANSAKSI,maxRangeDate}, this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_DATE_RANGE);
		}
		
		
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		Date dateStart = null;
		Date dateEnd = null;
		if(!StringUtils.isBlank(req.getTransactionDateStart())) {
			dateStart = sdf.parse(req.getTransactionDateStart());
		}
		
		if(!StringUtils.isBlank(req.getTransactionDateEnd())) {
			dateEnd = sdf.parse(req.getTransactionDateEnd());
		}
		
		List<BalanceMutationBean> listBalanceMutation = daoFactory.getBalanceMutationDao().getListBalanceMutation(tenant, vendor, balanceType, req.getTransactionType(), 
																		dateStart, dateEnd, req.getDocumentType(), req.getReferenceNo(), req.getDocumentName(), 0, 0, "", null);

		response.setFilename(generateExcelName(req, msgBean, vendorCode, balType));
		
		byte[] excel = null;
		try {
			excel = this.excelLogic.generateEmbed((workbook, styleBoldText) -> 
				this.createListBalanceMutationSheet(workbook, styleBoldText, listBalanceMutation, tenant.getRefNumberLabel())
			);
		} catch (IOException e) {
			LOG.error("Download list balance mutation embed error", e);
			status.setCode(200);
			status.setMessage(e.getMessage());
		}
		String base64 = Base64.getEncoder().encodeToString(excel);
		response.setExcelBase64(base64);

		return response;
	}
	
	private String generateExcelName(DownloadListBalanceMutationEmbedRequest req, EmbedMsgBean msgBean, String vendorCode, String balType) {
		StringBuilder name = new StringBuilder();
		name.append(BALMUT_FILENAME_PREFIX);
		if(StringUtils.isNotBlank(msgBean.getTenantCode())) {
			name.append("_" + DownloadListBalanceMutationRequest.TENANT_CODE + msgBean.getTenantCode());
		}
		if(StringUtils.isNotBlank(vendorCode)) {
			name.append("_" + DownloadListBalanceMutationRequest.VENDOR_CODE + vendorCode);
		}
		if(StringUtils.isNotBlank(balType)) {
			name.append("_" + DownloadListBalanceMutationRequest.BALANCE_TYPE + balType);
		}
		if(StringUtils.isNotBlank(req.getDocumentName())) {
			name.append("_" + DownloadListBalanceMutationRequest.DOC_NAME + req.getDocumentName());
		}
		if(StringUtils.isNotBlank(req.getDocumentType())) {
			name.append("_" + DownloadListBalanceMutationRequest.DOC_TYPE + req.getDocumentType());
		}
		if(StringUtils.isNotBlank(req.getReferenceNo())) {
			name.append("_" + DownloadListBalanceMutationRequest.REF_NO + req.getReferenceNo());
		}
		if(StringUtils.isNotBlank(req.getTransactionDateStart())) {
			name.append("_" + DownloadListBalanceMutationRequest.TRX_DATE_START + req.getTransactionDateStart());
		}
		if(StringUtils.isNotBlank(req.getTransactionDateEnd())) {
			name.append("_" + DownloadListBalanceMutationRequest.TRX_DATE_END + req.getTransactionDateEnd());
		}
		if(StringUtils.isNotBlank(req.getTransactionType())) {
			name.append("_" + DownloadListBalanceMutationRequest.TRX_TYPE + req.getTransactionType());
		}		
		name.append(GlobalVal.FILE_EXTENTION_XLSX);
		
		return name.toString();
	}
	
	@SuppressWarnings("unused")
	@Override
	public void sendReminderEmail(String tenantCode, String vendorCode, String balanceTypeCode) throws ParseException {			
		
		MsTenant tenant =  tenantLogic.getTenantByCode(tenantCode, null);		
		TrSchedulerJob schedJob = daoFactory.getSchedulerJobDao().getSchedulerJob(MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_FORMAT), 
				GlobalVal.CODE_LOV_JOB_TYPE_BALREM, balanceTypeCode, tenant);
		Date startDate = new Date();
		
		MsLov balanceTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceTypeCode);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);
		
		BalanceBean bean = this.checkSaldoBelowThreshold(tenant, vendor, balanceTypeLov);
		if (null == bean) {
			return;
		}
		
		if (null != schedJob) {
			LOG.info("Balance threshold email for tenant {}, vendor {}, balance {} has been sent today. Skip sending email.", tenant.getTenantCode(), vendor.getVendorCode(), balanceTypeCode);
			return;
		}
		
		LOG.info("Send reminder email: Tenant: {}, Vendor: {}, Balance type: {}", tenantCode, vendorCode, balanceTypeCode);
		String [] emailReminderDestinations = tenant.getEmailReminderDest().split("\\s*,\\s*"); //split string by comma and trim
		schedulerLogic.sendEmailReminder(emailReminderDestinations, tenant.getTenantName(), vendor.getVendorName(), balanceTypeLov.getDescription(), bean.getCurrentBalance());
				
		if (null == schedJob) {
			MsLov jobTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_JOB_TYPE, GlobalVal.CODE_LOV_JOB_TYPE_BALREM);
			MsLov schedTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SCHEDULER_TYPE, GlobalVal.CODE_LOV_SCHED_TYPE_DAILY);

			schedJob = new TrSchedulerJob();
			schedJob.setUsrCrt("QUEUE BALANCE CHECK");
			schedJob.setDtmCrt(new Date());
			schedJob.setMailReminderCount((short) 1);
			schedJob.setMsLovByBalanceType(balanceTypeLov);
			schedJob.setMsLovByJobType(jobTypeLov);
			schedJob.setMsLovBySchedulerType(schedTypeLov);
			schedJob.setSchedulerStart(startDate);
			schedJob.setSchedulerEnd(new Date());
			schedJob.setMsTenant(tenant);

			daoFactory.getSchedulerJobDao().insertSchedulerJob(schedJob);
		} else {
			schedJob.setMailReminderCount((short) (schedJob.getMailReminderCount()+1));
			schedJob.setSchedulerEnd(new Date());
			schedJob.setUsrUpd("QUEUE BALANCE CHECK");
			schedJob.setDtmUpd(new Date());

			daoFactory.getSchedulerJobDao().updateSchedulerJob(schedJob);
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public BalanceBean checkSaldoBelowThreshold(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov) {
		MsBalancevendoroftenant bvot = daoFactory.getVendorDao().getBalanceVendorOfTenant(vendor.getVendorCode(), tenant.getTenantCode(), balanceTypeLov.getCode());
		
		if (null == bvot) {
			throw new VendorException(messageSource.getMessage("businesslogic.vendor.balancvendoroftenantnotfound", new Object[] {balanceTypeLov.getDescription(), vendor.getVendorName(), tenant.getTenantName()}, this.retrieveDefaultLocale()), ReasonVendor.BALANCE_VENDOR_NOT_FOUND);
		}
		
		BigInteger currentBalance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenant(tenant, vendor, balanceTypeLov);
		
		ObjectMapper mapper = new ObjectMapper();
		Map<String, Integer> map = new HashMap<>();
		
		try {
			map = mapper.readValue(tenant.getThresholdBalance(), Map.class);
		} catch (IOException e) {
			LOG.error(e.getLocalizedMessage());
		}
		Integer balanceThreshold = null;
		
		try {
			balanceThreshold = map.get(balanceTypeLov.getCode());
			if (null == balanceThreshold) {
				LOG.info("T={}, V={}, BAL={} threshold not set-reminder skipped.",
						tenant.getTenantCode(), vendor.getVendorCode(), balanceTypeLov.getCode());
				//jika tidak ada disettingan threshold balance = tidak ada reminder
				return null;
			}
		}
		catch (Exception e) {
			LOG.error("T={}, V={}, BAL={} exception", 
					tenant.getTenantCode(), vendor.getVendorCode(), balanceTypeLov.getCode(), e);
			return null;
		}
		BalanceBean bean = null;
		if (currentBalance.intValue() < balanceThreshold) {
			bean = new BalanceBean();
			bean.setCurrentBalance(currentBalance);
		}
		if (null != bean) {
			LOG.info("CheckSaldoBelowThreshold T={}, V={}, BAL={}, CURR={}; THRES={}", 
					tenant.getTenantCode(), vendor.getVendorCode(), balanceTypeLov.getCode(),					
					bean.getCurrentBalance(), balanceThreshold.intValue());
		}

		return bean;

	}

	@Override
	public BalanceResponse getBalanceNotSecure(BalanceRequest request, AuditContext audit) {
		return this.getBalance(request, audit);
	}

	@Override
	public ListBalanceTenantResponse getListBalanceTenant(ListBalanceTenantRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {MsTenant.TENANT_CODE_HBM}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {MsTenant.TENANT_CODE_HBM, request.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		ListBalanceTenantResponse response = new ListBalanceTenantResponse();
		List<BalanceTenantBean> listBalanceTenant = daoFactory.getTenantDao().getListBalanceTenant(request.getTenantCode());
		response.setListBalanceTenant(listBalanceTenant);
		
		return response;
	}

	@SuppressWarnings("unchecked")
	@Override
	public UpdateBalanceTenantResponse updateBalanceTenant(UpdateBalanceTenantRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {MsTenant.TENANT_CODE_HBM}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		if (null == request.getListBalanceTenant() || request.getListBalanceTenant().isEmpty()) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {"listBalanceTenant"}, this.retrieveLocaleAudit(audit)), ReasonTenant.LIST_BALANCE_TENANT_EMPTY);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {MsTenant.TENANT_CODE_HBM, request.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		daoFactory.getTenantDao().deleteBalanceTenant(request.getTenantCode());
		
		ObjectMapper mapper = new ObjectMapper();
		Map<String, Integer> map = new HashMap<>();
		
		if (StringUtils.isNotBlank(tenant.getThresholdBalance())) {
			try {
				map = mapper.readValue(tenant.getThresholdBalance(), Map.class);
			} catch (IOException e) {
				LOG.error(e.getLocalizedMessage());
			}
		}
		
		List<MsBalancevendoroftenant> listBalanceTenant = new ArrayList<>();
		List<BalanceTenantBean> listBalanceTenantBean = request.getListBalanceTenant();
		for (BalanceTenantBean balanceTenantBean : listBalanceTenantBean) {
			if ("1".equalsIgnoreCase(balanceTenantBean.getIsActive())) {
				MsBalancevendoroftenant balanceTenant = new MsBalancevendoroftenant();
				balanceTenant.setDtmCrt(new Date());
				balanceTenant.setUsrCrt(audit.getCallerId());
				balanceTenant.setIsActive("1");
				balanceTenant.setMsTenant(tenant);
				
				MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(balanceTenantBean.getVendorCode());
				balanceTenant.setMsVendor(vendor);
				
				MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, 
						balanceTenantBean.getBalanceTypeCode());
				balanceTenant.setMsLov(balanceType);
				
				List<MsBalancevendoroftenant> filteredList = listBalanceTenant.stream()
						.filter(x -> x.getMsVendor().getVendorCode().equals(balanceTenantBean.getVendorCode())
								  && x.getMsLov().getCode().equals(balanceTenantBean.getBalanceTypeCode())).collect(Collectors.toList());
				if (filteredList.isEmpty()) {
					listBalanceTenant.add(balanceTenant);
				}
								
				LocalDate recapDate = LocalDate.now().minusDays(1);
				TrBalanceDailyRecap dailyRecap = daoFactory.getDailyRecapDao().getDailyRecap(recapDate.toString(), balanceType, tenant, vendor);
				
				
				if (0 == dailyRecap.getIdBalanceDailyRecap()) {
					TrBalanceDailyRecap newDailyRecap = new TrBalanceDailyRecap();
					newDailyRecap.setUsrCrt(audit.getCallerId());
					newDailyRecap.setDtmCrt(new Date());
					newDailyRecap.setMsLov(balanceType);
					newDailyRecap.setMsTenant(tenant);
					newDailyRecap.setMsVendor(vendor);
					try {
						newDailyRecap.setRecapDate(new SimpleDateFormat(GlobalVal.DATE_FORMAT).parse(recapDate.toString()));
					} catch (ParseException e) {
						LOG.error("error in parsing data : {}", recapDate);
					}
					newDailyRecap.setRecapTotalBalance(0);
					daoFactory.getDailyRecapDao().insertTrBalanceDailyRecap(newDailyRecap);
				}
				
				LocalDate endRecapDate = LocalDate.now().minusMonths(1);
				LocalDate endDate = endRecapDate.withDayOfMonth(endRecapDate.getMonth().length(endRecapDate.isLeapYear()));
				
				TrBalanceDailyRecap monthlyRecap = daoFactory.getDailyRecapDao().getDailyRecap(endDate.toString(), balanceType, tenant, vendor);

				if (0 == monthlyRecap.getIdBalanceDailyRecap()) {
					TrBalanceDailyRecap newDailyRecap = new TrBalanceDailyRecap();
					newDailyRecap.setUsrCrt(audit.getCallerId());
					newDailyRecap.setDtmCrt(new Date());
					newDailyRecap.setMsLov(balanceType);
					newDailyRecap.setMsTenant(tenant);
					newDailyRecap.setMsVendor(vendor);
					try {
						newDailyRecap.setRecapDate(new SimpleDateFormat(GlobalVal.DATE_FORMAT).parse(endDate.toString()));
					} catch (ParseException e) {
						LOG.error("error in parsing data : {}", endDate);
					}
					newDailyRecap.setRecapTotalBalance(0);
					daoFactory.getDailyRecapDao().insertTrBalanceDailyRecap(newDailyRecap);
				}
				
				
			}
		}
		daoFactory.getTenantDao().insertBalanceTenant(listBalanceTenant);
		
		List<BalanceTenantBean> listBalanceTypeTenant = daoFactory.getTenantDao().getListBalanceTypeTenant(request.getTenantCode());
		Map<String, Integer> newMap = new HashMap<>();
		for (BalanceTenantBean balanceTenantBean : listBalanceTypeTenant) {
			Integer balanceThreshold = map.get(balanceTenantBean.getBalanceTypeCode());
			if (null == balanceThreshold) {
				newMap.put(balanceTenantBean.getBalanceTypeCode(), 0);
			} else {
				newMap.put(balanceTenantBean.getBalanceTypeCode(), balanceThreshold);
			}
		}
		String newBalanceThreshold = StringUtils.EMPTY;
		try {
			newBalanceThreshold = mapper.writeValueAsString(newMap);
		} catch (JsonProcessingException e) {
			LOG.error(e.getLocalizedMessage());
		}
		tenant.setUsrUpd(audit.getCallerId());
		tenant.setDtmUpd(new Date());
		tenant.setThresholdBalance(newBalanceThreshold);
		daoFactory.getTenantDao().updateTenant(tenant);
		
		List<VendorTenantBean> listVendorTenantBean = daoFactory.getVendorDao().getListVendorTenantFromBalanceVendorOfTenant(request.getTenantCode());
		for (VendorTenantBean vendorTenantBean : listVendorTenantBean) {
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorTenantBean.getVendorCode());
			
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			if(null == vot) {
				MsVendoroftenant newVot = new MsVendoroftenant();
				newVot.setDtmCrt(new Date());
				newVot.setUsrCrt(audit.getCallerId());
				newVot.setMsTenant(tenant);
				newVot.setMsVendor(vendor);
				newVot.setIsUseVendorEkyc("0");
				daoFactory.getVendorDao().insertVendoroftenant(newVot);
			}
		}
		
		List<MsVendoroftenant> listVot = daoFactory.getVendorDao().getListVendoroftenantByTenantCode(request.getTenantCode());
		for (MsVendoroftenant vot : listVot) {
			List<VendorTenantBean> filteredList = listVendorTenantBean.stream()
					.filter(x -> x.getTenantCode().equals(vot.getMsTenant().getTenantCode()) 
							  && x.getVendorCode().equals(vot.getMsVendor().getVendorCode())).collect(Collectors.toList());
			if (filteredList.isEmpty()) {
				daoFactory.getVendorDao().deleteVendoroftenant(vot);
			}
		}
		
		return new UpdateBalanceTenantResponse();
	}

	@Override
	public ListBalanceVendoroftenantResponse getListBalanceVendoroftenant(ListBalanceVendoroftenantRequest request, AuditContext audit) {
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		if (vendor == null) {
			throw new VendorException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_VENDOR_NOT_EXIST, new Object[] {request.getVendorCode()}, this.retrieveLocaleAudit(audit)),
					ReasonVendor.VENDOR_CODE_INVALID);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (tenant == null) {
			throw new TenantException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_TENANT_NOT_EXIST, new Object[] {request.getTenantCode()}, this.retrieveLocaleAudit(audit)),
					ReasonTenant.TENANT_NOT_FOUND);
		}
		
		ListBalanceVendoroftenantResponse response = new ListBalanceVendoroftenantResponse();
		List<BalanceVendoroftenantBean> listBalanceVot = daoFactory.getBalanceVendoroftenantDao().getListBalanceVendoroftenant(request.getTenantCode(), request.getVendorCode());
		response.setListBalanceVot(listBalanceVot);
		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}

	@Override
	public CheckThresholdResponse checkThreshold(CheckThresholdRequest request, AuditContext audit) throws ParseException {
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {MsTenant.TENANT_CODE_HBM}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {MsTenant.TENANT_CODE_HBM, request.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		if (StringUtils.isBlank(request.getBalanceTypeCode())) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {PARAM_BAL_TYPE_CODE}, this.retrieveLocaleAudit(audit)), ReasonTenant.BALANCE_TYPE_EMPTY);
		}
		MsLov balanceTypeDoc = this.commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, request.getBalanceTypeCode(), audit);
		if (null == balanceTypeDoc) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {PARAM_BAL_TYPE_CODE, request.getBalanceTypeCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_BALANCE_TYPE_NOT_FOUND);
		}
		
		List<Map<String, Object>> listVendor = daoFactory.getVendorDao().getListVendorByTenantAndBalanceType(tenant.getTenantCode(), 
				request.getBalanceTypeCode());
		Iterator<Map<String, Object>> itr = listVendor.iterator();
		while(itr.hasNext()) {
			Map<String, Object> map = itr.next();
			MsVendor vendor = vendorLogic.getVendorByCode(map.get("d0").toString(), audit);
			this.sendReminderEmail(tenant.getTenantCode(), vendor.getVendorCode(), balanceTypeDoc.getCode());
		}
		
		return new CheckThresholdResponse();
	}

	@Override
	public AddBalanceTypeResponse addBalanceType(AddBalanceTypeRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getBalanceTypeCode())) {
			throw new SaldoException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM, 
					new String[] {PARAM_BAL_TYPE_CODE}, this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_TYPE_CODE_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getBalanceTypeName())) {
			throw new SaldoException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM, 
					new String[] {"balanceTypeName"}, this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_TYPE_NAME_EMPTY);
		}
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, request.getBalanceTypeCode());
		if (null != balanceType) {
			throw new SaldoException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_TYPE_ALREADY_EXIST, 
					new String[] {request.getBalanceTypeCode()}, this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_TYPE_ALREADY_EXIST);
		}
		
		MsLov newBalanceType = new MsLov();
		newBalanceType.setUsrCrt(audit.getCallerId());
		newBalanceType.setDtmCrt(new Date());
		newBalanceType.setIsActive("1");
		newBalanceType.setIsDeleted("0");
		newBalanceType.setLovGroup(GlobalVal.LOV_GROUP_BALANCE_TYPE);
		newBalanceType.setCode(request.getBalanceTypeCode());
		newBalanceType.setDescription(request.getBalanceTypeName());
		int balanceTypeSequence = daoFactory.getLovDao().getLastSequence(GlobalVal.LOV_GROUP_BALANCE_TYPE) / 10;
		newBalanceType.setSequence(10 * (balanceTypeSequence + 1));
		newBalanceType.setConstraint1(GlobalVal.LOV_CONSTRAINT_CONFIG_BALANCE);
		daoFactory.getLovDao().insertLov(newBalanceType);
		
		MsLov topUpTrxType = new MsLov();
		topUpTrxType.setUsrCrt(audit.getCallerId());
		topUpTrxType.setDtmCrt(new Date());
		topUpTrxType.setIsActive("1");
		topUpTrxType.setIsDeleted("0");
		topUpTrxType.setLovGroup(GlobalVal.LOV_GROUP_TRX_TYPE);
		topUpTrxType.setCode("T".concat(request.getBalanceTypeCode()));
		topUpTrxType.setDescription("Top Up ".concat(request.getBalanceTypeName()));
		int trxTypeSequence = daoFactory.getLovDao().getLastSequence(GlobalVal.LOV_GROUP_TRX_TYPE) / 10;
		topUpTrxType.setSequence(10 * (trxTypeSequence + 1));
		topUpTrxType.setConstraint1(request.getBalanceTypeCode());
		daoFactory.getLovDao().insertLov(topUpTrxType);
		
		MsLov useTrxType = new MsLov();
		useTrxType.setUsrCrt(audit.getCallerId());
		useTrxType.setDtmCrt(new Date());
		useTrxType.setIsActive("1");
		useTrxType.setIsDeleted("0");
		useTrxType.setLovGroup(GlobalVal.LOV_GROUP_TRX_TYPE);
		useTrxType.setCode("U".concat(request.getBalanceTypeCode()));
		useTrxType.setDescription("Use ".concat(request.getBalanceTypeName()));
		useTrxType.setSequence(10 * (trxTypeSequence + 2));
		useTrxType.setConstraint1(request.getBalanceTypeCode());
		daoFactory.getLovDao().insertLov(useTrxType);

		
		return new AddBalanceTypeResponse();
	}

	@Override
	public AddBalanceTypeResponse editBalanceType(AddBalanceTypeRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getBalanceTypeCode())) {
			throw new SaldoException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM, 
					new String[] {PARAM_BAL_TYPE_CODE}, this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_TYPE_CODE_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getBalanceTypeName())) {
			throw new SaldoException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM, 
					new String[] {"balanceTypeName"}, this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_TYPE_NAME_EMPTY);
		}
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, request.getBalanceTypeCode());
		if (null == balanceType) {
			throw new SaldoException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_TYPE_NOT_FOUND, 
					new String[] {request.getBalanceTypeCode()}, this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_TYPE_NOT_FOUND);
		}
		
		balanceType.setUsrUpd(audit.getCallerId());
		balanceType.setDtmUpd(new Date());
		balanceType.setDescription(request.getBalanceTypeName());
		daoFactory.getLovDao().updateLov(balanceType);
		
		MsLov topUpTrxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, "T".concat(request.getBalanceTypeCode()));
		topUpTrxType.setUsrUpd(audit.getCallerId());
		topUpTrxType.setDtmUpd(new Date());
		topUpTrxType.setDescription("Top Up ".concat(request.getBalanceTypeName()));
		daoFactory.getLovDao().updateLov(topUpTrxType);
		
		MsLov useTrxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, "U".concat(request.getBalanceTypeCode()));
		useTrxType.setUsrUpd(audit.getCallerId());
		useTrxType.setDtmUpd(new Date());
		useTrxType.setDescription("Use ".concat(request.getBalanceTypeName()));
		daoFactory.getLovDao().updateLov(useTrxType);
		
		return new AddBalanceTypeResponse();
	}

	@Override
	public ListBalanceHistoryResponse getListBalanceMutationEmbed(GetListBalanceMutationEmbedRequest request,
			AuditContext audit) throws ParseException {
		ListBalanceHistoryResponse response = new ListBalanceHistoryResponse();
		Status status = new Status();
		EmbedMsgBean msgBean = null;
		try {
			msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		}catch(Exception e) {
			throw new SaldoException("Decrypt msg invalid", ReasonSaldo.DECRYPT_FAILED);
		}
		
		String vendorCode = null;
		try {
			vendorCode = commonLogic.decryptMessageToString(request.getVendorCode(), audit);
		}catch(Exception e) {
			throw new SaldoException("Decrypt vendor invalid", ReasonSaldo.DECRYPT_FAILED);
		}
		
		String balanceType = null;
		try {
			balanceType = commonLogic.decryptMessageToString(request.getBalanceType(), audit);
		}catch(Exception e) {
			throw new SaldoException("Decrypt balance type invalid", ReasonSaldo.DECRYPT_FAILED);
		}
		
		MsTenant tenant = null;
		MsVendor vendor = null;

		tenant = tenantLogic.getTenantByCode(msgBean.getTenantCode(), audit);
		if(null == tenant) {
			throw new DocumentException("Tenant does not exist", ReasonDocument.TENANT_NOT_EXISTS);
		}
		
		vendor = vendorLogic.getVendorByCode(vendorCode, audit);
		if(null == vendor) {
			throw new DocumentException("Vendor does not exist", ReasonDocument.VENDOR_NOT_EXISTS);
		}
		
		this.tenantVendorValidation(msgBean.getTenantCode(), vendorCode, tenant, vendor, audit);
	
		if (StringUtils.isBlank(balanceType)) {
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_PLEASE_CHOOSE_BALANCE_TYPE, null, this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}
		MsLov balanceTypeLov = commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceType, audit);
		
		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);
		//penjagaan batas max hari
		if (!isDateRangeValid(request.getTransactionDateStart(), request.getTransactionDateEnd(), audit)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE_GENERAL_SETTING,
					new Object[] {GlobalVal.CONST_TRANSAKSI,maxRangeDate}, this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_DATE_RANGE);
		}

		
		Date dateStart = MssTool.formatStringToDate(request.getTransactionDateStart(), GlobalVal.DATE_FORMAT);
		
		Date dateEnd = MssTool.formatStringToDate(request.getTransactionDateEnd(), GlobalVal.DATE_FORMAT);
		

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		
		List<BalanceMutationBean> result = daoFactory.getBalanceMutationDao().getListBalanceMutation(tenant, vendor, balanceTypeLov, 
												request.getTransactionType(), dateStart, dateEnd, request.getDocumentType(), request.getReferenceNo(), request.getDocumentName(),
												min, max, "", null);
		
		int totalResult = daoFactory.getBalanceMutationDao().countBalanceMutation(tenant, vendor, balanceTypeLov,
												request.getTransactionType(), dateStart, dateEnd, request.getDocumentType(), request.getReferenceNo(), request.getDocumentName(), ""
												, null);
		
		double totalPage =  Math.ceil((double) totalResult / maxRow);
		
		response.setListMutation(result);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalResult);
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		
		return response;
	}

	@Override
	public ListBalanceTypeByVendorAndTenantResponse getListBalanceTypeByVendorAndTenant(
			ListBalanceTypeByVendorAndTenantRequest request, AuditContext audit) {
		ListBalanceTypeByVendorAndTenantResponse response = new ListBalanceTypeByVendorAndTenantResponse();
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		if (vendor == null || StringUtils.isBlank(request.getVendorCode())) {
			throw new VendorException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_VENDOR_NOT_EXIST, new Object[] {request.getVendorCode()}, this.retrieveLocaleAudit(audit)),
					ReasonVendor.VENDOR_CODE_INVALID);
		}
		
		List<BalanceVendoroftenantBean> listBalanceVot = new ArrayList<>();
		List<Map<String, Object>> result;
		if (StringUtils.isNotBlank(request.getTenantCode())) {
		    MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		    if (tenant == null) {
				throw new TenantException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_TENANT_NOT_EXIST, new Object[] {request.getTenantCode()}, this.retrieveLocaleAudit(audit)),
						ReasonTenant.TENANT_NOT_FOUND);
			}
			
	         listBalanceVot = daoFactory.getBalanceVendoroftenantDao().getListBalanceVendoroftenant(request.getTenantCode(), request.getVendorCode());	
		} else if (StringUtils.isBlank(request.getTenantCode())){
			 result = daoFactory.getLovDao().getMsLovListByLovGroup(GlobalVal.LOV_GROUP_BALANCE_TYPE);
				
			if (result.isEmpty()) {
				return response;
			}
				
			Iterator<Map<String, Object>> itr = result.iterator();
			while (itr.hasNext()) {
				Map<String, Object> map = itr.next();
					
				BalanceVendoroftenantBean bean = new BalanceVendoroftenantBean();
				bean.setBalanceTypeCode(String.valueOf(map.get("d0")));
				bean.setBalanceTypeName(String.valueOf(map.get("d1")));					
				listBalanceVot.add(bean);		
			}

		} 
		response.setListBalanceType(listBalanceVot);
		return response;
	}

	@Override
	public SignBalanceAvailabilityResponse getSignBalanceAvailability(SignBalanceAvailabilityRequest request, AuditContext audit) {
		
		List<String> listDocId = request.getListDocumentId();
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), false, audit);
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		MsVendor vendor = vendorValidatorLogic.validateGetVendor(request.getVendorCode(), true, audit);
		
		this.tenantVendorValidation(request.getTenantCode(), request.getVendorCode(), tenant, vendor, audit);
		
		String[] listDocumentId = listDocId.toArray(new String[listDocId.size()]);
		
		for(int i = 0; i<listDocId.size(); i++) {
			if(StringUtils.isBlank(listDocumentId[i])) {
				throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit), 
						ReasonDocument.EMPTY_DOCUMENT_ID);
			}
			TrDocumentD trDocumentD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listDocumentId[i]);
			if(trDocumentD == null) {
				throw new DocumentException(getMessage("businesslogic.document.documentnotfound", new Object[] {listDocumentId[i]}, audit), 
						ReasonDocument.DOCUMENT_NOT_FOUND);
			}
		}
		
		List<BigInteger> idUser = daoFactory.getBalanceMutationDao().getIdUserByListDoc(listDocumentId);
		if( user == null || !idUser.contains(BigInteger.valueOf(user.getIdMsUser())) ) {
			throw new UserException(getMessage("businesslogic.user.usernotsigner", new Object[] {audit.getCallerId()}, audit),
					ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		
		String paymentSignType = daoFactory.getBalanceMutationDao().getPaymentSignType(listDocumentId);
		String paymentName = null;
		
		if (paymentSignType.equals("TTD")) {
			paymentSignType = "SGN";
			paymentName = "Sign";
		} else if (paymentSignType.equals("DOC")) {
			paymentSignType = "DOC";
			paymentName = "Document";
		} 

		MsLov balanceTypeLov = commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, paymentSignType, audit);
		
		if(balanceTypeLov == null) {
			throw new LovException(messageSource.getMessage("service.global.lovnotvalid", new Object[] {paymentSignType}, this.retrieveLocaleAudit(audit)),
					Reason.CODE_INVALID);
		}
		
		
		BigInteger balance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenantAndType(tenant, vendor, balanceTypeLov);
		BigInteger paymentDoc = daoFactory.getBalanceMutationDao().getPaymentByDoc(listDocumentId);
		
		if (vendor.getIdMsVendor() != paymentDoc.longValue()) {
			throw new VendorException(messageSource.getMessage("businesslogic.vendor.vendorcodepayment", new Object[] {request.getVendorCode()}, this.retrieveLocaleAudit(audit)),
					ReasonVendor.VENDOR_CODE_INVALID);
		}
		
		if (balance == null) {
			throw new PaymentSignTypeException(messageSource.getMessage("businesslogic.saldo.balancetypenotfound", new Object[] {paymentSignType}, this.retrieveLocaleAudit(audit)),
					ReasonPaymentSignType.BALANCE_NULL);
		}
		
		BigInteger total = null;
		
		if ("DOC".equals(paymentSignType)) {
			total = daoFactory.getDocumentDao().getDocNeeded(listDocumentId, user);
		} else {
			total = daoFactory.getDocumentDao().getSignNeeded(listDocumentId,user);
		}
		
		if (balance.compareTo(total) < 0) {
			throw new SaldoException(getMessage("businesslogic.saldo.balancenotenough", new Object[] {paymentName}, audit), ReasonSaldo.BALANCE_NOT_ENOUGH);
		}
		
		Status status = new Status();
		status.setCode(0);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		SignBalanceAvailabilityResponse response = new SignBalanceAvailabilityResponse();
		response.setStatus(status);
		return response;
			

	}
	
	
	@Override
	public GetListBalanceTypeExternalResponse getListBalanceTypeExternal(GetListBalanceTypeExternalRequest request,String xApiKey, AuditContext audit) {
		
		//untuk check tenant dan key exists atau tidak
		tenantLogic.getTenantFromXApiKey(xApiKey, audit);
		
		LovListRequest requestList = new LovListRequest();
		requestList.setLovGroup(GlobalVal.LOV_GROUP_BALANCE_TYPE);
		
		List<Map<String, Object>> result = this.daoFactory.getLovDao().getMsLovListByGroupAndConstraint(requestList);

		if (result.isEmpty()) {
			return new GetListBalanceTypeExternalResponse();
		}
		
		Iterator<Map<String, Object>> itr = result.iterator();
		List<ListBalanceBean> listBalance = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			
			ListBalanceBean bean = new ListBalanceBean();
			bean.setBalanceTypeCode(String.valueOf(map.get("d0")));
			bean.setBalanceTypeName(String.valueOf(map.get("d1")));
			
			listBalance.add(bean);		
		}
		GetListBalanceTypeExternalResponse response = new GetListBalanceTypeExternalResponse();
		response.setListBalance(listBalance);
		return response;
			

	}
	
	
	public GetListBalanceMutationExternalResponse getListBalanceMutationExternal ( GetListBalanceMutationExternalRequest request,String xApiKey, AuditContext audit) {
		GetListBalanceMutationExternalResponse response = new GetListBalanceMutationExternalResponse();
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);
		String validationMessage ;
		
		
		validationMessage = this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
				new String[] {"Balance Type"}, this.retrieveLocaleAudit(audit));
		commonValidatorLogic.validateNotNull(request.getBalanceType(), validationMessage, StatusCode.READ_WRITE_PDF_ERROR);
		
		
		
		MsLov balanceTypeLov = commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, request.getBalanceType(), audit);
		
		validationMessage = this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
				new String[] {"Start Date"}, this.retrieveLocaleAudit(audit));
		commonValidatorLogic.validateNotNull(request.getStartDate(), validationMessage, StatusCode.READ_WRITE_PDF_ERROR);
		
		
		validationMessage = this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
				new String[] {"End Date"}, this.retrieveLocaleAudit(audit));
		commonValidatorLogic.validateNotNull(request.getEndDate(), validationMessage, StatusCode.READ_WRITE_PDF_ERROR);
		
		
		
		long dayCount = 0;
		Date start = null ;
		Date end = null;
		try {
			
			start = sdf.parse(request.getStartDate());
			end = sdf.parse(request.getEndDate());
			dayCount = (end.getTime() - start.getTime()) / (1000*60*60*24);
			
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		
		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);

		if (dayCount > Long.valueOf(maxRangeDate)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE_GENERAL_SETTING,
					new Object[] {GlobalVal.CONST_TRANSAKSI,maxRangeDate}, this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_DATE_RANGE);
		}
	
		
		if(start.after(end)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_EXCEED_LIMIT,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		
		if (StringUtils.isNotBlank(request.getOfficeCode())) {
			MsOffice tenantOffice = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(request.getOfficeCode(), tenant.getTenantCode());
			validationMessage = messageSource.getMessage(GlobalKey.MESSAGE_ERROR_OFFICE_NOT_EXIST, null, this.retrieveLocaleAudit(audit));
			commonValidatorLogic.validateNotNull(tenantOffice, validationMessage, StatusCode.OFFICE_NOT_EXISTS);
			
		}
		
		validationMessage = this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_TYPE_NOT_FOUND, 
				new String[] {request.getBalanceType()}, this.retrieveLocaleAudit(audit));
		commonValidatorLogic.validateNotNull(balanceTypeLov, validationMessage, StatusCode.BALANCE_MUTATION_NOT_FOUND);

		
		if (StringUtils.isNotBlank(request.getRegionCode())) {
			MsRegion region = daoFactory.getRegionDao().getRegionByCodeAndTenant(request.getRegionCode(), tenant.getTenantCode());
			validationMessage = getMessage("businessLogic.tenant.invalidregion", new String[] {request.getRegionCode()}, audit);
			commonValidatorLogic.validateNotNull(region, validationMessage, StatusCode.REGION_CODE_EMPTY);
			
		}
		
		if (StringUtils.isNotBlank(request.getBusinessLineCode())) {
			MsBusinessLine businessLine = daoFactory.getBusinessLineDao().getBusinessLineByCodeAndTenant(request.getBusinessLineCode(), tenant.getTenantCode());
			
			validationMessage = getMessage("businessLogic.tenant.invalidbusinessline", new String[] {request.getRegionCode()}, audit);
			commonValidatorLogic.validateNotNull(businessLine, validationMessage, StatusCode.BUSINESS_LINE_NOT_FOUND);
		
			
		}
		
		List<BalanceMutationExternalBean> result = daoFactory.getBalanceMutationDao().getListBalanceMutationExternal(tenant, balanceTypeLov, start, end, request.getOfficeCode(), request.getRegionCode(), request.getBusinessLineCode());
		response.setBalanceMutations(result);
		return response;
	}

	@Override
	public GetListTopupBalanceResponse getListTopupBalance(GetListTopupBalanceRequest request, AuditContext audit) {
		if (StringUtils.isNotBlank(request.getTenantCode())) {
			tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		}
		if (StringUtils.isNotBlank(request.getVendorCode())) {
			vendorValidatorLogic.validateGetVendor(request.getVendorCode(), true, audit);
		}
		if (StringUtils.isNotBlank(request.getBalanceTypeCode())) {
			MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, request.getBalanceTypeCode());
			commonValidatorLogic.validateNotNull(balanceType, getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_TYPE_NOT_FOUND, new String[] {request.getBalanceTypeCode()}, audit), StatusCode.SALDO_BALANCE_TYPE_NOT_FOUND);
		}

		if (StringUtils.equals(request.getStatus(), "Is Used")) {
			request.setStatus("1");
		} else if(StringUtils.equals(request.getStatus(), "Not Used")) {
			request.setStatus("0");
		}

		validateAndParseDateRange(request.getExpiredDateStart(), request.getExpiredDateEnd(), audit);

		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);
		if (!isDateRangeValid(request.getExpiredDateStart(), request.getExpiredDateEnd(), audit)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE_GENERAL_SETTING,
					new Object[] {GlobalVal.CONST_ELECTRONIC_CERTIFICATE_EXPIRED_STATUS, maxRangeDate}, this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_DATE_RANGE);
		}

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);

		BigInteger countList = daoFactory.getBalanceTopUpDao().countListTopupBalance(request);
		double totalPageD = Math.ceil(countList.doubleValue() / maxRow);
		int totalPage = (int) totalPageD;

		if (request.getPage() <= 0 || totalPage == 0) {
			request.setPage(1);
		} else if (request.getPage() > totalPage) {
			request.setPage(totalPage);
		}

		List<ListTopupBalanceBean> topupBalance = daoFactory.getBalanceTopUpDao().getListTopupBalanceByTenantAndVendor(request, min, max);

		GetListTopupBalanceResponse response = new GetListTopupBalanceResponse();
		response.setTotalResult(countList.intValue());
		response.setPage(request.getPage());
		response.setTopupBalance(topupBalance);
		response.setTotalPage(totalPage);
		return response;
	}

	private void validateAndParseDateRange(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		sdf.setLenient(false);

		if (StringUtils.isNotBlank(startDate)) {
			validateDateStringLength(startDate, audit);
			if (StringUtils.isNotBlank(endDate)) {
				validateDateStringLength(endDate, audit);
			}
			Date start = parseDate(startDate, audit, sdf);
			if (StringUtils.isBlank(endDate)) {
				throw new CommonException(this.messageSource.getMessage("businesslogic.global.daterangefiltermustbefilled",
						null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_RANGE);
			}
			Date end = parseDate(endDate, audit, sdf);
			if (start.compareTo(end) > 0) {
				throw new CommonException(this.messageSource.getMessage("businesslogic.error.invaliddatevalue",
						null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_RANGE);
			}
		} else if (StringUtils.isNotBlank(endDate)) {
			throw new CommonException(this.messageSource.getMessage("businesslogic.global.daterangefiltermustbefilled",
					null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_RANGE);
		}
	}

	private void validateDateStringLength(String date, AuditContext audit) {
		if (date.length() != 10) {
			throw new CommonException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH,
					null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_FORMAT);
		}
	}

	private Date parseDate(String dateStr, AuditContext audit, SimpleDateFormat sdf) {
		try {
			return sdf.parse(dateStr);
		} catch (ParseException e) {
			throw new CommonException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH,
					null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_FORMAT);
		}
	}

	@Override
	public MssResponseType extendTopupBalance(ExtendTopUpBalanceRequest request, AuditContext audit) {
		MsTenant tenant =  tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		MsVendor vendor =  vendorValidatorLogic.validateVendorOfTenant(request.getVendorCode(), request.getTenantCode(), true, audit);
		
		annotationValidatorLogic.validateAttributes(request, audit);
		
	    if (!request.getIdBalanceMutation().matches("\\d+")) {
	        throw new CommonException(
	                getMessage(GlobalKey.MESSAGE_ERROR_SALDO_ID_BALANCE_MUTATION_INVALID, new String[] { "Id Balance Mutation" }, audit), 
	                ReasonCommon.INVALID_VALUE);
	    }
		
		TrBalanceMutation balance = daoFactory.getBalanceMutationDao().getBalanceMutationByIdBalanceMutation(Long.parseLong(request.getIdBalanceMutation()));
		commonValidatorLogic.validateNotNull(balance, getMessage(GlobalKey.MESSAGE_ERROR_SALDO_ID_BALANCE_MUTATION_INVALID, null, audit), StatusCode.BALANCE_MUTATION_NOT_FOUND);
		
		TrBalanceTopUp topup = daoFactory.getBalanceTopUpDao().getBalanceTopupByIdBalanceMutation(balance.getIdBalanceMutation());

		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		sdf.setLenient(false);
		
		commonValidatorLogic.validateNotNull(request.getTopupDate(), getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] { "Topup Date" }, audit), StatusCode.MANDATORY_PARAMETER);
		commonValidatorLogic.validateNotNull(request.getExtendDate(), getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] { "Extend Date" }, audit), StatusCode.MANDATORY_PARAMETER);

		String datePattern = "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$";
		
		if (!request.getTopupDate().matches(datePattern)) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH, null, audit), ReasonCommon.INVALID_DATE_FORMAT);
		}
		
		try {
			sdf.parse(request.getTopupDate());
		} catch (ParseException e) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH, null, audit), ReasonCommon.INVALID_DATE_FORMAT);
		}
		
		if (!request.getExtendDate().matches(datePattern)) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH, null, audit), ReasonCommon.INVALID_DATE_FORMAT);
		}
		
		Date extendDateParse = null;
		try {
			extendDateParse = sdf.parse(request.getExtendDate());
		} catch (ParseException e) {
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH, null, audit), ReasonCommon.INVALID_DATE_FORMAT);
		}
		
		commonValidatorLogic.validateMustEquals(balance.getMsTenant(), tenant, "Tenant tidak sesuai terdaftar", StatusCode.INVALID_VALUE);
		commonValidatorLogic.validateMustEquals(balance.getMsVendor(), vendor, "Vendor tidak sesuai dengan yang terdaftar", StatusCode.INVALID_VALUE);
		commonValidatorLogic.validateMustEquals(request.getTopupDate(), sdf.format(balance.getTrxDate()), "Tanggal topup tidak sesuai dengan yang terdaftar", StatusCode.INVALID_VALUE);
		
		if (extendDateParse.before(topup.getExpiredDate())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_INVALID_DATE_VALUE, null, audit), ReasonParam.INVALID_DATE_RANGE);
		}
		
		topup.setLastExpiredDate(topup.getExpiredDate());
		topup.setExpiredDate(extendDateParse);
		
		if(null == topup.getExtendExpiredAttempt()) {
			topup.setExtendExpiredAttempt(1);
		} else {			
			topup.setExtendExpiredAttempt(topup.getExtendExpiredAttempt() + 1);
		}
		
		topup.setDtmUpd(new Date());
		topup.setUsrUpd(audit.getCallerId());
		daoFactory.getBalanceTopUpDao().updateTrBalanceTopUp(topup);
		
		sendEmailExtendTopUpBalance(topup, audit);
		Status status = new Status();
		status.setMessage(getMessage("businesslogic.saldo.successupdate", null, audit));
		MssResponseType response = new MssResponseType();
		response.setStatus(status);
		return response;
	}

	private void sendEmailExtendTopUpBalance(TrBalanceTopUp topup, AuditContext audit) {
		Map<String, Object> topupDetail = new HashMap<>();
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT_LONG_MONTH);
		sdf.setLenient(false);
	    
        topupDetail.put("tenantName", topup.getTrBalanceMutation().getMsTenant().getTenantName());
        topupDetail.put("balanceName", topup.getTrBalanceMutation().getMsLovByLovBalanceType().getDescription());
        topupDetail.put("refNumber", topup.getTrBalanceMutation().getRefNo());
        topupDetail.put("trxDate", sdf.format(topup.getTrBalanceMutation().getTrxDate()));
        topupDetail.put("lastExpiredDate", sdf.format(topup.getLastExpiredDate()));
        topupDetail.put("extendExpiredAttempt", topup.getExtendExpiredAttempt());
        topupDetail.put("expiredDate", sdf.format(topup.getExpiredDate()));
        topupDetail.put("notes", topup.getTrBalanceMutation().getNotes());
           
		Map<String, Object> templateParam = new HashMap<>();
        templateParam.put("topup", topupDetail);

        MsMsgTemplate template = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_SEND_EMAIL_EXTEND_BALANCE_SUCCESS, templateParam);
        String[] recipient = topup.getTrBalanceMutation().getMsTenant().getEmailReminderDest().split(",");
        AmGeneralsetting picOperation = daoFactory.getGeneralSettingDao().getGsObjByCode("PIC_OPERATION_ADINS");
        String[] recipientOperation = picOperation.getGsValue().split(";");

        EmailInformationBean emailInfo = new EmailInformationBean();
        emailInfo.setSubject(template.getSubject());
        emailInfo.setCc(recipientOperation);
        emailInfo.setBodyMessage(template.getBody());
        emailInfo.setTo(recipient);
   
		try {
			emailSenderLogic.sendEmailWithoutAsync(emailInfo, null);
		} catch (Exception e) {
			TrBackgroundProcessFail processFail = new TrBackgroundProcessFail();
			processFail.setProcessName(template.getSubject());
			processFail.setProcessLocation("Extend balance");
			processFail.setStackTrace(Arrays.toString(e.getStackTrace()));
			processFail.setProcessStatus("0");
			processFail.setUsrCrt(audit.getCallerId());
			processFail.setDtmCrt(new Date());
			
			daoFactory.getBackgroundProcessFailDao().insertTrBackgroundProcessFail(processFail);
			
			EmailAttachmentBean[] attachments = null;
			if (null != e) {
				byte[] stackTraceFile = buildStackTraceTextFile(e);
				String filename = buildStackTraceFileName(processFail.getProcessLocation());
				EmailAttachmentBean attachment = new EmailAttachmentBean(stackTraceFile, filename);
				attachments = new EmailAttachmentBean[] {attachment};
			}
	        
			StringBuilder htmlBuilder = new StringBuilder();

			htmlBuilder.append("<div style=\"font-family:&quot;Segoe UI&quot;, &quot;Lucida Sans&quot;, sans-serif;background-color:rgb(255, 255, 255);width:772.8px;max-width:800px;border-collapse:collapse !important\">")
			    .append("<ul style=\\\"font-family:Arial, Helvetica, sans-serif;margin:0px 0px 15px;color:rgb(34, 34, 34);background-color:rgb(255, 255, 255)\\\">")
			    
				.append("<li><strong>")
		    	.append("Service Name: ").append("</strong>")
		    	.append(topup.getTrBalanceMutation().getMsLovByLovBalanceType().getDescription()).append("</li>")

		    	.append("<li><strong>")
		    	.append("Invoice No: ").append("</strong>")
		    	.append(topup.getTrBalanceMutation().getRefNo()).append("</li>")

		    	.append("<li><strong>")
		    	.append("Tanggal Transaksi: ").append("</strong>")
		    	.append(sdf.format(topup.getTrBalanceMutation().getTrxDate())).append("</li>")

		    	.append("<li><strong>")
		    	.append("Tanggal Kedaluwarsa Terakhir: ").append("</strong>")
		    	.append(sdf.format(topup.getLastExpiredDate())).append("</li>")
			
		    	.append("<li><strong>")
		    	.append("Jumlah Percobaan Perpanjangan Saldo: ").append("</strong>")
		    	.append(topup.getExtendExpiredAttempt()).append("</li>")

		    	.append("<li><strong>")
		    	.append("Tanggal Kedaluwarsa Terbaru: ").append("</strong>")
		    	.append(sdf.format(topup.getExpiredDate())).append("</li>")
		    	
		    	.append("<li><strong>")
		    	.append("Catatan: ").append("</strong>")
		    	.append(topup.getTrBalanceMutation().getNotes()).append("</li>");

	        Map<String, Object> templateParameter = new HashMap<>();
	        templateParameter.put("module", htmlBuilder);
	        templateParameter.put("templateSubject", template.getSubject());
	        templateParameter.put(PARAM_NOTES, "Gagal saat melakukan proses Extend Balance untuk " + topup.getTrBalanceMutation().getMsTenant().getTenantName());

	        MsMsgTemplate templateError = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_SEND_EMAIL_FAIL, templateParameter);

	        EmailInformationBean emailInfoError = new EmailInformationBean();
			emailInfoError.setSubject(templateError.getSubject());
			emailInfoError.setBodyMessage(templateError.getBody());
			emailInfoError.setTo(recipientOperation);
			
			try {
				emailSenderLogic.sendEmail(emailInfoError, attachments);
			} catch (MessagingException e1) {
				e1.printStackTrace();
			}
			
	   }
	}
	
	@Override
	public MssResponseType updateRefNumber(UpdateRefNumberRequest request, AuditContext audit) {
		MsTenant tenant =  tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		MsVendor vendor =  vendorValidatorLogic.validateGetVendor(request.getVendorCode(), true, audit);
		
		commonValidatorLogic.validateNotNull(request.getTopupDate(), getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] { "Top Up Date" }, audit), StatusCode.MANDATORY_PARAMETER);
		
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		sdf.setLenient(false);
		
		if (request.getTopupDate().length() != 10) {
		    throw new CommonException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH,
		            null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_FORMAT);
		}
		
		try {
			sdf.parse(request.getTopupDate());
		} catch (ParseException e) {
			throw new CommonException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH,
					null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_FORMAT);
		}

		commonValidatorLogic.validateNotNull(request.getIdBalanceMutation(), getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] { "Id Balance Mutation" }, audit), StatusCode.MANDATORY_PARAMETER);
		
		TrBalanceMutation balance = daoFactory.getBalanceMutationDao().getBalanceMutationByIdBalanceMutation(Long.parseLong(request.getIdBalanceMutation()));
		
		commonValidatorLogic.validateNotNull(balance, getMessage(GlobalKey.MESSAGE_ERROR_SALDO_ID_BALANCE_MUTATION_INVALID, null, audit), StatusCode.BALANCE_MUTATION_NOT_FOUND);
		
		commonValidatorLogic.validateMustEquals(balance.getMsTenant(), tenant, getMessage(GlobalKey.MESSAGE_ERROR_SALDO_ID_BALANCE_MUTATION_INVALID, null, audit), StatusCode.BALANCE_MUTATION_NOT_FOUND);
		
		commonValidatorLogic.validateMustEquals(balance.getMsVendor(), vendor, getMessage(GlobalKey.MESSAGE_ERROR_SALDO_ID_BALANCE_MUTATION_INVALID, null, audit), StatusCode.BALANCE_MUTATION_NOT_FOUND);

		String balanceDateStr = sdf.format(balance.getTrxDate());

		commonValidatorLogic.validateMustEquals(balanceDateStr, request.getTopupDate(), getMessage(GlobalKey.MESSAGE_ERROR_SALDO_ID_BALANCE_MUTATION_INVALID, null, audit), StatusCode.BALANCE_MUTATION_NOT_FOUND);
		
		if (StringUtils.isBlank(request.getRefNumber())) {
			throw new ParameterException(
					getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] { "Ref Number" }, audit),
					ReasonParam.MANDATORY_PARAM);
		}
		
		balance.setRefNo(request.getRefNumber());
		
		balance.setDtmUpd(new Date());
		balance.setUsrUpd(audit.getCallerId());
		daoFactory.getBalanceMutationDao().updateTrBalanceMutation(balance);
		
		Status status = new Status();
		status.setMessage(getMessage("businesslogic.saldo.successupdate", null, audit));
		MssResponseType response = new MssResponseType();
		response.setStatus(status);
		return response;
	}

	@Override
	public BalanceResponse getBalanceAllTenant(BalanceRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		commonValidatorLogic.validateNotNull(request.getVendorCode(), CONST_VENDOR_CODE, audit);
		
		MsVendor vendor = vendorValidatorLogic.validateVendorOfTenant(request.getVendorCode(), request.getTenantCode(), true, audit); 		

		return this.getBalanceByVendorAndTenant(request.getBalanceType(), tenant, vendor, audit);
	}
	
	@Override
	public ListBalanceHistoryResponse getListBalanceMutationAllTenant(ListBalanceHistoryRequest request, AuditContext audit) throws ParseException {
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		commonValidatorLogic.validateNotNull(request.getVendorCode(), CONST_VENDOR_CODE, audit);
		
		MsVendor vendor = vendorValidatorLogic.validateVendorOfTenant(request.getVendorCode(), request.getTenantCode(), true, audit); 
	
		if (StringUtils.isBlank(request.getBalanceType())) {
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_PLEASE_CHOOSE_BALANCE_TYPE, null, this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}
		
		balanceValidatorLogic.validateBalanceMsUserOfTenant(request.getBalanceType(), tenant, vendor, audit);
		
		MsLov balanceTypeLov = commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, request.getBalanceType(), audit);
		
		if (StringUtils.isNotBlank(request.getTransactionType())) {			
			MsLov trxTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, request.getTransactionType());
			if(trxTypeLov == null) {
				throw new DocumentException(this.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, new String[] { request.getTransactionType() }, audit), ReasonDocument.PARAM_INVALID);
			}
		}

		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);
		//penjagaan batas max hari
		if (!isDateRangeValid(request.getTransactionDateStart(), request.getTransactionDateEnd(), audit)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE_GENERAL_SETTING,
					new Object[] {GlobalVal.CONST_TRANSAKSI,maxRangeDate}, this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_DATE_RANGE);
		}
		
		Date dateStart = MssTool.formatStringToDate(request.getTransactionDateStart(), GlobalVal.DATE_FORMAT);
		Date dateEnd = MssTool.formatStringToDate(request.getTransactionDateEnd(), GlobalVal.DATE_FORMAT);
		
		if (StringUtils.isNotBlank(request.getDocumentType())) {			
			MsLov lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE, request.getDocumentType());
			if (null == lovDocType) {
				String[] errParams = { "Doc Type " + request.getDocumentType() };
				throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, errParams, retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
			}
		}
		
		if (StringUtils.isNotBlank(request.getOfficeCode())) {
			MsOffice tenantOffice = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(request.getOfficeCode(), request.getTenantCode());
			if (tenantOffice == null) {
				throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_BRANCH_NOT_EXIST, null, this.retrieveLocaleAudit(audit)), ReasonDocument.BRANCH_NOT_EXIST);
			}
		}
		
		if (StringUtils.isNotBlank(request.getStatus()) && 
			    !(GlobalVal.SERVICES_RESULT_SUCCESS.equals(request.getStatus()) || "Failed".equals(request.getStatus()))) {   
			    request.setStatus("");
		}
		
		ListBalanceHistoryResponse response = new ListBalanceHistoryResponse();

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		
		List<BalanceMutationBean> result = daoFactory.getBalanceMutationDao().getListBalanceMutation(tenant, vendor, balanceTypeLov, 
												request.getTransactionType(), dateStart, dateEnd, request.getDocumentType(), request.getReferenceNo(), request.getDocumentName(),
												min, max, request.getOfficeCode(), request.getStatus());
		
		int totalResult = daoFactory.getBalanceMutationDao().countBalanceMutation(tenant, vendor, balanceTypeLov,
												request.getTransactionType(), dateStart, dateEnd, request.getDocumentType(), request.getReferenceNo(), request.getDocumentName(), request.getOfficeCode(),
												request.getStatus());
		double totalPage =  Math.ceil((double) totalResult / maxRow);
		
		response.setListMutation(result);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalResult);
		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		
		return response;
	}
	
	public DownloadListBalanceMutationResponse downloadListBalanceMutationAllTenant(
			DownloadListBalanceMutationRequest req, AuditContext audit) throws ParseException {
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(req.getTenantCode(), true, audit);
		commonValidatorLogic.validateNotNull(req.getVendorCode(), CONST_VENDOR_CODE, audit);
		MsVendor vendor = vendorValidatorLogic.validateVendorOfTenant(req.getVendorCode(), req.getTenantCode(), true,
				audit);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				req.getBalanceType());

		commonValidatorLogic.validateNotNull(req.getBalanceType(),
				messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_PLEASE_CHOOSE_BALANCE_TYPE, null,
						this.retrieveLocaleAudit(audit)),
				StatusCode.SALDO_BALANCE_TYPE_CODE_EMPTY);

		balanceValidatorLogic.validateBalanceMsUserOfTenant(req.getBalanceType(), tenant, vendor, audit);

		DownloadListBalanceMutationResponse response = new DownloadListBalanceMutationResponse();

		if (StringUtils.isNotBlank(req.getTransactionType())) {
			MsLov trxTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE,
					req.getTransactionType());
			commonValidatorLogic.validateNotNull(trxTypeLov,
					this.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
							new String[] { req.getTransactionType() }, audit),
					StatusCode.ENTITY_NOT_FOUND);
		}

		String maxRangeDate = daoFactory.getGeneralSettingDao()
				.getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);
		// penjagaan batas 30 hari
		if (!isDateRangeValid(req.getTransactionDateStart(), req.getTransactionDateEnd(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE_GENERAL_SETTING,
							new Object[] { GlobalVal.CONST_TRANSAKSI, maxRangeDate }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}

		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		Date dateStart = null;
		Date dateEnd = null;
		if (!StringUtils.isBlank(req.getTransactionDateStart())) {
			dateStart = sdf.parse(req.getTransactionDateStart());
		}

		if (!StringUtils.isBlank(req.getTransactionDateEnd())) {
			dateEnd = sdf.parse(req.getTransactionDateEnd());
		}

		if (StringUtils.isNotBlank(req.getDocumentType())) {
			MsLov lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE,
					req.getDocumentType());
			commonValidatorLogic.validateNotNull(lovDocType,
					messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
							new String[] { "Doc Type " + req.getDocumentType() }, this.retrieveLocaleAudit(audit)),
					StatusCode.ENTITY_NOT_FOUND);
		}

		if (StringUtils.isNotBlank(req.getOfficeCode())) {
			MsOffice tenantOffice = daoFactory.getOfficeDao()
					.getActiveOfficeByOfficeCodeAndTenantCode(req.getOfficeCode(), req.getTenantCode());
			commonValidatorLogic.validateNotNull(tenantOffice, messageSource
					.getMessage(GlobalKey.MESSAGE_ERROR_BRANCH_NOT_EXIST, null, this.retrieveLocaleAudit(audit)),
					StatusCode.BRANCH_NOT_EXIST);
		}

		if (StringUtils.isNotBlank(req.getStatus()) &&
				!(GlobalVal.SERVICES_RESULT_SUCCESS.equals(req.getStatus()) || "Failed".equals(req.getStatus()))) {
			req.setStatus("");
		}

		List<BalanceMutationBean> listBalanceMutation = daoFactory.getBalanceMutationDao().getListBalanceMutation(
				tenant, vendor, balanceType, req.getTransactionType(),
				dateStart, dateEnd, req.getDocumentType(), req.getReferenceNo(), req.getDocumentName(), 0, 0,
				req.getOfficeCode(),
				req.getStatus());

		StringBuilder name = new StringBuilder();
		name.append(BALMUT_FILENAME_PREFIX);
		appendFilenameParts(name, req);
		name.append(GlobalVal.FILE_EXTENTION_XLSX);
		response.setFilename(name.toString());

		byte[] excel = null;
		try {
			excel = this.excelLogic.generate((workbook, styleBoldText) -> this.createListBalanceMutationSheet(workbook,
					styleBoldText, listBalanceMutation, tenant.getRefNumberLabel()));
		} catch (IOException e) {
			LOG.error("Download list balance mutation error", e);
			Status status = new Status();
			status.setCode(200);
			status.setMessage(e.getMessage());
		}
		String base64 = Base64.getEncoder().encodeToString(excel);
		response.setExcelBase64(base64);

		return response;
	}

	private void appendFilenameParts(StringBuilder name, DownloadListBalanceMutationRequest req) {
		if (StringUtils.isNotBlank(req.getTenantCode())) {
			name.append("_" + DownloadListBalanceMutationRequest.TENANT_CODE + req.getTenantCode());
		}
		if (StringUtils.isNotBlank(req.getVendorCode())) {
			name.append("_" + DownloadListBalanceMutationRequest.VENDOR_CODE + req.getVendorCode());
		}
		if (StringUtils.isNotBlank(req.getBalanceType())) {
			name.append("_" + DownloadListBalanceMutationRequest.BALANCE_TYPE + req.getBalanceType());
		}
		if (StringUtils.isNotBlank(req.getDocumentName())) {
			name.append("_" + DownloadListBalanceMutationRequest.DOC_NAME + req.getDocumentName());
		}
		if (StringUtils.isNotBlank(req.getDocumentType())) {
			name.append("_" + DownloadListBalanceMutationRequest.DOC_TYPE + req.getDocumentType());
		}
		if (StringUtils.isNotBlank(req.getReferenceNo())) {
			name.append("_" + DownloadListBalanceMutationRequest.REF_NO + req.getReferenceNo());
		}
		if (StringUtils.isNotBlank(req.getTransactionDateStart())) {
			name.append("_" + DownloadListBalanceMutationRequest.TRX_DATE_START + req.getTransactionDateStart());
		}
		if (StringUtils.isNotBlank(req.getTransactionDateEnd())) {
			name.append("_" + DownloadListBalanceMutationRequest.TRX_DATE_END + req.getTransactionDateEnd());
		}
		if (StringUtils.isNotBlank(req.getTransactionType())) {
			name.append("_" + DownloadListBalanceMutationRequest.TRX_TYPE + req.getTransactionType());
		}
	}
	
	
}
