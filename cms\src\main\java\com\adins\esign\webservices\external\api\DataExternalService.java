package com.adins.esign.webservices.external.api;

import com.adins.esign.webservices.model.GetDocumentEMateraiTypeResponse;
import com.adins.esign.webservices.model.GetDocumentTypeRequest;
import com.adins.esign.webservices.model.GetDocumentTypeResponse;
import com.adins.esign.webservices.model.GetListBalanceTypeExternalRequest;
import com.adins.esign.webservices.model.GetListBalanceTypeExternalResponse;
import com.adins.esign.webservices.model.GetListPsrePriorityExternalResponse;
import com.adins.esign.webservices.model.GetPeruriDocumentTypeRequest;
import com.adins.esign.webservices.model.external.GetDistrictListExternalRequest;
import com.adins.esign.webservices.model.external.GetDistrictListExternalResponse;
import com.adins.esign.webservices.model.external.GetProvinceListExternalRequest;
import com.adins.esign.webservices.model.external.GetProvinceListExternalResponse;
import com.adins.esign.webservices.model.external.GetSubDistrictListExternalRequest;
import com.adins.esign.webservices.model.external.GetSubDistrictListExternalResponse;

public interface DataExternalService {
	GetProvinceListExternalResponse getProvinceListExternal(GetProvinceListExternalRequest request);
	GetDistrictListExternalResponse getDistrictListExternal(GetDistrictListExternalRequest request);
	GetSubDistrictListExternalResponse getSubDistrictListExternal(GetSubDistrictListExternalRequest request);
	GetListBalanceTypeExternalResponse getListbalance(GetListBalanceTypeExternalRequest request);
	GetDocumentTypeResponse getListDocumentType(GetDocumentTypeRequest request);
	GetDocumentEMateraiTypeResponse getListPeruriDocumentTypePublic(GetPeruriDocumentTypeRequest request);
	GetListPsrePriorityExternalResponse getListPsrePriority(GetListPsrePriorityExternalResponse request);
}
