package com.adins.esign;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.cxf.Bus;
import org.apache.cxf.endpoint.Server;
import org.apache.cxf.ext.logging.LoggingFeature;
import org.apache.cxf.feature.Feature;
import org.apache.cxf.jaxrs.JAXRSServerFactoryBean;
import org.apache.cxf.jaxrs.swagger.Swagger2Feature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import com.adins.esign.webservices.confins.api.ConfinsDocumentService;
import com.adins.esign.webservices.confins.api.ConfinsStampDutyService;
import com.adins.esign.webservices.embed.api.DataEmbedService;
import com.adins.esign.webservices.embed.api.DocumentEmbedService;
import com.adins.esign.webservices.embed.api.FeedbackEmbedService;
import com.adins.esign.webservices.embed.api.SaldoEmbedService;
import com.adins.esign.webservices.embed.api.TenantEmbedService;
import com.adins.esign.webservices.embed.api.UserEmbedService;
import com.adins.esign.webservices.external.api.DataExternalService;
import com.adins.esign.webservices.external.api.DocumentExternalService;
import com.adins.esign.webservices.external.api.LivenessExternalService;
import com.adins.esign.webservices.external.api.SaldoExternalService;
import com.adins.esign.webservices.external.api.UserExternalService;
import com.adins.esign.webservices.external.api.VendorExternalService;
import com.adins.esign.webservices.frontend.api.AutosignService;
import com.adins.esign.webservices.frontend.api.CallbackService;
import com.adins.esign.webservices.frontend.api.DataService;
import com.adins.esign.webservices.frontend.api.DocumentService;
import com.adins.esign.webservices.frontend.api.ErrorReportService;
import com.adins.esign.webservices.frontend.api.FeedbackService;
import com.adins.esign.webservices.frontend.api.JobResultService;
import com.adins.esign.webservices.frontend.api.JobService;
import com.adins.esign.webservices.frontend.api.LivenessService;
import com.adins.esign.webservices.frontend.api.ManualReportService;
import com.adins.esign.webservices.frontend.api.MateraiService;
import com.adins.esign.webservices.frontend.api.MenuService;
import com.adins.esign.webservices.frontend.api.MessageDeliveryReportService;
import com.adins.esign.webservices.frontend.api.NotificationService;
import com.adins.esign.webservices.frontend.api.RoleService;
import com.adins.esign.webservices.frontend.api.SaldoService;
import com.adins.esign.webservices.frontend.api.SigningProcessAuditTrailService;
import com.adins.esign.webservices.frontend.api.TenantService;
import com.adins.esign.webservices.frontend.api.TenantSettingsService;
import com.adins.esign.webservices.frontend.api.UserManagementService;
import com.adins.esign.webservices.frontend.api.UserService;
import com.adins.esign.webservices.frontend.api.VendorService;
import com.adins.cxf.SubsystemContextInterceptor;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.jaxrs.json.JacksonJsonProvider;

@Configuration
public class EsignWsConfiguration {
	
	@Value("${cxf.logging.limit:500000}") private int loggingLimit;
	@Value("${logging.sensitive.parameter}") private String sensitiveParams;
	
	@Bean
	@ConditionalOnProperty(value = "cxf.feature.swagger2.enabled", matchIfMissing = false, havingValue = "true")
	public Swagger2Feature swagger2Feature() {
		return new Swagger2Feature();
	}
	
	private Set<String> getSensitiveParameters() {
		String[] arrays = sensitiveParams.split(";");
		List<String> list = Arrays.asList(arrays);
		return new HashSet<>(list);
	}

	@Bean
	@ConditionalOnProperty(value = "cxf.feature.logging.enabled", matchIfMissing = true, havingValue = "true")
	public LoggingFeature loggingFeature() {
		LoggingFeature loggingFeature = new LoggingFeature();
		loggingFeature.addSensitiveElementNames(getSensitiveParameters());
		loggingFeature.setLogBinary(true);
		loggingFeature.setLimit(loggingLimit);
		return loggingFeature;
	}

	@Bean
	public Server rsServer(Environment environment, Bus bus, UserService userService, DocumentService documentService, 
			ConfinsDocumentService confinsDocumentService, DataService dataService, FeedbackService feedbackService, JobService jobService, 
			SaldoService saldoService, MateraiService materaiService, CallbackService callbackService, TenantService tenantService,
			ConfinsStampDutyService confinsStampDutyService, LivenessService livenessService, ErrorReportService errorreportService,
			JobResultService jobResultService, RoleService roleService, UserManagementService userManagementService, DocumentExternalService documentExternalService, 
			UserExternalService userExternalService, SaldoEmbedService saldoEmbedService, DocumentEmbedService documentEmbedService,
			TenantEmbedService tenantEmbedService, UserEmbedService userEmbedService, FeedbackEmbedService feedbackEmbedService, DataEmbedService dataEmbedService, VendorService vendorService,
			MessageDeliveryReportService messageDeliveryReportService, DataExternalService dataExternalService, SaldoExternalService saldoExternalService,
			LivenessExternalService livenessExternalService, AutosignService autosignService, VendorExternalService vendorExternalService,SigningProcessAuditTrailService signingProcessAuditTrailService,
			ManualReportService manualReportService, TenantSettingsService tenantSettingsService, NotificationService notificationService, MenuService menuService) {
		JAXRSServerFactoryBean endpoint = new JAXRSServerFactoryBean();
		final Map<String, Object> endpointProps = new HashMap<>();
		endpointProps.put("org.apache.cxf.endpoint.private", "true");

		endpoint.setBus(bus);
		endpoint.setAddress("/");
		endpoint.setProperties(endpointProps);

		endpoint.setServiceBeans(Arrays.<Object>asList(
				userService, documentService, confinsDocumentService, dataService, feedbackService, saldoService, materaiService,
				callbackService, tenantService, confinsStampDutyService, livenessService, errorreportService, jobService, jobResultService,
				roleService, userManagementService, documentExternalService,userExternalService, saldoEmbedService, documentEmbedService,
				tenantEmbedService, userEmbedService, feedbackEmbedService, dataEmbedService, vendorService, messageDeliveryReportService, dataExternalService, saldoExternalService,
				livenessExternalService, autosignService, vendorExternalService,signingProcessAuditTrailService, manualReportService, tenantSettingsService, notificationService,
				menuService
		));

		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
		objectMapper.setSerializationInclusion(Include.NON_NULL);
		objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
		JacksonJsonProvider jsonProvider = new JacksonJsonProvider(objectMapper);

		List<?> providers = Arrays.asList(jsonProvider);
		endpoint.setProviders(providers);
		List<Feature> features = new ArrayList<>(1);
		if ("true".equalsIgnoreCase(environment.getProperty("cxf.feature.logging.enabled", "true"))) {
			features.add(loggingFeature());
		}
		if ("true".equalsIgnoreCase(environment.getProperty("cxf.feature.swagger2.enabled", "true"))) {
			features.add(swagger2Feature());
		}
		endpoint.setFeatures(features);
		endpoint.getInInterceptors().add(new SubsystemContextInterceptor("WebServices"));
		return endpoint.create();
	}
}
