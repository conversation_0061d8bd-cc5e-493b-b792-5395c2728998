package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class EmailException extends AdInsException {
	private static final long serialVersionUID = 1L;
	
	public enum ReasonEmail {
		SEND_EMAIL_ERROR,
		READ_EMAIL_ERROR,
		DELETE_EMAIL_ERROR,
		SEND_SMS_ERROR,
		CREATE_EMAIL_ERROR,
		UNKNOWN
	}
	
	private final ReasonEmail reason;
	
	public EmailException(ReasonEmail reason) {
		this.reason = reason;
	}
	
	public EmailException(String message, ReasonEmail reason) {
		super(message);
		this.reason = reason;
	}
	
	public EmailException(Throwable ex, ReasonEmail reason) {
		super(ex);
		this.reason = reason;
	}
	
	public EmailException(String message, Throwable ex, ReasonEmail reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	public ReasonEmail getReason() {
		return reason;
	}
	
	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
			case SEND_EMAIL_ERROR:
				return StatusCode.SEND_EMAIL_ERROR;
			case READ_EMAIL_ERROR:
				return StatusCode.READ_EMAIL_ERROR;
			case DELETE_EMAIL_ERROR:
				return StatusCode.DELETE_EMAIL_ERROR;
			case SEND_SMS_ERROR:
				return StatusCode.SEND_SMS_ERROR;
			case CREATE_EMAIL_ERROR:
				return StatusCode.CREATE_EMAIL_ERROR;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}
}
