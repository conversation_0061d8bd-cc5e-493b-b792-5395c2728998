package com.adins.esign.businesslogic.impl;

import java.util.HashMap;
import java.util.Map;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.cxf.jaxrs.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.SendSmsResponse;
import com.google.gson.Gson;

@Primary
@Component
public class WassengerSmsLogic {
    
	@Value("${sms.url}") private String smsUrl;
	@Value("${sms.password}") private String smsToken;
	@Autowired private Gson gson;
	private static final Logger LOG = LoggerFactory.getLogger(WassengerSmsLogic.class);

	public SendSmsResponse sendSms(String phoneNo, String msg) {
		String phoneNoPrefixPlus62 = MssTool.changePrefixToPlus62(phoneNo);
		
		WebClient webClient = WebClient.create(smsUrl)
				.header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
				.header("Token", smsToken);
		
		Map<String, Object> payloadMap = new HashMap<>();
		payloadMap.put("phone", phoneNoPrefixPlus62);
		payloadMap.put("message", msg);
		String body = gson.toJson(payloadMap);
		
		try (Response response = webClient.post(body)) {
			LOG.debug("Wassenger response httpStatus={}", response.getStatus());
		}
		
		return new SendSmsResponse();
	}

}
