package com.adins.esign.dataaccess.api;

import java.util.Date;
import java.util.List;

import com.adins.esign.model.TrErrorHistory;
import com.adins.esign.model.TrErrorHistoryUserDetail;
import com.adins.esign.model.custom.ErrorHistoryBean;


public interface ErrorHistoryDao {
	
	void insertErrorHistory(TrErrorHistory errorHistory);
	void updateErrorHistory(TrErrorHistory errorHistory);
	
	void insertErrorHistoryUserDetail(TrErrorHistoryUserDetail errHistUserDetail);
	
	TrErrorHistory getLatestErrorHistoryByNik(String nik);
	TrErrorHistory getLatestErrorHistoryByNikAndIdMsVendor(String nik, Long idVendor);
	TrErrorHistory getErrorHistoryByIdErrorHistory(Long idErrorHistory);
	
	List<TrErrorHistory> getErrorHistoryListByRerunProcess(String rerunProcess);
	
	TrErrorHistoryUserDetail getErrorHistoryUserDetail(TrErrorHistory errorHistory, String signerTypeCode);
	
	List<TrErrorHistoryUserDetail> getErrorHistoryUserDetailByIdErrorHistory(Long idErrorHistory);
	
	List<ErrorHistoryBean> getListErrorHistory(int min, int max, String tenantCode, String modul, String refNumber, String namaKonsumen,
			String cabang, String region, String businessLine, Date tanggalDari, Date tanggalSampai, String tipe);
	
	Integer countListErrorHistory(String tenantCode, String modul, String refNumber, String namaKonsumen,
			String cabang, String region, String businessLine, Date tanggalDari, Date tanggalSampai, String tipe);
	
}
