package com.adins.esign.validatorlogic.impl;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.annotations.DecimalLimit;
import com.adins.esign.annotations.Required;
import com.adins.esign.annotations.StringLength;
import com.adins.esign.annotations.ValidationObjectName;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.validatorlogic.api.AnnotationValidatorLogic;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

@Component
public class GenericAnnotationValidatorLogic extends BaseLogic implements AnnotationValidatorLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericAnnotationValidatorLogic.class);

	@Override
	public void validateAttributes(Object object, AuditContext audit) {
		if (null == object) {
			return;
		}
		
		List<Field> fields = new ArrayList<>();
		
		Class<?> currentClass = object.getClass();
		Class<?> inheritedClass = object.getClass().getSuperclass();
		
		// Get field from inherited class
		// Currently only support checking 1 level of inheritance
		if (null != inheritedClass && MssResponseType.class != inheritedClass && Object.class != inheritedClass) {
			Collections.addAll(fields, inheritedClass.getDeclaredFields());
		}
		
		// Get field from current class
		Collections.addAll(fields, currentClass.getDeclaredFields());
		
		for (Field field : fields) {
			
			if (field.isAnnotationPresent(Required.class)) {
				validateMandatoryField(object, field, audit);
			}
			
			if (field.isAnnotationPresent(StringLength.class)) {
				validateStringLength(object, field, audit);
			}
			
			if (field.isAnnotationPresent(DecimalLimit.class)) {
				validateDecimalLimit(object, field, audit);
			}
			
		}
	}
	
	private Object getFieldValue(Object object, Field field) {
		try {
			String fieldName = field.getName();
			String getterMethodName = "get" + Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);
			Method getterMethod = object.getClass().getMethod(getterMethodName);
			return getterMethod.invoke(object);
		} catch (Exception e) {
			LOG.error("Failed to extract field value with error: {}", e.getLocalizedMessage());
			return null;
		}
	}
	
	private String getFieldName(Field field) {
		if (field.isAnnotationPresent(ValidationObjectName.class)) {
			return field.getAnnotation(ValidationObjectName.class).value();
		}
		return field.getName();
	}
	
	private void validateMandatoryField(Object object, Field field, AuditContext audit) {
		Required required = field.getAnnotation(Required.class);
		Object value = getFieldValue(object, field);
		
		if (null == value) {
			String objectName = getFieldName(field);
			throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {objectName}, audit), ReasonCommon.MANDATORY_PARAM);
		}
		
		if (value instanceof String && !required.allowBlankString()) {
			String valueString = (String) value;
			if (StringUtils.isBlank(valueString)) {
				String objectName = getFieldName(field);
				throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {objectName}, audit), ReasonCommon.MANDATORY_PARAM);
			}
		}
		
		if (value instanceof Collection && !required.allowEmptyCollection()) {
			Collection<?> listValue = (Collection<?>) value;
			if (CollectionUtils.isEmpty(listValue)) {
				String objectName = getFieldName(field);
				throw new CommonException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {objectName}, audit), ReasonCommon.MANDATORY_PARAM);
			}
		}
	}
	
	private void validateStringLength(Object object, Field field, AuditContext audit) {
		StringLength stringLength = field.getAnnotation(StringLength.class);
		Object value = getFieldValue(object, field);
		
		if (null == value) {
			return;
		}
		
		if (!(value instanceof String)) {
			return;
		}
		
		String valueString = (String) value;
		
		// Validate min length
		int minLength = stringLength.minValue();
		if (minLength > 0 && valueString.length() < minLength) {
			String objectName = getFieldName(field);
			throw new CommonException(getMessage("businesslogic.global.stringminlength", new Object[] {objectName, minLength}, audit), ReasonCommon.INVALID_VALUE);
		}
		
		// Validate max length
		int maxLength = stringLength.maxValue();
		if (maxLength > 0 && valueString.length() > maxLength) {
			String objectName = getFieldName(field);
			throw new CommonException(getMessage("businesslogic.global.stringmaxlength", new Object[] {objectName, maxLength}, audit), ReasonCommon.INVALID_CONDITION);
		}
	}
	
	private void validateDecimalLimit(Object object, Field field, AuditContext audit) {
		DecimalLimit decimalLimit = field.getAnnotation(DecimalLimit.class);
		Object value = getFieldValue(object, field);
		
		if (null == value) {
			return;
		}
		
		if (!(value instanceof BigDecimal)) {
			return;
		}
		
		BigDecimal decimal = (BigDecimal) value;
		
		// Validasi digit di belakang koma
		if (decimal.scale() > decimalLimit.scale()) {
			String fieldName = getFieldName(field);
			throw new CommonException(getMessage("businesslogic.global.decimal.point", new Object[] {fieldName, decimalLimit.scale()}, audit), ReasonCommon.INVALID_VALUE);
		}
		
		// Validasi total digit
		if (decimal.precision() > decimalLimit.precision()) {
			String fieldName = getFieldName(field);
			throw new CommonException(getMessage("businesslogic.global.decimal.digit", new Object[] {fieldName, decimalLimit.precision()}, audit), ReasonCommon.INVALID_VALUE);
		}
		
		// Validasi max value
		int n = decimalLimit.precision() - decimalLimit.scale();
		BigDecimal ceiling = BigDecimal.TEN.pow(n);
		if (decimal.abs().compareTo(ceiling) >= 0) {
			String fieldName = getFieldName(field);
			String maxValueString = "10^" + n;
			throw new CommonException(getMessage("businesslogic.global.decimal.maxvalue", new Object[] {fieldName, maxValueString}, audit), ReasonCommon.INVALID_VALUE);
		}
	}

}
