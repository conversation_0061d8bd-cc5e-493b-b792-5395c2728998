package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.OauthAccessTokenDB;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.dataaccess.api.OauthAccessTokenDBDao;
import com.adins.esign.dataaccess.factory.api.DaoFactory;

@Transactional
@Component
public class OauthAccessTokenDaoHbn extends BaseDaoHbn implements OauthAccessTokenDBDao{
	@Autowired DaoFactory daoFactory;
	
	
	@Override
	public OauthAccessTokenDB getOauthByUserName(String username) {
	    Map<String, Object> params = new HashMap<>();
	    params.put("username", username);
	    
	    return this.managerDAO.selectOne(
	            "from OauthAccessTokenDB "
	            + "where username = :username"
	            , params);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<OauthAccessTokenDB> getOauthByUpperUserName(String username) {
	    Map<String, Object> params = new HashMap<>();
	    params.put("username", username);
	    
		Object[][] param = new Object[][] {{"username", username}};
		
		StringBuilder query = new StringBuilder();
		query.append("from OauthAccessTokenDB vru ")
			.append(" where UPPER(username) = :username ");
		
		Map<String, Object> listResult = this.managerDAO.selectAll(query.toString(), param);
		return (List<OauthAccessTokenDB>) listResult.get(AmGlobalKey.MAP_RESULT_LIST);   
	}
	
	@Override
	public void deleteOauthByUserName(OauthAccessTokenDB oauthAccessTokenDB) {
	    this.managerDAO.delete(oauthAccessTokenDB);
	}
}
