package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class ErrorHistoryRerunSendDocJob {
	private static final Logger LOG = LoggerFactory.getLogger(ErrorHistoryRerunSendDocJob.class);
	private static final String SCHEDULER = "SCHEDULER";
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	
	public void runErrorHistoryRerunSendDoc() {
		try {
			LOG.info("Error history rerun job started");
			AuditContext audit = new AuditContext(SCHEDULER);
			schedulerLogic.errorHistoryRerunSendDoc(audit);
			LOG.info("Error history rerun job finished");
		} catch (Exception e) {
			LOG.error("Error history rerun job error", e);
		}
	}
}
