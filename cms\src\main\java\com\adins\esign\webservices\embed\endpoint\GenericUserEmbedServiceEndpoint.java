package com.adins.esign.webservices.embed.endpoint;

import java.io.UnsupportedEncodingException;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.InvitationLinkLogic;
import com.adins.esign.businesslogic.api.embed.UserEmbedLogic;
import com.adins.esign.webservices.embed.api.UserEmbedService;
import com.adins.esign.webservices.model.GenerateInvitationLinkForExpiredCertResponse;
import com.adins.esign.webservices.model.embed.GenerateInvitationLinkForExpiredCertEmbedRequest;
import com.adins.esign.webservices.model.embed.GetSignerDetailEmbedRequest;
import com.adins.esign.webservices.model.embed.GetSignerDetailEmbedResponse;
import com.adins.esign.webservices.model.embed.SentOtpSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.SentOtpSigningEmbedResponse;
import com.adins.esign.webservices.model.embed.SignerDataVerificationEmbedRequest;
import com.adins.esign.webservices.model.embed.SignerDataVerificationEmbedResponse;
import com.adins.esign.webservices.model.embed.VerifyLivenessFaceCompareEmbedRequest;
import com.adins.esign.webservices.model.embed.VerifyLivenessFaceCompareEmbedResponse;
import com.adins.esign.webservices.model.embed.VerifyOtpSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.VerifyOtpSigningEmbedResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/embed/user")
@Api(value = "UserEmbedService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericUserEmbedServiceEndpoint implements UserEmbedService {
	
	@Autowired private UserEmbedLogic userEmbedLogic;
	@Autowired private InvitationLinkLogic invitationLinkLogic;

	@Override
	@POST
	@Path("/signerDataVerificationEmbed")
	public SignerDataVerificationEmbedResponse signerDataVerificationEmbed(SignerDataVerificationEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userEmbedLogic.signerDataVerificationEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/getSignerDetailEmbed")
	public GetSignerDetailEmbedResponse getSignerDetailEmbed(GetSignerDetailEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userEmbedLogic.getSignerDetailEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/verifyLivenessFaceCompareEmbed")
	public VerifyLivenessFaceCompareEmbedResponse verifyLivenessFaceCompareEmbed(VerifyLivenessFaceCompareEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userEmbedLogic.verifyLivenessFaceCompareEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/sentOtpSigningEmbed")
	public SentOtpSigningEmbedResponse sentOtpSigningEmbed(SentOtpSigningEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userEmbedLogic.sentOtpSigningEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/verifyOtpSigningEmbed")
	public VerifyOtpSigningEmbedResponse verifyOtpSigningEmbed(VerifyOtpSigningEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userEmbedLogic.verifyOtpSigningEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/generateInvitationLinkECertExpired")
	public GenerateInvitationLinkForExpiredCertResponse generateInvLinkECertExpired(
			GenerateInvitationLinkForExpiredCertEmbedRequest request) throws UnsupportedEncodingException {
		AuditContext audit = request.getAudit().toAuditContext();
		return invitationLinkLogic.generateInvLinkExpiredCertEmbed(request, audit);
	}

}
