package com.adins.esign.businesslogic.impl.interfacing;

import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.interfacing.DukcapilLogic;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.DukcapilRequestBean;
import com.adins.esign.model.custom.DukcapilResponseBean;
import com.adins.exceptions.RemoteException;

@Component("DummyDukcapil")
public class DummyDukcapilLogic implements DukcapilLogic {

	/**
	 * 0 = Skor selfie tidak lolos, data lain match
	 * Genap selain 0 = Sukses
	 * Ganjil selain 9 = Data not matched
	 * 9 = throw error
	 */
	@Override
	public DukcapilResponseBean verifyDataAndBiometric(MsTenant tenant, DukcapilRequestBean request) {
		String nik = request.getNik();
		String lastDigitNik = StringUtils.right(nik, 1);
		switch (lastDigitNik) {
			case "0":
				return this.generateLowSelfieScore();
			case "2":
			case "4":
			case "6":
			case "8":
				return this.generateSuccessBean();
			case "1":
			case "3":
			case "5":
			case "7":
				return this.generateNotMatchedBean();
			default:
				throw new RemoteException("Error on call e-KYC API");
		}
	}

	private DukcapilResponseBean generateLowSelfieScore() {
		double failedScore = RandomUtils.nextDouble(40, 65);
		return new DukcapilResponseBean(true, true, true, true, failedScore);
	}

	private DukcapilResponseBean generateSuccessBean() {
		double randomSuccessScore = RandomUtils.nextDouble(90, 99);
		return new DukcapilResponseBean(true, true, true, true, randomSuccessScore);
	}
	
	private DukcapilResponseBean generateNotMatchedBean() {
		double randomSuccessScore = RandomUtils.nextDouble(90, 99);
		return new DukcapilResponseBean(true, false, true, true, randomSuccessScore);
	}
}
