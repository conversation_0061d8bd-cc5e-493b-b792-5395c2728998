package com.adins.esign.businesslogic.api;

import java.text.ParseException;
import java.util.Date;

import javax.annotation.security.RolesAllowed;

import org.springframework.context.NoSuchMessageException;
import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.BalanceBean;
import com.adins.esign.webservices.model.AddBalanceTypeRequest;
import com.adins.esign.webservices.model.AddBalanceTypeResponse;
import com.adins.esign.webservices.model.BalanceEmbedRequest;
import com.adins.esign.webservices.model.BalanceRequest;
import com.adins.esign.webservices.model.BalanceResponse;
import com.adins.esign.webservices.model.CheckThresholdRequest;
import com.adins.esign.webservices.model.CheckThresholdResponse;
import com.adins.esign.webservices.model.DownloadListBalanceMutationEmbedRequest;
import com.adins.esign.webservices.model.DownloadListBalanceMutationRequest;
import com.adins.esign.webservices.model.DownloadListBalanceMutationResponse;
import com.adins.esign.webservices.model.EditRoleManagementRequest;
import com.adins.esign.webservices.model.ExtendTopUpBalanceRequest;
import com.adins.esign.webservices.model.GetListBalanceTypeExternalRequest;
import com.adins.esign.webservices.model.GetListBalanceTypeExternalResponse;
import com.adins.esign.webservices.model.GetListTopupBalanceRequest;
import com.adins.esign.webservices.model.GetListTopupBalanceResponse;
import com.adins.esign.webservices.model.GetListBalanceMutationEmbedRequest;
import com.adins.esign.webservices.model.GetListBalanceMutationExternalRequest;
import com.adins.esign.webservices.model.GetListBalanceMutationExternalResponse;
import com.adins.esign.webservices.model.ListBalanceHistoryRequest;
import com.adins.esign.webservices.model.ListBalanceHistoryResponse;
import com.adins.esign.webservices.model.ListBalanceTenantRequest;
import com.adins.esign.webservices.model.ListBalanceTenantResponse;
import com.adins.esign.webservices.model.ListBalanceTypeByVendorAndTenantRequest;
import com.adins.esign.webservices.model.ListBalanceTypeByVendorAndTenantResponse;
import com.adins.esign.webservices.model.ListBalanceVendoroftenantRequest;
import com.adins.esign.webservices.model.ListBalanceVendoroftenantResponse;
import com.adins.esign.webservices.model.SaldoConfigurationRequest;
import com.adins.esign.webservices.model.SaldoConfigurationResponse;
import com.adins.esign.webservices.model.SignBalanceAvailabilityRequest;
import com.adins.esign.webservices.model.SignBalanceAvailabilityResponse;
import com.adins.esign.webservices.model.UpdateBalanceTenantRequest;
import com.adins.esign.webservices.model.UpdateBalanceTenantResponse;
import com.adins.esign.webservices.model.UpdateRefNumberRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface SaldoLogic {
	@RolesAllowed({"ROLE_BALANCE", "ROLE_MANUAL_SIGN"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	BalanceResponse getBalance(BalanceRequest request, AuditContext audit);
	BalanceResponse getBalanceEmbed(BalanceEmbedRequest request, AuditContext audit);
	

	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isValidTenant('ADINS', authentication) and @esignSecurityServices.isRoleTenant('ROLE_BALANCE_ALL_TENANT', 'ADINS', authentication)")
	BalanceResponse getBalanceAllTenant(BalanceRequest request, AuditContext audit);
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isRoleTenant('ROLE_BALANCE_ALL_TENANT', 'ADINS', authentication)")
	ListBalanceHistoryResponse getListBalanceMutationAllTenant(ListBalanceHistoryRequest request, AuditContext audit) throws ParseException;
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isRoleTenant('ROLE_BALANCE_ALL_TENANT', 'ADINS', authentication)")
	DownloadListBalanceMutationResponse downloadListBalanceMutationAllTenant(DownloadListBalanceMutationRequest req, AuditContext audit) throws ParseException;

	
	@RolesAllowed({"ROLE_BALANCE"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ListBalanceHistoryResponse getListBalanceMutation(ListBalanceHistoryRequest request, AuditContext audit) throws ParseException;
	ListBalanceHistoryResponse getListBalanceMutationEmbed(GetListBalanceMutationEmbedRequest request, AuditContext audit) throws ParseException;
	GetListBalanceMutationExternalResponse getListBalanceMutationExternal ( GetListBalanceMutationExternalRequest request,String xApiKey, AuditContext audit) throws ParseException;

	@RolesAllowed({"ROLE_BALANCE"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	DownloadListBalanceMutationResponse downloadListBalanceMutation(DownloadListBalanceMutationRequest req, AuditContext audit) throws ParseException;
	DownloadListBalanceMutationResponse downloadListBalanceMutationEmbed(DownloadListBalanceMutationEmbedRequest req, AuditContext audit) throws ParseException;
	
	BalanceResponse getBalanceNotSecure(BalanceRequest request, AuditContext audit);
	
	void insertBalanceMutation(TrStampDuty stampDuty, TrDocumentH docH, TrDocumentD docD, MsLov balanceType,
			MsLov trxType, MsTenant tenant, MsVendor vendor, Date trxDate, String refNo, int qty,
			String trxNo, AmMsuser user, String notes, String vendorTrxNo,  MsOffice office, MsBusinessLine businessLine, AuditContext audit);
	
	void insertBalanceMutation(TrStampDuty stampDuty, TrDocumentH docH, TrDocumentD docD, MsLov balanceType,
			MsLov trxType, MsTenant tenant, MsVendor vendor, Date trxDate, String refNo, int qty,
			String trxNo, AmMsuser user, String notes, String vendorTrxNo, AuditContext audit);
	
	void insertBalanceMutationNewSesh(TrStampDuty stampDuty, TrDocumentH docH, TrDocumentD docD, String balanceType,
			String trxType, MsTenant tenant, MsVendor vendor, Date trxDate, String refNo, int qty,
			String trxNo, AmMsuser user, String notes, AuditContext audit);
	
	void sendReminderEmail(String tenantCode, String vendorCode, String balanceTypeCode) throws ParseException;
	BalanceBean checkSaldoBelowThreshold(MsTenant tenant, MsVendor vendor, MsLov balanceTypeLov);
	
	@RolesAllowed({"ROLE_TOPUP"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	SaldoConfigurationResponse configureSaldo(SaldoConfigurationRequest request, AuditContext audit) throws ParseException, IllegalAccessException, NoSuchMessageException;
	
	ListBalanceTenantResponse getListBalanceTenant(ListBalanceTenantRequest request, AuditContext audit);
	UpdateBalanceTenantResponse updateBalanceTenant(UpdateBalanceTenantRequest request, AuditContext audit);
	ListBalanceVendoroftenantResponse getListBalanceVendoroftenant(ListBalanceVendoroftenantRequest request, AuditContext audit);
	
	CheckThresholdResponse checkThreshold(CheckThresholdRequest request, AuditContext audit) throws ParseException;
	
	AddBalanceTypeResponse addBalanceType(AddBalanceTypeRequest request, AuditContext audit);
	AddBalanceTypeResponse editBalanceType(AddBalanceTypeRequest request, AuditContext audit);
	
	ListBalanceTypeByVendorAndTenantResponse getListBalanceTypeByVendorAndTenant(ListBalanceTypeByVendorAndTenantRequest request, AuditContext audit);
	
	SignBalanceAvailabilityResponse getSignBalanceAvailability(SignBalanceAvailabilityRequest request,AuditContext audit);
	GetListBalanceTypeExternalResponse getListBalanceTypeExternal(GetListBalanceTypeExternalRequest request,String xApiKey, AuditContext audit);
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isRoleTenant('ROLE_TOPUP_LIST', 'ADINS', authentication)")
	GetListTopupBalanceResponse getListTopupBalance(GetListTopupBalanceRequest request, AuditContext audit);
	
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication) and @esignSecurityServices.isRoleTenant('ROLE_TOPUP_LIST', 'ADINS', authentication)")
	MssResponseType updateRefNumber(UpdateRefNumberRequest request, AuditContext audit);

	@RolesAllowed("ROLE_TOPUP")
	MssResponseType extendTopupBalance(ExtendTopUpBalanceRequest request, AuditContext audit);
}
