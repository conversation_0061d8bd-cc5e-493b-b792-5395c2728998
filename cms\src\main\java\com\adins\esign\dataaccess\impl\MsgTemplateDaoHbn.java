package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.MsgTemplateDao;
import com.adins.esign.model.MsMsgTemplate;

@Transactional
@Component
public class MsgTemplateDaoHbn extends BaseDaoHbn implements MsgTemplateDao {

	@Override
	public MsMsgTemplate getTemplateByCode(String templateCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("templateCode", templateCode);
		return this.managerDAO.selectOne("from MsMsgTemplate where templateCode=:templateCode", params);
	}

	@Override
	public MsMsgTemplate getTemplateByTypeAndCode(String templateType, String templateCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("templateType", templateType);
		params.put("templateCode", templateCode);
		
		return this.managerDAO.selectOne("from MsMsgTemplate where templateType = :templateType and templateCode=:templateCode ", params);
	}

}
