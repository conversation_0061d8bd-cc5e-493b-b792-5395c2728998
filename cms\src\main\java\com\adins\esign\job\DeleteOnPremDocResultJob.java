package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class DeleteOnPremDocResultJob {
	private static final Logger LOG = LoggerFactory.getLogger(DeleteOnPremDocResultJob.class);
	private static final String SCHEDULER = "SCHEDULER";
	@Autowired SchedulerLogic schedulerLogic;
	
	public void runDeleteStampResultDocOnPremJob() {
		LOG.info("Job Delete Stamping Result On Premise Started");
		AuditContext audit = new AuditContext(SCHEDULER);
		schedulerLogic.deleteOnPremDocResultJob(audit);
		LOG.info("Job Delete Stamping Result On Premise Finished");
	}
}
