package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.dataaccess.api.EmailDao;
import com.adins.esign.model.MsEmailHosting;
import com.adins.esign.model.MsEmailPattern;

@Transactional
@Component
public class EmailDaoHbn extends BaseDaoHbn implements EmailDao{

	@Override
	public MsEmailHosting getEmailHostingById(Long id) {
		return this.managerDAO.selectOne(MsEmailHosting.class, id);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<MsEmailHosting> getListActiveEmailHosting() {	
		Object[][] params = new Object[][] {{ Restrictions.eq(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1")}};
		Map<String, Object> mapResult = this.managerDAO.selectAll(MsEmailHosting.class, params, null);
		
		return (List<MsEmailHosting>) mapResult.get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public MsEmailPattern getEmailPattern(String subjectEmail, String sender, String[] actions) {
		if (null == actions || actions.length == 0) {
			return null;
		}
		
		Map<String, Object> params = new HashMap<>();
		params.put(MsEmailPattern.SUBJECT_EMAIL_HBM, StringUtils.upperCase(subjectEmail));
		params.put(MsEmailPattern.EMAIL_SENDER_HBM, sender);
		params.put("actions", actions);
		
		return this.managerDAO.selectOne(
				"from MsEmailPattern ep "
				+ "join fetch ep.msLov "
				+ "join fetch ep.msVendor mv "
				+ "left join fetch ep.msMsgTemplate mmt "
				+ "left join fetch ep.msMsgTemplateWa mmtw "
				+ "left join fetch ep.lovSendingPoint lsp "
				+ "where ep.action in (:actions) and ep.subjectEmail = :subjectEmail "
				+ "and ep.emailSender = :emailSender "
				+ "and ep.isActive = '1' ", params);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<AmMsuser> getAllUserActiveEmailByHostingDomain(MsEmailHosting emailHosting) {
		Object[][] params = new Object[][] {{ Restrictions.eq("msEmailHosting", emailHosting) }};
		Map<String, Object> mapResult = this.managerDAO.selectAll(AmMsuser.class, params, null);
		return (List<AmMsuser>) mapResult.get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public MsEmailPattern getEmailPatternWithoutSender(String subjectEmail, String[] actions) {
		if (null == actions || actions.length == 0) {
			return null;
		}
		
		Map<String, Object> params = new HashMap<>();
		params.put(MsEmailPattern.SUBJECT_EMAIL_HBM, StringUtils.upperCase(subjectEmail));
		params.put("actions", actions);
		
		return this.managerDAO.selectOne(
				"from MsEmailPattern ep "
				+ "join fetch ep.msLov "
				+ "left join fetch ep.msMsgTemplate mmt "
				+ "left join fetch ep.msMsgTemplateWa mmtw "
				+ "where ep.action in (:actions) and ep.subjectEmail = :subjectEmail "
				+ "and ep.isActive = '1' ", params);
	}
}
