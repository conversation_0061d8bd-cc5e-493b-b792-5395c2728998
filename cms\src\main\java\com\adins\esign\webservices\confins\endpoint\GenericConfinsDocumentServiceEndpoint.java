
package com.adins.esign.webservices.confins.endpoint;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.SendDocumentLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.webservices.confins.api.ConfinsDocumentService;
import com.adins.esign.webservices.model.CancelAgreementRequest;
import com.adins.esign.webservices.model.CancelAgreementResponse;
import com.adins.esign.webservices.model.DocumentConfinsRequest;
import com.adins.esign.webservices.model.DocumentConfinsResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

import io.swagger.annotations.Api;

@Component
@Path("/confins/document")
@Api(value = "ConfinsDocumentService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericConfinsDocumentServiceEndpoint implements ConfinsDocumentService{
	@Autowired private DocumentLogic documentLogic;
	@Autowired private SendDocumentLogic sendDocumentLogic;
	
	@Override
	@POST
	@Path("/send")
	public DocumentConfinsResponse send(DocumentConfinsRequest documentConfinsRequest) throws Exception {
		AuditContext audit = documentConfinsRequest.getAudit().toAuditContext();
		
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		
		return sendDocumentLogic.sendDocument(
				documentConfinsRequest.getTenantCode(), documentConfinsRequest.getPsreCode(), documentConfinsRequest.getRequests(), xApiKey, audit);
	}
	
	@Override
	@POST
	@Path("/cancelAgreement")
	public CancelAgreementResponse cancelAgreement(CancelAgreementRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		audit.setCallerId(GlobalVal.CONST_CONFINS);
		
		CancelAgreementResponse response = new CancelAgreementResponse();
		Status status = new Status();
		status.setCode(9999);
		status.setMessage("This Endpoint is Deprecated.");
		response.setStatus(status);
		return response;
	}
}
