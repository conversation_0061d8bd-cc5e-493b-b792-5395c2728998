package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class EmeteraiOnPremException extends AdInsException {
	// Tidak pakai attribute Reason karena tidak dipakai sebagai response API
	private static final long serialVersionUID = 1L;
	private final String message;
	
	public EmeteraiOnPremException(String message) {
		super(message);
		this.message = message;
	}
	
	@Override
    public String getMessage() {
        return message;
    }

    @Override
    public String getLocalizedMessage() {
        return getMessage();
    }

	@Override
	public int getErrorCode() {
		return StatusCode.EMETERAI_ERROR;
	}

}
