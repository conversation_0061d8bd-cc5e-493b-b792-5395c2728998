package com.adins.esign.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsUserHistory;
import com.adins.am.model.AmUserPersonalDataHistory;
import com.adins.esign.dataaccess.api.UserHistoryDao;
import com.adins.esign.util.MssTool;
@Transactional
@Component
public class UserHistoryDaoHbn extends BaseDaoHbn implements UserHistoryDao {
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertUserHistroyNewTran(AmMsUserHistory newUserhistory) {
		this.managerDAO.insert(newUserhistory);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertUserPersonalDataHistroyNewTran(AmUserPersonalDataHistory newUserPersonalDataHistory) {
		this.managerDAO.insert(newUserPersonalDataHistory);
	}
}
