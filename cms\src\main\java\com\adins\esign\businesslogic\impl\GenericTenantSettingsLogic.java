package com.adins.esign.businesslogic.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.custom.TenantSettingsBean;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.webservices.model.ListTenantSettingsRequest;
import com.adins.esign.webservices.model.ListTenantSettingsResponse;
import com.adins.esign.webservices.model.SaveTenantSettingsRequest;
import com.adins.esign.webservices.model.SaveTenantSettingsResponse;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Component
public class GenericTenantSettingsLogic extends BaseLogic implements TenantSettingsLogic {

	@Autowired TenantValidatorLogic tenantValidatorLogic;
	@Autowired CommonValidatorLogic commonValidatorLogic;
	
	@Override
	public boolean getSettingValue(MsTenant tenant, String settingTypeCode) {
		MsTenantSettings setting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, settingTypeCode);
		if (null == setting || StringUtils.isBlank(setting.getSettingValue())) {
			return false;
		}
		
		return "1".equals(setting.getSettingValue());
	}

	@Override
	public int getSettingValue(MsTenant tenant, String settingTypeCode, int defaultValue) {
		MsTenantSettings setting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, settingTypeCode);
		if (null == setting || StringUtils.isBlank(setting.getSettingValue())) {
			return defaultValue;
		}
		
		try {
			return Integer.parseInt(setting.getSettingValue());
		} catch (Exception e) {
			return defaultValue;
		}
	}

	@Override
	public long getSettingValue(MsTenant tenant, String settingTypeCode, long defaultValue) {
		MsTenantSettings setting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, settingTypeCode);
		if (null == setting || StringUtils.isBlank(setting.getSettingValue())) {
			return defaultValue;
		}
		
		try {
			return Long.parseLong(setting.getSettingValue());
		} catch (Exception e) {
			return defaultValue;
		}
	}
	
	@Override
	public short getSettingValue(MsTenant tenant, String settingTypeCode, short defaultValue) {
		MsTenantSettings setting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, settingTypeCode);
		if (null == setting || StringUtils.isBlank(setting.getSettingValue())) {
			return defaultValue;
		}
		
		try {
			return Short.parseShort(setting.getSettingValue());
		} catch (Exception e) {
			return defaultValue;
		}
	}

	@Override
	public String getSettingValue(MsTenant tenant, String settingTypeCode, String defaultValue) {
		MsTenantSettings setting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, settingTypeCode);
		if (null == setting || StringUtils.isBlank(setting.getSettingValue())) {
			return defaultValue;
		}
		
		return setting.getSettingValue();
	}

	@Override
	public ListTenantSettingsResponse getListSettingValue(ListTenantSettingsRequest request, AuditContext audit) {
		ListTenantSettingsResponse response = new ListTenantSettingsResponse();
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		long totalResult = daoFactory.getTenantSettingsDao().countListTenantSettings(request, tenant.getIdMsTenant());
		double totalPage =  Math.ceil((double) totalResult / maxRow);
		
		List<TenantSettingsBean> settings = daoFactory.getTenantSettingsDao().getListTenantSettings(request, tenant.getIdMsTenant(), min, max);
		
		response.setTenantSettings(settings);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult((int) totalResult);
		
		return response;
	}

	@Override
	public SaveTenantSettingsResponse saveTenantSettings(SaveTenantSettingsRequest request, AuditContext audit) {
		
		String messageValidation = null;

		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_TENANT_CODE_EMPTY,  null, audit);
		commonValidatorLogic.validateNotNull(request.getTenantCode(), messageValidation, StatusCode.TENANT_CODE_EMPTY);

		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);

		MsLov tenantSettingsLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TENANT_SETTING_TYPE, request.getTenantSettingsCode());
	
		messageValidation = getMessage(GlobalKey.MESSAGE_TENANT_SETTINGS_NOT_FOUND, null, audit);
		commonValidatorLogic.validateNotNull(tenantSettingsLov, messageValidation, StatusCode.TENANT_SETTINGS_NOT_FOUND);

		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, request.getTenantSettingsCode());

		String value = cleanValueString(request.getTenantSettingsValue());
		
		if (tenantSettings == null) {
			MsTenantSettings newTenantSettings = new MsTenantSettings();
			newTenantSettings.setDtmCrt(new Date());
			newTenantSettings.setUsrCrt(audit.getCallerId());
			newTenantSettings.setSettingValue(value == null ? "" : value);
			newTenantSettings.setLovSettingType(tenantSettingsLov);
			newTenantSettings.setMsTenant(tenant);
			daoFactory.getTenantSettingsDao().inserMsTenantSettings(newTenantSettings);
		} else {
			tenantSettings.setSettingValue(value == null ? "" : value);
			tenantSettings.setDtmUpd(new Date());
			tenantSettings.setUsrUpd(audit.getCallerId());

			daoFactory.getTenantSettingsDao().updateMsTenantSettings(tenantSettings);
		}

		SaveTenantSettingsResponse response = new SaveTenantSettingsResponse();

		Status status = new Status();
		status.setCode(0);
		response.setStatus(status);

		return response;
	}

	public static String cleanValueString(String input) {
        if (input == null) {
            return null;
        }

        String cleaned = input.replace("\n", " ");

        cleaned = cleaned.trim();

        return cleaned;
    }

	@Override
	public MsTenantSettings getTenantSettings(MsTenant tenant, String settingTypeCode, boolean objectMustExist, AuditContext audit) {
		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, settingTypeCode);
		if (null == tenantSettings && objectMustExist) {
			throw new ParameterException(getMessage("businesslogic.tenantsettings.tenantsettingsnotfoundfortenant",
				new String[] {settingTypeCode, tenant.getTenantName()}, audit), ReasonParam.MANDATORY_PARAM);
		}

		return tenantSettings;
	}
	
}
