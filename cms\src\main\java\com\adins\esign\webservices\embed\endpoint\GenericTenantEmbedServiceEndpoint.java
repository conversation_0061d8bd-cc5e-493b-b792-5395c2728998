package com.adins.esign.webservices.embed.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.embed.TenantEmbedLogic;
import com.adins.esign.webservices.embed.api.TenantEmbedService;
import com.adins.esign.webservices.model.TenantSettingsEmbedRequest;
import com.adins.esign.webservices.model.TenantSettingsResponse;
import com.adins.esign.webservices.model.embed.CheckLivenessFaceCompareServiceEmbedRequest;
import com.adins.esign.webservices.model.embed.CheckLivenessFaceCompareServiceEmbedResponse;
import com.adins.esign.webservices.model.embed.GetAvailableSendingPointEmbedRequest;
import com.adins.esign.webservices.model.embed.GetAvailableSendingPointEmbedResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/embed/tenant")
@Api(value = "TenantEmbedService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericTenantEmbedServiceEndpoint implements TenantEmbedService {
	
	@Autowired private TenantEmbedLogic tenantEmbedLogic;

	@Override
	@POST
	@Path("/getTenantSettings")
	public TenantSettingsResponse getTenantSettingsEmbed(TenantSettingsEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantEmbedLogic.getTenantSettingsEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/checkLivenessFaceCompareServiceEmbed")
	public CheckLivenessFaceCompareServiceEmbedResponse checkLivenessFaceCompareServiceEmbed(CheckLivenessFaceCompareServiceEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantEmbedLogic.checkLivenessFaceCompareServiceEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/getAvailableSendingOptionsEmbed")
	public GetAvailableSendingPointEmbedResponse getAvailableSendingPointEmbed (GetAvailableSendingPointEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantEmbedLogic.getAvailableSendingPointEmbed(request, audit);
	}
	
	

}
