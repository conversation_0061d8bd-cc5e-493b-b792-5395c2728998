package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class CommonException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonCommon {
		DATA_ALREADY_EXISTED,
		DATA_NOT_EXISTED,
		CALLER_ID_NOT_HAVE_TENANT,
		CALLER_ID_IS_NOT_ADM_CLIENT,
		CALLER_ID_IS_NOT_TENTANT_OF_LOGIN_ID,
		INVALID_DATE_FORMAT,
		INVALID_DATE_RANGE,
		MANDATORY_PARAM,
		INVALID_VALUE,
		INVALID_CONDITION,
		ALREADY_PROCESSED
	}
	
	private final ReasonCommon reason;
	
	public CommonException(String message, ReasonCommon reason) {
		super(message);
		this.reason = reason;
	}

	@Override
	public int getErrorCode() {
		if (reason != null) {
			switch (reason) {
				case DATA_ALREADY_EXISTED:
					return StatusCode.ERROR_EXIST;
				case DATA_NOT_EXISTED:
					return StatusCode.ERROR_EXIST;
				case CALLER_ID_NOT_HAVE_TENANT:
					return StatusCode.TENANT_NOT_FOUND;
				case CALLER_ID_IS_NOT_ADM_CLIENT: 
					return StatusCode.ROLE_IS_NOT_ADM_CLIENT;
				case CALLER_ID_IS_NOT_TENTANT_OF_LOGIN_ID:
					return StatusCode.CALLER_ID_IS_NOT_TENANT_OF_LOGIN_ID;
				case INVALID_DATE_FORMAT:
					return StatusCode.INVALID_DATE_FORMAT;
				case INVALID_DATE_RANGE:
					return StatusCode.INVALID_DATE_RANGE;
				case MANDATORY_PARAM:
					return StatusCode.MANDATORY_PARAMETER;
				case INVALID_VALUE:
					return StatusCode.INVALID_VALUE;
				case INVALID_CONDITION:
					return StatusCode.INVALID_CONDITION;
				case ALREADY_PROCESSED:
					return StatusCode.ALREADY_PROCESSED;
				default:
					return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}

}
