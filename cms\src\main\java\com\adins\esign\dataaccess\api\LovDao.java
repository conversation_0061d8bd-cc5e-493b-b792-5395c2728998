package com.adins.esign.dataaccess.api;

import java.util.List;
import java.util.Map;

import com.adins.esign.model.MsLov;
import com.adins.esign.model.custom.LovBean;
import com.adins.esign.webservices.model.LovListRequest;

public interface LovDao {
	MsLov getMsLovByCode(String lovCode);
	MsLov getMsLovByGroupAndCode(String lovGroup, String lovCode);
	MsLov getMsLovByGroupAndCodeNewTrx(String lovGroup, String lovCode);
	MsLov getMsLovByIdLov(long idMsLov);

	List<LovBean> getMsLovListByGroup (String lovGroup);
	List<LovBean> getCodeBalanceVendorOfTenantByIdMsTenant (long idMsTenant);
	List<Map<String, Object>> getMsLovListByGroupAndConstraint(LovListRequest request);
	List<Map<String, Object>> getMsLovListByLovGroup(String group);
	void insertLov(MsLov insertLov);
	void updateLov(MsLov updateLov);
	MsLov checkLovExist(MsLov checkLov);
	int getLastSequence(String lovGroup);
	List<String> getListofCodeByLovGroup(String group);
}
