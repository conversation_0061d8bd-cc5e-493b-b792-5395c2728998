package com.adins.esign.businesslogic.api;

import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.webservices.model.SendSmsResponse;
import com.adins.esign.webservices.model.SendSmsValueFirstRequestBean;

public interface SmsOtpLogic {
	SendSmsResponse sendSms(SendSmsValueFirstRequestBean requestBean);
	SendSmsResponse sendSms(SendSmsValueFirstRequestBean requestBean,SigningProcessAuditTrailBean auditTrailBean);
}
