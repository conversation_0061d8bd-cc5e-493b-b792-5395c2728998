package com.adins.esign.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.webservices.model.SendNotificationTestRequest;
import com.adins.esign.webservices.model.SendNotificationTestResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface NotificationLogic {

    @RolesAllowed("ROLE_CHECK_SENDING_NOTIF")
    @PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
    SendNotificationTestResponse sendNotificationTesting(SendNotificationTestRequest request, AuditContext audit);  
} 