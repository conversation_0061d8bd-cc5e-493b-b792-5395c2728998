package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.esign.model.MsRegion;

public interface RegionDao {
	List<MsRegion> getRegionListByTenant(String tenantCode);
	MsRegion getRegionByCodeAndTenant(String regionCode, String tenantCode);
	MsRegion getRegionByCodeAndTenantNewTrx(String regionCode, String tenantCode);
	MsRegion getRegionById(long idRegion);
	void insertRegion(MsRegion region);
	void insertRegionNewTrx(MsRegion region);
}
