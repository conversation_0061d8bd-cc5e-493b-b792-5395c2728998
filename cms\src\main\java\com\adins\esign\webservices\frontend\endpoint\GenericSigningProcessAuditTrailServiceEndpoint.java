package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SigningProcessAuditTrailLogic;
import com.adins.esign.webservices.frontend.api.SigningProcessAuditTrailService;
import com.adins.esign.webservices.model.DownloadAuditTrailExcelRequest;
import com.adins.esign.webservices.model.DownloadAuditTrailExcelResponse;
import com.adins.esign.webservices.model.InquiryAuditTrailSignProcessRequest;
import com.adins.esign.webservices.model.InquiryAuditTrailSignProcessResponse;
import com.adins.esign.webservices.model.SigningProcessAuditTrailRelatedDocumentRequest;
import com.adins.esign.webservices.model.SigningProcessAuditTrailRelatedDocumentResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/signingProcessAuditTrail")
@Api(value = "SigningAuditTrailProcess")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericSigningProcessAuditTrailServiceEndpoint implements SigningProcessAuditTrailService {
	@Autowired SigningProcessAuditTrailLogic signingProcessAuditTrailLogic;
	
	@Override
	@POST
	@Path("/s/getInquiryAuditTrailSignProcess")
	public InquiryAuditTrailSignProcessResponse getInquriyAuditTrailSignProcess (InquiryAuditTrailSignProcessRequest request ) {
		AuditContext audit = request.getAudit().toAuditContext();
		return signingProcessAuditTrailLogic.getInquiryAuditTrail(request, audit);
	}

	@Override
	@POST
	@Path("/s/downloadAuditTrailSignProcess")
	public DownloadAuditTrailExcelResponse downloadAuditTrailSignProcess (DownloadAuditTrailExcelRequest request ) {
		AuditContext audit = request.getAudit().toAuditContext();
		return signingProcessAuditTrailLogic.downloadAuditTrailSignProcess(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/getAuditTrailSignProcessDetail")
	public SigningProcessAuditTrailRelatedDocumentResponse getAuditTrailSignProcessDetail (SigningProcessAuditTrailRelatedDocumentRequest request ) {
		AuditContext audit = request.getAudit().toAuditContext();
		return signingProcessAuditTrailLogic.getSigningProcessAuditTrailRelatedDocument(request, audit);
	}
}
