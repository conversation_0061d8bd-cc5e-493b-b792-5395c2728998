package com.adins.esign.confins.model;

import java.io.Serializable;

import com.adins.framework.service.base.model.MssRequestType;
import com.google.gson.annotations.SerializedName;

@SuppressWarnings("serial")
public class AuthenticateUserRequest extends MssRequestType implements Serializable{
	@SerializedName("Username") private String userId;
	@SerializedName("password") private String password;
	
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
}
