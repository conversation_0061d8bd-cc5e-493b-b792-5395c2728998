package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.interfacing.PaymentReceiptOnPremStampingLogic;
import com.adins.esign.businesslogic.api.interfacing.PaymentReceiptStampingLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class PaymentReceiptConfinsSyncAndDmsUploadJob {
	
	private static final Logger LOG = LoggerFactory.getLogger(PaymentReceiptConfinsSyncAndDmsUploadJob.class);
	
	@Autowired PaymentReceiptOnPremStampingLogic prOnPremStampingLogic;
	
	public void runPaymentReceiptSyncAndDmsUpload() {
		AuditContext audit = new AuditContext("SCHEDULER");
		try {
			LOG.info("Job Payment Receipt Sync Confins Starts");
			prOnPremStampingLogic.syncEmeteraiLogsToConfins(audit);
			LOG.info("Job Payment Receipt Sync Confins Ends");
		} catch (Exception e) {
			LOG.info("Error on Running Confins Sync Use Stamping Job", e);
		}
		
		try {
			LOG.info("Job Payment Receipt Upload DMS Starts");
			prOnPremStampingLogic.uploadPaymentReceiptsToDms(audit);
			LOG.info("Job Payment Receipt Upload DMS Ends");
		} catch (Exception e) {
			LOG.info("Error on Running Upload Payment Receipt to DMS Job", e);
		}
		
		prOnPremStampingLogic.deleteStampingBaseFromOss(audit);
	}
}
