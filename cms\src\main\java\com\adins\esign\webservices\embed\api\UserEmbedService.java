package com.adins.esign.webservices.embed.api;

import java.io.UnsupportedEncodingException;

import com.adins.esign.webservices.model.GenerateInvitationLinkForExpiredCertResponse;
import com.adins.esign.webservices.model.embed.GenerateInvitationLinkForExpiredCertEmbedRequest;
import com.adins.esign.webservices.model.embed.GetSignerDetailEmbedRequest;
import com.adins.esign.webservices.model.embed.GetSignerDetailEmbedResponse;
import com.adins.esign.webservices.model.embed.SentOtpSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.SentOtpSigningEmbedResponse;
import com.adins.esign.webservices.model.embed.SignerDataVerificationEmbedRequest;
import com.adins.esign.webservices.model.embed.SignerDataVerificationEmbedResponse;
import com.adins.esign.webservices.model.embed.VerifyLivenessFaceCompareEmbedRequest;
import com.adins.esign.webservices.model.embed.VerifyLivenessFaceCompareEmbedResponse;
import com.adins.esign.webservices.model.embed.VerifyOtpSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.VerifyOtpSigningEmbedResponse;

public interface UserEmbedService {
	SignerDataVerificationEmbedResponse signerDataVerificationEmbed(SignerDataVerificationEmbedRequest request);
	GetSignerDetailEmbedResponse getSignerDetailEmbed(GetSignerDetailEmbedRequest request);
	VerifyLivenessFaceCompareEmbedResponse verifyLivenessFaceCompareEmbed(VerifyLivenessFaceCompareEmbedRequest request);
	SentOtpSigningEmbedResponse sentOtpSigningEmbed(SentOtpSigningEmbedRequest request);
	VerifyOtpSigningEmbedResponse verifyOtpSigningEmbed(VerifyOtpSigningEmbedRequest request);
	GenerateInvitationLinkForExpiredCertResponse generateInvLinkECertExpired(GenerateInvitationLinkForExpiredCertEmbedRequest request) throws UnsupportedEncodingException ;
}
