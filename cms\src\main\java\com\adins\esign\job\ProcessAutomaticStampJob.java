package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class ProcessAutomaticStampJob {

	private static final Logger LOG = LoggerFactory.getLogger(ProcessAutomaticStampJob.class);
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void runProcessAutomaticStamp() {
		LOG.info("Job process automatic stamp started");
		try {
			AuditContext audit = new AuditContext("AUTO_STAMP_JOB");
			schedulerLogic.processAutomaticStamp(audit);
		} catch (Exception e) {
			LOG.error("Job process automatic stamp error: {}", e.getLocalizedMessage(), e);
		}
		LOG.info("Job process automatic stamp finished");
	}
}
