package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.UserManagementLogic;
import com.adins.esign.constants.MediaType;
import com.adins.esign.webservices.frontend.api.UserManagementService;
import com.adins.esign.webservices.model.GetDataUserManagementViewRequest;
import com.adins.esign.webservices.model.GetDataUserManagementViewResponse;
import com.adins.esign.webservices.model.GetListDataPenggunaRequest;
import com.adins.esign.webservices.model.GetListDataPenggunaResponse;
import com.adins.esign.webservices.model.GetListUserManagementRequest;
import com.adins.esign.webservices.model.GetListUserManagementResponse;
import com.adins.esign.webservices.model.InsertUserManagementResponse;
import com.adins.esign.webservices.model.InsestUserManagementRequest;
import com.adins.esign.webservices.model.UpdateUserManagementRequest;
import com.adins.esign.webservices.model.UpdateUserManagementResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/user-management")
@Api(value = "UserManagementService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericUserManagementServiceEndpoint implements UserManagementService{

	@Autowired UserManagementLogic userManagementLogic;
	
	@Override
	@POST
	@Path("/s/getListUserManagement")
	public GetListUserManagementResponse getListUserManagement(GetListUserManagementRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userManagementLogic.getListUserManagement(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/insertUserManagement")
	public InsertUserManagementResponse insertUserManagement(InsestUserManagementRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userManagementLogic.insertUserManagement(request, audit);
	}

	
	@Override
	@POST
	@Path("/s/getListDataPengguna")
	public GetListDataPenggunaResponse getListDataPengguna(GetListDataPenggunaRequest request) {
			AuditContext audit = request.getAudit().toAuditContext();
			return userManagementLogic.getListDataPengguna(request, audit);
	}


	@Override
	@POST
	@Path("/s/getDataUserManagementView")
	public GetDataUserManagementViewResponse getDataUserManagementView(GetDataUserManagementViewRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userManagementLogic.getDataUserManagementView(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/updateUserManagement")
	public UpdateUserManagementResponse updateUserManagement(UpdateUserManagementRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return userManagementLogic.updateUserManagement(request, audit);
	}
}
