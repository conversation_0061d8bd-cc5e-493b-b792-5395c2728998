package com.adins.esign.model.custom.halosis;

import java.util.List;

public class HalosisWhatsAppTemplate {
	private String name;
	private HalosisWhatsAppTemplateLanguage language;
	private List<HalosisWhatsAppTemplateComponent> components;
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public HalosisWhatsAppTemplateLanguage getLanguage() {
		return language;
	}
	public void setLanguage(HalosisWhatsAppTemplateLanguage language) {
		this.language = language;
	}
	public List<HalosisWhatsAppTemplateComponent> getComponents() {
		return components;
	}
	public void setComponents(List<HalosisWhatsAppTemplateComponent> components) {
		this.components = components;
	}
	
}
