package com.adins.esign.webservices.frontend.endpoint;

import java.text.ParseException;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.StampDutyLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.webservices.frontend.api.MateraiService;
import com.adins.esign.webservices.model.CreateMateraiRequest;
import com.adins.esign.webservices.model.CreateMateraiResponse;
import com.adins.esign.webservices.model.TaxExcelReportRequest;
import com.adins.esign.webservices.model.TaxExcelReportResponse;
import com.adins.esign.webservices.model.InquiryStampDutyDetailRequest;
import com.adins.esign.webservices.model.InquiryStampDutyDetailResponse;
import com.adins.esign.webservices.model.InquiryStampDutyRequest;
import com.adins.esign.webservices.model.InquiryStampDutyResponse;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiEmbedRequest;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiRequest;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiResponse;
import com.adins.esign.webservices.model.ReverseTopupRequest;
import com.adins.esign.webservices.model.ReverseTopupResponse;
import com.adins.esign.webservices.model.StampDutyExcelReportRequest;
import com.adins.esign.webservices.model.StampDutyExcelReportResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

import io.swagger.annotations.Api;

@Component
@Path("/stampduty")
@Api(value = "MateraiService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericMateraiServiceEndpoint implements MateraiService{
	@Autowired private StampDutyLogic stampDutyLogic;
	
	@Override @Deprecated
	@POST @Path("/s/topup")
	public CreateMateraiResponse createMaterai(CreateMateraiRequest request) {
		CreateMateraiResponse response = new CreateMateraiResponse();
		Status status = new Status();
		status.setCode(200);
		status.setMessage(GlobalVal.CONST_ENDPOINT_IS_DEPRECATED);
		response.setStatus(status);
		return response;
	}

	@Override
	@POST
	@Path("/s/list")
	public InquiryStampDutyResponse getListStampDuty(InquiryStampDutyRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		return stampDutyLogic.getListStampDuty(request, audit);
	}

	@Override
	@POST
	@Path("/s/detail")
	public InquiryStampDutyDetailResponse getStampDutyDetail(InquiryStampDutyDetailRequest request) {
		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		return stampDutyLogic.getStampDutyDetail(request, audit);
	}

	@Override @Deprecated
	@POST @Path("/s/getListReverseTopup")
	public InquiryStampDutyResponse getListReverseTopup(InquiryStampDutyRequest request) {
//		AuditContext audit = new AuditContext(request.getAudit().getCallerId());
		InquiryStampDutyResponse response = new InquiryStampDutyResponse();
//		try {
//			response = stampDutyLogic.getListReverseTopup(request, audit);
//		} catch (ParseException e) {
			Status status = new Status();
			status.setCode(200);
			status.setMessage(GlobalVal.CONST_ENDPOINT_IS_DEPRECATED);
			response.setStatus(status);
//		}
		
		return response;
	}

	@Override @Deprecated
	@POST @Path("/s/reverseTopup")
	public ReverseTopupResponse reverseTopupMaterai(ReverseTopupRequest request) {
		ReverseTopupResponse response = new ReverseTopupResponse();
		Status status = new Status();
		status.setCode(200);
		status.setMessage(GlobalVal.CONST_ENDPOINT_IS_DEPRECATED);
		response.setStatus(status);
		return response;
	}

	@Override
	@POST
	@Path("/s/downloadStampDutyReport")
	public StampDutyExcelReportResponse exportStampDutyReport(StampDutyExcelReportRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return stampDutyLogic.exportStampDutyReport(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/listMonitoring")
	public ListMonitoringEmeteraiResponse getListMonitoringEmeterai(ListMonitoringEmeteraiRequest request) throws ParseException{
		AuditContext audit = request.getAudit().toAuditContext();
		return stampDutyLogic.getListMonitoringEmeterai(request, audit);
	}
	
	@Override
	@POST
	@Path("/listMonitoringEmbed")
	public ListMonitoringEmeteraiResponse getListMonitoringEmeteraiEmbed(ListMonitoringEmeteraiEmbedRequest request) throws ParseException{
		AuditContext audit = request.getAudit().toAuditContext();
		return stampDutyLogic.getListMonitoringEmeteraiEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/excelTaxReport")
	public TaxExcelReportResponse excelTaxReport(TaxExcelReportRequest request) throws ParseException{
		AuditContext audit = request.getAudit().toAuditContext();
		return stampDutyLogic.exportTaxReport(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/excelTaxReportNormal")
	public TaxExcelReportResponse excelTaxReportNormal(TaxExcelReportRequest request) throws ParseException{
		AuditContext audit = request.getAudit().toAuditContext();
		return stampDutyLogic.exportTaxReportNormal(request, audit);
	}
}
