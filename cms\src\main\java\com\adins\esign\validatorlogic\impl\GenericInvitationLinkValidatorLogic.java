package com.adins.esign.validatorlogic.impl;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.validatorlogic.api.InvitationLinkValidatorLogic;
import com.adins.exceptions.InvitationLinkException;
import com.adins.exceptions.InvitationLinkException.ReasonInvitationLink;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericInvitationLinkValidatorLogic extends BaseLogic implements InvitationLinkValidatorLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericInvitationLinkValidatorLogic.class);
	
	@Autowired private CommonLogic commonLogic;
	
	private String getMessages(String code, Object[] params, AuditContext audit) {
		return messageSource.getMessage(code, params, retrieveLocaleAudit(audit));
	}
	
	@Override
	public TrInvitationLink validateGetInvitationLink(String invitationCode, AuditContext audit) {
		TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByInvitationCode(invitationCode);
		if (null == invLink) {
			throw new InvitationLinkException(getMessages(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID, null, audit),
					ReasonInvitationLink.INV_LINK_NOT_EXIST);
		}
		if (!"1".equals(invLink.getIsActive())) {
			throw new InvitationLinkException(getMessages(GlobalKey.MESSAGE_ERROR_INV_LINK_INACTIVE_LINK, null, audit),
					ReasonInvitationLink.INACTIVE_LINK);
		}
		return invLink;
	}

	@Override
	public TrInvitationLink validateGetInvitationLinkNewTran(String invitationCode, AuditContext audit) {
		TrInvitationLink invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByInvitationCodeNewTran(invitationCode);
		if (null == invLink) {
			throw new InvitationLinkException(getMessages(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID, null, audit),
					ReasonInvitationLink.INV_LINK_NOT_EXIST);
		}
		if (!"1".equals(invLink.getIsActive())) {
			throw new InvitationLinkException(getMessages(GlobalKey.MESSAGE_ERROR_INV_LINK_INACTIVE_LINK, null, audit),
					ReasonInvitationLink.INACTIVE_LINK);
		}
		return invLink;
	}
	
	@Override
	public boolean validateAutofillInvitationRegisteredUser(TrInvitationLink invitationLink) {
		if (!StringUtils.isBlank(invitationLink.getPlaceOfBirth()) || invitationLink.getDateOfBirth() != null
			|| !StringUtils.isBlank(invitationLink.getGender()) || !StringUtils.isBlank(invitationLink.getAddress())
			|| !StringUtils.isBlank(invitationLink.getProvinsi()) || !StringUtils.isBlank(invitationLink.getKota())
			|| !StringUtils.isBlank(invitationLink.getKecamatan()) || !StringUtils.isBlank(invitationLink.getKelurahan())
			|| !StringUtils.isBlank(invitationLink.getZipCode())) {
			return false;
		}
		return true;
	}

	@Override
	public String decryptInvitationCode(String encryptedCode, AuditContext audit) {
		try {
			return commonLogic.decryptMessageToString(encryptedCode, audit);
		} catch (Exception e) {
			LOG.error("Error when decrypting invitation code: {}", encryptedCode, e);
			throw new InvitationLinkException(getMessages("businesslogic.invitationlink.decrypterror", null, audit),
					ReasonInvitationLink.DECRYPT_CODE_ERROR);
		}
	}

	@Override
	public void validateInvitationExpiredDate(TrInvitationLink invLink, AuditContext audit) {
		MsTenant tenant = invLink.getMsTenant();
		if (null == tenant.getInvitationLinkActiveDuration() || 0 == tenant.getInvitationLinkActiveDuration()) {
			return;
		}
		
		Date invitationTime = (null == invLink.getDtmUpd()) ? invLink.getDtmCrt() : invLink.getDtmUpd();
		Date currentTime = new Date();
		long differenceInMillis = currentTime.getTime() - invitationTime.getTime();
		long differenceInMinutes = TimeUnit.MILLISECONDS.toMinutes(differenceInMillis);
		boolean isExpired = differenceInMinutes >= tenant.getInvitationLinkActiveDuration();
		
		if (isExpired) {
			invLink.setIsActive("0");
			invLink.setUsrUpd(audit.getCallerId());
			invLink.setDtmUpd(new Date());
			daoFactory.getInvitationLinkDao().updateInvitationLinkNewTran(invLink);
			
			throw new InvitationLinkException(getMessage("businesslogic.invitationlink.invitationexpired", null, audit), ReasonInvitationLink.INVITATION_EXPIRED);
		}
	}

}
