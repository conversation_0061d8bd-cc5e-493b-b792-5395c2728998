package com.adins.esign.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import com.adins.esign.webservices.model.DocumentConfinsRequestBean;
import com.adins.esign.webservices.model.DocumentConfinsResponse;
import com.adins.esign.webservices.model.SendDocFullApiRequest;
import com.adins.esign.webservices.model.SendDocFullApiResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface SendDocumentLogic {
	
	@RolesAllowed("ROLE_CORESYSTEM")
	DocumentConfinsResponse sendDocument(String tenantCode, String vendorCode, DocumentConfinsRequestBean[] documentConfinsRequest, String apiKey, AuditContext audit) throws Exception;
	SendDocFullApiResponse sendDocFullApi(SendDocFullApiRequest request, String apiKey, AuditContext audit) throws Exception;
}
