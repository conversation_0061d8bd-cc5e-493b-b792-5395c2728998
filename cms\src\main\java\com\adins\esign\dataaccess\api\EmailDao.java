package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsEmailHosting;
import com.adins.esign.model.MsEmailPattern;

public interface EmailDao {
	// MsEmailHosting
	MsEmailHosting getEmailHostingById(Long id);
	List<MsEmailHosting> getListActiveEmailHosting();
	
	// MsEmailPattern
	MsEmailPattern getEmailPattern(String subjectEmail, String sender, String[] actions);
	MsEmailPattern getEmailPatternWithoutSender(String subjectEmail, String[] actions);
	
	List<AmMsuser> getAllUserActiveEmailByHostingDomain(MsEmailHosting emailHosting);
}
