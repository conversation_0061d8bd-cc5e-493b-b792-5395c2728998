package com.adins.am.businesslogic.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.businesslogic.api.GeneralSettingLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.custom.GenSetBean;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.webservices.model.GetGeneralSettingRequest;
import com.adins.esign.webservices.model.GetGeneralSettingResponse;
import com.adins.exceptions.StatusCode;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;

@Component
public class GenericGeneralSettingLogic extends BaseLogic implements GeneralSettingLogic {
	private AuditInfo auditInfo;

	@Autowired CommonValidatorLogic commonValidatorLogic;
	
	public GenericGeneralSettingLogic() {
		String[] pkCols = {"uuidGeneralSetting"};
		String[] pkDbCols = {"UUID_GENERAL_SETTING"};
		String[] cols = {"uuidGeneralSetting", "isActive", AmGeneralsetting.GS_CODE_HBM, "gsPrompt", "gsType", "gsValue","addonInvLink", "addonSignLink"};
		String[] dbCols = {"UUID_GENERAL_SETTING", "IS_ACTIVE", "GS_CODE", "GS_PROMPT", "GS_TYPE", "GS_VALUE", "ADDON_INV_LINK", "ADDON_SIGN_LINK"};
		this.auditInfo = new AuditInfo("AM_GENERALSETTING", pkCols, pkDbCols, cols, dbCols);
	}

	@Transactional(readOnly=true)
	@Override
	public Map<String, Object> listGeneralSetting(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId) {
		Object[][] tmpParam = (Object[][])params;
		Object[][] parameters = new Object[3][2];
		parameters[0] = tmpParam[0];
		parameters[1] = tmpParam[1];
		parameters[2] = new Object[]{Restrictions.not(Restrictions.like(AmGeneralsetting.GS_CODE_HBM, GlobalVal.SUBSYSTEM_ESIGN+"%"))};

//		return this.managerDAO.selectAll(AmGeneralsetting.class, parameters, orders, pageNumber, pageSize);
		return null;
	}

	@Transactional(readOnly=true)
	@Override
	public AmGeneralsetting getGeneralSetting(long uuid, AuditContext callerId) {
//		return this.managerDAO.selectOne(AmGeneralsetting.class, uuid);
		return null;

	}

	@Transactional
	@Override
	public void updateGeneralSetting(AmGeneralsetting obj, AuditContext callerId, long uuid) {			
		AmGeneralsetting dbModel = daoFactory.getGeneralSettingDao().getGsObjById(uuid);
		dbModel.setIsActive(obj.getIsActive());
		dbModel.setGsCode(obj.getGsCode());
		dbModel.setGsPrompt(obj.getGsPrompt());
		dbModel.setGsValue(obj.getGsValue());
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId());
		
		this.auditManager.auditEdit(dbModel, auditInfo, callerId.getCallerId(), "");
		daoFactory.getGeneralSettingDao().updateGs(dbModel);
	}

	@Override
	public AmGeneralsetting getGeneralSetting(String code, AuditContext callerId) {
		Map<String, Object> params = new HashMap<>(1);
		params.put("code", code);
//		return this.managerDAO.selectOne("from AmGeneralsetting where gsCode = :code", params);
		return null;
		
	}

	@Override
	public GetGeneralSettingResponse getGeneralSetting(GetGeneralSettingRequest request, AuditContext audit) {
		GetGeneralSettingResponse response = new GetGeneralSettingResponse();

		String messageValidation = "";
		
		commonValidatorLogic.validateNotNull(request.getGsCode(), "Gs Code", audit);

		AmGeneralsetting generalsetting = daoFactory.getGeneralSettingDao().getGsObjByCode(request.getGsCode());

		messageValidation = getMessage(GlobalKey.GENERAL_SETTING_NOT_FOUND, null, audit);
		commonValidatorLogic.validateNotNull(generalsetting, messageValidation, StatusCode.GENERAL_SETTING_NOT_FOUND);

		
		GenSetBean bean = new GenSetBean();

		bean.setDescription(generalsetting.getGsPrompt());
		bean.setType(generalsetting.getGsType());
		bean.setValue(generalsetting.getGsValue());

		response.setGeneralSettingData(bean);
		return response;


	}
	
}
