package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrJobCheckRegisterStatus;

public interface JobCheckRegisterStatusDao {
	List<TrJobCheckRegisterStatus> getUnprocessedJobCheckRegStatus();
	List<TrJobCheckRegisterStatus> getUnprocessedJobCheckRegStatusNewTrx();
	TrJobCheckRegisterStatus getLatestJobCheckRegStatusByIdNo(String idNo, MsVendor vendor);
	
	void updateJobCheckRegStatusNewTrx(TrJobCheckRegisterStatus jobCheckRegisStatus);
	void insertJobCheckRegStatusNewTrx(TrJobCheckRegisterStatus jobCheckRegisStatus);
}
