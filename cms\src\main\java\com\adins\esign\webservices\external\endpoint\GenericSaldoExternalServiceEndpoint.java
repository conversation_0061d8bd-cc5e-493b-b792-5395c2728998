package com.adins.esign.webservices.external.endpoint;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.webservices.external.api.SaldoExternalService;
import com.adins.esign.webservices.model.GetListBalanceMutationExternalRequest;
import com.adins.esign.webservices.model.GetListBalanceMutationExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/external/saldo")
@Api(value = "SaldoExternalService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericSaldoExternalServiceEndpoint implements SaldoExternalService {
	@Autowired private SaldoLogic saldoLogic;
	

	@POST
	@Path("/getbalancemutation")
	public GetListBalanceMutationExternalResponse getBalanceMutation(GetListBalanceMutationExternalRequest request) throws Exception {
		AuditContext audit = request.getAudit().toAuditContext();
		
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		
		return saldoLogic.getListBalanceMutationExternal(request, apiKey, audit);
	}
}
