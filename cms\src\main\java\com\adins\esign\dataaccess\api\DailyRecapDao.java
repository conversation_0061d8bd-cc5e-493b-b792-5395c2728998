package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceDailyRecap;

public interface DailyRecapDao {
	void insertTrBalanceDailyRecap(TrBalanceDailyRecap dailyRecap);

	TrBalanceDailyRecap getDailyRecap(String dateRecap, MsLov balanceType, MsTenant tenant, MsVendor vendor);

	int countQtyDailyRecap(String dateRecap, MsLov balanceType, MsTenant tenant, MsVendor vendor);
}
