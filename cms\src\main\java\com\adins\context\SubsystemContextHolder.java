package com.adins.context;

import org.slf4j.MDC;

public class SubsystemContextHolder {
	SubsystemContextHolder() {

	}

	private static final ThreadLocal<String> schemaHolder = new ThreadLocal<>();
	private static final String KEY_MDC_SUBSYSTEMCONTEXT = "subsystemContext";

	public static void setSchema(String schema) {
		MDC.put(KEY_MDC_SUBSYSTEMCONTEXT, schema);
		schemaHolder.set(schema);
	}

	public static String getSchema() {
		return schemaHolder.get();
	}

	public static void clearSchema() {
		MDC.remove(KEY_MDC_SUBSYSTEMCONTEXT);
		schemaHolder.remove();
	}
}
