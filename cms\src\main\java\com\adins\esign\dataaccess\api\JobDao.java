package com.adins.esign.dataaccess.api;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.esign.model.MsJob;
import com.adins.esign.model.TrJobResult;
import com.adins.esign.model.custom.ListJobResultBean;

public interface JobDao {
	// ms_job
	MsJob getMsJob(String msJobCode);
	List<Map<String, Object>> getListJobByJobType(String jobType);
	
	// tr_job_result
	void insertTrJobResult(TrJobResult newJobResult);
	void updateJobResult(TrJobResult jobResult);
	
	TrJobResult checkJobResult(String requestParams, String tenantCode, String msJobCode);
	TrJobResult getNewJobResultByTenantUserAndIdJobResult(String tenantCode, long idUser, Long idJobResult);
	TrJobResult getJobResultById(Long idJobResult);
	
	List<ListJobResultBean> getListJobRekonResult(int min, int max, Long idJob, String requestBy, Short processStatus, Date requestDateStart, Date requestDateEnd);
	int getCountListJobRekonResult(Long idJob, String requestBy, Short processStatus, Date requestDateStart, Date requestDateEnd);
	
}
