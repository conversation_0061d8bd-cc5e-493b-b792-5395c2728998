package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class ErrorHistoryException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonErrorHistory {
		ERR_HIST_NOT_FOUND
	}
	
	private final ReasonErrorHistory reason;
	
	public ErrorHistoryException(ReasonErrorHistory reason) {
		this.reason = reason;
	}
	
	public ErrorHistoryException(String message, ReasonErrorHistory reason) {
		super(message);
		this.reason = reason;
	}
	
	public ErrorHistoryException(Throwable ex, ReasonErrorHistory reason) {
		super(ex);
		this.reason = reason;
	}
	
	public ErrorHistoryException(String message, Throwable ex, ReasonErrorHistory reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	public ReasonErrorHistory getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (null != this.reason) {
			switch (reason) {
				case ERR_HIST_NOT_FOUND:
					return StatusCode.ERR_HIST_NOT_FOUND;
				default:
					return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}
	
}
