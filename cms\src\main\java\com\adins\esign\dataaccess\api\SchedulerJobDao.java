package com.adins.esign.dataaccess.api;

import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrSchedulerJob;

public interface SchedulerJobDao {
	void insertSchedulerJob(TrSchedulerJob schedulerJob);
	void insertSchedulerJobNewTrx(TrSchedulerJob schedulerJob);
	
	void updateSchedulerJob(TrSchedulerJob schedulerJob);
	void updateSchedulerJobNewTrx(TrSchedulerJob schedulerJob);

	TrSchedulerJob getSchedulerJob(String dateRecap, String jobType, String balanceType, MsTenant tenant);
	TrSchedulerJob getSchedulerJobNewTrx(String dateRecap, String jobType, String balanceType);
	TrSchedulerJob getSchedulerJobNewTrx(MsTenant tenant, String dateRecap, String jobType, String balanceType);
	TrSchedulerJob getSchedulerJobByTenant(String dateRecap, String jobType, String balanceType,String tenantCode );
}
