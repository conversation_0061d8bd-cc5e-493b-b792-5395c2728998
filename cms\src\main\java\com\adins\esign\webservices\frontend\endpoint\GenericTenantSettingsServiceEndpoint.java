package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.webservices.frontend.api.TenantSettingsService;
import com.adins.esign.webservices.model.ListTenantSettingsRequest;
import com.adins.esign.webservices.model.ListTenantSettingsResponse;
import com.adins.esign.webservices.model.SaveTenantSettingsRequest;
import com.adins.esign.webservices.model.SaveTenantSettingsResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/tenantSettings")
@Api(value = "TenantSettingsService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericTenantSettingsServiceEndpoint implements TenantSettingsService {
	
	@Autowired TenantSettingsLogic tenantSettingsLogic;

	@Override
	@POST
	@Path("/s/list")
	public ListTenantSettingsResponse getListTenantSettings(ListTenantSettingsRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantSettingsLogic.getListSettingValue(request, audit);
	}

	@Override
	@POST
	@Path("/s/saveTenantSettings")
	public SaveTenantSettingsResponse saveTenantSettings (SaveTenantSettingsRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantSettingsLogic.saveTenantSettings(request, audit);
	}

}
