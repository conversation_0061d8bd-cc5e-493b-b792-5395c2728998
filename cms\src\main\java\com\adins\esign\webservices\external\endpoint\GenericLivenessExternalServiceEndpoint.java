package com.adins.esign.webservices.external.endpoint;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.interfacing.LivenessLogic;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.webservices.external.api.LivenessExternalService;
import com.adins.esign.webservices.model.LivenessUrlResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssRequestType;

import io.swagger.annotations.Api;

@Component
@Path("/external/liveness")
@Api(value = "UserExternalService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericLivenessExternalServiceEndpoint implements LivenessExternalService {
	
	@Autowired private LivenessLogic livenessLogic;

	@Override
	@POST
	@Path("/getLivenessUrl")
	public LivenessUrlResponse getLivenessUrl(MssRequestType request) {
		
		AuditContext audit = request.getAudit().toAuditContext();
		
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		
		return livenessLogic.getLivenessUrl(xApiKey, audit);
	}

}
