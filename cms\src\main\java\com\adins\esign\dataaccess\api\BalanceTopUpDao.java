package com.adins.esign.dataaccess.api;

import java.math.BigInteger;
import java.util.List;

import com.adins.esign.model.TrBalanceTopUp;
import com.adins.esign.model.custom.ListTopupBalanceBean;
import com.adins.esign.webservices.model.GetListTopupBalanceRequest;

public interface BalanceTopUpDao {
	void insertTrBalanceTopUp(TrBalanceTopUp balanceTopUp);
	
	BigInteger countListTopupBalance(GetListTopupBalanceRequest request);
	
	List<ListTopupBalanceBean> getListTopupBalanceByTenantAndVendor(GetListTopupBalanceRequest request, int min, int max);
	
	TrBalanceTopUp getBalanceTopupByIdBalanceMutation(long idBalanceMutation);
	void updateTrBalanceTopUp(TrBalanceTopUp balanceTopUp);
}
