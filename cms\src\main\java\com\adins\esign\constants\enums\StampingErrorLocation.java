package com.adins.esign.constants.enums;

public enum StampingErrorLocation {
	LOGIN("Login Peruri"),
	UPL_DOC("Upload Base Document"),
	GEN_SDT("Generate Emeterai"),
	STM_SDT("Stamp Emeterai"),
	UPL_OSS("Upload to OSS"),
	UPL_DMS("Upload to DMS"),
	FINAL_VAL("Final Validation");
	
	private final String description;
	
	private StampingErrorLocation(String description) {
		this.description = description;
	}
	
	@Override
	public String toString() {
		return this.description;
	}
}
