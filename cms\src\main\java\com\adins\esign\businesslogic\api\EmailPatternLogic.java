package com.adins.esign.businesslogic.api;

import com.adins.am.model.AmMsuser;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsEmailPattern;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface EmailPatternLogic {
	String shortenParsedEmailContent(MsEmailPattern emailPattern, AmMsuser user, NotificationType notifType, String parsedContent, AuditContext audit);
}
