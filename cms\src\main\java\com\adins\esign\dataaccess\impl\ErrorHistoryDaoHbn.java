package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.ErrorHistoryDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrErrorHistory;
import com.adins.esign.model.TrErrorHistoryUserDetail;
import com.adins.esign.model.custom.ErrorHistoryBean;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class ErrorHistoryDaoHbn extends BaseDaoHbn implements ErrorHistoryDao {
	
	public String constructParamErrorHistory(Map<String, Object> params, String modul, String refNumber,
			String namaKonsumen, String cabang, String region, String businessLine, Date tanggalDari,
			Date tanggalSampai, String tipe) {
		
	   StringBuilder query = new StringBuilder();
	   
	   if (StringUtils.isNotBlank(modul)) {
			query.append("AND lov.lov_group = :lovGroup AND lov.code = :modul ");
			params.put("lovGroup", GlobalVal.LOV_GROUP_ERR_HIST_MODULE);
			params.put("modul", StringUtils.upperCase(modul));
		}
	   
	   if (StringUtils.isNotBlank(refNumber)) {
			query.append(" AND eh.ref_number LIKE :refNumber ");
			params.put("refNumber", "%" + StringUtils.upperCase(refNumber) + "%");
		}

	   if (StringUtils.isNotBlank(namaKonsumen)) {
			query.append(" AND eh.cust_name LIKE :namaKonsumen ");
			params.put("namaKonsumen", "%" + StringUtils.upperCase(namaKonsumen) + "%");
		}
	   
	   if (StringUtils.isNotBlank(cabang)) {
			query.append(" AND eh.office LIKE :cabang ");
			params.put("cabang", "%" + StringUtils.upperCase(cabang) + "%");
		}
	   
	   if (StringUtils.isNotBlank(region)) {
			query.append(" AND eh.region LIKE :region ");
			params.put("region", "%" + StringUtils.upperCase(region) + "%");
		}
	   
	   if (StringUtils.isNotBlank(businessLine)) {
			query.append(" AND eh.business_line LIKE :businessLine ");
			params.put("businessLine", "%" + StringUtils.upperCase(businessLine) + "%");
		}
	   
	   if (StringUtils.isNotBlank(tipe)) {
			query.append(" AND eh.error_type = :tipe ");
			params.put("tipe", StringUtils.upperCase(tipe) );
		}
	   
	   if(tanggalDari != null && tanggalSampai != null) {			
			query.append("AND eh.error_date >= :tanggalDari AND eh.error_date <= :tanggalSampai ");
			params.put("tanggalDari", tanggalDari);
			params.put("tanggalSampai", tanggalSampai);
		} else {
			 	 query.append("AND eh.error_date >= date_trunc('MONTH', now()) and eh.error_date <= now() ");
			 
		}

	   return query.toString();
	}

	private String constructParamTenant(Map<String, Object> params, String tenantCode) {
		StringBuilder query = new StringBuilder();
		if (StringUtils.isNotBlank(tenantCode)) {
			query.append(" AND tenant_code = :tenantCode ");
			params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		}
		
		return query.toString();
	}
	
	@Override
	public List<ErrorHistoryBean> getListErrorHistory(int min, int max, String tenantCode,String modul, String refNumber,
			String namaKonsumen, String cabang, String region, String businessLine, Date tanggalDari,
			Date tanggalSampai, String tipe) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		
		String paramQueryTenant = this.constructParamTenant(params, tenantCode);		
		String paramQueryErrorHistory = this.constructParamErrorHistory(params, modul, refNumber, 
				namaKonsumen, cabang, region, businessLine, tanggalDari, tanggalSampai, tipe);
		
		StringBuilder query = new StringBuilder();
		query.append(" with ilLink AS ")
		.append(" ( ")
		.append(" SELECT eh.id_error_history AS \"idErrorHistory\", lov.description AS \"modul\", ")
			.append(" eh.ref_number AS \"refNumber\", ")
			.append(" eh.cust_name AS \"namaKonsumen\", ")
			.append(" eh.office AS \"cabang\", ")
		    .append(" eh.region AS \"region\", ")
			.append(" eh.business_line AS \"businessLine\", ")
		    .append(" eh.error_type AS \"tipe\", ")
		    .append(" TO_CHAR(eh.error_date, 'YYYY-MM-DD HH24:MI:SS') AS \"tanggal\", ")
		    .append(" eh.error_message AS \"errorMessage\", ")
		    .append(" vendor.vendor_code AS \"vendorCode\", ")
		    .append(" row_number() over (order by eh.id_error_history) AS \"rowNum\" ")
		.append(" FROM tr_error_history eh ")
		.append(" JOIN ms_tenant tent ON tent.id_ms_tenant = eh.id_ms_tenant ")
		.append(" JOIN ms_lov lov ON lov.id_lov = eh.lov_modul ")
		.append(" LEFT JOIN ms_vendor vendor ON vendor.id_ms_vendor = eh.id_ms_vendor ")
		.append(" WHERE 1 = 1 ")
		.append(paramQueryTenant)
		.append(paramQueryErrorHistory)
		.append(" order by eh.error_date ")
		.append(" ) ")

		.append(" SELECT \"idErrorHistory\", \"modul\", \"refNumber\", \"namaKonsumen\", \"cabang\", \"region\", \"businessLine\",\"tipe\", ")
		.append(" \"tanggal\", \"errorMessage\", \"vendorCode\" ")
		.append(" FROM ilLink ")
		.append(" WHERE \"rowNum\" between :min and :max ");

		return this.managerDAO.selectForListString(ErrorHistoryBean.class, query.toString(), params, null);
	}
	
	@Override
	public Integer countListErrorHistory(String tenantCode, String modul, String refNumber, String namaKonsumen,
			String cabang, String region, String businessLine, Date tanggalDari, Date tanggalSampai, String tipe) {
		
		Map<String, Object> params = new HashMap<>();
		
		String paramQueryTenant = this.constructParamTenant(params, tenantCode);		
		String paramQueryErrorHistory = this.constructParamErrorHistory(params, modul, refNumber, 
				namaKonsumen, cabang, region, businessLine, tanggalDari, tanggalSampai, tipe);
		
		StringBuilder query = new StringBuilder();
		query.append(" with ilLink AS ")
		.append(" ( ")
		.append(" SELECT lov.description AS \"modul\", ")
			.append(" eh.ref_number AS \"refNumber\", ")
			.append(" eh.cust_name AS \"namaKonsumen\", ")
			.append(" eh.office AS \"cabang\", ")
		    .append(" eh.region AS \"region\", ")
			.append(" eh.business_line AS \"businessLine\", ")
		    .append(" eh.error_type AS \"tipe\", ")
		    .append(" TO_CHAR(eh.error_date, 'YYYY-MM-DD HH24:MI:SS') AS \"tanggal\", ")
		    .append(" eh.error_message AS \"errorMessage\", ")
		    .append(" vendor.vendor_code AS \"vendorCode\", ")
		    .append(" row_number() over (order by eh.id_error_history) AS \"rowNum\" ")
		.append(" FROM tr_error_history eh ")
		.append(" JOIN ms_tenant tent ON tent.id_ms_tenant = eh.id_ms_tenant ")
		.append(" JOIN ms_lov lov ON lov.id_lov = eh.lov_modul ")
		.append(" LEFT JOIN ms_vendor vendor ON vendor.id_ms_vendor = eh.id_ms_vendor ")
		.append(" WHERE 1 = 1 ")
		.append(paramQueryTenant)
		.append(paramQueryErrorHistory)
		.append(" order by eh.error_date ")
		.append(" ) ")

		.append(" SELECT COUNT(\"tipe\") ")
		.append(" FROM ilLink ");
		
        BigInteger totalData = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		
		return totalData.intValue();
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertErrorHistory(TrErrorHistory errorHistory) {
		errorHistory.setUsrCrt(MssTool.maskData(errorHistory.getUsrCrt()));
		this.managerDAO.insert(errorHistory);
	}

	@Override
	public void updateErrorHistory(TrErrorHistory errorHistory) {
		errorHistory.setUsrUpd(MssTool.maskData(errorHistory.getUsrUpd()));
		this.managerDAO.update(errorHistory);
	}

	@Override
	public TrErrorHistory getLatestErrorHistoryByNik(String nik) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("nik", nik);
		params.put(MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_ERR_HIST_MODULE);
		params.put(MsLov.CODE_HBM, GlobalVal.CODE_LOV_ERR_HIST_MODULE_SEND_DOC);
		
		StringBuilder query = new StringBuilder();
		query
			.append(" select id_error_history ")
			.append(" from tr_error_history eh ")
			.append(" join ms_lov ml on eh.lov_modul = ml.id_lov ")
			.append(" join ms_vendor mv on eh.id_ms_vendor = mv.id_ms_vendor ")
			.append(" where ml.lov_group = :lovGroup ")
			.append(" and ml.code = :code ")
			.append(" and ( cust_idno = :nik ")
			.append(" or sps_idno = :nik ")
			.append(" or grt_idno = :nik) ")
			.append(" order by eh.error_date desc limit 1 ");
		
		BigInteger idErrorHistory = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idErrorHistory) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrErrorHistory eh "
				+ "join fetch eh.msTenant mt "
				+ "join fetch eh.msVendor mv "
				+ "where eh.idErrorHistory = :idErrorHistory ",
				new Object[][] {{TrErrorHistory.ID_ERROR_HISTORY_HBM, idErrorHistory.longValue()}});
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrErrorHistory> getErrorHistoryListByRerunProcess(String rerunProcess) {
		Object[][] params = new Object[][] {
			{TrErrorHistory.RERUN_PROCESS_HBM, rerunProcess},
			{MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_ERR_HIST_MODULE},
			{MsLov.CODE_HBM, GlobalVal.CODE_LOV_ERR_HIST_MODULE_SEND_DOC}
		};
		return (List<TrErrorHistory>) this.managerDAO.list(
				"from TrErrorHistory eh "
				+ "join fetch eh.msTenant "
				+ "join fetch eh.msLov ml "
				+ "where eh.rerunProcess = :rerunProcess "
				+ "and ml.lovGroup = :lovGroup "
				+ "and ml.code = :code "
				+ "order by eh.dtmCrt ", params)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public TrErrorHistory getErrorHistoryByIdErrorHistory(Long idErrorHistory) {
		if (null == idErrorHistory) {
			return null;
		}
		return this.managerDAO.selectOne(
				"from TrErrorHistory eh "
				+ "join fetch eh.msTenant mt "
				+ "join fetch eh.msLov ml "
				+ "where eh.idErrorHistory = :idErrorHistory ", 
				new Object[][] {{TrErrorHistory.ID_ERROR_HISTORY_HBM, idErrorHistory}});
	}
	
	@Override
	public TrErrorHistory getLatestErrorHistoryByNikAndIdMsVendor(String nik, Long idVendor) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("nik", nik);
		params.put("idVendor", idVendor);
		params.put(MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_ERR_HIST_MODULE);
		params.put(MsLov.CODE_HBM, GlobalVal.CODE_LOV_ERR_HIST_MODULE_SEND_DOC);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select eh.id_error_history ")
			.append("from tr_error_history eh ")
			.append("join tr_error_history_user_detail ehud on eh.id_error_history = ehud.id_error_history ")
			.append("join ms_lov ml on eh.lov_modul = ml.id_lov ")
			.append("where eh.id_ms_vendor = :idVendor ")
			.append("and ehud.user_idno = :nik ")
			.append("and ml.lov_group = :lovGroup ")
			.append("and ml.code = :code ")
			.append("order by eh.id_error_history desc limit 1 ");
		
		BigInteger idErrorHistory = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idErrorHistory) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrErrorHistory eh "
				+ "join fetch eh.msTenant mt "
				+ "join fetch eh.msVendor mv "
				+ "where eh.idErrorHistory = :idErrorHistory ",
				new Object[][] {{TrErrorHistory.ID_ERROR_HISTORY_HBM, idErrorHistory.longValue()}});
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TrErrorHistoryUserDetail> getErrorHistoryUserDetailByIdErrorHistory(Long idErrorHistory) {
			if (null == idErrorHistory) {
				return null;
			}
			Object[][] params = new Object[][] {{TrErrorHistory.ID_ERROR_HISTORY_HBM, idErrorHistory}};
			
			return (List<TrErrorHistoryUserDetail>) this.managerDAO.list(
					"from TrErrorHistoryUserDetail ehud "
					+ "join fetch ehud.msLov ml "
					+ "join fetch ehud.trErrorHistory eh "
					+ "where eh.idErrorHistory = :idErrorHistory "
					,params).get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertErrorHistoryUserDetail(TrErrorHistoryUserDetail errHistUserDetail) {
		errHistUserDetail.setUsrCrt(MssTool.maskData(errHistUserDetail.getUsrCrt()));
		this.managerDAO.insert(errHistUserDetail);
	}

	@Override
	public TrErrorHistoryUserDetail getErrorHistoryUserDetail(TrErrorHistory errorHistory, String signerTypeCode) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("errorHistory", errorHistory);
		params.put(MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_SIGNER_TYPE);
		params.put(MsLov.CODE_HBM, StringUtils.upperCase(signerTypeCode));
		
		return managerDAO.selectOne(
				"from TrErrorHistoryUserDetail ehud "
				+ "join fetch ehud.msLov ml "
				+ "where ehud.trErrorHistory = :errorHistory "
				+ "and ml.lovGroup = :lovGroup "
				+ "and ml.code = :code ", params);
	}

}
