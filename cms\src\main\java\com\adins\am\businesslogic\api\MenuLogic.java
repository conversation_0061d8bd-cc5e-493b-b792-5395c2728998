package com.adins.am.businesslogic.api;

import java.util.List;

import javax.annotation.security.RolesAllowed;

import com.adins.am.model.AmMsmenu;
import com.adins.am.model.AmMsrole;
import com.adins.esign.webservices.model.GetListMenuOfRoleRequest;
import com.adins.esign.webservices.model.GetListMenuResponse;
import com.adins.esign.webservices.model.UpdateMenuOfRoleRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface MenuLogic {
	List<AmMsmenu> getMenuListByIdRole(long idMsRole, AuditContext callerId);
	void insertMenuOfRole(AmMsmenu menu, AmMsrole role, AuditContext audit);
	
	@RolesAllowed("ROLE_ROLE_MANAGEMENT")
	MssResponseType updateMenuOfRole(UpdateMenuOfRoleRequest request, AuditContext audit);
	@RolesAllowed("ROLE_ROLE_MANAGEMENT")
	GetListMenuResponse getListManageableMenu(AuditContext audit);
	@RolesAllowed("ROLE_ROLE_MANAGEMENT")
	GetListMenuResponse getListMenuOfRole(GetListMenuOfRoleRequest request, AuditContext audit);
}