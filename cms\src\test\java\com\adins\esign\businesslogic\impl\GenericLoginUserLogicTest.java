package com.adins.esign.businesslogic.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.Calendar;
import java.util.Date;

import javax.transaction.Transactional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.stereotype.Component;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.LoginUserLogic;
import com.adins.esign.businesslogic.api.TestSetUpLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
//import com.adins.esign.model.MsBranch;
//import com.adins.esign.model.MsJob;
//import com.adins.esign.model.MsPsre;
import com.adins.exceptions.LoginException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.password.PasswordHash;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
@TestPropertySource(locations = "classpath:application.properties")
@Component
class GenericLoginUserLogicTest extends BaseLogic{
	@Autowired private TestSetUpLogic testSetUpLogic;
	@Autowired private LoginUserLogic loginUserLogic;
	
	private AuditContext auditContext = new AuditContext();

	
	private String password;
	private String loginId;
	private Integer maxFailCount;
	
	@BeforeEach
	@Rollback(true)
	public void setUp() {
//		auditContext.setCallerId("JUNIT");
//		job = daoFactory.getJobDao().getJobByCode(GlobalVal.JOB_ADMIN);
//		branch = daoFactory.getBranchDao().getActiveBranchByCode(GlobalVal.BRANCH_CODE_HO);
//		subsystem = daoFactory.getSubsystemDao().getSubsystemByName(GlobalVal.SUBSYSTEM_ESIGN);
//		user = testSetUpLogic.setUpUser("<EMAIL>", "JUNIT", job, branch, null, subsystem);
//		loginId = user.getLoginId();
//		psre = daoFactory.getPsreDao().getPsreByCode(GlobalVal.PSRE_CODE_DIGISIGN);
//		
//		password = daoFactory.getGeneralSettingDao().getGsValueByCode(GlobalKey.GENERALSETTING_DEFAULT_PASSWORD);
//		maxFailCount = Integer.parseInt(daoFactory.getGeneralSettingDao().getGsValueByCode(GlobalKey.GENERALSETTING_MAX_FAIL_COUNT));
	}
//	
//	@Test
//	void doLoginWithoutExistingData() {
//		assertThrows(LoginException.class, () -> loginUserLogic.doLogin("<EMAIL>", password, auditContext));
//	}
//	
//	@Test
//	@Rollback(true)
//	void doLoginWithCorrectExistingData() {
//		user.setIsLocked(null);
//		user.setFailCount(null);
//		daoFactory.getUserDao().updateUser(user);
//		AmMsuser result = loginUserLogic.doLogin(user.getLoginId(), password, auditContext);
//		assertEquals(result.getLoginId(), user.getLoginId());
//	}
//	
//	@Test
//	@Rollback(true)
//	void doLoginWithLockedUser() {
//		user.setIsLocked("1");
//		daoFactory.getUserDao().updateUser(user);
//		assertThrows(LoginException.class, () -> loginUserLogic.doLogin(loginId, password, auditContext));
//	}
//	
//	@Test
//	@Rollback(true)
//	void doLoginWithInActiveUser() {
//		user.setIsActive("0");
//		daoFactory.getUserDao().updateUser(user);
//		assertThrows(LoginException.class, () -> loginUserLogic.doLogin(loginId, password, auditContext));
//	}
//	
//	@Test
//	@Rollback(true)
//	void doLoginWithExpiredPwd() {
//		Calendar calendar = Calendar.getInstance();
//		calendar.setTime(new Date());
//		calendar.add(Calendar.YEAR, 2);
//		
//		testSetUpLogic.setUpUserPwdhistory(calendar.getTime(), user);
//		AmMsuser result = loginUserLogic.doLogin(user.getLoginId(), password, auditContext);
//		assertEquals(result.getLoginId(), user.getLoginId());
//	}
//	
//	@Test
//	@Rollback(true)
//	void doLoginWithLoginProviderNC() {
//		user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_NC);
//		daoFactory.getUserDao().updateUser(user);
//		AmMsuser result = loginUserLogic.doLogin(user.getLoginId(), password, auditContext);
//		assertEquals(result.getLoginId(), user.getLoginId());
//	}
//	
//	@Test
//	@Rollback(true)
//	void doLoginWithLoginProviderAD() {
//		user = daoFactory.getUserDao().getUserByLoginId("irwan.santoso");
//		user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_AD);
//		user.setPassword(PasswordHash.createHash("Santos313"));
//		daoFactory.getUserDao().updateUser(user);
//		AmMsuser result = loginUserLogic.doLogin(user.getLoginId(), "Santos313", auditContext);
//		assertEquals(result.getLoginId(), user.getLoginId());
//	}
//	
	@Test
	@Rollback(true)
	void doLoginWithPasswordFalse() {
		String loginId ="<EMAIL>";
		
		assertThrows(LoginException.class, () -> loginUserLogic.doLogin(loginId, "password", auditContext));
	}
//	
//	@Test
//	@Rollback(true)
//	void doLoginWithPasswordFalseAndLocked() {
//		user.setIsLocked("1");
//		daoFactory.getUserDao().updateUser(user);
//		assertThrows(LoginException.class, () -> loginUserLogic.doLogin(loginId, "password_false", auditContext));
//	}
//	
//	@Test
//	@Rollback(true)
//	void doLoginWithMaxFailCount() {
//		user.setFailCount(maxFailCount);
//		daoFactory.getUserDao().updateUser(user);
//		assertThrows(LoginException.class, () -> loginUserLogic.doLogin(loginId, "password_false", auditContext));
//	}
//	
//	@Test
//	void checkPreRegisterWithoutPsreCode() {
//		String result = loginUserLogic.checkPreRegister(user.getUuidMsUser(), "", auditContext);
//		assertEquals("0", result);
//	}
//	
//	@Test
//	@Rollback(true)
//	void checkPreRegisterWithExistingPsreCode() {
//		testSetUpLogic.setUpPsreRegisteredUser(user, psre);
//		String result = loginUserLogic.checkPreRegister(user.getUuidMsUser(), psre.getPsreCode(), auditContext);
//		assertEquals("0", result);
//	}
//	
//	@Test
//	void checkPreRegisterWithoutExistingPsreCode() {
//		String result = loginUserLogic.checkPreRegister(user.getUuidMsUser(), psre.getPsreCode(), auditContext);
//		assertEquals( "1", result);
//	}
}
