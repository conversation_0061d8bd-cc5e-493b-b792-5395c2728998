package com.adins.esign.webservices.embed.api;

import com.adins.esign.webservices.model.OfficeListResponse;
import com.adins.esign.webservices.model.RegionListResponse;
import com.adins.esign.webservices.model.embed.OfficeListRequest;
import com.adins.esign.webservices.model.embed.RegionListRequest;

public interface DataEmbedService {
	OfficeListResponse getOfficeListEmbed(OfficeListRequest request);
	RegionListResponse getRegionListEmbed(RegionListRequest request);
}
