package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.esign.dataaccess.api.BalanceTopUpDao;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrBalanceTopUp;
import com.adins.esign.model.custom.ListRoleManagamentBean;
import com.adins.esign.model.custom.ListTopupBalanceBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.GetListManualReportRequest;
import com.adins.esign.webservices.model.GetListTopupBalanceRequest;


@Transactional
@Component
public class BalanceTopUpDaoHbn  extends BaseDaoHbn implements BalanceTopUpDao {

	
	
	@Override
	public void insertTrBalanceTopUp(TrBalanceTopUp balanceTopUp) {
		balanceTopUp.setUsrCrt(MssTool.maskData(balanceTopUp.getUsrCrt()));
		this.managerDAO.insert(balanceTopUp);
	}
	
	private String builConditionalParamListTopupBalance(GetListTopupBalanceRequest request, Map<String, Object> params) {
		StringBuilder builder = new StringBuilder();
		
		if (StringUtils.isNotBlank(request.getTenantCode())) {
			builder.append(" and t.tenant_code = :tenantCode ");
			params.put("tenantCode", request.getTenantCode());
		}
		
		if (StringUtils.isNotBlank(request.getVendorCode())) {
			builder.append(" and v.vendor_code = :vendorCode ");
			params.put("vendorCode", request.getVendorCode());
		}
		if (StringUtils.isNotBlank(request.getBalanceTypeCode())) {
			builder.append(" and l.code = :balanceTypeCode ");
			params.put("balanceTypeCode", request.getBalanceTypeCode());
		}
		
		if (StringUtils.isNotBlank(request.getExpiredDateStart())) {
			builder.append(" and cast(btu.expired_date as date) >= cast( :expiredDateStart as date) ")
					.append(" and cast(btu.expired_date as date) <= cast( :expiredDateEnd as date) ");
			params.put("expiredDateStart", request.getExpiredDateStart());
			params.put("expiredDateEnd", request.getExpiredDateEnd());
		}
		if (StringUtils.isNotBlank(request.getStatus())) {
			builder.append(" and btu.is_used = :status ");
			params.put("status", request.getStatus());
		}
		if (StringUtils.isNotBlank(request.getRefNumber())) {
		    builder.append(" and LOWER(bm.ref_no) like :refNumber ");
		    params.put("refNumber", "%" + request.getRefNumber().toLowerCase() + "%");
		}
		if (StringUtils.isNotBlank(request.getDescription())) {
		    builder.append(" and LOWER(bm.notes) like :description ");
		    params.put("description", "%" + request.getDescription().toLowerCase() + "%");
		}
		
		return builder.toString();
	}

	@Override
	public BigInteger countListTopupBalance(GetListTopupBalanceRequest request) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		String conditionalParams = builConditionalParamListTopupBalance(request, params);
		
		query.append("select count(*) ")
		 .append(" from tr_balance_top_up btu ")
		 .append(" join tr_balance_mutation bm on bm.id_balance_mutation = btu.id_balance_mutation ")
		 .append(" join ms_tenant t on t.id_ms_tenant = bm.id_ms_tenant ")
		 .append(" join ms_vendor v on v.id_ms_vendor = bm.id_ms_vendor ")
		 .append(" join ms_lov l on l.id_lov = bm.lov_balance_type ")
		 .append("where 1=1 ")
		 .append(conditionalParams);
		
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public List<ListTopupBalanceBean> getListTopupBalanceByTenantAndVendor(GetListTopupBalanceRequest request, int min, int max) {
		Map<String, Object> params = new HashMap<>();
	    params.put("min", min);
	    params.put("max", max);

	    StringBuilder query = new StringBuilder();
	    String conditionalParams = builConditionalParamListTopupBalance(request, params);

	    query.append("select * from ( ")
	         .append("select bm.id_balance_mutation as \"idBalanceMutation\", bm.trx_no as \"trxNo\", t.tenant_name as \"tenantName\", v.vendor_name as \"vendorName\", l.description as \"balanceName\", CAST(bm.trx_date AS DATE) as \"topupDate\", ")
	         .append("CAST(btu.expired_date AS DATE) as \"expiredDate\", CAST(btu.last_expired_date AS DATE) as \"lastExpiredDate\", btu.extend_expired_attempt as \"extendExpiredAttempt\", btu.balance_price_per_qty as \"balancePrice\", ")
	         .append("bm.ref_no as \"refNumber\", t.tenant_code as \"tenantCode\", v.vendor_code as \"vendorCode\", l.code as \"balanceTypeCode\", bm.notes as \"description\", btu.is_used as \"status\", row_number() over (order by btu.id_balance_top_up) as no ") 
	         .append("from tr_balance_top_up btu ")
	         .append(" join tr_balance_mutation bm on bm.id_balance_mutation = btu.id_balance_mutation ")
	         .append(" join ms_tenant t on t.id_ms_tenant = bm.id_ms_tenant ")
	         .append(" join ms_vendor v on v.id_ms_vendor = bm.id_ms_vendor ")
	         .append(" join ms_lov l on l.id_lov = bm.lov_balance_type ")
	         .append("where 1=1 ")
	         .append(conditionalParams);

	    query.append(") as a where a.no between :min and :max "); 

	    return managerDAO.selectForListString(ListTopupBalanceBean.class, query.toString(), params, null);
	 
	}

	@Override
	public TrBalanceTopUp getBalanceTopupByIdBalanceMutation(long idBalanceMutation) {
		return this.managerDAO.selectOne(
				"from TrBalanceTopUp tbt "
				+ "join fetch tbt.trBalanceMutation tbm "
				+ "where tbm.idBalanceMutation = :idBalanceMutation", 
						new Object[][] {{"idBalanceMutation", idBalanceMutation}});
		
//		Object[][] params = new Object[][] {{Restrictions.eq("idBalanceMutation", idBalanceMutation)}};
//		return managerDAO.selectOne(TrBalanceTopUp.class, params);
	}

	@Override
	public void updateTrBalanceTopUp(TrBalanceTopUp balanceTopUp) {
		balanceTopUp.setUsrUpd(MssTool.maskData(balanceTopUp.getUsrUpd()));
		this.managerDAO.update(balanceTopUp);
	}

	
}
