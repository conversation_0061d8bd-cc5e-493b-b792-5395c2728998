package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class TenantException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonTenant {
		TENANT_CODE_EMPTY,
		TENANT_NOT_FOUND,
		REMINDER_RECEIVER_TOO_LONG,
		TENANT_CODE_EXISTED,
		TENANT_BALANCE_TYPE_NOT_FOUND,
		INVALID_THRESHOLD,
		LIST_BALANCE_TENANT_EMPTY,
		BALANCE_TYPE_EMPTY,
		VALIDATE_TENANT_API_KEY_ERROR,
		TENANT_API_KEY_EMPTY,
		TENANT_API_KEY_INVALID,
		TENANT_LIVENESS_FACE_COMPARE_SERVICES_NOT_ACTIVE,
		TENANT_WITH_TENANT_CODE_NOT_REGISTERED,
		INCORRECT_API_KEY,
		INVALID_CALLBACK_URL,
		INVALID_USE_WA_MESSAGE,
		INVALID_CLIENT_URL,
		MSG_TEMPLATE_WITH_OTP_ACTIVE_DURATION_NOT_FOUND,
		REGION_CODE_EMPTY,
		BUSINESS_LINE_CODE_NOT_FOUND,
		VENDOR_CODE_EMPTY,
		UNKNOWN
	}
	
	private final ReasonTenant reason;
	
	public TenantException(ReasonTenant reason) {
		this.reason = reason;
	}
	
	public TenantException(String message, ReasonTenant reason) {
		super(message);
		this.reason = reason;
	}
	
	public TenantException(Throwable ex, ReasonTenant reason) {
		super(ex);
		this.reason = reason;
	}
	
	public TenantException(String message, Throwable ex, ReasonTenant reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	@Override
	public int getErrorCode() {
		if (null != this.reason) {
			switch (reason) {
			case TENANT_CODE_EMPTY:
				return StatusCode.TENANT_CODE_EMPTY;
			case TENANT_NOT_FOUND:
				return StatusCode.TENANT_NOT_FOUND;
			case REMINDER_RECEIVER_TOO_LONG:
				return StatusCode.TENANT_REMINDER_RECEIVER_TOO_LONG;
			case TENANT_CODE_EXISTED:
				return StatusCode.TENANT_CODE_EXISTED;
			case TENANT_BALANCE_TYPE_NOT_FOUND:
				return StatusCode.TENANT_BALANCE_TYPE_NOT_FOUND;
			case INVALID_THRESHOLD:
				return StatusCode.TENANT_INVALID_THRESHOLD;
			case LIST_BALANCE_TENANT_EMPTY:
				return StatusCode.TENANT_LIST_BALANCE_EMPTY;
			case BALANCE_TYPE_EMPTY:
				return StatusCode.TENANT_BALANCE_TYPE_EMTPY;
			case VALIDATE_TENANT_API_KEY_ERROR:
				return StatusCode.TENANT_VALIDATE_API_KEY_ERROR;
			case TENANT_API_KEY_EMPTY:
				return StatusCode.TENANT_API_KEY_EMPTY;
			case TENANT_API_KEY_INVALID:
				return StatusCode.TENANT_API_KEY_INVALID;
			case TENANT_LIVENESS_FACE_COMPARE_SERVICES_NOT_ACTIVE:
				return StatusCode.TENANT_LIVENESS_FACE_COMPARE_SERVICES_NOT_ACTIVE;
			case TENANT_WITH_TENANT_CODE_NOT_REGISTERED:
				return StatusCode.TENANT_WITH_TENANT_CODE_NOT_REGISTERED;
			case INCORRECT_API_KEY:
				return StatusCode.INCORRECT_API_KEY;
			case INVALID_CALLBACK_URL:
				return StatusCode.TENANT_CALLBACK_URL_INVALID;
			case INVALID_USE_WA_MESSAGE:
				return StatusCode.INVALID_USE_WA_MESSAGE;
			case INVALID_CLIENT_URL:
				return StatusCode.INVALID_CLIENT_URL;
			case MSG_TEMPLATE_WITH_OTP_ACTIVE_DURATION_NOT_FOUND:
				return StatusCode.MSG_TEMPLATE_WITH_OTP_ACTIVE_DURATION_NOT_FOUND;
			case REGION_CODE_EMPTY:
				return StatusCode.REGION_CODE_EMPTY;
			case BUSINESS_LINE_CODE_NOT_FOUND:
				return StatusCode.BUSINESS_LINE_NOT_FOUND;
			case VENDOR_CODE_EMPTY:
				return StatusCode.TENANT_VENDOR_CODE_EMPTY;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}
}
