package com.adins.esign.webservices.frontend.api;

import com.adins.esign.model.custom.UpdatePSrERequest;
import com.adins.esign.webservices.model.UpdatePsrePriorityRequest;
import com.adins.esign.webservices.model.UpdatePsrePriorityResponse;
import com.adins.framework.service.base.model.MssResponseType;

public interface VendorService {
	MssResponseType updateVendorPSrE (UpdatePSrERequest request);
	
	UpdatePsrePriorityResponse updatePSrEPriority (UpdatePsrePriorityRequest request);
	
}
