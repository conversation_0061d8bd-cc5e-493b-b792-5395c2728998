package com.adins.esign.job;

import java.util.concurrent.BlockingQueue;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.custom.QueueDeleteBean;
import com.adins.esign.model.custom.SaveSignCallbackCompleteSIgnTekenAjaBean;
import com.adins.framework.persistence.dao.model.AuditContext;


@DisallowConcurrentExecution
@Component
public class SigningTekenAjaJob {
	private static final Logger LOG = LoggerFactory.getLogger(SigningTekenAjaJob.class);
	@Autowired private DocumentLogic documentLogic;
	
	public void takeQueueSaveSigningResult() {
	    BlockingQueue<SaveSignCallbackCompleteSIgnTekenAjaBean> saveSigningResultCompleteTekenAja = QueuePublisher.getSaveSigningResultCompleteTekenAja();
		AuditContext audit = new AuditContext(GlobalVal.VENDOR_CODE_DJELAS);

		while (!saveSigningResultCompleteTekenAja.isEmpty()) {
			try {
				QueueDeleteBean bean = new QueueDeleteBean();
				SaveSignCallbackCompleteSIgnTekenAjaBean beanSign = saveSigningResultCompleteTekenAja.take();
				documentLogic.saveDocumentSignResultTekenAjaWithoutRolesAllowed(beanSign, audit);
				String refNumber = documentLogic.getRefNumberByPsreId(beanSign.getDocumentId());
				bean.setRefNumber(refNumber);
				documentLogic.deleteFromOssLogic(bean);
			} catch (Exception e) {
				LOG.error("Error on running take queue save signing result", e);
			}
		}
		
		
		LOG.debug( "Queue save signing result tekenAja empty! Consumer Finished!" );
	}
}
