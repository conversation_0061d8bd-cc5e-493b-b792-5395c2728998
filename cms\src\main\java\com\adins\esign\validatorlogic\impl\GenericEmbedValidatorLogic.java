package com.adins.esign.validatorlogic.impl;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.OfficeLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.embed.BaseEmbedMsgBeanV2;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.exceptions.EmbedMsgException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.EmbedMsgException.ReasonEmbedMsg;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericEmbedValidatorLogic extends BaseLogic implements EmbedValidatorLogic {
	
	@Autowired private CommonLogic commonLogic;
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private OfficeLogic officeLogic;
	
	@Override
	public EmbedMsgBean validateEmbedMessage(String encryptedMsg, AuditContext audit) {
		if (StringUtils.isBlank(encryptedMsg)) {
			throw new EmbedMsgException(getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY,
					null, audit), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		
		try {
			return commonLogic.decryptMessageToClass(encryptedMsg, audit, EmbedMsgBean.class);
		} catch (Exception e) {
			throw new EmbedMsgException(getMessage(GlobalKey.MESSAGE_ERROR_EMBED_INVALID_ENCRYPTED, null, audit), e, ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}
	}
	
	@Override
	public EmbedMsgBeanV2 validateEmbedMessageV2(String encryptedMsg, String tenantCode, boolean userMustExists, AuditContext audit) {
		
		if (StringUtils.isBlank(encryptedMsg)) {
			throw new EmbedMsgException(getMessage("businesslogic.embedmsg.emptymsg", null, audit), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(tenantCode, true, audit);
		String aesKey = tenant.getAesEncryptKey();
		
		BaseEmbedMsgBeanV2 embedBean = null;
		try {
			embedBean = commonLogic.decryptMessageToClass(encryptedMsg, aesKey, BaseEmbedMsgBeanV2.class, audit);
		} catch (Exception e) {
			throw new EmbedMsgException(getMessage(GlobalKey.MESSAGE_ERROR_EMBED_INVALID_ENCRYPTED, null, audit), e, ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}
		
		if (StringUtils.isBlank(embedBean.getEmail())) {
			throw new EmbedMsgException(getMessage("businesslogic.embedmsg.datanotfoundinencryptedobject", new String[] {"email"}, audit), ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}
		if (StringUtils.isBlank(embedBean.getOfficeCode())) {
			throw new EmbedMsgException(getMessage("businesslogic.embedmsg.datanotfoundinencryptedobject", new String[] {"office"}, audit), ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}
		
		validateEmbedTimestamp(embedBean, audit);
		
		AmMsuser user = null;
		if (userMustExists) {
			user = userValidatorLogic.validateGetUserByEmailv2(embedBean.getEmail(), true, audit);
			
			MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCode(user.getIdMsUser(), tenantCode);
			if (null == useroftenant) {
				throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USERVAL_EMAILNOTFOUND, new Object[] {embedBean.getEmail()}, audit), ReasonUser.USER_NOT_FOUND);
			}
		}
		
		MsOffice office = officeLogic.getOfficeByCodeAndTenant(embedBean.getOfficeCode(), true, tenant, audit);
		
		EmbedMsgBeanV2 bean = new EmbedMsgBeanV2();
		bean.setMsTenant(tenant);
		bean.setAmMsuser(user);
		bean.setMsOffice(office);
		bean.setDecryptedEmail(embedBean.getEmail());
		return bean;
	}
	
	private long getEmbedSessionDuration() {
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode("EMBED_V2_SESSION_TIME_MINUTES");
		if (null == gs) {
			return 15L;
		}
		
		try {
			return Long.valueOf(gs.getGsValue());
		} catch (Exception e) {
			return 15L;
		}
	}
	
	private void validateEmbedTimestamp(BaseEmbedMsgBeanV2 embedMsgBean, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_TIME_FORMAT_SEC);
		sdf.setLenient(false);
		
		Date sessionDate = null;
		try {
			sessionDate = sdf.parse(embedMsgBean.getTimestamp());
		} catch (Exception e) {
			throw new EmbedMsgException(getMessage(GlobalKey.MESSAGE_ERROR_EMBED_INVALID_ENCRYPTED, null, audit), e, ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}
		
		// Validasi session
		Date currentTime = new Date();
		long diffMs = currentTime.getTime() - sessionDate.getTime();
		long diffMinutes = diffMs / 1000 / 60;
		long maxSessionTimeMinutes = getEmbedSessionDuration();
		
		// Validasi x menit ke belakang
		if (diffMinutes >= maxSessionTimeMinutes) {
			throw new EmbedMsgException(getMessage("businesslogic.embedmsg.sessionexpired", null, audit), ReasonEmbedMsg.EMBED_SESSION_EXPIRED);
		}
		
		// Validasi x menit ke depan
		diffMs = sessionDate.getTime() - currentTime.getTime();
		diffMinutes = diffMs / 1000 / 60;
		if (diffMinutes >= maxSessionTimeMinutes) {
			throw new EmbedMsgException(getMessage("businesslogic.embedmsg.sessionexpired", null, audit), ReasonEmbedMsg.EMBED_SESSION_EXPIRED);
		}
		
		
	}

}
