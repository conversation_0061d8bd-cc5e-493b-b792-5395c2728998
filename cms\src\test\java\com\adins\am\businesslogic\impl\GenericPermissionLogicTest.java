package com.adins.am.businesslogic.impl;

import static org.junit.Assert.assertTrue;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.businesslogic.api.PermissionLogic;
import com.adins.am.model.AmMsuser;
import com.adins.esign.constants.GlobalVal;
import com.adins.framework.persistence.dao.model.AuditContext;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class GenericPermissionLogicTest extends BaseLogic{
	@Autowired private PermissionLogic permissionLogic;
	
	private AuditContext auditContext = new AuditContext();
	private AmMsuser user;
	
	@BeforeEach
	public void setUp() {
		user = daoFactory.getUserDao().getUserByLoginId(GlobalVal.USER_ADMIN_LOGIN_ID);
		auditContext.setCallerId("JUNIT");
	}
	
	@Test
	void getPermissionsEmptyTest() {
		List<Map<String,Object>> permissions = permissionLogic.getPermissions(user, auditContext);
		assertTrue(permissions.isEmpty());
	}
}
