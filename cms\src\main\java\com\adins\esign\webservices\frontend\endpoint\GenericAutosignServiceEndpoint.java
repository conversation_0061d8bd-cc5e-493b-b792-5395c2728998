package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.AutosignLogic;
import com.adins.esign.webservices.frontend.api.AutosignService;
import com.adins.esign.webservices.model.DetailImportAutosignBmRequest;
import com.adins.esign.webservices.model.DetailImportAutosignBmResponse;
import com.adins.esign.webservices.model.DownloadTemplateExcelImportAutosignBmRequest;
import com.adins.esign.webservices.model.DownloadTemplateExcelImportAutosignBmResponse;
import com.adins.esign.webservices.model.ImportAutosignBmDataRequest;
import com.adins.esign.webservices.model.ImportAutosignBmDataResponse;
import com.adins.esign.webservices.model.InquiryImportAutosignBmRequest;
import com.adins.esign.webservices.model.InquiryImportAutosignBmResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/autosign")
@Api(value = "AutosignService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericAutosignServiceEndpoint implements AutosignService {
	
	@Autowired private AutosignLogic autoSignLogic;
	
	@Override
	@POST
	@Path("/s/importBmAutosign")
	public  ImportAutosignBmDataResponse importAutosignBmData(ImportAutosignBmDataRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return autoSignLogic.importDataBmAutosign(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/inquiryImportBmAutosign")
	public InquiryImportAutosignBmResponse listImportAutosignBmDataResponse(InquiryImportAutosignBmRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return autoSignLogic.inquiryImportAutosignBm(request, audit);
	}

	@Override
	@POST
	@Path("/s/detailImportBmAutosign")
	public DetailImportAutosignBmResponse detailImportAutosignBm(DetailImportAutosignBmRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return autoSignLogic.detailImportAutosignBm(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/downloadTemplateExcelImportAutosignBm")
	public DownloadTemplateExcelImportAutosignBmResponse downloadTemplateExcelImportAutosignBm(DownloadTemplateExcelImportAutosignBmRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return autoSignLogic.downloadTemplateExcelImportAutosignBm(request, audit);
	}
}
