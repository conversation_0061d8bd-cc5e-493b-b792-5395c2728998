package com.adins.esign.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.EmailLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.RegistrationLogic;
import com.adins.esign.businesslogic.api.SigningProcessAuditTrailLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.businesslogic.api.interfacing.PrivyGeneralLogic;
import com.adins.esign.businesslogic.api.interfacing.PrivyLogic;
import com.adins.esign.businesslogic.api.interfacing.TekenAjaLogic;
import com.adins.esign.businesslogic.api.interfacing.VidaLogic;
import com.adins.esign.businesslogic.impl.interfacing.GenericDigisignLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.TekenAjaConstant;
import com.adins.esign.constants.enums.RegistrationType;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.TrJobCheckRegisterStatus;
import com.adins.esign.model.TrReregistrationUser;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.custom.RegisterDigisignResponseBean;
import com.adins.esign.model.custom.RegisterVerificationStatusBean;
import com.adins.esign.model.custom.TknajRegisterCekResponse;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.privy.PrivyRegisterV2ResponseContainer;
import com.adins.esign.model.custom.privy.PrivyVerifV2CredentialsBean;
import com.adins.esign.model.custom.privygeneral.PrivyGeneralRegisterResponseContainer;
import com.adins.esign.model.custom.vida.VidaRegisterResponseContainer;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.validatorlogic.api.InvitationLinkValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.esign.webservices.model.CreateSingleEmailRequest;
import com.adins.esign.webservices.model.CreateSingleEmailResponse;
import com.adins.esign.webservices.model.RegisterResponse;
import com.adins.esign.webservices.model.RegistrationByInvitationRequest;
import com.adins.esign.webservices.model.digisign.DigisignRegisterRequest;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.external.RegisterExternalResponse;
import com.adins.esign.webservices.model.privy.PrivyRegisterV2Response;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralRegisterResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterApiResponse;
import com.adins.esign.webservices.model.vida.VidaRegisterResponse;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.UserException;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.google.gson.Gson;

@Component
public class GenericRegistrationLogic extends BaseLogic implements RegistrationLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericRegistrationLogic.class);
	
	@Autowired private Gson gson;
	
	@Autowired private InvitationLinkValidatorLogic invLinkValidatorLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private BalanceValidatorLogic balanceValidatorLogic;
	@Autowired private VendorValidatorLogic vendorValidatorLogic;
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private EmailLogic emailLogic;
	@Autowired private UserLogic userLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private PersonalDataEncryptionLogic encryptionLogic;
	@Autowired private SigningProcessAuditTrailLogic auditTrailLogic;
	
	// Logic PSrE
	@Autowired private VidaLogic vidaLogic;
	@Autowired private PrivyLogic privyLogic;
	@Autowired private PrivyGeneralLogic privyGeneralLogic;
	@Autowired private DigisignLogic digisignLogic;
	@Autowired private TekenAjaLogic tekenAjaLogic;
	
	private static final String VERIF_SUCCESS_NOTES = "Verifikasi berhasil";
	private static final String PRIVY_SUBMIT_SUCCESS_NOTES = "Success retrieve data";
	private static final String SYNC_PRIVY_SUCCESS_REGIS_CODE = "R200";

	private boolean isPhoneRegisteredInVendor(MsVendorRegisteredUser phoneVendorUser, String email, String phone, String nik, String vendorCode, AuditContext audit) {
		if (GlobalVal.VENDOR_CODE_VIDA.equals(vendorCode)) {

			// Allow register jika vendor_registration_id kosong dan personal data tidak digunakan orang lain
			if (StringUtils.isBlank(phoneVendorUser.getVendorRegistrationId())) {
				userValidatorLogic.validateVendorRegisteredUserDataState(phoneVendorUser, nik, email, phone, audit);
				return false;
			}

			// Allow register jika certificate expired
			// Allow return false
			return !userValidatorLogic.isCertifExpiredForRegister(phoneVendorUser, audit);
		}
		
		if (GlobalVal.VENDOR_CODE_PRIVY_ID.contentEquals(vendorCode) && StringUtils.isBlank(phoneVendorUser.getVendorRegistrationId())) {
			userValidatorLogic.validateVendorRegisteredUserDataState(phoneVendorUser, nik, email, phone, audit);
			return false;
		}
		
		return true;
	}

	private boolean isEmailRegisteredInVendor(MsVendorRegisteredUser emailVendorUser, String email, String phone, String nik, String vendorCode, AuditContext audit) {
		if (GlobalVal.VENDOR_CODE_VIDA.equals(vendorCode)) {
			
			// Allow register jika vendor_registration_id kosong dan personal data tidak digunakan orang lain
			if (StringUtils.isBlank(emailVendorUser.getVendorRegistrationId())) {
				userValidatorLogic.validateVendorRegisteredUserDataState(emailVendorUser, nik, email, phone, audit);
				return false;
			}
			
			// Allow register jika certificate expired
			// Allow return false
			return !userValidatorLogic.isCertifExpiredForRegister(emailVendorUser, audit);
		}
		
		if (GlobalVal.VENDOR_CODE_PRIVY_ID.contentEquals(vendorCode) && StringUtils.isBlank(emailVendorUser.getVendorRegistrationId())) {
			userValidatorLogic.validateVendorRegisteredUserDataState(emailVendorUser, nik, email, phone, audit);
			return false;			
		}

		return true;
	}

	private boolean emailPhoneRegisteredInVendor(String email, String phone, String vendorCode, String nik, AuditContext audit) {
		if (StringUtils.isNotBlank(phone)) {
			List<MsVendorRegisteredUser> phoneVendorUser = daoFactory.getVendorRegisteredUserDao().getListVendorUserByPhone(phone, vendorCode);
			if (CollectionUtils.isNotEmpty(phoneVendorUser)) {
				return isPhoneRegisteredInVendor(phoneVendorUser.get(0), email, phone, nik, vendorCode, audit);
			}
		}
		
		if (StringUtils.isNotBlank(email)) {
			List<MsVendorRegisteredUser> emailVendorUser = daoFactory.getVendorRegisteredUserDao().getListVendorUserByEmail(email, vendorCode);
			if (CollectionUtils.isNotEmpty(emailVendorUser)) {
				return isEmailRegisteredInVendor(emailVendorUser.get(0), email, phone, nik, vendorCode, audit);
			}
		}
		
		return false;
	}
	
	private boolean userCanReregister(String nik, String phone, String email, String vendorCode) {
		TrReregistrationUser reregistrationUser = daoFactory.getReregistrationUserDao().getRegistrationUser(nik, phone, email, vendorCode);
		return null != reregistrationUser;
	}

	private boolean bypassSyncPrivyVerif() {
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode("BYPASS_SYNC_PRIVY_VERIF");
		if (null == gs) {
			return false;
		}

		try {
			return "1".equals(gs.getGsValue());
		} catch (Exception e) {
			return false;
		}
	}

	private RegisterResponse registerPrivySynchronous(UserBean userData, TrInvitationLink invLink, RegistrationType registrationType, MsLov lovUserType, AuditContext audit) {

		MsTenant tenant = invLink.getMsTenant();
		MsVendor vendor = invLink.getMsVendor();

		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_VRF_V2, tenant, vendor, audit);

		MsTenantSettings usernameSetting = tenantSettingsLogic.getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_PRIVY_SYNC_VERIF_USERNAME, true, audit);
		String username = usernameSetting.getSettingValue();

		MsTenantSettings passwordSetting = tenantSettingsLogic.getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_PRIVY_SYNC_VERIF_PASSWORD, true, audit);
		String password = passwordSetting.getSettingValue();

		MsTenantSettings merchantKeySetting = tenantSettingsLogic.getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_PRIVY_SYNC_VERIF_MERCHANT_KEY, true, audit);
		String merchantKey = merchantKeySetting.getSettingValue();

		PrivyVerifV2CredentialsBean credentials = new PrivyVerifV2CredentialsBean(username, password, merchantKey);

		long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_VRF_V2);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UVRF_V2);

		PrivyRegisterV2ResponseContainer responseContainer = null;
		if (bypassSyncPrivyVerif()) {
			responseContainer = privyLogic.registerV2WithInvitationRequestDummy(userData, reservedTrxNo, credentials, audit);
		} else {
			responseContainer = privyLogic.registerV2WithInvitationRequest(userData, reservedTrxNo, credentials, audit);
		}
		
		PrivyRegisterV2Response registerResponse = responseContainer.getResponse();

		if (registerResponse.getCode() == 200
				&& "verified".equalsIgnoreCase(registerResponse.getUserStatus())
				&& SYNC_PRIVY_SUCCESS_REGIS_CODE.equals(registerResponse.getRegistrationStatus())) {
			
			AmMsuser user = privyLogic.insertRegisteredUserV2(userData, registerResponse, tenant, registrationType, lovUserType, audit);

			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(reservedTrxNo);
			mutation.setTrxDate(new Date());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setUsrCrt(userData.getUserPhone());
			mutation.setDtmCrt(new Date());
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setRefNo(invLink.getRefNumber());
			mutation.setMsOffice(invLink.getMsOffice());
			mutation.setMsBusinessLine(invLink.getMsBusinessLine());
			mutation.setNotes(VERIF_SUCCESS_NOTES);
			mutation.setAmMsuser(user);
			mutation.setVendorTrxNo(registerResponse.getTransactionId());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);

			TrSigningProcessAuditTrail trail = insertRegistrationAuditTrail(userData.getUserPhone(), userData.getEmail(), user, invLink, VERIF_SUCCESS_NOTES, audit);
			auditTrailLogic.logProcessRequestResponse(trail, GlobalVal.AUDIT_TRAIL_SUBFOLDER_REGISTER, responseContainer.getJsonRequest(), responseContainer.getJsonResponse(), false, audit);

			return new RegisterResponse();
		}

		if (!SYNC_PRIVY_SUCCESS_REGIS_CODE.equals(registerResponse.getRegistrationStatus())) {

			String errorMessage = getSynchronousPrivyRegisterStatus(registerResponse, audit);

			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(reservedTrxNo);
			mutation.setTrxDate(new Date());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setUsrCrt(userData.getUserPhone());
			mutation.setDtmCrt(new Date());
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setRefNo(invLink.getRefNumber());
			mutation.setMsOffice(invLink.getMsOffice());
			mutation.setMsBusinessLine(invLink.getMsBusinessLine());
			mutation.setNotes(StringUtils.left(errorMessage, 200));
			mutation.setVendorTrxNo(registerResponse.getTransactionId());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);

			insertRegistrationAuditTrail(userData.getUserPhone(), userData.getEmail(), null, invLink, errorMessage, audit);

			Status status = new Status();
			status.setCode(StatusCode.PRIVY_ERROR);
			status.setMessage(errorMessage);

			RegisterResponse response = new RegisterResponse();
			response.setStatus(status);
			return response;
		}

		// Handling verif failed response
		if (StringUtils.isNotBlank(registerResponse.getRejectedCode())) {

			logPrivyVerifV2RejectCode(registerResponse);

			String errorMessage = "Verifikasi Gagal. Nama, Tanggal Lahir, atau Foto Diri tidak sesuai. Harap cek kembali Nama dan Tanggal Lahir Anda serta mengambil ulang Foto Diri.";

			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(reservedTrxNo);
			mutation.setTrxDate(new Date());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setUsrCrt(userData.getUserPhone());
			mutation.setDtmCrt(new Date());
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setRefNo(invLink.getRefNumber());
			mutation.setMsOffice(invLink.getMsOffice());
			mutation.setMsBusinessLine(invLink.getMsBusinessLine());
			mutation.setNotes(StringUtils.left(errorMessage, 200));
			mutation.setVendorTrxNo(registerResponse.getTransactionId());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);

			insertRegistrationAuditTrail(userData.getUserPhone(), userData.getEmail(), null, invLink, errorMessage, audit);

			Status status = new Status();
			status.setCode(StatusCode.PRIVY_ERROR);
			status.setMessage(errorMessage);

			RegisterResponse response = new RegisterResponse();
			response.setStatus(status);
			return response;
		}

		// Handle other error
		String errorMessage = registerResponse.getMessage();

		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(reservedTrxNo);
		mutation.setTrxDate(new Date());
		mutation.setQty(0);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setUsrCrt(userData.getUserPhone());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(tenant);
		mutation.setMsVendor(vendor);
		mutation.setRefNo(invLink.getRefNumber());
		mutation.setMsOffice(invLink.getMsOffice());
		mutation.setMsBusinessLine(invLink.getMsBusinessLine());
		mutation.setNotes(StringUtils.left(errorMessage, 200));
		mutation.setVendorTrxNo(registerResponse.getTransactionId());
		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);

		insertRegistrationAuditTrail(userData.getUserPhone(), userData.getEmail(), null, invLink, errorMessage, audit);

		Status status = new Status();
		status.setCode(StatusCode.PRIVY_ERROR);
		status.setMessage(errorMessage);

		RegisterResponse response = new RegisterResponse();
		response.setStatus(status);
		return response;
	}

	private void logPrivyVerifV2RejectCode(PrivyRegisterV2Response response) {
		if (null == response || StringUtils.isBlank(response.getRejectedCode())) {
			return;
		}

		String rejectedCode = response.getRejectedCode();
		if ("R102".equals(rejectedCode)) {
			LOG.info("Privy V2 register rejected. NIK not found.");
			return;
		}

		if ("R103".equals(rejectedCode)) {
			LOG.info("Privy V2 register rejected. Fullname does not match.");
			return;
		}

		if ("R104".equals(rejectedCode)) {
			LOG.info("Privy V2 register rejected. Date of birth does not match.");
			return;
		}

		if ("R106-1".equals(rejectedCode)) {
			LOG.info("Privy V2 register rejected. Low selfie score (30 <= X < 75).");
			return;
		}

		if ("R106-2".equals(rejectedCode)) {
			LOG.info("Privy V2 register rejected. Low selfie score (0 <= X < 30).");
			return;
		}

		if ("R107".equals(rejectedCode)) {
			LOG.info("Privy V2 register rejected. Name and date of birth does not match.");
			return;
		}

		if ("R108".equals(rejectedCode)) {
			LOG.info("Privy V2 register rejected. Name and selfie does not match.");
			return;
		}

		if ("R109".equals(rejectedCode)) {
			LOG.info("Privy V2 register rejected. Date of birth and selfie does not match.");
			return;
		}

		if ("R110".equals(rejectedCode)) {
			LOG.info("Privy V2 register rejected. Name, date of birth, and selfie does not match.");
			return;
		}

		LOG.warn("Privy V2 register rejected. Unhandled reject code.");
	}

	private String getSynchronousPrivyRegisterStatus(PrivyRegisterV2Response response, AuditContext audit) {
		if (null == response || StringUtils.isBlank(response.getRegistrationStatus())) {
			return null;
		}

		if ("R201".equals(response.getRegistrationStatus())) {
			LOG.info("Privy V2 register rejected. Phone is used by another NIK.");
			return getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_EMAIL_USED_BY_OTHER_NIK, null, audit);
		}

		if ("R202".equals(response.getRegistrationStatus())) {
			LOG.info("Privy V2 register rejected. Email is used by another NIK.");
			return getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_PHONE_USED_BY_OTHER_NIK, null, audit);
		}

		if ("R203".equals(response.getRegistrationStatus())) {
			LOG.info("Privy V2 register rejected. Phone and email is used by another NIK.");
			return getMessage("businesslogic.register.phoneemailusedbyothernik", null, audit);
		}

		LOG.warn("Unhandled Privy registration status");
		return null;
	}
	
	private RegisterResponse registerPrivy(UserBean userData, TrInvitationLink invLink, MsLov lovUserType, AuditContext audit) {
		
		MsTenant tenant = invLink.getMsTenant();
		MsVendor vendor = invLink.getMsVendor();
		
		validateActiveJobCheckRegisterStatus(userData.getIdNo(), vendor, audit);
		
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_VRF, tenant, vendor, audit);
		
		long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UVRF);
		
		PrivyGeneralRegisterResponseContainer responseContainer = privyGeneralLogic.registerWithInvitationRequest(userData, reservedTrxNo, tenant, vendor, audit);
		PrivyGeneralRegisterResponse registerResponse = responseContainer.getRegisterResponse();
		
		// Registrasi berhasil, menunggu verifikasi
		if (PRIVY_SUBMIT_SUCCESS_NOTES.equals(registerResponse.getMessage())) {
			
			TrBalanceMutation balanceMutation = new TrBalanceMutation();
			balanceMutation.setTrxNo(reservedTrxNo);
			balanceMutation.setTrxDate(new Date());
			balanceMutation.setQty(-1);
			balanceMutation.setMsLovByLovBalanceType(balanceType);
			balanceMutation.setMsLovByLovTrxType(trxType);
			balanceMutation.setUsrCrt(userData.getUserPhone());
			balanceMutation.setDtmCrt(new Date());
			balanceMutation.setMsTenant(tenant);
			balanceMutation.setMsVendor(vendor);
			balanceMutation.setNotes(registerResponse.getData().getStatus());
			balanceMutation.setVendorTrxNo(registerResponse.getData().getRegisterToken());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(balanceMutation);
			
			TrJobCheckRegisterStatus job = new TrJobCheckRegisterStatus();
			job.setTrBalanceMutation(balanceMutation);
			job.setHashedIdNo(MssTool.getHashedString(userData.getIdNo()));
			job.setRequestStatus((short) 0);
			job.setIsExternal("0");
			job.setLovUserType(lovUserType);
			job.setUsrCrt(audit.getCallerId());
			job.setDtmCrt(new Date());
			daoFactory.getJobCheckRegisterStatusDao().insertJobCheckRegStatusNewTrx(job);
			
			userValidatorLogic.removeUnnecessaryRegisterParam(userData, tenant);
			String jsonRequest = gson.toJson(userData);
			cloudStorageLogic.storeRegisterRequest(job, jsonRequest);
			
			TrSigningProcessAuditTrail trail = insertPrivyRegistrationAuditTrail(userData.getUserPhone(), userData.getEmail(), invLink, PRIVY_SUBMIT_SUCCESS_NOTES, audit);
			auditTrailLogic.logProcessRequestResponse(trail, GlobalVal.AUDIT_TRAIL_SUBFOLDER_REGISTER, responseContainer.getRequestBody(), responseContainer.getResponseBody(), false, audit);
			
			return new RegisterResponse();
		}
		
		// Handling gagal
		String errorMessage = privyGeneralLogic.buildRegisterErrorMessage(registerResponse, audit);
		
		insertPrivyRegistrationAuditTrail(userData.getUserPhone(), userData.getEmail(), invLink, errorMessage, audit);
		
		Status status = new Status();
		status.setCode(StatusCode.PRIVY_ERROR);
		status.setMessage(errorMessage);
		
		RegisterResponse response = new RegisterResponse();
		response.setStatus(status);
		return response;
		
	}
	
	private RegisterResponse registerVida(UserBean userData, TrInvitationLink invLink, RegistrationType registrationType, MsLov lovUserType, AuditContext audit) {
		
		MsTenant tenant = invLink.getMsTenant();
		MsVendor vendor = invLink.getMsVendor();
		
		MsTenantSettings pnbpSetting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_USE_PNBP_REGISTER_VIDA);
		boolean usePnbp = pnbpSetting != null && "1".equals(pnbpSetting.getSettingValue());
		
		userValidatorLogic.validateRegistrationAttemptAmount(invLink, audit);
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_VRF, tenant, vendor, audit);
		if(usePnbp) {
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_PNBP, tenant, vendor, audit);	
		}
		
		Date consentTime = vidaLogic.getConsentTime();
		
		// Reserve trx no
		long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UVRF);
		
		TrBalanceMutation balanceMutation = new TrBalanceMutation();
		balanceMutation.setTrxNo(reservedTrxNo);
		balanceMutation.setTrxDate(new Date());
		balanceMutation.setQty(0);
		balanceMutation.setMsLovByLovBalanceType(balanceType);
		balanceMutation.setMsLovByLovTrxType(trxType);
		balanceMutation.setUsrCrt(userData.getUserPhone());
		balanceMutation.setDtmCrt(new Date());
		balanceMutation.setMsTenant(tenant);
		balanceMutation.setMsVendor(vendor);
		balanceMutation.setRefNo(invLink.getRefNumber());
		balanceMutation.setMsOffice(invLink.getMsOffice());
		balanceMutation.setMsBusinessLine(invLink.getMsBusinessLine());
		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(balanceMutation);
		
		VidaRegisterResponseContainer responseContainer = vidaLogic.register(userData, tenant, consentTime, reservedTrxNo, audit);
		VidaRegisterResponse registerResponse = responseContainer.getResponse();
		
		String errorMessage = vidaLogic.getErrorRegisterMessage(registerResponse, audit);
		if (StringUtils.isNotBlank(errorMessage)) {
			
			if (errorMessage.contains("NIK is invalid or not registered in local authoritative source")
					|| errorMessage.contains("NIK tidak ditemukan, silahkan yang bersangkutan melakukan validasi ulang ke sumber otoritatif setempat")
					|| errorMessage.contains("Kuota habis untuk NIK")) {
				
				balanceMutation.setQty(-1);
				
				// Potong saldo PNBP
				if(usePnbp) {
					MsLov uPnbpBalType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_PNBP);
					MsLov uPnbpTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UPNBP);
					
					long pnbpTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
					TrBalanceMutation pnbpBalmut = new TrBalanceMutation();
					pnbpBalmut.setTrxNo(String.valueOf(pnbpTrxNo));
					pnbpBalmut.setTrxDate(new Date());
					pnbpBalmut.setQty(-1);
					pnbpBalmut.setMsLovByLovBalanceType(uPnbpBalType);
					pnbpBalmut.setMsLovByLovTrxType(uPnbpTrxType);
					pnbpBalmut.setUsrCrt(userData.getUserPhone());
					pnbpBalmut.setDtmCrt(new Date());
					pnbpBalmut.setMsTenant(tenant);
					pnbpBalmut.setMsVendor(vendor);
					pnbpBalmut.setNotes(StringUtils.left(errorMessage, 200));
					pnbpBalmut.setRefNo(invLink.getRefNumber());
					pnbpBalmut.setMsOffice(invLink.getMsOffice());
					pnbpBalmut.setMsBusinessLine(invLink.getMsBusinessLine());
					daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(pnbpBalmut);	
				}
			}
			
			balanceMutation.setUsrUpd(userData.getUserPhone());
			balanceMutation.setDtmUpd(new Date());
			balanceMutation.setNotes(StringUtils.left(errorMessage, 200));
			daoFactory.getBalanceMutationDao().updateTrBalanceMutationNewTran(balanceMutation);
			
			insertRegistrationAuditTrail(userData.getUserPhone(), userData.getEmail(), null, invLink, errorMessage, audit);
			
			Status status = new Status();
			status.setCode(StatusCode.VIDA_ERROR);
			status.setMessage(errorMessage);
			RegisterResponse response = new RegisterResponse();
			response.setStatus(status);
			return response;
		}
		
		if (registerResponse.getData().getCertificateIssued() == 200) {
			
//			livenessLogic.callAsyncLiveness(reservedTrxNo, balanceMutation.getTrxDate(), userData.getSelfPhoto());
			
			AmMsuser insertedUser = vidaLogic.insertRegisteredUser(registerResponse, userData, tenant, registrationType, lovUserType, audit);
			
			String balanceMutationNotes = VERIF_SUCCESS_NOTES;
			
			balanceMutation.setQty(-1);
			balanceMutation.setUsrUpd(userData.getUserPhone());
			balanceMutation.setDtmUpd(new Date());
			balanceMutation.setAmMsuser(insertedUser);
			balanceMutation.setNotes(balanceMutationNotes);
			balanceMutation.setVendorTrxNo(registerResponse.getData().getEventId());
			daoFactory.getBalanceMutationDao().updateTrBalanceMutationNewTran(balanceMutation);
			
			if(usePnbp) {
				MsLov uPnbpBalType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_PNBP);
				MsLov uPnbpTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UPNBP);
				
				long pnbpTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
				TrBalanceMutation pnbpBalmut = new TrBalanceMutation();
				pnbpBalmut.setTrxNo(String.valueOf(pnbpTrxNo));
				pnbpBalmut.setTrxDate(new Date());
				pnbpBalmut.setQty(-1);
				pnbpBalmut.setMsLovByLovBalanceType(uPnbpBalType);
				pnbpBalmut.setMsLovByLovTrxType(uPnbpTrxType);
				pnbpBalmut.setUsrCrt(userData.getUserPhone());
				pnbpBalmut.setDtmCrt(new Date());
				pnbpBalmut.setMsTenant(tenant);
				pnbpBalmut.setMsVendor(vendor);
				pnbpBalmut.setAmMsuser(insertedUser);
				pnbpBalmut.setNotes(balanceMutationNotes);
				pnbpBalmut.setVendorTrxNo(registerResponse.getData().getEventId());
				pnbpBalmut.setRefNo(invLink.getRefNumber());
				pnbpBalmut.setMsOffice(invLink.getMsOffice());
				pnbpBalmut.setMsBusinessLine(invLink.getMsBusinessLine());
				daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(pnbpBalmut);
			}
			
			invLink.setAmMsuser(insertedUser);
			invLink.setUsrUpd(audit.getCallerId());
			invLink.setDtmUpd(new Date());
			daoFactory.getInvitationLinkDao().updateInvitationLinkNewTran(invLink);
			
			TrSigningProcessAuditTrail trail = insertRegistrationAuditTrail(userData.getUserPhone(), userData.getEmail(), insertedUser, invLink, balanceMutationNotes, audit);
			auditTrailLogic.logProcessRequestResponse(trail, GlobalVal.AUDIT_TRAIL_SUBFOLDER_REGISTER, responseContainer.getRequestBody(), responseContainer.getResponseBody(), false, audit);
			return new RegisterResponse();
		}
		
		// Handling khusus kalau liveness failed
		if (vidaLogic.isRegisterLivenessFailed(registerResponse, audit)) {
			
//			livenessLogic.callAsyncLiveness(reservedTrxNo, balanceMutation.getTrxDate(), userData.getSelfPhoto());
			
			String message = getMessage("businesslogic.vida.livenessfailed", null, audit);
			
			balanceMutation.setQty(-1);
			balanceMutation.setUsrUpd(userData.getUserPhone());
			balanceMutation.setDtmUpd(new Date());
			balanceMutation.setNotes(StringUtils.left(message, 200));
			balanceMutation.setVendorTrxNo(registerResponse.getData().getEventId());
			daoFactory.getBalanceMutationDao().updateTrBalanceMutationNewTran(balanceMutation);
			
			insertRegistrationAuditTrail(userData.getUserPhone(), userData.getEmail(), null, invLink, message, audit);
			
			Status status = new Status();
			status.setCode(StatusCode.VIDA_ERROR);
			status.setMessage(message);
			RegisterResponse response = new RegisterResponse();
			response.setStatus(status);
			return response;
		}
		
//		livenessLogic.callAsyncLiveness(reservedTrxNo, balanceMutation.getTrxDate(), userData.getSelfPhoto());
		
		// Handling verifikasi gagal
		String message = vidaLogic.getFailedRegisterMessage(registerResponse, audit);
		
		balanceMutation.setQty(-1);
		balanceMutation.setUsrUpd(userData.getUserPhone());
		balanceMutation.setDtmUpd(new Date());
		balanceMutation.setNotes(StringUtils.left(message, 200));
		balanceMutation.setVendorTrxNo(registerResponse.getData().getEventId());
		daoFactory.getBalanceMutationDao().updateTrBalanceMutationNewTran(balanceMutation);
		
		if(usePnbp) {
			MsLov uPnbpBalType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_PNBP);
			MsLov uPnbpTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UPNBP);
			
			long pnbpTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
			TrBalanceMutation pnbpBalmut = new TrBalanceMutation();
			pnbpBalmut.setTrxNo(String.valueOf(pnbpTrxNo));
			pnbpBalmut.setTrxDate(new Date());
			pnbpBalmut.setQty(-1);
			pnbpBalmut.setMsLovByLovBalanceType(uPnbpBalType);
			pnbpBalmut.setMsLovByLovTrxType(uPnbpTrxType);
			pnbpBalmut.setUsrCrt(userData.getUserPhone());
			pnbpBalmut.setDtmCrt(new Date());
			pnbpBalmut.setMsTenant(tenant);
			pnbpBalmut.setMsVendor(vendor);
			pnbpBalmut.setNotes(StringUtils.left(message, 200));
			pnbpBalmut.setVendorTrxNo(registerResponse.getData().getEventId());
			pnbpBalmut.setRefNo(invLink.getRefNumber());
			pnbpBalmut.setMsOffice(invLink.getMsOffice());
			pnbpBalmut.setMsBusinessLine(invLink.getMsBusinessLine());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(pnbpBalmut);
		}
		
		insertRegistrationAuditTrail(userData.getUserPhone(), userData.getEmail(), null, invLink, message, audit);
		
		Status status = new Status();
		status.setCode(StatusCode.VIDA_ERROR);
		status.setMessage(message);
		
		RegisterResponse response = new RegisterResponse();
		response.setStatus(status);
		return response;
	}
	
	private RegisterResponse registerTekenAja(UserBean userData, MsTenant tenant, MsVendor vendor, MsLov lovUserType, AuditContext audit) {
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_VRF, tenant, vendor, audit);
		
		TknajRegisterCekResponse nikCheckResponse = tekenAjaLogic.registerCek(userData.getIdNo(), TekenAjaConstant.REGCEK_ACTION_CHECK_NIK, vendor, tenant, audit);
		
		// USER_EXISTS_CERTIFICATE_EXPIRED
		if (TekenAjaConstant.NIK_CHECK_RESPONSE_CODE_EXISTS_CERT_EXPIRED.equals(nikCheckResponse.getCode()) && TekenAjaConstant.NIK_CHECK_RESPONSE_MSG_EXISTS_CERT_EXPIRED.equals(nikCheckResponse.getMessage())) {
			// Hit ulang API registrasi untuk update certificate
			return callTekenAjaRegisterApi(userData, tenant, vendor, lovUserType, audit);
		}
		
		// USER_EXISTS_VERIFIED
		if (TekenAjaConstant.NIK_CHECK_RESPONSE_CODE_EXISTS_VERIFIED.equals(nikCheckResponse.getCode()) && TekenAjaConstant.NIK_CHECK_RESPONSE_MSG_EXISTS_VERIFIED.equals(nikCheckResponse.getMessage())) {
			return callTekenAjaRegisterApi(userData, tenant, vendor, lovUserType, audit);
		}
		
		// USER_EXISTS_UNVERIFIED (Belum verifikasi email)
		if (TekenAjaConstant.NIK_CHECK_RESPONSE_CODE_EXISTS_UNVERIFIED.equals(nikCheckResponse.getCode()) && TekenAjaConstant.NIK_CHECK_RESPONSE_MSG_EXISTS_UNVERIFIED.equals(nikCheckResponse.getMessage())) {
			tekenAjaLogic.registerCek(userData.getIdNo(), TekenAjaConstant.REGCEK_ACTION_RESEND_EMAIL, vendor, tenant, audit);
			tekenAjaLogic.insertRegisteredUser(userData, lovUserType, vendor, tenant, false, true, audit);
			
			// Return code 0 supaya tidak muncul error di FE
			Status status = new Status();
			status.setCode(0);
			status.setMessage(getMessage("businesslogic.tekenaja.pleaseverifyemail", null, audit));
			
			RegisterResponse response = new RegisterResponse();
			response.setStatus(status);
			return response;
		}
		
		return callTekenAjaRegisterApi(userData, tenant, vendor, lovUserType, audit);
	}
	
	private RegisterResponse callTekenAjaRegisterApi(UserBean userData, MsTenant tenant, MsVendor vendor, MsLov lovUserType, AuditContext audit) {
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UVRF);
		
		try {
			TekenAjaRegisterApiResponse registerResponse = tekenAjaLogic.registerUser(userData, vendor, tenant, audit);
			
			if ("CERTIFICATE_RENEWED".equals(registerResponse.getCode())) {
				
				AmMsuser user = tekenAjaLogic.insertRegisteredUser(userData, lovUserType, vendor, tenant, false, true, audit);
				
				TrBalanceMutation mutation = new TrBalanceMutation();
				mutation.setTrxNo(trxNo);
				mutation.setTrxDate(new Date());
				mutation.setQty(-1);
				mutation.setMsLovByLovTrxType(trxType);
				mutation.setMsLovByLovBalanceType(balanceType);
				mutation.setUsrCrt(userData.getUserPhone());
				mutation.setDtmCrt(new Date());
				mutation.setMsTenant(tenant);
				mutation.setMsVendor(vendor);
				mutation.setAmMsuser(user);
				mutation.setNotes(userData.getUserPhone());
				mutation.setVendorTrxNo(registerResponse.getRefId());
				daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
				
				Status status = new Status();
				status.setMessage(getMessage("businesslogic.tekenaja.certificaterenewed", null, audit));
				
				RegisterResponse response = new RegisterResponse();
				response.setStatus(status);
				return response;
			}
			
			if ("OK".equals(registerResponse.getStatus()) || "Pengguna telah terdaftar".equals(registerResponse.getMessage())) {
				AmMsuser user = tekenAjaLogic.insertRegisteredUser(userData, lovUserType, vendor, tenant, false, false, audit);
				
				TrBalanceMutation mutation = new TrBalanceMutation();
				mutation.setTrxNo(trxNo);
				mutation.setTrxDate(new Date());
				mutation.setQty(-1);
				mutation.setMsLovByLovTrxType(trxType);
				mutation.setMsLovByLovBalanceType(balanceType);
				mutation.setUsrCrt(userData.getUserPhone());
				mutation.setDtmCrt(new Date());
				mutation.setMsTenant(tenant);
				mutation.setMsVendor(vendor);
				mutation.setAmMsuser(user);
				mutation.setNotes(userData.getUserPhone());
				mutation.setVendorTrxNo(registerResponse.getRefId());
				daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
				
				return new RegisterResponse();
			}
			
			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(trxNo);
			mutation.setTrxDate(new Date());
			mutation.setQty(-1);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setUsrCrt(userData.getUserPhone());
			mutation.setDtmCrt(new Date());
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setNotes(userData.getUserPhone());
			mutation.setVendorTrxNo(registerResponse.getRefId());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
			
			Status status = new Status();
			status.setCode(StatusCode.TEKEN_AJA_ERROR);
			status.setMessage(tekenAjaLogic.buildRegisterErrorMessage(registerResponse));
			
			RegisterResponse response = new RegisterResponse();
			response.setStatus(status);
			return response;
			
		} catch (Exception e) {
			
			LOG.error("Register TekenAja exception occurred: {}", e.getLocalizedMessage(), e);
			
			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(trxNo);
			mutation.setTrxDate(new Date());
			mutation.setQty(-1);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setUsrCrt(userData.getUserPhone());
			mutation.setDtmCrt(new Date());
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setNotes(userData.getUserPhone());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
			
			Status status = new Status();
			status.setCode(StatusCode.TEKEN_AJA_ERROR);
			status.setMessage(e.getLocalizedMessage());
			
			RegisterResponse response = new RegisterResponse();
			response.setStatus(status);
			return response;
		}
		
	}
	
	private RegisterResponse registerBasedOnVendor(UserBean userData, TrInvitationLink invLink, RegistrationType registrationType, MsLov lovUserType, AuditContext audit) {
		
		MsTenant tenant = invLink.getMsTenant();
		MsVendor vendor = invLink.getMsVendor();
		
		if (RegistrationType.INVITATION_SMS == registrationType) {
			CreateSingleEmailRequest emailRequest = new CreateSingleEmailRequest(tenant, userData.getUserName(), userData.getUserDob(), userData.getIdNo());
			CreateSingleEmailResponse emailResponse = emailLogic.createEmail(emailRequest, audit);
			userData.setEmail(emailResponse.getEmail());
			userData.setIdEmailHosting(emailResponse.getIdEmailHosting());
		}
		
		if (GlobalVal.VENDOR_CODE_DIGISIGN.equals(vendor.getVendorCode())) {
			DigisignRegisterRequest digisignRequest = new DigisignRegisterRequest();
			digisignRequest.setUserData(userData);
			digisignRequest.setTenantCode(tenant.getTenantCode());
			digisignRequest.setVendorCode(vendor.getVendorCode());
			return userLogic.registerDigisign(digisignRequest, registrationType, lovUserType, audit);
		}
		
		if (GlobalVal.VENDOR_CODE_VIDA.equals(vendor.getVendorCode())) {
			return registerVida(userData, invLink, registrationType, lovUserType, audit);
		}
		
		if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {
			if ("1".equals(invLink.getUseSyncPrivyVerif())) {
				return registerPrivySynchronous(userData, invLink, registrationType, lovUserType, audit);
			}
			return registerPrivy(userData, invLink, lovUserType, audit);
		}
		
		if (GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendor.getVendorCode())) {
			return registerTekenAja(userData, tenant, vendor, lovUserType, audit);
		}
		
		throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_UNHANDLED_REGIS_VENDOR,
				new String[] {vendor.getVendorName()}, audit), ReasonUser.VENDOR_NOT_FOUND);
	}
	
	private void setPsreMandatoryParamForInvitation(RegistrationByInvitationRequest request, MsTenant tenant, MsVendor vendor) {
		boolean useMandatoryParam = userValidatorLogic.usePsreMandatoryParamOnly(tenant);
		if (!useMandatoryParam) {
			return;
		}
		
		if (GlobalVal.VENDOR_CODE_VIDA.equals(vendor.getVendorCode())) {
			request.getUserData().setUserPob(null);
			request.getUserData().setUserGender(null);
			request.getUserData().setUserAddress(null);
			request.getUserData().setKecamatan(null);
			request.getUserData().setKelurahan(null);
			request.getUserData().setKota(null);
			request.getUserData().setProvinsi(null);
			request.getUserData().setZipcode(null);
			request.getUserData().setIdPhoto(null);
		}
		
		if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {
			
			request.getUserData().setUserPob(null);
			request.getUserData().setUserGender(null);
			request.getUserData().setUserAddress(null);
			request.getUserData().setKecamatan(null);
			request.getUserData().setKelurahan(null);
			request.getUserData().setKota(null);
			request.getUserData().setProvinsi(null);
			request.getUserData().setZipcode(null);
			
		}
	}

	@Override
	public RegisterResponse registerByInvitation(RegistrationByInvitationRequest request, AuditContext audit) {
		
		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		TrInvitationLink invLink = invLinkValidatorLogic.validateGetInvitationLinkNewTran(invCode, audit);
		userValidatorLogic.validateRegisterUserBean(request.getUserData(), audit);
		
		MsVendor vendor = invLink.getMsVendor();
		MsTenant tenant = invLink.getMsTenant();
		
		if (invLink.getLastRegisterAttempt() == null || !DateUtils.isSameDay(invLink.getLastRegisterAttempt(), new Date())) {
			invLink.setDailyRegisterAttemptAmount((short) 1);
		} else {
			short currentCount = invLink.getDailyRegisterAttemptAmount();
			currentCount += 1;
			invLink.setDailyRegisterAttemptAmount(currentCount);
		}
		invLink.setLastRegisterAttempt(new Date());
		daoFactory.getInvitationLinkDao().updateInvitationLinkNewTran(invLink);
		
		UserBean userData = request.getUserData();
		// Parameter foto di request Privy butuh prefix data:image/jpeg;base64, sehingga diexclude disini
		if (!GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {
			userData.setIdPhoto(MssTool.cutImageStringPrefix(userData.getIdPhoto()));
			userData.setSelfPhoto(MssTool.cutImageStringPrefix(userData.getSelfPhoto()));
		}
		
		setPsreMandatoryParamForInvitation(request, tenant, vendor);
		
		RegistrationType registrationType = GlobalVal.INV_BY_SMS.equals(invLink.getInvitationBy()) ? RegistrationType.INVITATION_SMS : RegistrationType.INVITATION_EMAIL;
		
		if (!emailPhoneRegisteredInVendor(userData.getEmail(), userData.getUserPhone(), vendor.getVendorCode(),request.getUserData().getIdNo(), audit)) {
			return registerBasedOnVendor(userData, invLink, registrationType, invLink.getLovUserType(), audit);
		}
		
		if (!"1".equals(vendor.getReregistAvailable())) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_ALREADY_REGISTERED,
					new String[] { vendor.getVendorName() }, audit), ReasonUser.ALREADY_REGISTERED);
		}
		
		if (!userCanReregister(userData.getIdNo(), userData.getUserPhone(), userData.getEmail(), vendor.getVendorCode())) {
			RegisterResponse response = new RegisterResponse();
			response.setReregistrationAvailable("1");
			return response;
		}
		
		return registerBasedOnVendor(userData, invLink, registrationType, invLink.getLovUserType(), audit);
	}
	
	private RegisterExternalResponse registerExternalVida(RegisterExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		
		MsTenantSettings pnbpSetting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_USE_PNBP_REGISTER_VIDA);
		boolean usePnbp = pnbpSetting != null && "1".equals(pnbpSetting.getSettingValue());
		
		// userValidatorLogic.validateRegistrationAttemptAmount(vendor, tenant, request.getTlp(), audit);
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_VRF, tenant, vendor, audit);
		if(usePnbp) {
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_PNBP, tenant, vendor, audit);	
		}
		
		Date consentTime = vidaLogic.getConsentTime();
		
		// Reserve trx no
		long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UVRF);
		
		TrBalanceMutation balanceMutation = new TrBalanceMutation();
		balanceMutation.setTrxNo(reservedTrxNo);
		balanceMutation.setTrxDate(new Date());
		balanceMutation.setQty(0);
		balanceMutation.setMsLovByLovBalanceType(balanceType);
		balanceMutation.setMsLovByLovTrxType(trxType);
		balanceMutation.setUsrCrt(request.getTlp());
		balanceMutation.setDtmCrt(new Date());
		balanceMutation.setMsTenant(tenant);
		balanceMutation.setMsVendor(vendor);
		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(balanceMutation);
		
		VidaRegisterResponseContainer responseContainer = vidaLogic.registerExternal(request, tenant, consentTime, reservedTrxNo, audit);
		VidaRegisterResponse registerResponse = responseContainer.getResponse();
		
		String errorMessage = vidaLogic.getErrorRegisterMessage(registerResponse, audit);
		if (StringUtils.isNotBlank(errorMessage)) {
			
			if (errorMessage.contains("NIK is invalid or not registered in local authoritative source")
					|| errorMessage.contains("NIK tidak ditemukan, silahkan yang bersangkutan melakukan validasi ulang ke sumber otoritatif setempat")
					|| errorMessage.contains("Kuota habis untuk NIK")) {
				
				// Potong saldo verifikasi
				balanceMutation.setQty(-1);
				
				// Potong saldo PNBP
				long pnbpTrxNo = 0;
				if(usePnbp) {
					MsLov uPnbpBalType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_PNBP);
					MsLov uPnbpTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UPNBP);
					
					pnbpTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
					TrBalanceMutation pnbpBalmut = new TrBalanceMutation();
					pnbpBalmut.setTrxNo(String.valueOf(pnbpTrxNo));
					pnbpBalmut.setTrxDate(new Date());
					pnbpBalmut.setQty(-1);
					pnbpBalmut.setMsLovByLovBalanceType(uPnbpBalType);
					pnbpBalmut.setMsLovByLovTrxType(uPnbpTrxType);
					pnbpBalmut.setUsrCrt(request.getTlp());
					pnbpBalmut.setDtmCrt(new Date());
					pnbpBalmut.setMsTenant(tenant);
					pnbpBalmut.setMsVendor(vendor);
					pnbpBalmut.setNotes(StringUtils.left(errorMessage, 200));
					daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(pnbpBalmut);
				}
				insertRegistrationAuditTrail(request.getTlp(), request.getEmail(), null, tenant, vendor, errorMessage, audit);
				
				Status status = new Status();
				status.setCode(StatusCode.VIDA_ERROR);
				status.setMessage(errorMessage);
				
				List<String> trxNos = new ArrayList<>();
				trxNos.add(reservedTrxNo);
				if(usePnbp) {
					trxNos.add(String.valueOf(pnbpTrxNo));
				}
				
				RegisterVerificationStatusBean verifStatus = vidaLogic.buildVerificationStatusBean(registerResponse, audit);
				verifStatus.setNik(GlobalVal.NIK_UNREGISTERED);
				
				RegisterExternalResponse response = new RegisterExternalResponse();
				response.setStatus(status);
				response.setTrxNo(trxNos);
				response.setVerifStatus(false);
				response.setResults(verifStatus);
				response.setPsreCode(vendor.getVendorCode());
				return response;
			}
			
			balanceMutation.setUsrUpd(request.getTlp());
			balanceMutation.setDtmUpd(new Date());
			balanceMutation.setNotes(StringUtils.left(errorMessage, 200));
			daoFactory.getBalanceMutationDao().updateTrBalanceMutationNewTran(balanceMutation);
			
			insertRegistrationAuditTrail(request.getTlp(), request.getEmail(), null, tenant, vendor, errorMessage, audit);
			
			Status status = new Status();
			status.setCode(StatusCode.VIDA_ERROR);
			status.setMessage(errorMessage);
			
			RegisterVerificationStatusBean verifStatus = vidaLogic.buildVerificationStatusBean(registerResponse, audit);
			
			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setVerifStatus(false);
			response.setStatus(status);
			response.setResults(verifStatus);
			response.setPsreCode(vendor.getVendorCode());
			return response;
		}
		
		if (registerResponse.getData().getCertificateIssued() == 200) {
			AmMsuser insertedUser = vidaLogic.insertExternalRegisteredUser(registerResponse, request, tenant, audit);
			
			String balanceMutationNotes = VERIF_SUCCESS_NOTES;
			
			balanceMutation.setQty(-1);
			balanceMutation.setUsrUpd(request.getTlp());
			balanceMutation.setDtmUpd(new Date());
			balanceMutation.setAmMsuser(insertedUser);
			balanceMutation.setNotes(balanceMutationNotes);
			balanceMutation.setVendorTrxNo(registerResponse.getData().getEventId());
			daoFactory.getBalanceMutationDao().updateTrBalanceMutationNewTran(balanceMutation);
			
			long pnbpTrxNo = 0;
			if(usePnbp) {
				MsLov uPnbpBalType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_PNBP);
				MsLov uPnbpTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UPNBP);
				
				pnbpTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
				TrBalanceMutation pnbpBalmut = new TrBalanceMutation();
				pnbpBalmut.setTrxNo(String.valueOf(pnbpTrxNo));
				pnbpBalmut.setTrxDate(new Date());
				pnbpBalmut.setQty(-1);
				pnbpBalmut.setMsLovByLovBalanceType(uPnbpBalType);
				pnbpBalmut.setMsLovByLovTrxType(uPnbpTrxType);
				pnbpBalmut.setUsrCrt(request.getTlp());
				pnbpBalmut.setDtmCrt(new Date());
				pnbpBalmut.setMsTenant(tenant);
				pnbpBalmut.setMsVendor(vendor);
				pnbpBalmut.setAmMsuser(insertedUser);
				pnbpBalmut.setNotes(balanceMutationNotes);
				pnbpBalmut.setVendorTrxNo(registerResponse.getData().getEventId());
				daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(pnbpBalmut);
			}
			
			TrSigningProcessAuditTrail trail = insertRegistrationAuditTrail(request.getTlp(), request.getEmail(), insertedUser, tenant, vendor, balanceMutationNotes, audit);
			auditTrailLogic.logProcessRequestResponse(trail, GlobalVal.AUDIT_TRAIL_SUBFOLDER_REGISTER, responseContainer.getRequestBody(), responseContainer.getResponseBody(), false, audit);
			
			List<String> trxNos = new ArrayList<>();
			trxNos.add(reservedTrxNo);
			if(usePnbp) {
				trxNos.add(String.valueOf(pnbpTrxNo));
			}
			
			RegisterVerificationStatusBean verifStatus = vidaLogic.buildVerificationStatusBean(registerResponse, audit);
			
			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setTrxNo(trxNos);
			response.setVerifStatus(true);
			response.setResults(verifStatus);
			if (request.getIdEmailHosting() != null && request.getIdEmailHosting() != 0) {
				response.setEmail(StringUtils.upperCase(request.getEmail()));
			}
			response.setPsreCode(vendor.getVendorCode());
			return response;
		}
		
		// Handling khusus kalau liveness failed
		if (vidaLogic.isRegisterLivenessFailed(registerResponse, audit)) {
			String message = getMessage("businesslogic.vida.livenessfailed", null, audit);
			
			balanceMutation.setQty(-1);
			balanceMutation.setUsrUpd(request.getTlp());
			balanceMutation.setDtmUpd(new Date());
			balanceMutation.setNotes(StringUtils.left(message, 200));
			balanceMutation.setVendorTrxNo(registerResponse.getData().getEventId());
			daoFactory.getBalanceMutationDao().updateTrBalanceMutationNewTran(balanceMutation);
			
			insertRegistrationAuditTrail(request.getTlp(), request.getEmail(), null, tenant, vendor, message, audit);
			
			List<String> trxNos = new ArrayList<>();
			trxNos.add(reservedTrxNo);
			
			Status status = new Status();
			status.setCode(StatusCode.VIDA_ERROR);
			status.setMessage(message);
			
			RegisterVerificationStatusBean verifStatus = vidaLogic.buildVerificationStatusBean(registerResponse, audit);
			
			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setStatus(status);
			response.setTrxNo(trxNos);
			response.setVerifStatus(false);
			response.setResults(verifStatus);
			response.setPsreCode(vendor.getVendorCode());
			return response;
		}
		
		// Handling verifikasi gagal
		String message = vidaLogic.getFailedRegisterMessage(registerResponse, audit);
		
		balanceMutation.setQty(-1);
		balanceMutation.setUsrUpd(request.getTlp());
		balanceMutation.setDtmUpd(new Date());
		balanceMutation.setNotes(StringUtils.left(message, 200));
		balanceMutation.setVendorTrxNo(registerResponse.getData().getEventId());
		daoFactory.getBalanceMutationDao().updateTrBalanceMutationNewTran(balanceMutation);
		
		long pnbpTrxNo = 0;
		if(usePnbp) {
			MsLov uPnbpBalType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_PNBP);
			MsLov uPnbpTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UPNBP);
			
			pnbpTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
			TrBalanceMutation pnbpBalmut = new TrBalanceMutation();
			pnbpBalmut.setTrxNo(String.valueOf(pnbpTrxNo));
			pnbpBalmut.setTrxDate(new Date());
			pnbpBalmut.setQty(-1);
			pnbpBalmut.setMsLovByLovBalanceType(uPnbpBalType);
			pnbpBalmut.setMsLovByLovTrxType(uPnbpTrxType);
			pnbpBalmut.setUsrCrt(request.getTlp());
			pnbpBalmut.setDtmCrt(new Date());
			pnbpBalmut.setMsTenant(tenant);
			pnbpBalmut.setMsVendor(vendor);
			pnbpBalmut.setNotes(StringUtils.left(message, 200));
			pnbpBalmut.setVendorTrxNo(registerResponse.getData().getEventId());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(pnbpBalmut);
		}
		
		insertRegistrationAuditTrail(request.getTlp(), request.getEmail(), null, tenant, vendor, message, audit);
		
		Status status = new Status();
		status.setCode(StatusCode.VIDA_ERROR);
		status.setMessage(message);
		
		List<String> trxNos = new ArrayList<>();
		trxNos.add(reservedTrxNo);
		if(usePnbp) {
			trxNos.add(String.valueOf(pnbpTrxNo));
		}
		
		RegisterVerificationStatusBean verifStatus = vidaLogic.buildVerificationStatusBean(registerResponse, audit);
		
		RegisterExternalResponse response = new RegisterExternalResponse();
		response.setStatus(status);
		response.setTrxNo(trxNos);
		response.setVerifStatus(false);
		response.setResults(verifStatus);
		response.setPsreCode(vendor.getVendorCode());
		return response;
	}
	
	private void validateActiveJobCheckRegisterStatus(String idNo, MsVendor vendor, AuditContext audit) {
		Short failedRequestStatus = (short) 2;
		TrJobCheckRegisterStatus job = daoFactory.getJobCheckRegisterStatusDao().getLatestJobCheckRegStatusByIdNo(idNo, vendor);
		if (null != job && !failedRequestStatus.equals(job.getRequestStatus())) {
			throw new ParameterException(getMessage("businesslogic.register.verificationinprogress", null, audit), ReasonParam.INVALID_CONDITION);
		}
	}

	private RegisterExternalResponse registerExternalPrivySynchronous(RegisterExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit) {

		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_VRF_V2, tenant, vendor, audit);

		MsTenantSettings usernameSetting = tenantSettingsLogic.getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_PRIVY_SYNC_VERIF_USERNAME, true, audit);
		String username = usernameSetting.getSettingValue();

		MsTenantSettings passwordSetting = tenantSettingsLogic.getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_PRIVY_SYNC_VERIF_PASSWORD, true, audit);
		String password = passwordSetting.getSettingValue();

		MsTenantSettings merchantKeySetting = tenantSettingsLogic.getTenantSettings(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_PRIVY_SYNC_VERIF_MERCHANT_KEY, true, audit);
		String merchantKey = merchantKeySetting.getSettingValue();

		PrivyVerifV2CredentialsBean credentials = new PrivyVerifV2CredentialsBean(username, password, merchantKey);

		long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_VRF_V2);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UVRF_V2);

		PrivyRegisterV2ResponseContainer responseContainer = null;
		if (bypassSyncPrivyVerif()) {
			responseContainer = privyLogic.registerV2WithExternalRequestDummy(request, reservedTrxNo, credentials, audit);
		} else {
			responseContainer = privyLogic.registerV2WithExternalRequest(request, reservedTrxNo, credentials, audit);
		}
		
		PrivyRegisterV2Response registerResponse = responseContainer.getResponse();

		if (registerResponse.getCode() == 200
				&& "verified".equalsIgnoreCase(registerResponse.getUserStatus())
				&& SYNC_PRIVY_SUCCESS_REGIS_CODE.equals(registerResponse.getRegistrationStatus())) {
			
			AmMsuser user = privyLogic.insertRegisteredUserV2External(request, registerResponse, tenant, audit);

			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(reservedTrxNo);
			mutation.setTrxDate(new Date());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setUsrCrt(request.getTlp());
			mutation.setDtmCrt(new Date());
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setNotes(VERIF_SUCCESS_NOTES);
			mutation.setAmMsuser(user);
			mutation.setVendorTrxNo(registerResponse.getTransactionId());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);

			TrSigningProcessAuditTrail trail = insertRegistrationAuditTrail(request.getTlp(), request.getEmail(), user, tenant, vendor, VERIF_SUCCESS_NOTES, audit);
			auditTrailLogic.logProcessRequestResponse(trail, GlobalVal.AUDIT_TRAIL_SUBFOLDER_REGISTER, responseContainer.getJsonRequest(), responseContainer.getJsonResponse(), false, audit);

			List<String> trxNos = new ArrayList<>();
			trxNos.add(reservedTrxNo);

			RegisterVerificationStatusBean verifStatus = privyLogic.buildVerificationStatusBean(registerResponse);

			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setTrxNo(trxNos);
			response.setVerifStatus(true);
			response.setResults(verifStatus);
			if (request.getIdEmailHosting() != null && request.getIdEmailHosting() != 0) {
				response.setEmail(StringUtils.upperCase(request.getEmail()));
			}
			response.setPsreCode(vendor.getVendorCode());
			return response;
		}

		if (!SYNC_PRIVY_SUCCESS_REGIS_CODE.equals(registerResponse.getRegistrationStatus())) {

			String errorMessage = getSynchronousPrivyRegisterStatus(registerResponse, audit);
			
			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(reservedTrxNo);
			mutation.setTrxDate(new Date());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setUsrCrt(request.getTlp());
			mutation.setDtmCrt(new Date());
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setNotes(StringUtils.left(errorMessage, 200));
			mutation.setVendorTrxNo(registerResponse.getTransactionId());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);

			insertRegistrationAuditTrail(request.getTlp(), request.getEmail(), null, tenant, vendor, errorMessage, audit);

			Status status = new Status();
			status.setCode(StatusCode.PRIVY_ERROR);
			status.setMessage(errorMessage);

			List<String> trxNos = new ArrayList<>();
			trxNos.add(reservedTrxNo);

			RegisterVerificationStatusBean verifStatus = privyLogic.buildVerificationStatusBean(registerResponse);

			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setStatus(status);
			response.setTrxNo(trxNos);
			response.setVerifStatus(false);
			response.setResults(verifStatus);
			response.setPsreCode(vendor.getVendorCode());
			return response;
		}

		if (StringUtils.isNotBlank(registerResponse.getRejectedCode())) {

			logPrivyVerifV2RejectCode(registerResponse);

			String errorMessage = "Verifikasi Gagal. Nama, Tanggal Lahir, atau Foto Diri tidak sesuai. Harap cek kembali Nama dan Tanggal Lahir Anda serta mengambil ulang Foto Diri.";

			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(reservedTrxNo);
			mutation.setTrxDate(new Date());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setUsrCrt(request.getTlp());
			mutation.setDtmCrt(new Date());
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setNotes(StringUtils.left(errorMessage, 200));
			mutation.setVendorTrxNo(registerResponse.getTransactionId());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);

			insertRegistrationAuditTrail(request.getTlp(), request.getEmail(), null, tenant, vendor, errorMessage, audit);

			Status status = new Status();
			status.setCode(StatusCode.PRIVY_ERROR);
			status.setMessage(errorMessage);

			List<String> trxNos = new ArrayList<>();
			trxNos.add(reservedTrxNo);

			RegisterVerificationStatusBean verifStatus = privyLogic.buildVerificationStatusBean(registerResponse);

			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setStatus(status);
			response.setTrxNo(trxNos);
			response.setVerifStatus(false);
			response.setResults(verifStatus);
			response.setPsreCode(vendor.getVendorCode());
			return response;
		}

		String errorMessage = registerResponse.getMessage();

		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(reservedTrxNo);
		mutation.setTrxDate(new Date());
		mutation.setQty(0);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setUsrCrt(request.getTlp());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(tenant);
		mutation.setMsVendor(vendor);
		mutation.setNotes(StringUtils.left(errorMessage, 200));
		mutation.setVendorTrxNo(registerResponse.getTransactionId());
		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);

		insertRegistrationAuditTrail(request.getTlp(), request.getEmail(), null, tenant, vendor, errorMessage, audit);

		Status status = new Status();
		status.setCode(StatusCode.PRIVY_ERROR);
		status.setMessage(errorMessage);

		RegisterVerificationStatusBean verifStatus = privyLogic.buildVerificationStatusBean(registerResponse);

		RegisterExternalResponse response = new RegisterExternalResponse();
		response.setStatus(status);
		response.setTrxNo(new ArrayList<>());
		response.setVerifStatus(false);
		response.setResults(verifStatus);
		response.setPsreCode(vendor.getVendorCode());
		return response;
	}
	
	private RegisterExternalResponse registerExternalPrivy(RegisterExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		
		validateActiveJobCheckRegisterStatus(request.getIdKtp(), vendor, audit);
		
		// Validasi jumlah saldo Verifikasi Privy
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_VRF, tenant, vendor, audit);
		
		if (!request.getIdPhoto().startsWith(GlobalVal.IMG_PNG_PREFIX) && !request.getIdPhoto().startsWith(GlobalVal.IMG_JPEG_PREFIX) && !request.getIdPhoto().startsWith(GlobalVal.IMG_JPG_PREFIX)) {
			request.setIdPhoto(GlobalVal.IMG_JPEG_PREFIX + request.getIdPhoto());
		}
		
		if (!request.getSelfPhoto().startsWith(GlobalVal.IMG_PNG_PREFIX) && !request.getSelfPhoto().startsWith(GlobalVal.IMG_JPEG_PREFIX) && !request.getSelfPhoto().startsWith(GlobalVal.IMG_JPG_PREFIX)) {
			request.setSelfPhoto(GlobalVal.IMG_JPEG_PREFIX + request.getSelfPhoto());
		}
		
		// Prepare trx no untuk request ID privy
		long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UVRF);
		
		PrivyGeneralRegisterResponseContainer responseContainer = privyGeneralLogic.registerExternal(request, reservedTrxNo, tenant, vendor, audit);
		PrivyGeneralRegisterResponse registerResponse = responseContainer.getRegisterResponse();
				
		// Registrasi berhasil, menunggu verifikasi
		if (PRIVY_SUBMIT_SUCCESS_NOTES.equals(registerResponse.getMessage())) {
			
			TrBalanceMutation balanceMutation = new TrBalanceMutation();
			balanceMutation.setTrxNo(reservedTrxNo);
			balanceMutation.setTrxDate(new Date());
			balanceMutation.setQty(-1);
			balanceMutation.setMsLovByLovBalanceType(balanceType);
			balanceMutation.setMsLovByLovTrxType(trxType);
			balanceMutation.setUsrCrt(request.getTlp());
			balanceMutation.setDtmCrt(new Date());
			balanceMutation.setMsTenant(tenant);
			balanceMutation.setMsVendor(vendor);
			balanceMutation.setNotes(registerResponse.getData().getStatus());
			balanceMutation.setVendorTrxNo(registerResponse.getData().getRegisterToken());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(balanceMutation);
			
			TrJobCheckRegisterStatus job = new TrJobCheckRegisterStatus();
			job.setTrBalanceMutation(balanceMutation);
			job.setHashedIdNo(MssTool.getHashedString(request.getIdKtp()));
			job.setRequestStatus((short) 0);
			job.setIsExternal("1");
			job.setUsrCrt(audit.getCallerId());
			job.setDtmCrt(new Date());
			daoFactory.getJobCheckRegisterStatusDao().insertJobCheckRegStatusNewTrx(job);
			
			userValidatorLogic.removeUnnecessaryRegisterExternalParam(request, tenant);
			String jsonRequest = gson.toJson(request);
			cloudStorageLogic.storeRegisterRequest(job, jsonRequest);
			
			TrSigningProcessAuditTrail trail = insertPrivyRegistrationAuditTrail(request.getTlp(), request.getEmail(), tenant, vendor,  PRIVY_SUBMIT_SUCCESS_NOTES, audit);
			auditTrailLogic.logProcessRequestResponse(trail, GlobalVal.AUDIT_TRAIL_SUBFOLDER_REGISTER, responseContainer.getRequestBody(), responseContainer.getResponseBody(), false, audit);
			
			RegisterExternalResponse response = new RegisterExternalResponse();
			List<String> trxNos = new ArrayList<>();
			trxNos.add(reservedTrxNo);
			response.setTrxNo(trxNos);
			response.setResults(new RegisterVerificationStatusBean());
			if (request.getIdEmailHosting() != null && request.getIdEmailHosting() != 0) {
				response.setEmail(StringUtils.upperCase(request.getEmail()));
			}
			response.setPsreCode(vendor.getVendorCode());
			return response;
			
		}
		
		// Handling gagal lainnya
		String errorMessage = privyGeneralLogic.buildRegisterErrorMessage(registerResponse, audit);
		insertPrivyRegistrationAuditTrail(request.getTlp(), request.getEmail(), tenant, vendor, errorMessage, audit);
		
		RegisterExternalResponse response = new RegisterExternalResponse();
		response.setResults(new RegisterVerificationStatusBean());
		response.setVerifStatus(false);
		
		Status status = new Status();
		status.setCode(StatusCode.PRIVY_ERROR);
		status.setMessage(errorMessage);
		response.setStatus(status);
		response.setPsreCode(vendor.getVendorCode());
		return response;
		
	}
	
	private RegisterExternalResponse registerExternalDigisign(RegisterExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_TEXT_VRF, tenant, vendor, audit);
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_VRF, tenant, vendor, audit);
		
		RegisterDigisignResponseBean registerResponse = digisignLogic.registerForExternalRequest(request, tenant, vendor, audit);
		
		// Kalau sudah terdaftar, tapi pakai email lain
		if (GenericDigisignLogic.DIGI_ERR_MSG_NIK_USED.equals(registerResponse.getNotif())) {
			
			LOG.info("Changing Digisign email from {} to {}", request.getEmail(), registerResponse.getEmailRegistered());
			request.setEmail(registerResponse.getEmailRegistered());
			request.setIdEmailHosting(null);
			
			digisignLogic.registerForExternalRequest(request, tenant, vendor, audit);
			digisignLogic.insertRegisteredUserExternal(request, true, tenant, vendor, audit);
			
			String message = getMessage("businesslogic.user.nikalreadyregistered",
					new String[] { request.getIdKtp(), registerResponse.getEmailRegistered() }, audit);
			
			Status status = new Status();
			status.setMessage(message);
			
			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setStatus(status);
			response.setEmail(StringUtils.upperCase(registerResponse.getEmailRegistered()));
			response.setPsreCode(vendor.getVendorCode());
			return response;
		}
		
		// Kalau sudah terdaftar dan aktivasi
		if (GenericDigisignLogic.DIGI_REGISTERED_MSG.equals(registerResponse.getNotif())) {
			
			digisignLogic.insertRegisteredUserExternal(request, true, tenant, vendor, audit);
			
			String message = getMessage("businesslogic.user.alreadyactivated", null, audit);
			Status status = new Status();
			status.setMessage(message);
			
			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setStatus(status);
			response.setPsreCode(vendor.getVendorCode());
			return response;
		}
		
		// Kalau sebelumnya sudah registrasi tapi belum aktivasi
		if (GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(registerResponse.getResult()) && "Email sudah terdaftar, namun belum melakukan aktivasi. Silahkan untuk melakukan aktivasi sebelum data dihapus dari daftar aktivasi.".equals(registerResponse.getNotif())) {
			
			digisignLogic.insertRegisteredUserExternal(request, false, tenant, vendor, audit);
			
			Status status = new Status();
			status.setCode(0);
			status.setMessage(getMessage("businesslogic.digisign.registerednotactivated", null, audit));

			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setStatus(status);
			response.setPsreCode(vendor.getVendorCode());
			return response;
		}
		
		// Registrasi berhasil, perlu aktivasi
		if (GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(registerResponse.getResult())) {
			
			AmMsuser user = digisignLogic.insertRegisteredUserExternal(request, false, tenant, vendor, audit);
			
			// Potong verif
			MsLov verifBalType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
			MsLov verifTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UVRF);
			long verifTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
			
			TrBalanceMutation verifBalMut = new TrBalanceMutation();
			verifBalMut.setTrxNo(String.valueOf(verifTrxNo));
			verifBalMut.setTrxDate(new Date());
			verifBalMut.setQty(-1);
			verifBalMut.setMsLovByLovBalanceType(verifBalType);
			verifBalMut.setMsLovByLovTrxType(verifTrxType);
			verifBalMut.setUsrCrt(request.getTlp());
			verifBalMut.setAmMsuser(user);
			verifBalMut.setDtmCrt(new Date());
			verifBalMut.setMsTenant(tenant);
			verifBalMut.setMsVendor(vendor);
			verifBalMut.setNotes(VERIF_SUCCESS_NOTES);
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(verifBalMut);
			
			// Potong text verif
			MsLov textVerifBalType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_TEXT_VRF);
			MsLov textVerifTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UTEXT_VRF);
			long textVerifTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
			
			TrBalanceMutation textVerifBalMut = new TrBalanceMutation();
			textVerifBalMut.setTrxNo(String.valueOf(textVerifTrxNo));
			textVerifBalMut.setTrxDate(new Date());
			textVerifBalMut.setQty(-1);
			textVerifBalMut.setMsLovByLovBalanceType(textVerifBalType);
			textVerifBalMut.setMsLovByLovTrxType(textVerifTrxType);
			textVerifBalMut.setUsrCrt(request.getTlp());
			textVerifBalMut.setAmMsuser(user);
			textVerifBalMut.setDtmCrt(new Date());
			textVerifBalMut.setMsTenant(tenant);
			textVerifBalMut.setMsVendor(vendor);
			textVerifBalMut.setNotes(VERIF_SUCCESS_NOTES);
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(verifBalMut);
			
			List<String> trxNos = new ArrayList<>();
			trxNos.add(String.valueOf(verifTrxNo));
			trxNos.add(String.valueOf(textVerifTrxNo));
			
			RegisterVerificationStatusBean verifResult = digisignLogic.buildVerificationStatusBean(registerResponse, audit);
			
			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setTrxNo(trxNos);
			response.setResults(verifResult);
			response.setVerifStatus(true);
			if (request.getIdEmailHosting() != null && request.getIdEmailHosting() != 0) {
				response.setEmail(StringUtils.upperCase(request.getEmail()));
			}
			response.setPsreCode(vendor.getVendorCode());
			return response;
			
		}	
		
		// Handling gagal yang cuma potong saldo TEXT_VRF
		if ("12".equals(registerResponse.getResult()) && "verifikasi text gagal.".equals(registerResponse.getInfo())) {
			String message = digisignLogic.buildRegisterVerifMessage(registerResponse.getData());
			
			MsLov textVerifBalType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_TEXT_VRF);
			MsLov textVerifTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UTEXT_VRF);
			long textVerifTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
			
			TrBalanceMutation textVerifBalMut = new TrBalanceMutation();
			textVerifBalMut.setTrxNo(String.valueOf(textVerifTrxNo));
			textVerifBalMut.setTrxDate(new Date());
			textVerifBalMut.setQty(-1);
			textVerifBalMut.setMsLovByLovBalanceType(textVerifBalType);
			textVerifBalMut.setMsLovByLovTrxType(textVerifTrxType);
			textVerifBalMut.setUsrCrt(request.getTlp());
			textVerifBalMut.setDtmCrt(new Date());
			textVerifBalMut.setMsTenant(tenant);
			textVerifBalMut.setMsVendor(vendor);
			textVerifBalMut.setNotes(StringUtils.left(message, 200));
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(textVerifBalMut);
			
			Status status = new Status();
			status.setCode(StatusCode.DIGISIGN_FAILED);
			status.setMessage(message);
			
			List<String> trxNos = new ArrayList<>();
			trxNos.add(String.valueOf(textVerifTrxNo));
			
			RegisterVerificationStatusBean verifResult = digisignLogic.buildVerificationStatusBean(registerResponse, audit);
			
			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setTrxNo(trxNos);
			response.setResults(verifResult);
			response.setVerifStatus(false);
			response.setStatus(status);
			response.setPsreCode(vendor.getVendorCode());
			return response;
			
		}
		
		// Handling gagal yang potong saldo VRF dan TEXT_VRF
		if (digisignLogic.cutVerifAndTextVerif(registerResponse, audit)) {
			
			String notes = registerResponse.getInfo();
			
			// Potong verif
			MsLov verifBalType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
			MsLov verifTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UVRF);
			long verifTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
						
			TrBalanceMutation verifBalMut = new TrBalanceMutation();
			verifBalMut.setTrxNo(String.valueOf(verifTrxNo));
			verifBalMut.setTrxDate(new Date());
			verifBalMut.setQty(-1);
			verifBalMut.setMsLovByLovBalanceType(verifBalType);
			verifBalMut.setMsLovByLovTrxType(verifTrxType);
			verifBalMut.setUsrCrt(request.getTlp());
			verifBalMut.setDtmCrt(new Date());
			verifBalMut.setMsTenant(tenant);
			verifBalMut.setMsVendor(vendor);
			verifBalMut.setNotes(notes);
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(verifBalMut);
			
			// Potong text verif
			MsLov textVerifBalType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_TEXT_VRF);
			MsLov textVerifTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UTEXT_VRF);
			long textVerifTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
						
			TrBalanceMutation textVerifBalMut = new TrBalanceMutation();
			textVerifBalMut.setTrxNo(String.valueOf(textVerifTrxNo));
			textVerifBalMut.setTrxDate(new Date());
			textVerifBalMut.setQty(-1);
			textVerifBalMut.setMsLovByLovBalanceType(textVerifBalType);
			textVerifBalMut.setMsLovByLovTrxType(textVerifTrxType);
			textVerifBalMut.setUsrCrt(request.getTlp());
			textVerifBalMut.setDtmCrt(new Date());
			textVerifBalMut.setMsTenant(tenant);
			textVerifBalMut.setMsVendor(vendor);
			textVerifBalMut.setNotes(notes);
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(verifBalMut);
			
			List<String> trxNos = new ArrayList<>();
			trxNos.add(String.valueOf(verifTrxNo));
			trxNos.add(String.valueOf(textVerifTrxNo));
			
			Status status = new Status();
			status.setCode(StatusCode.DIGISIGN_FAILED);
			status.setMessage(digisignLogic.getRegisterErrorMessage(registerResponse, audit));
			
			RegisterVerificationStatusBean verifResult = digisignLogic.buildVerificationStatusBean(registerResponse, audit);
			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setTrxNo(trxNos);
			response.setResults(verifResult);
			response.setVerifStatus(false);
			response.setStatus(status);
			response.setPsreCode(vendor.getVendorCode());
			return response;
		}
		
		// Handling gagal lainnya yang tidak potong saldo
		
		String messageFormat = StringUtils.isEmpty(registerResponse.getInfo()) ? "%1$s" : "%1$s (%2$s)";
		String message = String.format(messageFormat, registerResponse.getNotif(), registerResponse.getInfo());
		
		Status status = new Status();
		status.setCode(StatusCode.DIGISIGN_FAILED);
		status.setMessage(message);
		
		RegisterExternalResponse response = new RegisterExternalResponse();
		response.setStatus(status);
		response.setPsreCode(vendor.getVendorCode());
		return response;
	}
	
	private RegisterExternalResponse registerExternalBasedOnVendor(RegisterExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		if (StringUtils.isBlank(request.getEmail())) {
			CreateSingleEmailRequest emailRequest = new CreateSingleEmailRequest(tenant, request.getNama(), request.getTglLahir(), request.getIdKtp());
			CreateSingleEmailResponse emailResponse = emailLogic.createEmail(emailRequest, audit);
			request.setEmail(emailResponse.getEmail());
			request.setIdEmailHosting(emailResponse.getIdEmailHosting());
		}
		
		if (GlobalVal.VENDOR_CODE_VIDA.equals(vendor.getVendorCode())) {
			return registerExternalVida(request, tenant, vendor, audit);
		}
		
		if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {

			boolean useSyncPrivyVerif = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.LOV_CODE_TENANT_SETTING_USE_SYNC_PRIVY_VERIF);
			if (useSyncPrivyVerif) {
				return registerExternalPrivySynchronous(request, tenant, vendor, audit);
			}

			return registerExternalPrivy(request, tenant, vendor, audit);
		}
		
		if (GlobalVal.VENDOR_CODE_DIGISIGN.equals(vendor.getVendorCode())) {
			return registerExternalDigisign(request, tenant, vendor, audit);
		}
		
		throw new UserException(getMessage("businesslogic.vendor.unhandledregistrationvendor",
				new String[] {vendor.getVendorName()}, audit), ReasonUser.VENDOR_NOT_FOUND);	
	}

	@Override
	public RegisterExternalResponse registerExternal(RegisterExternalRequest request, String apiKey, AuditContext audit) {
		
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(apiKey, audit);
		
		MsVendor vendor = null;
		if (StringUtils.isNotBlank(request.getPsreCode())) {
			vendor = vendorValidatorLogic.validateGetVendor(request.getPsreCode(), true, audit);
		} else {
			List<String> excludedVendorCodes = new ArrayList<>();
			excludedVendorCodes.add(GlobalVal.VENDOR_CODE_ESG);
			vendor = daoFactory.getVendorDao().getHighestPriorityVendorAvailable(tenant.getTenantCode(), excludedVendorCodes);
		}
		
		if (null  == vendor) {
			throw new VendorException(getMessage("businesslogic.vendor.defaultvendornotfound", new String[] {tenant.getTenantName()}, audit), ReasonVendor.VENDOR_NOT_FOUND);
		}
		
		userValidatorLogic.validateExternalRegisterRequestParam(request, tenant, vendor, audit);
		
		// Kalau vendor VIDA dan message validasinya "Anda sudah terdaftar di eSignHub", hit ulang API registrasi
		if (GlobalVal.VENDOR_CODE_VIDA.equals(vendor.getVendorCode())) {
			
			Status validationStatus = userValidatorLogic.getValidateNikPhoneEmailForRegisterMessage(request.getIdKtp(), request.getTlp(), request.getEmail(), vendor, audit);
			
			if (validationStatus.getCode() == 0) {
				return registerExternalBasedOnVendor(request, tenant, vendor, audit);
			}
			
			// Kalau "Anda sudah terdaftar di eSignHub" dan pakai VIDA, hit ulang API register VIDA
			String expectedMessage = getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_USER_REGISTERED, null, audit);
			if (expectedMessage.equals(validationStatus.getMessage())) {
				
				if ("1".equals(tenant.getRegisterAsDukcapilCheck())) {
					return registerExternalBasedOnVendor(request, tenant, vendor, audit);
				}
				
				MsVendorRegisteredUser vendorUser = null;
				if (StringUtils.isBlank(request.getEmail())) {
					vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(request.getTlp(), GlobalVal.VENDOR_CODE_VIDA);
				} else {
					vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(request.getEmail(), GlobalVal.VENDOR_CODE_VIDA);
				}

				if (StringUtils.isBlank(vendorUser.getVendorRegistrationId())) {
					userValidatorLogic.validateVendorRegisteredUserDataState(vendorUser, request.getIdKtp(), request.getEmail(), request.getTlp(), audit);
					return registerExternalBasedOnVendor(request, tenant, vendor, audit);
				}
				
				boolean certExpired = userValidatorLogic.isCertifExpiredForSign(vendorUser, audit);
				if (certExpired) {
					return registerExternalBasedOnVendor(request, tenant, vendor, audit);
				}
				
			}
			
			// Handling validation message lainnya, return fail response
			insertUnregisteredUseroftenant(request.getIdKtp(), tenant, audit);
			RegisterExternalResponse response = new RegisterExternalResponse();
			response.setStatus(validationStatus);
			return response;
		}
		
		// Selain VIDA, tetap validasi seperti biasa
		Status validationStatus = userValidatorLogic.getValidateNikPhoneEmailForRegisterMessage(request.getIdKtp(), request.getTlp(), request.getEmail(), vendor, audit);
		if (validationStatus.getCode() == 0) {
			// Validation status code = 0 -> NIK, email, HP belum terpakai sama sekali, lanjut registrasi
			return registerExternalBasedOnVendor(request, tenant, vendor, audit);
		}

		// Kalau "Anda sudah terdaftar di eSignHub", insert user of tenant
		String expectedMessage = getMessage(GlobalKey.MESSAGE_ERROR_REGISTER_USER_REGISTERED, null, audit);
		if (expectedMessage.equals(validationStatus.getMessage())) {
			
			MsVendorRegisteredUser vendorUser = null;
			if (StringUtils.isBlank(request.getEmail())) {
				vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(request.getTlp(), GlobalVal.VENDOR_CODE_PRIVY_ID);
			} else {
				vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(request.getEmail(), GlobalVal.VENDOR_CODE_PRIVY_ID);
			}

			// Allow register jika vendor_registration_id kosong
			if (StringUtils.isBlank(vendorUser.getVendorRegistrationId())) {
				userValidatorLogic.validateVendorRegisteredUserDataState(vendorUser, request.getIdKtp(), request.getEmail(), request.getTlp(), audit);
				return registerExternalBasedOnVendor(request, tenant, vendor, audit);
			}

			insertUnregisteredUseroftenant(request.getIdKtp(), tenant, audit);
		}
		
		RegisterExternalResponse response = new RegisterExternalResponse();
		response.setStatus(validationStatus);
		return response;
		
	}
	
	private void insertUnregisteredUseroftenant(String idNo, MsTenant tenant, AuditContext audit) {
		AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(idNo);
		if (null == user) {
			return;
		}
		
		MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCodeNewTran(user.getIdMsUser(), tenant.getTenantCode());
		if (null == useroftenant) {
			useroftenant = new MsUseroftenant();
			useroftenant.setDtmCrt(new Date());
			useroftenant.setUsrCrt(audit.getCallerId());
			useroftenant.setAmMsuser(user);
			useroftenant.setMsTenant(tenant);
			daoFactory.getUseroftenantDao().insertUseroftenantNewTran(useroftenant);
		}
	}
	
	private TrSigningProcessAuditTrail insertRegistrationAuditTrail(String phone, String email, AmMsuser user, TrInvitationLink invLink, String notes, AuditContext audit) {
		byte[] phoneByteArray = encryptionLogic.encryptFromString(phone);
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_REGISTRATION);
		String resultStatus = VERIF_SUCCESS_NOTES.equals(notes) ? "1" : "0";
		
		TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
		trail.setPhoneNoBytea(phoneByteArray);
		trail.setHashedPhoneNo(MssTool.getHashedString(phone));
		if (StringUtils.isNotBlank(email)) {
			trail.setEmail(StringUtils.upperCase(email));
		}
		trail.setAmMsUser(user);
		trail.setMsTenant(invLink.getMsTenant());
		trail.setMsVendor(invLink.getMsVendor());
		trail.setTrInvitationLink(invLink);
		trail.setLovProcessType(processType);
		trail.setResultStatus(resultStatus);
		trail.setNotes(StringUtils.left(notes, 200));
		trail.setUsrCrt(audit.getCallerId());
		trail.setDtmCrt(new Date());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(trail);
		
		return trail;
		
	}
	
	private TrSigningProcessAuditTrail insertRegistrationAuditTrail(String phone, String email, AmMsuser user, MsTenant tenant, MsVendor vendor, String notes, AuditContext audit) {
		byte[] phoneByteArray = encryptionLogic.encryptFromString(phone);
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_REGISTRATION);
		String resultStatus = VERIF_SUCCESS_NOTES.equals(notes) ? "1" : "0";
		
		TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
		trail.setPhoneNoBytea(phoneByteArray);
		trail.setHashedPhoneNo(MssTool.getHashedString(phone));
		if (StringUtils.isNotBlank(email)) {
			trail.setEmail(StringUtils.upperCase(email));
		}
		trail.setAmMsUser(user);
		trail.setMsTenant(tenant);
		trail.setMsVendor(vendor);
		trail.setLovProcessType(processType);
		trail.setResultStatus(resultStatus);
		trail.setNotes(StringUtils.left(notes, 200));
		trail.setUsrCrt(audit.getCallerId());
		trail.setDtmCrt(new Date());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(trail);
		
		return trail;
		
	}
	
	private TrSigningProcessAuditTrail insertPrivyRegistrationAuditTrail(String phone, String email, TrInvitationLink invLink, String notes, AuditContext audit) {
		byte[] phoneByteArray = encryptionLogic.encryptFromString(phone);
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_REGISTRATION);
		String resultStatus = PRIVY_SUBMIT_SUCCESS_NOTES.equals(notes) ? "1" : "0";
		
		TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
		trail.setPhoneNoBytea(phoneByteArray);
		trail.setHashedPhoneNo(MssTool.getHashedString(phone));
		if (StringUtils.isNotBlank(email)) {
			trail.setEmail(StringUtils.upperCase(email));
		}
		trail.setMsTenant(invLink.getMsTenant());
		trail.setMsVendor(invLink.getMsVendor());
		trail.setTrInvitationLink(invLink);
		trail.setLovProcessType(processType);
		trail.setResultStatus(resultStatus);
		trail.setNotes(StringUtils.left(notes, 200));
		trail.setUsrCrt(audit.getCallerId());
		trail.setDtmCrt(new Date());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(trail);
		
		return trail;
		
	}
	
	private TrSigningProcessAuditTrail insertPrivyRegistrationAuditTrail(String phone, String email, MsTenant tenant, MsVendor vendor, String notes, AuditContext audit) {
		byte[] phoneByteArray = encryptionLogic.encryptFromString(phone);
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_REGISTRATION);
		String resultStatus = PRIVY_SUBMIT_SUCCESS_NOTES.equals(notes) ? "1" : "0";
		
		TrSigningProcessAuditTrail trail = new TrSigningProcessAuditTrail();
		trail.setPhoneNoBytea(phoneByteArray);
		trail.setHashedPhoneNo(MssTool.getHashedString(phone));
		if (StringUtils.isNotBlank(email)) {
			trail.setEmail(StringUtils.upperCase(email));
		}
		trail.setMsTenant(tenant);
		trail.setMsVendor(vendor);
		trail.setLovProcessType(processType);
		trail.setResultStatus(resultStatus);
		trail.setNotes(StringUtils.left(notes, 200));
		trail.setUsrCrt(audit.getCallerId());
		trail.setDtmCrt(new Date());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(trail);
		
		return trail;
		
	}

}
