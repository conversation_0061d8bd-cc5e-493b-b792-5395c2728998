package com.adins.esign.businesslogic.api.embed;

import com.adins.esign.webservices.model.embed.SignBalanceAvailabilityEmbedRequest;
import com.adins.esign.webservices.model.embed.SignBalanceAvailabilityEmbedResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface SaldoEmbedLogic {

	SignBalanceAvailabilityEmbedResponse getSignBalanceAvailabilityEmbed(SignBalanceAvailabilityEmbedRequest request, AuditContext audit);
}
