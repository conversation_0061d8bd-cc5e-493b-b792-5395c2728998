package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.esign.businesslogic.api.interfacing.PeruriOnPremLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class StampingOnPremJob extends BaseLogic {
	private static final Logger LOG = LoggerFactory.getLogger(StampingOnPremJob.class);
	
	@Value("${emeterai.onprem.processnumber}") private String processNumber;
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void runStampingOnPrem() {
		try {
			LOG.info("Job On-Premise stamping {} started", processNumber);
			AuditContext audit = new AuditContext("SCHEDULER");
			schedulerLogic.attachMeteraiPajakku(audit);
			LOG.info("Job On-Premise stamping {} finished", processNumber);
		} catch (Exception e) {
			LOG.error("Error on running On-Premise stamping {} job", processNumber, e);
		}
		
	}
}
