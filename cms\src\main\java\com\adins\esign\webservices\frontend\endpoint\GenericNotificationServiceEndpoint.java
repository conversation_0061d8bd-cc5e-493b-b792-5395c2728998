package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.NotificationLogic;
import com.adins.esign.webservices.frontend.api.NotificationService;
import com.adins.esign.webservices.model.SendNotificationTestRequest;
import com.adins.esign.webservices.model.SendNotificationTestResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/notification")
@Api(value = "NotificationService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericNotificationServiceEndpoint implements NotificationService {
    
    @Autowired private NotificationLogic notificationLogic;

    @Override
	@POST
	@Path("/s/sendNotificationTesting")
    public SendNotificationTestResponse sendNotificationTesting(SendNotificationTestRequest request) {
        AuditContext audit = request.getAudit().toAuditContext();

        return notificationLogic.sendNotificationTesting(request, audit);
    }

    
    
}
