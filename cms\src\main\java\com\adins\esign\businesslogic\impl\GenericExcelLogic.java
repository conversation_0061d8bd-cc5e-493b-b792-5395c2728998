package com.adins.esign.businesslogic.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.ExcelLogic;
import com.adins.esign.util.OnExcelCallback;

@Component
public class GenericExcelLogic implements ExcelLogic {

	@Override
	public byte[] generate(OnExcelCallback callback) throws IOException {
		XSSFWorkbook workbook = new XSSFWorkbook();

        XSSFCellStyle styleBoldText = workbook.createCellStyle();
        XSSFFont fontBold = workbook.createFont();
        fontBold.setBold(true);
        styleBoldText.setFont(fontBold);

        callback.onBuild(workbook, styleBoldText);

//        ByteArrayOutputStream bos = new ByteArrayOutputStream();
//        try {
//        	workbook.write(bos);
//		} finally {
//			bos.close();
//		}
        
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
        	workbook.write(bos);
        	return bos.toByteArray();
        }
        
	}

	@Override
	public byte[] generateEmbed(OnExcelCallback callback) throws IOException {
		return this.generate(callback);
	}

}
