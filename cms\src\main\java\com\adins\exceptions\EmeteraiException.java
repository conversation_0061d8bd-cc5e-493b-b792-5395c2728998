package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class EmeteraiException extends AdInsException {
	private static final long serialVersionUID = 1L;
	
	public enum ReasonEmeterai {
		INVALID_CREDENTIAL,
		LOGIN_EXCEPTION,
		LOGIN_RESPONSE_ERROR,
		UPLOAD_DOC_EXCEPTION,
		UPLOAD_DOC_RESPONSE_ERROR,
		GENERATE_EXCEPTION,
		GENERATE_RESPONSE_ERROR,
		STAMPING_EXCEPTION,
		STAMPING_RESPONSE_ERROR,
		DOWNLOAD_DOC_EXCEPTION,
		DOWNLOAD_DOC_RESPONSE_ERROR,
		STAMP_PROCESS_FAILED
	}
	
	private final ReasonEmeterai reason;
	
	public EmeteraiException(ReasonEmeterai reason) {
		this.reason = reason;
	}
	
	public EmeteraiException(String message, ReasonEmeterai reason) {
		super(message);
		this.reason = reason;
	}
	
	public EmeteraiException(Throwable ex, ReasonEmeterai reason) {
		super(ex);
		this.reason = reason;
	}
	
	public EmeteraiException(String message, Throwable ex, ReasonEmeterai reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	public ReasonEmeterai getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (null != this.reason) {
			switch (reason) {
			case INVALID_CREDENTIAL:
				return StatusCode.EMETERAI_INVALID_CREDENTIAL;
			case LOGIN_EXCEPTION:
				return StatusCode.EMETERAI_LOGIN_EXCEPTION;
			case LOGIN_RESPONSE_ERROR:
				return StatusCode.EMETERAI_LOGIN_RESPONSE_ERROR;
			case UPLOAD_DOC_EXCEPTION:
				return StatusCode.EMETERAI_UPLOAD_DOC_EXCEPTION;
			case UPLOAD_DOC_RESPONSE_ERROR:
				return StatusCode.EMETERAI_UPLOAD_DOC_RESPONSE_ERROR;
			case GENERATE_EXCEPTION:
				return StatusCode.EMETERAI_GENERATE_EXCEPTION;
			case GENERATE_RESPONSE_ERROR:
				return StatusCode.EMETERAI_GENERATE_RESPONSE_ERROR;
			case STAMPING_EXCEPTION:
				return StatusCode.EMETERAI_STAMPING_EXCEPTION;
			case STAMPING_RESPONSE_ERROR:
				return StatusCode.EMETERAI_STAMPING_RESPONSE_ERROR;
			case DOWNLOAD_DOC_EXCEPTION:
				return StatusCode.EMETERAI_DOWNLOAD_DOC_EXCEPTION;
			case DOWNLOAD_DOC_RESPONSE_ERROR:
				return StatusCode.EMETERAI_DOWNLOAD_DOC_RESPONSE_ERROR;
			case STAMP_PROCESS_FAILED:
				return StatusCode.EMETERAI_PROCESS_FAILED;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}

}
