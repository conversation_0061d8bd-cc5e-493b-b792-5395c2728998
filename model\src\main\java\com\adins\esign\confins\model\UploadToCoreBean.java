package com.adins.esign.confins.model;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;

public class UploadToCoreBean implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	@JsonProperty("RefNo") @SerializedName("RefNo") private String refNo;
	@JsonProperty("DocumentObjs") @SerializedName("DocumentObjs") private List<DocumentToUploadBean> documentObjs;
	public String getRefNo() {
		return refNo;
	}
	public void setRefNo(String refNo) {
		this.refNo = refNo;
	}
	public List<DocumentToUploadBean> getDocumentObjs() {
		return documentObjs;
	}
	public void setDocumentObjs(List<DocumentToUploadBean> documentObjs) {
		this.documentObjs = documentObjs;
	}
}
