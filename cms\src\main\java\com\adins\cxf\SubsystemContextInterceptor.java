package com.adins.cxf;

import org.apache.cxf.message.Message;
import org.apache.cxf.phase.AbstractPhaseInterceptor;
import org.apache.cxf.phase.Phase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.adins.context.SubsystemContextHolder;

public class SubsystemContextInterceptor extends AbstractPhaseInterceptor<Message>{
	public SubsystemContextInterceptor(String phase) {
		super(Phase.RECEIVE);
	}

	private static final Logger LOG = LoggerFactory.getLogger(SubsystemContextInterceptor.class);
	
	@Override
	public void handleFault(Message arg0) {
		LOG.error("Fault: {}", arg0);
	}

	@Override
	public void handleMessage(Message arg0) {
		LOG.trace("Clear Subsystem Context");
		SubsystemContextHolder.clearSchema();
		SubsystemContextHolder.setSchema("WebServices");
	}

}
