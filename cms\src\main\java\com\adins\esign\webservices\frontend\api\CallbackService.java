package com.adins.esign.webservices.frontend.api;

import javax.ws.rs.core.Response;

import com.adins.esign.webservices.model.TekenAjaCallbackRequest;
import com.adins.esign.webservices.model.TekenAjaCallbackResponse;

public interface CallbackService {
	
	Response callbackDigisignSigning(String tenantCode, String msg);

//	Response callbackTekenAajaToeSignHub(TekenAjaCallbackRequest request);

	TekenAjaCallbackResponse tekenAjaCallback(TekenAjaCallbackRequest request); 
}
