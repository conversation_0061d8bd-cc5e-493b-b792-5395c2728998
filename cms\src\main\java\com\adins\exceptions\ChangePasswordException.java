package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class ChangePasswordException extends AdInsException{
	private static final long serialVersionUID = 1L;
	public enum Reason{
		INVALID_OLD_PASSWORD, 
		CHANGE_PASSWORD_VIOLATION,
		INVALID_NEW_PASSWORD,
		INVALID_USER,
		INACTIVE_USER,
		INCORRECT_RESET_CODE,
		UNKNOWN
	} 
	private final Reason reason;
	public ChangePasswordException(Reason reason) {
		this.reason = reason;
	}

	public ChangePasswordException(String message, Reason reason) {
		super(message);
		this.reason = reason;
	}

	public ChangePasswordException(Throwable ex, Reason reason) {
		super(ex);
		this.reason = reason;
	}
	
	public ChangePasswordException(String message, Throwable ex, Reason reason) {
		super(message, ex);
		this.reason = reason;
	}
	@Override
	public int getErrorCode() {
		if(this.reason!=null){
			switch(reason){
				case INVALID_OLD_PASSWORD:
					return StatusCode.INVALID_OLD_PASSWORD;
				case CHANGE_PASSWORD_VIOLATION:
					return StatusCode.CHANGE_PASSWORD_VIOLATION;
				case INVALID_NEW_PASSWORD:
					return StatusCode.INVALID_NEW_PASSWORD;
				case INVALID_USER:
					return StatusCode.INVALID_USER;
				case INACTIVE_USER:
					return StatusCode.INACTIVE_USER;
				case INCORRECT_RESET_CODE:
					return StatusCode.INCORRECT_RESET_CODE;
				default:
					return StatusCode.UNKNOWN;
			}
		} 
		return StatusCode.UNKNOWN;
	}

}
