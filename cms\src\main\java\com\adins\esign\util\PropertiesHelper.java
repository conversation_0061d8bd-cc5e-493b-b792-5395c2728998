package com.adins.esign.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import com.adins.framework.tool.properties.SpringPropertiesUtils;

import com.adins.esign.constants.GlobalKey;

@Component
@SuppressWarnings("squid:S2696")
public class PropertiesHelper {
	
    @Value("${login.bypass}") private String loginBypassProp;
    private static String loginBypass;
    
	@Value("${login.bypass}")
	public void setLoginBypassStatic(String loginBypassProp){
		 PropertiesHelper.loginBypass = loginBypassProp;
	}
	 
	public static boolean isBypassLogin() {
		return (!StringUtils.isEmpty(PropertiesHelper.loginBypass) &&
				"1".equals(PropertiesHelper.loginBypass));
	}
}
