package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.VendorRegisteredUserDao;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendorRegisteredUserHistory;
import com.adins.esign.model.TrUserDataAccessLog;
import com.adins.esign.model.custom.EmailBean;
import com.adins.esign.util.MssTool;

@Component
@Transactional
public class VendorRegisteredUserDaoHbn extends BaseDaoHbn implements VendorRegisteredUserDao {
	
	@Override
	public void insertVendorRegisteredUser(MsVendorRegisteredUser newVendorRegisteredUser) {
		newVendorRegisteredUser.setUsrCrt(MssTool.maskData(newVendorRegisteredUser.getUsrCrt()));
		managerDAO.insert(newVendorRegisteredUser);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertVendorRegisteredUserNewTran(MsVendorRegisteredUser newVendorRegisteredUser) {
		newVendorRegisteredUser.setUsrCrt(MssTool.maskData(newVendorRegisteredUser.getUsrCrt()));
		managerDAO.insert(newVendorRegisteredUser);
	}
	
	
	@Override
	public void updateVendorRegisteredUser(MsVendorRegisteredUser vRUser) {
		vRUser.setUsrUpd(MssTool.maskData(vRUser.getUsrUpd()));
		managerDAO.update(vRUser);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateVendorRegisteredUserNewTran(MsVendorRegisteredUser vRUser) {
		vRUser.setUsrUpd(MssTool.maskData(vRUser.getUsrUpd()));
		managerDAO.update(vRUser);
	}
	
	@Override
	public MsVendorRegisteredUser getVendorRegisteredUserByLoginId(String loginId) {
		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser vuser "
				+ "join fetch vuser.msVendor mv "
				+ "join fetch vuser.amMsuser mu "
				+ "where mv.isActive ='1' and  mu.loginId = :loginId ", 
		new Object[][] {{ AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId) }});
	}
	
	@Override
	public MsVendorRegisteredUser getVendorRegisteredUserByLoginIdAndVendorCode(String loginId, String vendorCode) {
		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser vuser "
				+ "join fetch vuser.msVendor mv "
				+ "join fetch vuser.amMsuser mu "
				+ "where mv.isActive ='1' and  mu.loginId = :loginId and mv.vendorCode = :vendorCode", 
		new Object[][] {{ AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId) }, {MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode)}});
	}
	
	@Override
	public MsVendorRegisteredUser getVendorRegisteredUserByIdMsVendorRegisteredUser(long vendorRegisteredUserId) {
		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser vuser "
				+ "join fetch vuser.msVendor mv "
				+ "join fetch vuser.amMsuser mu "
				+ "where vuser.idMsVendorRegisteredUser = :vendorRegisteredUserId ", 
				new Object[][] {{"vendorRegisteredUserId", vendorRegisteredUserId}});
	}
	
	@Override
	public MsVendorRegisteredUser getVendorRegisteredUserByPhoneAndVendorCode(String phone, String vendorCode) {
		if (StringUtils.isBlank(phone) || StringUtils.isBlank(vendorCode)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser vuser "
				+ "join fetch vuser.msVendor mv "
				+ "join fetch vuser.amMsuser mu "
				+ "where mv.isActive ='1' and  vuser.hashedSignerRegisteredPhone = :hashedPhone and mv.vendorCode = :vendorCode", 
		new Object[][] {{ AmMsuser.HASHED_PHONE_HBM, MssTool.getHashedString(phone)}, {MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode)}});
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<MsVendorRegisteredUser> getListVendorRegisteredUserByLoginId(String loginId) {
		if (StringUtils.isBlank(loginId))
			return Collections.emptyList();
			
		try {
			Object[][] param = new Object[][] {{"loginId", loginId}};
			
			StringBuilder query = new StringBuilder();
			query.append("from MsVendorRegisteredUser vru ")
				.append( "join fetch vru.msVendor mv ")
				.append( "join fetch vru.amMsuser mu ")
				.append(" where mu.loginId = :loginId ");
			
			Map<String, Object> listResult = this.managerDAO.selectAll(query.toString(), param);
			return (List<MsVendorRegisteredUser>) listResult.get(AmGlobalKey.MAP_RESULT_LIST);
		} catch (Exception e) {
			return Collections.emptyList();
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<MsVendorRegisteredUser> getListVendorRegisteredUserByIdMsUser(long idMsUser) {
		try {
			Object[][] param = new Object[][] {{AmMsuser.ID_MS_USER_HBM, idMsUser}};
			
			StringBuilder query = new StringBuilder();
			query.append("from MsVendorRegisteredUser vru ")
				.append( "join fetch vru.msVendor mv ")
				.append( "join fetch vru.amMsuser mu ")
				.append(" where mu.idMsUser = :idMsUser ");
			
			Map<String, Object> listResult = this.managerDAO.selectAll(query.toString(), param);
			return (List<MsVendorRegisteredUser>) listResult.get(AmGlobalKey.MAP_RESULT_LIST);
		} catch (Exception e) {
			return Collections.emptyList();
		}
	}
	
	@Override
	public MsVendorRegisteredUser getVendorRegisteredUserBySignerRegisteredEmail(String email) {
		Object[][] params = new Object[][] {{ Restrictions.eq(MsVendorRegisteredUser.SIGNER_REGISTERED_EMAIL_HBM, StringUtils.upperCase(email)) }};
		return this.managerDAO.selectOne(MsVendorRegisteredUser.class, params);
	}
	
	@Override
	public MsVendorRegisteredUser getVendorRegisteredUserByPhoneNumber(String phoneNum) {

		Object[][] params = new Object[][] {{MsVendorRegisteredUser.HASHED_SIGNER_REGISTERED_PHONE, MssTool.getHashedString(phoneNum)}};

		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser mvru "
				+ "where mvru.hashedSignerRegisteredPhone = :hashedSignerRegisteredPhone ", 
					params);
	}
	
	@Override
	public MsVendorRegisteredUser getVendorRegisteredUserByIdMsUserAndVendorCode(long idMsUser, String vendorCode) {
		Object[][] params = new Object[][] {
			{AmMsuser.ID_MS_USER_HBM, idMsUser},
			{MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode)}
		};
		
		return managerDAO.selectOne(
				"from MsVendorRegisteredUser vru "
					+ "join fetch vru.msVendor mv "
					+ "join fetch vru.amMsuser mu "
					+ "where mu.idMsUser = :idMsUser and mv.vendorCode = :vendorCode ", params);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsVendorRegisteredUser getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(long idMsUser, String vendorCode) {
		Object[][] params = new Object[][] {
			{AmMsuser.ID_MS_USER_HBM, idMsUser},
			{MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode)}
		};
		
		return managerDAO.selectOne(
				"from MsVendorRegisteredUser vru "
					+ "join fetch vru.msVendor mv "
					+ "join fetch vru.amMsuser mu "
					+ "where mu.idMsUser = :idMsUser and mv.vendorCode = :vendorCode ", params);
	}
	
	@Override
	public List<EmailBean> getUserRegisteredEmailByNik(String idNo) {
		StringBuilder query = new StringBuilder();
		query.append("select signer_registered_email as \"email\", vru.email_service as \"emailService\" ")
			 .append("from ms_vendor_registered_user vru ")
			 .append("join am_msuser u on u.id_ms_user = vru.id_ms_user ")
			 .append("where u.hashed_id_no = :idNo ");
		Map<String, Object> param = new HashMap<>();
		param.put("idNo", MssTool.getHashedString(idNo));
		
		return this.managerDAO.selectForListString(EmailBean.class, query.toString(), param, null);
	}

	@Override
	public MsVendorRegisteredUser getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(String email,
			String vendorCode) {
		if (StringUtils.isBlank(email) || StringUtils.isBlank(vendorCode)) {
			return null;
		}
		
		Map<String, Object> params = new HashMap<>();
		params.put(GlobalVal.CONST_EMAIL, StringUtils.upperCase(email));
		params.put(MsVendor.VENDOR_CODE_HBM, vendorCode);
		
		return this.managerDAO.selectOne("from MsVendorRegisteredUser vru "
										 + "join fetch vru.msVendor v "
										 + "join fetch vru.amMsuser u "
										 + "where vru.signerRegisteredEmail = :email and v.vendorCode = :vendorCode ",
										 params);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<MsVendorRegisteredUser> getListVendorRegisteredUserBySignerRegisteredEmail(String email) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsVendorRegisteredUser.SIGNER_REGISTERED_EMAIL_HBM, StringUtils.upperCase(email));

		return (List<MsVendorRegisteredUser>) this.managerDAO.list(
				"from MsVendorRegisteredUser vuser "
				+ "where signerRegisteredEmail = :signerRegisteredEmail", params).get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<MsVendorRegisteredUser> getListVendorRegisteredUserByPhone(String phone) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsVendorRegisteredUser.HASHED_SIGNER_REGISTERED_PHONE, MssTool.getHashedString(phone));

		return (List<MsVendorRegisteredUser>) this.managerDAO.list(
				"from MsVendorRegisteredUser vuser "
				+ "where hashedSignerRegisteredPhone = :hashedSignerRegisteredPhone", params).get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<String> getUserRegisteredVendorsByNik(String idNo) {
		List<String> vendors = new ArrayList<>();
		Object[][] param = new Object[][] {{"idNo", MssTool.getHashedString(idNo)}};
		
		Map<String, Object> mapResultList = this.managerDAO.list(
				"from MsVendorRegisteredUser vru "
				+ "join fetch vru.msVendor mv "
				+ "join fetch vru.amMsuser u "
				+ "where u.hashedIdNo = :idNo "
			, param);
		
		List<MsVendorRegisteredUser> vrus = (List<MsVendorRegisteredUser>) mapResultList.get(AmGlobalKey.MAP_RESULT_LIST);
		
		for(MsVendorRegisteredUser vru : vrus) {
			vendors.add(vru.getMsVendor().getVendorCode());
		}
		
		return vendors;
	}
	
	@Override
	public MsVendorRegisteredUser getVendorRegisteredUserByIdNoAndVendorCode(String idNo, String vendorCode) {
		if (StringUtils.isBlank(idNo) || StringUtils.isBlank(vendorCode)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser vuser "
				+ "join fetch vuser.msVendor mv "
				+ "join fetch vuser.amMsuser mu "
				+ "where mv.isActive ='1' and  mu.hashedIdNo = :hashedIdNo and mv.vendorCode = :vendorCode", 
		new Object[][] {{ AmMsuser.HASHED_IDNO_HBM, MssTool.getHashedString(idNo) }, {MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode)}});
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<String> getUserRegisteredVendorsByIdMsUser(Long idUser) {
		List<String> vendors = new ArrayList<>();
		Object[][] param = new Object[][] {{"idMsUser", idUser}};
		
		Map<String, Object> mapResultList = this.managerDAO.list(
				"from MsVendorRegisteredUser vru "
				+ "join fetch vru.msVendor mv "
				+ "join fetch vru.amMsuser u "
				+ "where u.idMsUser = :idMsUser "
			, param);
		
		List<MsVendorRegisteredUser> vrus = (List<MsVendorRegisteredUser>) mapResultList.get(AmGlobalKey.MAP_RESULT_LIST);
		
		for(MsVendorRegisteredUser vru : vrus) {
			vendors.add(vru.getMsVendor().getVendorCode());
		}
		
		return vendors;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<MsVendorRegisteredUser> getListVendorUserByPhone(String phone, String vendorCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsVendorRegisteredUser.HASHED_SIGNER_REGISTERED_PHONE, MssTool.getHashedString(phone));
		params.put(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode));
		
		return (List<MsVendorRegisteredUser>) this.managerDAO.list(
				"from MsVendorRegisteredUser vru "
				+ "join fetch vru.amMsuser mu "
				+ "join fetch vru.msVendor mv "
				+ "where vru.hashedSignerRegisteredPhone = :hashedSignerRegisteredPhone "
				+ "and mv.vendorCode = :vendorCode ", params).get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<MsVendorRegisteredUser> getListVendorUserByEmail(String email, String vendorCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsVendorRegisteredUser.SIGNER_REGISTERED_EMAIL_HBM, StringUtils.upperCase(email));
		params.put(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode));
		
		return (List<MsVendorRegisteredUser>) this.managerDAO.list(
				"from MsVendorRegisteredUser vru "
				+ "join fetch vru.amMsuser mu "
				+ "join fetch vru.msVendor mv "
				+ "where vru.signerRegisteredEmail = :signerRegisteredEmail "
				+ "and mv.vendorCode = :vendorCode ", params).get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<MsVendorRegisteredUser> getListVendorUserByIdMsUserAndEmailService(Long idMsUser, String emailService) {
		Map<String, Object> params = new HashMap<>();
		params.put(AmMsuser.ID_MS_USER_HBM, idMsUser);
		params.put("emailService", emailService);
		
		return (List<MsVendorRegisteredUser>) managerDAO.list(
				"from MsVendorRegisteredUser vru "
				+ "join fetch vru.amMsuser mu "
				+ "join fetch vru.msVendor mv "
				+ "where mu.idMsUser = :idMsUser "
				+ "and vru.emailService = :emailService ", params).get(AmGlobalKey.MAP_RESULT_LIST);
		
	}

	@Override
	public MsVendorRegisteredUser getEveryVendorRegisteredByLoginIdAndVendorCode(String loginId, String vendorCode) {
		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser vuser "
				+ "join fetch vuser.msVendor mv "
				+ "join fetch vuser.amMsuser mu "
				+ "where vuser.signerRegisteredEmail = :loginId and mv.vendorCode = :vendorCode", 
		new Object[][] {{ AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId) }, {MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode)}});
	}

	@Override
	public MsVendorRegisteredUser getLatestVendorRegisteredUserByPhoneNumber(String phoneNum) {
		Map<String, Object> params = new HashMap<>();
		params.put("phone", MssTool.getHashedString(phoneNum));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_ms_vendor_registered_user ")
			.append("from ms_vendor_registered_user ")
			.append("where hashed_signer_registered_phone = :phone ")
			.append("order by id_ms_vendor_registered_user desc limit 1 ");
		
		BigInteger idMsVendorRegisteredUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsVendorRegisteredUser) {
			return null;
		}
		
		return managerDAO.selectOne(
				"from MsVendorRegisteredUser vru "
				+ "join fetch vru.amMsuser mu "
				+ "join fetch vru.msVendor mv "
				+ "where vru.idMsVendorRegisteredUser = :idMsVendorRegisteredUser ", new Object[][] {{"idMsVendorRegisteredUser", idMsVendorRegisteredUser.longValue()}});
	}

	@Override
	public MsVendorRegisteredUser getLatestVendorRegisteredUserBySignerRegisteredEmail(String email) {
		Map<String, Object> params = new HashMap<>();
		params.put(GlobalVal.CONST_EMAIL, StringUtils.upperCase(email));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_ms_vendor_registered_user ")
			.append("from ms_vendor_registered_user ")
			.append("where signer_registered_email = :email ")
			.append("order by id_ms_vendor_registered_user desc limit 1");
		
		BigInteger idMsVendorRegisteredUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsVendorRegisteredUser) {
			return null;
		}
		
		return managerDAO.selectOne(
				"from MsVendorRegisteredUser vru "
				+ "join fetch vru.amMsuser mu "
				+ "join fetch vru.msVendor mv "
				+ "where vru.idMsVendorRegisteredUser = :idMsVendorRegisteredUser ", new Object[][] {{"idMsVendorRegisteredUser", idMsVendorRegisteredUser.longValue()}});
	}
	
	@Override
	public MsVendorRegisteredUser getLatestVendorRegisteredUserBySignerRegisteredEmailandDtmUpd(String email) {
		Map<String, Object> params = new HashMap<>();
		params.put(GlobalVal.CONST_EMAIL, StringUtils.upperCase(email));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_ms_vendor_registered_user ")
			.append("from ms_vendor_registered_user ")
			.append("where signer_registered_email = :email ")
			.append("order by coalesce (dtm_upd, dtm_crt) desc limit 1");
		
		BigInteger idMsVendorRegisteredUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsVendorRegisteredUser) {
			return null;
		}
		
		return managerDAO.selectOne(
				"from MsVendorRegisteredUser vru "
				+ "join fetch vru.amMsuser mu "
				+ "join fetch vru.msVendor mv "
				+ "where vru.idMsVendorRegisteredUser = :idMsVendorRegisteredUser ", new Object[][] {{"idMsVendorRegisteredUser", idMsVendorRegisteredUser.longValue()}});
	}

	@Override
	public MsVendorRegisteredUser getActiveVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(String email, String vendorCode) {
		if (StringUtils.isBlank(email) || StringUtils.isBlank(vendorCode)) {
			return null;
		}
		
		Map<String, Object> params = new HashMap<>();
		params.put(GlobalVal.CONST_EMAIL, StringUtils.upperCase(email));
		params.put(MsVendor.VENDOR_CODE_HBM, vendorCode);
		
		return this.managerDAO.selectOne("from MsVendorRegisteredUser vru "
										 + "join fetch vru.msVendor v "
										 + "join fetch vru.amMsuser u "
										 + "where vru.signerRegisteredEmail = :email and v.vendorCode = :vendorCode and v.isActive ='1'",
										 params);
	}

	@Override
	public String getVendorAccessTokenByVendorCodeAndidMsUser(String vendorCode, long idUser) {

		
		Map<String, Object> params = new HashMap<>();
		params.put(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode));
		params.put("idUser", idUser);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select vendor_access_token ")
			.append("from ms_vendor_registered_user mvru ")
			.append("join ms_vendor mv on mvru.id_ms_vendor = mv.id_ms_vendor ")
			.append("Where mv.vendor_code = :vendorCode ")
			.append("and mvru.id_ms_user = :idUser");
		
		return (String) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public MsVendorRegisteredUser getLatestVendorRegisteredUserByidNo(String idNo) {
		Map<String, Object> params = new HashMap<>();
		params.put("idNo", MssTool.getHashedString(idNo));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_ms_vendor_registered_user ")
			.append("from ms_vendor_registered_user mv ")
			.append("join am_msuser mu on (mu.id_ms_user = mv.id_ms_user) ")
			.append("where hashed_id_no = :idNo ")
			.append("order by mv.dtm_crt desc ")
			.append("limit 1");
		
		BigInteger idMsVendorRegisteredUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsVendorRegisteredUser) {
			return null;
		}
		return managerDAO.selectOne(
				"from MsVendorRegisteredUser vru "
				+ "join fetch vru.amMsuser mu "
				+ "join fetch vru.msVendor mv "
				+ "where vru.idMsVendorRegisteredUser = :idMsVendorRegisteredUser ", new Object[][] {{"idMsVendorRegisteredUser", idMsVendorRegisteredUser.longValue()}});
	}

	@Override
	public MsVendorRegisteredUser getLatestVendorRegisteredUserByPhoneandDtmUpd(String phone) {
		Map<String, Object> params = new HashMap<>();
		params.put("phone", MssTool.getHashedString(phone));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_ms_vendor_registered_user ")
			.append("from ms_vendor_registered_user ")
			.append("where hashed_signer_registered_phone = :phone ")
			.append("order by coalesce (dtm_upd, dtm_crt) desc limit 1");
		
		BigInteger idMsVendorRegisteredUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsVendorRegisteredUser) {
			return null;
		}
		
		return managerDAO.selectOne(
				"from MsVendorRegisteredUser vru "
				+ "join fetch vru.amMsuser mu "
				+ "join fetch vru.msVendor mv "
				+ "where vru.idMsVendorRegisteredUser = :idMsVendorRegisteredUser ", new Object[][] {{"idMsVendorRegisteredUser", idMsVendorRegisteredUser.longValue()}});
	}
}
