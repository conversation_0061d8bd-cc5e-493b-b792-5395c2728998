package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.StampDutyDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.StampDutyBean;
import com.adins.esign.model.custom.StampDutyHistoryBean;
import com.adins.esign.model.custom.StampDutyReportBean;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class StampDutyDaoHbn extends BaseDaoHbn implements StampDutyDao{
	private static final String INVOICE_NO = "invoiceNo";
	private static final String STAMPDUTY_NO = "stampDutyNo";
	private static final String STAMPDUTY_FEE  = "stampDutyFee";
	private static final String TRX_NO = "trxNo";

	@Override
	public BigInteger countAvailableStampDutyByIdTenant(long idMsTenant) {
		Object[][] params = new Object[][] {{MsTenant.ID_TENANT_HBM, idMsTenant}};
		StringBuilder query = new StringBuilder()
				.append(" select count(1) ")
				.append(" from tr_stamp_duty sd ")
				.append(" join ms_lov lov on sd.lov_stamp_duty_status = lov.id_lov ")
				.append(" where lov.lov_group = '"+GlobalVal.LOV_GROUP_SDT_STATUS+"' ")
					.append(" and lov.code = '"+GlobalVal.CODE_LOV_SDT_AVAILABLE+"' ")
					.append(" and sd.id_ms_tenant = :idMsTenant ");
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}
	
	@Override
	public BigInteger countUsedStampDutyByTenantCodeAndInvoiceNo(String tenantCode, String invoiceNo) {
		Object[][] params = new Object[][] {{MsTenant.TENANT_CODE_HBM, tenantCode}, {"refNo", invoiceNo}};
		StringBuilder query = new StringBuilder()
				.append(" select count(1) ")
				.append(" from tr_stamp_duty sd ")
				.append(" join ms_lov lov on sd.lov_stamp_duty_status = lov.id_lov ")
				.append(" join tr_balance_mutation bm on sd.trx_no = bm.trx_no ")
				.append(" join ms_tenant tenant on tenant.id_ms_tenant = sd.id_ms_tenant ")
				.append(" where lov.lov_group = '"+GlobalVal.LOV_GROUP_SDT_STATUS+"' ")
					.append(" and lov.code != '"+GlobalVal.CODE_LOV_SDT_AVAILABLE+"' ")
					.append(" and tenant.tenant_code = :tenantCode ")
					.append(" and bm.ref_no = :refNo ");
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public Map<String, Object> getAvailableStampDutyByIdTenant(long idMsTenant) {
		StringBuilder query = new StringBuilder();
		query
			.append(" select sd.id_stamp_duty, sd.stamp_duty_fee, bm.ref_no, to_char(trx_date, 'DD Month YYYY'), sd.stamp_duty_no ")
			.append(" from tr_stamp_duty sd ")
			.append(" join tr_balance_mutation bm on (sd.trx_no = bm.trx_no) ")
			.append(" join ms_lov lov on (sd.lov_stamp_duty_status = lov.id_lov) ")
			.append(" where sd.id_ms_tenant = :idMsTenant ")
				.append(" and lov.lov_group = '"+GlobalVal.LOV_GROUP_SDT_STATUS+"' ")
				.append(" and lov.code = '"+GlobalVal.CODE_LOV_SDT_AVAILABLE+"' ")
			.append(" order by sd.id_stamp_duty asc ")
			.append(" limit 1 ");
		
		Object[][] params = new Object[][] {{ MsTenant.ID_TENANT_HBM, idMsTenant }};
		Object[] results = (Object[]) this.managerDAO.selectOneNativeString(query.toString(), params);
		
		Map<String, Object> resultMap = new HashMap<>(5);
		resultMap.put("idStampDuty", results[0]);
		resultMap.put(STAMPDUTY_FEE, results[1]);
		resultMap.put(INVOICE_NO, results[2]);
		resultMap.put("invoiceDate", results[3]);
		resultMap.put(STAMPDUTY_NO, results[4]);
		
		return resultMap;
	}

	@Override
	public TrStampDuty getStampDutyByStampDutyNo(String stampDutyNo) {
		return this.managerDAO.selectOne(TrStampDuty.class,
				new Object[][] {{ Restrictions.eq(STAMPDUTY_NO, stampDutyNo)}});
	}
	
	@Override
	public TrStampDuty getStampDutyById(long idStampDuty) {
		if (0 == idStampDuty)
			return null;
	
		return this.managerDAO.selectOne(
				"from TrStampDuty std "
				+ "join fetch std.msTenant mt "
				+ "join fetch std.msVendor mv "
				+ "join fetch std.msLov ml "
				+ "where std.idStampDuty = :idStampDuty ", 
						new Object[][] {{TrStampDuty.ID_STAMP_DUTY_HBM, idStampDuty}});
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrStampDuty getStampDutyByIdNewTran(long idStampDuty) {
		if (0 == idStampDuty) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrStampDuty sdt "
				+ "join fetch sdt.msTenant mt "
				+ "join fetch sdt.msVendor mv "
				+ "join fetch sdt.msLov ml "
				+ "where sdt.idStampDuty = :idStampDuty ", 
						new Object[][] {{TrStampDuty.ID_STAMP_DUTY_HBM, idStampDuty}});
	}
	
	@Override
	public void updateTrStampDuty(TrStampDuty stampDuty) {
		stampDuty.setUsrUpd(MssTool.maskData(stampDuty.getUsrUpd()));
		this.managerDAO.update(stampDuty);
	}

	@Override
	public void insertTrStampDuty(TrStampDuty stampDuty) {
		stampDuty.setUsrCrt(MssTool.maskData(stampDuty.getUsrCrt()));
		this.managerDAO.insert(stampDuty);
	}

	private String constructParamTenant(Map<String, Object> params, String tenantCode) {
		StringBuilder query = new StringBuilder();
		if (StringUtils.isNotBlank(tenantCode)) {
			query.append(" and tenant_code = :tenantCode ");
			params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		}
		
		return query.toString();
	}
	

	private String constructParamStampDuty(Map<String, Object> params, String invoiceNo,
			String stampDutyNo, String businessLineCode, String officeCode,
			String regionCode, Date usedDateStart, Date usedDateEnd) {
		StringBuilder query = new StringBuilder();

		if(StringUtils.isNotBlank(invoiceNo)) {
			query.append("and bm.ref_no like :invoiceNo ");
			params.put(INVOICE_NO, "%" + StringUtils.upperCase(invoiceNo) + "%");
		}
		
		if(StringUtils.isNotBlank(stampDutyNo)) {
			query.append("and UPPER(stamp_duty_no) like :sdtNo ");
			params.put("sdtNo", "%" + StringUtils.upperCase(stampDutyNo) + "%");
		}
		
		if (StringUtils.isNotBlank(businessLineCode)) {
			query.append(" and mutation.business_line_code = :businessLineCode ");
			params.put("businessLineCode", StringUtils.upperCase(businessLineCode));
		}
		
		if (StringUtils.isNotBlank(officeCode)) {
			query.append(" and mutation.office_code = :officeCode ");
			params.put("officeCode", StringUtils.upperCase(officeCode));
		}
		
		if (StringUtils.isNotBlank(regionCode)) {
			query.append(" and mutation.region_code = :regionCode ");
			params.put("regionCode", StringUtils.upperCase(regionCode));
		}
		
		if(usedDateStart != null && usedDateEnd != null) {			
			query.append("and sdt.dtm_crt >= :dateStart and sdt.dtm_crt <= :dateEnd ");
			params.put("dateStart", usedDateStart);
			params.put("dateEnd", usedDateEnd);
		} else {
			query.append(" and sdt.dtm_crt >= date_trunc('MONTH', now()) and sdt.dtm_crt <= now() ");
		}
		return query.toString();
	}
	

	private String constructParamlovSdt(Map<String, Object> params, String stampDutyStatus) {
		StringBuilder query = new StringBuilder();

		if(StringUtils.isNotBlank(stampDutyStatus)) {
			query.append(" and mlovSDT.code = :sdtStatus ");
			params.put("sdtStatus", StringUtils.upperCase(stampDutyStatus));
		}
		return query.toString();
	}
	
	@Override
	public List<StampDutyBean> getListStampDuty(String tenantCode, String invoiceNo, String stampDutyStatus,
			String stampDutyNo, String businessLineCode, String officeCode, String regionCode,
			Date usedDateStart, Date usedDateEnd, int min, int max, long idBalanceType) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		
		String paramQueryTenant = this.constructParamTenant(params, tenantCode);		
		String paramQueryStampDuty = this.constructParamStampDuty(params, invoiceNo, 
				stampDutyNo, businessLineCode, officeCode,
				regionCode, usedDateStart, usedDateEnd);		
		String paramQueryLovSdt = this.constructParamlovSdt(params, stampDutyStatus);		

		StringBuilder query = new StringBuilder();
		query.append(" with sdtCTE as ")
			 .append(" ( ")
			 	.append(" select sdt.stamp_duty_no as \"stampDutyNo\", bm.ref_no as \"invoiceNo\", to_char(bm.trx_date, '"+GlobalVal.DATE_FORMAT+"') as \"invoiceDate\", ")
			 		   .append(" cast(sdt.stamp_duty_fee as varchar) as \"stampDutyFee\", mlovSDT.description as \"stampDutyStatusDesc\", ")
			 		   .append(" case when mlovSDT.code = '"+GlobalVal.CODE_LOV_SDT_GO_LIVE+"' then to_char(sdt.dtm_upd, '"+GlobalVal.DATE_TIME_FORMAT_SEC+"') else null end as \"stampDutyUsedDt\", ")
			 		   .append(" mlovSDT.code as \"stampDutyStatusCode\", v.vendor_code as \"vendorCode\", v.vendor_name as \"vendorName\", ")
			 		   .append(" cast(sdt.id_stamp_duty as varchar) as \"idStampDuty\", row_number() over (order by sdt.id_stamp_duty) as \"rowNum\", ")
			 		   .append(" mutation.office_name as \"officeName\", mutation.region_name as \"regionName\", mutation.business_line_name as \"businessLineName\" ")
			 	.append(" from tr_stamp_duty sdt ")
			 	.append(" join ms_tenant t on t.id_ms_tenant = sdt.id_ms_tenant ")
			 	.append(" join ms_vendor v on v.id_ms_vendor = sdt.id_ms_vendor ")
			 	.append(" join ms_lov mlovSDT on mlovSDT.id_lov = sdt.lov_stamp_duty_status ")
			 	.append(" join tr_balance_mutation bm on ")
			 		   .append(" t.id_ms_tenant = bm.id_ms_tenant and v.id_ms_vendor = bm.id_ms_vendor and ")
			 		   .append(" bm.lov_balance_type = "+idBalanceType+" and bm.trx_no = sdt.trx_no ")
			 	.append(" left join lateral ( ")
			 		   .append(" select office_code, office_name, region_code, region_name, business_line_code, business_line_name ")
			 		   .append(" from tr_balance_mutation tbm ")
			 		   .append(" join tr_document_h dh on dh.id_document_h = tbm.id_document_h ")
			 		   .append(" join ms_office mo on mo.id_ms_office = dh.id_ms_office ")
			 		   .append(" join ms_region mr on mr.id_ms_region = mo.id_ms_region ")
			 		   .append(" join ms_business_line bl on bl.id_ms_business_line = dh.id_ms_business_line ")
			 		   .append(" where tbm.id_stamp_duty = sdt.id_stamp_duty ")
			 		   .append(" order by tbm.trx_date desc ")
			 		   .append(" limit 1 ")
			 	.append(" ) mutation on true ")
			 	.append(" where 1 = 1 ")
			 		   .append(paramQueryTenant)
			 		   .append(paramQueryStampDuty)
			 		   .append(paramQueryLovSdt)
			 	.append(" ) ")
			 		   .append(" select * ")
			 		   .append(" from sdtCTE ")
			 		   .append(" where \"rowNum\" between :min and :max ");
		
		return this.managerDAO.selectForListString(StampDutyBean.class, query.toString(), params, null);
	}
	
	@Override
	public List<StampDutyBean> getListReverseTopup(String tenantCode, String invoiceNo, String stampDutyStatus, boolean isReversalSearchFilter) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder query = new StringBuilder();
		
		if (isReversalSearchFilter) {
			query.append(" select distinct ref_no ")
				 .append(" from tr_stamp_duty ")
				 .append(" join lateral ( select ref_no, id_ms_tenant ")
				 				.append(" from tr_balance_mutation bm ")
				 				.append(" where bm.trx_no = tr_stamp_duty.trx_no ) bm ON TRUE ")
				 .append(" join lateral ( select tenant_code from ms_tenant t ")
				 				.append(" where t.id_ms_tenant = bm.id_ms_tenant ) t ON TRUE ")
				 .append(" join lateral ( select code from ms_lov lov ")
				 				.append(" where lov.id_lov = lov_stamp_duty_status ) lov ON TRUE ")
				 .append(" where t.tenant_code = '"+tenantCode+"' and lov.code = '"+stampDutyStatus+"' ");
		} else {
			query.append(" select ref_no ")
				 .append(" , trx_no, TO_CHAR(trx_date, '"+GlobalVal.DATE_FORMAT+"') as trx_date, ")
				 .append(" cast(stamp_duty_fee as varchar) as stamp_duty_fee, vendor_code, vendor_name, ")
				 .append(" string_agg(qty, ',') as qty ")
				 .append(" from ( select trx_no, stamp_duty_fee, ")
				 	   .append(" CAST(count(id_stamp_duty) AS VARCHAR) as qty from tr_stamp_duty ")
				 	   .append(" join lateral ( select code from ms_lov lov ")
				 	   				  .append(" where lov.id_lov = lov_stamp_duty_status) lov ON TRUE ")
				 	   .append(" where trx_no in ( select distinct trx_no from tr_stamp_duty ")
				 	   					 .append(" join lateral ( select code from ms_lov lov ")
				 	   					 				.append(" where lov.id_lov = lov_stamp_duty_status) lov ON TRUE ")
				 	   					 .append(" where code = '"+stampDutyStatus+"' ) ")
				 	   .append(" group by trx_no, code, stamp_duty_fee ")
				 	   .append(" ) sdt")
				 .append(" join lateral ( select ref_no, trx_date, id_ms_tenant, id_ms_vendor from tr_balance_mutation bm ")
				 				.append(" where bm.trx_no = sdt.trx_no ) bm ON TRUE ")
				 .append(" join lateral ( select vendor_code, vendor_name from ms_vendor v ")
								.append(" where v.id_ms_vendor = bm.id_ms_vendor ) v ON TRUE ")
				 .append(" join lateral ( select tenant_code from ms_tenant t ")
				 				.append(" where t.id_ms_tenant = bm.id_ms_tenant ) t ON TRUE ")
				 .append(" where t.tenant_code = '"+tenantCode+"' ");
			if (StringUtils.isNotBlank(invoiceNo)) {
				query.append(" and bm.ref_no = '"+invoiceNo+"' ");
			}
			query.append(" group by ref_no, trx_no, trx_date, stamp_duty_fee, vendor_code, vendor_name ");
		}
		
		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), params);
		Iterator<Map<String, Object>> itr = result.iterator();
		List<StampDutyBean> listStampDutyBean = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();

			StampDutyBean bean = new StampDutyBean();
			bean.setInvoiceNo((String) map.get("d0"));
			if (!isReversalSearchFilter) {
				bean.setInvoiceDate((String) map.get("d2"));
				bean.setStampDutyFee((String) map.get("d3"));
				bean.setVendorCode((String) map.get("d4"));
				bean.setVendorName((String) map.get("d5"));
				String[] quantities = ((String) map.get("d6")).split(",");
				bean.setAvailableQty(new BigInteger(quantities[0]));
				if (quantities.length > 1) {
					bean.setUsedQty(new BigInteger(quantities[1]));
				} else {
					bean.setUsedQty(BigInteger.valueOf(0));
				}
			}
			
			listStampDutyBean.add(bean);
		}
		
		return listStampDutyBean;
	}

	@Override
	public int countListStampDuty(String tenantCode, String invoiceNo, String stampDutyStatus,
			String stampDutyNo, String businessLineCode, String officeCode, String regionCode,
			Date usedDateStart, Date usedDateEnd, long idBalanceType) {
		
		Map<String, Object> params = new HashMap<>();
		
		String paramQueryTenant = this.constructParamTenant(params, tenantCode);		
		String paramQueryStampDuty = this.constructParamStampDuty(params, invoiceNo,
				stampDutyNo, businessLineCode, officeCode,
				regionCode, usedDateStart, usedDateEnd);
		String paramQueryLovSdt = this.constructParamlovSdt(params, stampDutyStatus);		

		StringBuilder query = new StringBuilder();
		query.append(" with sdtCTE as ")
			 .append(" ( ")
			 	.append(" select sdt.id_stamp_duty, bm.ref_no as invoice_no, t.tenant_code, sdt.stamp_duty_no, mutation.business_line_code, ")
			 		   .append(" mutation.office_code, mutation.region_code, sdt.dtm_upd, sdt.dtm_crt, mlovSDT.code, mlovSDT.lov_group ")
			 	.append(" from tr_stamp_duty sdt ")
			 	.append(" join ms_tenant t on t.id_ms_tenant = sdt.id_ms_tenant ")
			 	.append(" join ms_vendor v on v.id_ms_vendor = sdt.id_ms_vendor ")
			 	.append(" join ms_lov mlovSDT on mlovSDT.id_lov = sdt.lov_stamp_duty_status ")
			 	.append(" join tr_balance_mutation bm on ")
			 		   .append(" t.id_ms_tenant = bm.id_ms_tenant and v.id_ms_vendor = bm.id_ms_vendor and ")
			 		   .append(" bm.lov_balance_type = "+idBalanceType+" and bm.trx_no = sdt.trx_no ")
			 	.append(" left join lateral ( ")
			 		   .append(" select office_code, office_name, region_code, region_name, business_line_code, business_line_name ")
			 		   .append(" from tr_balance_mutation tbm ")
			 		   .append(" join tr_document_h dh on dh.id_document_h = tbm.id_document_h ")
			 		   .append(" join ms_office mo on mo.id_ms_office = dh.id_ms_office ")
			 		   .append(" join ms_region mr on mr.id_ms_region = mo.id_ms_region ")
			 		   .append(" join ms_business_line bl on bl.id_ms_business_line = dh.id_ms_business_line ")
			 		   .append(" where tbm.id_stamp_duty = sdt.id_stamp_duty ")
			 		   .append(" order by tbm.trx_date desc ")
			 		   .append(" limit 1 ")
			 	.append(" ) mutation on true ")
			 	.append(" where 1 = 1 ")
			 		   .append(paramQueryTenant)
			 		   .append(paramQueryStampDuty)
			 		   .append(paramQueryLovSdt)
			 .append(" ) ")
			 	.append(" select count (distinct (sdtCTE.id_stamp_duty)) ")
			 	.append(" from sdtCTE ");
		
		List<Map<String, Object>> total = this.getManagerDAO().selectAllNativeString(query.toString(), params);

		String totalString = total.get(0).toString();
		int start = total.get(0).toString().indexOf("=");
		int end = total.get(0).toString().indexOf("}");
		String subString = totalString.substring(start + 1, end);

		return Integer.parseInt(subString);
	}

	@Override
	public List<StampDutyHistoryBean> getListStampDutyDetail(String tenantCode, Long idStampDuty) {
		Map<String, Object> params = new HashMap<>();
		params.put("idSdt", idStampDuty);
		
		String paramQueryTenant = this.constructParamTenant(params, tenantCode);		

		StringBuilder query = new StringBuilder();
		query.append("select bm.trx_no as \"trxNo\", ref_no as \"refNo\", sdt.stamp_duty_no as \"stampDutyNo\", trxType.description as \"trxType\", ")
			 .append("to_char(trx_date, 'DD-Mon-YYYY HH24:MI') as \"trxDate\", case when d.id_msuser_customer is not null then u.full_name else 'SYSTEM' end as \"custName\", ")
			 .append("case when bm.id_document_d is null or d.id_ms_doc_template is null then '' else d.doc_template_name end as \"docName\", ")
			 .append("trxType.description as \"description\" ")
			 .append("from tr_balance_mutation bm ")
			 .append("join ms_lov as trxType on trxType.id_lov = bm.lov_trx_type ")
			 .append("join tr_stamp_duty as sdt on sdt.id_stamp_duty = bm.id_stamp_duty ")
			 .append("join ms_tenant as t on t.id_ms_tenant = bm.id_ms_tenant ")
			 .append("left outer join (select id_document_d, dt.doc_template_name, d,id_ms_doc_template,h.id_msuser_customer ")
			 .append("                 from tr_document_d d ")
			 .append("                 left outer join ms_doc_template as dt on dt.id_doc_template = d.id_ms_doc_template ")
			 .append("                 left outer join tr_document_h h on h.id_document_h = d.id_document_h ")
			 .append("                 ) as d on d.id_document_d = bm.id_document_d ")
			 .append("left outer join am_msuser as u on u.id_ms_user = d.id_msuser_customer ")
			 .append("where bm.id_stamp_duty is not null and bm.id_stamp_duty = :idSdt ")
			 .append(paramQueryTenant)
			 .append("order by trx_date desc, trxType.code asc ");
		
		return this.managerDAO.selectForListString(StampDutyHistoryBean.class, query.toString(), params, null);
	}

	@Override
	public Object getLatestStampDutyDetailTrxType (Long idStampDuty) {
		StringBuilder query = new StringBuilder();
		query.append("select trxType.code from tr_balance_mutation bm ")
			 .append("join ms_lov trxType on trxType.id_lov = bm.lov_trx_type ")
			 .append("where id_stamp_duty = :idSDT ")
			 .append("order by trx_date desc limit 1 ");
		Map<String, Object> params = new HashMap<>();
		params.put("idSDT", idStampDuty);
		
		return this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public void updateNativeStringTrStampDuty(TrStampDuty stampDuty) {
		Object[][] params = new Object[][] {
			{TRX_NO, StringUtils.upperCase(stampDuty.getTrxNo())},
			{STAMPDUTY_NO, StringUtils.upperCase(stampDuty.getStampDutyNo())},
			{"lovStampDutyStatus", stampDuty.getMsLov().getIdLov()},
			{"usrUpd", MssTool.maskData(stampDuty.getUsrUpd())},
			{"dtmUpd", stampDuty.getDtmUpd()},
			{MsTenant.ID_TENANT_HBM, stampDuty.getMsTenant().getIdMsTenant()},
			{MsVendor.ID_VENDOR_HBM, stampDuty.getMsVendor().getIdMsVendor()},
			{STAMPDUTY_FEE, stampDuty.getStampDutyFee()},
			{TrStampDuty.ID_STAMP_DUTY_HBM, stampDuty.getIdStampDuty()}
		};
		
		StringBuilder query = new StringBuilder();
		query
			.append("update tr_stamp_duty ")
			.append("set trx_no = :trxNo, ")
			.append("stamp_duty_no = :stampDutyNo, ")
			.append("lov_stamp_duty_status = :lovStampDutyStatus, ")
			.append("usr_upd = :usrUpd, ")
			.append("dtm_upd = :dtmUpd, ")
			.append("id_ms_tenant = :idMsTenant, ")
			.append("id_ms_vendor = :idMsVendor, ")
			.append("stamp_duty_fee = :stampDutyFee ")
			.append("where id_stamp_duty = :idStampDuty ");
		
		this.managerDAO.updateNativeString(query.toString(), params);
	}

	@Override
	public void deleteTrStampDuty(List<TrStampDuty> listTrStampDuty) {
		this.managerDAO.delete(listTrStampDuty, listTrStampDuty.size());
	}

	@Override
	public void deleteListStampDutyByTenantCodeAndInvoiceNoAndStampDutyStatus(String tenantCode, String vendorCode, String invoiceNo, String stampDutyStatus) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder query = new StringBuilder();
		query.append(" delete from tr_stamp_duty ")
			 .append(" using tr_balance_mutation, ms_lov ")
			 .append(" join lateral ( select vendor_code from ms_vendor ")
			 				.append(" where ms_vendor.id_ms_vendor = tr_balance_mutation.id_ms_vendor ) ms_vendor ON TRUE ")
			 .append(" join lateral ( select tenant_code from ms_tenant ")
			 				.append(" where ms_tenant.id_ms_tenant = tr_balance_mutation.id_ms_tenant ) ms_tenant ON TRUE ")
			 .append(" where tenant_code = '"+tenantCode+"' and tr_balance_mutation.ref_no = '"+invoiceNo+"' ")
			 .append(" and vendor_code='"+vendorCode+"' and ms_lov.code = '"+stampDutyStatus+"' ")
			 .append(" and tr_balance_mutation.trx_no = tr_stamp_duty.trx_no ")
			 .append(" and ms_lov.id_lov = tr_stamp_duty.lov_stamp_duty_status ");		
		this.managerDAO.deleteNativeString(query.toString(), params);
		
	}

	@Override
	public List<Map<String, Object>> getListStampDutyByIdDocumentH(Long idDocumentH) {
		Object[][] params = new Object[][] {{ "idDocumentH", idDocumentH }};
		
		StringBuilder query = new StringBuilder();
		query
			.append(" select sd.id_stamp_duty ")
			.append(" from tr_balance_mutation bm ")
			.append(" join tr_stamp_duty sd on bm.id_stamp_duty = sd.id_stamp_duty ")
			.append(" where id_document_h = :idDocumentH ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
		
	}

	@Override
	public StampDutyBean getStampDutyByInvoiceNo(String invoiceNo) {
		Object[][] params = new Object[][] {{INVOICE_NO, invoiceNo }};
		
		StringBuilder query = new StringBuilder();
		query.append("select distinct on (invoice_no) invoice_no as \"invoiceNo\", vendor_code as \"vendorCode\" ")
			 .append("from tr_stamp_duty sdt ")
			 .append("join ms_tenant as t on t.id_ms_tenant = sdt.id_ms_tenant ")
			 .append("join lateral (Select bm.ref_no as invoice_no ")
			 .append("              from tr_balance_mutation bm ")
			 .append("              where bm.trx_no = sdt.trx_no) bm ON TRUE ")
			 .append("join lateral (Select vendor_code,vendor_name from ms_vendor ")
			 .append("              where ms_vendor.id_ms_vendor = sdt.id_ms_vendor) ms_vendor ON TRUE ")
			 .append("where invoice_no like :invoiceNo ");
		

		return this.managerDAO.selectForObjectString(StampDutyBean.class, query.toString(), params);
	}

	@Override
	public void insertTrStampDuty(String loginId, Long idMsLovSdtStatus, Long idMsTenant, Long idMsVendor, Integer fee, String trxNo, Integer qty) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		
		params.put("usrCrt", MssTool.maskData(loginId));
		params.put("idMsLovSdtStatus", idMsLovSdtStatus);
		params.put("idMsTenant", idMsTenant);
		params.put("idMsVendor", idMsVendor);
		params.put("fee", fee);
		params.put(TRX_NO, trxNo);
		params.put("qty", qty);
		
		query
			.append(" insert into tr_stamp_duty (dtm_crt, usr_crt, dtm_upd, usr_upd, ")
			.append(" lov_stamp_duty_status, id_ms_tenant, id_ms_vendor, stamp_duty_fee, stamp_duty_no, trx_no) ")
			.append(" select now(), :usrCrt, null, null, :idMsLovSdtStatus, :idMsTenant, ")
			.append(" :idMsVendor, :fee, generate_no_meterai(21), :trxNo ")
			.append(" from generate_series(1, :qty) ");
		
		this.managerDAO.insertNativeString(query.toString(), params);		
	}

	@Override
	public List<StampDutyReportBean> getListStampDutyReport(String tenantCode, String invoiceNo, String stampDutyStatus,
			String stampDutyNo, String businessLineCode, String officeCode, String regionCode, Date usedDateStart,
			Date usedDateEnd) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		String queryParams = this.constructStampDutyReportParam(params, tenantCode, invoiceNo, stampDutyStatus,
				stampDutyNo, businessLineCode, officeCode, regionCode, usedDateStart, usedDateEnd);
		
		query
			.append(" select sd.stamp_duty_no as \"stampDutyNo\", bm.ref_no as \"noKontrak\", ")
			.append(" CASE WHEN sdtStatus.lov_group = 'STAMP_DUTY_STATUS' and sdtStatus.code = 'GO LIVE' THEN TO_CHAR(sd.dtm_upd, 'dd Mon yyyy') ")
			.append(" ELSE TO_CHAR(bm.trx_date, 'dd Mon yyyy') END as \"stampDutyUsedDt\", ")
			.append(" CAST(sd.stamp_duty_fee as CHARACTER VARYING) as \"stampDutyFee\", sdtStatus.description as \"stampDutyStatusDesc\", ")
			.append(" dt.doc_template_name as \"docName\", mu.full_name as \"custName\", ")
			.append(" mo.office_name as \"officeName\", mr.region_name as \"regionName\", bl.business_line_name as \"businessLineName\" ")
			.append(" from tr_stamp_duty sd ")
			.append(" join ms_tenant mt on sd.id_ms_tenant = mt.id_ms_tenant ")
			.append(" join tr_balance_mutation bm on sd.id_stamp_duty = bm.id_stamp_duty and bm.notes = sd.stamp_duty_no ")
			.append(" join tr_document_d dd on bm.id_document_d = dd.id_document_d ")
			.append(" join ms_doc_template dt on dd.id_ms_doc_template = dt.id_doc_template ")
			.append(" join tr_document_h dh on bm.id_document_h = dh.id_document_h ")
			.append(" join am_msuser mu on dh.id_msuser_customer = mu.id_ms_user ")
			.append(" join ms_business_line bl on dh.id_ms_business_line = bl.id_ms_business_line ")
			.append(" join ms_office mo on dh.id_ms_office = mo.id_ms_office ")
			.append(" join ms_region mr on mo.id_ms_region = mr.id_ms_region ")
			.append(" join ms_lov sdtStatus on sd.lov_stamp_duty_status = sdtStatus.id_lov ")
			.append(queryParams)
			.append(" order by bm.trx_date asc ");
		return this.managerDAO.selectForListString(StampDutyReportBean.class, query.toString(), params, null);
	}
	
	private String constructStampDutyReportParam(Map<String, Object> params, String tenantCode, String invoiceNo,
			String stampDutyStatus, String stampDutyNo, String businessLineCode,
			String officeCode, String regionCode, Date usedDateStart, Date usedDateEnd) {
		
		StringBuilder queryParams = new StringBuilder();
		if (StringUtils.isNotBlank(tenantCode)) {
			queryParams.append(" and mt.tenant_code = :tenantCode ");
			params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		}
		if (StringUtils.isNotBlank(invoiceNo)) {
			queryParams.append(" and bm.ref_no LIKE :invoiceNo ");
			params.put(INVOICE_NO,  "%" + StringUtils.upperCase(invoiceNo) + "%");
		}
		if (StringUtils.isNotBlank(stampDutyStatus)) {
			queryParams.append(" and sdtStatus.code = :statusCode ");
			params.put("statusCode", StringUtils.upperCase(stampDutyStatus));
		}
		if (StringUtils.isNotBlank(stampDutyNo)) {
			queryParams.append(" and UPPER(sd.stamp_duty_no) LIKE :stampDutyNo ");
			params.put(STAMPDUTY_NO, StringUtils.upperCase(stampDutyNo));
		}
		if (StringUtils.isNotBlank(businessLineCode)) {
			queryParams.append(" and bl.business_line_code = :businessLineCode ");
			params.put("businessLineCode", StringUtils.upperCase(businessLineCode));
		}
		if (StringUtils.isNotBlank(officeCode)) {
			queryParams.append(" and mo.office_code = :officeCode ");
			params.put("officeCode", StringUtils.upperCase(officeCode));
		}
		if (StringUtils.isNotBlank(regionCode)) {
			queryParams.append(" and mr.region_code = :regionCode ");
			params.put("regionCode", StringUtils.upperCase(regionCode));
		}
		if(usedDateStart != null && usedDateEnd != null) {
			queryParams.append("and sd.dtm_upd >= :dateStart and sd.dtm_upd <= :dateEnd ");
			params.put("dateStart", usedDateStart);
			params.put("dateEnd", usedDateEnd);
		} else {
			queryParams
				.append(" and case when sd.dtm_upd is not null then ( ")
				.append(" sd.dtm_upd >= date_trunc('MONTH', now()) and sd.dtm_upd <= now()) ")
				.append(" else sd.dtm_crt <= now() end ");
		}
		
		return queryParams.toString();
	}

	@Override
	public BigInteger countAvailableStampDutyForDocument(Long idDocumentD) {
		StringBuilder query = new StringBuilder();
		Object[][] params = new Object[][] {{ TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD }};
		
		query
			.append(" select count(*) from tr_stamp_duty sd ")
			.append(" join lateral ( ")
				.append(" select id_lov from ms_lov ")
				.append(" where lov_group = '" + GlobalVal.LOV_GROUP_SDT_STATUS + "' ")
				.append(" and code = '" + GlobalVal.CODE_LOV_SDT_AVAILABLE + "' ")
				.append(" and sd.lov_stamp_duty_status = id_lov ")
			.append(" ) status on true ")
			.append(" join lateral ( ")
				.append(" select id_balance_mutation ")
				.append(" from tr_balance_mutation bm ")
				.append(" where bm.id_stamp_duty = sd.id_stamp_duty ")
//				.append(" and bm.notes = sd.stamp_duty_no ")
				.append(" and bm.qty = -1 ")
				.append(" and bm.id_document_d = :idDocumentD ")
				.append(" limit 1 ")
			.append(" ) mutation on true ");
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getIdStampDutyForDocument(Long idDocumentD) {
		StringBuilder query = new StringBuilder();
		Object[][] params = new Object[][] { 
			{TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD},
			{"balTypeGroup", GlobalVal.LOV_GROUP_BALANCE_TYPE},
			{"balTyleCode", GlobalVal.CODE_LOV_BALANCE_TYPE_SDT},
			{"trxTypegroup", GlobalVal.LOV_GROUP_TRX_TYPE},
			{"trxTypeCode", GlobalVal.CODE_LOV_TRX_TYPE_USDT}
		};
		
		query
			.append(" select bm.id_stamp_duty ")
			.append(" from tr_balance_mutation bm ")
			.append(" join ms_lov balType on bm.lov_balance_type = balType.id_lov ")
			.append(" join ms_lov trxType on bm.lov_trx_type = trxType.id_lov ")
			.append(" where bm.id_document_d = :idDocumentD ")
			.append(" and balType.lov_group = :balTypeGroup and balType.code = :balTyleCode ")
			.append(" and trxType.lov_group = :trxTypegroup and trxType.code = :trxTypeCode ")
			.append(" order by bm.id_stamp_duty asc ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertTrStampDutyNewTran(TrStampDuty stampDuty) {
		stampDuty.setUsrCrt(MssTool.maskData(stampDuty.getUsrCrt()));
		this.managerDAO.insert(stampDuty);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateNativeStringTrStampDutyNewTran(TrStampDuty stampDuty) {
		Object[][] params = new Object[][] {
			{TRX_NO, StringUtils.upperCase(stampDuty.getTrxNo())},
			{STAMPDUTY_NO, StringUtils.upperCase(stampDuty.getStampDutyNo())},
			{"lovStampDutyStatus", stampDuty.getMsLov().getIdLov()},
			{"usrUpd", MssTool.maskData(stampDuty.getUsrUpd())},
			{"dtmUpd", stampDuty.getDtmUpd()},
			{MsTenant.ID_TENANT_HBM, stampDuty.getMsTenant().getIdMsTenant()},
			{MsVendor.ID_VENDOR_HBM, stampDuty.getMsVendor().getIdMsVendor()},
			{STAMPDUTY_FEE, stampDuty.getStampDutyFee()},
			{TrStampDuty.ID_STAMP_DUTY_HBM, stampDuty.getIdStampDuty()}
		};
		
		StringBuilder query = new StringBuilder();
		query
			.append("update tr_stamp_duty ")
			.append("set trx_no = :trxNo, ")
			.append("stamp_duty_no = :stampDutyNo, ")
			.append("lov_stamp_duty_status = :lovStampDutyStatus, ")
			.append("usr_upd = :usrUpd, ")
			.append("dtm_upd = :dtmUpd, ")
			.append("id_ms_tenant = :idMsTenant, ")
			.append("id_ms_vendor = :idMsVendor, ")
			.append("stamp_duty_fee = :stampDutyFee ")
			.append("where id_stamp_duty = :idStampDuty ");
		
		this.managerDAO.updateNativeString(query.toString(), params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateTrStampDutyNewTran(TrStampDuty stampDuty) {
		stampDuty.setUsrUpd(MssTool.maskData(stampDuty.getUsrUpd()));
		this.managerDAO.update(stampDuty);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<Map<String, Object>> getIdStampDutyForDocumentNewTran(TrDocumentD document) {
		StringBuilder query = new StringBuilder();
		String balanceTypeCode = null;
		String trxTypeCode = null;
		if ("1".equals(document.getTrDocumentH().getIsPostpaidStampduty())) {
			balanceTypeCode = GlobalVal.CODE_LOV_BALANCE_TYPE_SDT_POSTPAID;
			trxTypeCode = GlobalVal.CODE_LOV_TRX_TYPE_USDT_POSTPAID;
		} else {
			balanceTypeCode = GlobalVal.CODE_LOV_BALANCE_TYPE_SDT;
			trxTypeCode = GlobalVal.CODE_LOV_TRX_TYPE_USDT;
		}
		
		Object[][] params = new Object[][] { 
			{TrDocumentD.ID_DOCUMENT_D_HBM, document.getIdDocumentD()},
			{"balTypeGroup", GlobalVal.LOV_GROUP_BALANCE_TYPE},
			{"balTypeCode", balanceTypeCode},
			{"trxTypegroup", GlobalVal.LOV_GROUP_TRX_TYPE},
			{"trxTypeCode", trxTypeCode}
		};
		
		query
			.append(" select bm.id_stamp_duty ")
			.append(" from tr_balance_mutation bm ")
			.append(" join ms_lov balType on bm.lov_balance_type = balType.id_lov ")
			.append(" join ms_lov trxType on bm.lov_trx_type = trxType.id_lov ")
			.append(" where bm.id_document_d = :idDocumentD ")
			.append(" and balType.lov_group = :balTypeGroup and balType.code = :balTypeCode ")
			.append(" and trxType.lov_group = :trxTypegroup and trxType.code = :trxTypeCode ")
			.append(" order by bm.id_stamp_duty asc ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public TrStampDuty getLatestStampDuty(TrDocumentD document) {
		boolean isPostapid = "1".equals(document.getTrDocumentH().getIsManualUpload());
		
		Object[][] balanceTypeParams = {
				{Restrictions.eq(MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_BALANCE_TYPE)},
				{Restrictions.eq(MsLov.CODE_HBM, isPostapid ? GlobalVal.CODE_LOV_BALANCE_TYPE_SDT_POSTPAID : GlobalVal.CODE_LOV_BALANCE_TYPE_SDT)}
		};
		MsLov lovBalanceType = managerDAO.selectOne(MsLov.class, balanceTypeParams);
		
		Object[][] trxTypeParams = {
				{Restrictions.eq(MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_TRX_TYPE)},
				{Restrictions.eq(MsLov.CODE_HBM, isPostapid ? GlobalVal.CODE_LOV_TRX_TYPE_USDT_POSTPAID : GlobalVal.CODE_LOV_TRX_TYPE_USDT)}
		};
		MsLov lovTrxType = managerDAO.selectOne(MsLov.class, trxTypeParams);
		
		Map<String, Object> params = new HashMap<>();
		params.put("lovBalanceType", lovBalanceType.getIdLov());
		params.put("lovTrxType", lovTrxType.getIdLov());
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, document.getIdDocumentD());
		params.put(TrDocumentH.ID_DOCUMENT_H_HBM, document.getTrDocumentH().getIdDocumentH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_stamp_duty from tr_balance_mutation ")
			.append("where id_document_h = :idDocumentH ")
			.append("and id_document_d = :idDocumentD ")
			.append("and lov_balance_type = :lovBalanceType ")
			.append("and lov_trx_type = :lovTrxType")
			.append("order by id_balance_mutation desc limit 1 ");
		
		BigInteger idStampDuty = (BigInteger) managerDAO.selectOneNative(query.toString(), params);
		return managerDAO.selectOne(TrStampDuty.class, new Object[][] {{ Restrictions.eq(TrStampDuty.ID_STAMP_DUTY_HBM, idStampDuty.longValue()) }});
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<StampDutyBean> getListStampDutyByIdDocumentHNewTran(long documentHeaderId) {
		Map<String, Object> params = new HashMap<>();
        StringBuilder query = new StringBuilder();
        	query.append(" SELECT DISTINCT stamp_duty_no AS \"stampDutyNo\" ")
        	.append(" FROM tr_stamp_duty sd  ")
        	.append(" JOIN tr_balance_mutation bm ON sd.id_stamp_duty = bm.id_stamp_duty ")
        	.append(" WHERE id_document_h = :documentHeaderId ");

        	params.put("documentHeaderId", documentHeaderId);

        return this.managerDAO.selectForListString(StampDutyBean.class, query.toString(), params, null);
		
	}


	
}
