package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.esign.dataaccess.api.UseroftenantDao;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class UseroftenantDaoHbn extends BaseDaoHbn implements UseroftenantDao {
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertUseroftenantNewTran(MsUseroftenant useroftenant) {
		useroftenant.setUsrCrt(MssTool.maskData(useroftenant.getUsrCrt()));
		this.managerDAO.insert(useroftenant);
	}
	
	@Override
	public MsUseroftenant getUserTenantByLoginIdAndTenantCode(String loginId, String tenantCode) {
		Object[][] params = new Object[][] {
			{AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId)},
			{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
				"from MsUseroftenant ut "
				+ "join fetch ut.amMsuser u "
				+ "join fetch ut.msTenant t "
				+ "where u.loginId = :loginId and t.tenantCode = :tenantCode ", 
				params);
	}
	
	@Override
	public MsUseroftenant getUserTenantByIdMsUserAndTenantCode(Long idMsUser, String tenantCode) {
		Object[][] params = new Object[][] {
			{AmMsuser.ID_MS_USER_HBM, idMsUser},
			{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
				"from MsUseroftenant ut "
				+ "join fetch ut.amMsuser u "
				+ "join fetch ut.msTenant t "
				+ "where u.idMsUser = :idMsUser and t.tenantCode = :tenantCode ", 
				params);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsUseroftenant getUserTenantByIdMsUserAndTenantCodeNewTran(Long idMsUser, String tenantCode) {
		Object[][] params = new Object[][] {
			{AmMsuser.ID_MS_USER_HBM, idMsUser},
			{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
				"from MsUseroftenant ut "
				+ "join fetch ut.amMsuser u "
				+ "join fetch ut.msTenant t "
				+ "where u.idMsUser = :idMsUser and t.tenantCode = :tenantCode ", 
				params);
	}
	
	@Override
	public MsUseroftenant getLatestUserTenant(String loginId) {
		StringBuilder query = new StringBuilder();
		query
			.append(" select ut.id_ms_useroftenant from ms_useroftenant ut ")
			.append(" join am_msuser mu on ut.id_ms_user = mu.id_ms_user ")
			.append(" where mu.login_id = :loginId ")
			.append(" order by ut.dtm_crt desc limit 1 ");
		
		BigInteger idMsUseroftenant =  (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), 
				new Object[][] {{ AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId) }});
		
		if (null == idMsUseroftenant) {
			return null;
		}
		
		Object[][] params = new Object[][] {{ "idMsUseroftenant", idMsUseroftenant.longValue() }};
		
		return this.managerDAO.selectOne(
				"from MsUseroftenant ut " 
				+ "join fetch ut.amMsuser u "
				+ "join fetch ut.msTenant t "
				+ "where ut.idMsUseroftenant = :idMsUseroftenant ", params);
	}

	@Override
	public MsUseroftenant getLatestUseroftenant(AmMsuser user, MsVendor vendor) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select uot.id_ms_useroftenant ")
			.append("from ms_useroftenant uot ")
			.append("join ms_vendoroftenant vot on uot.id_ms_tenant = vot.id_ms_tenant ")
			.append("where uot.id_ms_user = :idMsUser ")
			.append("and vot.id_ms_vendor = :idMsVendor ")
			.append("order by uot.dtm_crt desc limit 1 " );
		
		BigInteger idUseroftenant = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idUseroftenant) {
			return null;
		}
		
		return managerDAO.selectOne(
				"from MsUseroftenant ut " 
						+ "join fetch ut.amMsuser u "
						+ "join fetch ut.msTenant t "
						+ "where ut.idMsUseroftenant = :idUseroftenant ", new Object[][] {{"idUseroftenant", idUseroftenant.longValue()}});
	}
}
