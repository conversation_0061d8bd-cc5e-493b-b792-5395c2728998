package com.adins.am.model.custom;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
public abstract class ActiveDeleteAndUpdateableEntity extends ActiveAndUpdateableEntity {
	protected String isDeleted;
	public static final String IS_DELETED_HBM = "isDeleted";

	@Column(name = "is_deleted", length = 1)
	public String getIsDeleted() {
		return this.isDeleted;
	}

	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}
}
