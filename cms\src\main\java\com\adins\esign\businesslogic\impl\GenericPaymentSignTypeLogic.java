package com.adins.esign.businesslogic.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.PaymentSignTypeLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.custom.PaymentSignTypeBean;
import com.adins.esign.webservices.model.GetListPaymentTypeRequest;
import com.adins.esign.webservices.model.PaymentSignTypeListRequest;
import com.adins.esign.webservices.model.PaymentSignTypeListResponse;
import com.adins.exceptions.PaymentSignTypeException;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.PaymentSignTypeException.ReasonPaymentSignType;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericPaymentSignTypeLogic extends BaseLogic implements PaymentSignTypeLogic {

	@Override
	public PaymentSignTypeListResponse getPaymentSignTypeList(PaymentSignTypeListRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new PaymentSignTypeException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_PAYMENT_SIGN_TYPE_TENANT_EMPTY_TENANT,
					null, this.retrieveLocaleAudit(audit)), ReasonPaymentSignType.TENANT_CODE_EMPTY);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new PaymentSignTypeException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_PAYMENT_SIGN_TYPE_TENANT_NOT_FOUND,
					null, this.retrieveLocaleAudit(audit)), ReasonPaymentSignType.TENANT_NOT_FOUND);
		}
		
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(audit.getCallerId());
		if (null == user) {
			throw new PaymentSignTypeException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND,
					null, this.retrieveLocaleAudit(audit)), ReasonPaymentSignType.USER_NOT_FOUND);
		}
		
		PaymentSignTypeListResponse response = new PaymentSignTypeListResponse();
		List<PaymentSignTypeBean> paymentBeanList = new ArrayList<>();
		
		List<Map<String, Object>> paymentList = daoFactory.getPaymentSignTypeDao().getListPaymentSignTypeByTenantCode(request.getTenantCode());
		Iterator<Map<String, Object>> itr = paymentList.iterator();
		
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			PaymentSignTypeBean bean = new PaymentSignTypeBean();
			bean.setCode((String) map.get("d0"));
			bean.setDescription((String) map.get("d1"));
			paymentBeanList.add(bean);
		}
		
		response.setPaymentSignTypeList(paymentBeanList);
		return response;
	}

	@Override
	public PaymentSignTypeListResponse getListPaymentType(GetListPaymentTypeRequest request, AuditContext audit) {
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new PaymentSignTypeException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_PAYMENT_SIGN_TYPE_TENANT_NOT_FOUND,
					null, this.retrieveLocaleAudit(audit)), ReasonPaymentSignType.TENANT_NOT_FOUND);
		}
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		if(null == vendor) {
			throw new VendorException( this.messageSource.getMessage("businesslogic.saldo.vendornotexist", new Object[] {request.getVendorCode()},
							this.retrieveLocaleAudit(audit)),ReasonVendor.VENDOR_NOT_FOUND);
		}
		
		PaymentSignTypeListResponse response = new PaymentSignTypeListResponse();
		List<PaymentSignTypeBean> paymentBeanList = new ArrayList<>();
		
		List<Map<String, Object>> paymentList = daoFactory.getPaymentSignTypeDao().getListPaymentSignTypeByTenantCodeAndVendorCode(tenant.getTenantCode(), vendor.getVendorCode());
		Iterator<Map<String, Object>> itr = paymentList.iterator();
		
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			PaymentSignTypeBean bean = new PaymentSignTypeBean();
			bean.setCode((String) map.get("d0"));
			bean.setDescription((String) map.get("d1"));
			paymentBeanList.add(bean);
		}
		
		response.setPaymentSignTypeList(paymentBeanList);
		return response;
	}

}