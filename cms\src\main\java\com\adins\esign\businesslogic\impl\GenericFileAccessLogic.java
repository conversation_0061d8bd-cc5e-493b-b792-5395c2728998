package com.adins.esign.businesslogic.impl;

import java.io.File;
import java.io.IOException;

import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.FileAccessLogic;
import com.adins.esign.model.TrDocumentD;

@Component
public class GenericFileAccessLogic extends BaseLogic implements FileAccessLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericFileAccessLogic.class);
	
	@Value("${emeterai.onprem.dir.unsigned}") private String unsignedDir;
	@Value("${emeterai.onprem.dir.signed}") private String signedDir;
	@Value("${emeterai.onprem.dir.stamp}") private String stampDir;
	
	/**
	 * Writes a byte array to a file creating the file if it does not exist.
	 * If exists, file will be overwritten
	 */
	private void saveFile(byte[] fileContent, String directory, String filename) throws IOException {
		File dir = new File(directory);
		File file = new File(dir, filename);
		if (file.exists()) {
			LOG.warn("File {} existed, file overridden", file.getAbsolutePath());
		}
		FileUtils.writeByteArrayToFile(file, fileContent, false);
		LOG.info("File {} saved", file.getAbsolutePath());
	}
	
	/**
	 * Reads the contents of a file into a byte array
	 */
	private byte[] readFile(String directory, String filename) throws IOException {
		File dir = new File(directory);
		File file = new File(dir, filename);
		if (!file.exists()) {
			LOG.info("File {} does not exist", file.getAbsolutePath());
			return null;
		}
		LOG.info("Reading {}", file.getAbsolutePath());
		return FileUtils.readFileToByteArray(file);
	}
	
	/**
	 * @return {@code true} if the file or directory was deleted, otherwise {@code false}
	 */
	private boolean deleteFile(String directory, String filename) {
		File dir = new File(directory);
		File file = new File(dir, filename);
		LOG.info("Deleting {}", file.getAbsolutePath());
		return FileUtils.deleteQuietly(file);
	}

	@Override
	public void storeBaseStampDocument(byte[] fileContent, TrDocumentD document) throws IOException {
		String filename = document.getDocumentId() + ".pdf";
		LOG.info("Kontrak {}, Dokumen {}, Upload document for stamping with filename {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), filename);
		saveFile(fileContent, unsignedDir, filename);
	}

	@Override
	public byte[] getBaseStampDocument(TrDocumentD document) throws IOException {
		String filename = document.getDocumentId() + ".pdf";
		return readFile(unsignedDir, filename);
	}

	@Override
	public boolean deleteBaseStampDocument(TrDocumentD document) {
		String filename = document.getDocumentId() + ".pdf";
		return deleteFile(unsignedDir, filename);
	}

	@Override
	public void storeStampQr(byte[] imageContent, String serialNumber) throws IOException {
		String filename = serialNumber + ".png";
		LOG.info("Storing meterai QR with filename {}", filename);
		saveFile(imageContent, stampDir, filename);
	}

	@Override
	public byte[] getStampQr(String serialNumber) throws IOException {
		String filename = serialNumber + ".png";
		return readFile(stampDir, filename);
	}

	@Override
	public boolean deleteStampQr(String serialNumber) {
		String filename = serialNumber + ".png";
		return deleteFile(stampDir, filename);
	}

	@Override
	public byte[] getStampedDocument(TrDocumentD document) throws IOException {
		String filename  = document.getDocumentId() + ".pdf";
		return readFile(signedDir, filename);
	}

	@Override
	public boolean deleteStampedDocument(TrDocumentD document) {
		String backupFilename = document.getDocumentId() + ".pdf.bckp";
		deleteFile(signedDir, backupFilename);
		
		String filename = document.getDocumentId() + ".pdf";
		return deleteFile(signedDir, filename);
	}

	@Override
	public boolean isStampedDocumentExists(TrDocumentD document) {
		String filename = document.getDocumentId() + ".pdf";
		File dir = new File(signedDir);	
		File file = new File(dir, filename);
		return file.exists();
	}

	@Override
	public boolean deleteStampedDocumentbckp(TrDocumentD document) {
		String backupFilename = document.getDocumentId() + ".pdf.bckp";
		return deleteFile(signedDir, backupFilename);
	}
}
