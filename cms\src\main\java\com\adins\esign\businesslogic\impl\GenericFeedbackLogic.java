package com.adins.esign.businesslogic.impl;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.FeedbackLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrFeedback;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.FeedbackEmbedRequest;
import com.adins.esign.webservices.model.FeedbackRequest;
import com.adins.esign.webservices.model.embed.FeedbackHybridEmbedRequest;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericFeedbackLogic extends BaseLogic implements FeedbackLogic {
	
	@Autowired private DocumentLogic documentLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private EmbedValidatorLogic embedValidatorLogic;
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericFeedbackLogic.class);

	@Override
	public void insertFeedbackEmbed(FeedbackEmbedRequest request, AuditContext audit) {
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		boolean checkUserExistence = false;
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(msgBean.getEmail(), checkUserExistence, audit);
		TrDocumentD docD = null;
		if (StringUtils.isNotBlank(request.getEncryptedDocumentId())) {
			String documentId = commonLogic.decryptMessageToString(request.getEncryptedDocumentId(), audit);
			docD = documentLogic.getDocumentDetailByDocumentIdEmbed(documentId);
			if(docD == null) {
				throw new IllegalArgumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_FEEDBACK_INVALIDDOCID,	new Object[] {documentId}, this.retrieveLocaleAudit(audit)));
			}
		}
		
		this.insertFeed(user, docD, request.getFeedbackValue(), request.getComment(), audit);
	}
	
	private void insertFeed(AmMsuser user, TrDocumentD docD, short feedbackValue,  String comment, AuditContext audit) {
		if(feedbackValue < 1 || feedbackValue > 5) {
			throw new IllegalArgumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_FEEDBACK_INVALIDVALUE,	null, this.retrieveLocaleAudit(audit)));
		}
		
		TrFeedback feedback = new TrFeedback();
		feedback.setUsrCrt(audit.getCallerId());
		feedback.setDtmCrt(new Date());
		feedback.setFeedbackValue(feedbackValue);
		feedback.setComment(comment);
		feedback.setAmMsuser(user);
		if (null != docD) {
			feedback.setTrDocumentD(docD);
		}
		daoFactory.getFeedbackDao().insertFeedbackNewTrx(feedback);
	}
	
	private void insertFeedHybrid(AmMsuser user, TrDocumentD docD, short feedbackValue,  String comment, AuditContext audit) {
		if(feedbackValue < 1 || feedbackValue > 5) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_FEEDBACK_INVALIDVALUE,  null, this.retrieveLocaleAudit(audit)), 
					ReasonDocument.PARAM_INVALID);
		}
		
		TrFeedback feedback = new TrFeedback();
		feedback.setUsrCrt(audit.getCallerId());
		feedback.setDtmCrt(new Date());
		feedback.setFeedbackValue(feedbackValue);
		feedback.setComment(comment);
		feedback.setAmMsuser(user);
		feedback.setTrDocumentD(docD);
		daoFactory.getFeedbackDao().insertFeedback(feedback);
	}

	@Override
	public void insertFeedbackHybridEmbed(FeedbackHybridEmbedRequest request, AuditContext audit) {
		EmbedMsgBeanV2 msgBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), true, audit);
		AmMsuser user = msgBean.getAmMsuser();
		TrDocumentD docD = null;
		if (StringUtils.isNotBlank(request.getEncryptedDocumentId())) {
			String documentId = commonLogic.decryptMessageToString(request.getEncryptedDocumentId(), msgBean.getMsTenant().getAesEncryptKey(), audit);
			docD = documentLogic.getDocumentDetailByDocumentIdEmbed(documentId);
			if(docD == null) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_FEEDBACK_INVALIDDOCID, new Object[] {documentId}, this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
			}
		}
		
		this.insertFeedHybrid(user, docD, request.getFeedbackValue(), request.getComment(), audit);
	}

	@Override
	public void insertFeedback(FeedbackRequest request, AuditContext audit) {
		
		tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2NewTrx(request.getLoginId(), true, audit);
		
		try {
			TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getEncryptedDocumentId());
			insertFeed(user, document, request.getFeedbackValue(), request.getComment(), audit);
		} catch (Exception e) {
			LOG.error("Failed to insert feedback with exception: {}", e.getLocalizedMessage(), e);
		}
		
	}

}
