package com.adins.esign.webservices.embed.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.embed.SaldoEmbedLogic;
import com.adins.esign.webservices.embed.api.SaldoEmbedService;
import com.adins.esign.webservices.model.embed.SignBalanceAvailabilityEmbedRequest;
import com.adins.esign.webservices.model.embed.SignBalanceAvailabilityEmbedResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/embed/saldo")
@Api(value = "SaldoEmbedService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericSaldoEmbedServiceEndpoint implements SaldoEmbedService {
	
	@Autowired private SaldoEmbedLogic saldoEmbedLogic;

	@Override
	@POST
	@Path("/SignBalanceAvailabilityEmbed")
	public SignBalanceAvailabilityEmbedResponse getSignBalanceAvailabilityEmbed(SignBalanceAvailabilityEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return saldoEmbedLogic.getSignBalanceAvailabilityEmbed(request, audit);
	}

}
