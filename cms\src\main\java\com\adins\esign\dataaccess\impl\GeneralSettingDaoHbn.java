package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.dataaccess.api.GeneralSettingDao;
import com.adins.esign.model.MsTenant;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class GeneralSettingDaoHbn extends BaseDaoHbn implements GeneralSettingDao{

	@Override
	public AmGeneralsetting getGsObjByCode(String gsCode) {
		if(StringUtils.isBlank(gsCode)) {
			return null;
		}
		
		Object[][] queryParams = { { Restrictions.eq("gsCode", StringUtils.upperCase(gsCode))}, 
									{ Restrictions.eq(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1")} };
		
		return this.managerDAO.selectOne(AmGeneralsetting.class, queryParams);
	}
	
	@Override
	public AmGeneralsetting getGsObjById(long id) {
		if(0 == id) {
			return null;
		}
		
		Object[][] queryParams = { { Restrictions.eq("idGeneralSetting", id)}, 
				{ Restrictions.eq(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1")} };
		
		return this.managerDAO.selectOne(AmGeneralsetting.class, queryParams);
	}
	
	@Override
	public String getGsValueByCode(String gsCode) {
		if(StringUtils.isBlank(gsCode)) {
			return StringUtils.EMPTY;
		}
		
		Object[][] queryParams = { { Restrictions.eq("gsCode", StringUtils.upperCase(gsCode))}, 
									{ Restrictions.eq(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1")} };
    	AmGeneralsetting result = this.managerDAO.selectOne(AmGeneralsetting.class, queryParams);
		if(null != result){
	    	return result.getGsValue();
		}
		else {
			return StringUtils.EMPTY;
		}
	}

	@Override
	public void insertGs(AmGeneralsetting insertedGs) {
		insertedGs.setUsrCrt(MssTool.maskData(insertedGs.getUsrCrt()));
		this.managerDAO.insert(insertedGs);
	}
	
	@Override
	public void updateGs(AmGeneralsetting updGs) {
		updGs.setUsrUpd(MssTool.maskData(updGs.getUsrUpd()));
		this.managerDAO.update(updGs);
	}

	@Override
	public AmGeneralsetting getGsObjByCodeAndTenant(String gsCode, MsTenant tenant) {
		Map<String, Object> params = new HashMap<>();
		params.put(AmGeneralsetting.GS_CODE_HBM, StringUtils.upperCase(gsCode));
		params.put("msTenant", tenant);
		
		return this.managerDAO.selectOne(
				"from AmGeneralsetting gs "
				+ "join fetch gs.msTenant mt "
				+ "where gs.gsCode = :gsCode and gs.msTenant = :msTenant ", params);
	}

	@Override
	public List<AmGeneralsetting> getListGsObjByCode(String gsCode) {
		Object[][] queryParams = {{AmGeneralsetting.GS_CODE_HBM, StringUtils.upperCase(gsCode)}};
		return (List<AmGeneralsetting>) this.managerDAO.list(" from AmGeneralsetting gs "
				+ " "
				+ " where gs.gsCode = :gsCode ",queryParams 
				).get(GlobalKey.MAP_RESULT_LIST);
	}
}
