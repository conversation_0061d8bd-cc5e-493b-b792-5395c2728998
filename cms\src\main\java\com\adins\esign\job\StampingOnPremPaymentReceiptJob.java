package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.interfacing.PaymentReceiptOnPremStampingLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class StampingOnPremPaymentReceiptJob {
	private static final Logger LOG = LoggerFactory.getLogger(StampingOnPremPaymentReceiptJob.class);
	
	@Autowired private PaymentReceiptOnPremStampingLogic prStampingLogic;
	
	public void runStampingOnPremPaymentReceipt() {
		try {
			LOG.info("Job On-Premise payment receipt stamping started");
			AuditContext audit = new AuditContext(GlobalVal.SCHEDULER);
			prStampingLogic.retryStampingAllPaymentReceipt(audit);
			LOG.info("Job On-Premise payment receipt stamping finished");
		} catch (Exception e) {
			LOG.error("Job On-Premise payment receipt stamping error", e);
		}
		
	}
}
