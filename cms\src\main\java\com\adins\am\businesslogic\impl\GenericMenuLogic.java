package com.adins.am.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.businesslogic.api.MenuLogic;
import com.adins.am.model.AmMenuofrole;
import com.adins.am.model.AmMsmenu;
import com.adins.am.model.AmMsrole;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.custom.ListMenuBean;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.RoleValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.webservices.model.GetListMenuOfRoleRequest;
import com.adins.esign.webservices.model.GetListMenuResponse;
import com.adins.esign.webservices.model.UpdateMenuOfRoleRequest;
import com.adins.exceptions.MenuException;
import com.adins.exceptions.MenuException.ReasonMenu;
import com.adins.exceptions.StatusCode;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Component
@Transactional
public class GenericMenuLogic extends BaseLogic implements MenuLogic {
	
	@Autowired RoleValidatorLogic roleValidatorLogic;
	@Autowired CommonValidatorLogic commonValidatorLogic;
	@Autowired TenantValidatorLogic tenantValidatorLogic;

	@Override
	public List<AmMsmenu> getMenuListByIdRole (long idMsRole, AuditContext callerId) {
		return daoFactory.getMenuDao().getMenuByIdRole(idMsRole);
	}

	@Override
	public void insertMenuOfRole(AmMsmenu menu, AmMsrole role, AuditContext audit) {
		AmMenuofrole menuOfRole = new AmMenuofrole();
		menuOfRole.setAmMsmenu(menu);
		menuOfRole.setAmMsrole(role);
		menuOfRole.setDtmCrt(new Date());
		menuOfRole.setUsrCrt(audit.getCallerId());
		daoFactory.getMenuDao().insertMenuOfRole(menuOfRole);
	}

	@Override
	public MssResponseType updateMenuOfRole(UpdateMenuOfRoleRequest request, AuditContext audit) {
		tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsrole role = roleValidatorLogic.validateGetRole(request.getTenantCode(), request.getRoleCode(), true,false, audit);
		
		List<AmMsmenu> menuList = new ArrayList<>();
		for (String menuCode : request.getMenus()) {
			AmMsmenu menu = daoFactory.getMenuDao().getMenuByCode(menuCode);
			validateMenu(menu, menuCode, audit);
			menuList.add(menu);
			
			if (null != menu.getIdParentMsMenu()) {
				AmMsmenu parent = daoFactory.getMenuDao().getMenuById(menu.getIdParentMsMenu());
				if (!menuList.contains(parent)) {
					menuList.add(parent);
				}
			}
		}
		
		daoFactory.getMenuDao().deleteMenuOfRoleByIdRole(role.getIdMsRole());
		
		for (AmMsmenu menu : menuList) {
			AmMenuofrole mor = new AmMenuofrole();
			mor.setAmMsmenu(menu);
			mor.setAmMsrole(role);
			mor.setDtmCrt(new Date());
			mor.setUsrCrt(audit.getCallerId());
			
			daoFactory.getMenuDao().insertMenuOfRole(mor);
		}
		
		Status status = new Status();
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		MssResponseType response = new MssResponseType();
		response.setStatus(status);
		return response;
	}
	
	void validateMenu(AmMsmenu menu, String menuCode, AuditContext audit) {
		
		commonValidatorLogic.validateNotNull(menu, getMessage("businesslogic.menu.menunotfound", new Object[] {menuCode}, audit), StatusCode.MENU_NOT_FOUND);
		
		if (!"1".equals(menu.getIsActive())) {
			throw new MenuException(getMessage("businesslogic.menu.menunotactive", new Object[] {menu.getMenuPrompt()}, audit), ReasonMenu.NOT_ACTIVE);
		}
		
		if (!"1".equals(menu.getIsManageable())) {
			throw new MenuException(getMessage("businesslogic.menu.menunotmanageable", new Object[] {menu.getMenuPrompt()}, audit), ReasonMenu.NOT_MANAGEABLE);
		}
		
		if (StringUtils.isBlank(menu.getPath())) {
			throw new MenuException(getMessage("businesslogic.menu.pathempty", new Object[] {menu.getMenuPrompt()}, audit), ReasonMenu.EMPTY_PATH);
		}
	}

	@Override
	public GetListMenuResponse getListManageableMenu(AuditContext audit) {
		GetListMenuResponse response = new GetListMenuResponse();
		
		List<ListMenuBean> menus = daoFactory.getMenuDao().getListManageableMenu();
		
		response.setMenus(menus);
		
		Status status = new Status();
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}

	@Override
	public GetListMenuResponse getListMenuOfRole(GetListMenuOfRoleRequest request, AuditContext audit) {
		tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsrole role = roleValidatorLogic.validateGetRole(request.getTenantCode(), request.getRoleCode(), true,true, audit);
		GetListMenuResponse response = new GetListMenuResponse();
		
		List<ListMenuBean> menus = daoFactory.getMenuDao().getListMenuOfRole(role.getIdMsRole());
		response.setMenus(menus);
		
		Status status = new Status();
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}

}
