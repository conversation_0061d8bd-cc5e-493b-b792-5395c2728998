package com.adins.esign.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.am.model.custom.CreateableEntity;

@Entity
@Table(name = "tr_message_delivery_report")
public class TrMessageDeliveryReport extends CreateableEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public static final String ID_MESSAGE_DELIVERY_REPORT_HBM = "idMessageDeliveryReport";

	private long idMessageDeliveryReport;
	private MsLov msLov;
	private MsVendor msVendor;
	private MsTenant msTenant;
	private String trxNo;
	private String recipientDetail;
	private String deliveryStatus;
	private Date reportTime;
	private String vendorTrxNo;
	private Date requestTime;
	private MsLov lovMessageGateway;
	private MsLov lovCredentialType;
	private MsLov lovSendingPoint;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_message_delivery_report", unique = true, nullable = false)
	public long getIdMessageDeliveryReport() {
		return idMessageDeliveryReport;
	}

	public void setIdMessageDeliveryReport(long idMessageDeliveryReport) {
		this.idMessageDeliveryReport = idMessageDeliveryReport;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_message_media", nullable = false)
	public MsLov getMsLov() {
		return msLov;
	}

	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_vendor", nullable = false)
	public MsVendor getMsVendor() {
		return msVendor;
	}

	public void setMsVendor(MsVendor msVendor) {
		this.msVendor = msVendor;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@Column(name = "trx_no", length = 45)
	public String getTrxNo() {
		return trxNo;
	}

	public void setTrxNo(String trxNo) {
		this.trxNo = trxNo;
	}

	@Column(name = "recipient_detail", length = 36)
	public String getRecipientDetail() {
		return recipientDetail;
	}

	public void setRecipientDetail(String recipientDetail) {
		this.recipientDetail = recipientDetail;
	}

	@Column(name = "delivery_status", length = 36)
	public String getDeliveryStatus() {
		return deliveryStatus;
	}

	public void setDeliveryStatus(String deliveryStatus) {
		this.deliveryStatus = deliveryStatus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "report_time", length = 29)
	public Date getReportTime() {
		return reportTime;
	}

	public void setReportTime(Date reportTime) {
		this.reportTime = reportTime;
	}

	@Column(name = "vendor_trx_no", length = 45)
	public String getVendorTrxNo() {
		return vendorTrxNo;
	}

	public void setVendorTrxNo(String vendorTrxNo) {
		this.vendorTrxNo = vendorTrxNo;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "request_time", length = 29)
	public Date getRequestTime() {
		return requestTime;
	}

	public void setRequestTime(Date requestTime) {
		this.requestTime = requestTime;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_message_gateway")
	public MsLov getLovMessageGateway() {
		return lovMessageGateway;
	}

	public void setLovMessageGateway(MsLov lovMessageGateway) {
		this.lovMessageGateway = lovMessageGateway;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_credential_type")
	public MsLov getLovCredentialType() {
		return lovCredentialType;
	}

	public void setLovCredentialType(MsLov lovCredentialType) {
		this.lovCredentialType = lovCredentialType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_sending_point")
	public MsLov getLovSendingPoint() {
		return lovSendingPoint;
	}

	public void setLovSendingPoint(MsLov lovSendingPoint) {
		this.lovSendingPoint = lovSendingPoint;
	}

}
