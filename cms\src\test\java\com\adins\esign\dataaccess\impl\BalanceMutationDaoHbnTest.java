package com.adins.esign.dataaccess.impl;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.BalanceMutationDao;
import com.adins.esign.dataaccess.api.StampDutyDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.custom.BalanceBean;
import com.adins.esign.model.custom.BalanceMutationBean;
import com.adins.esign.util.MssTool;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.AuditDataType;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class BalanceMutationDaoHbnTest {
	@Autowired private BalanceMutationDao balMutDao;
	@Autowired private StampDutyDao sdtDao;
	@Autowired private CommonLogic commonLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private VendorLogic vendorLogic;
	
	private AuditDataType auditData;
	private AuditContext auditContext;

	private MsLov balanceType;
	private MsLov transType;
	private MsLov docType;
	private MsTenant tenantJunit;
	private MsVendor vendorJunit;
	
	private String refNoJunit;
	private String documentNameJunit;
	private Date dateStartJunit;
	private Date dateEndJunit;
	
	@BeforeEach
	public void setUp() {
		auditData = new AuditDataType();
		auditData.setCallerId("INITIAL");
		
		auditContext = new AuditContext(auditData.getCallerId());
		
		balanceType = this.commonLogic.getLovByGroupAndCode(
				GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_DOC,auditContext);
		
		transType= this.commonLogic.getLovByGroupAndCode(
				GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UDOC,auditContext);
		
		docType= this.commonLogic.getLovByGroupAndCode(
				GlobalVal.LOV_GROUP_DOC_TYPE, GlobalVal.DOC_TYPE_AGREEMENT,auditContext);
		
		tenantJunit = tenantLogic.getTenantByCode("WOMF", auditContext);
		vendorJunit = vendorLogic.getVendorByCode(GlobalVal.VENDOR_CODE_DIGISIGN, auditContext);
		
		refNoJunit = "";
		documentNameJunit = "A";
		dateStartJunit = MssTool.formatStringToDate("2022-01-05", GlobalVal.DATE_FORMAT);
		dateEndJunit = MssTool.formatStringToDate("2022-02-05", GlobalVal.DATE_FORMAT);
	}
	
//	@Test
//	void getStampDutyBalanceMutation() {
//		TrStampDuty sdt = sdtDao.getStampDutyById(3089410);
//		TrBalanceMutation bm = balMutDao.getStampDutyBalanceMutation(sdt);
//		assertNotNull(bm);
//	}
	
	@Order(1)
	@Test
	void getListBalanceMutationTest() {
		List<BalanceMutationBean> listBalMut = balMutDao.getListBalanceMutation(tenantJunit, vendorJunit, 
				balanceType, transType.getCode(), dateStartJunit, dateEndJunit, 
				docType.getCode(), refNoJunit, documentNameJunit, 1, 100, "", null);
		assertNotEquals(0, listBalMut.size());
	}
	
	@Order(1)
	@Test
	void countBalanceMutationTest() {
		int countBalMut = balMutDao.countBalanceMutation(tenantJunit, vendorJunit, 
				balanceType, transType.getCode(), dateStartJunit, dateEndJunit, 
				docType.getCode(), refNoJunit, documentNameJunit, "", null);
		assertNotEquals(0, countBalMut);
	}
	
	@Order(1)
	@Test
	void getSingleBalanceByVendorAndTenantTest() {
		BigInteger currentBalance =  balMutDao.getSingleBalanceByVendorAndTenant(tenantJunit, vendorJunit, balanceType);
		assertNotEquals(0, currentBalance.intValue());
	}
	
	@Order(1)
	@Test
	void getBalanceByVendorAndTenantWithBalTypeTest() {
		List<BalanceBean> currentBalanceList =  balMutDao.getBalanceByVendorAndTenant
				(tenantJunit.getTenantCode(), vendorJunit.getVendorCode(), balanceType.getCode());
		assertNotEquals(0, currentBalanceList.size());
	}
	
	@Order(1)
	@Test
	void getBalanceByVendorAndTenantNoBalTypeTest() {
		List<BalanceBean> currentBalanceList =  balMutDao.getBalanceByVendorAndTenant
				(tenantJunit.getTenantCode(), vendorJunit.getVendorCode(), null);
		assertNotEquals(0, currentBalanceList.size());
	}
}
