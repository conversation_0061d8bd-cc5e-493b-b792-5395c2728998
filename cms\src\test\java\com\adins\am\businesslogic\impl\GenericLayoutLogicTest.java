package com.adins.am.businesslogic.impl;

import static org.junit.Assert.assertNull;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.api.LayoutLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class GenericLayoutLogicTest {
	@Autowired private LayoutLogic layoutLogic;
	
	private AuditContext auditContext = new AuditContext();
	
	@BeforeEach
	public void setUp() {
		auditContext.setCallerId("JUNIT");
	}
	
	@Test
	void logUserEventNullTest() {
		List<Map<String, Object>> menuTree = layoutLogic.retrieveMenuTree(auditContext);
		assertNull(menuTree);
	}

}
