package com.adins.esign.businesslogic.api;

import java.io.IOException;
import java.text.ParseException;
import java.util.Map;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.webservices.model.CreateMateraiRequest;
import com.adins.esign.webservices.model.CreateMateraiResponse;
import com.adins.esign.webservices.model.TaxExcelReportRequest;
import com.adins.esign.webservices.model.InquiryStampDutyDetailRequest;
import com.adins.esign.webservices.model.InquiryStampDutyDetailResponse;
import com.adins.esign.webservices.model.InquiryStampDutyRequest;
import com.adins.esign.webservices.model.InquiryStampDutyResponse;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiEmbedRequest;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiRequest;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiResponse;
import com.adins.esign.webservices.model.ReverseTopupRequest;
import com.adins.esign.webservices.model.ReverseTopupResponse;
import com.adins.esign.webservices.model.StampDutyExcelReportRequest;
import com.adins.esign.webservices.model.StampDutyExcelReportResponse;
import com.adins.esign.webservices.model.TaxExcelReportResponse;
import com.adins.esign.webservices.model.UpdateStampDutyStatusRequest;
import com.adins.esign.webservices.model.UpdateStampDutyStatusResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface StampDutyLogic {
	TrStampDuty getStampDutyById(long idStampDuty);
	Map<String, Object> getStampDutyByIdTenant(long idMsTenant);
	long countAvailableStampDutyByIdTenant(long idMsTenant);

	void updateStampDutyStatus(TrStampDuty stampDuty, String codeLovStampDutyStatus, AuditContext audit);
	
	@RolesAllowed({"ROLE_STAMP_DUTY"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	CreateMateraiResponse createMaterai(CreateMateraiRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_STAMP_DUTY"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	InquiryStampDutyResponse getListStampDuty(InquiryStampDutyRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_STAMP_DUTY"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	InquiryStampDutyDetailResponse getStampDutyDetail(InquiryStampDutyDetailRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_STAMP_DUTY"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	InquiryStampDutyResponse getListReverseTopup(InquiryStampDutyRequest request, AuditContext audit) throws ParseException;
	
	@RolesAllowed({"ROLE_STAMP_DUTY"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ReverseTopupResponse reverseTopupMaterai(ReverseTopupRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_STAMP_DUTY"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	StampDutyExcelReportResponse exportStampDutyReport(StampDutyExcelReportRequest request, AuditContext audit);
	
	UpdateStampDutyStatusResponse updateStampDutyStatus(UpdateStampDutyStatusRequest request, AuditContext audit) throws IOException;
	UpdateStampDutyStatusResponse attachMeteraiRKG(UpdateStampDutyStatusRequest request, AuditContext audit) throws IOException;
	UpdateStampDutyStatusResponse attachMeteraiPajakku(UpdateStampDutyStatusRequest request, String apiKey, AuditContext audit) throws IOException;
	
	@RolesAllowed({"ROLE_EMETERAI_MONITORING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ListMonitoringEmeteraiResponse getListMonitoringEmeterai(ListMonitoringEmeteraiRequest request, AuditContext audit) throws ParseException;
	ListMonitoringEmeteraiResponse getListMonitoringEmeteraiEmbed(ListMonitoringEmeteraiEmbedRequest request, AuditContext audit) throws ParseException;
	TaxExcelReportResponse exportTaxReport(TaxExcelReportRequest request, AuditContext audit) throws ParseException;
	
	@RolesAllowed({"ROLE_EMETERAI_MONITORING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	TaxExcelReportResponse exportTaxReportNormal(TaxExcelReportRequest request, AuditContext audit) throws ParseException;
	
	boolean isAgreementStamped(TrDocumentH documentH);
}
