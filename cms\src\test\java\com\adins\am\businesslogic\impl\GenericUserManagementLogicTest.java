package com.adins.am.businesslogic.impl;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.model.AmMsuser;
import com.adins.esign.dataaccess.factory.api.DaoFactory;
import com.adins.constants.AmGlobalKey;

@Disabled
@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class GenericUserManagementLogicTest {
    @Autowired
	private DaoFactory daoFactory;	

    @Order(1)
    @Test
    void listUserTest() {
    	int days = daoFactory.getUserDao().getDateDiffPassHistByIdMsUser(5);
		
		String generalSettingPwdExpired = daoFactory.getGeneralSettingDao().getGsValueByCode(
				AmGlobalKey.GENERALSETTING_PASSWORD_EXPIRED);
		System.out.println((Integer.parseInt(generalSettingPwdExpired) < days));
    }
 
}
