package com.adins.am.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.am.model.custom.UpdateableEntity;
import com.adins.esign.model.TrUserDataAccessLog;
import com.adins.esign.model.custom.ZipcodeCityBean;

@Entity
@Table(name = "am_user_personal_data_history")
public class AmUserPersonalDataHistory extends UpdateableEntity implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private long idUserPersonalDataHistory;
	private TrUserDataAccessLog trUserDataAccessLog;
	private AmMsuser amMsuser;
	private String gender;
	private Date dateOfBirth;
	private String placeOfBirth;
	private String email;
	private byte[] photoSelf;
	private byte[] idNoBytea;
	private byte[] phoneBytea;
	private byte[] addressBytea;
	private byte[] photoIdBytea;
	
	@Embedded
	private transient ZipcodeCityBean zipcodeBean;
	

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_user_personal_data_history", unique = true, nullable = false)
	public long getIdUserPersonalDataHistory() {
		return idUserPersonalDataHistory;
	}

	public void setIdUserPersonalDataHistory(long idUserPersonalDataHistory) {
		this.idUserPersonalDataHistory = idUserPersonalDataHistory;
	}

	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_user_access_data_log")
	public TrUserDataAccessLog getTrUserDataAccessLog() {
		return trUserDataAccessLog;
	}

	public void setTrUserDataAccessLog(TrUserDataAccessLog trUserDataAccessLog) {
		this.trUserDataAccessLog = trUserDataAccessLog;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@Column(name = "gender", length = 2)
	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "date_of_birth", length = 13)
	public Date getDateOfBirth() {
		return this.dateOfBirth;
	}

	public void setDateOfBirth(Date dateOfBirth) {
		this.dateOfBirth = dateOfBirth;
	}

	@Column(name = "photo_self")
	public byte[] getPhotoSelf() {
		return this.photoSelf;
	}

	public void setPhotoSelf(byte[] photoSelf) {
		this.photoSelf = photoSelf;
	}

	@Column(name = "place_of_birth", length = 100)
	public String getPlaceOfBirth() {
		return this.placeOfBirth;
	}

	public void setPlaceOfBirth(String placeOfBirth) {
		this.placeOfBirth = placeOfBirth;
	}


	@Column(name = "email", length = 80)
	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	public ZipcodeCityBean getZipcodeBean() {
		return zipcodeBean;
	}

	public void setZipcodeBean(ZipcodeCityBean zipcodeBean) {
		this.zipcodeBean = zipcodeBean;
	}
	
	@Column(name = "id_no_bytea")
	public byte[] getIdNoBytea() {
		return this.idNoBytea;
	}

	public void setIdNoBytea(byte[] idNoBytea) {
		this.idNoBytea = idNoBytea;
	}

	@Column(name = "phone_bytea")
	public byte[] getPhoneBytea() {
		return this.phoneBytea;
	}

	public void setPhoneBytea(byte[] phoneBytea) {
		this.phoneBytea = phoneBytea;
	}
	
	@Column(name = "address_bytea")
	public byte[] getAddressBytea() {
		return this.addressBytea;
	}

	public void setAddressBytea(byte[] addressBytea) {
		this.addressBytea = addressBytea;
	}

	@Column(name = "photo_id_bytea")
	public byte[] getPhotoIdBytea() {
		return this.photoIdBytea;
	}

	public void setPhotoIdBytea(byte[] photoIdBytea) {
		this.photoIdBytea = photoIdBytea;
	}
}
