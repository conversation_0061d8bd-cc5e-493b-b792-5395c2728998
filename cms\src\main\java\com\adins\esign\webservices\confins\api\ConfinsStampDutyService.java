package com.adins.esign.webservices.confins.api;

import java.io.IOException;
import java.text.ParseException;

import com.adins.esign.webservices.model.UpdateStampDutyStatusRequest;
import com.adins.esign.webservices.model.UpdateStampDutyStatusResponse;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptRequest;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptResponse;

public interface ConfinsStampDutyService {
	
	UpdateStampDutyStatusResponse attachMeteraiPajakku(UpdateStampDutyStatusRequest request) throws IOException;
	InsertStampingPaymentReceiptResponse insertStampingPaymentReceipt(InsertStampingPaymentReceiptRequest request) throws ParseException;
	InsertStampingPaymentReceiptResponse insertStampingPaymentReceiptDummy(InsertStampingPaymentReceiptRequest request) throws ParseException;
}
