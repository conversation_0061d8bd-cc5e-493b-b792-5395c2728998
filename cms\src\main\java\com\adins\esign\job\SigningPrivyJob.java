package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class SigningPrivyJob {
private static final Logger LOG = LoggerFactory.getLogger(SigningPrivyJob.class);
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void runSigningPrivy() {
		try {
			LOG.info("Sign Privy job started");
			AuditContext audit = new AuditContext("SIGN PRIVY SCHEDULER");
			schedulerLogic.signPrivy(audit);
			LOG.info("Sign Privy job finished");
		} catch (Exception e) {
			LOG.error("Error on running sign Privy job", e);
		}
	}
}
