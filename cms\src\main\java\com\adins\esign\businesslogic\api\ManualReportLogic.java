package com.adins.esign.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.webservices.model.AddManualReportRequest;
import com.adins.esign.webservices.model.DeleteManualReportRequest;
import com.adins.esign.webservices.model.DownloadManualReportRequest;
import com.adins.esign.webservices.model.DownloadManualReportResponse;
import com.adins.esign.webservices.model.GetListManualReportRequest;
import com.adins.esign.webservices.model.GetListManualReportResponse;
import com.adins.esign.webservices.model.GetListReportRequest;
import com.adins.esign.webservices.model.GetListReportResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface ManualReportLogic {
	@RolesAllowed({"ROLE_MANUAL_REPORT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	GetListManualReportResponse getList(GetListManualReportRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_MANUAL_REPORT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	public MssResponseType saveManualReport(AddManualReportRequest request, AuditContext audit) ;
	
	@RolesAllowed({"ROLE_DOWNLOAD_REPORT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	GetListReportResponse getListForDownload(GetListReportRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_DOWNLOAD_REPORT", "ROLE_MANUAL_REPORT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	DownloadManualReportResponse download(DownloadManualReportRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_DOWNLOAD_REPORT", "ROLE_MANUAL_REPORT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	public MssResponseType deleteManualReport(DeleteManualReportRequest request , AuditContext audit );
}
