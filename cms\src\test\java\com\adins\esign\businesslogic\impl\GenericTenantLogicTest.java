package com.adins.esign.businesslogic.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

import java.util.List;

import javax.transaction.Transactional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.TestSetUpLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.custom.GeneralSettingBean;
import com.adins.esign.model.custom.MenuUserBean;
import com.adins.framework.persistence.dao.model.AuditContext;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class GenericTenantLogicTest extends BaseLogic{
	@Autowired private TenantLogic tenantLogic;
//	@Autowired private TestSetUpLogic testSetUpLogic;
//	
	private AuditContext auditContext = new AuditContext();
//	private AmGeneralsetting generalSetting;
//	private MsLov lov;
//	private AmMsuser user;
//	
	@BeforeEach
	@Rollback(true)
	public void setUp() {
		auditContext.setCallerId("1");
//		generalSetting = testSetUpLogic.setUpGenSet("JUNIT");
//		user = daoFactory.getUserDao().getUserByLoginId(GlobalVal.USER_ADMIN_LOGIN_ID);
	}

	@Test
	@Rollback(true)
	void getTenantByIdTest( ) {
		tenantLogic.getTenantById(0, auditContext);
//		assertFalse(listGeneralSetting.isEmpty());
	}
	
	@Test
	void getListTenantTest( ) {
		assertNotEquals(0, tenantLogic.getListTenant(auditContext));
	}
	
	@Test
	@Rollback(true)
	void getListTenantByUserTest( ) {
//		tenantLogic.getListTenantByUser(null, auditContext);
//		List<MenuUserBean> listMenuUser = commonLogic.listMenuByUserId(user.getUuidMsUser(), auditContext);
//		assertFalse(listMenuUser.isEmpty());
	}
	
}
