package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class MessageDeliveryExepction extends AdInsException {
	private static final long serialVersionUID = 1L;
	
	public enum ReasonMessageDelivery {
		RANGE_DATE_INVALID
	}
	
	private final ReasonMessageDelivery reason;
	
	public MessageDeliveryExepction(String message, ReasonMessageDelivery reason) {
		super(message);
		this.reason = reason;
	}
	
	public MessageDeliveryExepction(Throwable ex, ReasonMessageDelivery reason) {
		super(ex);
		this.reason = reason;
	}
	
	public MessageDeliveryExepction(String message, Throwable ex, ReasonMessageDelivery reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	public ReasonMessageDelivery getReason() {
		return reason;
	}
	
	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
			case RANGE_DATE_INVALID:
				return StatusCode.INVALID_DATE_RANGE;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}
}
