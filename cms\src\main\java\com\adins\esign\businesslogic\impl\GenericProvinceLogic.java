package com.adins.esign.businesslogic.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.ProvinceLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.ProvinceBean;
import com.adins.esign.model.custom.ProvinceExternalBean;
import com.adins.esign.validatorlogic.api.InvitationLinkValidatorLogic;
import com.adins.esign.webservices.model.GetProvinceByInvitationRequest;
import com.adins.esign.webservices.model.GetProvinceEmbedRequest;
import com.adins.esign.webservices.model.GetProvinceRequest;
import com.adins.esign.webservices.model.GetProvinceResponse;
import com.adins.esign.webservices.model.external.GetProvinceListExternalRequest;
import com.adins.esign.webservices.model.external.GetProvinceListExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericProvinceLogic extends BaseLogic implements ProvinceLogic{
	
	@Autowired private CommonLogic commonLogic;
	
	@Autowired private InvitationLinkValidatorLogic invLinkValidatorLogic;
	@Autowired private TenantLogic tenantLogic;

	@Override
	public GetProvinceResponse getProvinceList(GetProvinceRequest request, AuditContext audit) {
		GetProvinceResponse response = new GetProvinceResponse();
		List<ProvinceBean> provinceList = daoFactory.getProvinceDao().getProvinceList(StringUtils.upperCase(request.getProvinceName()));
		
		if(provinceList.isEmpty()) {
			List<ProvinceBean> provinceListAll= daoFactory.getProvinceDao().getProvinceList(null);
			response.setProvinceList(provinceListAll);
			return response;
		}
		response.setProvinceList(provinceList);
		return response;
	}

	@Override
	public GetProvinceResponse getProvinceListEmbed(GetProvinceEmbedRequest request, AuditContext audit) {
		commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		
		GetProvinceRequest provinceListRequest = new GetProvinceRequest();
		provinceListRequest.setProvinceName(request.getProvinceName());
		
		return this.getProvinceList(provinceListRequest, audit);
	}

	@Override
	public GetProvinceResponse getProvinceListInvReg(GetProvinceByInvitationRequest request, AuditContext audit) {
		
		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);
		
		GetProvinceRequest provinceListRequest = new GetProvinceRequest();
		provinceListRequest.setProvinceName(request.getProvinceName());
		
		return this.getProvinceList(provinceListRequest, audit);
	}

	@Override
	public GetProvinceListExternalResponse getProvinceListExternal(GetProvinceListExternalRequest request,
			String apiKey, AuditContext audit) {
		
		tenantLogic.getTenantFromXApiKey(apiKey, audit);
		
		List<ProvinceExternalBean> provinceList = daoFactory.getProvinceDao().getProvinceExternalList();
		GetProvinceListExternalResponse response = new GetProvinceListExternalResponse();
		response.setProvinceList(provinceList);
		return response;
	}

	

}
