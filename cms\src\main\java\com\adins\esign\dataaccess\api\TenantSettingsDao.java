package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.custom.TenantSettingsBean;
import com.adins.esign.webservices.model.ListTenantSettingsRequest;

public interface TenantSettingsDao {
	MsTenantSettings getTenantSettings(MsTenant tenant, String lovTenantSettingCode);
	List<TenantSettingsBean> getListTenantSettings(ListTenantSettingsRequest request, long idMsTenant, int min, int max);
	long countListTenantSettings(ListTenantSettingsRequest request, long idMsTenant);
	void inserMsTenantSettings(MsTenantSettings tenantSettings);
	void updateMsTenantSettings(MsTenantSettings tenantSettings);
}
