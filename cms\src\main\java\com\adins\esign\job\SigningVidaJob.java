package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class SigningVidaJob extends BaseLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(SigningVidaJob.class);
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void runSigningVida() {
		try {
			LOG.info("Sign VIDA job started");
			AuditContext audit = new AuditContext("SIGN SCHEDULER");
			schedulerLogic.signVida(audit);
			LOG.info("Sign VIDA job finished");
		} catch (Exception e) {
			LOG.error("Error on running sign VIDA job", e);
		}
	}
}
