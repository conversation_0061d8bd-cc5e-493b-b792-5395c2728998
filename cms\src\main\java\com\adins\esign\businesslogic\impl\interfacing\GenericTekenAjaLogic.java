package com.adins.esign.businesslogic.impl.interfacing;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import javax.persistence.EntityNotFoundException;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.ContentDisposition;
import org.apache.cxf.jaxrs.ext.multipart.MultipartBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsdistrict;
import com.adins.am.model.AmMsprovince;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMssubdistrict;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserPersonalData;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.interfacing.TekenAjaLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.TekenAjaConstant;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsEmailHosting;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.custom.ManualSignBean;
import com.adins.esign.model.custom.ManualSignerBean;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.SignatureBean;
import com.adins.esign.model.custom.SignatureDetailBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.model.custom.TknajBulkSignResponse;
import com.adins.esign.model.custom.TknajDownloadDocResponse;
import com.adins.esign.model.custom.TknajRegisterCekResponse;
import com.adins.esign.model.custom.TknajUplDocResponse;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.ZipcodeCityBean;
import com.adins.esign.model.custom.tekenaja.HashSignDocumentListBean;
import com.adins.esign.model.custom.tekenaja.TekenAjaLocationBean;
import com.adins.esign.model.custom.tekenaja.TekenAjaRegisterResponseBean;
import com.adins.esign.model.custom.tekenaja.TekenAjaUserBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.DistrictValidatorLogic;
import com.adins.esign.validatorlogic.api.ProvinceValidatorLogic;
import com.adins.esign.validatorlogic.api.SubdistrictValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.esign.webservices.model.DocumentConfinsRequestBean;
import com.adins.esign.webservices.model.InsertDocumentManualSignRequest;
import com.adins.esign.webservices.model.TekenAjaSignBulkRequest;
import com.adins.esign.webservices.model.TekenAjaSignRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.tekenaja.DownloadTekenAjaLinkResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaDownloadCertificateRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaDownloadCertificateResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaHashSignApiRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaHashSignRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaHashSignResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaHashSignSendOtpRequest;
import com.adins.esign.webservices.model.tekenaja.TekenAjaHashSignSentOtpResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterApiResponse;
import com.adins.esign.webservices.model.tekenaja.TekenAjaRegisterRequest;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.TekenajaException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.google.gson.Gson;

@Component
public class GenericTekenAjaLogic extends BaseLogic implements TekenAjaLogic{
	private static final Logger LOG = LoggerFactory.getLogger(GenericTekenAjaLogic.class);
	
	@Autowired private Gson gson;
	
	@Autowired private CommonLogic commonLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private VendorValidatorLogic vendorValidatorLogic;
	@Autowired private ProvinceValidatorLogic provinceValidatorLogic;
	@Autowired private DistrictValidatorLogic districtValidatorLogic;
	@Autowired private SubdistrictValidatorLogic subdistrictValidatorLogic;
	
	private String headerContentType = HttpHeaders.CONTENT_TYPE;
    private String headerMultipart = MediaType.MULTIPART_FORM_DATA;
    private String headerJson = MediaType.APPLICATION_JSON;
    private String headerAccept = HttpHeaders.ACCEPT;
    private String headerApiKey = "apikey";
    private String headerPdf = org.springframework.http.MediaType.APPLICATION_PDF_VALUE;
    
    @Value("${tekenaja.uri}") private String urlTekenaja;
    @Value("${tekenaja.register.uri}") private String urlRegister;
    @Value("${tekenaja.registercek.uri}") private String urlRegisterCek;
    @Value("${tekenaja.uploaddoc.uri}") private String urlUploadDocument;
    @Value("${tekenaja.downloaddoc.uri}") private String urlDownloadDocument;
    @Value("${tekenaja.url.province}") private String urlTekenAjaProvince;
	@Value("${tekenaja.url.district}") private String urlTekenAjaDistrict;
	@Value("${tekenaja.url.subdistrict}") private String urlTekenAjaSubdistrict;
	@Value("${tekenaja.sign.url}") private String urlSign;
	@Value("${tekenaja.sign.bulk.url}") private String urlSignBulk;
	@Value("${tekenaja.hashsign.uri}") private String urlTekenajaHashSign;
	@Value("${tekenaja.hashsign.sendotp}") private String urlTekenajaHashSignSentOtp;
	@Value("${tekenaja.hashsign.downloadcert}") private String urlDownloadCert;
	@Value("${tekenaja.hashsign.sign}") private String urlTekenajaHashSignForSigning;
	
	private static final String VOT_NOTFOUND_MSG = "Vendor of tenant mapping not found with Vendor : %1$s  and Tenant : %2$s";
	private static final String TOKEN_VENDOR_OF_TENANT = "Token Vendor of tenant";
	private static final String ERR_MSG_GET_DOCUMENT_FAILED = "Error Get Document Teken Aja";
	
	private static final String FORM_DATA_PREFIX = "form-data; name=\"";
	private static final String FORM_DATA_SUFFIX = "\"";
	
	private static final String KEY_EMAIL ="email";
	private static final String KEY_NAME = "name";
	private static final String KEY_GENDER = "gender";
	private static final String KEY_DOB = "dob";
	private static final String KEY_POB = "pob";
	private static final String KEY_NIK = "nik";
	private static final String KEY_MOBILE = "mobile";
	private static final String KEY_PROVINCE = "province";
	private static final String KEY_DISTRICT = "district";
	private static final String KEY_SUBDISTRICT = "sub_district";
	private static final String KEY_ADDRESS = "address";
	private static final String KEY_ZIPCODE = "zip_code";
	private static final String KEY_SKIP_PHONE_VAL = "skip-phone-validation";
	private static final String KEY_KTP_PHOTO = "ktp_photo";
	private static final String KEY_SELFIE_PHOTO = "selfie_photo";
	
	@Override
	public TknajRegisterCekResponse registerCek(String nik, String action, MsVendor vendor, MsTenant tenant, AuditContext audit) {
		try {
			if (!TekenAjaConstant.REGCEK_ACTION_CHECK_NIK.equals(action) && !TekenAjaConstant.REGCEK_ACTION_RESEND_EMAIL.equals(action)) {
				throw new TekenajaException("Invalid parameter for action: " + action);
			}
			
			LOG.info("TekenAja NIK Check for {} with action {}", nik, action);
			
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			if(null == vot) {
				throw new EntityNotFoundException(String.format(VOT_NOTFOUND_MSG, vendor.getVendorCode(), tenant.getTenantCode()));
			}
			if(StringUtils.isBlank(vot.getToken())) {
				this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new Object[] { TOKEN_VENDOR_OF_TENANT, vendor.getVendorCode() +" - "+ tenant.getTenantCode() }, this.retrieveLocaleAudit(audit));
			}
			
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(headerApiKey, vot.getToken());
			mapHeader.add(headerContentType, headerMultipart);
			mapHeader.add(headerAccept, headerJson);
				
			WebClient client = WebClient.create(urlTekenaja + urlRegisterCek).headers(mapHeader);
				
			List<Attachment> atts = new LinkedList<>();
			ContentDisposition cdNik = new ContentDisposition("form-data; name=\"nik\"");
			atts.add(new Attachment("nik", new ByteArrayInputStream(nik.getBytes()), cdNik));
			
			ContentDisposition cdAction = new ContentDisposition("form-data; name=\"action\"");
			atts.add(new Attachment("action", new ByteArrayInputStream(action.getBytes()), cdAction));
				
			MultipartBody body = new MultipartBody(atts);
			Response response = client.post(body);
				
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

			String tekenajaResult = IOUtils.toString(isReader);
			LOG.info("TekenAja NIK Check Response: {}", tekenajaResult);
				
			TknajRegisterCekResponse responseClass = gson.fromJson(tekenajaResult, TknajRegisterCekResponse.class);
			
			if (!"OK".equals(responseClass.getStatus())) {
				String message = buildRegisterCekErrorMessage(responseClass);
				throw new TekenajaException(message);
			}
			
			return responseClass;
		} catch (Exception e) {
			throw new TekenajaException("TekenAja NIK Check Error: " + e.getLocalizedMessage());
		}
	}

	@Override
	public TknajUplDocResponse uploadDoc(DocumentConfinsRequestBean request, InsertDocumentManualSignRequest manualSignReq, String documentId, MsVendor vendor, MsTenant tenant,
			 TrDocumentD docD, TrDocumentH docH, AuditContext audit) throws IOException {
		TknajUplDocResponse responseClass = new TknajUplDocResponse();
		try {
			String refNo = null != request ? request.getReferenceNo() : manualSignReq.getReferenceNo();
			LOG.info("Upload Document for Ref. Number : {}", refNo);
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			if(null == vot) {
				throw new EntityNotFoundException(String.format(VOT_NOTFOUND_MSG, vendor.getVendorCode(), tenant.getTenantCode()));
			}
			if(StringUtils.isBlank(vot.getToken())) {
				this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
						new Object[] { TOKEN_VENDOR_OF_TENANT, vendor.getVendorCode() +" - "+ tenant.getTenantCode() }, this.retrieveLocaleAudit(audit));
			}
			
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(headerApiKey, vot.getToken());
			mapHeader.add(headerContentType, headerMultipart);
			mapHeader.add(headerAccept, headerJson);
			
			WebClient client = WebClient.create(urlTekenaja + urlUploadDocument).headers(mapHeader);	
			
			List<Attachment> atts = new LinkedList<>();
			
			List<SignatureBean> signature = prepareSignature(request, manualSignReq, tenant, vendor, docD, docH, audit);
			String stringSignature = gson.toJson(signature);
			LOG.info("Signature Request String : {}", stringSignature);
			
			ContentDisposition cdJson = new ContentDisposition("form-data; name=\"signature\"");
			atts.add(new Attachment("signature", new ByteArrayInputStream(stringSignature.getBytes()), cdJson));
			
			String documentName = GlobalVal.PREFIX_DOCUMENT_FILE_NAME + documentId;
			ContentDisposition cdDocument = new ContentDisposition("form-data; name=\"document\"; filename=\""+documentName+".pdf\"");
	
			String docFile = null != request ? request.getDocumentFile() : manualSignReq.getDocumentFile();
			byte[] dataPdfDocument = Base64.getDecoder().decode(docFile);
			atts.add(new Attachment("document*", new ByteArrayInputStream(dataPdfDocument), cdDocument));
			
			MultipartBody body = new MultipartBody(atts);
			Response response = client.post(body);
			
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
	
			String tekenajaResult = IOUtils.toString(isReader);
			LOG.info("Upload Document TekenAja response: {}", tekenajaResult);
			
			responseClass = gson.fromJson(tekenajaResult, TknajUplDocResponse.class);
		} catch (Exception e) {
			LOG.error("Upload Document TekenAja error", e);
			responseClass.setStatus("ERROR");
			responseClass.setMessage(e.getMessage());
		}
		
		return responseClass;
	}
	
	private void insertTrBalanceMutationTekenaj(TrDocumentD docD, TrDocumentH docH, MsTenant tenant, MsVendor vendor, AmMsuser user, int qty, AuditContext audit) {
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SGN);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USGN);
		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String notes = String.format("Signing Document with Document ID %1$s by %2$s", docD.getDocumentId(), user.getFullName());
		
		saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, tenant, vendor, new Date(), docH.getRefNumber(), qty, String.valueOf(nextTrxNo), user, notes, null, audit);
	}
	
	private List<SignatureBean> prepareSignature(DocumentConfinsRequestBean req, InsertDocumentManualSignRequest manualSignReq, MsTenant tenant, MsVendor vendor, TrDocumentD docD, TrDocumentH docH, AuditContext audit) {
		List<SignatureBean> toReturn = new ArrayList<>();
		
		if (null != req) {
			MsDocTemplate docTemplate = daoFactory.getDocumentDao().
					getDocumentTemplateByCodeAndTenantCode(req.getDocumentTemplateCode(), tenant.getTenantCode());
			List<MsDocTemplateSignLoc> signLocList = daoFactory.getDocumentDao().
					getListSignLocationByTemplateCodeAndIdTenant(docTemplate.getDocTemplateCode(), tenant.getIdMsTenant());
			
			for (SignerBean bean : req.getSigner()) {
				AmMsuser user = daoFactory.getUserDao().getUserByIdNo(bean.getIdNo());
				MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(user.getLoginId(), GlobalVal.VENDOR_CODE_TEKENAJA);
				MsLov signerType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNER_TYPE, bean.getSignerType());
				
				SignatureBean signature = new SignatureBean();
				signature.setEmail(vru.getSignerRegisteredEmail());
				
				List<SignatureDetailBean> detail = new ArrayList<>();
				for (MsDocTemplateSignLoc signLoc : signLocList) {
					if (null == signerType) {
						throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
								new Object[] {"Signer Type : " + signLoc.getMsLovByLovSignerType().getCode()}, 
								this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
					}
					
					if (!signLoc.getMsLovByLovSignType().getCode().equalsIgnoreCase(GlobalVal.CODE_LOV_SIGN_TYPE_SDT) 
							&& signLoc.getMsLovByLovSignerType().getCode().equalsIgnoreCase(signerType.getCode())) {
						SignatureDetailBean detailBean = gson.fromJson(signLoc.getTekenAjaSignLocation(), SignatureDetailBean.class);
						detailBean.setP(signLoc.getSignPage());
						detailBean.setVisibleSignature(1);
						detail.add(detailBean);
						insertTrBalanceMutationTekenaj(docD, docH, tenant, vendor, user, -1, audit);
					}
				}
				signature.setDetail(detail.toArray(new SignatureDetailBean[0]));
				toReturn.add(signature);
			}
		} else {
			for (ManualSignerBean signer : manualSignReq.getSigners()) {
				AmMsuser user = daoFactory.getUserDao().getUserByIdNo(signer.getIdNo());
				MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(user.getLoginId(), GlobalVal.VENDOR_CODE_TEKENAJA);
				
				SignatureBean signature = new SignatureBean();
				signature.setEmail(vru.getSignerRegisteredEmail());
				List<SignatureDetailBean> detail = new ArrayList<>();
				for (ManualSignBean sign : signer.getSignLocations()) {
					SignatureDetailBean detailBean = gson.fromJson(sign.getPosition(), SignatureDetailBean.class);
					detailBean.setP(Integer.parseInt(sign.getSignPage()));
					detail.add(detailBean);
					insertTrBalanceMutationTekenaj(docD, docH, tenant, vendor, user, -1, audit);
				}
				signature.setDetail(detail.toArray(new SignatureDetailBean[0]));
				toReturn.add(signature);
			}
		}
		
		return toReturn;
	}

	@Override
	public TekenAjaLocationBean getProvinceList(String vendorCode) throws IOException {
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		
		List<MsVendoroftenant> mvts = daoFactory.getVendorDao().getListVendoroftenantByVendorCode(vendorCode);
		
		String apikey = mvts.get(0).getToken();
		
		mapHeader.add(headerApiKey, apikey);
		
		WebClient clientProvince = WebClient.create(urlTekenAjaProvince).headers(mapHeader);
		
		Response responseProvince = clientProvince.get();
		
		InputStreamReader isReaderProvince = new InputStreamReader((InputStream) responseProvince.getEntity());

		String resultProvince = IOUtils.toString(isReaderProvince);
		LOG.info("JSON province result : {}", resultProvince);
		return gson.fromJson(resultProvince, TekenAjaLocationBean.class);
	}

	@Override
	public TekenAjaLocationBean getDistrictList(String provinceKey, String vendorCode) throws IOException {
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		
		List<MsVendoroftenant> mvts = daoFactory.getVendorDao().getListVendoroftenantByVendorCode(vendorCode);
		
		String apikey = mvts.get(0).getToken();
		
		mapHeader.add(headerApiKey, apikey);
		
		WebClient clientDistrict = WebClient.create(urlTekenAjaDistrict + "?province=" + provinceKey).headers(mapHeader);
		Response responseDistrict = clientDistrict.get();
		
		InputStreamReader isReaderDistrict = new InputStreamReader((InputStream) responseDistrict.getEntity());

		String resultDistrict = IOUtils.toString(isReaderDistrict);
		LOG.info("JSON district result : {}", resultDistrict);
		
		return gson.fromJson(resultDistrict, TekenAjaLocationBean.class);
	}

	@Override
	public TekenAjaLocationBean getSubDistrictList(String provinceKey, String districtKey, String vendorCode) throws IOException {
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();

		List<MsVendoroftenant> mvts = daoFactory.getVendorDao().getListVendoroftenantByVendorCode(vendorCode);
		
		String apikey = mvts.get(0).getToken();
		
		mapHeader.add(headerApiKey, apikey);
		
		WebClient clientSubdistrict = WebClient.create(urlTekenAjaSubdistrict + "?province=" + provinceKey + "&district=" + districtKey).headers(mapHeader);
		Response responseSubdistrict = clientSubdistrict.get();
		
		InputStreamReader isReaderSubdistrict = new InputStreamReader((InputStream) responseSubdistrict.getEntity());

		String resultSubdistrict = IOUtils.toString(isReaderSubdistrict);
		LOG.info("JSON subdistrict result : {}", resultSubdistrict);
		return gson.fromJson(resultSubdistrict, TekenAjaLocationBean.class);
	}

	@Override
	public TknajDownloadDocResponse downloadDoc(String psreDocumentId, MsVendor vendor, MsTenant tenant, AuditContext audit) throws IOException {
		LOG.info("Download Document for Document Id : {}", psreDocumentId);
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		if(null == vot) {
			throw new EntityNotFoundException(String.format(VOT_NOTFOUND_MSG, vendor.getVendorCode(), tenant.getTenantCode()));
		}
		if(StringUtils.isBlank(vot.getToken())) {
			messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new Object[] { TOKEN_VENDOR_OF_TENANT, vendor.getVendorCode() +" - "+ tenant.getTenantCode() }, this.retrieveLocaleAudit(audit));
		}
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
								
		mapHeader.add(headerApiKey, vot.getToken());
		mapHeader.add(headerAccept, headerJson);
		mapHeader.add(headerContentType, headerMultipart);
		
		WebClient client = WebClient.create(urlTekenaja + urlDownloadDocument).headers(mapHeader);	
		
		List<Attachment> atts = new LinkedList<>();
		
		ContentDisposition cdJson = new ContentDisposition("form-data; name=\"document_id\"");
		atts.add(new Attachment("signature", new ByteArrayInputStream(psreDocumentId.getBytes()), cdJson));
		
		MultipartBody body = new MultipartBody(atts);
		Response response = client.post(body);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String tekenajaResult = IOUtils.toString(isReader);
		LOG.info("Download document TekenAja response: {}", tekenajaResult);
		
		return gson.fromJson(tekenajaResult, TknajDownloadDocResponse.class);
	}
	
	@Override
	public TekenAjaRegisterApiResponse registerUser(TekenAjaRegisterRequest request, AuditContext audit) {
		try {
			MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
			MsVendor vendor = vendorValidatorLogic.validateGetVendor(request.getVendorCode(), true, audit);
			
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			if(null == vot) {
				throw new EntityNotFoundException(String.format(VOT_NOTFOUND_MSG, vendor.getVendorCode(), tenant.getTenantCode()));
			}
			
			// Header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
			mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_FORM_DATA);
			mapHeader.add(headerApiKey, vot.getToken());
			LOG.info("TekenAja register header: {}", mapHeader);
			
			// Body
			List<Attachment> attachments = new ArrayList<>();
			prepareRegisterRequest(attachments, request.getUserData(), audit);
			MultipartBody body = new MultipartBody(attachments);
			
			WebClient client = WebClient.create(urlTekenaja + urlRegister).headers(mapHeader);
			Response response = client.post(body);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String result = IOUtils.toString(isReader);
			LOG.info("TekenAja register response: {}", result);
			if (StringUtils.isBlank(result)) {
				throw new TekenajaException(messageSource.getMessage("businesslogic.global.noresponse",
						null, retrieveLocaleAudit(audit)));
			}
			
			return gson.fromJson(result, TekenAjaRegisterApiResponse.class);
		} catch (Exception e) {
			throw new TekenajaException("TekenAja Register Error: " + e.getLocalizedMessage());
		}
	}
	
	private void prepareRegisterRequest(List<Attachment> attachments, TekenAjaUserBean userData, AuditContext audit) {
		
		AmMsprovince province = provinceValidatorLogic.validateGetProvince(userData.getProvinceId(), true, audit);
		AmMsdistrict district = districtValidatorLogic.validateGetDistrict(userData.getDistrictId(), province, true, audit);
		AmMssubdistrict subDistrict = subdistrictValidatorLogic.validateGetSubdistrict(userData.getSubdistrictId(), district, true, audit);
		
		String skipPhoneVal = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_TEKENAJA_SKIP_PHONE_VAL, audit);
		if (StringUtils.isBlank(skipPhoneVal)) {
			// 0: Do phone validation
			// 1: Skip phone validation
			skipPhoneVal = "0";
		}
		
		String selfieFilename = GlobalVal.PREFIX_PHOTO_SELF_FILE_NAME + userData.getEmail() + GlobalVal.FILE_FORMAT_JPEG;
		String ktpFilename = GlobalVal.PREFIX_PHOTO_ID_FILE_NAME + userData.getEmail() + GlobalVal.FILE_FORMAT_JPEG;
		
		Map<String, Object> userDetail = new HashMap<>();
		userDetail.put(KEY_EMAIL, StringUtils.upperCase(userData.getEmail()));
		userDetail.put(KEY_NAME, StringUtils.upperCase(userData.getNama()));
		userDetail.put(KEY_GENDER, formatGenderTekenAjaFormat(userData.getJenisKelamin(), audit));
		userDetail.put(KEY_DOB, userData.getTglLahir());
		userDetail.put(KEY_POB, StringUtils.upperCase(userData.getTmpLahir()));
		userDetail.put(KEY_NIK, userData.getIdKtp());
		userDetail.put(KEY_MOBILE, userData.getTlp());
		userDetail.put(KEY_PROVINCE, province.getProvinceId());
		userDetail.put(KEY_DISTRICT, district.getDistrictId());
		userDetail.put(KEY_SUBDISTRICT, subDistrict.getSubdistrictId());
		userDetail.put(KEY_ADDRESS, StringUtils.upperCase(userData.getAlamat()));
		userDetail.put(KEY_ZIPCODE, userData.getKodePos());
		userDetail.put(KEY_SKIP_PHONE_VAL, skipPhoneVal);
		userDetail.put(KEY_SELFIE_PHOTO, selfieFilename);
		userDetail.put(KEY_KTP_PHOTO, ktpFilename);
		LOG.info("TekenAja register request: {}", userDetail);
		
		ContentDisposition cdEmail = new ContentDisposition(FORM_DATA_PREFIX + KEY_EMAIL + FORM_DATA_SUFFIX);
		byte[] emailByteArray = StringUtils.upperCase(userData.getEmail()).getBytes();
		attachments.add(new Attachment(KEY_EMAIL, new ByteArrayInputStream(emailByteArray), cdEmail));
		
		ContentDisposition cdName = new ContentDisposition(FORM_DATA_PREFIX + KEY_NAME + FORM_DATA_SUFFIX);
		byte[] nameByteArray = StringUtils.upperCase(userData.getNama()).getBytes();
		attachments.add(new Attachment(KEY_NAME, new ByteArrayInputStream(nameByteArray), cdName));
		
		ContentDisposition cdGender = new ContentDisposition(FORM_DATA_PREFIX + KEY_GENDER + FORM_DATA_SUFFIX);
		byte[] genderByteArray = formatGenderTekenAjaFormat(userData.getJenisKelamin(), audit).getBytes();
		attachments.add(new Attachment(KEY_GENDER, new ByteArrayInputStream(genderByteArray), cdGender));
		
		ContentDisposition cdDob = new ContentDisposition(FORM_DATA_PREFIX + KEY_DOB + FORM_DATA_SUFFIX);
		byte[] dobByteArray = userData.getTglLahir().getBytes();
		attachments.add(new Attachment(KEY_DOB, new ByteArrayInputStream(dobByteArray), cdDob));
		
		ContentDisposition cdPob = new ContentDisposition(FORM_DATA_PREFIX + KEY_POB + FORM_DATA_SUFFIX);
		byte[] pobByteArray = StringUtils.upperCase(userData.getTmpLahir()).getBytes();
		attachments.add(new Attachment(KEY_POB, new ByteArrayInputStream(pobByteArray), cdPob));
		
		ContentDisposition cdNik = new ContentDisposition(FORM_DATA_PREFIX + KEY_NIK + FORM_DATA_SUFFIX);
		byte[] nikByteArray = userData.getIdKtp().getBytes();
		attachments.add(new Attachment(KEY_NIK, new ByteArrayInputStream(nikByteArray), cdNik));
		
		ContentDisposition cdMobile = new ContentDisposition(FORM_DATA_PREFIX + KEY_MOBILE + FORM_DATA_SUFFIX);
		byte[] mobileByteArray = userData.getTlp().getBytes();
		attachments.add(new Attachment(KEY_MOBILE, new ByteArrayInputStream(mobileByteArray), cdMobile));
		
		ContentDisposition cdProvince = new ContentDisposition(FORM_DATA_PREFIX + KEY_PROVINCE + FORM_DATA_SUFFIX);
		byte[] provinceByteArray = String.valueOf(province.getProvinceId()).getBytes();
		attachments.add(new Attachment(KEY_PROVINCE, new ByteArrayInputStream(provinceByteArray), cdProvince));
		
		ContentDisposition cdDistrict = new ContentDisposition(FORM_DATA_PREFIX + KEY_DISTRICT + FORM_DATA_SUFFIX);
		byte[] districtByteArray = String.valueOf(district.getDistrictId()).getBytes();
		attachments.add(new Attachment(KEY_DISTRICT, new ByteArrayInputStream(districtByteArray), cdDistrict));
		
		ContentDisposition cdSubdistrict = new ContentDisposition(FORM_DATA_PREFIX + KEY_SUBDISTRICT + FORM_DATA_SUFFIX);
		byte[] subdistrictByteArray = String.valueOf(subDistrict.getSubdistrictId()).getBytes();
		attachments.add(new Attachment(KEY_SUBDISTRICT, new ByteArrayInputStream(subdistrictByteArray), cdSubdistrict));
		
		ContentDisposition cdAddress = new ContentDisposition(FORM_DATA_PREFIX + KEY_ADDRESS + FORM_DATA_SUFFIX);
		byte[] addressByteArray = StringUtils.upperCase(userData.getAlamat()).getBytes();
		attachments.add(new Attachment(KEY_ADDRESS, new ByteArrayInputStream(addressByteArray), cdAddress));
		
		ContentDisposition cdZipCode = new ContentDisposition(FORM_DATA_PREFIX + KEY_ZIPCODE + FORM_DATA_SUFFIX);
		byte[] zipCodeByteArray = StringUtils.upperCase(userData.getKodePos()).getBytes();
		attachments.add(new Attachment(KEY_ZIPCODE, new ByteArrayInputStream(zipCodeByteArray), cdZipCode));
		
		ContentDisposition cdSkipPhoneVal = new ContentDisposition(FORM_DATA_PREFIX + KEY_SKIP_PHONE_VAL + FORM_DATA_SUFFIX);
		byte[] skipPhoneValByteArray = skipPhoneVal.getBytes();
		attachments.add(new Attachment(KEY_SKIP_PHONE_VAL, new ByteArrayInputStream(skipPhoneValByteArray), cdSkipPhoneVal));
		
		try {
			ContentDisposition cdKtp = new ContentDisposition(FORM_DATA_PREFIX + KEY_KTP_PHOTO + FORM_DATA_SUFFIX + "; filename=\"" + ktpFilename + "\"");
			byte[] dataImage = MssTool.imageStringToByteArray(userData.getIdPhoto());
			attachments.add(new Attachment(KEY_KTP_PHOTO, new ByteArrayInputStream(dataImage), cdKtp));
		} catch (Exception e) {
			LOG.error("Error processing KTP photo", e);
			throw new TekenajaException(messageSource.getMessage("businesslogic.tekenaja.errorprocessingktp",
					null, retrieveLocaleAudit(audit)));
		}
		
		try {
			ContentDisposition cdSelfie = new ContentDisposition(FORM_DATA_PREFIX + KEY_SELFIE_PHOTO + FORM_DATA_SUFFIX + "; filename=\"" + selfieFilename + "\"");
			byte[] dataImage = MssTool.imageStringToByteArray(userData.getSelfPhoto());
			attachments.add(new Attachment(KEY_SELFIE_PHOTO, new ByteArrayInputStream(dataImage), cdSelfie));
		} catch (Exception e) {
			LOG.error("Error processing selfie photo", e);
			throw new TekenajaException(messageSource.getMessage("businesslogic.tekenaja.errorprocessingselfie",
					null, retrieveLocaleAudit(audit)));
		}
	}

	@Override
	public String formatGenderTekenAjaFormat(String gender, AuditContext audit) {
		if (GlobalVal.CODE_LOV_MALE.equals(gender)) {
			return "1";
		}
		if (GlobalVal.CODE_LOV_FEMALE.equals(gender)) {
			return "0";
		}
		throw new TekenajaException(messageSource.getMessage("businesslogic.user.invalidgender", null, retrieveLocaleAudit(audit)));
	}

	@Override
	public ViewDocumentResponse getPdfFromLinkTekenAja(DownloadTekenAjaLinkResponse responselink) {
		ViewDocumentResponse responsePDF = new ViewDocumentResponse();
		String url = responselink.getUrl();
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerContentType, headerPdf);
		WebClient client = WebClient.create(url).headers(mapHeader);
		
		Response response = null;
		try {
			response = client.get();
		} catch (Exception e) {
			LOG.error(ERR_MSG_GET_DOCUMENT_FAILED, e);
			Status status = new Status();
			status.setCode(200);
			status.setMessage(ERR_MSG_GET_DOCUMENT_FAILED);
			responsePDF.setStatus(status);
			return responsePDF;
		}
		
		InputStream is = (InputStream) response.getEntity();
		String base64String;
		try {
			byte[] pdfBytes = org.apache.commons.io.IOUtils.toByteArray(is);
			base64String = Base64.getEncoder().encodeToString(pdfBytes);
			responsePDF.setPdfBase64(base64String);
			
		} catch (Exception e) {
			LOG.error(ERR_MSG_GET_DOCUMENT_FAILED, e);
			Status status = new Status();
			status.setCode(200);
			status.setMessage(ERR_MSG_GET_DOCUMENT_FAILED);
			responsePDF.setStatus(status);
			return responsePDF;
		}
		return responsePDF;
	}
	
	private String convertMapMessage(Map<String, List<Object>> messageMap) {
		StringBuilder message = new StringBuilder();
		for (Map.Entry<String, List<Object>> entry : messageMap.entrySet()) {
			for (Object msg : entry.getValue()) {
				if (StringUtils.isNotBlank(message)) {
					message.append(" ");
				}
				message.append(msg);
			}
		}
		return message.toString();
	}
	
	private String buildRegisterVerificationMessage(TekenAjaRegisterResponseBean verifResult) {
		if (null == verifResult) {
			return StringUtils.EMPTY;
		}
		int totalFail = verifResult.failCount();
		if (0 == totalFail) {
			return StringUtils.EMPTY;
		}
		
		Integer currentFailCount = 0;
		StringBuilder messageBuilder = new StringBuilder();
		if (null != verifResult.getNik() && !verifResult.getNik()) {
			messageBuilder.append("NIK");
			currentFailCount ++;
			appendSeparator(messageBuilder, currentFailCount, totalFail);
		}
		if (null != verifResult.getName() && !verifResult.getName()) {
			messageBuilder.append("Nama");
			currentFailCount ++;
			appendSeparator(messageBuilder, currentFailCount, totalFail);
		}
		if (null != verifResult.getBirthplace() && !verifResult.getBirthplace()) {
			messageBuilder.append("Tempat Lahir");
			currentFailCount ++;
			appendSeparator(messageBuilder, currentFailCount, totalFail);
		}
		if (null != verifResult.getBirthdate() && !verifResult.getBirthdate()) {
			messageBuilder.append("Tanggal Lahir");
			currentFailCount ++;
			appendSeparator(messageBuilder, currentFailCount, totalFail);
		}
		messageBuilder.append(" tidak sesuai.");
		return messageBuilder.toString();
	}
	
	private void appendSeparator(StringBuilder messageBuilder, Integer currentFailCount, int totalFail) {
		if (currentFailCount < totalFail && totalFail != 2) {
			messageBuilder.append(",");
		}
		if (currentFailCount < totalFail) {
			messageBuilder.append(" ");
		}
		if (currentFailCount == totalFail - 1) {
			messageBuilder.append("dan ");
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public String buildRegisterErrorMessage(TekenAjaRegisterApiResponse registerResponse) {
		StringBuilder message = new StringBuilder();
		if (registerResponse.getMessage() instanceof String) {
			message.append(registerResponse.getMessage());
			message.append(".");
		} else if (registerResponse.getMessage() instanceof Map<?, ?>) {
			Map<String, List<Object>> messageMap = (Map<String, List<Object>>) registerResponse.getMessage();
			message.append(convertMapMessage(messageMap));
		}
		if (StringUtils.isNotBlank(message) && null != registerResponse.getData()) {
			message.append(" ");
		}
		message.append(buildRegisterVerificationMessage(registerResponse.getData()));
		return message.toString();
	}

	@SuppressWarnings("unchecked")
	@Override
	public String buildRegisterCekErrorMessage(TknajRegisterCekResponse response) {
		StringBuilder message = new StringBuilder();
		if (response.getMessage() instanceof String) {
			message.append(response.getMessage());
		} else if (response.getMessage() instanceof Map<?, ?>) {
			Map<String, List<Object>> messageMap = (Map<String, List<Object>>) response.getMessage();
			message.append(convertMapMessage(messageMap));
		}
		return message.toString();
	}

	@Override
	public TknajBulkSignResponse tekenAjaSign(TekenAjaSignRequest request, AuditContext audit) throws IOException {
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentId());

		MsVendoroftenant vot = daoFactory.getVendorDao().getVendorTenantByCode(docD.getMsTenant().getTenantCode(), docD.getMsVendor().getVendorCode());
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerApiKey, vot.getToken());
		mapHeader.add(headerContentType, headerMultipart);
		mapHeader.add(headerAccept, headerJson);
		
		WebClient client = WebClient.create(urlTekenaja + urlSign).headers(mapHeader);	
		List<Attachment> atts = new LinkedList<>();
		
		String documentId = docD.getPsreDocumentId();
		String userEmail = request.getUserEmail();
		
	
		ContentDisposition cdDocument = new ContentDisposition("form-data; name=\"document_id\"");
		ContentDisposition cdUserEmail = new ContentDisposition("form-data; name=\"user_email\"");

		
		atts.add(new Attachment("document_id", new ByteArrayInputStream(documentId.getBytes()), cdDocument));
		atts.add(new Attachment("user_email*", new ByteArrayInputStream(userEmail.getBytes()), cdUserEmail));


		MultipartBody body = new MultipartBody(atts);
		Response response = client.post(body);

		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String tekenajaResult = IOUtils.toString(isReader);
		LOG.info("Get sign link TekenAja response: {}", tekenajaResult);

		TknajBulkSignResponse responseClass = gson.fromJson(tekenajaResult, TknajBulkSignResponse.class);

		if (!"OK".equals(responseClass.getStatus())) {
			throw new TekenajaException(this.messageSource.getMessage("businesslogic.document.errorsend",
					new Object[] {"TekenAja", responseClass.getCode()}, this.retrieveLocaleAudit(audit)));
		}		
		return responseClass;
	}

	@Override
	public TknajBulkSignResponse tekenAjaSignBulk(TekenAjaSignBulkRequest request, AuditContext audit)
			throws IOException {
		TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentIds()[0]);
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(
				request.getLoginId(), document.getMsVendor().getVendorCode());
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(document.getMsTenant(), vendorUser.getMsVendor());

		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerApiKey, vot.getToken());
		mapHeader.add(headerContentType, headerJson);
		mapHeader.add(headerAccept, headerJson);
		
		WebClient client = WebClient.create(urlTekenaja + urlSignBulk).headers(mapHeader);	
		String[] arrPSREDocumentId = new String[request.getDocumentIds().length];

		
		for(int i = 0; i<request.getDocumentIds().length; i++) {
			String tempDocId = request.getDocumentIds()[i];
			TrDocumentD tempDocD = daoFactory.getDocumentDao().getDocumentDetailByDocId(tempDocId);
			arrPSREDocumentId[i] = tempDocD.getPsreDocumentId(); 
		}
		
		StringBuilder convertToString = new StringBuilder();
		for(int i = 0; i<arrPSREDocumentId.length; i++) {
			if(i!=0) {
				convertToString.append(",");
			}
			convertToString.append("\"" + arrPSREDocumentId[i] + "\"");
		}
		String convertToJson = "{\"document_id\": [" + convertToString +"]}";
		
		
		
		Response responseTknAja = client.post(convertToJson);
		InputStreamReader isReader = new InputStreamReader((InputStream) responseTknAja.getEntity());
		
		String tekenajaResult = IOUtils.toString(isReader);
		LOG.info("JSON result Upload Document TekenAja : {}", tekenajaResult);
		
		TknajBulkSignResponse responseClass = gson.fromJson(tekenajaResult, TknajBulkSignResponse.class);

		if (!"OK".equals(responseClass.getStatus())) {
			throw new TekenajaException(this.messageSource.getMessage("businesslogic.document.errorsend",
					new Object[] {"TekenAja", responseClass.getCode()}, this.retrieveLocaleAudit(audit)));
		}
		return responseClass;
	}

	@Override
	public TekenAjaHashSignSentOtpResponse tekenAjaHashSignSendOtp(String identifier, String apikey, AuditContext audit) {
		try {
			// Prepare header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(headerAccept, headerJson);
			mapHeader.add(headerContentType, headerJson);
			mapHeader.add(headerApiKey, apikey);
			LOG.info("TekenAja send OTP request header: {}", mapHeader);
			WebClient client = WebClient.create(urlTekenajaHashSign + urlTekenajaHashSignSentOtp).headers(mapHeader);
			
			// Prepare body
			long epochNow = System.currentTimeMillis()/1000;
			String externalId = identifier + "-" + epochNow;
			TekenAjaHashSignSendOtpRequest sendOtpRequest = new TekenAjaHashSignSendOtpRequest();
			sendOtpRequest.setIdentifier(identifier);
			sendOtpRequest.setExternalId(externalId);
			String jsonRequest = gson.toJson(sendOtpRequest);
			LOG.info("TekenAja Hash Sign Sent OTP request: {}", jsonRequest);
						
			// Get response
			Response response = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String jsonResponse = IOUtils.toString(isReader);
			LOG.info("TekenAja Hash Sign Sent OTP response: {}", jsonResponse);
			
			TekenAjaHashSignSentOtpResponse tekenAjaHashSignSentOtpResponse = gson.fromJson(jsonResponse, TekenAjaHashSignSentOtpResponse.class);
			
			if (GlobalVal.ERROR_TYPE_ERROR.equals(tekenAjaHashSignSentOtpResponse.getStatus())) {
				if (GlobalVal.TEKEN_AJA_HASH_SIGN_USER_NOT_FOUND.equals(tekenAjaHashSignSentOtpResponse.getCode())) {
					throw new TekenajaException(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_HASHSIGN_USER_HAS_NOT_REGISTERED, null, audit));
				} else {
					throw new TekenajaException(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_HASHSIGN_SENT_OTP_FAILED, null, audit));
				}
			}
			
			return tekenAjaHashSignSentOtpResponse;
			
		} catch (Exception e) {
			throw new TekenajaException(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public TekenAjaDownloadCertificateResponse downloadCertificate(MsVendorRegisteredUser vRUser, MsTenant tenant,
			AuditContext audit){
		try {
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vRUser.getMsVendor());
		if (StringUtils.isBlank(vot.getToken())) {
			throw new TekenajaException(getMessage("businesslogic.tekenaja.hashsign.sentotpfailed", null, audit));
		}
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerAccept, headerJson);
		mapHeader.add(headerContentType, headerJson);
		mapHeader.add(headerApiKey, vot.getToken());
		WebClient client = WebClient.create(urlTekenajaHashSign + urlDownloadCert).headers(mapHeader);	
		
		// Prepare body
		TekenAjaDownloadCertificateRequest downloadCertRequest = new TekenAjaDownloadCertificateRequest();
		downloadCertRequest.setIdentifier(vRUser.getVendorRegistrationId());
		String jsonRequest = gson.toJson(downloadCertRequest);
		LOG.info("TekenAja Download Certificate request: {}", jsonRequest);

		// Get Response
		Response responseTknAja = client.post(jsonRequest);
		InputStreamReader isReader = new InputStreamReader((InputStream) responseTknAja.getEntity());
		String tekenajaResult = null;
		TekenAjaDownloadCertificateResponse responseClass = null;
		
			tekenajaResult = IOUtils.toString(isReader);
			LOG.info("JSON result Download Certificate TekenAja : {}", tekenajaResult);
			responseClass = gson.fromJson(tekenajaResult, TekenAjaDownloadCertificateResponse.class);
			if (!"OK".equals(responseClass.getStatus())) {
				throw new TekenajaException(this.messageSource.getMessage("businesslogic.tekenaja.errordownloadcert",
						new Object[] {responseClass.getMessage()}, this.retrieveLocaleAudit(audit)));
			}
			return responseClass;
		} catch (IOException e) {
			throw new TekenajaException(e.getLocalizedMessage(), e);
		}
		
	}

	@Override
	public TekenAjaHashSignResponse tekenAjaHashSign(TekenAjaHashSignRequest request, AuditContext audit) {
		try {
			// Prepare header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(headerAccept, headerJson);
			mapHeader.add(headerContentType, headerJson);
			mapHeader.add(headerApiKey, request.getApikey());
			LOG.info("TekenAja Hash Sign request header: {}", mapHeader);
			WebClient client = WebClient.create(urlTekenajaHashSign + urlTekenajaHashSignForSigning).headers(mapHeader);
			
			// Prepare body
			TekenAjaHashSignApiRequest hashSignApiRequest = new TekenAjaHashSignApiRequest();
			
			List<HashSignDocumentListBean> documentList = new ArrayList<>();
			
			List<String> documentHashList = request.getDocumentHashList();
			List<String> trxNoList = request.getTrxNoList();
			for (int documentHashNum = 0; documentHashNum < documentHashList.size(); documentHashNum++) {
				String trxNo = trxNoList.get(documentHashNum);
				HashSignDocumentListBean documentHashSignListBean  = new HashSignDocumentListBean();
				documentHashSignListBean.setDocumentIdentifier(trxNo);
				documentHashSignListBean.setDocumentDigest(documentHashList.get(documentHashNum));
				documentList.add(documentHashSignListBean);
			}
			
			hashSignApiRequest.setIdentifier(request.getIdentifier());
			hashSignApiRequest.setOtp(request.getOtp());
			hashSignApiRequest.setDocumentList(documentList);
			
			String jsonRequest = gson.toJson(hashSignApiRequest);
			LOG.info("TekenAja Hash Sign request: {}", jsonRequest);
						
			// Get response
			Response response = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String jsonResponse = IOUtils.toString(isReader);
			LOG.info("TekenAja Hash Sign response: {}", jsonResponse);
			
			TekenAjaHashSignResponse tekenAjaHashSignResponse = gson.fromJson(jsonResponse, TekenAjaHashSignResponse.class);
			
			if (GlobalVal.ERROR_TYPE_ERROR.equals(tekenAjaHashSignResponse.getStatus())) {
				if (GlobalVal.TEKEN_AJA_HASH_SIGN_INVALID_OTP.equals(tekenAjaHashSignResponse.getCode())) {
					throw new TekenajaException(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_HASHSIGN_INVALID_OTP, null, audit));
				} else if (GlobalVal.TEKEN_AJA_HASH_SIGN_OTP_EXPIRED.equals(tekenAjaHashSignResponse.getCode())) {
					throw new TekenajaException(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_HASHSIGN_OTP_EXPIRED, null, audit));
				} else if (GlobalVal.TEKEN_AJA_HASH_SIGN_USER_NOT_FOUND.equals(tekenAjaHashSignResponse.getCode())) {
					throw new TekenajaException(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_HASHSIGN_USER_HAS_NOT_REGISTERED, null, audit));
				} else if (GlobalVal.TEKEN_AJA_HASH_SIGN_SYSTEM_FAILURE.equals(tekenAjaHashSignResponse.getCode())) {
					throw new TekenajaException(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_HASHSIGN_SYSTEM_FAILURE, null, audit));
				} else {
					throw new TekenajaException(getMessage(GlobalKey.MESSAGE_ERROR_TEKENAJA_HASHSIGN_FAILED_SIGN, null, audit));
				}
			}
			
			return tekenAjaHashSignResponse;
			
		} catch (Exception e) {
			throw new TekenajaException(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public TekenAjaRegisterApiResponse registerUser(UserBean userData, MsVendor vendor, MsTenant tenant, AuditContext audit) throws IOException {
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		
		// Header
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
		mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_FORM_DATA);
		mapHeader.add(headerApiKey, vot.getToken());
		
		// Body
		List<Attachment> attachments = prepareRegisterRequest(userData, audit);
		MultipartBody body = new MultipartBody(attachments);
		
		WebClient client = WebClient.create(urlTekenaja + urlRegister).headers(mapHeader);
		MssTool.setWebClientConnReadTimeout(client, 20_000L, 60_000L);
		Response response = client.post(body);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String result = IOUtils.toString(isReader);
		LOG.info("TekenAja register response: {}", result);
		
		return gson.fromJson(result, TekenAjaRegisterApiResponse.class);
	}
	
	private List<Attachment> prepareRegisterRequest(UserBean userData, AuditContext audit) {
		String skipPhoneVal = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_TEKENAJA_SKIP_PHONE_VAL, audit);
		if (StringUtils.isBlank(skipPhoneVal)) {
			// 0: Do phone validation
			// 1: Skip phone validation
			skipPhoneVal = "0";
		}
		
		String selfieFilename = GlobalVal.PREFIX_PHOTO_SELF_FILE_NAME + userData.getUserPhone() + GlobalVal.FILE_FORMAT_JPEG;
		
		Map<String, Object> userDetail = new HashMap<>();
		userDetail.put(KEY_EMAIL, StringUtils.upperCase(userData.getEmail()));
		userDetail.put(KEY_NAME, StringUtils.upperCase(userData.getUserName()));
		userDetail.put(KEY_GENDER, formatGenderTekenAjaFormat(userData.getUserGender(), audit));
		userDetail.put(KEY_DOB, userData.getUserDob());
		userDetail.put(KEY_POB, StringUtils.upperCase(userData.getUserPob()));
		userDetail.put(KEY_NIK, userData.getIdNo());
		userDetail.put(KEY_MOBILE, userData.getUserPhone());
		userDetail.put(KEY_SKIP_PHONE_VAL, skipPhoneVal);
		userDetail.put(KEY_SELFIE_PHOTO, selfieFilename);
		LOG.info("TekenAja register request: {}", userDetail);
		
		List<Attachment> attachments = new ArrayList<>();
		
		ContentDisposition cdEmail = new ContentDisposition(FORM_DATA_PREFIX + KEY_EMAIL + FORM_DATA_SUFFIX);
		byte[] emailByteArray = StringUtils.upperCase(userData.getEmail()).getBytes();
		attachments.add(new Attachment(KEY_EMAIL, new ByteArrayInputStream(emailByteArray), cdEmail));
		
		ContentDisposition cdName = new ContentDisposition(FORM_DATA_PREFIX + KEY_NAME + FORM_DATA_SUFFIX);
		byte[] nameByteArray = StringUtils.upperCase(userData.getUserName()).getBytes();
		attachments.add(new Attachment(KEY_NAME, new ByteArrayInputStream(nameByteArray), cdName));
		
		ContentDisposition cdGender = new ContentDisposition(FORM_DATA_PREFIX + KEY_GENDER + FORM_DATA_SUFFIX);
		byte[] genderByteArray = formatGenderTekenAjaFormat(userData.getUserGender(), audit).getBytes();
		attachments.add(new Attachment(KEY_GENDER, new ByteArrayInputStream(genderByteArray), cdGender));
		
		ContentDisposition cdDob = new ContentDisposition(FORM_DATA_PREFIX + KEY_DOB + FORM_DATA_SUFFIX);
		byte[] dobByteArray = userData.getUserDob().getBytes();
		attachments.add(new Attachment(KEY_DOB, new ByteArrayInputStream(dobByteArray), cdDob));
		
		ContentDisposition cdPob = new ContentDisposition(FORM_DATA_PREFIX + KEY_POB + FORM_DATA_SUFFIX);
		byte[] pobByteArray = StringUtils.upperCase(userData.getUserPob()).getBytes();
		attachments.add(new Attachment(KEY_POB, new ByteArrayInputStream(pobByteArray), cdPob));
		
		ContentDisposition cdNik = new ContentDisposition(FORM_DATA_PREFIX + KEY_NIK + FORM_DATA_SUFFIX);
		byte[] nikByteArray = userData.getIdNo().getBytes();
		attachments.add(new Attachment(KEY_NIK, new ByteArrayInputStream(nikByteArray), cdNik));
		
		ContentDisposition cdMobile = new ContentDisposition(FORM_DATA_PREFIX + KEY_MOBILE + FORM_DATA_SUFFIX);
		byte[] mobileByteArray = userData.getUserPhone().getBytes();
		attachments.add(new Attachment(KEY_MOBILE, new ByteArrayInputStream(mobileByteArray), cdMobile));
		
		ContentDisposition cdSkipPhoneVal = new ContentDisposition(FORM_DATA_PREFIX + KEY_SKIP_PHONE_VAL + FORM_DATA_SUFFIX);
		byte[] skipPhoneValByteArray = skipPhoneVal.getBytes();
		attachments.add(new Attachment(KEY_SKIP_PHONE_VAL, new ByteArrayInputStream(skipPhoneValByteArray), cdSkipPhoneVal));
		
		try {
			ContentDisposition cdSelfie = new ContentDisposition(FORM_DATA_PREFIX + KEY_SELFIE_PHOTO + FORM_DATA_SUFFIX + "; filename=\"" + selfieFilename + "\"");
			byte[] dataImage = MssTool.imageStringToByteArray(userData.getSelfPhoto());
			attachments.add(new Attachment(KEY_SELFIE_PHOTO, new ByteArrayInputStream(dataImage), cdSelfie));
		} catch (Exception e) {
			LOG.error("Error processing selfie photo", e);
			throw new TekenajaException(messageSource.getMessage("businesslogic.tekenaja.errorprocessingselfie",
					null, retrieveLocaleAudit(audit)));
		}
		
		return attachments;
	}

	@Override
	public AmMsuser insertRegisteredUser(UserBean userData, MsLov lovUserType, MsVendor vendor, MsTenant tenant, boolean isActivated, boolean externalActivation, AuditContext audit) {
		
		String hashedPhone = MssTool.getHashedString(userData.getUserPhone());
		String hashedIdNo = MssTool.getHashedString(userData.getIdNo());
		String emailService = (userData.getIdEmailHosting() != null && userData.getIdEmailHosting() != 0) ? "1" :"0";
		MsEmailHosting emailHosting = daoFactory.getEmailDao().getEmailHostingById(userData.getIdEmailHosting());
		
		AmMsrole role = null;
		if (null != lovUserType && GlobalVal.CODE_LOV_USER_TYPE_EMPLOYEE.equals(lovUserType.getCode())) {
			role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(GlobalVal.ROLE_BM_MF, tenant.getTenantCode());
		} else {
			role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(GlobalVal.ROLE_CUSTOMER, tenant.getTenantCode());
		}
		
		AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(userData.getIdNo());
		if (null == user) {
			// insert user
			MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(tenant.getTenantCode());
			String[] separatedName = userData.getUserName().split(" ");
			
			user = new AmMsuser();
			user.setIsActive("1");
			user.setIsDeleted("0");
			user.setLoginId(StringUtils.upperCase(userData.getEmail()));
			user.setFullName(StringUtils.upperCase(userData.getUserName()));
			user.setInitialName(StringUtils.upperCase(separatedName[0]));
			user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
			user.setPassword("newInv");
			user.setFailCount(0);
			user.setIsLoggedIn("0");
			user.setIsLocked("0");
			user.setIsDormant("0");
			user.setMsOffice(office);
			user.setChangePwdLogin("1");
			user.setUsrCrt(audit.getCallerId());
			user.setDtmCrt(new Date());
			user.setEmailService(emailService);
			user.setMsEmailHosting(emailHosting);
			user.setHashedPhone(hashedPhone);
			user.setHashedIdNo(hashedIdNo);
			daoFactory.getUserDao().insertUserNewTran(user);
		}
		
		MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCodeNewTran(user.getIdMsUser(), tenant.getTenantCode());
		if (null == useroftenant) {
			useroftenant = new MsUseroftenant();
			useroftenant.setAmMsuser(user);
			useroftenant.setMsTenant(tenant);
			useroftenant.setUsrCrt(audit.getCallerId());
			useroftenant.setDtmCrt(new Date());
			daoFactory.getUseroftenantDao().insertUseroftenantNewTran(useroftenant);
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(user.getIdMsUser(), GlobalVal.VENDOR_CODE_TEKENAJA);
		if (null == vendorUser) {
			byte[] phoneBytea = personalDataEncLogic.encryptFromString(userData.getUserPhone());
			Date activatedDate = new Date();
			Date expiredDate = DateUtils.addYears(activatedDate, 1);
			
			vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(userData.getEmail()));
			vendorUser.setIsActive("0");
			vendorUser.setUsrCrt(audit.getCallerId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(vendor);
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			vendorUser.setIsExternalActivation(externalActivation ? "1" : "0");
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUserNewTran(vendorUser);
		}
		
		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), false);
		if (null == personalData || null == personalData.getUserPersonalData()) {
			
			Date dob = MssTool.formatStringToDate(userData.getUserDob(), GlobalVal.DATE_FORMAT);
			byte[] selfiePhoto = MssTool.imageStringToByteArray(userData.getSelfPhoto());
			byte[] idPhoto = MssTool.imageStringToByteArray(userData.getIdPhoto());
			
			ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(userData.getProvinsi());
			zipcodeCityBean.setKota(userData.getKota());
			zipcodeCityBean.setKecamatan(userData.getKecamatan());
			zipcodeCityBean.setKelurahan(userData.getKelurahan());
			zipcodeCityBean.setZipcode(userData.getZipcode());
			
			PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(audit.getCallerId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(StringUtils.upperCase(userData.getUserGender()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(userData.getUserPob()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);
			personalDataBean.setSelfPhotoRaw(selfiePhoto);
			personalDataBean.setPhotoIdRaw(idPhoto);
			personalDataBean.setIdNoRaw(userData.getIdNo());
			personalDataBean.setPhoneRaw(userData.getUserPhone());
			personalDataBean.setAddressRaw(StringUtils.upperCase(userData.getUserAddress()));
			daoFactory.getUserDao().insertUserPersonalData(personalDataBean);

		} else {

			Date dob = MssTool.formatStringToDate(userData.getUserDob(), GlobalVal.DATE_FORMAT);
			byte[] selfiePhoto = MssTool.imageStringToByteArray(userData.getSelfPhoto());
			byte[] idPhoto = MssTool.imageStringToByteArray(userData.getIdPhoto());
			
			ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(userData.getProvinsi());
			zipcodeCityBean.setKota(userData.getKota());
			zipcodeCityBean.setKecamatan(userData.getKecamatan());
			zipcodeCityBean.setKelurahan(userData.getKelurahan());
			zipcodeCityBean.setZipcode(userData.getZipcode());

			AmUserPersonalData userPersonalData = personalData.getUserPersonalData();
			userPersonalData.setUsrUpd(audit.getCallerId());
			userPersonalData.setDtmUpd(new Date());
			userPersonalData.setGender(StringUtils.upperCase(userData.getUserGender()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(userData.getUserPob()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);

			personalData.setUserPersonalData(userPersonalData);
			personalData.setSelfPhotoRaw(selfiePhoto);
			personalData.setPhotoIdRaw(idPhoto);
			personalData.setIdNoRaw(userData.getIdNo());
			personalData.setPhoneRaw(userData.getUserPhone());
			personalData.setAddressRaw(StringUtils.upperCase(userData.getUserAddress()));
			daoFactory.getUserDao().updateUserPersonalData(personalData);
		}
		
		AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofroleNewTran(user, role);
		if (null == userRole) {
			userRole = new AmMemberofrole();
			userRole.setAmMsrole(role);
			userRole.setAmMsuser(user);
			userRole.setUsrCrt(audit.getCallerId());
			userRole.setDtmCrt(new Date());
			daoFactory.getRoleDao().insertMemberOfRoleNewTran(userRole);
		}
		
		return user;
	}
}
