package com.adins.am.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "am_msdistrict")
public class AmMsdistrict extends UpdateableEntity {
	public static final String ID_MSDISTRICT_HBM = "idMsdistrict";
	public static final String DISTRICT_ID_HBM = "districtId";
	public static final String DISTRICT_NAME_HBM = "districtName";
	public static final String AM_MSPROVINCE_HBM = "amMsprovince";

	private Long idMsdistrict;
	private String districtName;
	private Long districtId;
	private AmMsprovince amMsprovince;
	
	public AmMsdistrict() {
		
	}
	
	public AmMsdistrict(Long idMsdistrict, String districtName, Long districtId,
			String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd, AmMsprovince amMsprovince) {
		super();
		this.idMsdistrict = idMsdistrict;
		this.districtName = districtName;
		this.districtId = districtId;
		this.amMsprovince = amMsprovince;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
	}
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_msdistrict", unique = true, nullable = false)
	public Long getIdMsdistrict() {
		return idMsdistrict;
	}

	public void setIdMsdistrict(Long idMsdistrict) {
		this.idMsdistrict = idMsdistrict;
	}
	
	@Column(name = "district_name", length = 70)
	public String getDistrictName() {
		return districtName;
	}

	public void setDistrictName(String districtName) {
		this.districtName = districtName;
	}
	
	@Column(name = "district_id")
	public Long getDistrictId() {
		return districtId;
	}

	public void setDistrictId(Long districtId) {
		this.districtId = districtId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_msprovince", nullable = false)
	public AmMsprovince getAmMsprovince() {
		return amMsprovince;
	}

	public void setAmMsprovince(AmMsprovince amMsprovince) {
		this.amMsprovince = amMsprovince;
	}




}
