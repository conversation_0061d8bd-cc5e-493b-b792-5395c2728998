package com.adins.esign.businesslogic.impl;


import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.FunctionComputeLogic;
import com.adins.esign.businesslogic.api.JobResultLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrJobResult;
import com.adins.esign.model.custom.ViewRequestParamJobResultBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.CancelJobRequest;
import com.adins.esign.webservices.model.CancelJobResponse;
import com.adins.esign.webservices.model.GetListJobRekonResultRequest;
import com.adins.esign.webservices.model.GetListJobRekonResultResponse;
import com.adins.esign.webservices.model.SubmitRekonJobResultRequest;
import com.adins.esign.webservices.model.SubmitRekonJobResultResponse;
import com.adins.esign.webservices.model.ViewRequestParamJobResultRequest;
import com.adins.esign.webservices.model.ViewRequestParamJobResultResponse;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.LovException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.JobException.ReasonJob;
import com.adins.exceptions.JobException;
import com.adins.exceptions.LovException.Reason;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.google.gson.Gson;


@Transactional
@Component
public class GenericJobResultLogic extends BaseLogic implements JobResultLogic {

	private static final Logger LOG = LoggerFactory.getLogger(GenericJobResultLogic.class);
	private static final String CONST_BALANCETYPE_CODE_INVALID = "Kode tipe balance tidak valid";
	
    @Autowired private Gson gson;
	@Autowired private FunctionComputeLogic functionComputeLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	
	@Override
	public SubmitRekonJobResultResponse submitRekonJobResult(SubmitRekonJobResultRequest request, AuditContext audit) {
		SubmitRekonJobResultResponse response = new SubmitRekonJobResultResponse();
		Status status = new Status();
		
		// Validasi vendor
		MsVendor msVendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		String vendorValMsg = getMessage("businesslogic.saldo.vendornotexist", new Object[] {request.getVendorCode()}, audit);
		commonValidatorLogic.validateNotNull(msVendor, vendorValMsg, StatusCode.VENDOR_NOT_FOUND);
		
		if (!StringUtils.equals(msVendor.getVendorCode(), GlobalVal.VENDOR_CODE_DIGISIGN)) {
			throw new VendorException(getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_INVALID_VENDOR_TYPE, new String[] {msVendor.getVendorCode()}, audit), ReasonVendor.VENDOR_CODE_INVALID);
		}
		
		// Validasi tenant
		MsTenant msTenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		
		if (StringUtils.isNotBlank(request.getTenantCode())) {
			String tenantValMsg = getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_TENANT_CODE_INVALID, null, audit);
			commonValidatorLogic.validateNotNull(msTenant, tenantValMsg, StatusCode.VENDOR_TENANT_NOT_EXIST);
		}
		
		if (StringUtils.isNotBlank(request.getBalanceType())) {
			MsLov msLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, request.getBalanceType());
			commonValidatorLogic.validateNotNull(msLov, CONST_BALANCETYPE_CODE_INVALID, StatusCode.LOV_CODE_INVALID);
						
			if(!StringUtils.equals(GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, msLov.getCode())) {
				String message = CONST_BALANCETYPE_CODE_INVALID;
				throw new LovException(message, Reason.CODE_INVALID);	
			}
		}
		
		//Check misalnya tenant all ada data di msLov tidak balanceTypenya
		if(StringUtils.isBlank(request.getTenantCode())) {
			MsLov msLov = daoFactory.getLovDao().getMsLovByCode(request.getBalanceType());
			commonValidatorLogic.validateNotNull(msLov, CONST_BALANCETYPE_CODE_INVALID, StatusCode.LOV_CODE_INVALID);
		}
		
		//Check tenant ada di vendor atau tidak
		if (null!=msTenant) {
			MsVendoroftenant vendorOfTenant = daoFactory.getVendorDao().getVendoroftenant(msTenant, msVendor);
			if(null==vendorOfTenant) {
				String message = "Tenant tidak ditemukan pada vendor (" + request.getVendorCode() + ")";
				throw new VendorException(message, ReasonVendor.TENANT_NOT_FOUND);
			}
		}
		
		if (!isDateRangeValidSubmitJob(request.getStartDate(), request.getEndDate(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE_RECON,
							new Object[] { "Submit Rekonsil" }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}
		
		HashMap<String, String> mapper = new HashMap<>();
		mapper.put("vendorCode", request.getVendorCode());
		mapper.put("tenantCode", request.getTenantCode());
		mapper.put("balanceType", request.getBalanceType());
		mapper.put("transactionDateStart", request.getStartDate());
		mapper.put("transactionDateEnd", request.getEndDate());

		String jsonMap = gson.toJson(mapper);
		LOG.info("Isi JSON: {}", jsonMap);
		
		TrJobResult checkDup = daoFactory.getJobDao().checkJobResult(jsonMap, request.getTenantCode(), GlobalVal.JOB_TYPE_JOB_REKONSIL);
		
		if(null!=checkDup) {
			status.setCode(9000);
			status.setMessage("Permintaan rekonsil tidak boleh duplikat! Tolong tunggu proses sebelumnya selesai.");
			response.setStatus(status);
			return response;
		}
		
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(request.getLoginId());
		MsLov lovJob = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_JOB_TYPE, GlobalVal.CODE_LOV_JOB_TYPE_REKONSIL);
		
		
		TrJobResult insertJob = new TrJobResult();
		insertJob.setAmMsuser(user);
		insertJob.setDtmCrt(new Date());
		insertJob.setMsJob(daoFactory.getJobDao().getMsJob(GlobalVal.JOB_TYPE_JOB_REKONSIL));
		insertJob.setRequestParams(jsonMap);
		insertJob.setMsTenant(daoFactory.getTenantDao().getTenantByCode(request.getTenantCode()));
		insertJob.setUsrCrt(request.getLoginId());
		insertJob.setProcessStatus((short) 0);
		insertJob.setMsLovJobType(lovJob);

		daoFactory.getJobDao().insertTrJobResult(insertJob);
		
		
		TrJobResult checkIdForUpl = daoFactory.getJobDao().checkJobResult(jsonMap, request.getTenantCode(), GlobalVal.JOB_TYPE_JOB_REKONSIL);

		
		functionComputeLogic.invokeReconcileOtpDigisign(checkIdForUpl.getIdJobResult());
		
		status.setMessage("Submit Rekon Sukses!");
		response.setStatus(status);
		return response;
	}
	
	private boolean isDateRangeValidSubmitJob(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		String maxRangeDate = daoFactory.getGeneralSettingDao()
				.getGsValueByCode(AmGlobalKey.GENERALSETTING_SUBMIT_RECON_MAXDATE);

		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return true;
		}
		if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
			try {
				sdf.parse(startDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
			try {
				sdf.parse(endDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}

		long dayCount = 0;
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			dayCount = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		LOG.info("Date range: {} hari", dayCount);
		return dayCount <= Long.valueOf(maxRangeDate);
	}

	@Override
	public CancelJobResponse cancelJobResult(CancelJobRequest request, AuditContext audit) {
		AmMsuser user = userValidatorLogic.validateGetUserByEmail(audit.getCallerId(), true, audit);
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(this.messageSource.getMessage("businesslogic.paymentsigntype.tenantnotfound", null, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		Long idJobResult = Long.valueOf(request.getIdJobResult());
		TrJobResult jobResult = daoFactory.getJobDao().getNewJobResultByTenantUserAndIdJobResult(request.getTenantCode(), user.getIdMsUser(), idJobResult);
		if (null == jobResult) {
			throw new JobException(this.messageSource.getMessage("businesslogic.job.jobresultnotfound", null, this.retrieveLocaleAudit(audit)), ReasonJob.JOB_RESULT_NOT_FOUND);
		}
		
		jobResult.setUsrUpd(audit.getCallerId());
		jobResult.setDtmUpd(new Date());
		jobResult.setProcessStatus((short) 3);
		daoFactory.getJobDao().updateJobResult(jobResult);
		
		Status status = new Status();
		status.setMessage("Success");
		
		CancelJobResponse resp = new CancelJobResponse();
		resp.setStatus(status);
		
		return resp;
	}

	@Override
	public GetListJobRekonResultResponse getListJobRekonResultResponse(GetListJobRekonResultRequest request,
			AuditContext audit) throws ParseException {
		GetListJobRekonResultResponse response = new GetListJobRekonResultResponse();
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		
		Date dateStart = null;
		Date dateEnd = null;
		
		//Validasi 30 hari
		if (!isDateRangeValid(request.getRequestDateStart(), request.getRequestDateEnd(), audit)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
					new Object[] {"List Rekon Result"}, this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_DATE_RANGE);
		}
		
		if(StringUtils.isNotBlank(request.getRequestDateStart())) {
			dateStart =  MssTool.formatStringToDate(request.getRequestDateStart() + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC);
			
		}
		if(StringUtils.isNotBlank(request.getRequestDateEnd())) {
			dateEnd = MssTool.formatStringToDate(request.getRequestDateEnd() + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		
		Integer totalData = daoFactory.getJobDao().getCountListJobRekonResult(request.getJobId(), request.getLoginId(), 
				request.getProcessResult(), dateStart, dateEnd);
		double totalPage = Math.ceil((double) totalData / maxRow);

		response.setTotalResult(totalData);
		response.setTotalPage((int)totalPage);
		response.setListJobResult(daoFactory.getJobDao().getListJobRekonResult(min, max, request.getJobId(), request.getLoginId(), 
				request.getProcessResult(), dateStart, dateEnd));

		
		return response;
	}

	@Override
	public ViewRequestParamJobResultResponse  getRequestParamJobResult(ViewRequestParamJobResultRequest request, AuditContext audit) {
		ViewRequestParamJobResultResponse response = new ViewRequestParamJobResultResponse();
		Long idJobResult = request.getJobResultId();
		if(idJobResult == null) {
			throw new JobException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_ID_JOB_RESULT_EMPTY,
					null, this.retrieveLocaleAudit(audit)), ReasonJob.ID_JOB_RESULT_EMPTY);
		}
		
		TrJobResult jobResult= daoFactory.getJobDao().getJobResultById(request.getJobResultId());
		
		if(jobResult == null) {
			throw new JobException(this.messageSource.getMessage("businesslogic.job.jobresultnotfound", 
					null, this.retrieveLocaleAudit(audit)), ReasonJob.JOB_RESULT_NOT_FOUND);
		}
		ViewRequestParamJobResultBean requestParam = gson.fromJson(jobResult.getRequestParams(), ViewRequestParamJobResultBean.class);
		List<ViewRequestParamJobResultBean> listResultParam = new ArrayList<>();
		
			ViewRequestParamJobResultBean parambean = new ViewRequestParamJobResultBean();
	    	parambean.setBalanceType(requestParam.getBalanceType());
	     	parambean.setTransactionDateStart(requestParam.getTransactionDateStart());
		    parambean.setTransactionDateEnd(requestParam.getTransactionDateEnd());
		    parambean.setTenantCode(requestParam.getTenantCode());
		    parambean.setVendorCode(requestParam.getVendorCode());
		    listResultParam.add(requestParam);
		
		response.setListRequestParam(listResultParam);
		return response;
	}
	
	private boolean isDateRangeValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		String maxRangeDate = daoFactory.getGeneralSettingDao()
				.getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);

		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return true;
		}
		if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
			try {
				sdf.parse(startDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
			try {
				sdf.parse(endDate);
			} catch (ParseException e) {
				throw new DocumentException(
						this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR, null,
								this.retrieveLocaleAudit(audit)),
						ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}

		long dayCount = 0;
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			dayCount = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		LOG.info("Date range: {} hari", dayCount);
		return dayCount <= Long.valueOf(maxRangeDate);
	}
	
}