package com.adins.esign.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.dom4j.DocumentException;
import org.springframework.context.NoSuchMessageException;
import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.webservices.model.FeedbackEmbedRequest;
import com.adins.esign.webservices.model.FeedbackRequest;
import com.adins.esign.webservices.model.embed.FeedbackHybridEmbedRequest;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface FeedbackLogic {
	
	@RolesAllowed({"ROLE_FEEDBACK"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	void insertFeedback(FeedbackRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_ANONYMOUS"})
	void insertFeedbackEmbed(FeedbackEmbedRequest request, AuditContext audit);
	
	void insertFeedbackHybridEmbed(FeedbackHybridEmbedRequest request, AuditContext audit) throws NoSuchMessageException, DocumentException;
}
