package com.adins.esign.businesslogic.impl.interfacing;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Base64;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.ContentDisposition;
import org.apache.cxf.jaxrs.ext.multipart.MultipartBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.interfacing.LivenessLogic;
import com.adins.esign.businesslogic.api.interfacing.PrivyLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.webservices.model.LivenessUrlResponse;
import com.adins.esign.webservices.model.RegistrationRequest;
import com.adins.esign.webservices.model.privy.PrivyLivenessUrlResponse;
import com.adins.exceptions.PrivyException;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericLivenessLogic extends BaseLogic implements LivenessLogic {

	private static final Logger LOG = LoggerFactory.getLogger(GenericLivenessLogic.class);

    private String headerContentType = javax.ws.rs.core.HttpHeaders.CONTENT_TYPE;
    private String headerMultipart = javax.ws.rs.core.MediaType.MULTIPART_FORM_DATA;
    
	@Autowired private TenantLogic tenantLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private PrivyLogic privyLogic;
	
	@Async
	@Override
	public void registerLiveness(RegistrationRequest request, AuditContext audit) throws IOException {
		UserBean userBean = request.getUserData();
		MsTenant tenant = tenantLogic.getTenantByCode(request.getTenantCode(), audit);
		String urlLivenessKtp = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_URL_LIVENESS_KTP, audit);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerContentType, headerMultipart);
		WebClient client = WebClient.create(urlLivenessKtp).headers(mapHeader);
		
		List<Attachment> atts = new LinkedList<>();

		if(null != userBean.getSelfPhoto()) {
			String fileName = GlobalVal.PREFIX_PHOTO_SELF_FILE_NAME + userBean.getLoginId() ;
			ContentDisposition cdImg = new ContentDisposition("form-data; name=\"img\"; filename=\""+fileName+"\"");
			String photoClean = userBean.getSelfPhoto().replace(GlobalVal.IMG_JPG_PREFIX, "");
			byte[] dataImagePhoto = Base64.getDecoder().decode(photoClean);
			atts.add(new Attachment("img", new ByteArrayInputStream(dataImagePhoto), cdImg));
		}
		
		if (null != tenant.getLivenessKey()) {
			ContentDisposition cdKey = new ContentDisposition("form-data; name=\"key\"");
			atts.add(new Attachment("key", new ByteArrayInputStream(tenant.getLivenessKey().getBytes()), cdKey));
		}
		
		if (null != tenant.getTenantCode()) {
			ContentDisposition cdTenantCode = new ContentDisposition("form-data; name=\"tenant_code\"");
			atts.add(new Attachment("tenant_code", new ByteArrayInputStream(tenant.getTenantCode().getBytes()), cdTenantCode));
		}
		
		if (null != userBean.getIdNo()) {
			ContentDisposition cdNik = new ContentDisposition("form-data; name=\"nik\"");
			atts.add(new Attachment("nik", new ByteArrayInputStream(userBean.getIdNo().getBytes()), cdNik));
		}
		
		MultipartBody body = new MultipartBody(atts);
		Response clientResponse = client.post(body);

		InputStreamReader isReader = new InputStreamReader((InputStream) clientResponse.getEntity());
		String result = StringUtils.EMPTY;
		try {
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			LOG.error("Error on Register Liveness result", e);
		}
		LOG.info("Register Liveness result: {}", result);
	}

	private String getPrivyLivenessUrl(MsTenant tenant, AuditContext audit) {

		try {
			PrivyLivenessUrlResponse response = privyLogic.getPrivyLivenessV3Url(tenant, audit);
			return response.getData().getUserLandingUrl();	
		} catch (Exception e) {
			throw new PrivyException(StringUtils.isBlank(e.getMessage()) ? getMessage("businesslogic.privy.failedtogetlivenessurl", null, audit) : e.getMessage(), e);
		}
		
	}
		
	@Override
	public LivenessUrlResponse getLivenessUrl(String xApiKey, AuditContext audit) {
		Date date = new Date();
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(xApiKey, audit);
		logProcessDuration("Get tenant from api time", date, new Date());
		Date date1 = new Date();
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_PRIVY_ID);
		logProcessDuration("Get vendor from code time", date1, new Date());
		Date date2 = new Date();
		MsVendoroftenant vendoroftenant = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		if (null == vendoroftenant) {
			throw new PrivyException(getMessage("businesslogic.context.unauthorizedaccess", null, audit));
		}
		logProcessDuration("Get vendoroftenant time", date2, new Date());
		Date date3 = new Date();
		String livenessUrl = getPrivyLivenessUrl(tenant, audit);
		logProcessDuration("Get liveness url time", date3, new Date());
		LivenessUrlResponse response = new LivenessUrlResponse();
		response.setLivenessUrl(livenessUrl);
		return response;
	}

}
