package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class UploadDmsWfJob {
private static final Logger LOG = LoggerFactory.getLogger(UploadDmsWfJob.class);
	
	@Autowired SchedulerLogic schedulerLogic;

	public void runUploadDmsWf() {
		try {
			LOG.info("Job Upload DMS Wf Started");
			AuditContext auditContext = new AuditContext(GlobalVal.SCHEDULER_WORKFLOW);
			schedulerLogic.uploadDmsWF(auditContext);
			LOG.info("Job Upload DMS Wf Finished");
		} catch (Exception e) {
			LOG.error("Error on running Job Upload DMS Wf", e);
		}
	}
}
