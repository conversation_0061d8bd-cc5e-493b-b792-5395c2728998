package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class LovException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum Reason {
		ERROR_EXIST,
		CODE_INVALID,
		UNKNOWN
	}

	private final Reason reason;

	public LovException(Reason reason) {
		this.reason = reason;
	}

	public LovException(String message, Reason reason) {
		super(message);
		this.reason = reason;
	}

	public LovException(Throwable ex, Reason reason) {
		super(ex);
		this.reason = reason;
	}

	public LovException(String message, Throwable ex, Reason reason) {
		super(message, ex);
		this.reason = reason;
	}

	public Reason getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
				case ERROR_EXIST:
					return StatusCode.ERROR_EXIST;
				case CODE_INVALID:
					return StatusCode.LOV_CODE_INVALID;
				default:
					return StatusCode.UNKNOWN;
			}
			
		}
		return StatusCode.UNKNOWN;
	}

}
