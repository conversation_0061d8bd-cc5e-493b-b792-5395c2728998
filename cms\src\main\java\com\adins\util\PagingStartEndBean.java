package com.adins.util;

public class PagingStartEndBean {
	private int start;
	private int end;
	
	public PagingStartEndBean() {}
	public PagingStartEndBean(int start, int end) {
		super();
		this.start = start;
		this.end = end;
	}
	
	public int getStart() {
		return start;
	}
	public void setStart(int start) {
		this.start = start;
	}
	public int getEnd() {
		return end;
	}
	public void setEnd(int end) {
		this.end = end;
	}

	public static PagingStartEndBean fromPageNoAndSize(int pageNo, int pageSize) {
		int min = ((pageNo - 1) * pageSize) + 1;
		int max = (pageNo * pageSize);
		return new PagingStartEndBean(min, max);
	}
}
