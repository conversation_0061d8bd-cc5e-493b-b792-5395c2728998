package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class TekenajaException extends AdInsException{
	private static final long serialVersionUID = 1L;

	//message diisi dengan field "code" dari response API teken aja
	public TekenajaException(String message){
		super(message);
	}
	public TekenajaException(String message, Throwable ex) {
		super(message, ex);
	}

	@Override
	public int getErrorCode() {
		return StatusCode.TEKEN_AJA_ERROR;
	}
}
