package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class LoginException extends AdInsException {

	private static final long serialVersionUID = 1L;

	public enum Reason {
		LOGIN_CONCURRENCE, 
		LOGIN_DORMANT, 
		LOGIN_INACTIVE, 
		LOGIN_INVALID, 
		LOGIN_LOCKED, 
		LOGIN_PWDEXPIRED,
		LOGIN_CHANGEPASSWORD,
		LOGIN_REQUIRED,
		WAIT_FOR_DOC,
		UNKNOWN
	}

	private final Reason reason;

	public LoginException(Reason reason) {
		this.reason = reason;
	}

	public LoginException(String message, Reason reason) {
		super(message);
		this.reason = reason;
	}

	public LoginException(Throwable ex, Reason reason) {
		super(ex);
		this.reason = reason;
	}

	public LoginException(String message, Throwable ex, Reason reason) {
		super(message, ex);
		this.reason = reason;
	}

	public Reason getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (null == this.reason) {
			return StatusCode.UNKNOWN;
		}
		
		switch (reason) {
			case LOGIN_CONCURRENCE:
				return StatusCode.LOGIN_CONCURRENCE;
			case LOGIN_DORMANT:
				return StatusCode.LOGIN_DORMANT;
			case LOGIN_INACTIVE:
				return StatusCode.LOGIN_INACTIVE;
			case LOGIN_INVALID:
				return StatusCode.LOGIN_INVALID;
			case LOGIN_LOCKED:
				return StatusCode.LOGIN_LOCKED;
			case LOGIN_PWDEXPIRED:
				return StatusCode.LOGIN_PWDEXPIRED;
			case LOGIN_CHANGEPASSWORD:
				return StatusCode.LOGIN_CHANGEPASSWORD;
			case LOGIN_REQUIRED:
				return StatusCode.LOGIN_REQUIRED;
			case UNKNOWN:
				return StatusCode.UNKNOWN;
			default:
				return StatusCode.UNKNOWN;
		}
	}

}
