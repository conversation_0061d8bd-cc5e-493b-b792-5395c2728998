
package com.adins.esign.businesslogic.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserPersonalData;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.UserManagementLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.GetDataUserManagementViewRequest;
import com.adins.esign.webservices.model.GetDataUserManagementViewResponse;
import com.adins.esign.webservices.model.GetListDataPenggunaRequest;
import com.adins.esign.webservices.model.GetListDataPenggunaResponse;
import com.adins.esign.webservices.model.GetListUserManagementRequest;
import com.adins.esign.webservices.model.GetListUserManagementResponse;
import com.adins.esign.webservices.model.InsertUserManagementResponse;
import com.adins.esign.webservices.model.InsestUserManagementRequest;
import com.adins.esign.webservices.model.ListDataPengguna;
import com.adins.esign.webservices.model.ListUserManagement;
import com.adins.esign.webservices.model.UpdateUserManagementRequest;
import com.adins.esign.webservices.model.UpdateUserManagementResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.UserException;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.UserManagementException;
import com.adins.exceptions.UserManagementException.ReasonUserManagement;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.tool.password.PasswordHash;

@Transactional
@Component
public class GenericUserManagementLogic extends BaseLogic implements UserManagementLogic{
	
	@Autowired CommonLogic commonLogic;
	@Autowired UserValidatorLogic userValidatorLogic;
	@Autowired TenantValidatorLogic tenantValidatorLogic;
	@Autowired CommonValidatorLogic commonValidatorLogic;


	@Override
	public GetListUserManagementResponse getListUserManagement(GetListUserManagementRequest request,
			AuditContext audit) {
		
		GetListUserManagementResponse response = new GetListUserManagementResponse();
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
				
		String checkLoginIdByCallerIdAndTenant = daoFactory.getUserDao().getLoginIdByTenantAndCallerId(tenant.getTenantCode(), audit.getCallerId());
		
		if (!audit.getCallerId().equals(checkLoginIdByCallerIdAndTenant)) {
			throw new UserException(getMessage("businesslogic.usermanagement.invalidtenantandcaller", new Object[] { audit.getCallerId(), request.getTenantCode()}, audit)
					, ReasonUser.INVALID_TENANT_AND_CALLER);
		}
		
		if (StringUtils.isNotBlank(request.getRoleCode())) {
			String errorMessage = getMessage("businesslogic.usermanagement.rolecodenotavailableintenant", new Object[] {request.getRoleCode(), request.getTenantCode()}, audit);
			int errorCode = StatusCode.ROLE_CODE_NOT_AVAILABLE_IN_TENANT;
	
			AmMsrole role = daoFactory.getRoleDao().getRoleUserManagementByTenant(request.getRoleCode(), tenant.getTenantCode());
			 
			commonValidatorLogic.validateNotNull(role, errorMessage, errorCode);
		}
		
		int totalResult = daoFactory.getUserDao().countListUserManagement(request, tenant.getIdMsTenant());
		double totalPage = Math.ceil((double) totalResult / maxRow);
		
		List<Map<String, Object>> userManagement = daoFactory.getUserDao().getListUserManagement(request, tenant.getIdMsTenant(), min, max);
		
		List<ListUserManagement> listUserManagement = new ArrayList<>();
		Iterator<Map<String, Object>> itr = userManagement.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			ListUserManagement listUserManagement1 = new ListUserManagement();
			listUserManagement1.setFullName(((String) map.get("d0")));
			listUserManagement1.setLoginId(((String) map.get("d1")));
			listUserManagement1.setRoleCode(((String) map.get("d2")));
			listUserManagement1.setIsActive(((String) map.get("d3")));
			listUserManagement1.setIsUserManagement(((String) map.get("d4")));
			
			listUserManagement.add(listUserManagement1);
		}
		response.setListUserManagement(listUserManagement);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalResult);
		
		return response;
		
	}
	
	@Override
	public InsertUserManagementResponse insertUserManagement(InsestUserManagementRequest request, AuditContext audit) {
		AmMsuser user = daoFactory.getUserDao().getDeletedUserManagementByLoginId(request.getLoginId());
		if (null != user) {
			throw new UserManagementException(getMessage("businesslogic.usermanagement.loginidexist",
					new Object[] { request.getLoginId() }, audit), ReasonUserManagement.USER_ID_ALREADY_EXIST);
		}

		AmMsrole role = daoFactory.getRoleDao().getRoleByCode(request.getRoleCode(), request.getTenantCode());
		if (null == role) {
			throw new UserManagementException(getMessage("businesslogic.usermanagement.rolenotexist", null, audit),
					ReasonUserManagement.ROLE_NOT_EXIST);
		}

		AmMsrole role2 = daoFactory.getRoleDao().getRoleUserManagementByTenant(request.getRoleCode(),request.getTenantCode());
		if (null == role2) {
			throw new UserManagementException(
					getMessage("businesslogic.usermanagement.rolenotisusermanagement", null, audit),
					ReasonUserManagement.ROLE_NOT_EXIST);
		}
		
		MsUseroftenant tenant = daoFactory.getTenantDao().getTenantByloginId(audit.getCallerId(), request.getTenantCode());
		if (null == tenant) {
			throw new CommonException(getMessage("businesslogic.user.tenantwithcalleridnotfound", new Object[]{audit.getCallerId(), request.getTenantCode()}, audit), ReasonCommon.CALLER_ID_NOT_HAVE_TENANT);
		}
		
		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(request.getOfficeCode(), request.getTenantCode());
		if (null == office) {
			throw new UserManagementException(getMessage("businesslogic.usermanagement.officeisnotexist", null, audit),
					ReasonUserManagement.OFFICE_IS_NOT_AVAIABLE);
		}
		
		AmMsuser newUser = new AmMsuser();
		newUser.setLoginId(StringUtils.upperCase(StringUtils.upperCase(request.getLoginId())));
		newUser.setFullName(StringUtils.upperCase(request.getFullName()));
		newUser.setPassword(PasswordHash.createHash(request.getPassword()));
		newUser.setUsrCrt(audit.getCallerId());
		newUser.setLoginProvider("DB");
		newUser.setMsOffice(office);
		newUser.setDtmCrt(new Date());
		newUser.setIsActive("1");
		newUser.setIsDeleted("0");
		newUser.setFailCount(0);
		newUser.setIsLoggedIn("1");
		newUser.setIsLocked("0");
		newUser.setIsDormant("0");
		newUser.setChangePwdLogin("1");
		newUser.setEmailService("0");
		newUser.setIsDormant("2");
		daoFactory.getUserDao().insertUser(newUser);

		MsUseroftenant userOfTenant = new MsUseroftenant();
		userOfTenant.setAmMsuser(newUser);
		userOfTenant.setMsTenant(tenant.getMsTenant());
		userOfTenant.setUsrCrt(audit.getCallerId());
		userOfTenant.setDtmCrt(new Date());
		daoFactory.getTenantDao().insertUserOfTenant(userOfTenant);

		AmMemberofrole memberOfRole = new AmMemberofrole();
		memberOfRole.setAmMsrole(role);
		memberOfRole.setAmMsuser(newUser);
		memberOfRole.setDtmCrt(new Date());
		memberOfRole.setUsrCrt(audit.getCallerId());
		daoFactory.getRoleDao().insertMemberOfRole(memberOfRole);

		AmGeneralsetting defaultVendor = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_DEFAULT_VENDOR_INSERT_USER_MANAGEMENT);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(defaultVendor.getGsValue());
		
		MsVendorRegisteredUser vendorRegisteredUser = new MsVendorRegisteredUser();
		vendorRegisteredUser.setMsVendor(vendor);
		vendorRegisteredUser.setAmMsuser(newUser);
		vendorRegisteredUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getLoginId()));
		vendorRegisteredUser.setIsActive("1");
		vendorRegisteredUser.setIdMsVendorRegisteredUser(1);
		vendorRegisteredUser.setUsrCrt(audit.getCallerId());
		vendorRegisteredUser.setDtmCrt(new Date());
		vendorRegisteredUser.setIsRegistered("1");
		daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUser(vendorRegisteredUser);
		
		AmUserPersonalData personaldata = new AmUserPersonalData();
		PersonalDataBean personaldatabean = new PersonalDataBean();
		personaldata.setAmMsuser(newUser);
		personaldata.setDtmCrt(new Date());
		personaldata.setEmail(StringUtils.upperCase(request.getLoginId()));
		personaldata.setUsrCrt(audit.getCallerId());
		personaldatabean.setUserPersonalData(personaldata);
		daoFactory.getUserDao().insertUserPersonalData(personaldatabean);
		
		InsertUserManagementResponse response = new InsertUserManagementResponse();
		Status status = new Status();
		status.setCode(0);
		status.setMessage("Success");
		response.setStatus(status);
		return response;

	}


	@Override
	public GetListDataPenggunaResponse getListDataPengguna(GetListDataPenggunaRequest request, AuditContext audit) {
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		List<Map<String,Object>> dataPengguna = daoFactory.getUserDao().getListDataPengguna(request, tenant.getIdMsTenant());
		List<ListDataPengguna> listDataPengguna = new ArrayList<>();
		Iterator<Map<String,Object>> itr = dataPengguna.iterator();
		
		while (itr.hasNext()) {
			
			Map<String,Object> map = itr.next();
			
			BigInteger idMsvendorRegisteredUser = (BigInteger) map.get("d7");
			MsVendorRegisteredUser vuser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsVendorRegisteredUser(idMsvendorRegisteredUser.longValue());
			
			String certificateStatus = "Active";
			if (null == vuser.getCertExpiredDate()) {
				certificateStatus = "Not Active";
 			}
			if (null != vuser.getCertExpiredDate() && GlobalVal.VENDOR_CODE_VIDA.equals(vuser.getMsVendor().getVendorCode()) && userValidatorLogic.isCertifExpiredForInquiry(vuser)) {
				certificateStatus = "Expired";
 			}
			
			ListDataPengguna listDataPengguna1 = new ListDataPengguna();
			listDataPengguna1.setLoginId((String) map.get("d0"));
			listDataPengguna1.setFullName((String) map.get("d1"));
			listDataPengguna1.setIsActive((String) map.get("d6"));
			listDataPengguna1.setVendorName((String) map.get("d2"));
			listDataPengguna1.setVendorCode((String) map.get("d3"));
			listDataPengguna1.setIsActivated((String) map.get("d4"));
			listDataPengguna1.setIsRegistered((String) map.get("d5"));
			listDataPengguna1.setCertificateStatus(certificateStatus);
			listDataPengguna.add(listDataPengguna1);
		}
		
		GetListDataPenggunaResponse response = new GetListDataPenggunaResponse();
		response.setListDataPengguna(listDataPengguna);
		return response;
	}

	@Override
	public GetDataUserManagementViewResponse getDataUserManagementView(GetDataUserManagementViewRequest request,
			AuditContext audit) {
		if(StringUtils.isEmpty(request.getLoginId())) {
			throw new UserException(getMessage("businesslogic.usermanagement.datauserempty", new Object[]{request.getLoginId()}, audit),
					ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		
		if(StringUtils.isEmpty(request.getRole())) {
			throw new UserException(getMessage("businesslogic.usermanagement.rolenotfound", null, audit)
					, ReasonUser.ROLE_CODE_NOT_FOUND);
		}
		
		GetDataUserManagementViewResponse response = new GetDataUserManagementViewResponse();
		
		
		AmMsuser dataView = daoFactory.getUserDao().getDataUserByLoginId(request.getLoginId());
		
		if(null == dataView) {
			throw new UserException(getMessage("businesslogic.usermanagement.datausermanagementnotfound", new Object[]{request.getLoginId()}, audit),
					ReasonUser.LOGIN_ID_NOT_EXISTS);
		}

		
		AmMsrole roleCurrUser = daoFactory.getRoleDao().getDataRoleByIdUser(dataView.getIdMsUser(), request.getRole(), request.getTenantCode());
		
		if(null == roleCurrUser) {
			throw new UserException(getMessage("businesslogic.usermanagement.invalidroleanduserandtenant", new Object[]{request.getLoginId(), request.getRole(), request.getTenantCode()}, audit),
					ReasonUser.INVALID_ROLE_AND_USER_AND_TENANT);
		}
		
		if(!StringUtils.equals(roleCurrUser.getIsUserManagement(), "1")) {
			throw new UserException(getMessage("businesslogic.usermanagement.isusermanagementinvalid", new Object[]{request.getRole()}, audit),
					ReasonUser.IS_USER_MANAGEMENT_INVALID);
		}

		
			response.setName(dataView.getFullName());
			response.setLoginId(request.getLoginId());
			response.setRole(roleCurrUser.getRoleCode());
			response.setActivatedDate(MssTool.formatDateToStringIn(dataView.getDtmCrt(), GlobalVal.DATE_FORMAT));
			response.setOfficeCode(dataView.getMsOffice().getOfficeCode());
			response.setOffice(dataView.getMsOffice().getOfficeName());
			response.setIsActive(dataView.getIsActive());	
			
		
		return response;
	
	}

	@Override
	public UpdateUserManagementResponse updateUserManagement(UpdateUserManagementRequest request, AuditContext audit) {
		UpdateUserManagementResponse response = new UpdateUserManagementResponse();
		
		AmMsuser user = daoFactory.getUserDao().getNotActiveUser(request.getLoginId());
		
		validateUserForUpdateUserManagement (user,request,audit);

		Status status = new Status();
		status.setCode(0);
		status.setMessage("Success");
		response.setStatus(status);
		return response;
	}

	private void validateUserForUpdateUserManagement (AmMsuser user, UpdateUserManagementRequest request, AuditContext audit) {
		
			if (null != user) {
			
			MsUseroftenant msUserofTenant = daoFactory.getTenantDao().getTenantByloginId(audit.getCallerId(), request.getTenantCode());
			
			MsTenant loginIdTenant = daoFactory.getTenantDao().getTenantByUser(request.getLoginId());
			
			if (null == msUserofTenant) {
				throw new CommonException(getMessage("businesslogic.user.tenantwithcalleridnotfound", new Object[]{audit.getCallerId(), request.getTenantCode()}, audit), ReasonCommon.CALLER_ID_NOT_HAVE_TENANT);			
			}
			
			if (loginIdTenant != null) {
				msUserofTenant = daoFactory.getTenantDao().getTenantByloginId(audit.getCallerId(), loginIdTenant.getTenantCode());
				
				if (null == msUserofTenant) {
				throw new CommonException(getMessage("businesslogic.usermanagement.calleridnotatenantofloginid", new Object[]{request.getLoginId(), audit.getCallerId()}, audit), ReasonCommon.CALLER_ID_IS_NOT_TENTANT_OF_LOGIN_ID);
				}
			}
			
			validateAndSetRoleUpdateUserManagement(user, request, audit);
			
			validateOfficeAndUpdateUserManagement(user,request,audit);

			
			} else { 
				throw new UserManagementException(getMessage("businesslogic.usermanagement.loginidisnotexist", null, audit),
					ReasonUserManagement.LOGIN_ID_IS_NOT_FOUND);
			}
	}
	
	private void validateAndSetRoleUpdateUserManagement(AmMsuser user, UpdateUserManagementRequest request, AuditContext audit){
		if (StringUtils.isNotEmpty(request.getRoleCode())) {
			AmMsrole role = daoFactory.getRoleDao().getRoleUserManagementByTenant(request.getRoleCode(), request.getTenantCode());
			
			if (null != role) {
				AmMemberofrole memberofRole = daoFactory.getRoleDao().getMemberofroleByUser(user);
				memberofRole.setAmMsrole(role);
				memberofRole.setDtmUpd(new Date());
				memberofRole.setUsrUpd(audit.getCallerId());
				memberofRole.setAmMsrole(role);
				daoFactory.getRoleDao().updateMemberofRole(memberofRole);
			} else {
				throw new UserManagementException(getMessage("businesslogic.usermanagement.rolenotfound", null, audit),
						ReasonUserManagement.ROLE_IS_NOT_FOUND);
			}
		}
	}
	
	private void validateOfficeAndUpdateUserManagement(AmMsuser user, UpdateUserManagementRequest request, AuditContext audit) {
		if(StringUtils.isNotEmpty(request.getOfficeCode())) {
			MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(request.getOfficeCode(), request.getTenantCode());		
			if (null == office) {
				throw new UserManagementException(getMessage("businesslogic.usermanagement.officeisnotexist", null, audit),
						ReasonUserManagement.OFFICE_IS_NOT_AVAIABLE);
			}
			user.setMsOffice(office);
		}
		
		if(StringUtils.isNotEmpty(request.getIsActive())) {
			user.setIsActive(request.getIsActive());
		}
		user.setDtmCrt(new Date());
		user.setUsrUpd(audit.getCallerId());
		daoFactory.getUserDao().updateUser(user);
		
	}
}
