package com.adins.esign.model.custom.jatis;

public class JatisSmsResponse {
	private String status;
	private String messageId;
	
	public static JatisSmsResponse parseString(String rawSmsResponse) {
		JatisSmsResponse response = new JatisSmsResponse();
		String[] parts = rawSmsResponse.split("&");
		
		for (String part : parts) {	
			String[] splitPart = part.split("=");
			String key = splitPart[0];
			String value = splitPart[1];
			
			switch (key) {
				case "Status":
					response.setStatus(value);
					break;
				case "MessageId":
					response.setMessageId(value);
					break;
				default:
					break;
			}
		}
		
		return response;
	}
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getMessageId() {
		return messageId;
	}
	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}
	
	
}
