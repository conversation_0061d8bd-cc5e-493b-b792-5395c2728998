package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.TenantDao;
import com.adins.esign.model.MsBalancevendoroftenant;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.custom.BalanceTenantBean;
import com.adins.esign.model.custom.TenantListBean;
import com.adins.esign.model.custom.TenantRekonBean;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class TenantDaoHbn extends BaseDaoHbn implements TenantDao{

	@Override
	public MsTenant getTenantById(long idTenant) {
		if (0 == idTenant) {
			return null;
		}
	
		return this.managerDAO.selectOne(
				"from MsTenant t "
				+ "where t.idMsTenant = :idMsTenant ", 
						new Object[][] {{MsTenant.ID_TENANT_HBM, idTenant}});
	}
	
	@Override
	public MsTenant getTenantByCode(String tenantCode) {
		
		// Query diubah pakai left join karena untuk sekarang ada case tenant id_email_hosting null atau notif_type null
		if (StringUtils.isBlank(tenantCode))
			return null;
	
		return this.managerDAO.selectOne(
				"from MsTenant t "
				+ "left join fetch t.msLov "
				+ "left join fetch t.msEmailHosting "
				+ "left join fetch t.lovSmsGateway "
				+ "left join fetch t.lovDefaultOtpSendingOption "
				+ "where t.tenantCode = :tenantCode and t.isActive ='1'", 
						new Object[][] {{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}});
	}
	
	@Override
	public void insertTenant(MsTenant tenant) {
		tenant.setUsrCrt(MssTool.maskData(tenant.getUsrCrt()));
		this.managerDAO.insert(tenant);
	}

	@Override
	public String getVendorOfTenantEncryptionKey(String tenantCode, String vendorCode) {
		StringBuilder query = new StringBuilder();
		query.append("SELECT vot.encryption_key FROM ms_tenant tn")
  			.append(" inner join ms_vendoroftenant vot on (tn.id_ms_tenant = vot.id_ms_tenant)")
  			.append(" inner join ms_vendor v on (vot.id_ms_vendor = v.id_ms_vendor)")
  			.append(" WHERE tn.is_active ='1' ")
  				.append(" AND tn.tenant_code = :tenantCode AND v.vendor_code = :vendorCode");
		
		Map<String, Object> params = new HashMap<>();
		params.put("tenantCode", StringUtils.upperCase(tenantCode));
		params.put("vendorCode", StringUtils.upperCase(vendorCode));
		
		return (String) this.managerDAO.selectOneNativeString(query.toString(), params);		
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<MsTenant> getListTenant() {
		Map<String, Object> mapListTenant =  this.getManagerDAO().list(MsTenant.class, 
				new Object[][] {{ActiveAndUpdateableEntity.IS_ACTIVE_HBM, "1"}}, null);
		return (List<MsTenant>) mapListTenant.get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public List<MsLov> getPaymentSignTypeByIdTenant(long idTenant) {
		if (0 == idTenant)
			return Collections.emptyList();
		
		StringBuilder query = new StringBuilder();
		query.append(" SELECT lovPst.id_lov, lovPst.is_active, lovPst.code, lovPst.description ")
				.append(" FROM ms_lov lovPst ")
				.append(" JOIN ms_paymentsigntypeoftenant psot ON lovPst.id_lov = psot.lov_payment_sign_type ")
				.append(" JOIN ms_tenant mt ON psot.id_ms_tenant = mt.id_ms_tenant ")
				.append(" WHERE lovPst.is_active ='1' AND mt.is_active ='1' ")
					.append(" AND mt.id_ms_tenant = :idMsTenant ");
		
		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), 
				new Object[][] {{MsTenant.ID_TENANT_HBM, idTenant}});
		
		Iterator<Map<String, Object>> itr = result.iterator();
		List<MsLov> paymentSignTypeList = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			MsLov lovPaymentSignType = new MsLov();
			lovPaymentSignType.setIdLov(((BigInteger) map.get("d0")).longValue());
			lovPaymentSignType.setIsActive((String) map.get("d1"));
			lovPaymentSignType.setCode((String) map.get("d2"));
			lovPaymentSignType.setDescription((String) map.get("d3"));
			
			paymentSignTypeList.add(lovPaymentSignType);
		}
		return paymentSignTypeList;
	}

	@Override
	public List<MsTenant> getListTenantByUser(AmMsuser user) {
		if (null == user)
			return Collections.emptyList();

		StringBuilder query = new StringBuilder();
		query.append(" SELECT mt.id_ms_tenant, mt.tenant_code, mt.tenant_name, mt.is_active ")
				.append(" FROM ms_tenant mt ")
				.append(" JOIN ms_useroftenant uot ON uot.id_ms_tenant = mt.id_ms_tenant ")
				.append(" JOIN am_msuser amu ON amu.id_ms_user = uot.id_ms_user ")
				.append(" WHERE mt.is_active ='1' AND amu.is_active ='1' AND amu.id_ms_user = :idMsUser ");
		
		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), new Object[][] {{"idMsUser", user.getIdMsUser()}});
		
		Iterator<Map<String, Object>> itr = result.iterator();
		List<MsTenant> tenantList = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			MsTenant tenant = new MsTenant();
			tenant.setIdMsTenant(((BigInteger) map.get("d0")).longValue());
			tenant.setTenantCode((String) map.get("d1"));
			tenant.setTenantName((String) map.get("d2"));
			tenant.setIsActive((String) map.get("d3"));

			tenantList.add(tenant);
		}
		return tenantList;
	}

	@Override
	public MsUseroftenant getUseroftenantByUserTenant(AmMsuser user, MsTenant tenant) {
		return this.managerDAO.selectOne(MsUseroftenant.class, 
				new Object[][] {{ Restrictions.eq("amMsuser", user) },{ Restrictions.eq("msTenant", tenant) }
		});
	}
	
	@Override
	public MsUseroftenant getUseroftenantByLoginIdTenantCode(String loginId, String tenantCode) {
		return this.managerDAO.selectOne("from MsUseroftenant uot "
				+ " join fetch uot.amMsuser amu "
				+ " join fetch uot.msTenant mt "
				+ " where amu.loginId = :loginId and mt.tenantCode = :tenantCode", 
				new Object[][] {{ AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId) },
								{ MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode) }
		});
	}

	@Override
	public MsTenant getTenantByUser(String loginId) {
		StringBuilder query = new StringBuilder();
		query
			.append(" select id_ms_tenant ")
			.append(" from am_msuser mu ")
			.append(" join ms_useroftenant ut on mu.id_ms_user = ut.id_ms_user ")
			.append(" join ms_vendor_registered_user vru on mu.id_ms_user = vru.id_ms_user ")
			.append(" where signer_registered_email = :loginId ")
			.append(" order by ut.id_ms_useroftenant desc limit 1 ");
		
		BigInteger idMsTenant = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(),
				new Object[][] {{ AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId) }});
		return this.managerDAO.selectOne(MsTenant.class,
				new Object[][] {{ Restrictions.eq(MsTenant.ID_TENANT_HBM, idMsTenant.longValue()) }});
	}

	@Override
	public void updateTenant(MsTenant tenant) {
		tenant.setUsrUpd(MssTool.maskData(tenant.getUsrUpd()));
		this.managerDAO.update(tenant);
	}
	
	@Override
	public List<MsTenant> getListTenantByVendor(MsVendor vendor) {
		if (null == vendor)
			return Collections.emptyList();
		
		StringBuilder query = new StringBuilder();
		query.append("select t.id_ms_tenant, t.tenant_code, t.tenant_name, t.is_active ")
			 .append("from ms_tenant t ")
			 .append("join ms_vendoroftenant vot on vot.id_ms_tenant = t.id_ms_tenant ")
			 .append("join ms_vendor v on v.id_ms_vendor = vot.id_ms_vendor ")
			 .append("where v.vendor_code = :vendorCode and t.is_active = '1' ");
		
		List<Map<String, Object>> listMapTenant = this.managerDAO.selectAllNativeString(query.toString(),
				new Object[][] {{"vendorCode", vendor.getVendorCode()}});
		
		Iterator<Map<String, Object>> itr = listMapTenant.iterator();
		List<MsTenant> listTenant = new ArrayList<>();
		while(itr.hasNext()) {
			Map<String, Object> map = itr.next();
			MsTenant tenant = new MsTenant();
			BigInteger id = (BigInteger) map.get("d0");
			tenant.setIdMsTenant(id.longValue());
			tenant.setTenantCode((String) map.get("d1"));
			tenant.setTenantName((String) map.get("d2"));
			tenant.setIsActive((String) map.get("d3"));
			
			listTenant.add(tenant);
		}
		
		return listTenant;
	}

	@Override
	public List<TenantListBean> getListTenant(String tenantName, String status) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder query = new StringBuilder();
		String conditionalQuery = this.constructListTenantParam(params, tenantName, status);
		query
			.append(" select tenant_code as \"tenantCode\", tenant_name as \"tenantName\", is_active as \"isActive\" ")
			.append(" from ms_tenant where 1=1 ")
			.append(conditionalQuery);
		
		return this.managerDAO.selectForListString(TenantListBean.class, query.toString(), params, null);
	}
	
	private String constructListTenantParam(Map<String, Object> params, String tenantName, String isActive) {
		StringBuilder conditionalQuery = new StringBuilder();
		
		if (StringUtils.isNotBlank(tenantName)) {
			params.put("tenantName", "%" + StringUtils.upperCase(tenantName) + "%");
			conditionalQuery.append(" and tenant_name LIKE :tenantName ");
		}
		if (StringUtils.isNotBlank(isActive)) {
			params.put(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, isActive);
			conditionalQuery.append(" and is_active = :isActive ");
		}
		return conditionalQuery.toString();
	}

	@Override
	public void insertUserOfTenant(MsUseroftenant useroftenant) {
		useroftenant.setUsrCrt(MssTool.maskData(useroftenant.getUsrCrt()));
		this.managerDAO.insert(useroftenant);
	}
	
	@Override
	public List<BalanceTenantBean> getListBalanceTenant(String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		
		StringBuilder query = new StringBuilder();
		query.append(" select v.vendor_code as \"vendorCode\", v.vendor_name as \"vendorName\", ")
			 .append(" l.code as \"balanceTypeCode\", l.description as \"balanceTypeName\", ")
			 .append(" case when bvot.is_active is null then '0' else bvot.is_active end as \"isActive\" ")
			 .append(" from ms_lov l, ms_vendor v ")
			 .append(" left join lateral ( select bvot.is_active ")
			 					 .append(" from ms_balancevendoroftenant bvot ")
			 					 .append(" inner join ms_tenant t on t.id_ms_tenant = bvot.id_ms_tenant ")
			 					 .append(" where bvot.id_ms_vendor = v.id_ms_vendor ")
			 					 .append(" and t.tenant_code = :tenantCode ")
			 					 .append(" and lov_balance_type = l.id_lov ) bvot on true ")
			 .append(" where l.lov_group = '"+GlobalVal.LOV_GROUP_BALANCE_TYPE+"' ");
		
		return this.managerDAO.selectForListString(BalanceTenantBean.class, query.toString(), params, null);
	}

	@Override
	public void deleteBalanceTenant(String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		StringBuilder query = new StringBuilder();
		query.append(" delete from ms_balancevendoroftenant bvot ")
			 .append(" using ms_tenant t  ")
			 .append(" where t.id_ms_tenant = bvot.id_ms_tenant and t.tenant_code = :tenantCode ");
		this.managerDAO.deleteNativeString(query.toString(), params);
	}

	@Override
	public void insertBalanceTenant(List<MsBalancevendoroftenant> listBalanceTenant) {
		for (MsBalancevendoroftenant msBalancevendoroftenant : listBalanceTenant) {
			msBalancevendoroftenant.setUsrCrt(MssTool.maskData(msBalancevendoroftenant.getUsrCrt()));
		}
		this.managerDAO.insert(listBalanceTenant, listBalanceTenant.size());
	}

	@Override
	public List<BalanceTenantBean> getListBalanceTypeTenant(String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		
		StringBuilder query = new StringBuilder();
		query.append(" select l.code as \"balanceTypeCode\", l.description as \"balanceTypeName\" ")
			 .append(" from ms_lov l ")
			 .append(" join lateral ( select distinct(bvot.lov_balance_type) ")
			 					 .append(" from ms_balancevendoroftenant bvot ")
			 					 .append(" inner join ms_tenant t on t.id_ms_tenant = bvot.id_ms_tenant ")
			 					 .append(" where t.tenant_code = :tenantCode ")
			 					 .append(" and lov_balance_type = l.id_lov ) bvot on true ")
			 .append(" where l.lov_group = '"+GlobalVal.LOV_GROUP_BALANCE_TYPE+"' ");
		
		return this.managerDAO.selectForListString(BalanceTenantBean.class, query.toString(), params, null);
	}

	@Override
	public List<TenantListBean> getListTenantPaging(String tenantName, String isActive, int min, int max) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder query = new StringBuilder();
		String conditionalQuery = this.constructListTenantParam(params, tenantName, isActive);
		query
			.append("select \"tenantCode\", \"tenantName\", \"isActive\" from ( ")
			.append(" select tenant_code as \"tenantCode\", tenant_name as \"tenantName\", is_active as \"isActive\", ")
			.append("row_number() over (order by id_ms_tenant asc) as \"row\" ")
			.append(" from ms_tenant where 1=1 ")
			.append(conditionalQuery)
			.append(") as a where row between :min and :max ");
		params.put("min", min);
		params.put("max", max);
		
		return this.managerDAO.selectForListString(TenantListBean.class, query.toString(), params, null);
	}

	@Override
	public int countListTenantPaging(String tenantName, String isActive) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder query = new StringBuilder();
		String conditionalQuery = this.constructListTenantParam(params, tenantName, isActive);
		query
			.append(" select count(*) ")
			.append(" from ms_tenant where 1=1 ")
			.append(conditionalQuery);
		return ((BigInteger) this.getManagerDAO().selectOneNativeString(query.toString(), params)).intValue();
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsTenant getTenantByCodeNewTrx(String tenantCode) {
		// Query diubah pakai left join karena untuk sekarang ada case tenant id_email_hosting null atau notif_type null
				if (StringUtils.isBlank(tenantCode))
					return null;
			
				return this.managerDAO.selectOne(
						"from MsTenant t "
						+ "left join fetch t.msLov "
						+ "left join fetch t.msEmailHosting "
						+ "left join fetch t.lovSmsGateway "
						+ "where t.tenantCode = :tenantCode and t.isActive ='1'", 
								new Object[][] {{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}});
	}
	
	@Override
	public List<TenantRekonBean> getListTenantRekon(String vendorCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsVendor.VENDOR_CODE_HBM, StringUtils.upperCase(vendorCode));
		
		StringBuilder query = new StringBuilder();
			query.append(" SELECT DISTINCT(mt.tenant_code) AS \"tenantCode\", mv.vendor_code AS \"vendorCode\",  mt.tenant_name AS \"tenantName\"  ")
			 .append(" FROM ms_vendoroftenant mvot ")
			 .append(" JOIN ms_vendor mv ON mv.id_ms_vendor = mvot.id_ms_vendor ")
			 .append(" JOIN ms_tenant mt ON mt.id_ms_tenant = mvot.id_ms_tenant ")
			 .append(" where mv.vendor_code = :vendorCode ")
			 .append(" AND mv.is_active = '1' ")
			 .append(" AND mt.is_active = '1' ")
			 .append(" AND mvot.token IS NOT null");
		
		
		return this.managerDAO.selectForListString(TenantRekonBean.class, query.toString(), params, null);
	}

	@Override
	public MsUseroftenant getTenantByloginId(String loginId, String tenantCode) {
		Object[][] params = new Object[][] {{"loginid", StringUtils.upperCase(loginId)}, {"tenantcode", StringUtils.upperCase(tenantCode)}};
		
		return this.managerDAO.selectOne(
				"from MsUseroftenant nt "
				+ "join fetch nt.msTenant mt "
				+ "join fetch nt.amMsuser us "
				+ "where us.loginId = :loginid and mt.tenantCode = :tenantcode  "
				, params);
	}

	@Override
	public String getApiKeyBytenantCode(String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		
		StringBuilder query = new StringBuilder();
			query.append("SELECT api_key FROM ms_tenant WHERE tenant_code = :tenantCode ");
			return (String) managerDAO.selectOneNativeString(query.toString(), params);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<MsUseroftenant> getUserTenant(long idMsUser) {
		Object[][] queryParams = {{AmMsuser.ID_MS_USER_HBM, idMsUser}};
		
		return (List<MsUseroftenant>) this.managerDAO.list(
				"from MsUseroftenant uot "
				+ "JOIN fetch uot.amMsuser amu "
				+ "JOIN fetch uot.msTenant mt "
				+ "where amu.idMsUser = :idMsUser ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
		
			
	}

	@Override
	public MsTenant getTenantByApiKeyAndTenantCode(String apiKey, String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		params.put("apiKey", apiKey);
		
		return managerDAO.selectOne(
				"from MsTenant mt "
				+ "where mt.apiKey = :apiKey and mt.tenantCode = :tenantCode ", params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsTenant getTenantByApiKeyAndTenantCodeNewTrx(String apiKey, String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		params.put("apiKey", apiKey);
		
		return managerDAO.selectOne(
				"from MsTenant mt "
				+ "where mt.apiKey = :apiKey ", params);
	}
	
}
