package com.adins.esign.businesslogic.api;

import com.adins.esign.webservices.model.RegisterResponse;
import com.adins.esign.webservices.model.RegistrationByInvitationRequest;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.external.RegisterExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface RegistrationLogic {
	RegisterResponse registerByInvitation(RegistrationByInvitationRequest request, AuditContext audit);
	RegisterExternalResponse registerExternal(RegisterExternalRequest request, String apiKey, AuditContext audit);
}
