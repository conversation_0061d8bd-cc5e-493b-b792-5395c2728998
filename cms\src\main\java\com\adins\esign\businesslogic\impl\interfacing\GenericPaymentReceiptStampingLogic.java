package com.adins.esign.businesslogic.impl.interfacing;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.CommonStampingLogic;
import com.adins.esign.businesslogic.api.interfacing.PaymentReceiptStampingLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.MediaType;
import com.adins.esign.constants.enums.StampingDocumentType;
import com.adins.esign.constants.enums.StampingErrorDetail;
import com.adins.esign.constants.enums.StampingErrorLocation;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsPeruriDocType;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDStampduty;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.EmeteraiPajakkuLoginRequestBean;
import com.adins.esign.model.custom.EmeteraiPajakkuLoginResponseBean;
import com.adins.esign.model.custom.IncrementAgreementStampingErrorCountBean;
import com.adins.esign.model.custom.PaymentReceiptConfinsSyncBean;
import com.adins.esign.model.custom.PaymentReceiptConfinsSyncRequest;
import com.adins.esign.model.custom.PaymentReceiptConfinsSyncResponse;
import com.adins.esign.model.custom.SignLocationBean;
import com.adins.esign.model.custom.StampProcessParameter;
import com.adins.esign.model.custom.UploadDocPajakkuResponseBean;
import com.adins.esign.model.custom.UploadPaymentReceiptToDmsResponse;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.webservices.model.GenerateEmeteraiPajakkuRequest;
import com.adins.esign.webservices.model.GenerateEmeteraiPajakkuResponse;
import com.adins.esign.webservices.model.StampingEmeteraiPajakkuRequest;
import com.adins.esign.webservices.model.StampingEmeteraiPajakkuResponse;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptRequest;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptResponse;
import com.adins.esign.webservices.model.confins.UploadStampedDocumentResponse;
import com.adins.esign.webservices.model.confins.UploadStampedPaymenReceiptRequest;
import com.adins.exceptions.EmeteraiException;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.EmeteraiException.ReasonEmeterai;
import com.adins.exceptions.LovException;
import com.adins.exceptions.StampingException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.LovException.Reason;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.google.gson.Gson;

import okhttp3.Headers;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

@Component
public class GenericPaymentReceiptStampingLogic extends BaseLogic implements PaymentReceiptStampingLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericPaymentReceiptStampingLogic.class);
	
	public static final String METERAI_PROCESS_NOT_STARTED	= "320";
	public static final String METERAI_PROCESS_FAILED		= "321";
	public static final String METERAI_PROCESS_IN_QUEUE		= "322";
	public static final String METERAI_PROCESS_SUCCESS		= "323";
	public static final String METERAI_PROCESS_IN_PROGRESS	= "325";
	
	private static final String PAJAKKU_SUCCESS_CODE	= "00";
	private static final String PAJAKKU_ERROR_CODE		= "01";
	
	public static final String DOC_TRX_ID_DELIMITER = ";";
	
	private static final String BEARER	= "Bearer ";
	
	private static final String MANDATORY_CANNOT_BE_EMPTY = "businesslogic.document.mandatorycannotbeempty";
	
	// Peruri URL
	@Value("${e-meterai.pajakku.login}") private String urlLogin;
	@Value("${e-meterai.pajakku.upload}") private String urlUploadDoc;
	@Value("${emeterai.pajakku.generate.uri}") private String generateEmeteraiUrl;
	@Value("${emeterai.pajakku.stamping.uri}") private String urlStamping;
	
	// Peruri API parameter
	@Value("${e-meterai.pajakku.processnumber}") private String processNumber;
	@Value("${emeterai.pajakku.folder}") private String folder;
	@Value("${emeterai.pajakku.certificatelevel}") private String certifLevel;
	@Value("${emeterai.pajakku.profilename}") private String profileName;
	
	@Autowired private Gson gson;
	
	@Autowired CloudStorageLogic cloudStorageLogic;
	@Autowired CommonLogic commonLogic;
	@Autowired CommonStampingLogic commonStampingLogic;
	@Autowired CommonValidatorLogic commonValidatorLogic;
	
	private GenerateEmeteraiPajakkuResponse callGenerateEmeteraiApi(GenerateEmeteraiPajakkuRequest request, String peruriLoginToken, TrDocumentD document, long connectionTimeout, long readTimeout) throws IOException {
		String url = generateEmeteraiUrl;
		String jsonRequest = gson.toJson(request);
		LOG.info("Kontrak {}, Dokumen {}, Generate Emeterai request: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonRequest);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.AUTHORIZATION, BEARER + peruriLoginToken);
		mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
		WebClient client = WebClient.create(url).headers(mapHeader);
		MssTool.setWebClientConnReadTimeout(client, connectionTimeout, readTimeout);
		
		Response response = client.post(jsonRequest);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String generateResult = IOUtils.toString(isReader);
		
		LOG.info("Kontrak {}, Dokumen {}, Generate Emeterai response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), generateResult);
		GenerateEmeteraiPajakkuResponse generateResponse = gson.fromJson(generateResult, GenerateEmeteraiPajakkuResponse.class);
		if (!GlobalVal.PERURI_SUCCESS_CODE.equals(generateResponse.getStatusCode())) {
			generateResponse.setErrorMessage(generateResult);
			generateResponse.setErrorMsg(generateResult);
			generateResponse.setJsonRequest(jsonRequest);
			generateResponse.setJsonResponse(generateResult);
		}
		return generateResponse;
	}
	
	private StampingEmeteraiPajakkuResponse callStampEmeteraiApi(StampingEmeteraiPajakkuRequest request, String peruriLoginToken, TrDocumentD document, long connectionTimeout, long readTimeout) throws IOException {
		
		String jsonRequest = gson.toJson(request);
		LOG.info("Kontrak {}, Dokumen {}, Stamping Emeterai request: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), jsonRequest);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.AUTHORIZATION, BEARER + peruriLoginToken);
		mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
		WebClient client = WebClient.create(urlStamping).headers(mapHeader);
		MssTool.setWebClientConnReadTimeout(client, connectionTimeout, readTimeout);
		
		Response response = client.post(jsonRequest);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String result = IOUtils.toString(isReader);
		
		LOG.info("Kontrak {}, Dokumen {}, Stamping Emeterai response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), result);
		StampingEmeteraiPajakkuResponse stampingResponse = gson.fromJson(result, StampingEmeteraiPajakkuResponse.class);
		if (!GlobalVal.PERURI_SUCCESS_CODE.equals(stampingResponse.getErrorCode())) {
			stampingResponse.setErrorMsg(result);
			stampingResponse.setErrorMessage(result);
			stampingResponse.setJsonRequest(jsonRequest);
			stampingResponse.setJsonResponse(result);
		}
		
		return stampingResponse;
	}
	
	private String getDocumentFileToUpload(TrDocumentD document, AuditContext audit) {
		String transactionId = document.getTransactionId();
		String[] transactionIds = {};
		
		if (StringUtils.isNotBlank(transactionId)) {
			transactionIds = transactionId.split(DOC_TRX_ID_DELIMITER);
		}
		
		// Get stamped document
		if (transactionIds.length > 0) {
			return commonStampingLogic.getStampedDocumentFromOss(document, audit);
		}
		
		// Get payment receipt document from OSS
		byte[] documentByteArray = cloudStorageLogic.getStampingPaymentReceipt(document);
		if (null == documentByteArray) {
			throw new EmeteraiException(getMessage("businesslogic.emeterai.failedtogetossdocument",
					new String[] {document.getDocumentId()}, audit), ReasonEmeterai.DOWNLOAD_DOC_EXCEPTION);
		}
		
		return Base64.getEncoder().encodeToString(documentByteArray);
	}

	private EmeteraiPajakkuLoginResponseBean loginPajakku(TrDocumentH documentH, StampProcessParameter param, AuditContext audit) {
		
		long connectionTimeout = param.getConnectionTimeout();
		long readTimeout = param.getReadTimeout();
		boolean throwMaxError = param.isThrowMaxError();
		
		String jsonRequest = null;
		String jsonResponse = null;
		
		try {
			String username = commonStampingLogic.getAccountUsername(documentH, audit);
			String password = commonStampingLogic.getAccountPassword(documentH, audit);
			
			if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
				String message = getMessage("businesslogic.emeterai.emptylogincredential", null, audit);
				throw new EmeteraiException(message, ReasonEmeterai.INVALID_CREDENTIAL);
			}
			
			// Prepare header
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			WebClient client = WebClient.create(urlLogin).headers(mapHeader);
			MssTool.setWebClientConnReadTimeout(client, connectionTimeout, readTimeout);
			
			// Prepare request body
			EmeteraiPajakkuLoginRequestBean request = new EmeteraiPajakkuLoginRequestBean();
			request.setUser(username);
			request.setPassword(password);
			jsonRequest = gson.toJson(request);
			LOG.info("Kontrak {}, Login Pajakku request: {}", documentH.getRefNumber(), jsonRequest);
			
			// Get response
			Response clientResponse = client.post(jsonRequest);
			InputStreamReader isReader = new InputStreamReader((InputStream) clientResponse.getEntity());
			jsonResponse = IOUtils.toString(isReader);
			LOG.info("Kontrak {}, Login Pajakku response: {}", documentH.getRefNumber(), jsonResponse);
			
			EmeteraiPajakkuLoginResponseBean response = gson.fromJson(jsonResponse, EmeteraiPajakkuLoginResponseBean.class);
			response.setJsonRequest(jsonRequest);
			response.setJsonResponse(jsonResponse);
			
			if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, null, jsonResponse, null, jsonRequest, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.LOGIN, StampingErrorDetail.FAIL_RESPONSE, throwMaxError, audit);
			}
			
			return response;
		} catch (StampingException e) {
			throw e;
		} catch (Exception e) {
			
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, null, e.getLocalizedMessage(), e, jsonRequest, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.LOGIN, StampingErrorDetail.EXCEPTION, throwMaxError, audit);
			
			EmeteraiPajakkuLoginResponseBean response = new EmeteraiPajakkuLoginResponseBean();
			response.setStatusCode(PAJAKKU_ERROR_CODE);
			response.setErrorMsg(e.getLocalizedMessage());
			response.setErrorMessage(e.getLocalizedMessage());
			response.setException(e);
			response.setJsonRequest(jsonRequest);
			response.setJsonResponse(jsonResponse);
			return response;
		}
	}
	
	private UploadDocPajakkuResponseBean uploadDocPajakku(TrDocumentD document, TrDocumentDStampduty docSdt, String token, StampProcessParameter param, AuditContext audit) {
		long connectionTimeout = param.getConnectionTimeout();
		long readTimeout = param.getReadTimeout();
		boolean throwMaxError = param.isThrowMaxError();
		
		String jsonResponse = null;
		try {
			// Set stamping start time
			if (null == docSdt.getStartStampProcess()) {
				docSdt.setStartStampProcess(new Date());
				daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(docSdt);
			}
			
			UploadDocPajakkuResponseBean response = new UploadDocPajakkuResponseBean();
			
			if (StringUtils.isNotBlank(document.getTransactionId()) && document.getTransactionId().split(DOC_TRX_ID_DELIMITER).length >= document.getTotalMaterai()) {
				response.setStatusCode(GlobalVal.PERURI_SUCCESS_CODE);
				return response;
			}
			
			String pdfBase64 = getDocumentFileToUpload(document, audit);
			if (StringUtils.isBlank(pdfBase64)) {
				String message = "Empty document file";
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, message, null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.UPL_DOC, StampingErrorDetail.VALIDATION, throwMaxError, audit);
				
				response.setErrorCode(PAJAKKU_ERROR_CODE);
				response.setErrorMsg(message);
				return response;
			}
			
			byte[] pdfFile = Base64.getDecoder().decode(pdfBase64);
			String fileName = document.getDocumentId() + ".pdf";
				
			OkHttpClient client = new OkHttpClient.Builder()
					.connectTimeout(connectionTimeout, TimeUnit.MILLISECONDS)
					.writeTimeout(15L, TimeUnit.SECONDS)
					.readTimeout(readTimeout, TimeUnit.MILLISECONDS)
					.build();
				
			RequestBody body = new MultipartBody.Builder()
					.setType(MultipartBody.FORM)
					.addFormDataPart("file", fileName, RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_PDF), pdfFile))
					.addFormDataPart("token", token)
					.build();
				
			Request request = new Request.Builder()
					.url(urlUploadDoc)
					.addHeader(HttpHeaders.AUTHORIZATION, BEARER + token)
					.post(body).build();
			
			LOG.info("Kontrak {}, Dokumen {}, Upload document to Peruri with filename: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), fileName);
			
			okhttp3.Response responseClient = client.newCall(request).execute();
			String result = responseClient.body().string();
			jsonResponse = result;
			LOG.info("Kontrak {}, Dokumen {}, Upload document to Peruri response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), result);
			
			response = gson.fromJson(result, UploadDocPajakkuResponseBean.class);
			
			if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, jsonResponse, null, null, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.UPL_DOC, StampingErrorDetail.FAIL_RESPONSE, throwMaxError, audit);
				
				response.setErrorMessage(response.getMessage());
				response.setJsonResponse(result);
				return response;
			}
			
			String updatedTransactionId;
			if (StringUtils.isNotBlank(document.getTransactionId())) {
				updatedTransactionId = document.getTransactionId() + ";" + response.getId();
			} else {
				updatedTransactionId = response.getId();
			}
			LOG.info("Kontrak {}, Dokumen {}, Updated Transaction Id: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), updatedTransactionId);
			
			String updatedDocumentNameSdt;
			if (StringUtils.isNotBlank(document.getDocumentNameSdt())) {
				updatedDocumentNameSdt = document.getDocumentNameSdt() + ";" + response.getSaveAs();
			} else {
				updatedDocumentNameSdt = response.getSaveAs();
			}
			LOG.info("Kontrak {}, Dokumen {}, Updated Document Name Sdt: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), updatedDocumentNameSdt);
					
			document.setUsrUpd(audit.getCallerId());
			document.setDtmUpd(new Date());
			document.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_GEN_SDT);
			document.setTransactionId(updatedTransactionId);
			document.setDocumentNameSdt(updatedDocumentNameSdt);
			daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
			
			commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_GEN_SDT, audit);
			return response;
		} catch (StampingException e) {
			throw e;
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Upload document to Peruri exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage());
			
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, null, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.UPL_DOC, StampingErrorDetail.EXCEPTION, throwMaxError, audit);
			
			UploadDocPajakkuResponseBean response = new UploadDocPajakkuResponseBean();
			response.setStatusCode(PAJAKKU_ERROR_CODE);
			response.setErrorMessage(response.getMessage());
			response.setJsonResponse(jsonResponse);
			return response;
		}
	}	
	
	private GenerateEmeteraiPajakkuResponse generateEmeteraiPajakku(TrDocumentD document, TrDocumentDStampduty docSdt, int currentLoop, String peruriLoginToken, StampProcessParameter param, AuditContext audit) {
		long connectionTimeout = param.getConnectionTimeout();
		long readTimeout = param.getReadTimeout();
		boolean throwMaxError = param.isThrowMaxError();
		
		try {
			GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
			
			if (StringUtils.isBlank(document.getTransactionId())) {
				String message = "Transaction id empty";
				
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, message, null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.GEN_SDT, StampingErrorDetail.VALIDATION, throwMaxError, audit);
			
				response.setMessage(message);
				response.setStatusCode(PAJAKKU_ERROR_CODE);
				response.setErrorMsg(message);
				return response;
			}
			
			int sdtNeeded = document.getTotalMaterai() - document.getTotalStamping();
			int availableSdt = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
			
			LOG.info("Kontrak {}, Document {}, Need SDT: {}, Available SDT: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), sdtNeeded, availableSdt);
			if (availableSdt >= sdtNeeded) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_STM_SDT, audit);
				response.setMessage("Stamp duty already available");
				response.setStatusCode(PAJAKKU_SUCCESS_CODE);
				return response;
			}
			
			boolean enoughBalance = commonStampingLogic.enoughSdtBalance(document, sdtNeeded, audit);
			if (!enoughBalance) {
				String message = "Not enough stamp duty balance";
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, message, null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.GEN_SDT, StampingErrorDetail.VALIDATION, throwMaxError, audit);
				
				response.setMessage(message);
				response.setStatusCode(PAJAKKU_ERROR_CODE);
				response.setErrorMsg(message);
				return response;
			}
			
			String[] transactionIds = document.getTransactionId().split(DOC_TRX_ID_DELIMITER);
			String[] documentNames = document.getDocumentNameSdt().split(DOC_TRX_ID_DELIMITER);
			
			String nilaiMeteraiLunas = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_NILAI_METERAI_LUNAS, audit);
			if (StringUtils.isBlank(nilaiMeteraiLunas)) {
				nilaiMeteraiLunas = GlobalVal.PAJAKKU_NILAI_METERAI_LUNAS;
			}
			
			GenerateEmeteraiPajakkuRequest request = new GenerateEmeteraiPajakkuRequest();
			request.setIdfile(transactionIds[currentLoop]);
			request.setUpload(true);
			request.setNamadoc(commonStampingLogic.getNamaDocForGenerate(document, audit));
			request.setNamafile(documentNames[currentLoop]);
			request.setNilaidoc(nilaiMeteraiLunas);
			request.setSnOnly(false);
			request.setNodoc(commonStampingLogic.getNoDocForGenerate(document, audit));
			request.setTgldoc(commonStampingLogic.getTglDocForGenerate(document, audit));
			
			if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && "1".equals(document.getTrDocumentH().getIsPostpaidStampduty()) && StringUtils.isNotBlank(document.getDocumentName())) {
				request.setNamejidentitas(document.getMsLovIdType().getCode());
				request.setNoidentitas(document.getIdNo());
				request.setNamedipungut(document.getIdName());
			}
			
			response = callGenerateEmeteraiApi(request, peruriLoginToken, document, connectionTimeout, readTimeout);
			if (!PAJAKKU_SUCCESS_CODE.equals(response.getStatusCode())) {
				String message = response.getErrorMessage();
				String jsonRequest = response.getJsonRequest();
				String jsonResponse = response.getJsonResponse();
				
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, message, null, jsonRequest, jsonResponse);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.GEN_SDT, StampingErrorDetail.FAIL_RESPONSE, throwMaxError, audit);
				return response;
			}
			
			MsTenant tenant = document.getMsTenant();
			MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
			
			String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_AVAILABLE);
			MsLov balanceType = null;
			MsLov trxType = null;
			if ("1".equals(document.getTrDocumentH().getIsPostpaidStampduty())) {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT_POSTPAID);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT_POSTPAID);
			} else {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT);
			}
			
			TrStampDuty sdt = new TrStampDuty();
			sdt.setTrxNo(trxNo);
			sdt.setStampDutyNo(response.getResult().getSn());
			sdt.setStampQr(response.getResult().getFilenameQR());
			sdt.setMsLov(sdtStatus);
			sdt.setUsrCrt(audit.getCallerId());
			sdt.setDtmCrt(new Date());
			sdt.setMsTenant(tenant);
			sdt.setMsVendor(vendor);
			sdt.setStampDutyFee(Integer.valueOf(nilaiMeteraiLunas));
			sdt.setTransactionId(transactionIds[currentLoop]);
			sdt.setDocumentNameSdt(documentNames[currentLoop]);
			daoFactory.getStampDutyDao().insertTrStampDutyNewTran(sdt);
			LOG.info("Kontrak {}, stamp duty with SN {} inserted.", document.getTrDocumentH().getRefNumber(), sdt.getStampDutyNo());
			
			docSdt.setTrStampDuty(sdt);
			docSdt.setUsrUpd(audit.getCallerId());
			docSdt.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(docSdt);
			
			String notes = commonStampingLogic.getBalanceMutationNotesForGenerate(docSdt, audit);
			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(trxNo);
			mutation.setTrxDate(new Date());
			mutation.setRefNo(document.getTrDocumentH().getRefNumber());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setTrDocumentD(document);
			mutation.setTrDocumentH(document.getTrDocumentH());
			mutation.setNotes(notes);
			mutation.setTrStampDuty(sdt);
			mutation.setUsrCrt(audit.getCallerId());
			mutation.setDtmCrt(new Date());
			mutation.setMsOffice(document.getTrDocumentH().getMsOffice());
			mutation.setMsBusinessLine(document.getTrDocumentH().getMsBusinessLine());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
			LOG.info("Kontrak {}, balance mutation with stamp duty SN {} inserted.", document.getTrDocumentH().getRefNumber(), sdt.getStampDutyNo());
			
			commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_STM_SDT, audit);
			return response;
		} catch (StampingException e) {
			throw e;
		} catch (Exception e) {
			LOG.error("Generate Emeterai exception: {}", e.getLocalizedMessage(), e);
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, null, null);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.GEN_SDT, StampingErrorDetail.EXCEPTION, throwMaxError, audit);
			
			GenerateEmeteraiPajakkuResponse response = new GenerateEmeteraiPajakkuResponse();
			response.setMessage(e.getLocalizedMessage());
			response.setStatusCode(PAJAKKU_ERROR_CODE);
			response.setErrorMsg(e.getLocalizedMessage());
			return response;
		}
	}
	
	private StampingEmeteraiPajakkuResponse stampEmeteraiPajakku(TrDocumentD document, TrDocumentDStampduty docSdt, String peruriLoginToken, StampProcessParameter param, AuditContext audit) {
		long connectionTimeout = param.getConnectionTimeout();
		long readTimeout = param.getReadTimeout();
		boolean throwMaxError = param.isThrowMaxError();
		
		TrDocumentH documentH = document.getTrDocumentH();
		TrStampDuty sdt = docSdt.getTrStampDuty();
		try {
			StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
			
			int sdtAvailable = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
			LOG.info("Kontrak {}, Dokumen {}, available SDT qty: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), sdtAvailable);
			if (0 == sdtAvailable) {
				String message = "No stamp duty for " + document.getDocumentId();
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, document, message, null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.STM_SDT, StampingErrorDetail.VALIDATION, throwMaxError, audit);
				
				response.setErrorCode(PAJAKKU_ERROR_CODE);
				response.setErrorMessage(message);
				response.setErrorMsg(message);
				return response;
			}
			
			String transactionId = sdt.getTransactionId();
			String documentName = sdt.getDocumentNameSdt();
			
			if (GlobalVal.CODE_LOV_SDT_GO_LIVE.equals(sdt.getMsLov().getCode())) {
				String message = "SDT with number " + sdt.getStampDutyNo() + " cannot be used.";
				
				IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, document, message, null, null, null);
				commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.STM_SDT, StampingErrorDetail.VALIDATION, throwMaxError, audit);
				
				response.setErrorCode(PAJAKKU_ERROR_CODE);
				response.setErrorMessage(message);
				response.setErrorMsg(message);
				return response;
			}
			
			SignLocationBean coordinate = gson.fromJson(docSdt.getSignLocation(), SignLocationBean.class);
			
			StampingEmeteraiPajakkuRequest request = new StampingEmeteraiPajakkuRequest();
			request.setOnPrem(false);
			request.setDocId(transactionId);
			request.setCertificatelevel(certifLevel);
			request.setDest(folder + "final_" +transactionId + ".pdf");
			request.setDocpass(StringUtils.EMPTY);
			request.setJwToken(peruriLoginToken);
			request.setLocation(document.getTrDocumentH().getMsOffice().getOfficeName());
			request.setProfileName(profileName);
			request.setReason(commonStampingLogic.getReasonForStamp(document, audit));
			request.setRefToken(sdt.getStampDutyNo());
			request.setSpesimenPath(folder + sdt.getStampQr());
			request.setSrc(folder + documentName);
			request.setRetryFlag("1");
			request.setVisLLX(Double.valueOf(coordinate.getLlx()));
			request.setVisLLY(Double.valueOf(coordinate.getLly()));
			request.setVisURX(Double.valueOf(coordinate.getUrx()));
			request.setVisURY(Double.valueOf(coordinate.getUry()));
			request.setVisSignaturePage(docSdt.getSignPage());
			
			response = callStampEmeteraiApi(request, peruriLoginToken, document, connectionTimeout, readTimeout);
			if (PAJAKKU_SUCCESS_CODE.equals(response.getErrorCode())) {
				// Update SDT Status to GO LIVE
				MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_GO_LIVE);
				sdt.setMsLov(sdtStatus);
				sdt.setUsrUpd(audit.getCallerId());
				sdt.setDtmUpd(new Date());
				daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
				
				// Update tr_document_d_stampduty, fill id_stamp_duty and stamp date
				docSdt.setTrStampDuty(sdt);
				docSdt.setStampingDate(new Date());
				docSdt.setUsrUpd(audit.getCallerId());
				docSdt.setDtmUpd(new Date());
				daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(docSdt);
				
				// Update documentD
				short stamped = document.getTotalStamping();
				stamped += 1;
				document.setTotalStamping(stamped);
				
				String currentLink = StringUtils.EMPTY;
				if (StringUtils.isNotBlank(document.getDocumentSdtLink())) {
					currentLink += document.getDocumentSdtLink() + DOC_TRX_ID_DELIMITER;
				}
				currentLink += response.getUrlFile();
				document.setDocumentSdtLink(currentLink);
				daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
				
				commonStampingLogic.storeStampedDocumentToOss(document, peruriLoginToken, audit);
				if (document.getTotalMaterai().equals(document.getTotalStamping())) {
					commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_SDT_FIN, audit);
				} else {
					commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
				}
				return response;
			}
			
			// If failed, call API again
			int retryAttemps = commonStampingLogic.getStampingRetryAttempts(audit);
			int i = 0;
			while (i < retryAttemps) {
				response = callStampEmeteraiApi(request, peruriLoginToken, document, connectionTimeout, readTimeout);
				if (PAJAKKU_SUCCESS_CODE.equals(response.getErrorCode())) {
					// Update SDT Status to GO LIVE
					MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_GO_LIVE);
					sdt.setMsLov(sdtStatus);
					sdt.setUsrUpd(audit.getCallerId());
					sdt.setDtmUpd(new Date());
					daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
					
					// Update tr_document_d_stampduty, fill id_stamp_duty and stamp date
					docSdt.setTrStampDuty(sdt);
					docSdt.setStampingDate(new Date());
					docSdt.setUsrUpd(audit.getCallerId());
					docSdt.setDtmUpd(new Date());
					daoFactory.getDocumentDao().updateDocumentDetailSdtNewTran(docSdt);
					
					// Update documentD
					short stamped = document.getTotalStamping();
					stamped += 1;
					document.setTotalStamping(stamped);
					
					String currentLink = StringUtils.EMPTY;
					if (StringUtils.isNotBlank(document.getDocumentSdtLink())) {
						currentLink += document.getDocumentSdtLink() + DOC_TRX_ID_DELIMITER;
					}
					currentLink += response.getUrlFile();
					document.setDocumentSdtLink(currentLink);
					daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
					
					commonStampingLogic.storeStampedDocumentToOss(document, peruriLoginToken, audit);
					if (document.getTotalMaterai().equals(document.getTotalStamping())) {
						commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_SDT_FIN, audit);
					} else {
						commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
					}
					return response;
				}
				i += 1;
			}
			
			// Update status tr_stamp_duty ke FAILED
			MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_STAMP_FAILED);
			sdt.setMsLov(sdtStatus);
			sdt.setErrorMessage(response.getErrorMessage());
			sdt.setUsrUpd(audit.getCallerId());
			sdt.setDtmUpd(new Date());
			daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
			
			String message = response.getErrorMsg();
			String jsonRequest = response.getJsonRequest();
			String jsonResponse = response.getJsonResponse();
			
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, document, message, null, jsonRequest, jsonResponse);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.STM_SDT, StampingErrorDetail.FAIL_RESPONSE, throwMaxError, audit);
			return response;	
			
		} catch (StampingException e) {
			throw e;
		} catch (Exception e) {
			LOG.info("Kontrak {}, Dokumen {}, Stamping Emeterai exception: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), e.getLocalizedMessage());
			IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(document.getTrDocumentH(), document, e.getLocalizedMessage(), e, null, null);
			commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.STM_SDT, StampingErrorDetail.EXCEPTION, throwMaxError, audit);
			
			StampingEmeteraiPajakkuResponse response = new StampingEmeteraiPajakkuResponse();
			response.setErrorCode(PAJAKKU_ERROR_CODE);
			response.setErrorMessage(e.getLocalizedMessage());
			response.setErrorMsg(e.getLocalizedMessage());
			response.setException(e);
			return response;
		}
	}
	
	/**
	 * 
	 * IF param.throwMaxError = <code>true</code>, will throw exception when retried X times.
	 * ELSE, will retry until success.<br>
	 * X is based on general settings.
	 */
	private String doAndRetryLoginPajakku(TrDocumentH documentH, StampProcessParameter param, AuditContext audit) {
		EmeteraiPajakkuLoginResponseBean loginResponse = loginPajakku(documentH, param, audit);
		if (PAJAKKU_SUCCESS_CODE.equals(loginResponse.getStatusCode())) {
			return loginResponse.getToken();
		}
		return doAndRetryLoginPajakku(documentH, param, audit);
	}
	
	private InsertStampingPaymentReceiptResponse doAndRetryStampPaymentReceipt(TrDocumentH documentH, String peruriLoginToken, StampProcessParameter param, boolean returnDocument, AuditContext audit) {
		
		List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
		TrDocumentD document = documents.get(0);
		List<TrDocumentDStampduty> docSdts = daoFactory.getDocumentDao().getDocumentStampDutyByIdDocumentDNewTran(document.getIdDocumentD());
		
		int currentLoop = document.getTotalStamping();
		for (int i = currentLoop; i < document.getTotalMaterai(); i++) {
			TrDocumentDStampduty docSdt = docSdts.get(i);
			if (GlobalVal.STEP_ATTACH_METERAI_NOT_STR.equals(document.getSdtProcess())) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
			}
			if (GlobalVal.STEP_ATTACH_METERAI_UPL_DOC.equals(document.getSdtProcess())) {
				UploadDocPajakkuResponseBean response = uploadDocPajakku(document, docSdt, peruriLoginToken, param, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
					return doAndRetryStampPaymentReceipt(documentH, peruriLoginToken, param, returnDocument, audit);
				}
			}
			if (GlobalVal.STEP_ATTACH_METERAI_GEN_SDT.equals(document.getSdtProcess())) {
				GenerateEmeteraiPajakkuResponse response = generateEmeteraiPajakku(document, docSdt, i, peruriLoginToken, param, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
					// Update tr_document_d untuk UPL DOC ulang
					String cutTransactionId = MssTool.cutLastObjectFromListString(document.getTransactionId(), DOC_TRX_ID_DELIMITER);
					String cutDocumentNameSdt = MssTool.cutLastObjectFromListString(document.getDocumentNameSdt(), DOC_TRX_ID_DELIMITER);
					document.setTransactionId(cutTransactionId);
					document.setDocumentNameSdt(cutDocumentNameSdt);
					document.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_UPL_DOC);
					document.setUsrUpd(audit.getCallerId());
					document.setDtmUpd(new Date());
					daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
					
					return doAndRetryStampPaymentReceipt(documentH, peruriLoginToken, param, returnDocument, audit);
				}
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_STM_SDT, audit);
			}
			if (GlobalVal.STEP_ATTACH_METERAI_STM_SDT.equals(document.getSdtProcess())) {
				StampingEmeteraiPajakkuResponse response = stampEmeteraiPajakku(document, docSdt, peruriLoginToken, param, audit);
				if (!GlobalVal.PERURI_STAMPING_SUCCESS_STATUS.equalsIgnoreCase(response.getStatus())) {
					// Update tr_document_d untuk UPL DOC ulang
					String cutTransactionId = MssTool.cutLastObjectFromListString(document.getTransactionId(), DOC_TRX_ID_DELIMITER);
					String cutDocumentNameSdt = MssTool.cutLastObjectFromListString(document.getDocumentNameSdt(), DOC_TRX_ID_DELIMITER);
					document.setTransactionId(cutTransactionId);
					document.setDocumentNameSdt(cutDocumentNameSdt);
					document.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_UPL_DOC);
					document.setUsrUpd(audit.getCallerId());
					document.setDtmUpd(new Date());
					daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
					
					// Update status tr_stamp_duty ke FAILED
					TrStampDuty sdt = docSdt.getTrStampDuty();
					MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_STAMP_FAILED);
					sdt.setMsLov(sdtStatus);
					sdt.setErrorMessage(response.getErrorMessage());
					sdt.setUsrUpd(audit.getCallerId());
					sdt.setDtmUpd(new Date());
					daoFactory.getStampDutyDao().updateTrStampDutyNewTran(sdt);
					
					return doAndRetryStampPaymentReceipt(documentH, peruriLoginToken, param, returnDocument, audit);
				}
			}
		}
		
		if (GlobalVal.STEP_ATTACH_METERAI_SDT_FIN.equals(document.getSdtProcess())) {
			commonStampingLogic.updateDocumentHMeteraiProcess(documentH, METERAI_PROCESS_SUCCESS, audit);
			
			documentH.setCallbackProcess((short) 11);
			documentH.setUsrUpd(audit.getCallerId());
			documentH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
			
			InsertStampingPaymentReceiptResponse response = new InsertStampingPaymentReceiptResponse();
			if (returnDocument) {
				response.setStampedDocument(commonStampingLogic.getStampedDocumentFromOss(document, audit));
			}
			return response;
		}
		
		String message = "Should not reach this process. Please check the data";
		IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, document, message, null, null, null);
		commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.FINAL_VAL, StampingErrorDetail.VALIDATION, true, audit);
		return new InsertStampingPaymentReceiptResponse();
	}
	
	private InsertStampingPaymentReceiptResponse stampPaymentReceipt(TrDocumentH documentH, boolean returnDocument, AuditContext audit) {
		
		long connectionTimeout = commonStampingLogic.getStampingConnectionTimeout(StampingDocumentType.PAYMENT_RECEIPT, audit);
		long readTimeout = commonStampingLogic.getStampingReadTimeout(StampingDocumentType.PAYMENT_RECEIPT, audit);
		
		StampProcessParameter param = new StampProcessParameter();
		param.setConnectionTimeout(connectionTimeout);
		param.setReadTimeout(readTimeout);
		param.setThrowMaxError(true);
	
		String peruriLoginToken = doAndRetryLoginPajakku(documentH, param, audit);
		return doAndRetryStampPaymentReceipt(documentH, peruriLoginToken, param, returnDocument, audit);
	}
	
	@Override
	public InsertStampingPaymentReceiptResponse insertStampingPaymentReceipt(InsertStampingPaymentReceiptRequest request, AuditContext audit) throws ParseException {
		Date now = new Date();
		String messageValidation = "";
		insertStampingPaymentReceiptValidation(request, audit);
		MsLov lovIdType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_ID_TYPE, request.getIdType());
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCodeNewTrx(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(this.messageSource.getMessage("businesslogic.paymentsigntype.tenantnotfound", null, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		this.checkDuplicateRefNumber(request.getDocumentNumber(), tenant, audit);
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_DIRJENPAJAK);
		
		MsRegion region = daoFactory.getRegionDao().getRegionByCodeAndTenantNewTrx(request.getRegionCode(), tenant.getTenantCode());
		if (null == region && StringUtils.isNotBlank(request.getRegionCode())) {
			region = new MsRegion();
			region.setDtmCrt(now);
			region.setUsrCrt(audit.getCallerId());
			region.setMsTenant(tenant);
			region.setRegionCode(request.getRegionCode());
			region.setRegionName(request.getRegionName());
			daoFactory.getRegionDao().insertRegionNewTrx(region);
		}
		
		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(request.getOfficeCode(), tenant.getTenantCode());
		if (null == office) {
			office = new MsOffice();
			office.setUsrCrt(audit.getCallerId());
			office.setDtmCrt(now);
			office.setOfficeCode(request.getOfficeCode());
			office.setOfficeName(request.getOfficeName());
			office.setIsActive("1");
			office.setMsTenant(tenant);
			if (null != region) {
				office.setMsRegion(region);
			}
			daoFactory.getOfficeDao().insertOfficeNewTrx(office);
		}
		
		MsDocTemplate docTemplate = daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(request.getDocumentTemplateCode(), tenant.getTenantCode());
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
				new Object[] { "Document Template Code " + request.getDocumentTemplateCode() }, audit);
		commonValidatorLogic.validateNotNull(docTemplate, messageValidation, StatusCode.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
		
		
		MsPeruriDocType peruriDocType = daoFactory.getPeruriDocTypeDao().getPeruriDocTypeByDocId(request.getPeruriDocTypeId());
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
				new Object[] { "Peruri Document Type " + request.getPeruriDocTypeId() }, audit);
		commonValidatorLogic.validateNotNull(peruriDocType, messageValidation, StatusCode.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
		
		
		MsLov docType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE, "TRX");
		MsLov signStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS, GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED);
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		List<MsDocTemplateSignLoc> sdts = daoFactory.getDocumentDao().getListSignLocationByTemplateCodeAndIdTenant(docTemplate.getDocTemplateCode(), tenant.getIdMsTenant());
		short totalMeterai = 0;
		for (MsDocTemplateSignLoc sdt : sdts) {
			if (GlobalVal.CODE_LOV_SIGN_TYPE_SDT.equals(sdt.getMsLovByLovSignType().getCode())) {
				totalMeterai++;
			}
		}
		
		if (totalMeterai == 0) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.templatemusthavesdt", null, this.retrieveLocaleAudit(audit)),
					ReasonDocument.TEMPLATE_NO_SDT);
		}
		
		TrDocumentH docH = new TrDocumentH();
		docH.setUsrCrt(audit.getCallerId());
		docH.setDtmCrt(now);
		docH.setRefNumber(request.getDocumentNumber());
		docH.setTotalDocument((short) 1);
		docH.setMsOffice(office);
		docH.setMsTenant(tenant);
		docH.setMsLov(docType);
		docH.setUrlUpload(tenant.getUploadUrl());
		docH.setIsActive("1");
		docH.setProsesMaterai((short) 320);
		docH.setIsManualUpload("1");
		docH.setIsPostpaidStampduty("1");
		daoFactory.getDocumentDao().insertDocumentHeaderNewTrx(docH);
		
		TrDocumentD docD = new TrDocumentD();
		docD.setUsrCrt(audit.getCallerId());
		docD.setDtmCrt(now);
		docD.setDocumentId(daoFactory.getDocumentDao().generateDocumentId());
		docD.setMsLovByLovSignStatus(signStatus);
		docD.setMsDocTemplate(docTemplate);
		docD.setTrDocumentH(docH);
		docD.setRequestDate(sdf.parse(request.getDocDate()));
		docD.setMsTenant(tenant);
		docD.setMsVendor(vendor);
		docD.setTotalMaterai(totalMeterai);
		docD.setDocumentName(request.getDocName());
		docD.setMsPeruriDocType(peruriDocType);
		docD.setDocumentNominal(Double.parseDouble(request.getDocNominal()));
		docD.setMsLovIdType(lovIdType);
		docD.setIdName(request.getTaxOwedsName());
		docD.setIdNo(request.getIdNo());
		docD.setTenantTransactionId(request.getDocumentTransactionId());
		docD.setTotalStamping((short) 0);
		docD.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_NOT_STR);
		daoFactory.getDocumentDao().insertDocumentDetailNewTrx(docD);
		
		short seq = 0;
		for (MsDocTemplateSignLoc sdt : sdts) {
			if (GlobalVal.CODE_LOV_SIGN_TYPE_SDT.equals(sdt.getMsLovByLovSignType().getCode())) {
				TrDocumentDStampduty docDSdt = new TrDocumentDStampduty();
				docDSdt.setUsrCrt(audit.getCallerId());
				docDSdt.setDtmCrt(now);
				docDSdt.setSignLocation(sdt.getSignLocation());
				docDSdt.setSignPage(sdt.getSignPage());
				docDSdt.setSeqNo(seq);
				docDSdt.setTransform(sdt.getTransform());
				docDSdt.setTrDocumentD(docD);
				daoFactory.getDocumentDao().insertDocumentDetailSdtNewTrx(docDSdt);
				
				seq++;
			}
		}
		
		boolean returnDocument = "1".equals(request.getReturnStampResult());
		
		// Store document to OSS
		byte[] documentByteArray = Base64.getDecoder().decode(request.getDocumentFile());
		cloudStorageLogic.storeStampingPaymentReceipt(docD, documentByteArray);
		
		return stampPaymentReceipt(docH, returnDocument, audit);
	}
	
	private void insertStampingPaymentReceiptValidation(InsertStampingPaymentReceiptRequest request, AuditContext audit) {
		String var;
		if (StringUtils.isBlank(request.getDocDate())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docdate", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		try {
			sdf.parse(request.getDocDate());
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.global.dateformat", new Object[] {request.getDocDate(), GlobalVal.DATE_FORMAT},  this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_DATE_RANGE);
		}

		if (StringUtils.isBlank(request.getDocName())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docname", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getDocNominal())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docnominal", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (!StringUtils.isNumeric(request.getDocNominal()) && !request.getDocNominal().matches("[0-9]{1,17}\\.[0-9]{2}")) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.insertstamping.invaliddocnominal", null, this.retrieveLocaleAudit(audit)), ReasonDocument.DOC_NOMINAL_INVALID);
		}
		
		if (StringUtils.isBlank(request.getDocumentFile())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.file", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getDocumentNumber())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docnumber", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getDocumentTemplateCode())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.doctemplate", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getDocumentTransactionId())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.trxid", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		this.idTypeValidation(request.getIdType(), request.getIdNo(), audit);
		
		if (StringUtils.isBlank(request.getOfficeCode())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.officeCode", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getOfficeName())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.officeName", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getPeruriDocTypeId())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.doctypeperuri", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getReturnStampResult())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.returnresult", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getTaxOwedsName())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.name", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByTenantTrxId(request.getDocumentTransactionId());
		if (null != docD) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.document.tenanttrxidexist", new Object[] {request.getDocumentTransactionId()}, this.retrieveLocaleAudit(audit))
					, ReasonDocument.TENANT_TRX_ID_EXIST);
		}
	}
	
	private void idTypeValidation(String idTypeCode, String idNo, AuditContext audit) {
		String var;
		if (StringUtils.isBlank(idNo)) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.idno", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(idTypeCode)) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.idtype", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MANDATORY_CANNOT_BE_EMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		MsLov lovIdType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_ID_TYPE, idTypeCode);
		if (null == lovIdType) {
			throw new LovException(this.messageSource.getMessage("businesslogic.insertstamping.invalididtype", new Object[] {idTypeCode}, this.retrieveLocaleAudit(audit)), Reason.CODE_INVALID);
		}
		
		if (lovIdType.getCode().equals(GlobalVal.CODE_LOV_TIPE_IDENTITAS_NIK) && idNo.length() != 16) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.user.invalidniklength", null, this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_ID);
		} else if (!(idNo.length() >= 15 && idNo.length() <= 16)){
			throw new DocumentException(this.messageSource.getMessage("businesslogic.user.invalidnpwplength", null, this.retrieveLocaleAudit(audit)), ReasonDocument.INVALID_ID);
		}
	}
	
	private void checkDuplicateRefNumber(String refNumber, MsTenant tenant, AuditContext audit) {
		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCodeNewTran(refNumber, tenant.getTenantCode());
		String newRefNumber = StringUtils.EMPTY;
		if (null != docH) {
			int x = 1;
			while (true) {
				newRefNumber = refNumber + "_" + x;
				TrDocumentH docHWithNewRefNumber = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCodeNewTran(newRefNumber, tenant.getTenantCode());		
				if (null == docHWithNewRefNumber) {
					docH.setDtmUpd(new Date());
					docH.setUsrUpd(audit.getCallerId());
					docH.setRefNumber(newRefNumber);
					docH.setResultUrl(null);
					docH.setUrlUpload(null);
					docH.setIsActive("0");
					daoFactory.getDocumentDao().updateDocumentHNewTran(docH);
					break;
				}
				x++;
			}
		}
		
		List<TrBalanceMutation> listBm = daoFactory.getBalanceMutationDao().getListBalanceMutationByRefNoAndTenantCode(refNumber, 
				tenant.getTenantCode());
		if (!listBm.isEmpty() && StringUtils.isNotBlank(newRefNumber)) {
			for (TrBalanceMutation bm : listBm) {
				bm.setDtmUpd(new Date());
				bm.setUsrUpd(audit.getCallerId());
				bm.setRefNo(newRefNumber);
			}
			daoFactory.getBalanceMutationDao().updateListBalanceMutation(listBm);
		}
		
	}

	@Override
	public void retryStampingAllPaymentReceipt(AuditContext audit) {
		Short meteraiProcess = Short.valueOf(METERAI_PROCESS_IN_QUEUE);
		List<TrDocumentH> documentHs = daoFactory.getDocumentDao().getListDocumentHeaderByProsesMeteraiNewTran(meteraiProcess);
		for (TrDocumentH documentH : documentHs) {
			retryStampPaymentReceipt(documentH, audit);
		}
	}
	
	private void retryStampPaymentReceipt(TrDocumentH documentH, AuditContext audit) {
		commonStampingLogic.updateDocumentHMeteraiProcess(documentH, METERAI_PROCESS_IN_PROGRESS, audit);
		
		long connectionTimeout = commonStampingLogic.getStampingConnectionTimeout(StampingDocumentType.PAYMENT_RECEIPT, audit);
		long readTimeout = commonStampingLogic.getStampingReadTimeout(StampingDocumentType.PAYMENT_RECEIPT, audit);
		
		StampProcessParameter param = new StampProcessParameter();
		param.setConnectionTimeout(connectionTimeout);
		param.setReadTimeout(readTimeout);
		param.setThrowMaxError(false);
		
		EmeteraiPajakkuLoginResponseBean loginResponse = loginPajakku(documentH, param, audit);
		if (!PAJAKKU_SUCCESS_CODE.equals(loginResponse.getStatusCode())) {
			return;
		}
		String peruriLoginToken = loginResponse.getToken();
		
		List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
		TrDocumentD document = documents.get(0);
		LOG.info("Kontrak {}, Dokumen {}, Total Meterai: {}, Total Stamping: {}", documentH.getRefNumber(), document.getDocumentId(), document.getTotalMaterai(), document.getTotalStamping());
		List<TrDocumentDStampduty> docSdts = daoFactory.getDocumentDao().getDocumentStampDutyByIdDocumentDNewTran(document.getIdDocumentD());
			
		if (null == document.getTotalMaterai() || 0 == document.getTotalMaterai()) {
			commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_NOT_SDT, audit);
			return;
		}
			
		int currentLoop = document.getTotalStamping();
		for (int i = currentLoop; i < document.getTotalMaterai(); i++) {
			TrDocumentDStampduty docSdt = docSdts.get(i);
			if (GlobalVal.STEP_ATTACH_METERAI_NOT_STR.equals(document.getSdtProcess())) {
				commonStampingLogic.updateDocumentDMeteraiProcess(document, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
			}
			if (GlobalVal.STEP_ATTACH_METERAI_UPL_DOC.equals(document.getSdtProcess())) {
				UploadDocPajakkuResponseBean response = uploadDocPajakku(document, docSdt, peruriLoginToken, param, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
					return;
				}
			}
			if (GlobalVal.STEP_ATTACH_METERAI_GEN_SDT.equals(document.getSdtProcess())) {
				GenerateEmeteraiPajakkuResponse response = generateEmeteraiPajakku(document, docSdt, i, peruriLoginToken, param, audit);
				if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getStatusCode())) {
					// Update tr_document_d untuk UPL DOC ulang
					String cutTransactionId = MssTool.cutLastObjectFromListString(document.getTransactionId(), DOC_TRX_ID_DELIMITER);
					String cutDocumentNameSdt = MssTool.cutLastObjectFromListString(document.getDocumentNameSdt(), DOC_TRX_ID_DELIMITER);
					document.setTransactionId(cutTransactionId);
					document.setDocumentNameSdt(cutDocumentNameSdt);
					document.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_UPL_DOC);
					document.setUsrUpd(audit.getCallerId());
					document.setDtmUpd(new Date());
					daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
					return;
				}
			}
			if (GlobalVal.STEP_ATTACH_METERAI_STM_SDT.equals(document.getSdtProcess())) {
				StampingEmeteraiPajakkuResponse response = stampEmeteraiPajakku(document, docSdt, peruriLoginToken, param, audit);
				if (!GlobalVal.PERURI_STAMPING_SUCCESS_STATUS.equalsIgnoreCase(response.getStatus())) {
					// Update tr_document_d untuk UPL DOC ulang
					String cutTransactionId = MssTool.cutLastObjectFromListString(document.getTransactionId(), DOC_TRX_ID_DELIMITER);
					String cutDocumentNameSdt = MssTool.cutLastObjectFromListString(document.getDocumentNameSdt(), DOC_TRX_ID_DELIMITER);
					document.setTransactionId(cutTransactionId);
					document.setDocumentNameSdt(cutDocumentNameSdt);
					document.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_UPL_DOC);
					document.setUsrUpd(audit.getCallerId());
					document.setDtmUpd(new Date());
					daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
					
					return;
				}	
			}
		}
		
		if (GlobalVal.STEP_ATTACH_METERAI_SDT_FIN.equals(document.getSdtProcess())) {
			commonStampingLogic.updateDocumentHMeteraiProcess(documentH, METERAI_PROCESS_SUCCESS, audit);
			
			documentH.setCallbackProcess((short) 11);
			documentH.setUsrUpd(audit.getCallerId());
			documentH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
			
			return;
		}
		
		String message = "Should not reach this process. Please check the data";
		IncrementAgreementStampingErrorCountBean bean = new IncrementAgreementStampingErrorCountBean(documentH, document, message, null, null, null);
		commonStampingLogic.incrementAgreementStampingErrorCount(bean, METERAI_PROCESS_IN_QUEUE, StampingDocumentType.PAYMENT_RECEIPT, StampingErrorLocation.FINAL_VAL, StampingErrorDetail.VALIDATION, false, audit);
	}

	@Override
	public PaymentReceiptConfinsSyncResponse synConfins(AuditContext audit) throws IOException {
		List<TrDocumentH> lDocH = daoFactory.getDocumentDao().getListDocumentHeaderWithoutUserByCallbackProcessNewTrx((short) 11);
		if (lDocH.isEmpty()) {
			LOG.info("No Data to Sync to Confins");
		} else {
			LOG.info("{} Documents to Sync to Confins", lDocH.size());
		}
		
		for (TrDocumentH docH : lDocH) {
			SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_TIME_FORMAT_SEC_WITHSLASH);
			PaymentReceiptConfinsSyncRequest request = new PaymentReceiptConfinsSyncRequest();
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocumentHeaderIdNewTran(docH.getIdDocumentH());
			List<TrDocumentDStampduty> docDsdts = daoFactory.getDocumentDao().getDocumentStampDutyByIdDocumentDNewTran(docD.getIdDocumentD());
			List<PaymentReceiptConfinsSyncBean> beans = new ArrayList<>();
			List<Map<String, Object>> sdts = daoFactory.getStampDutyDao().getIdStampDutyForDocumentNewTran(docD);
			
			for (Map<String, Object> sdtId : sdts) {
				BigInteger id = (BigInteger) sdtId.get("d0");
				TrStampDuty sdt = daoFactory.getStampDutyDao().getStampDutyById(id.longValue());
				TrDocumentDStampduty docDSdt = new TrDocumentDStampduty();
				for (TrDocumentDStampduty temp : docDsdts) {
					if (temp.getStartStampProcess().compareTo(sdt.getDtmCrt()) < 0 && temp.getStampingDate().compareTo(sdt.getDtmCrt()) > 0) {
						docDSdt = temp;
						break;
					}
				}
				
				String stampingStat = sdt.getMsLov().getCode().equals(GlobalVal.CODE_LOV_SDT_GO_LIVE) ? "Success" : "Failed";
				String peruriTrxNo = StringUtils.isBlank(sdt.getTransactionId()) ? "ONPREMISE" : sdt.getTransactionId();
				String docName = StringUtils.isBlank(sdt.getDocumentNameSdt()) ? "ONPREMISE" : sdt.getDocumentNameSdt();
				
				PaymentReceiptConfinsSyncBean bean = new PaymentReceiptConfinsSyncBean();
				bean.setDocName(docName);
				bean.setEndStampingDt(sdf.format(docDSdt.getStampingDate()));
				bean.setErrMsg(sdt.getErrorMessage());
				bean.setPayRcvDTrxId(docD.getTenantTransactionId());
				bean.setPeruriTrxNo(peruriTrxNo);
				bean.setReqDt(sdf.format(docD.getDtmCrt()));
				bean.setSnCreatedDt(sdf.format(sdt.getDtmCrt()));
				bean.setSnEMeterai(sdt.getStampDutyNo());
				bean.setStampingStat(stampingStat);
				bean.setStartStampingDt(sdf.format(docDSdt.getStartStampProcess()));
				beans.add(bean);
			}
			
			request.setRequestLogPeruri(beans);
			Status status = syncDataToConfins(request, docD.getMsTenant(), audit);
			
			if (200 == status.getCode()) {
				LOG.info("Sync to Confins for {} Successful", docH.getRefNumber());
				docH.setCallbackProcess((short) 13);
				docH.setUsrUpd(audit.getCallerId());
				docH.setDtmUpd(new Date());
				daoFactory.getDocumentDao().updateDocumentHNewTran(docH);
			}

		}
		
		return new PaymentReceiptConfinsSyncResponse();
	}
	
	private Status syncDataToConfins(PaymentReceiptConfinsSyncRequest request, MsTenant tenant, AuditContext audit) throws IOException {
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
		String url = daoFactory.getCommonDao().getGeneralSettingByTenant("USE_STAMPING_SYNC_URL", tenant).getGsValue();
		
		WebClient client = WebClient.create(url).headers(mapHeader);
		MssTool.trustAllSslCertificate(client);
		
		String jsonRequest = gson.toJson(request);
		LOG.info("Json Request Sync Data : {}", jsonRequest);
		
		Response response = client.post(jsonRequest);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String digisignResult = IOUtils.toString(isReader);
		LOG.info("JSON result Sync Data : {}", digisignResult);
		
		return gson.fromJson(digisignResult, Status.class);
	}

	@Override
	public UploadPaymentReceiptToDmsResponse uploadDms(AuditContext audit) throws IOException {
		List<TrDocumentH> lDocH = daoFactory.getDocumentDao().getListDocumentHeaderWithoutUserByCallbackProcessNewTrx((short) 13);
		if (lDocH.isEmpty()) {
			LOG.info("No Data to Upload to DMS");
		} else {
			LOG.info("{} Documents to Upload to DMS", lDocH.size());
		}
		
		for (TrDocumentH docH : lDocH) {
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocumentHeaderIdNewTran(docH.getIdDocumentH());
			String documentDate = MssTool.formatDateToStringIn(docD.getRequestDate(), "yyyy/MM/dd");
			MsTenant tenant = docD.getMsTenant();
			String dmsUsername = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, AmGlobalKey.GENERALSETTING_DMS_USERNAME).getSettingValue();
            if (StringUtils.isBlank(dmsUsername)) {
				throw new StampingException(String.format("Tenant Setting %1s tidak ditemukan untuk tenant %2s", AmGlobalKey.GENERALSETTING_DMS_USERNAME, tenant.getTenantCode()));
			}
			String uplDmsIntegrationValue = Base64.getEncoder().encodeToString(dmsUsername.getBytes());
			UploadStampedPaymenReceiptRequest request = new UploadStampedPaymenReceiptRequest();	
			String docHSplit = "";
			if(null!=docH.getRefNumber()) {
				if(docH.getRefNumber().contains("-")) {
					docHSplit = docH.getRefNumber().split("-")[0];
					request.setAgreementNo(docHSplit);
				}
				else if(!docH.getRefNumber().contains("-")){
					request.setAgreementNo(docH.getRefNumber());
				}
			}
			request.setUsername(uplDmsIntegrationValue);
			request.setContent("[String Base64]");
			request.setDokumenDate(documentDate);
			request.setDokumenPeruri(docD.getMsPeruriDocType().getDocName());
			request.setFilename(docD.getDocumentName() + ".pdf");
			request.setPaymentHistoryId(docD.getTenantTransactionId());
			String json = gson.toJson(request);
			LOG.info("Kontrak {}, Dokumen {}, Request Upload Payment Receipt to DMS : {}", docH.getRefNumber(), docD.getDocumentId(), json);
			
			request.setContent(getDocumentFileToUpload(docD, audit));
			
			String url = docH.getUrlUpload();
			if (StringUtils.isEmpty(url)) {
				throw new EmeteraiException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
						new String[] {"Upload URL"}, audit), ReasonEmeterai.UPLOAD_DOC_EXCEPTION);
			}
			
			OkHttpClient okHClient = MssTool.getUnsafeOkHttpClient();
			
			// Prepare header
			Map<String, String> header = new HashMap<>();
			header.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			
			header.put("Integration", uplDmsIntegrationValue);
			Headers headers = Headers.of(header);
			LOG.info("Kontrak {}, Dokumen {}, Upload Payment Receipt to DMS request header: {}", docH.getRefNumber(), docD.getDocumentId(), header);
			LOG.info("Kontrak {}, Dokumen {}, Upload Payment Receipt to DMS with URL: {}", docH.getRefNumber(), docD.getDocumentId(), url);
			
			String jsonBody = gson.toJson(request);
			RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_JSON), jsonBody);
			
			Request okHRequest = new Request.Builder()
					.headers(headers)
					.url(url)
					.post(body).build();
			
			okhttp3.Response okHResponse = okHClient.newCall(okHRequest).execute();
			String result = okHResponse.body().string();
			LOG.info("Kontrak {}, Dokumen {}, Upload document to DMS response: {}", docH.getRefNumber(), docD.getDocumentId(), result);
			
			UploadStampedDocumentResponse uploadResponse = gson.fromJson(result, UploadStampedDocumentResponse.class);
			
			if ("200".equals(uploadResponse.getStatusCode())) {
				LOG.info("Upload to DMS for {} Successful", docH.getRefNumber());
				docH.setCallbackProcess((short) 14);
				docH.setUsrUpd(audit.getCallerId());
				docH.setDtmUpd(new Date());
				daoFactory.getDocumentDao().updateDocumentHNewTran(docH);
			}
		}
		
		return new UploadPaymentReceiptToDmsResponse();
	}

	@Override
	public void deleteStampingBaseFromOss(AuditContext audit) {
		List<TrDocumentH> lDocH = daoFactory.getDocumentDao().getListDocumentHeaderWithoutUserByCallbackProcessNewTrx((short) 14);
		if (lDocH.isEmpty()) {
			LOG.info("No Data to Delete from stamping_document/base/ OSS");
		} else {
			LOG.info("{} Documents to Delete from stamping_document/base/ OSS", lDocH.size());
		}
		
		for (TrDocumentH docH : lDocH) {
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocumentHeaderIdNewTran(docH.getIdDocumentH());
			cloudStorageLogic.deleteStampingPaymentReceipt(docD);
			
			docH.setCallbackProcess((short) 901);
			docH.setUsrUpd(audit.getCallerId());
			docH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHNewTran(docH);
		}
	}
	
}
