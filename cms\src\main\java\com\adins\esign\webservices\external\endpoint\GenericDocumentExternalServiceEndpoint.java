package com.adins.esign.webservices.external.endpoint;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.SendDocumentLogic;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.webservices.external.api.DocumentExternalService;
import com.adins.esign.webservices.model.CheckStampingStatusRequest;
import com.adins.esign.webservices.model.CheckStampingStatusResponse;
import com.adins.esign.webservices.model.CheckStatusSigningRequest;
import com.adins.esign.webservices.model.CheckStatusSigningResponse;
import com.adins.esign.webservices.model.DownloadDocumentFullApiRequest;
import com.adins.esign.webservices.model.GetTotalUnsignedDocumentRequest;
import com.adins.esign.webservices.model.GetTotalUnsignedDocumentResponse;
import com.adins.esign.webservices.model.RequestStampingRequest;
import com.adins.esign.webservices.model.RequestStampingResponse;
import com.adins.esign.webservices.model.SendDocFullApiRequest;
import com.adins.esign.webservices.model.SendDocFullApiResponse;
import com.adins.esign.webservices.model.SignDocumentFullApiRequest;
import com.adins.esign.webservices.model.SignDocumentFullApiResponse;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptResponse;
import com.adins.esign.webservices.model.external.CancelDocumentExternalRequest;
import com.adins.esign.webservices.model.external.GetSignLinkExternalRequest;
import com.adins.esign.webservices.model.external.GetSignLinkExternalResponse;
import com.adins.esign.webservices.model.external.GetTemplateSignLocationRequest;
import com.adins.esign.webservices.model.external.GetTemplateSignLocationResponse;
import com.adins.esign.webservices.model.external.InsertStampingMateraiExternalRequest;
import com.adins.esign.webservices.model.external.SigningHashFileRequest;
import com.adins.esign.webservices.model.external.SigningHashFileResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/external/document")
@Api(value = "DocumentExternalService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericDocumentExternalServiceEndpoint implements DocumentExternalService {

	@Autowired private DocumentLogic documentLogic;
	@Autowired private SendDocumentLogic sendDocumentLogic;
	
	@Override
	@POST
	@Path("/sendDocumentSigning")
	public SendDocFullApiResponse sendDocument(SendDocFullApiRequest request) throws Exception {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return sendDocumentLogic.sendDocFullApi(request, xApiKey, audit);
	}
	
	@Override
	@POST
	@Path("/signDocument")
	public SignDocumentFullApiResponse signDocumentFullApi(SignDocumentFullApiRequest signDocRequest)
			throws IOException, ParseException {
		AuditContext audit = signDocRequest.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return documentLogic.signDocumentFullApi(signDocRequest, xApiKey, audit);
	}
	
	@Override
	@POST
	@Path("/downloadDocument")
	public ViewDocumentResponse downloadDocumentFullApi(DownloadDocumentFullApiRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return documentLogic.downloadDocumentFullApi(request, xApiKey, audit);
	}

	@Override
	@POST
	@Path("/checkStampingStatus")
	public CheckStampingStatusResponse checkStampingStatus(CheckStampingStatusRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);

		
		return documentLogic.checkStampingStatus(request,apiKey, audit);
	}


	@Override
	@POST
	@Path("/requestStamping")
	public RequestStampingResponse requestStamping(RequestStampingRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return documentLogic.requestStamping(request, xApiKey, audit);
	}


	@Override
	@POST
	@Path("/checkStatusSigning")
	public CheckStatusSigningResponse checkStatusSigning(CheckStatusSigningRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return documentLogic.checkStatusSigning(request, xApiKey, audit);
	}

	@Override
	@POST
	@Path("/getTotalUnsignedDocuments")
	public GetTotalUnsignedDocumentResponse getTotalUnsignedDocuments(GetTotalUnsignedDocumentRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return documentLogic.getTotalUnsignedDoc(request, apiKey, audit);
	}
	
	@Override
	@POST
	@Path("/getSignLink")
	public GetSignLinkExternalResponse getSignLink(GetSignLinkExternalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return documentLogic.getSignLinkExternal(request, apiKey, audit);
	}

	@Override
	@POST
	@Path("/getTemplateSignLocation")
	public GetTemplateSignLocationResponse getTemplateSignLocation(GetTemplateSignLocationRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return documentLogic.getTemplateSignLocation(request, apiKey, audit);
	}

	@Override
	@POST
	@Path("/signingHashFile")
	public SigningHashFileResponse signingHashFile(SigningHashFileRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return documentLogic.signingHashFile(request, xApiKey, audit);
	}

	@Override
	@POST
	@Path("/insertStampingMaterai")
	public InsertStampingPaymentReceiptResponse insertStampingMaterai(InsertStampingMateraiExternalRequest request) throws ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return documentLogic.insertStampMateraiExternal(request, xApiKey, audit);
	}

	@Override
	@POST
	@Path("/cancelDocument")
	public MssResponseType cancelDocument(CancelDocumentExternalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return documentLogic.cancelAgreementExternal(request, xApiKey, audit);
	}
}
