package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.TenantSettingsDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.custom.TenantSettingsBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.ListTenantSettingsRequest;

@Transactional
@Component
public class TenantSettingsDaoHbn extends BaseDaoHbn implements TenantSettingsDao {

	@Override
	public MsTenantSettings getTenantSettings(MsTenant tenant, String lovTenantSettingCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("tenant", tenant);
		params.put(MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_TENANT_SETTING_TYPE);
		params.put(MsLov.CODE_HBM, StringUtils.upperCase(lovTenantSettingCode));
		
		return managerDAO.selectOne(
				"from MsTenantSettings ts "
				+ "join fetch ts.msTenant t "
				+ "join fetch ts.lovSettingType st "
				+ "where ts.msTenant = :tenant "
				+ "and st.lovGroup = :lovGroup "
				+ "and st.code = :code ", params);
	}

	@Override
	public List<TenantSettingsBean> getListTenantSettings(ListTenantSettingsRequest request, long idMsTenant, int min, int max) {
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		params.put("idMsTenant", idMsTenant);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select * from ( ")
			.append("with settings as ( ")
			.append(" select * from ms_tenant_settings where id_ms_tenant = :idMsTenant ")
			.append(") ")
			.append("select row_number() over(order by l.id_lov asc) as \"no\", description as \"settingType\", settings.setting_value as \"value\", code as \"settingTypeCode\" from ms_lov l ")
			.append("left join settings on settings.lov_setting_type = l.id_lov ")
			.append("where lov_group = 'TENANT_SETTING_TYPE' ");
		
		if (StringUtils.equalsIgnoreCase("Yes", request.getConfigured())) {
			query.append("and setting_value is not null ");
		} else if (StringUtils.equalsIgnoreCase("No", request.getConfigured())) {
			query.append("and setting_value is null ");
		}
		
		if (StringUtils.isNotBlank(request.getSettingType())) {
			query.append("and code = :settingType ");
			params.put("settingType", request.getSettingType());
		}
		
		query.append(") as a where no between :min and :max ");
		
		return managerDAO.selectForListString(TenantSettingsBean.class, query.toString(), params, null);
	}

	@Override
	public long countListTenantSettings(ListTenantSettingsRequest request, long idMsTenant) {
		Map<String, Object> params = new HashMap<>();
		params.put("idMsTenant", idMsTenant);
		
		StringBuilder query = new StringBuilder();
		query
			.append("with settings as ( ")
			.append("select * from ms_tenant_settings where id_ms_tenant = :idMsTenant ")
			.append(") ")
			.append("select count(1) from ms_lov l ")
			.append("left join settings on settings.lov_setting_type = l.id_lov ")
			.append("where lov_group = 'TENANT_SETTING_TYPE' ");
		
		if (StringUtils.equalsIgnoreCase("Yes", request.getConfigured())) {
			query.append("and setting_value is not null ");
		} else if (StringUtils.equalsIgnoreCase("No", request.getConfigured())) {
			query.append("and setting_value is null ");
		}
		
		if (StringUtils.isNotBlank(request.getSettingType())) {
			query.append("and code = :settingType ");
			params.put("settingType", request.getSettingType());
		}
		
		BigInteger result = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == result) {
			return 0;
		}
		return result.longValue();
	}

	@Override
	public void inserMsTenantSettings(MsTenantSettings tenantSettings) {
		tenantSettings.setUsrCrt(MssTool.maskData(tenantSettings.getUsrCrt()));
		this.managerDAO.insert(tenantSettings);
	}

	@Override
	public void updateMsTenantSettings(MsTenantSettings tenantSettings) {
		tenantSettings.setUsrUpd(MssTool.maskData(tenantSettings.getUsrUpd()));
		this.managerDAO.update(tenantSettings);
	}

}
