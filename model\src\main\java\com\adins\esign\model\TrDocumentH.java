package com.adins.esign.model;
// Generated 09-Sep-2021 22:49:32 by Hibernate Tools 5.2.12.Final

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;

/**
 * TrDocumentH generated by hbm2java
 */
@Entity
@Table(name = "tr_document_h")
public class TrDocumentH extends ActiveAndUpdateableEntity implements java.io.Serializable {
	private static final long serialVersionUID = 1L;
	
	public static final String ID_DOCUMENT_H_HBM = "idDocumentH";
	public static final String REF_NUMBER_HBM = "refNumber";
	public static final String CALLBACK_PROCESS_HBM = "callbackProcess";
	
	private long idDocumentH;
	private AmMsuser amMsuserByIdMsuserCustomer;
	private AmMsuser amMsuserByIdMsuserRequestBy;
	private MsLov msLov;
	private MsOffice msOffice;
	private MsTenant msTenant;
	private String refNumber;
	private Short totalDocument;
	private Short totalSigned;
	private String resultUrl;
	private String urlUpload;
	private MsBusinessLine msBusinessLine;
	private Short prosesMaterai;
	private Short emailSaldo;
	private Short callbackProcess;
	private String isManualUpload;
	private String isPostpaidStampduty;
	private String isStandardUploadUrl;
	private String automaticStampingAfterSign;
	private String signingProcess;
	private Short retryResumeAttemptNum;
	private Short notificationAttemptNum;
	private Date notificationAttemptDate;

	private Set<TrBalanceMutation> trBalanceMutations = new HashSet<>(0);
	private Set<TrDocumentD> trDocumentDs = new HashSet<>(0);
	private Set<TrFaceVerify> trFaceVerifies = new HashSet<>(0);
	private Set<TrDocumentHStampdutyError> trDocumentHStampdutyErrors = new HashSet<>(0);
	private Set<TrDocumentSigningRequest> trDocumentSigningRequests = new HashSet<>(0);

	public TrDocumentH() {
	}

	public TrDocumentH(long idDocumentH, MsLov msLov, MsOffice msOffice, MsTenant msTenant, String usrCrt, Date dtmCrt,
			String refNumber) {
		this.idDocumentH = idDocumentH;
		this.msLov = msLov;
		this.msOffice = msOffice;
		this.msTenant = msTenant;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.refNumber = refNumber;
	}

	public TrDocumentH(long idDocumentH, AmMsuser amMsuserByIdMsuserCustomer, AmMsuser amMsuserByIdMsuserRequestBy,
			MsLov msLov, MsOffice msOffice, MsTenant msTenant, String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd,
			String refNumber, Short totalDocument, Short totalSigned, String resultUrl, String urlUpload, MsBusinessLine msBusinessLine,
			Short prosesMaterai, Short emailSaldo, Short callbackProcess,
			Set<TrBalanceMutation> trBalanceMutations, Set<TrDocumentD> trDocumentDs, Set<TrFaceVerify> trFaceVerifies) {
		this.idDocumentH = idDocumentH;
		this.amMsuserByIdMsuserCustomer = amMsuserByIdMsuserCustomer;
		this.amMsuserByIdMsuserRequestBy = amMsuserByIdMsuserRequestBy;
		this.msLov = msLov;
		this.msOffice = msOffice;
		this.msTenant = msTenant;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
		this.refNumber = refNumber;
		this.totalDocument = totalDocument;
		this.totalSigned = totalSigned;
		this.resultUrl = resultUrl;
		this.urlUpload = urlUpload;
		this.msBusinessLine = msBusinessLine;
		this.trBalanceMutations = trBalanceMutations;
		this.trDocumentDs = trDocumentDs;
		this.trFaceVerifies = trFaceVerifies;
		this.prosesMaterai = prosesMaterai;
		this.emailSaldo = emailSaldo;
		this.callbackProcess = callbackProcess;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_document_h", unique = true, nullable = false)
	public long getIdDocumentH() {
		return this.idDocumentH;
	}

	public void setIdDocumentH(long idDocumentH) {
		this.idDocumentH = idDocumentH;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_msuser_customer")
	public AmMsuser getAmMsuserByIdMsuserCustomer() {
		return this.amMsuserByIdMsuserCustomer;
	}

	public void setAmMsuserByIdMsuserCustomer(AmMsuser amMsuserByIdMsuserCustomer) {
		this.amMsuserByIdMsuserCustomer = amMsuserByIdMsuserCustomer;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_msuser_request_by")
	public AmMsuser getAmMsuserByIdMsuserRequestBy() {
		return this.amMsuserByIdMsuserRequestBy;
	}

	public void setAmMsuserByIdMsuserRequestBy(AmMsuser amMsuserByIdMsuserRequestBy) {
		this.amMsuserByIdMsuserRequestBy = amMsuserByIdMsuserRequestBy;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_doc_type", nullable = false)
	public MsLov getMsLov() {
		return this.msLov;
	}

	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_office")
	public MsOffice getMsOffice() {
		return this.msOffice;
	}

	public void setMsOffice(MsOffice msOffice) {
		this.msOffice = msOffice;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@Column(name = "ref_number", nullable = false, length = 53)
	public String getRefNumber() {
		return this.refNumber;
	}

	public void setRefNumber(String refNumber) {
		this.refNumber = refNumber;
	}

	@Column(name = "total_document")
	public Short getTotalDocument() {
		return this.totalDocument;
	}

	public void setTotalDocument(Short totalDocument) {
		this.totalDocument = totalDocument;
	}

	@Column(name = "total_signed")
	public Short getTotalSigned() {
		return this.totalSigned;
	}

	public void setTotalSigned(Short totalSigned) {
		this.totalSigned = totalSigned;
	}

	@Column(name = "result_url", length = 200)
	public String getResultUrl() {
		return this.resultUrl;
	}

	public void setResultUrl(String resultUrl) {
		this.resultUrl = resultUrl;
	}

	@Column(name = "url_upload", length = 100)
	public String getUrlUpload() {
		return this.urlUpload;
	}

	public void setUrlUpload(String urlUpload) {
		this.urlUpload = urlUpload;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_business_line")
	public MsBusinessLine getMsBusinessLine() {
		return this.msBusinessLine;
	}

	public void setMsBusinessLine(MsBusinessLine msBusinessLine) {
		this.msBusinessLine = msBusinessLine;
	}
	
	@Column(name = "proses_materai")
	public Short getProsesMaterai() {
		return this.prosesMaterai;
	}

	public void setProsesMaterai(Short prosesMaterai) {
		this.prosesMaterai = prosesMaterai;
	}

	@Column(name = "email_saldo")
	public Short getEmailSaldo() {
		return this.emailSaldo;
	}

	public void setEmailSaldo(Short emailSaldo) {
		this.emailSaldo = emailSaldo;
	}
	
	@Column(name = "callback_process")
	public Short getCallbackProcess() {
		return callbackProcess;
	}

	public void setCallbackProcess(Short callbackProcess) {
		this.callbackProcess = callbackProcess;
	}
	
	@Column(name = "is_manual_upload", length = 1)
	public String getIsManualUpload() {
		return isManualUpload;
	}

	public void setIsManualUpload(String isManualUpload) {
		this.isManualUpload = isManualUpload;
	}

	@Column(name = "is_postpaid_stampduty", length = 1)
	public String getIsPostpaidStampduty() {
		return isPostpaidStampduty;
	}

	public void setIsPostpaidStampduty(String isPostpaidStampduty) {
		this.isPostpaidStampduty = isPostpaidStampduty;
	}

	@Column(name = "is_standard_upload_url", length = 1)
	public String getIsStandardUploadUrl() {
		return isStandardUploadUrl;
	}

	public void setIsStandardUploadUrl(String isStandardUploadUrl) {
		this.isStandardUploadUrl = isStandardUploadUrl;
	}

	@Column(name = "automatic_stamping_after_sign", length = 1)
	public String getAutomaticStampingAfterSign() {
		return automaticStampingAfterSign;
	}

	public void setAutomaticStampingAfterSign(String automaticStampingAfterSign) {
		this.automaticStampingAfterSign = automaticStampingAfterSign;
	}
	
	@Column(name = "signing_process", length = 1)
	public String getSigningProcess() {
		return signingProcess;
	}

	public void setSigningProcess(String signingProcess) {
		this.signingProcess = signingProcess;
	}

	@Column(name = "retry_resume_attempt_num")
	public Short getRetryResumeAttemptNum() {
		return retryResumeAttemptNum;
	}

	public void setRetryResumeAttemptNum(Short retryResumeAttemptNum) {
		this.retryResumeAttemptNum = retryResumeAttemptNum;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentH")
	public Set<TrBalanceMutation> getTrBalanceMutations() {
		return this.trBalanceMutations;
	}

	public void setTrBalanceMutations(Set<TrBalanceMutation> trBalanceMutations) {
		this.trBalanceMutations = trBalanceMutations;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentH")
	public Set<TrDocumentD> getTrDocumentDs() {
		return this.trDocumentDs;
	}

	public void setTrDocumentDs(Set<TrDocumentD> trDocumentDs) {
		this.trDocumentDs = trDocumentDs;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentH")
	public Set<TrFaceVerify> getTrFaceVerifies() {
		return this.trFaceVerifies;
	}

	public void setTrFaceVerifies(Set<TrFaceVerify> trFaceVerifies) {
		this.trFaceVerifies = trFaceVerifies;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentH")
	public Set<TrDocumentHStampdutyError> getTrDocumentHStampdutyErrors() {
		return trDocumentHStampdutyErrors;
	}

	public void setTrDocumentHStampdutyErrors(Set<TrDocumentHStampdutyError> trDocumentHStampdutyErrors) {
		this.trDocumentHStampdutyErrors = trDocumentHStampdutyErrors;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trDocumentH")
	public Set<TrDocumentSigningRequest> getTrDocumentSigningRequests() {
		return trDocumentSigningRequests;
	}

	public void setTrDocumentSigningRequests(Set<TrDocumentSigningRequest> trDocumentSigningRequests) {
		this.trDocumentSigningRequests = trDocumentSigningRequests;
	}

	@Column(name = "notification_attempt_num")
	public Short getNotificationAttemptNum() {
		return this.notificationAttemptNum;
	}

	public void setNotificationAttemptNum(Short notificationAttemptNum) {
		this.notificationAttemptNum = notificationAttemptNum;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "notification_attempt_date")
	public Date getNotificationAttemptDate() {
		return this.notificationAttemptDate;
	}

	public void setNotificationAttemptDate(Date notificationAttemptDate) {
		this.notificationAttemptDate = notificationAttemptDate;
	}

}
