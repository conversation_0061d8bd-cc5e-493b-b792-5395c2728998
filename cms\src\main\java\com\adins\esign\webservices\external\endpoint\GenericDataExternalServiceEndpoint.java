package com.adins.esign.webservices.external.endpoint;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.DistrictLogic;
import com.adins.esign.businesslogic.api.ProvinceLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SubDistrictLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.webservices.external.api.DataExternalService;
import com.adins.esign.webservices.model.GetDocumentEMateraiTypeResponse;
import com.adins.esign.webservices.model.GetDocumentTypeRequest;
import com.adins.esign.webservices.model.GetDocumentTypeResponse;
import com.adins.esign.webservices.model.GetListBalanceTypeExternalRequest;
import com.adins.esign.webservices.model.GetListBalanceTypeExternalResponse;
import com.adins.esign.webservices.model.GetListPsrePriorityExternalResponse;
import com.adins.esign.webservices.model.GetPeruriDocumentTypeRequest;
import com.adins.esign.webservices.model.external.GetDistrictListExternalRequest;
import com.adins.esign.webservices.model.external.GetDistrictListExternalResponse;
import com.adins.esign.webservices.model.external.GetProvinceListExternalRequest;
import com.adins.esign.webservices.model.external.GetProvinceListExternalResponse;
import com.adins.esign.webservices.model.external.GetSubDistrictListExternalRequest;
import com.adins.esign.webservices.model.external.GetSubDistrictListExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/external/data")
@Api(value = "DataExternalService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericDataExternalServiceEndpoint implements DataExternalService{
	
	@Autowired private ProvinceLogic provinceLogic;
	@Autowired private DistrictLogic districtLogic;
	@Autowired private SubDistrictLogic subDistrictLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private CommonLogic commonLogic;
	@Override
	@POST
	@Path("/getProvinceList")
	public GetProvinceListExternalResponse getProvinceListExternal(GetProvinceListExternalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(GlobalVal.CONST_X_API_KEY);
		return provinceLogic.getProvinceListExternal(request, xApiKey, audit);
	}

	@Override
	@POST
	@Path("/getDistrictList")
	public GetDistrictListExternalResponse getDistrictListExternal(GetDistrictListExternalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(GlobalVal.CONST_X_API_KEY);
		return districtLogic.getDistrictListExternal(request, xApiKey, audit);
	}

	@Override
	@POST
	@Path("/getSubDistrictList")
	public GetSubDistrictListExternalResponse getSubDistrictListExternal(GetSubDistrictListExternalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(GlobalVal.CONST_X_API_KEY);
		return subDistrictLogic.getSubDistrictListExternal(request, xApiKey, audit);
	}
	
	@Override
	@POST
	@Path("/getListBalanceType")
	public GetListBalanceTypeExternalResponse getListbalance(GetListBalanceTypeExternalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return saldoLogic.getListBalanceTypeExternal(request, apiKey, audit);
	}
	
	@Override
	@POST
	@Path("/getDocumentType")
	public GetDocumentTypeResponse getListDocumentType(GetDocumentTypeRequest request) {
		AuditContext audit = new AuditContext (GlobalVal.CONST_API_KEY);
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return commonLogic.getListDocumentTypeWithApiKey(apiKey, audit);
	}
	
	@Override
	@POST
	@Path("/getListPeruriDocumentType")
	public GetDocumentEMateraiTypeResponse getListPeruriDocumentTypePublic(GetPeruriDocumentTypeRequest request) {
		AuditContext audit = new AuditContext (GlobalVal.CONST_API_KEY);
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return commonLogic.getListPeruriDocTypeWithApiKey(apiKey, audit);
	}
	
	@Override
	@POST
	@Path("/getListPsrePriority")
	public GetListPsrePriorityExternalResponse getListPsrePriority(GetListPsrePriorityExternalResponse request) {
		AuditContext audit = new AuditContext (GlobalVal.CONST_API_KEY);
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader(HttpHeaders.X_API_KEY);
		return commonLogic.getListPsrePriority(apiKey, audit);
	}

}
