package com.adins.esign.webservices.external.endpoint;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.webservices.external.api.VendorExternalService;
import com.adins.esign.webservices.model.external.UpdatePsrePriorityExternalRequest;
import com.adins.esign.webservices.model.external.UpdatePsrePriorityExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/external/vendor")
@Api(value = "VendorExternalService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericVendorExternalServiceEndpoint implements VendorExternalService{

	@Autowired VendorLogic vendorLogic;
		
	@Override
	@POST
	@Path("/updateVendorPSrE")
	public UpdatePsrePriorityExternalResponse updateVendorPSreExternal(UpdatePsrePriorityExternalRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xApiKey = httpRequest.getHeader(GlobalVal.CONST_X_API_KEY);
		return vendorLogic.updatePSrEPriorityExternal(request, xApiKey, audit);
	}
}
