<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd        
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">	
	
	<bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
    	<property name="locations" value="classpath:application.properties"/>
	</bean>
    
    <bean id="gson" class="com.google.gson.Gson" />
	
	<bean class="com.adins.esign.businesslogic.impl.WassengerSmsLogic" />    	
	
</beans>