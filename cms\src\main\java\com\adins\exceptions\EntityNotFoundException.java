package com.adins.exceptions;

import java.util.Collections;

import com.adins.framework.exception.AdInsException;

public class EntityNotFoundException extends AdInsException {
	private static final long serialVersionUID = 1L;
	private static final String MISSING_ENTITY = "MISSING_ENTITY";	

	public EntityNotFoundException(String message, Throwable cause, String entityId) {
		super(message, cause, Collections.singletonMap(MISSING_ENTITY, entityId));
	}

	public EntityNotFoundException(String message, String entityId) {
		super(message, Collections.singletonMap(MISSING_ENTITY, entityId));
	}

	public EntityNotFoundException(Throwable cause, String entityId) {
		super(cause, Collections.singletonMap(MISSING_ENTITY, entityId));
	}

	@Override
	public int getErrorCode() {
		return StatusCode.ENTITY_NOT_FOUND;
	}

}
