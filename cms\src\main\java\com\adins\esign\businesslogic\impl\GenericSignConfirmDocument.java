package com.adins.esign.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.SignConfirmationLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDSign;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrDocumentSigningRequest;
import com.adins.esign.model.TrDocumentSigningRequestDetail;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.DocumentValidatorLogic;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.SignConfirmDocumentRequest;
import com.adins.esign.webservices.model.SignConfirmDocumentResponse;
import com.adins.esign.webservices.model.embed.ConfirmSignEmbedRequest;
import com.adins.esign.webservices.model.embed.ConfirmSignEmbedResponse;
import com.adins.exceptions.SignConfirmationDocumentException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.SignConfirmationDocumentException.ReasonSignConfirmationDokumen;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

import org.apache.commons.collections.CollectionUtils;


@Transactional
@Component
public class GenericSignConfirmDocument extends BaseLogic implements SignConfirmationLogic {
	
	@Autowired private CommonLogic commonLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private EmbedValidatorLogic embedValidatorLogic;
	@Autowired private DocumentValidatorLogic documentValidatorLogic;
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	
	@Override
	public SignConfirmDocumentResponse signConfirmationDocument(SignConfirmDocumentRequest request, AuditContext audit) {
		
		if (StringUtils.isBlank(request.getIpAddress())) {
			request.setIpAddress(MssTool.getApplicationIpAddress());
		}
		
		if (StringUtils.isEmpty(request.getIpAddress())) {
			throw new SignConfirmationDocumentException(getMessage("businesslogic.document.ipaddressempty", null, audit),
					ReasonSignConfirmationDokumen.EMPTY_IP_ADDRESS);
		} else if (StringUtils.isEmpty(request.getEmail())) {
			throw new SignConfirmationDocumentException(getMessage("businesslogic.user.signeremailempty",null,audit)
					, ReasonSignConfirmationDokumen.EMPTY_EMAIL);
		} else if (StringUtils.isEmpty(request.getBrowser())) {
			throw new SignConfirmationDocumentException(getMessage("businesslogic.document.browserempty",null,audit)
					, ReasonSignConfirmationDokumen.EMPTY_BROWSER);
		} else if (CollectionUtils.isEmpty(request.getDocumentId())) {
			throw new SignConfirmationDocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID,null,audit)
					, ReasonSignConfirmationDokumen.EMPTY_DOCUMENT_ID);
		}
		
		TrDocumentD docD = null;

		List<TrDocumentSigningRequest> trCekRequest = null;
		List<TrDocumentD> listDocD =  new ArrayList<>();
		List<String> listdocId = request.getDocumentId();
		for (int i = 0; i < listdocId.stream().count(); i++ ) {
			docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(i));
			if (null == docD )	{
				
				throw new SignConfirmationDocumentException(getMessage("businesslogic.document.documentnotfound",new Object[] {listdocId.get(i)},audit)
						, ReasonSignConfirmationDokumen.DOCUMENT_ID_INVALID);
			}
			
			AmMsuser checkuser = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(),false, audit);
			if (null == checkuser )	{
				throw new SignConfirmationDocumentException(getMessage(GlobalKey.MESSAGE_ERROR_USER_INVALID_EMAIL,new Object[] {request.getEmail(),listdocId.get(i)},audit)
						, ReasonSignConfirmationDokumen.INVALID_EMAIL);
			}
			
			MsVendorRegisteredUser msVendorRegistered = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(checkuser.getIdMsUser(), docD.getMsVendor().getVendorCode());
			if (null == msVendorRegistered ) {
				throw new SignConfirmationDocumentException(getMessage(GlobalKey.MESSAGE_ERROR_USER_INVALID_EMAIL,new Object[] {request.getEmail(),listdocId.get(i)},audit)
						, ReasonSignConfirmationDokumen.INVALID_USER_VENDOR);
			}
			
			
			List<TrDocumentDSign> docSign = daoFactory.getDocumentDao().getDocumentDSignByIdDocumentDAndIdUser(docD.getIdDocumentD(), checkuser.getIdMsUser());
			
			if ( CollectionUtils.isEmpty(docSign)) {
				throw new SignConfirmationDocumentException(getMessage("businesslogic.document.signerinvalid",new Object[] {request.getEmail(),listdocId.get(i)},audit)
						, ReasonSignConfirmationDokumen.INVALID_USER_SIGNER);
			}
			
			if(!GlobalVal.VENDOR_CODE_VIDA.equals(docD.getMsVendor().getVendorCode()) && !GlobalVal.VENDOR_CODE_PRIVY_ID.equals(docD.getMsVendor().getVendorCode())) {
				throw new SignConfirmationDocumentException(getMessage("businesslogic.document.invalidvendorsign",new Object[] {GlobalVal.VENDOR_CODE_VIDA+" / "+GlobalVal.VENDOR_CODE_PRIVY_ID},audit)
						, ReasonSignConfirmationDokumen.INVALID_VENDOR);
			}
			
			trCekRequest = daoFactory.getDocumentSigningRequestDao().getDocumentSigningRequestByDocIdandUserId(listdocId.get(i),msVendorRegistered.getAmMsuser().getIdMsUser());
			
			if ( null != trCekRequest && CollectionUtils.isNotEmpty(trCekRequest)) {
				if (3 == trCekRequest.get(0).getRequestStatus()) {
					throw new SignConfirmationDocumentException(getMessage("businesslogic.document.requestsignconfirmationdone",new Object[] {listdocId.get(i)},audit)
							, ReasonSignConfirmationDokumen.INVALID_REQUEST_DUPLICATE);
				}
				
				if (2 != trCekRequest.get(0).getRequestStatus()) {
					throw new SignConfirmationDocumentException(getMessage("businesslogic.document.requestsignconfirmationduplicate",new Object[] {listdocId.get(i)},audit)
							, ReasonSignConfirmationDokumen.INVALID_REQUEST_DUPLICATE);
				} 
			}
			
			documentValidatorLogic.validateSignSequence(docD, checkuser, audit);
			listDocD.add(docD);
			
		}
		
		AmMsuser user = null;
		
		user = userValidatorLogic.validateGetUserByEmailv2(request.getEmail(),false, audit);
		documentValidatorLogic.validateDocumentsPrioritySequence(listDocD, user, audit);
		if (null == user ) 	{
			throw new SignConfirmationDocumentException(getMessage(GlobalKey.MESSAGE_ERROR_USER_INVALID_EMAIL,new Object[] {request.getEmail()},audit)
					, ReasonSignConfirmationDokumen.DOCUMENT_ID_INVALID);
		}
		
		TrDocumentD documentD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(0));
		
		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), documentD.getMsVendor().getVendorCode());
		
		if (userValidatorLogic.isCertifExpiredForSign(msVendorRegisteredUser, audit)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_CERTIFICATE_ACTIVE_STATUS_EXPIRED, new Object[] { documentD.getMsVendor().getVendorCode() }, audit), ReasonUser.USER_CERTIFICATE_EXPIRED);
		}
		
		SignConfirmDocumentResponse response = new SignConfirmDocumentResponse();
		
		if (!GlobalVal.VENDOR_CODE_PRIVY_ID.equals(docD.getMsVendor().getVendorCode())) {
			insertDocSignRequest(listdocId, user, request, audit);
		} else {
			insertDocSignRequestWithDetail(listdocId, user, request, audit);
		}
		
		Status status = new Status();
		status.setCode(0);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}
	
	private void insertDocSignRequest(List<String> listdocId, AmMsuser user, SignConfirmDocumentRequest request, AuditContext audit) {
		for (int i = 0; i < listdocId.stream().count(); i++ ) {
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(i));
			TrDocumentH docH = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(i)).getTrDocumentH();
			TrDocumentSigningRequest docSignRequest = new TrDocumentSigningRequest();
			docSignRequest.setAmMsuser(user);
			docSignRequest.setUsrCrt(audit.getCallerId());
			docSignRequest.setDtmCrt(new Date());
			docSignRequest.setTrDocumentD(docD);
			docSignRequest.setTrDocumentH(docH);
			docSignRequest.setUserRequestIp(request.getIpAddress());
			docSignRequest.setUserRequestBrowserInformation(request.getBrowser());
			docSignRequest.setRequestStatus((short) 0);
			docSignRequest.setUserSigningConsentTimestamp(new Date());
			docSignRequest.setMsVendor(docD.getMsVendor());
			daoFactory.getDocumentSigningRequestDao().insertDocumentSigningRequest(docSignRequest);
		}
	}
	
	private void insertDocSignRequestWithDetail(List<String> listdocId, AmMsuser user, SignConfirmDocumentRequest request, AuditContext audit) {
		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(0)).getTrDocumentH();
		MsVendor vendor = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(0)).getMsVendor();
		TrDocumentSigningRequest docSignRequest = new TrDocumentSigningRequest();
		docSignRequest.setAmMsuser(user);
		docSignRequest.setUsrCrt(audit.getCallerId());
		docSignRequest.setDtmCrt(new Date());
		docSignRequest.setTrDocumentH(docH);
		docSignRequest.setUserRequestIp(request.getIpAddress());
		docSignRequest.setUserRequestBrowserInformation(request.getBrowser());
		docSignRequest.setRequestStatus((short) 0);
		docSignRequest.setUserSigningConsentTimestamp(new Date());
		docSignRequest.setMsVendor(vendor);
		daoFactory.getDocumentSigningRequestDao().insertDocumentSigningRequest(docSignRequest);
		
		for (int i = 0; i < listdocId.stream().count(); i++ ) {
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(listdocId.get(i));
			
			TrDocumentSigningRequestDetail detail = new TrDocumentSigningRequestDetail();
			detail.setDtmCrt(new Date());
			detail.setTrDocumentD(docD);
			detail.setTrDocumentSigningRequest(docSignRequest);
			detail.setUsrCrt(audit.getCallerId());
			daoFactory.getDocumentSigningRequestDao().insertDocumentSigningRequestDetail(detail);
		}
	}

	@Override
	public ConfirmSignEmbedResponse signConfirmationDocumentEmbed(ConfirmSignEmbedRequest request, AuditContext audit) {
		EmbedMsgBeanV2 embedBean = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), true, audit);
		audit.setCallerId(embedBean.getDecryptedEmail());
		
		if (StringUtils.isBlank(request.getIpAddress())) {
			request.setIpAddress(MssTool.getApplicationIpAddress());
		}
		
		if (StringUtils.isEmpty(request.getIpAddress())) {
			throw new SignConfirmationDocumentException(getMessage("businesslogic.document.ipaddressempty", null, audit),
					ReasonSignConfirmationDokumen.EMPTY_IP_ADDRESS);
		}
		
		if (StringUtils.isEmpty(request.getBrowser())) {
			throw new SignConfirmationDocumentException(getMessage("businesslogic.document.browserempty", null, audit),
					ReasonSignConfirmationDokumen.EMPTY_BROWSER);
		}
		
		if (CollectionUtils.isEmpty(request.getDocumentIds())) {
			throw new SignConfirmationDocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID, null, audit),
					ReasonSignConfirmationDokumen.EMPTY_DOCUMENT_ID);
		}
		
		TrDocumentD docD = null;

		List<TrDocumentSigningRequest> trCekRequest = null;
		
		List<String> listdocId = request.getDocumentIds();
		for (int i = 0; i < listdocId.stream().count(); i++ ) {
			String decryptedId = commonLogic.decryptMessageToString(listdocId.get(i), embedBean.getMsTenant().getAesEncryptKey(), audit);
			docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(decryptedId);
			if (null == docD) {
				throw new SignConfirmationDocumentException(getMessage("businesslogic.document.documentnotfound",
						new Object[] {decryptedId},audit), ReasonSignConfirmationDokumen.DOCUMENT_ID_INVALID);
			}
			
			AmMsuser checkuser = embedBean.getAmMsuser();
			MsVendorRegisteredUser msVendorRegistered = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(checkuser.getIdMsUser(), docD.getMsVendor().getVendorCode());
			if (null == msVendorRegistered) {
				throw new SignConfirmationDocumentException(getMessage(GlobalKey.MESSAGE_ERROR_USER_INVALID_EMAIL,
						new Object[] {checkuser.getLoginId(),decryptedId}, audit), ReasonSignConfirmationDokumen.INVALID_USER_VENDOR);
			}
			
			List<TrDocumentDSign> docSign = daoFactory.getDocumentDao().getDocumentDSignByIdDocumentDAndIdUser(docD.getIdDocumentD(), checkuser.getIdMsUser());
			if (CollectionUtils.isEmpty(docSign)) {
				throw new SignConfirmationDocumentException(getMessage("businesslogic.document.signerinvalid",
						new Object[] {checkuser.getLoginId(),decryptedId}, audit), ReasonSignConfirmationDokumen.INVALID_USER_SIGNER);
			}
			
			if(!GlobalVal.VENDOR_CODE_VIDA.equals(docD.getMsVendor().getVendorCode()) && !GlobalVal.VENDOR_CODE_PRIVY_ID.equals(docD.getMsVendor().getVendorCode())) {
				throw new SignConfirmationDocumentException(getMessage("businesslogic.document.invalidvendorsign",
						new Object[] {GlobalVal.VENDOR_CODE_VIDA+" / "+GlobalVal.VENDOR_CODE_PRIVY_ID},audit), ReasonSignConfirmationDokumen.INVALID_VENDOR);
			}
			
			trCekRequest = daoFactory.getDocumentSigningRequestDao().getDocumentSigningRequestByDocIdandUserId(decryptedId,msVendorRegistered.getAmMsuser().getIdMsUser());
			
			if (null != trCekRequest && CollectionUtils.isNotEmpty(trCekRequest)) {
				if (3 == trCekRequest.get(0).getRequestStatus()) {
					throw new SignConfirmationDocumentException(getMessage("businesslogic.document.requestsignconfirmationdone",
							new Object[] {decryptedId}, audit), ReasonSignConfirmationDokumen.INVALID_REQUEST_DUPLICATE);
				}
				
				if (2 != trCekRequest.get(0).getRequestStatus()) {
					throw new SignConfirmationDocumentException(getMessage("businesslogic.document.requestsignconfirmationduplicate",
							new Object[] {decryptedId}, audit), ReasonSignConfirmationDokumen.INVALID_REQUEST_DUPLICATE);
				}
			}
			
			List<TrDocumentD> documentToCheck = new ArrayList<>();
			documentToCheck.add(docD);
			documentValidatorLogic.validateDocumentsPrioritySequence(documentToCheck, checkuser, audit);
			
			documentValidatorLogic.validateSignSequence(docD, checkuser, audit);
		}

		AmMsuser user = embedBean.getAmMsuser();
		
		String decryptedDocId = commonLogic.decryptMessageToString(listdocId.get(0), embedBean.getMsTenant().getAesEncryptKey(), audit);
		
		TrDocumentD documentD = daoFactory.getDocumentDao().getDocumentDetailByDocId(decryptedDocId);
		
		MsVendorRegisteredUser msVendorRegisteredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), documentD.getMsVendor().getVendorCode());
		
		if (userValidatorLogic.isCertifExpiredForSign(msVendorRegisteredUser, audit)) {
			throw new UserException(getMessage(GlobalKey.MESSAGE_ERROR_USER_CERTIFICATE_ACTIVE_STATUS_EXPIRED, new Object[] { documentD.getMsVendor().getVendorCode() }, audit), ReasonUser.USER_CERTIFICATE_EXPIRED);
		}
		
		ConfirmSignEmbedResponse response = new ConfirmSignEmbedResponse();
		
		SignConfirmDocumentRequest signConfirmDocumentRequest = new SignConfirmDocumentRequest();
		signConfirmDocumentRequest.setIpAddress(request.getIpAddress());
		signConfirmDocumentRequest.setBrowser(request.getBrowser());
		
		List<String> listDocumentIds = new ArrayList<>();
		for (int i = 0; i < listdocId.size(); i++ ) {
			String documentIdDecrypt = commonLogic.decryptMessageToString(listdocId.get(i), embedBean.getMsTenant().getAesEncryptKey(), audit);
			listDocumentIds.add(documentIdDecrypt);
		}
		
		String redirectAfterSignSetting = tenantSettingsLogic.getSettingValue(embedBean.getMsTenant(), GlobalVal.CODE_LOV_TENANT_SETTING_REDIRECT_AFTER_SIGNING, null);
		response.setRedirectCountDown(0);
		
		if (null != redirectAfterSignSetting) {
			String gensetValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_DEFAULT_REDIRECT_AFTER_SIGNING_COUNTDOWN_TIME);
			int redirectCountDown = tenantSettingsLogic.getSettingValue(embedBean.getMsTenant(), GlobalVal.CODE_LOV_TENANT_SETTING_TIME_TO_REDIRECT,(gensetValue.isEmpty()) ? 0 : Integer.parseInt(gensetValue));
			if (redirectCountDown != 0) {
				response.setRedirectUrl(redirectAfterSignSetting);
				response.setRedirectCountDown(redirectCountDown);
			}
		}
		
		if (!GlobalVal.VENDOR_CODE_PRIVY_ID.equals(docD.getMsVendor().getVendorCode())) {
			insertDocSignRequest(listDocumentIds, user, signConfirmDocumentRequest, audit);
		} else {
			insertDocSignRequestWithDetail(listDocumentIds, user, signConfirmDocumentRequest, audit);
		}
		
		Status status = new Status();
		status.setCode(0);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}

}
