package com.adins.esign.businesslogic.impl;

import static org.junit.Assert.assertNotNull;

import java.text.ParseException;

import javax.transaction.Transactional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.custom.BalanceBean;
import com.adins.esign.model.custom.BalanceThresholdBean;
import com.adins.esign.webservices.model.BalanceRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.google.gson.Gson;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class GenericSaldoLogicTest extends BaseLogic{
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private VendorLogic vendorLogic;
	@Autowired private CommonLogic commonLogic;

	@Autowired private Gson gson;
	private MsTenant tenant;
	private MsVendor vendor;

	private AuditContext auditContext = new AuditContext();
	private String tenantCodeJunit ="WOMF";
	private String vendorCodeJunit ="DIGI";

	@BeforeEach
	void setup() {
		auditContext.setCallerId("JUNIT");
		tenant =  tenantLogic.getTenantByCode(tenantCodeJunit, auditContext);
		vendor = vendorLogic.getVendorByCode(vendorCodeJunit, auditContext);
	}

	@Test
	@Rollback(true)
	void sendReminderEmailTest() {
		try {
			saldoLogic.sendReminderEmail("WOMF", "DIGI", "SDT");
		} catch (ParseException e) {
			e.printStackTrace();
		}

	}
	@Test
	@Rollback(true)
	void balanceThresholdBeanTest() {
		BalanceThresholdBean balanceThresholdBean = gson.fromJson(tenant.getThresholdBalance(), BalanceThresholdBean.class);

		assertNotNull(balanceThresholdBean);
	}

	@Test
	@Rollback(true)
	void checkSaldoBelowThresholdTest() {
		BalanceBean bean = new BalanceBean();

		MsLov balanceTypeLov = commonLogic.getLovByGroupAndCode
				(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_OTP, auditContext);
		
		bean = saldoLogic.checkSaldoBelowThreshold(tenant, vendor, balanceTypeLov);
		assertNotNull(bean);
	}
	
	@Test
	@Rollback(true)
	void getBalanceTest() {
		BalanceRequest request = new BalanceRequest();
		saldoLogic.getBalance(request, auditContext);
	
	}

}
