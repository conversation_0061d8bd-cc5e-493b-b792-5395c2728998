package com.adins.esign.businesslogic.impl;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.adins.esign.businesslogic.api.SmsLogic;

@Disabled
@ExtendWith(SpringExtension.class)
class WassengerSmsLogicTest {
	@Autowired private SmsLogic smsLogic;
	
//	@Test
//	void sendSmsTest() {
//		smsLogic.sendSms("085691160989", "hellow");
//	}
}
