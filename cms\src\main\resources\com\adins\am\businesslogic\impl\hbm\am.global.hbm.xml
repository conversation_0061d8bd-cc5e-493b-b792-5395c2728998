<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="am.global.getGsValue">
	<query-param name="gsCode" type="string"/>
		SELECT GS_VALUE, UUID_GENERAL_SETTING FROM AM_GENERALSETTING with (nolock) WHERE IS_ACTIVE='1' AND GS_CODE = :gsCode
	</sql-query>
</hibernate-mapping>