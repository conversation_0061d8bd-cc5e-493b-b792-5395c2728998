package com.adins.esign.dataaccess.impl;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.OtpDao;
import com.adins.esign.model.TrOtp;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class OtpDaoHbn extends BaseDaoHbn implements OtpDao {

	@Override
	public void insertOtp(TrOtp otp) {
		otp.setUsrCrt(MssTool.maskData(otp.getUsrCrt()));
		this.managerDAO.insert(otp);
	}
	
	@Override
	public void updateOtp(TrOtp otp) {
		otp.setUsrUpd(MssTool.maskData(otp.getUsrUpd()));
		this.managerDAO.update(otp);
	}

	@Override
	public void deleteOtp(TrOtp otp) {
		this.managerDAO.delete(otp);
	}

	@Override
	public TrOtp getOtpByLoginId(String loginId) {
		return this.managerDAO.selectOne(TrOtp.class,
				new Object[][] {{ Restrictions.eq(TrOtp.LOGIN_ID_HBM, StringUtils.upperCase(loginId)) }});
	}

}
