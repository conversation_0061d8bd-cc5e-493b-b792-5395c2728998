package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.JobLogic;
import com.adins.esign.webservices.frontend.api.JobService;
import com.adins.esign.webservices.model.GetListJobByJobProcessTypeRequest;
import com.adins.esign.webservices.model.GetListJobRequest;
import com.adins.esign.webservices.model.GetListJobResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/job")
@Api(value = "JobService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericJobServiceEndpoint implements JobService {
	
	@Autowired private JobLogic jobLogic;

	@Override
	@POST
	@Path("/s/getListJobByJobType")
	public GetListJobResponse getListJobByJobType(GetListJobRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return jobLogic.getListJobByJobType(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/getListJobByJobProcessType")
	public GetListJobResponse getListJobByJobProcessType(GetListJobByJobProcessTypeRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return jobLogic.getListJobByJobProcessType(request, audit);
	}


}
