package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class SignConfirmationDocumentException extends AdInsException {

	private static final long serialVersionUID = 1L;

	public enum ReasonSignConfirmationDokumen {
		EMPTY_IP_ADDRESS
		, E<PERSON>TY_BROWSER
		, EMPTY_EMAIL
		, EMPTY_DOCUMENT_ID
		, DOCUMENT_ID_INVALID
		, INVALID_EMAIL_SIGNER
		, INVALID_VENDOR
		, INVALID_REQUEST_DUPLICATE
		, INVALID_USER_SIGNER
		, INVALID_EMAIL
		, INVALID_USER_VENDOR
		, MUST_OTP
		, EMPTY_PHONE_NO
		
	}

	private ReasonSignConfirmationDokumen reason;

	public SignConfirmationDocumentException(String message, ReasonSignConfirmationDokumen reason) {
		super(message);
		this.reason = reason;

	}

	public ReasonSignConfirmationDokumen getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {

		switch (reason) {
		case EMPTY_IP_ADDRESS:
			return StatusCode.EMPTY_IP_ADDRESS;
		case EMPTY_BROWSER:
			return StatusCode.EMPTY_BROWSER;
		case EMPTY_EMAIL:
			return StatusCode.SIGNER_EMAIL_EMPTY;
		case EMPTY_DOCUMENT_ID:
			return StatusCode.DOCUMENT_ID_EMPTY;
		case DOCUMENT_ID_INVALID:
			return StatusCode.DOCUMENT_NOT_FOUND;
		case INVALID_EMAIL_SIGNER:
			return StatusCode.INVALID_LOGIN_ID;
		case INVALID_VENDOR:
			return StatusCode.INVALID_VENDOR;
		case INVALID_REQUEST_DUPLICATE : 
			return StatusCode.INVALID_REQUEST_DUPLICATE;
		case INVALID_USER_SIGNER :
			return StatusCode.INVALID_USER_SIGNER;
		case INVALID_EMAIL : 
			return StatusCode.UNREGISTERED_EMAIL;
		case INVALID_USER_VENDOR:
			return StatusCode.VENDOR_USER_NOT_FOUND;
		case MUST_OTP:
			return StatusCode.MUST_OTP;
		case EMPTY_PHONE_NO:
			return StatusCode.EMPTY_PHONE_NO;
		default:
			return StatusCode.UNKNOWN;
		}
	}

}
