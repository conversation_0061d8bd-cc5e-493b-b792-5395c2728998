package com.adins.esign.dataaccess.factory.api;

import com.adins.esign.dataaccess.api.OfficeDao;
import com.adins.esign.dataaccess.api.OtpDao;
import com.adins.esign.dataaccess.api.PaymentSignTypeDao;
import com.adins.esign.dataaccess.api.PeruriDocTypeDao;
import com.adins.esign.dataaccess.api.ProcessAutosignBmDao;
import com.adins.esign.dataaccess.api.ProvinceDao;
import com.adins.esign.dataaccess.api.RegionDao;
import com.adins.esign.dataaccess.api.ReregistrationUserDao;
import com.adins.esign.dataaccess.api.BackgroundProcessFailDao;
import com.adins.esign.dataaccess.api.BalanceMutationDao;
import com.adins.esign.dataaccess.api.BalanceTopUpDao;
import com.adins.esign.dataaccess.api.BalanceVendoroftenantDao;
import com.adins.esign.dataaccess.api.BusinessLineDao;
import com.adins.esign.dataaccess.api.ClientCallbackRequestDao;
import com.adins.esign.dataaccess.api.CommonDao;
import com.adins.esign.dataaccess.api.DailyRecapDao;
import com.adins.esign.dataaccess.api.DistrictDao;
import com.adins.esign.dataaccess.api.DocumentDao;
import com.adins.esign.dataaccess.api.DocumentInquiryDao;
import com.adins.esign.dataaccess.api.DocumentSigningRequestDao;
import com.adins.esign.dataaccess.api.EmailDao;
import com.adins.esign.dataaccess.api.ErrorHistoryDao;
import com.adins.esign.dataaccess.api.FaceVerifyDao;
import com.adins.esign.dataaccess.api.FeedbackDao;
import com.adins.esign.dataaccess.api.GeneralSettingDao;
import com.adins.esign.dataaccess.api.InvitationLinkDao;
import com.adins.esign.dataaccess.api.InvitationLinkHistoryDao;
import com.adins.esign.dataaccess.api.JobCheckRegisterStatusDao;
import com.adins.esign.dataaccess.api.JobDao;
import com.adins.esign.dataaccess.api.JobUpdatePsreIdDao;
import com.adins.esign.dataaccess.api.RoleDao;
import com.adins.esign.dataaccess.api.SchedulerJobDao;
import com.adins.esign.dataaccess.api.SignLinkRequestDao;
import com.adins.esign.dataaccess.api.SigningProcessAuditTrailDao;
import com.adins.esign.dataaccess.api.StampDutyDao;
import com.adins.esign.dataaccess.api.SubDistrictDao;
import com.adins.esign.dataaccess.api.LogDao;
import com.adins.esign.dataaccess.api.LovDao;
import com.adins.esign.dataaccess.api.ManualReportDao;
import com.adins.esign.dataaccess.api.MenuDao;
import com.adins.esign.dataaccess.api.MessageDeliveryReportDao;
import com.adins.esign.dataaccess.api.MsgTemplateDao;
import com.adins.esign.dataaccess.api.NotificationtypeoftenantDao;
import com.adins.esign.dataaccess.api.OauthAccessTokenDBDao;
import com.adins.esign.dataaccess.api.VendorDao;
import com.adins.esign.dataaccess.api.VendorRegisteredUserDao;

import com.adins.esign.dataaccess.api.VendorRegisteredUserHistoryDao;

import com.adins.esign.dataaccess.api.TenantDao;
import com.adins.esign.dataaccess.api.TenantSettingsDao;
import com.adins.esign.dataaccess.api.UrlForwarderDao;
import com.adins.esign.dataaccess.api.UserDao;
import com.adins.esign.dataaccess.api.UserDataAccessLogDao;
import com.adins.esign.dataaccess.api.UserActivityLogDao;
import com.adins.esign.dataaccess.api.UserHistoryDao;
import com.adins.esign.dataaccess.api.UseroftenantDao;

public interface DaoFactory {
	OfficeDao getOfficeDao();
	
	UserDao getUserDao();
	
	GeneralSettingDao getGeneralSettingDao();
	
	RoleDao getRoleDao();
	
	LovDao getLovDao();
	
	MenuDao getMenuDao();
	
	CommonDao getCommonDao();
	
	VendorDao getVendorDao();
	
	MsgTemplateDao getMsgTemplateDao();

	DocumentDao getDocumentDao();

	DocumentInquiryDao getDocumentInquiryDao();
		
	LogDao getLogDao();

	TenantDao getTenantDao();
	
	StampDutyDao getStampDutyDao();
	
	BalanceMutationDao getBalanceMutationDao();
	
	FeedbackDao getFeedbackDao();
	
	DailyRecapDao getDailyRecapDao();
	
	PaymentSignTypeDao getPaymentSignTypeDao();
	
	EmailDao getEmailDao();
	
	SchedulerJobDao getSchedulerJobDao();

	FaceVerifyDao getFaceVerifyDao();
	
	BusinessLineDao getBusinessLineDao();
	
	RegionDao getRegionDao();
	
	InvitationLinkDao getInvitationLinkDao();
	
	BalanceVendoroftenantDao getBalanceVendoroftenantDao();
	
	OtpDao getOtpDao();
	
	ErrorHistoryDao getErrorHistoryDao();
	
	ProvinceDao getProvinceDao();
	
	SubDistrictDao getSubDistrictDao();
	
	DistrictDao getDistrictDao();

	ReregistrationUserDao getReregistrationUserDao();
	
	PeruriDocTypeDao getPeruriDocTypeDao();
	
	JobDao getJobDao();
	
	UseroftenantDao getUseroftenantDao();
	
	VendorRegisteredUserDao getVendorRegisteredUserDao();
	
	DocumentSigningRequestDao getDocumentSigningRequestDao();
	
	SignLinkRequestDao getSignLinkRequestDao();
	
	JobUpdatePsreIdDao getJobUpdatePsreIdDao();

	JobCheckRegisterStatusDao getJobCheckRegisterStatusDao();

	MessageDeliveryReportDao getMessageDeliveryReportDao();
	
	ClientCallbackRequestDao getClientCallbackRequestDao();
	
	UrlForwarderDao getUrlForwarderDao();
	
	NotificationtypeoftenantDao getNotificationtypeoftenantDao();
	
	UserDataAccessLogDao getUserDataAccessLogDao();

	UserActivityLogDao getUserActivityLogDao();

	TenantSettingsDao getTenantSettingsDao();
	
	OauthAccessTokenDBDao getOauthAccessTokenDBDao();
	
	ProcessAutosignBmDao getProcessExcelBmDao();

	UserHistoryDao getUserHistoryDao();
	
	VendorRegisteredUserHistoryDao getVendorRegisteredUserHistoryDao();

	SigningProcessAuditTrailDao getSigningProcessAuditTrailDao();
	
	InvitationLinkHistoryDao getInvitationLinkHistoryDao();
	
	ManualReportDao getManualReportDao();
	
	BalanceTopUpDao getBalanceTopUpDao();
	
	BackgroundProcessFailDao getBackgroundProcessFailDao();
}
