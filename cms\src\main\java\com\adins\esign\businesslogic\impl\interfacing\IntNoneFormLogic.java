package com.adins.esign.businesslogic.impl.interfacing;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.interfacing.IntFormLogic;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;


@Component
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
@SuppressWarnings({"unchecked", "rawtypes"})
public class IntNoneFormLogic  extends BaseLogic implements IntFormLogic {
	
//	private LuOnlineLogic luOnlineLogic;
	

	
//	public void setLuOnlineLogic(LuOnlineLogic luOnlineLogic) {
//		this.luOnlineLogic = luOnlineLogic;
//	}
	
/*
	//Collection History
	@Override
	public CollectionHistoryResponse getListCollActHistByAgrNo(
			AuditContext auditContext, String taskID) {
		CollectionHistoryResponse responseResult = this.getIntFormLogic().getListCollActHistByAgrNo(auditContext, taskID);
		return responseResult;
	}

	//Installment Schedule
	@Override
	public InstallmentScheduleResponse getListInstSchdlByAgrNo(
			AuditContext auditContext, String taskID) {
		InstallmentScheduleResponse responseResult = this.getIntFormLogic().getListInstSchdlByAgrNo(auditContext, taskID);
		return responseResult;
	}

	//PaymentHistory
	@Override
	public PaymentHistoryResponse getListPayHistByAgrNo(
			AuditContext auditContext, String taskID) throws ParseException {
		PaymentHistoryResponse responseResult = this.getIntFormLogic().getListPayHistByAgrNo(auditContext, taskID);
		return responseResult;
	}
	
	@Override
	public String submitResult(AuditContext auditContext, String taskId, String isFinal) {
		return null;
	}

	@Override
	public String synSchema(String schemaId, String action, String callerId) {
		return null;
	}

	@Override
	public String cancelTask(String taskID, String flagSource, String callerID) {
		return null;
	}

	@Override
	public String saveResult(AuditContext auditContext, String taskID, String flagSource,
			String subsystemCode, String callerID, String isFinal) {
		return null;
	}
*/
	@Override
	public boolean authenticateUser(AmMsuser amMsUser, String password) {
		return true;
	}
/*
	@Override
	public String saveDepositReport(String batchID) {
		return null;
	}

	@Override
	public Map getPathApp(String isIPPublic) {
		Map result = new HashMap();
		String webPath = SpringPropertiesUtils.getProperty(GlobalKey.APP_BASE);
		String jspPath = StringUtils.EMPTY;
		if ("1".equals(isIPPublic)) {
			jspPath = SpringPropertiesUtils.getProperty(GlobalKey.APP_BASE_JSP_PUBLIC);
		}
		else {
			jspPath = SpringPropertiesUtils.getProperty(GlobalKey.APP_BASE_JSP);
		}
		result.put("WEB", webPath);
		result.put("JSP", jspPath);
		return result;
	}
	
	//tambahan luOnline
	@Override
	@Transactional(readOnly = true)
	public List luOnline(String refId,String lovGroup,String searchVal,
			String choiceFilterVal,AuditContext callerId){
		return this.luOnlineLogic.luOnline(refId, lovGroup, searchVal, choiceFilterVal, callerId);
	}

	@Override
	@Transactional(readOnly = true)
	public boolean validateLuOnlineCode(String code, String lovGroup, AuditContext callerId) {
		return this.luOnlineLogic.validateLuOnlineCode(code, lovGroup, callerId);
	}

	@Override
	public DetailPointResponse getDetailPoint(DetailPointRequest detailPointRequest, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public GetListKompetisiResponse getListKompetisi(String loginId, AuditContext callerId) throws ParseException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public GetDetailKompetisiResponse getRankOrPoint(String programCode, String infotype, int page, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getIconKompetisi(String programCode, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public GetDetailKompetisiResponse changeLevelLoyalti(GetDetailKompetisiResponse detailKompetisi,
			AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public DetailPointResponse changeLevelPointLoyalti(DetailPointResponse detailKompetisi, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public GetDetailKompetisiMobileResponse setRankOrPoint(GetListKompetisiResponse competition,
			AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getLoyaltiPage(AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}
	*/
}
