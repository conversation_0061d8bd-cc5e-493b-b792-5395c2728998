package com.adins.esign.model.custom.validation;
import java.util.Objects;

public class DocumentValidationBean {

    private String referenceNo;
    private String tenantCode;
    
    public String getReferenceNo() {
        return this.referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    @Override
    public boolean equals(Object object) {
        if (this == object) {
            return true;
        }

        if (null == object) {
            return false;
        }

        if (!(object instanceof DocumentValidationBean)) {
            return false;
        }

        DocumentValidationBean bean = (DocumentValidationBean) object;

        if (!Objects.equals(referenceNo, bean.getReferenceNo())) {
            return false;
        }

        return Objects.equals(tenantCode, bean.getTenantCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.referenceNo, this.tenantCode);
    }

    @Override
    public String toString() {
        return "DocumentValidationBean{" +
               "referenceNo='" + getReferenceNo() + '\'' +
               ", tenantCode='" + getTenantCode() + '\'' +
               '}';
    }
}