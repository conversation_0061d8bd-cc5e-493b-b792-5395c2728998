package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.am.model.AmMsprovince;
import com.adins.esign.model.custom.ProvinceBean;
import com.adins.esign.model.custom.ProvinceExternalBean;

public interface ProvinceDao {
	List<ProvinceBean> getProvinceList(String provinceName);
	
	AmMsprovince getProvinceById(Long idProvince);
	AmMsprovince getProvince(Long idMsprovince);
	
	void insertAmMsprovince(AmMsprovince province);
	void updateAmMsprovince(AmMsprovince province);
	
	List<ProvinceExternalBean> getProvinceExternalList();
	AmMsprovince getProvinceByName(String provinceName);
}