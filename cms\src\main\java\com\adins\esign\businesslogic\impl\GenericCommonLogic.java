package com.adins.esign.businesslogic.impl;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.custom.DocumentEMateraiTypeBean;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.ListPsrePriorityBean;
import com.adins.esign.model.custom.ListPsrePriorityExternalBean;
import com.adins.esign.model.custom.LovBean;
import com.adins.esign.model.custom.MenuUserBean;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.webservices.model.GetDocumentEMateraiTypeRequest;
import com.adins.esign.webservices.model.GetDocumentEMateraiTypeResponse;
import com.adins.esign.webservices.model.GetDocumentTypeResponse;
import com.adins.esign.webservices.model.GetListPsrePriorityExternalResponse;
import com.adins.esign.webservices.model.GetPeruriDocumentTypeRequest;
import com.adins.esign.webservices.model.GetPsrePriorityResponse;
import com.adins.esign.webservices.model.LovListRequest;
import com.adins.esign.webservices.model.LovListResponse;
import com.adins.exceptions.UserException;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.util.AesEncryptionUtils;
import com.google.gson.Gson;

@SuppressWarnings("hiding")
@Transactional
@Component
public class GenericCommonLogic extends BaseLogic implements CommonLogic {

	private static final Logger LOG = LoggerFactory.getLogger(GenericCommonLogic.class);
	private static final String CONST_TENANTCODE_IS_INVALID = "tenantCode is invalid";

	@Autowired private Gson gson;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private TenantValidatorLogic tenantValidatorLogic;

	@Override
	public AmGeneralsetting getGeneralSettingByCode(String gsCode, AuditContext callerId) {
		return daoFactory.getGeneralSettingDao().getGsObjByCode(gsCode);
	}

	@Override
	public String getGeneralSettingValueByCode(String gsCode, AuditContext callerId) {
		return daoFactory.getGeneralSettingDao().getGsValueByCode(gsCode);
	}

	@Override
	public MsLov getLovByGroupAndCode(String lovGroup, String lovCode, AuditContext callerId) {
		return daoFactory.getLovDao().getMsLovByGroupAndCode(lovGroup, lovCode);
	}


	@Override
	public List<MenuUserBean> listMenuByRoleAndTenant(String roleCode, String tenantCode, AuditContext callerId) {
		return this.daoFactory.getMenuDao().getMenuByRoleAndTenant(roleCode, tenantCode);		
	}


	@Override
	public LovListResponse getLovByGroupAndConstraint(LovListRequest request) {
		LovListResponse response = new LovListResponse();
		
		if (null == request.getLovGroup() || 0 == request.getLovGroup().length()) {
			Status status = new Status();
			status.setCode(200);
			status.setMessage("lovGroup can not be empty.");
			response.setStatus(status);
			return response;
		}
		
		List<Map<String, Object>> result = this.daoFactory.getLovDao().getMsLovListByGroupAndConstraint(request);
		List<LovBean> lovList = new ArrayList<>();
		response.setLovList(lovList);
		
		if (result.isEmpty()) {
			return response;
		}
		
		Iterator<Map<String, Object>> itr = result.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			
			LovBean bean = new LovBean();
			bean.setCode(String.valueOf(map.get("d0")));
			bean.setDescription(String.valueOf(map.get("d1")));
			bean.setSequence(String.valueOf(map.get("d2")));
			bean.setConstraint1(null == map.get("d3") ? null : String.valueOf(map.get("d3")));
			bean.setConstraint2(null == map.get("d4") ? null : String.valueOf(map.get("d4")));
			bean.setConstraint3(null == map.get("d5") ? null : String.valueOf(map.get("d5")));
			bean.setConstraint4(null == map.get("d6") ? null : String.valueOf(map.get("d6")));
			bean.setConstraint5(null == map.get("d7") ? null : String.valueOf(map.get("d7")));
			
			lovList.add(bean);		
		}
		
		return response;
	}

	@Override
	public LovListResponse getLovEmbedByGroupAndConstraint(LovListRequest request, AuditContext audit) {
		LovListResponse response = new LovListResponse();
		this.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		
		if (StringUtils.isEmpty(request.getLovGroup())) {
			Status status = new Status();
			status.setCode(200);
			status.setMessage("lovGroup can not be empty.");
			response.setStatus(status);
			return response;
		}
		
		List<Map<String, Object>> result = this.daoFactory.getLovDao().getMsLovListByGroupAndConstraint(request);
		List<LovBean> lovList = new ArrayList<>();
		response.setLovList(lovList);
		
		if (result.isEmpty()) {
			return response;
		}
		
		Iterator<Map<String, Object>> itr = result.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			
			LovBean bean = new LovBean();
			bean.setCode(String.valueOf(map.get("d0")));
			bean.setDescription(String.valueOf(map.get("d1")));
			bean.setSequence(String.valueOf(map.get("d2")));
			bean.setConstraint1(String.valueOf(map.get("d3")));
			bean.setConstraint2(String.valueOf(map.get("d4")));
			bean.setConstraint3(String.valueOf(map.get("d5")));
			bean.setConstraint4(String.valueOf(map.get("d6")));
			bean.setConstraint5(String.valueOf(map.get("d7")));
			
			String code = (String) map.get("d0");
			String codeEncrypt = null;
			if(StringUtils.isNotBlank(code)){
				codeEncrypt = this.encryptMessageToString((String) map.get("d0"), audit);
			} else {
				codeEncrypt = "";
			}
			bean.setCodeEncrypt(codeEncrypt);
			
			lovList.add(bean);		
		}
		
		return response;
	}

	@Override
	public String getAesEncryptionKey(AuditContext audit) {
		return this.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_AES_KEY, audit);
	}
	
	@Override
	public <T> T decryptMessageToClass(String encryptedMessage, AuditContext audit, Class<T> classOfT) {
		String aesKey = this.getAesEncryptionKey(audit);
		return decryptMessageToClass(encryptedMessage, aesKey, classOfT, audit);
	}


	@Override
	public String decryptMessageToString(String encryptedMessage, AuditContext audit) {
		String aesKey = this.getAesEncryptionKey(audit);
		return decryptMessageToString(encryptedMessage, aesKey, audit);
	}


	@Override
	public String encryptMessageToString(String message, AuditContext audit) {
		String aesKey = this.getAesEncryptionKey(audit);
		return encryptMessageToString(message, aesKey, audit);
	}

	@Override
	public GetDocumentEMateraiTypeResponse getListPeruriDocTypeWithApiKey(String apiKey, AuditContext audit) {
		GetDocumentEMateraiTypeResponse response = new GetDocumentEMateraiTypeResponse();
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(apiKey, audit);
		if(null == tenant) {
			response.setMessage(CONST_TENANTCODE_IS_INVALID);
			return response;
		}
		List<DocumentEMateraiTypeBean> listDocEMateraiTypeBean = this.daoFactory.getCommonDao().getListDocumentEMateraiType("1");
		response.setDocumentEMateraiList(listDocEMateraiTypeBean);
		
		return response;
	}
	
	@Override
	public GetDocumentEMateraiTypeResponse getListPeruriDocType(GetPeruriDocumentTypeRequest request, AuditContext audit) {
		GetDocumentEMateraiTypeResponse response = new GetDocumentEMateraiTypeResponse();
		List<DocumentEMateraiTypeBean> listDocEMateraiTypeBean = this.daoFactory.getCommonDao().getListDocumentEMateraiType("1");
		response.setDocumentEMateraiList(listDocEMateraiTypeBean);
		
		return response;
	}
	
	@Override
	public GetDocumentTypeResponse getListDocumentTypeWithApiKey(String apiKey, AuditContext audit) {
		GetDocumentTypeResponse response = new GetDocumentTypeResponse();
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(apiKey, audit);

		if (null == tenant) {
			Status status = new Status();
			status.setCode(200);
			status.setMessage(CONST_TENANTCODE_IS_INVALID);
			response.setStatus(status);
			return response;
		}
		
		List<Map<String, Object>> result = this.daoFactory.getLovDao().getMsLovListByLovGroup(GlobalVal.LOV_GROUP_DOC_TYPE);
		List<LovBean> lovList = new ArrayList<>();
		
		if (result.isEmpty()) {
			return response;
		}
		
		Iterator<Map<String, Object>> itr = result.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			
			LovBean bean = new LovBean();
			bean.setCode(String.valueOf(map.get("d0")));
			bean.setDescription(String.valueOf(map.get("d1")));
			
			lovList.add(bean);		
		}
		response.setDocumentTypeList(lovList);
		
		return response;
	}
	
	@Override
	public GetDocumentEMateraiTypeResponse getListPeruriDocTypeEmbed(GetDocumentEMateraiTypeRequest request,
			AuditContext audit) {
		GetDocumentEMateraiTypeResponse response = new GetDocumentEMateraiTypeResponse();
		EmbedMsgBean msgBean = null;
		try {
			msgBean = decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);

		}catch(Exception e) {
			response.setMessage("Decrypt failed");
			return response;
		}
		String tenantCode = msgBean.getTenantCode();
		MsTenant tenant = this.daoFactory.getTenantDao().getTenantByCode(tenantCode);
		if(null == tenant) {
			response.setMessage(CONST_TENANTCODE_IS_INVALID);
			return response;
		}
		List<DocumentEMateraiTypeBean> listDocEMateraiTypeBean = this.daoFactory.getCommonDao().getListDocumentEMateraiType("1");
		response.setDocumentEMateraiList(listDocEMateraiTypeBean);
		
		return response;
	}
	
	private String doUrlDecode(String message) {
		if (StringUtils.contains(message, " ")) {
			message = message.replaceAll("\\s","+");
		}
		
		if (StringUtils.contains(message, "%")) {
			try {
				message = URLDecoder.decode(message, StandardCharsets.UTF_8.toString());
			}
			catch (UnsupportedEncodingException e) {
				LOG.warn("Exception when url-decoding message: {}", message);
			}
			message = message.replace("\n", "").replace("\r", "");
		}
		return message;
	}

	@Override
	public String decryptMessageToString(String encryptedMessage, String key, AuditContext audit) {
		String decodedMessage = doUrlDecode(encryptedMessage);
		return AesEncryptionUtils.decrypt(decodedMessage, key);
	}
	
	@Override
	public <T> T decryptMessageToClass(String encryptedMessage, String key, Class<T> classOfT, AuditContext audit) {
		String decryptedMessage = decryptMessageToString(encryptedMessage, key, audit);
		return gson.fromJson(decryptedMessage, classOfT);
	}

	@Override
	public String encryptMessageToString(String message, String key, AuditContext audit) {
		return AesEncryptionUtils.encrypt(message, key);
	}

	@Override
	public GetListPsrePriorityExternalResponse getListPsrePriority(String apiKey, AuditContext audit) {
		
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(apiKey, audit);
		
		List<ListPsrePriorityBean> listPsrePriority = daoFactory.getVendorDao().getListPsrePriority(tenant.getTenantCode());
		
		List<ListPsrePriorityExternalBean> listPsreExternalPriority = convertToListPsreExternalPriority(listPsrePriority);
				
		GetListPsrePriorityExternalResponse response = new GetListPsrePriorityExternalResponse();
		
		response.setListPsrePriority(listPsreExternalPriority);
		
		return response;
		
	}

	public static List<ListPsrePriorityExternalBean> convertToListPsreExternalPriority(List<ListPsrePriorityBean> listPsrePriority) {
		return listPsrePriority.stream().map(bean -> {
	    	ListPsrePriorityExternalBean anotherBean = new ListPsrePriorityExternalBean();
	        anotherBean.setPsreCode(bean.getVendorCode());
	        anotherBean.setPsreName(bean.getVendorName());
	        anotherBean.setPriority(bean.getPriority());
	        return anotherBean;
	    }).collect(Collectors.toList());
}
	
	
	
	
}
