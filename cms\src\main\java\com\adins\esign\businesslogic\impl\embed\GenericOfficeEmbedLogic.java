package com.adins.esign.businesslogic.impl.embed;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.embed.OfficeEmbedLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.OfficeBean;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.webservices.model.OfficeListResponse;
import com.adins.esign.webservices.model.embed.OfficeListRequest;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericOfficeEmbedLogic extends BaseLogic implements OfficeEmbedLogic {
	
	@Autowired EmbedValidatorLogic embedValidatorLogic;
	@Autowired CommonLogic commonLogic;

	@Override
	public OfficeListResponse getOfficeListEmbed(OfficeListRequest request, AuditContext audit) {
		EmbedMsgBeanV2 msgBean =  embedValidatorLogic.validateEmbedMessageV2(request.getMsg(), request.getTenantCode(), false, audit);
		audit.setCallerId(msgBean.getDecryptedEmail());
		
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(messageSource.getMessage("businesslogic.paymentsigntype.emptytenantcode", null
					, this.retrieveLocaleAudit(audit))
					, ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			throw new TenantException(getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {"Tenant code", request.getTenantCode()}, audit), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		OfficeListResponse response = new OfficeListResponse();
		List<OfficeBean> officeBeanList = new ArrayList<>();
		
		List<MsOffice> officeList = daoFactory.getOfficeDao().getOfficeList(tenant.getTenantCode());
		for (MsOffice office : officeList) {
			OfficeBean bean = new OfficeBean();
			bean.setOfficeCode(commonLogic.encryptMessageToString(office.getOfficeCode(), tenant.getAesEncryptKey(), audit));
			bean.setOfficeName(office.getOfficeName());
			officeBeanList.add(bean);
		}
		response.setOfficeList(officeBeanList);
		return response;
	}

}
