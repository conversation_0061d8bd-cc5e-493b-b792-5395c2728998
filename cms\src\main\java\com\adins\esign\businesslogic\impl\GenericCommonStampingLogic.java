package com.adins.esign.businesslogic.impl;

import java.io.IOException;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.utils.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.CommonStampingLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.interfacing.EmeteraiPajakkuLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.enums.StampingDocumentType;
import com.adins.esign.constants.enums.StampingErrorDetail;
import com.adins.esign.constants.enums.StampingErrorLocation;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDStampduty;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrDocumentHStampdutyError;
import com.adins.esign.model.TrSchedulerJob;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.DownloadStampedDocResponse;
import com.adins.esign.model.custom.EmailAttachmentBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.IncrementAgreementStampingErrorCountBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.BalanceRequest;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.exceptions.EmeteraiException;
import com.adins.exceptions.StampingException;
import com.adins.exceptions.EmeteraiException.ReasonEmeterai;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericCommonStampingLogic extends BaseLogic implements CommonStampingLogic {
	
	// Development 4.4.0
	// Development 4.5.0
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericCommonStampingLogic.class);

	// Stamping parameter default value
	public static final int MAX_ERROR_COUNT = 3;
	public static final int STM_SDT_ATTEMPTS = 1;
	public static final int STAMPED_DOC_CHECK_ATTEMPTS = 50;
	public static final long STAMPED_DOC_CHECK_DELAY = 100;
	public static final long CONN_TIMEOUT = 30_000;
	public static final long READ_TIMEOUT = 300_000;
	
	private static final String[] ERROR_EMAIL_RECEIVERS = {"<EMAIL>"};
	private static final String MSG_EMPTY_DOCUMENT = "(document not specified)";
	
	@Value("${e-meterai.pajakku.user}") private String username;
	@Value("${e-meterai.pajakku.password}") private String password;
	@Value("${spring.mail.username}") private String emailSender;
	
	// Generate parameter
	@Value("${emeterai.pajakku.namadoc}") private String namaDoc;
	
	// Stamp parameter
	@Value("${emeterai.pajakku.reason}") private String reason;
	@Value("${emeterai.pajakku.reason2}") private String reason2;
	@Value("${emeterai.onprem.stamp.dest}") private String onPremStampDest;
	@Value("${emeterai.onprem.stamp.specimen}") private String onPremSpecimenPath;
	@Value("${emeterai.onprem.stamp.src}") private String onPremStampSource;
	
	@Autowired private CommonLogic commonLogic;
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private DocumentLogic documentLogic;
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private MessageTemplateLogic messageTemplateLogic;
	@Autowired private EmeteraiPajakkuLogic pajakkuLogic;
	@Autowired private SaldoLogic saldoLogic;
	
	// Hotfix 4.3.1
	// Hotfix *******
	
	// Hotfix 4.3.2
	
	@Override
	public int getStampingMaxErrorCount(AuditContext audit) {
		String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_ATTACH_SDT_MAX_REPEATED_ERROR_COUNT, audit);
		if (StringUtils.isBlank(gsValue)) {
			return MAX_ERROR_COUNT;
		}
		
		try {
			return Integer.valueOf(gsValue);
		} catch (Exception e) {
			return MAX_ERROR_COUNT;
		}
	}

	@Override
	public long getStampingConnectionTimeout(StampingDocumentType documentType, AuditContext audit) {
		String timeoutValue = null;
		if (StampingDocumentType.PAYMENT_RECEIPT == documentType || StampingDocumentType.PAYMENT_RECEIPT_ON_PREM == documentType) {
			timeoutValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_PAYMENT_RECEIPT_PERURI_CONN_TIMEOUT, audit);
		} else {
			timeoutValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_PERURI_CONN_TIMEOUT, audit);
		}
		
		if (StringUtils.isBlank(timeoutValue)) {
			return CONN_TIMEOUT; 
		}
		
		try {
			return Long.parseLong(timeoutValue);
		} catch (Exception e) {
			return CONN_TIMEOUT;
		}
	}

	@Override
	public long getStampingReadTimeout(StampingDocumentType documentType, AuditContext audit) {
		String timeoutValue = null;
		if (StampingDocumentType.PAYMENT_RECEIPT == documentType || StampingDocumentType.PAYMENT_RECEIPT_ON_PREM == documentType) {
			timeoutValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_PAYMENT_RECEIPT_PERURI_READ_TIMEOUT, audit);
		} else {
			timeoutValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_PERURI_READ_TIMEOUT, audit);
		}
		
		if (StringUtils.isBlank(timeoutValue)) {
			return READ_TIMEOUT; 
		}
		
		try {
			return Long.parseLong(timeoutValue);
		} catch (Exception e) {
			return READ_TIMEOUT;
		}
	}

	@Override
	public String getAccountUsername(TrDocumentH documentH, AuditContext audit) {
		if (!"1".equals(documentH.getIsPostpaidStampduty())) {
			return username;
		}
		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(documentH.getMsTenant(), GlobalVal.CODE_LOV_TENANT_SETTING_PERURI_ACCOUNT_NAME);
		if (null == tenantSettings) {
			return null;
		}
		return tenantSettings.getSettingValue();
	}

	@Override
	public String getAccountPassword(TrDocumentH documentH, AuditContext audit) {
		if (!"1".equals(documentH.getIsPostpaidStampduty())) {
			return password;
		}
		
		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(documentH.getMsTenant(), GlobalVal.CODE_LOV_TENANT_SETTING_PERURI_ACCOUNT_PASSWORD);
		if (null == tenantSettings) {
			return null;
		}
		return tenantSettings.getSettingValue();
	}
	
	@Override
	public String[] getErrorEmailRecipients(MsTenant tenant, AuditContext audit) {
		if (null == tenant) {
			return ERROR_EMAIL_RECEIVERS;
		}
		
		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ATTACH_SDT_ERROR_EMAIL_RECEIVER);
		if (null == tenantSettings) {
			return ERROR_EMAIL_RECEIVERS;
		}
		
		String emailReceivers = tenantSettings.getSettingValue();
		if (StringUtils.isBlank(emailReceivers)) {
			return ERROR_EMAIL_RECEIVERS;
		}
		
		String[] receivers = emailReceivers.split(";");
		if (StringUtils.isBlank(receivers[0])) {
			receivers = ERROR_EMAIL_RECEIVERS;
		}
		return receivers;
	}

	@Override
	public void updateDocumentHMeteraiProcess(TrDocumentH documentH, String prosesMeterai, AuditContext audit) {
		LOG.info("Kontrak {}, updating proses_materai to {}", documentH.getRefNumber(), prosesMeterai);
		documentH.setProsesMaterai(new Short(prosesMeterai));
		documentH.setDtmUpd(new Date());
		documentH.setUsrUpd(audit.getCallerId());
		daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
	}
	
	private byte[] buildStackTraceTextFile(Exception e) {
		String stackTrace = ExceptionUtils.getStackTrace(e);
		String base64 = Base64.getEncoder().encodeToString(stackTrace.getBytes());
		return Base64.getDecoder().decode(base64);
	}
	
	private String buildStackTraceFileName(String errorLocation, String errorLocationDetail) {
		String currentTime = MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_TIME_FORMAT_SEQ);
		StringBuilder filename = new StringBuilder()
				.append(StringUtils.upperCase(errorLocation)).append("_")
				.append(StringUtils.upperCase(errorLocationDetail)).append("_")
				.append(currentTime)
				.append(".txt");
		return filename.toString();
	}
	
	private void sendAttachEmeteraiErrorEmail(TrDocumentHStampdutyError documentHSdtError, Exception e, String jsonRequest, String jsonResponse, AuditContext audit) {
		if (null == documentHSdtError) {
			return;
		}
		
		EmailAttachmentBean[] attachments = null;
		if (null != e) {
			byte[] stackTraceFile = buildStackTraceTextFile(e);
			String filename = buildStackTraceFileName(documentHSdtError.getErrorLocation(), documentHSdtError.getErrorLocationDetail());
			EmailAttachmentBean attachment = new EmailAttachmentBean(stackTraceFile, filename);
			attachments = new EmailAttachmentBean[] {attachment};
		}
		
		TrDocumentH documentH = documentHSdtError.getTrDocumentH();
		TrDocumentD documentD = documentHSdtError.getTrDocumentD();
		String totalMeterai = (null == documentD) ? MSG_EMPTY_DOCUMENT : String.valueOf(documentD.getTotalMaterai());
		String totalStamping = (null == documentD) ? MSG_EMPTY_DOCUMENT : String.valueOf(documentD.getTotalStamping());
		String documentId = (null == documentD) ? MSG_EMPTY_DOCUMENT : documentD.getDocumentId();
		
		Map<String, Object> kontrak = new HashMap<>();
		kontrak.put("refNumber", documentH.getRefNumber());
		kontrak.put("documentId", documentId);
		kontrak.put("totalMeterai", totalStamping + "/" + totalMeterai);
		kontrak.put("tenantName", documentH.getMsTenant().getTenantName());
		
		String errorLocation = StringUtils.isBlank(documentHSdtError.getErrorLocation()) ? "(error location unspecified)" : documentHSdtError.getErrorLocation();
		String errorLocationDetail = StringUtils.isBlank(documentHSdtError.getErrorLocationDetail()) ? "(error location detail unspecified)" : documentHSdtError.getErrorLocationDetail();
		String errorMsg = StringUtils.isBlank(documentHSdtError.getErrorMessage()) ? "(error message unspecified)" : documentHSdtError.getErrorMessage();
		String request = StringUtils.isBlank(jsonRequest) ? "(no request)" : jsonRequest;
		String response = StringUtils.isBlank(jsonResponse) ? "(no response)" : jsonResponse;
		
		Map<String, Object> error = new HashMap<>();
		error.put("errorTime", MssTool.formatDateToStringIn(documentHSdtError.getDtmUpd(), GlobalVal.DATE_TIME_FORMAT_SEC));
		error.put("errorCount", documentHSdtError.getErrorCount());
		error.put("errorLocation", errorLocation);
		error.put("errorLocationDetail", errorLocationDetail);
		error.put("errorMsg", errorMsg);
		error.put("request", request);
		error.put("response", response);
		
		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("kontrak", kontrak);
		templateParameters.put("error", error);
		
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_ATTACH_EMETERAI_ERROR, templateParameters);
		
		String[] receivers = getErrorEmailRecipients(documentH.getMsTenant(), audit);
		
		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setFrom(emailSender);
		emailInfo.setSubject(template.getSubject());
		emailInfo.setBodyMessage(template.getBody());
		emailInfo.setTo(receivers);
		try {
			String receiverInString = Arrays.toString(receivers);
			LOG.info("Sending Email to: {}", receiverInString);
			emailSenderLogic.sendEmail(emailInfo, attachments);
		} catch (Exception e1) {
			LOG.error("Error during sending attach emeterai error email: {}", e1.getLocalizedMessage());
		}
	}
	
	private String getExceptionErrorMessage(Exception e) {
		return StringUtils.isBlank(e.getLocalizedMessage()) ? e.toString() : e.getLocalizedMessage();
	}
	
	private boolean isRepeatedError(TrDocumentHStampdutyError documentHSdtError, StampingDocumentType documentType, String currentErrorLocation, String currentErrorLocationDetail, String currentErrorMessage) {
		// If payment receipt, any error is treated as repeated error (to increment error count)
		if (StampingDocumentType.PAYMENT_RECEIPT == documentType || StampingDocumentType.PAYMENT_RECEIPT_ON_PREM == documentType) {
			return true;
		}
		
		return StampingDocumentType.REGULAR_DOCUMENT == documentType 
						&& documentHSdtError.getErrorLocation().equals(currentErrorLocation)
						&& documentHSdtError.getErrorLocationDetail().equals(currentErrorLocationDetail)
						&& documentHSdtError.getErrorMessage().equals(currentErrorMessage);
	}

	@Override
	public void incrementAgreementStampingErrorCount(IncrementAgreementStampingErrorCountBean bean, String processMeterai, StampingDocumentType documentType, StampingErrorLocation errorLocation, StampingErrorDetail errorDetail, boolean throwMaxError, AuditContext audit) {
		
		TrDocumentH documentH = bean.getTrDocumentH();
		TrDocumentD documentD = bean.getTrDocumentD();
		Exception e = bean.getException();
		String errorMsg = (null != e) ? getExceptionErrorMessage(e) : bean.getErrorMsg();
		String jsonRequest = bean.getJsonRequest();
		String jsonResponse = bean.getJsonResponse();
		
		// IF throwMaxError is true, THEN it is a synchronous process. Does not firstly update proses_materai
		if (!throwMaxError) {
			updateDocumentHMeteraiProcess(documentH, processMeterai, audit);
		}
		
		Long idDocumentH = documentH.getIdDocumentH();
		Long idDocumentD = (null == documentD) ? null : documentD.getIdDocumentD();
		
		String currentErrorLocation = errorLocation.toString();
		String currentErrorLocationDetail = errorDetail.toString();
		String currentErrorMessage = StringUtils.isBlank(errorMsg) ? StringUtils.EMPTY : StringUtils.left(errorMsg, 1000);
		Integer maxErrorCount = getStampingMaxErrorCount(audit);
		
		TrDocumentHStampdutyError documentHSdtError = daoFactory.getDocumentDao().getDocumentHStampdutyErrorNewTran(idDocumentH, idDocumentD);
		
		if (null == documentHSdtError) {
			LOG.info("Kontrak {}, error count = {}, max error count = {}", documentH.getRefNumber(), 1, maxErrorCount);
			documentHSdtError = new TrDocumentHStampdutyError();
			documentHSdtError.setTrDocumentH(documentH);
			documentHSdtError.setTrDocumentD(documentD);
			documentHSdtError.setErrorCount((short) 1);
			documentHSdtError.setErrorLocation(currentErrorLocation);
			documentHSdtError.setErrorLocationDetail(currentErrorLocationDetail);
			documentHSdtError.setErrorMessage(currentErrorMessage);
			documentHSdtError.setIsEmailSent("0");
			documentHSdtError.setUsrCrt(audit.getCallerId());
			documentHSdtError.setDtmCrt(new Date());
			daoFactory.getDocumentDao().insertDocumentHStampdutyErrorNewTran(documentHSdtError);
			return;
		}
		
		if (isRepeatedError(documentHSdtError, documentType, currentErrorLocation, currentErrorLocationDetail, currentErrorMessage)) {
			
			short errorCount = documentHSdtError.getErrorCount();
			errorCount += 1;
			
			documentHSdtError.setErrorLocation(currentErrorLocation);
			documentHSdtError.setErrorLocationDetail(currentErrorLocationDetail);
			documentHSdtError.setErrorMessage(currentErrorMessage);
			documentHSdtError.setErrorCount(errorCount);
			documentHSdtError.setDtmUpd(new Date());
			documentHSdtError.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);
			
		} else {
			// Reset error count
			documentHSdtError.setErrorCount((short) 1);
			documentHSdtError.setTrDocumentD(documentD);
			documentHSdtError.setErrorLocation(currentErrorLocation);
			documentHSdtError.setErrorLocationDetail(currentErrorLocationDetail);
			documentHSdtError.setErrorMessage(currentErrorMessage);
			documentHSdtError.setDtmUpd(new Date());
			documentHSdtError.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);
		}
		
		LOG.info("Kontrak {}, error count = {}, max error count = {}", documentH.getRefNumber(), documentHSdtError.getErrorCount(), maxErrorCount);
		if (documentHSdtError.getErrorCount() >= maxErrorCount) {
			String prevEmailFlag = documentHSdtError.getIsEmailSent();
			
			documentHSdtError.setIsEmailSent("1");
			documentHSdtError.setUsrUpd(audit.getCallerId());
			documentHSdtError.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHStampdutyErrorNewTran(documentHSdtError);
			
			if (!"1".equals(prevEmailFlag)) {
				LOG.error("Send Email PaymentReciept");
				sendAttachEmeteraiErrorEmail(documentHSdtError, e, jsonRequest, jsonResponse, audit);
			}
			
			updateMeteraiProcessBasedOnDocumentType(documentH, documentType, audit);
			
			if (throwMaxError) {
				throw new StampingException(getMessage("businesslogic.emeterai.stampfailed", null, audit));
			}
		}
	}
	
	private void updateMeteraiProcessBasedOnDocumentType(TrDocumentH documentH, StampingDocumentType documentType, AuditContext audit) {
		if (StampingDocumentType.PAYMENT_RECEIPT == documentType) {
			updateDocumentHMeteraiProcess(documentH, GlobalVal.PR_STAMP_FAILED, audit);
		} else if (StampingDocumentType.PAYMENT_RECEIPT_ON_PREM == documentType) {
			updateDocumentHMeteraiProcess(documentH, GlobalVal.ON_PREM_PR_STAMP_FAILED, audit);
		} else if (StampingDocumentType.REGULAR_DOCUMENT == documentType) {
			updateDocumentHMeteraiProcess(documentH, GlobalVal.ON_PREM_STAMP_FAILED, audit);
		}
	}

	@Override
	public String storeStampedDocumentToOss(TrDocumentD document, String peruriLoginToken, AuditContext audit) {
		DownloadStampedDocResponse response = pajakkuLogic.downloadStampedDoc(document, peruriLoginToken, audit);
		if (!GlobalVal.PERURI_SUCCESS_CODE.equals(response.getResponseCode())) {
			throw new EmeteraiException(response.getMessage(), ReasonEmeterai.DOWNLOAD_DOC_RESPONSE_ERROR);
		}
		
		String base64Pdf = response.getDataReturn().getPdfFile();
		byte[] documentByteArray = Base64.getDecoder().decode(base64Pdf);
		return cloudStorageLogic.storeStampedDocument(	document, documentByteArray);
	}

	@Override
	public String getStampedDocumentFromOss(TrDocumentD document, AuditContext audit) {
		byte[] documentByteArray = cloudStorageLogic.getStampedDocument(document);
		if (null == documentByteArray) {
			throw new EmeteraiException(getMessage("businesslogic.emeterai.failedtogetossdocument",
					new String[] {document.getDocumentId()}, audit), ReasonEmeterai.DOWNLOAD_DOC_EXCEPTION);
		}
		return Base64.getEncoder().encodeToString(documentByteArray);
	}
	
	private void sendInsufficientSdtBalanceEmail(String action, String refNo, String documentName, String balanceType, Integer saldo, String[] emailDest) {
		Map<String, Object> reminder = new HashMap<>();
		reminder.put("title", action);
		reminder.put("action", action);
		reminder.put("refNo", refNo);
		reminder.put("documentName", documentName);
		reminder.put("balanceType", balanceType);
		reminder.put("saldo", saldo);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("reminder", reminder);

		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INSUFFICIENT_BAL, templateParameters);
		String[] recipient = emailDest;

		EmailInformationBean emailBean = new EmailInformationBean();
		emailBean.setFrom(emailSender);
		emailBean.setTo(recipient);
		emailBean.setBodyMessage(template.getBody());
		emailBean.setSubject(template.getSubject());

		try {
			emailSenderLogic.sendEmail(emailBean, null);
		} catch (Exception e) {
			LOG.error("Kontrak {}, Send email error: {}", refNo, e.getMessage());
		}
	}

	@Override
	public boolean enoughSdtBalance(TrDocumentD document, int sdtNeeded, AuditContext audit) {
		
		boolean isPostpaid = "1".equals(document.getTrDocumentH().getIsPostpaidStampduty());
		
		TrDocumentH documentH = document.getTrDocumentH();
		MsTenant tenant = document.getMsTenant();
		MsLov balanceType = null;
		if (isPostpaid) {
			balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT_POSTPAID);
		} else {
			balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		}
		
		BalanceRequest balanceRequest = new BalanceRequest();
		balanceRequest.setTenantCode(tenant.getTenantCode());
		balanceRequest.setVendorCode(GlobalVal.VENDOR_CODE_ESG);
		balanceRequest.setBalanceType(balanceType.getCode());
		
		Integer balance = saldoLogic.getBalanceNotSecure(balanceRequest, audit).getListBalance().get(0).getCurrentBalance().intValue();
		if (balance >= sdtNeeded) {
			return true;
		}
		
		if (null != documentH.getEmailSaldo() && documentH.getEmailSaldo().intValue() >= 1) {
			LOG.info("Insufficient balance email has already been sent for Nomor Kontrak {}", documentH.getRefNumber());
			return false;
		}
		
		if (null == documentH.getEmailSaldo() || 0 == documentH.getEmailSaldo().intValue()) {
			documentH.setEmailSaldo((short) 1);
			documentH.setUsrUpd(audit.getCallerId());
			documentH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
		}
		
		// Check tr_scheduler_job
		int dailyQuotaReminder = Integer.parseInt(commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_REMINDER_TOPUP_FQ, null));
		String currentDate = MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_FORMAT);
		TrSchedulerJob job = daoFactory.getSchedulerJobDao().getSchedulerJobNewTrx(document.getMsTenant(), currentDate, GlobalVal.CODE_LOV_JOB_TYPE_BALREM, balanceType.getCode());
		if (null != job && job.getMailReminderCount() >= dailyQuotaReminder) {
			LOG.info("Doesn't need to send email reminder. Mail reminder count: {}, daily reminder quota: {}", job.getMailReminderCount(), dailyQuotaReminder);
			return false;
		}
			
		if (null == job) {
			MsLov schedulerType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_SCHEDULER_TYPE, GlobalVal.CODE_LOV_SCHED_TYPE_NON_SCHED);
			MsLov jobType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_JOB_TYPE, GlobalVal.CODE_LOV_JOB_TYPE_BALREM);
				
			job = new TrSchedulerJob();
			job.setSchedulerStart(new Date());
			job.setSchedulerEnd(new Date());
			job.setDataProcessed(0L);
			job.setUsrCrt(audit.getCallerId());
			job.setDtmCrt(new Date());
			job.setMsLovByJobType(jobType);
			job.setMsLovBySchedulerType(schedulerType);
			job.setMsLovByBalanceType(balanceType);
			job.setMailReminderCount((short) 1);
			job.setMsTenant(document.getMsTenant());
			daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(job);
		} else {
			short reminderCount = job.getMailReminderCount();
			reminderCount += 1;
			job.setMailReminderCount(reminderCount);
			job.setUsrUpd(audit.getCallerId());
			job.setDtmUpd(new Date());
			daoFactory.getSchedulerJobDao().updateSchedulerJobNewTrx(job);
		}
			
		LOG.warn("Insufficient balance to generate Emeterai for tenant {}, Kontrak {}", tenant.getTenantCode(), documentH.getRefNumber());
		String[] recipient = tenant.getEmailReminderDest().split(",");
			
		String documentName = document.getMsDocTemplate() == null ? document.getDocumentName() : document.getMsDocTemplate().getDocTemplateName();
		sendInsufficientSdtBalanceEmail("generate e-Meterai", documentH.getRefNumber(), documentName, balanceType.getDescription(), balance, recipient);
		return false;
		
	}

	@Override
	public String getNamaDocForGenerate(TrDocumentD document, AuditContext audit) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			return document.getMsPeruriDocType().getDocName();
		}
		return namaDoc;
	}

	@Override
	public String getNoDocForGenerate(TrDocumentD document, AuditContext audit) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			return document.getTrDocumentH().getRefNumber();
		}
		return document.getDocumentId();
	}

	@Override
	public String getTglDocForGenerate(TrDocumentD document, AuditContext audit) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			return MssTool.formatDateToStringIn(document.getRequestDate(), GlobalVal.DATE_FORMAT);
		}
		return MssTool.formatDateToStringIn(document.getCompletedDate(), GlobalVal.DATE_FORMAT);
	}

	@Override
	public String getBalanceMutationNotesForGenerate(TrDocumentDStampduty docSdt, AuditContext audit) {
		TrDocumentD document = docSdt.getTrDocumentD();
		TrDocumentH documentH = document.getTrDocumentH();
		TrStampDuty sdt = docSdt.getTrStampDuty();
		
		if ("1".equals(documentH.getIsManualUpload()) && StringUtils.isNotBlank(document.getDocumentName())) {
			return StringUtils.isNotBlank(docSdt.getNotes()) ? docSdt.getNotes() : sdt.getStampDutyNo();
		}
		return sdt.getStampDutyNo();
	}

	@Override
	public void updateDocumentDMeteraiProcess(TrDocumentD document, String sdtProcess, AuditContext audit) {
		LOG.info("Kontrak {}, Dokumen {}, updating sdt_process to {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), sdtProcess);
		document.setDtmUpd(new Date());
		document.setUsrUpd(audit.getCallerId());
		document.setSdtProcess(sdtProcess);
		daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
	}

	@Override
	public String getReasonForStamp(TrDocumentD document, AuditContext audit) {
		if ("1".equals(document.getTrDocumentH().getIsManualUpload())) {
			return reason2;
		}
		return reason;
	}

	@Override
	public int getStampingRetryAttempts(AuditContext audit) {
		String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_NUMBER_OF_STM_SDT_ATTEMPTS, audit);
		if (StringUtils.isBlank(gsValue)) {
			return STM_SDT_ATTEMPTS;
		}
		
		try {
			int attempts = Integer.parseInt(gsValue);
			return (attempts < 0) ? 0 : attempts;
		} catch (Exception e) {
			return STM_SDT_ATTEMPTS;
		}
	}

	@Override
	public String getDocumentFileToUpload(TrDocumentD document, AuditContext audit) throws IOException {
		if (document.getTotalStamping() > 0) {
			return getStampedDocumentFromOss(document, audit);
		}
		
		ViewDocumentRequest request = new ViewDocumentRequest();
		request.setDocumentId(document.getDocumentId());
		
		ViewDocumentResponse response = documentLogic.viewDocumentWithoutSecurity(request, audit);
		return response.getPdfBase64();
	}

	@Override
	public String getStampDutyFee(AuditContext audit) {
		String nilaiMeteraiLunas =  commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_NILAI_METERAI_LUNAS, audit);
		if (StringUtils.isBlank(nilaiMeteraiLunas)) {
			nilaiMeteraiLunas = GlobalVal.PAJAKKU_NILAI_METERAI_LUNAS;
		}
		return nilaiMeteraiLunas;
	}

	@Override
	public String getOnPremStampDestination(TrDocumentD document, AuditContext audit) {
		return onPremStampDest + "/" + document.getDocumentId() + ".pdf";
	}

	@Override
	public String getOnPremSpecimenPath(TrDocumentDStampduty docSdt, AuditContext audit) {
		return onPremSpecimenPath + "/" + docSdt.getTrStampDuty().getStampQr();
	}

	@Override
	public String getOnPremSource(TrDocumentD document, AuditContext audit) {
		return onPremStampSource + "/" + document.getDocumentId() + ".pdf";
	}

	@Override
	public String getIntegrationValue(TrDocumentD document, AuditContext audit) {
		AmGeneralsetting genset = daoFactory.getGeneralSettingDao().getGsObjByCodeAndTenant(AmGlobalKey.GENERALSETTING_DMS_USERNAME, document.getMsTenant());
		if (null == genset || StringUtils.isBlank(genset.getGsValue())) {
			return null;
		}
		return Base64.getEncoder().encodeToString(genset.getGsValue().getBytes());
	}

	@Override
	public boolean allDocumentsProcessed(List<TrDocumentD> documents) {
		if (CollectionUtils.isEmpty(documents)) {
			return true;
		}
		for (TrDocumentD document : documents) {
			LOG.info("Kontrak {}, Document {}, FINAL CHECK: Total stamped {} / {}, Current process: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), document.getTotalStamping(), document.getTotalMaterai(), document.getSdtProcess());
			if (!document.getTotalMaterai().equals(document.getTotalStamping())) {
				return false;
			}
			if (!GlobalVal.STEP_ATTACH_METERAI_SDT_FIN.equals(document.getSdtProcess())) {
				return false;
			}
		}
		return true;
	}
	
	@Override
	public int getFileCheckAttempts(AuditContext audit) {
		String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_STAMPED_DOC_CHECK_ATTEMPTS, audit);
		if (StringUtils.isBlank(gsValue)) {
			return STAMPED_DOC_CHECK_ATTEMPTS;
		}
		
		try {
			return Integer.valueOf(gsValue);
		} catch (Exception e) {
			return STAMPED_DOC_CHECK_ATTEMPTS;
		}
	}
	
	@Override
	public long getFileCheckDelay(AuditContext audit) {
		String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_STAMPED_DOC_CHECK_DELAY, audit);
		if (StringUtils.isBlank(gsValue)) {
			return STAMPED_DOC_CHECK_DELAY;
		}
		
		try {
			return Long.valueOf(gsValue);
		} catch (Exception e) {
			return STAMPED_DOC_CHECK_DELAY;
		}
	}

	@Override
	public String getPaymentReceiptToUpload(TrDocumentD document, AuditContext audit) {
		if (document.getTotalStamping() == 0) {
			byte[] documentByteArray = cloudStorageLogic.getStampingPaymentReceipt(document);
			return Base64.getEncoder().encodeToString(documentByteArray);
		}
		
		return getStampedDocumentFromOss(document, audit);
	}

}
