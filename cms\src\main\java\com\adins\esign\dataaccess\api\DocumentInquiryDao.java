package com.adins.esign.dataaccess.api;

import java.util.List;
import java.util.Map;

public interface DocumentInquiryDao {
    
    // Query untuk API /document/s/inquiry
    List<Map<String, Object>> getListInquiryDocument(String[] paramsInquiry, String callerId, String queryType);
    long countListInquiryDocument(String[] paramsInquiry, String callerId, String queryType);

    // Query untuk monitoring document normal, embed V1, embed V2
    List<Map<String, Object>> getListInquiryDocumentMonitoring(String[] paramsInquiry,boolean filterActiveStatus);
    long countListInquiryDocumentMonitoring(String[] paramsInquiry, boolean filterActiveStatus);

    // Query untuk inquiry inbox embed
    List<Map<String, Object>> getListInquiryDocumentInboxEmbed(String[] paramsInquiry, String callerId, boolean isReadOffice);
    long countListInquiryDocumentMonitoringInboxEmbed(String[] paramsInquiry, String callerId, boolean isReadOffice);
}
