package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.esign.model.MsBalancevendoroftenant;
import com.adins.esign.model.custom.BalanceVendoroftenantBean;

public interface BalanceVendoroftenantDao {
	List<BalanceVendoroftenantBean> getListBalanceVendoroftenant(String tenantCode, String vendorCode);
	MsBalancevendoroftenant getBalanceVendorOfTenantByBalanceTypeAndTenant(String balanceType, String tenantCode, String vendorCode);
}
