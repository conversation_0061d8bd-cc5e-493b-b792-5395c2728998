package com.adins.esign.businesslogic.api;
import java.util.Date;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
//import com.adins.am.model.AmUserAutofillData;
import com.adins.am.model.AmUserpwdhistory;
//import com.adins.esign.model.MsBranch;
//import com.adins.esign.model.MsDocumentTemplate;
//import com.adins.esign.model.MsJob;
import com.adins.esign.model.MsLov;
//import com.adins.esign.model.MsPsre;
//import com.adins.esign.model.MsPsreRegisteredUser;
//import com.adins.esign.model.MsSignLocation;
import com.adins.esign.model.MsTenant;
//import com.adins.esign.model.TrAgreement;
//import com.adins.esign.model.TrDocument;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.webservices.model.DocumentConfinsRequest;
import com.adins.esign.webservices.model.DocumentTemplateGetOneRequest;
import com.adins.esign.webservices.model.SignLocationRequest;

public interface TestSetUpLogic {	
//	MsJob setUpJob(String jobCode, String isBranch, MsJob parentJob);
//
//	MsBranch setUpBranch(String branchCode, String branchName, MsBranch parentBranch, MsTenant tenant);
//
//    AmMsuser setUpUser(String loginId, String fullName, MsJob job, MsBranch branch, AmMsuser userSpv, AmMssubsystem subsystem);
//    
//    AmUserpwdhistory setUpUserPwdhistory(Date date, AmMsuser user);
//    
//    AmUserAutofillData setUpUserAutofillData(AmMsuser user);
//    
//    AmUserAutofillData setUpUserAutofillDataWithoutZipCode(AmMsuser user);
//    
//    AmUsereventlog setUpUserEventLog(AmMsuser amMsuser, String activity, String consequence);
//    
//    UserBean setUpUserBean(AmMsuser user);
//    
//    MsPsreRegisteredUser setUpPsreRegisteredUser(AmMsuser user, MsPsre psre);

//	AmGeneralsetting setUpGenSet();
//
//	MsApikey setUpApikey(String key);

//	MsLov setUpLovByCode(String lovCode);
//	
//	MsLov setUpLovByGroupAndCode(String lovGroup, String lovCode);

//	byte[] loadFiletoByteArray(File fileUpload) throws IOException;
//	
//	void setUpUserPwdHistory(AmMsuser user, boolean isExpired);

//	AmGeneralsetting setUpGenSet(String gsCode);
//	
//	
//	MsDocumentTemplate setUpDocumentTemplate(String docTempCode, String docTempName, String isActive, MsTenant tenant);
//	
//	DocumentTemplateRequest setUpDocumentTemplateRequest(String docTempCode, String docTempName, String tenantCode);
//	
//	SignLocationRequest setUpSignLocationReqest(String docTempCode, String signerTypeCode, String signTypeCode);
//	
//	DocumentConfinsRequest setUpDocumentConfinsRequest(String docTempCode, String loginId, String branchCode);
//	
//	MsSignLocation setUpSignLocation(MsDocumentTemplate docTemp, MsLov signerType, MsLov signType);
//	
//	TrAgreement setUpTrAgreement(String referenceNo, AmMsuser user, MsBranch branch, MsTenant tenant,
//			int totalDocument, int totalSigned);
//
//	MsTenant setUpTenant(String tenantCode, String tenantName);
//
//	TrDocument setUpTrDocument(MsDocumentTemplate docTemp, TrAgreement agreement, MsTenant tenant);
}
