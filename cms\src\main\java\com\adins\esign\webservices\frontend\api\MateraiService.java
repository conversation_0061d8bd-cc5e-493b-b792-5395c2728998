package com.adins.esign.webservices.frontend.api;

import java.text.ParseException;

import com.adins.esign.webservices.model.CreateMateraiRequest;
import com.adins.esign.webservices.model.CreateMateraiResponse;
import com.adins.esign.webservices.model.TaxExcelReportRequest;
import com.adins.esign.webservices.model.TaxExcelReportResponse;
import com.adins.esign.webservices.model.InquiryStampDutyDetailRequest;
import com.adins.esign.webservices.model.InquiryStampDutyDetailResponse;
import com.adins.esign.webservices.model.InquiryStampDutyRequest;
import com.adins.esign.webservices.model.InquiryStampDutyResponse;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiEmbedRequest;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiRequest;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiResponse;
import com.adins.esign.webservices.model.ReverseTopupRequest;
import com.adins.esign.webservices.model.ReverseTopupResponse;
import com.adins.esign.webservices.model.StampDutyExcelReportRequest;
import com.adins.esign.webservices.model.StampDutyExcelReportResponse;

public interface MateraiService {
	CreateMateraiResponse createMaterai(CreateMateraiRequest request);
	InquiryStampDutyResponse getListStampDuty(InquiryStampDutyRequest request);
	InquiryStampDutyDetailResponse getStampDutyDetail(InquiryStampDutyDetailRequest request);
	InquiryStampDutyResponse getListReverseTopup(InquiryStampDutyRequest request);
	ReverseTopupResponse reverseTopupMaterai(ReverseTopupRequest request);
	StampDutyExcelReportResponse exportStampDutyReport(StampDutyExcelReportRequest request);
	TaxExcelReportResponse excelTaxReport(TaxExcelReportRequest request) throws ParseException;
	TaxExcelReportResponse excelTaxReportNormal(TaxExcelReportRequest request) throws ParseException;

	ListMonitoringEmeteraiResponse getListMonitoringEmeterai (ListMonitoringEmeteraiRequest request) throws ParseException;
	ListMonitoringEmeteraiResponse getListMonitoringEmeteraiEmbed(ListMonitoringEmeteraiEmbedRequest request) throws ParseException;
}
