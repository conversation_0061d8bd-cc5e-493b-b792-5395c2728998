package com.adins.am.model;


import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.adins.am.model.custom.ActiveAndUpdateableEntity;


@Entity
@Table(name = "oauth_access_token")
public class OauthAccessTokenDB implements java.io.Serializable {
	public static final String USERNAME_HBM = "username";
	
	private static final long serialVersionUID = 1L;
	
	private String tokenId;
	private byte[] token;
	private String authenticationId;
	private String username;
	private String clientId;
	private byte[] authentication;
	private String refreshToken;
	
	public OauthAccessTokenDB() {
	}
	
	public OauthAccessTokenDB(String tokenId, byte[] token, String authenticationId, String username, String clientId, byte[] authentication, String refreshToken) {
		this.token = token;
		this.authenticationId = authenticationId;
		this.username = username;
		this.clientId = clientId;
		this.authentication = authentication;
		this.refreshToken = refreshToken;
	}

	@Id @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "token_id", unique = true, nullable = false)
	public String getTokenId() {
		return tokenId;
	}
	public void setTokenId(String tokenId) {
		this.tokenId = tokenId;
	}
	
	@Column(name = "token", nullable = false)
	public byte[] getToken() {
		return token;
	}
	
	public void setToken(byte[] token) {
		this.token = token;
	}
	
	@Column(name = "authentication_id", nullable = false)
	public String getAuthenticationId() {
		return authenticationId;
	}
	
	public void setAuthenticationId(String authenticationId) {
		this.authenticationId = authenticationId;
	}
	
	@Column(name = "user_name", nullable = false)
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	
	@Column(name = "authentication", nullable = false)
	public byte[] getAuthentication() {
		return authentication;
	}
	public void setAuthentication(byte[] authentication) {
		this.authentication = authentication;
	}
	
	@Column(name = "refresh_token", nullable = false)
	public String getRefreshToken() {
		return refreshToken;
	}
	public void setRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken;
	}

	@Column(name = "client_id", nullable = false)
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}

	
}
