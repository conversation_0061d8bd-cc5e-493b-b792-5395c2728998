package com.adins.esign.util;

import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.MGF1ParameterSpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Date;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.Mac;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.OAEPParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class PrivyUtils {
	
	private static final String CONST_HMAC_SHA256 = "HmacSHA256";
	
	private PrivyUtils() {
		throw new IllegalStateException("Utility class");
	}
	
	public static String createSignature(Date timestamp, String privyUsername, String privyPassword, String json) throws NoSuchAlgorithmException, InvalidKeyException {
		
		JsonObject jsonObject = JsonParser.parseString(json).getAsJsonObject();
		jsonObject.getAsJsonObject().remove("ktp");
		jsonObject.getAsJsonObject().remove("identity");
		jsonObject.getAsJsonObject().remove("selfie");
		jsonObject.getAsJsonObject().remove("supporting_docs");
		jsonObject.getAsJsonObject().remove("document");
		String formattedJson = jsonObject.toString().replace(" ", "");
		
		String formattedTimestamp = MssTool.formatDateToStringIn(timestamp, "yyyy-MM-dd'T'HH:mm:ssXXX");
		String apiKey = privyUsername;
		String secretKey = privyPassword;
		String method = "POST";
		
		MessageDigest md = MessageDigest.getInstance("MD5");
		byte[] hash = md.digest(formattedJson.getBytes(StandardCharsets.UTF_8));
		String bodyMd5 = Base64.getEncoder().encodeToString(hash);
		
		String hmacSignature = formattedTimestamp + ":" + apiKey + ":" + method + ":" + bodyMd5;
		
		Mac mac = Mac.getInstance(CONST_HMAC_SHA256);
		SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), CONST_HMAC_SHA256);
		mac.init(secretKeySpec);
		byte[] hmacSha256Bytes = mac.doFinal(hmacSignature.getBytes(StandardCharsets.UTF_8));
		
		String hmacBase64 = Base64.getEncoder().encodeToString(hmacSha256Bytes);
		String authString = apiKey + ":" + hmacBase64;
		return Base64.getEncoder().encodeToString(authString.getBytes(StandardCharsets.UTF_8));
	}
	
	public static String createLivenessSignature(Date timestamp, String privyUsername, String privyPassword, String merchantKey, String json) throws NoSuchAlgorithmException, InvalidKeyException {
		
		String formattedTimestamp = MssTool.formatDateToStringIn(timestamp, "yyyy-MM-dd'T'HH:mm:ssZ");
		String username = privyUsername;
		String password = privyPassword;
		String method = "GET";
		
		String jsonBody = json;
		
		MessageDigest md = MessageDigest.getInstance("MD5");
		byte[] hash = md.digest(jsonBody.getBytes(StandardCharsets.UTF_8));
		String bodyMd5 = Base64.getEncoder().encodeToString(hash);
		
		String hmacSignature = formattedTimestamp + ":" + username + ":" + method + ":" + bodyMd5;
		
		Mac mac = Mac.getInstance(CONST_HMAC_SHA256);
		SecretKeySpec secretKeySpec = new SecretKeySpec(password.getBytes(StandardCharsets.UTF_8), CONST_HMAC_SHA256);
		mac.init(secretKeySpec);
		byte[] hmacSha256Bytes = mac.doFinal(hmacSignature.getBytes(StandardCharsets.UTF_8));
		String hmacBase64 = Base64.getEncoder().encodeToString(hmacSha256Bytes);
		
		String signature = "#" + merchantKey + ":#" + hmacBase64;
		return Base64.getEncoder().encodeToString(signature.getBytes(StandardCharsets.UTF_8));
		
	}

		public static String createLivenessV3Signature(String salt, String clientId, String clientSecret, String publicKey, String timestamp) throws NoSuchAlgorithmException, InvalidKeyException, InvalidKeySpecException, NoSuchPaddingException, IllegalBlockSizeException, BadPaddingException, InvalidAlgorithmParameterException {
		
		String requestBody = "";

		MessageDigest md5Digest = MessageDigest.getInstance("MD5");
		byte[] md5HashBytes = md5Digest.digest(requestBody.getBytes(StandardCharsets.UTF_8));
		String hashedBody = Base64.getEncoder().encodeToString(md5HashBytes);

		String hmacPlainString = String.format("%s:%s:%s:%s:%s", salt, timestamp, clientId, "GET", hashedBody);

		Mac hmacSha256 = Mac.getInstance(CONST_HMAC_SHA256);
		SecretKeySpec secretKeySpec = new SecretKeySpec(clientSecret.getBytes(StandardCharsets.UTF_8), CONST_HMAC_SHA256);
        hmacSha256.init(secretKeySpec);
        byte[] hmacBytes = hmacSha256.doFinal(hmacPlainString.getBytes(StandardCharsets.UTF_8));

		String hmacBase64 = Base64.getEncoder().encodeToString(hmacBytes);
		String hmacSignature = Base64.getEncoder().encodeToString(hmacBase64.getBytes(StandardCharsets.UTF_8));
		return encryptWithRSA(publicKey, hmacSignature);
	}

	private static String encryptWithRSA(String publicKeyString, String data) throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException, InvalidAlgorithmParameterException {
		
		String formattedKey = publicKeyString
			.replace("-----BEGIN PUBLIC KEY-----", "")
			.replace("-----END PUBLIC KEY-----", "")
			.replaceAll("\\s", "");
		
		byte[] encoded = Base64.getDecoder().decode(formattedKey);

		X509EncodedKeySpec keySpec = new X509EncodedKeySpec(encoded);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(keySpec);

		Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-512AndMGF1Padding");
		OAEPParameterSpec oaepParams = new OAEPParameterSpec(
            "SHA-512",
            "MGF1",
            MGF1ParameterSpec.SHA512,
            javax.crypto.spec.PSource.PSpecified.DEFAULT
        );
		cipher.init(Cipher.ENCRYPT_MODE, publicKey, oaepParams);
		
		byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
		return Base64.getEncoder().encodeToString(encryptedBytes);
	}
	
}
