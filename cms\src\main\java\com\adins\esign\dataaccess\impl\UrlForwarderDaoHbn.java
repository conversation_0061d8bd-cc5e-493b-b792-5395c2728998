package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.esign.dataaccess.api.UrlForwarderDao;
import com.adins.esign.model.MsEmailPattern;
import com.adins.esign.model.TrUrlForwarder;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class UrlForwarderDaoHbn extends BaseDaoHbn implements UrlForwarderDao {
	
	@Override
	public void insertUrlForwarder(TrUrlForwarder urlForwarder) {
		urlForwarder.setUsrCrt(MssTool.maskData(urlForwarder.getUsrCrt()));
		managerDAO.insert(urlForwarder);
	}

	@Override
	public TrUrlForwarder getUrlForwarderByCode(String urlCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("urlCode", urlCode);
		
		return managerDAO.selectOne(
				"from TrUrlForwarder uf "
				+ "where uf.urlCode = :urlCode ", params);
	}

	@Override
	public TrUrlForwarder getLatestUrlForwarder(MsEmailPattern emailPattern, AmMsuser user) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsEmailPattern.ID_EMAIL_PATTERN_HBM, emailPattern.getIdEmailPattern());
		params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_url_forwarder from tr_url_forwarder ")
			.append("where id_ms_email_pattern = :idEmailPattern ")
			.append("and id_ms_user = :idMsUser ")
			.append("order by id_url_forwarder desc limit 1");
		
		BigInteger idUrlForwarder = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idUrlForwarder) {
			return null;
		}
		
		return managerDAO.selectOne(
				"from TrUrlForwarder uf "
				+ "where uf.idUrlForwarder = :idUrlForwarder ", new Object[][] {{ "idUrlForwarder", idUrlForwarder.longValue() }});
	}

	@Override
	public void updateUrlForwarder(TrUrlForwarder urlForwarder) {
		urlForwarder.setUsrUpd(MssTool.maskData(urlForwarder.getUsrUpd()));
		managerDAO.update(urlForwarder);
	}	

}
