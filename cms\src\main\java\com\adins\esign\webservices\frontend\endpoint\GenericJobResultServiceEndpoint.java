package com.adins.esign.webservices.frontend.endpoint;

import java.text.ParseException;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.JobResultLogic;
import com.adins.esign.webservices.frontend.api.JobResultService;
import com.adins.esign.webservices.model.CancelJobRequest;
import com.adins.esign.webservices.model.CancelJobResponse;
import com.adins.esign.webservices.model.GetListJobRekonResultRequest;
import com.adins.esign.webservices.model.GetListJobRekonResultResponse;
import com.adins.esign.webservices.model.SubmitRekonJobResultRequest;
import com.adins.esign.webservices.model.SubmitRekonJobResultResponse;
import com.adins.esign.webservices.model.ViewRequestParamJobResultRequest;
import com.adins.esign.webservices.model.ViewRequestParamJobResultResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;


@Component
@Path("/jobResult")
@Api(value = "JobResultService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericJobResultServiceEndpoint implements JobResultService{

	@Autowired private JobResultLogic jobResultLogic;

	
	@Override
	@POST
	@Path("/s/submitResultRekon")
	public SubmitRekonJobResultResponse submitRekonJobResult(SubmitRekonJobResultRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return jobResultLogic.submitRekonJobResult(request, audit);
	}


	@Override
	@POST
	@Path("/s/cancel")
	public CancelJobResponse cancelJob(CancelJobRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return jobResultLogic.cancelJobResult(request, audit);
	}


	@Override
	@POST
	@Path("/s/getListJobRekonResult")
	public GetListJobRekonResultResponse getListJobRekonResultResponse(GetListJobRekonResultRequest request) throws ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return jobResultLogic.getListJobRekonResultResponse(request, audit);
	}

	@Override
	@POST
	@Path("/s/getRequestParamJobResult")
	public ViewRequestParamJobResultResponse getRequestParamJobResult(ViewRequestParamJobResultRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return jobResultLogic.getRequestParamJobResult(request, audit);
	}
}