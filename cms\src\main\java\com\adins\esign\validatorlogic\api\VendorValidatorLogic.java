package com.adins.esign.validatorlogic.api;

import com.adins.esign.model.MsVendor;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface VendorValidatorLogic {
	MsVendor validateGetOperatingDefaultVendor(String tenantCode, boolean checkDefaultVendorExistence, AuditContext audit);
	MsVendor validateGetMainDefaultVendor(String tenantCode, boolean checkDefaultVendorExistence, AuditContext audit);
	MsVendor validateGetVendor(String vendorCode, boolean checkVendorExistence, AuditContext audit);
	Ms<PERSON><PERSON>or validateVendorOfTenant(String vendorCode, String tenantCode, boolean checkVendorExistence, AuditContext audit);
	MsVendor validateGetVendorByStatusAndTenant(String vendorCode, String tenantCode, AuditContext audit);
}
