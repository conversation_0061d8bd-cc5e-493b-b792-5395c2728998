package com.adins.esign.businesslogic.impl;

import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.ManualReportLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrManualReport;
import com.adins.esign.model.custom.ListManualReportBean;
import com.adins.esign.model.custom.ListReportBean;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.webservices.model.AddManualReportRequest;
import com.adins.esign.webservices.model.DeleteManualReportRequest;
import com.adins.esign.webservices.model.DownloadManualReportRequest;
import com.adins.esign.webservices.model.DownloadManualReportResponse;
import com.adins.esign.webservices.model.GetListManualReportRequest;
import com.adins.esign.webservices.model.GetListManualReportResponse;
import com.adins.exceptions.AutosignException;
import com.adins.esign.webservices.model.GetListReportRequest;
import com.adins.esign.webservices.model.GetListReportResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.AutosignException.ReasonAutosign;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import net.bull.javamelody.internal.common.LOG;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Transactional
@Component
public class GenericManualReportLogic extends BaseLogic implements ManualReportLogic {

	@Autowired TenantValidatorLogic tenantValidatorLogic;
	@Autowired CommonValidatorLogic commonValidatorLogic;
	@Autowired CommonLogic commonLogic;
	@Autowired CloudStorageLogic cloudStorageLogic;
	
	private static final String REPORT_TYPE = "Report Type";
	private static final String FILE_NAME = "File Name";
	
	@Override
	public GetListManualReportResponse getList(GetListManualReportRequest request, AuditContext audit) {
		
		if (StringUtils.isNotBlank(request.getTenantCode())) {
			tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		}
		
		if (StringUtils.isNotBlank(request.getReportType())) {
			MsLov reportType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_REPORT_TYPE, request.getReportType());
			commonValidatorLogic.validateNotNull(reportType, getMessage("businesslogic.global.datanotfound", new Object[] {REPORT_TYPE, request.getReportType()}, audit), StatusCode.LOV_CODE_INVALID);
		}
		
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		
		if (StringUtils.isNotBlank(request.getReportDateStart())) {
			Date start;
			Date end;
			try {
				start = sdf.parse(request.getReportDateStart());
			} catch (ParseException e) {
				throw new CommonException(this.messageSource.getMessage("businesslogic.error.parsedateformat",
						null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_FORMAT);
			}
			
			if (StringUtils.isBlank(request.getReportDateEnd())) {
				throw new CommonException(this.messageSource.getMessage("businesslogic.global.daterangefiltermustbefilled",
						null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_RANGE);
			} else {
				try {
					end = sdf.parse(request.getReportDateEnd());
				} catch (ParseException e) {
					throw new CommonException(this.messageSource.getMessage("businesslogic.error.parsedateformat",
							null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_FORMAT);
				}
			}
			
			if (start.compareTo(end) > 0) {
				throw new CommonException(this.messageSource.getMessage("businesslogic.error.invaliddatevalue",
						null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_RANGE);
			}
			
		} else if (StringUtils.isNotBlank(request.getReportDateEnd())) {
			throw new CommonException(this.messageSource.getMessage("businesslogic.global.daterangefiltermustbefilled",
					null, this.retrieveLocaleAudit(audit)), ReasonCommon.INVALID_DATE_RANGE);
		}
		
		GetListManualReportResponse response = new GetListManualReportResponse();
		List<ListManualReportBean> reports = null;
		
		String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_MAX_ROW, audit);
		Integer rowPerPage = Integer.parseInt(gsValue);

		int start = ((request.getPage() - 1) * rowPerPage) + 1;
		int end = request.getPage() * rowPerPage;
		
		BigInteger totalResult = daoFactory.getManualReportDao().countListManualReport(request);
		long total = totalResult.longValue();
		long totalPage = (total % rowPerPage == 0) ? total / rowPerPage : (total / rowPerPage) + 1;
		
		reports = daoFactory.getManualReportDao().getListManualReport(request, start, end);
		
		response.setPage(request.getPage());
		response.setReports(reports);
		response.setTotalPage((int) totalPage);
		response.setTotalResult((int) total);
		
		return response;
	}
	
	@Override
	public MssResponseType saveManualReport(AddManualReportRequest request, AuditContext audit) {
		byte[] decodedBytes;
		String messageValidation = "";
		Date reportDate;
		
		MsTenant msTenant =  tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		MsLov lov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_REPORT_TYPE, request.getReportType());
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"Report Date"}, audit);
		commonValidatorLogic.validateNotNull(request.getReportDate(), messageValidation, StatusCode.MANDATORY_PARAMETER);
		
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {REPORT_TYPE}, audit);
		commonValidatorLogic.validateNotNull(request.getReportType(), messageValidation, StatusCode.MANDATORY_PARAMETER);
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1 , new String[] {REPORT_TYPE}, audit);
		commonValidatorLogic.validateNotNull(lov, messageValidation, StatusCode.MANDATORY_PARAMETER);
		
		
		reportDate = isDateValid(request.getReportDate(), true, audit);
	
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {FILE_NAME}, audit);
		commonValidatorLogic.validateNotNull(request.getFileName(), messageValidation, StatusCode.EMPTYFILENAME);
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"File Base64"}, audit);
		commonValidatorLogic.validateNotNull(request.getBase64File(), messageValidation, StatusCode.MANDATORY_PARAMETER);
		
		TrManualReport trManualReportExisting = daoFactory.getManualReportDao().getManualReportByTenantCodeAndFileName(msTenant.getTenantCode(),request.getFileName());
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATAISEXIST, new String[] {request.getFileName()}, audit);
		commonValidatorLogic.validateMustNull(trManualReportExisting, messageValidation, StatusCode.DUPLICATE_REQUEST);
		
		try {
	        decodedBytes = Base64.getDecoder().decode(request.getBase64File());
		} catch (Exception e) {
			throw new AutosignException(this.messageSource.getMessage("businesslogic.autosign.base64notvalid", null, this.retrieveLocaleAudit(audit)), ReasonAutosign.BASE64NOTVALID);
		}
		
		TrManualReport trManualReport = new TrManualReport();
		trManualReport.setMsLov(lov);
		trManualReport.setMsTenant(msTenant);
		trManualReport.setReportDate(reportDate);
		trManualReport.setFileName(request.getFileName());
		trManualReport.setDtmCrt(new Date());
		trManualReport.setUsrCrt(audit.getCallerId());
		daoFactory.getManualReportDao().insertTrManualReport(trManualReport);
		String filePath = "";
		filePath = cloudStorageLogic.storeManualReportUpload(msTenant.getTenantCode(), request.getFileName(), decodedBytes);
		
		LOG.info("File Berhasil di upload dengan path " + filePath);

		return new MssResponseType();
	}
	
	
	private Date isDateValid(String date,  boolean objectMustNotEmpty, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		Date response = new Date();
		if (StringUtils.isBlank(date) && objectMustNotEmpty) {
			return response ;
		}
		
		try {
			response =sdf.parse(date);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		
		return response ;
		
		
	}

	@Override
	public GetListReportResponse getListForDownload(GetListReportRequest request, AuditContext audit) {

		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);

		int totalResult = daoFactory.getDocumentDao().countTrManualReport(request);
		double totalPage = Math.ceil((double) totalResult / maxRow);

		List<ListReportBean> reportBean2 = daoFactory.getDocumentDao().getReportyByIdTenantPage(request.getTenantCode(),
				min, max);

		GetListReportResponse response = new GetListReportResponse();
		Status status = new Status();

		status.setCode(0);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalResult);
		response.setStatus(status);
		response.setListReport(reportBean2);
		return response;
	}

	@Override
	public DownloadManualReportResponse download(DownloadManualReportRequest request, AuditContext audit) {

		TrManualReport doc = daoFactory.getDocumentDao().getDocReportById(request.getIdManualReport());
		if (doc == null) {
			throw new DocumentException(getMessage("businesslogic.document.docnotfounds", null, audit),
					ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
		}

		byte[] documentByteArray = cloudStorageLogic.getManualReport(doc);
		if (ArrayUtils.isEmpty(documentByteArray)) {
			throw new DocumentException(getMessage("businesslogic.document.docnotfounds", null, audit),
					ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
		}

		Status status = new Status();
		status.setCode(0);

		DownloadManualReportResponse response = new DownloadManualReportResponse();
		response.setXlBase64(Base64.getEncoder().encodeToString(documentByteArray));
		response.setStatus(status);
		return response;
	}
	
	@Override
	public MssResponseType deleteManualReport(DeleteManualReportRequest request , AuditContext audit ) {
		MsTenant msTenant =  tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		String messageValidation = "";
	
	
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {FILE_NAME}, audit);
		commonValidatorLogic.validateNotNull(request.getFileName(), messageValidation, StatusCode.EMPTYFILENAME);
		
		TrManualReport trManualReport = daoFactory.getManualReportDao().getManualReportByTenantCodeAndFileName(msTenant.getTenantCode(), request.getFileName());
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_DATANOTFOUND, new String[] {FILE_NAME,request.getFileName()}, audit);
		commonValidatorLogic.validateNotNull(trManualReport, messageValidation, StatusCode.ENTITY_NOT_FOUND);

		
		cloudStorageLogic.deleteManualReport(msTenant.getTenantCode(), request.getFileName());
		
		daoFactory.getManualReportDao().deleteTrManualReport(trManualReport);
		
		return new MssResponseType();
	}
}
