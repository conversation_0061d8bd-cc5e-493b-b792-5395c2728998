package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMenuofrole;
import com.adins.am.model.AmMsmenu;
import com.adins.am.model.AmMsrole;
import com.adins.esign.dataaccess.api.MenuDao;
import com.adins.esign.model.custom.ListMenuBean;
import com.adins.esign.model.custom.MenuUserBean;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class MenuDaoHbn extends BaseDaoHbn implements MenuDao{

	@Override
	public List<MenuUserBean> getMenuByRoleAndTenant(String roleCode, String tenantCode) {
		StringBuilder query = new StringBuilder();
		
		query
			.append(" select mm.menu_code as code, mm.menu_order as order, mm.menu_prompt as prompt, ")
			.append(" mm.path as path, mm.icon as icon, mm.css as css, mm.is_hidden as \"isHidden\", ")
			.append(" mm.is_external_link as \"isExternalLink\", mm.params as params, parent.menu_code as \"parentCode\" ")
			.append(" from am_msmenu mm ") 
			.append(" left join am_msmenu parent on parent.id_ms_menu = mm.id_parent_ms_menu ")
			.append(" join am_menuofrole mor on mm.id_ms_menu = mor.id_ms_menu ") 
			.append(" join am_msrole mr on mor.id_ms_role = mr.id_ms_role ") 
			.append(" join ms_tenant mt on mr.id_ms_tenant = mt.id_ms_tenant ")
			.append(" where mt.tenant_code = :tenantCode and mm.is_active = '1' ") 
			.append(" and mr.role_code = :roleCode and mr.is_active = '1' and mr.is_deleted = '0' ");
		
		Map<String, Object> params = new HashMap<>();
		params.put("roleCode", roleCode);
		params.put("tenantCode", tenantCode);
		
		return this.managerDAO.selectForListString(MenuUserBean.class, query.toString(), params, null);		
	}
	
	@Override
	public List<AmMsmenu> getMenuByIdRole(long idRole) {
		if (0 == idRole)
			return Collections.emptyList() ;

		StringBuilder query = new StringBuilder();		
		query.append(" SELECT menu.id_ms_menu, menu.is_active, menu.menu_code, menu.menu_order, menu.menu_prompt, ")
				.append(" menu.path, menu.id_parent_ms_menu, menu.icon, menu.css, menu.is_hidden, ")
				.append(" menu.is_external_link, menu.params ")
				.append(" FROM am_msmenu menu ")
				.append(" JOIN am_menuofrole mr ON mr.id_ms_menu = menu.id_ms_menu ")
				.append(" JOIN am_msrole role ON role.id_ms_role = mr.id_ms_role ")
				.append(" WHERE menu.is_active ='1' AND role.is_active ='1' AND role.id_ms_role = :idRole ");
		 List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(),
				new Object[][] {{"idRole", idRole}});
		 
		 List<AmMsmenu> menuList = new ArrayList<>();
		 for (Map<String, Object> resultMap : result) {
			 AmMsmenu menu= new AmMsmenu();
			 menu.setIdMsMenu(((BigInteger)resultMap.get("d0")).longValue());
			 menu.setIsActive((String) resultMap.get("d1"));
			 menu.setMenuCode((String) resultMap.get("d2"));
			 menu.setMenuOrder((Integer) resultMap.get("d3"));
			 menu.setMenuPrompt((String) resultMap.get("d4"));
			 menu.setPath((String) resultMap.get("d5"));
			 menu.setIdParentMsMenu((null != resultMap.get("d6")? ((BigInteger)resultMap.get("d6")).longValue() : null));
			 menu.setIcon((String) resultMap.get("d7"));
			 menu.setCss((String) resultMap.get("d8"));
			 menu.setIsHidden((String) resultMap.get("d9"));
			 menu.setIsExternalLink((String) resultMap.get("d10"));
			 menu.setParams((String) resultMap.get("d11"));

			 menuList.add(menu);
		 }
		 
		 return menuList;
	}

	@Override
	public void insertMenuOfRole(AmMenuofrole menuOfRole) {
		menuOfRole.setUsrCrt(MssTool.maskData(menuOfRole.getUsrCrt()));
		this.managerDAO.insert(menuOfRole);
	}

	@Override
	public AmMsmenu getMenuByCode(String menuCode) {
		return this.managerDAO.selectOne(AmMsmenu.class, 
				new Object[][] {{ Restrictions.eq(AmMsmenu.MENU_CODE_HBM, StringUtils.upperCase(menuCode)) }});
	}

	@Override
	public AmMenuofrole getMenuofrole(AmMsmenu menu, AmMsrole role) {
		Map<String, Object> params = new HashMap<>();
		params.put("menu", menu);
		params.put("role", role);
		
		return managerDAO.selectOne(
				"from AmMenuofrole mor "
				+ "join fetch mor.amMsmenu "
				+ "join fetch mor.amMsrole "
				+ "where mor.amMsmenu = :menu "
				+ "and mor.amMsrole = :role ", params);
	}

	@Override
	public void deleteMenuOfRoleByIdRole(long idRole) {
		Map<String, Object> params = new HashMap<>();
		params.put("idMsRole", idRole);
		
		StringBuilder query = new StringBuilder();
		query.append("delete from am_menuofrole ")
			 .append("where id_ms_role = :idMsRole ");
		
		managerDAO.deleteNativeString(query.toString(), params);
	}

	@Override
	public AmMsmenu getMenuById(long idMsMenu) {
		return this.managerDAO.selectOne(AmMsmenu.class, 
				new Object[][] {{ Restrictions.eq(AmMsmenu.ID_MS_MENU_HBM, idMsMenu) }});
	}

	@Override
	public List<ListMenuBean> getListManageableMenu() {
		StringBuilder query = new StringBuilder();
		query.append("select menu_prompt as \"prompt\", menu_code as \"code\" from am_msmenu am ")
			 .append("where \"path\" is not null and is_active = '1' and is_manageable = '1' ")
			 .append("order by am.menu_prompt ");
		
		return this.managerDAO.selectForListString(ListMenuBean.class, query.toString(), new Object[][] {}, null);	
	}

	@Override
	public List<ListMenuBean> getListMenuOfRole(long idRole) {
		Map<String, Object> params = new HashMap<>();
		params.put("idMsRole", idRole);
		
		StringBuilder query = new StringBuilder();
		query.append("select menu_prompt as \"prompt\", menu_code as \"code\" from am_msmenu am ")
			 .append("join am_menuofrole am2 on am2.id_ms_menu = am.id_ms_menu ")
		 	 .append("where \"path\" is not null and is_active = '1' and is_hidden = '0' ")
		 	 .append("and is_manageable = '1' and id_ms_role = :idMsRole ")
		 	 .append("order by am.menu_prompt ");
		
		return this.managerDAO.selectForListString(ListMenuBean.class, query.toString(), params, null);
	}	

}
