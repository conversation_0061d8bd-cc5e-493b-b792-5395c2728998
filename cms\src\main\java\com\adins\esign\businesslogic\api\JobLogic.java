package com.adins.esign.businesslogic.api;

import com.adins.esign.webservices.model.GetListJobByJobProcessTypeRequest;
import com.adins.esign.webservices.model.GetListJobRequest;
import com.adins.esign.webservices.model.GetListJobResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface JobLogic {
	GetListJobResponse getListJobByJobType(GetListJobRequest request, AuditContext audit);
	GetListJobResponse getListJobByJobProcessType(GetListJobByJobProcessTypeRequest request, AuditContext audit);
}
