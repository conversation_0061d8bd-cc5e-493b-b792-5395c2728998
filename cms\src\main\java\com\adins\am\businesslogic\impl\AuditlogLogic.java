package com.adins.am.businesslogic.impl;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmAuditlog;
import com.adins.framework.persistence.dao.api.IAuditable;
import com.adins.framework.persistence.dao.api.ManagerDAO;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class AuditlogLogic extends BaseLogic implements IAuditable {

	@Autowired
	private ManagerDAO managerDAO;
	
	@Override
	public void insertAuditLog(String activity, String tableName, String fieldName, String oldValue, String newValue, String keyValue) {
		long idAmMsuser = 0;
		String screenid = null;
		String terminaladdress = null;
		String terminalid = null;
		String terminaluserid = null;
		
		if (null != AuditContext.getContext()) {
			AuditContext auditContext = AuditContext.getContext();
			
			idAmMsuser = NumberUtils.toLong(auditContext.getCallerId());
			screenid = (String) auditContext.getParameters().get(AuditContext.KEY_PARAMS_SCREENID);
			terminaladdress = (String) auditContext.getParameters().get(AuditContext.KEY_PARAMS_TERMINALADDRESS);
			terminalid = (String) auditContext.getParameters().get(AuditContext.KEY_PARAMS_TERMINALID);
			terminaluserid = (String) auditContext.getParameters().get(AuditContext.KEY_PARAMS_TERMINALUSERID);
		}
				
		AmAuditlog amAuditlog = new AmAuditlog();
		amAuditlog.setActivityDate(new Date());
		amAuditlog.setActivity(activity);
		amAuditlog.setTableName(tableName);
		amAuditlog.setFieldName(fieldName);
		amAuditlog.setOldValue(StringUtils.left(oldValue, 4_000));
		amAuditlog.setNewValue(StringUtils.left(newValue, 4_000));
		amAuditlog.setKeyValue(keyValue);

		amAuditlog.setAmMsuser(daoFactory.getUserDao().getUserByIdMsUser(idAmMsuser));
		amAuditlog.setScreenId(screenid);
		amAuditlog.setTerminalAddress(terminaladdress);
		amAuditlog.setTerminalId(terminalid);
		amAuditlog.setTerminalUserId(terminaluserid);

		this.managerDAO.insert(amAuditlog);	
	}

}
