package com.adins.esign.validatorlogic.api;

import java.util.List;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.webservices.model.StampDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.external.InsertStampingMateraiExternalRequest;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface DocumentValidatorLogic {
	void validateAgreementForRetryStamp(TrDocumentH documentH, AuditContext audit);
	void validateAgreementForStartStamp(TrDocumentH documentH, MsTenant tenant, AuditContext audit);
	void validateStampDocumentRequest(StampDocumentRequest request, AuditContext audit);
	void validateViewDocumentRequest(ViewDocumentRequest request, AuditContext audit);
	void validateViewDocumentEmbedRequest(ViewDocumentRequest request, AuditContext audit);
	
	/**
	 * 
	 * @return <code>true</code> if the user sign sequence is valid<br><code>false</code> if the user sign sequence invalid
	 */
	boolean isSignSequenceValid(TrDocumentD document, AmMsuser user, AuditContext audit);
	void validateSignSequence(TrDocumentD document, AmMsuser user, AuditContext audit);
	
	boolean isDocumentHSignCompleted(TrDocumentH documentH);
	void validateRefNumber(String refNumber, AuditContext audit);
	void validateAgreementForStartStampEmbed(TrDocumentH documentH, MsTenant tenant, AuditContext audit);
	void validateRequestForExternalMateraiStamping(InsertStampingMateraiExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit);
	
	void validateDocumentsPrioritySequence(List<TrDocumentD> documents, AmMsuser user, AuditContext audit);
	boolean isDocumentTopPriorityForSigner(TrDocumentD document, AmMsuser user);
}
