package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class AttachMeteraiPrivyJob extends BaseLogic {
	private static final Logger LOG = LoggerFactory.getLogger(AttachMeteraiPrivyJob.class);
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void runAttachMeteraiPrivy() {
		try {
			LOG.info("Attach Meterai Privy job started");
			AuditContext audit = new AuditContext("ATTACH METERAI PRIVY SCHEDULER");
			schedulerLogic.attachMeteraiPrivy(audit);
			LOG.info("Attach Meterai job finished");
		} catch (Exception e) {
			LOG.error("Error on running Attach Meterai Privy job", e);
		}
	}
}
