package com.adins.esign.util;

import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class VidaUtils {
	
	private VidaUtils() {
		throw new IllegalStateException("Utility class");
	}
	
	private static byte[] hexStringToByteArray(String s) {
		int len = s.length();
		byte[] data = new byte[len / 2];
		for (int i = 0; i < len; i += 2) {
			data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4) + Character.digit(s.charAt(i+1), 16));
		}
		return data;
	}
	
	private static byte[] leftPad(byte[] data, int size, byte pad) {
		if (size <= data.length) {
			return data;
		}
		
		byte[] newData = new byte[size];
		for (int i = 0; i < size; i++) {
			newData[i] = pad;
		}
		for (int i = 0; i < data.length; i++) {
			newData[size - i - 1] = data[data.length - i - 1];
		}
		
		return newData;
    }
	
	public static String encrytDataAes(String plainText, String secretKey, String apiKey) throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
		byte[] key = hexStringToByteArray(secretKey);
		byte[] bytesIV = apiKey.getBytes();
		byte[] inputArr = plainText.getBytes(StandardCharsets.UTF_8);
		Cipher cipher = Cipher.getInstance("AES/CFB/NoPadding");
		int sizePad = cipher.getBlockSize() - (inputArr.length % cipher.getBlockSize());
		byte[] paddArr =leftPad(inputArr,16-sizePad, (byte) 0x00);
		/* KEY + IV setting */
		IvParameterSpec iv = new IvParameterSpec(bytesIV);
		SecretKeySpec skeySpec = new SecretKeySpec(key, "AES");
		/* Ciphering */
		cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
		byte[] encryptedBytes = cipher.doFinal(paddArr);
		return Base64.getUrlEncoder().encodeToString(encryptedBytes);
	}
	
	public static String decryptAes(String encrytedData, String myKey, String iVstring) throws InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InvalidKeyException {
		byte[] key = hexStringToByteArray(myKey);
		byte[] bytesIV = iVstring.getBytes();
		byte[] inputArr = Base64.getUrlDecoder().decode(encrytedData);
		
		SecretKeySpec skSpec = new SecretKeySpec(key, "AES");
		Cipher cipher = Cipher.getInstance("AES/CFB/NoPadding");
		int blockSize = cipher.getBlockSize();
		IvParameterSpec iv = new IvParameterSpec(bytesIV);
		byte[] dataToDecrypt;
		if (inputArr.length > 16) {
			dataToDecrypt = Arrays.copyOfRange(inputArr, blockSize, inputArr.length);
		} else {
			dataToDecrypt =inputArr;
		}
		cipher.init(Cipher.DECRYPT_MODE, skSpec, iv);
		byte[] result = cipher.doFinal(dataToDecrypt);
		return new String(result, StandardCharsets.UTF_8);
	}
}
