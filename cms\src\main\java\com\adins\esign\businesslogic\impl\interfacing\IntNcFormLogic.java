package com.adins.esign.businesslogic.impl.interfacing;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.DecimalFormat;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.interfacing.IntFormLogic;
import com.adins.esign.confins.model.AuthenticateUserRequest;
import com.adins.esign.constants.GlobalKey;
import com.adins.exceptions.LoginException;
import com.adins.exceptions.LoginException.Reason;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
@Component
@Scope(proxyMode = ScopedProxyMode.TARGET_CLASS)
//ScopedProxyMode untuk mendukung @Autowired pada class yang butuh, jika tidak pakai ini, butuh get manual dari applicationContext
public class IntNcFormLogic extends BaseLogic implements IntFormLogic {

	private Gson gson = new GsonBuilder().serializeNulls().create();
	private static final Logger LOG = LoggerFactory.getLogger(IntNcFormLogic.class);
	
	private static final String PATH_NC_AUTH_USER		= "/UserManagement/AuthenticateUser";
	
	DecimalFormat collCurrencyFormat = new DecimalFormat("###,##0.00");
		
	//Authenticate User
	@Override
	public boolean authenticateUser(AmMsuser amMsUser, String password) {
		AmGeneralsetting uriNCGenset = daoFactory.getGeneralSettingDao().getGsObjByCode(GlobalKey.NC_URI);

		LOG.info("Begin Authenticat User with userID : {}",amMsUser.getLoginId());
		WebClient client = WebClient.create(uriNCGenset.getGsValue())
                .accept(MediaType.APPLICATION_JSON).type(MediaType.APPLICATION_JSON)
                .path(PATH_NC_AUTH_USER);
		
		AuthenticateUserRequest bean = new AuthenticateUserRequest();
		bean.setUserId(amMsUser.getLoginId());
		bean.setPassword(password);
		
		String json = gson.toJson(bean, AuthenticateUserRequest.class);
		LOG.info("JSON AUTHENTICATE USER : {}", json);
		Response response = client.post(json);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String result = null;
		try {
			result = IOUtils.toString(isReader);
		}
		catch(Exception e) {
			throw new RuntimeException(e);
		}
		
		boolean canLogin = false;
		if (response.getStatus() == Response.Status.OK.getStatusCode()) {
			result = result.substring(1, result.length()-1);
			LOG.info("USER ID : {}, Success AUTHENTICATE USER with return : {}",
					amMsUser.getLoginId(), result);
			if ("1".equals(result))
				canLogin = true;
		}
		else {
			throw new LoginException("Error while AUTHENTICATE USER", Reason.LOGIN_INVALID);
		}

		return canLogin;
	}
	
}