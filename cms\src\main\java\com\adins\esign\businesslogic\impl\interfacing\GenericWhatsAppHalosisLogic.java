package com.adins.esign.businesslogic.impl.interfacing;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.MessageDeliveryReportLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrMessageDeliveryReport;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.halosis.HalosisCredentialBean;
import com.adins.esign.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esign.model.custom.halosis.HalosisTokenBean;
import com.adins.esign.model.custom.halosis.HalosisWhatsAppTemplate;
import com.adins.esign.model.custom.halosis.HalosisWhatsAppTemplateComponent;
import com.adins.esign.model.custom.halosis.HalosisWhatsAppTemplateComponentParameter;
import com.adins.esign.model.custom.halosis.HalosisWhatsAppTemplateLanguage;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.halosis.HalosisAccessTokenRequest;
import com.adins.esign.webservices.model.halosis.HalosisAccessTokenResponse;
import com.adins.esign.webservices.model.halosis.HalosisLoginRequest;
import com.adins.esign.webservices.model.halosis.HalosisLoginResponse;
import com.adins.esign.webservices.model.halosis.HalosisSendWhatsAppRequest;
import com.adins.esign.webservices.model.halosis.HalosisSendWhatsAppResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.google.gson.Gson;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
public class GenericWhatsAppHalosisLogic extends BaseLogic implements WhatsAppHalosisLogic {
	
	@Value("${halosis.base.url}") private String baseUrl;
	@Value("${halosis.login.url}") private String loginUrl;
	@Value("${halosis.accesstoken.url}") private String accessTokenUrl;
	@Value("${halosis.sendwa.url}") private String sendWaUrl;
	@Value("${halosis.cred.email}") private String halosisEmail;
	@Value("${halosis.cred.password}") private String halosisPassword;
	
	@Value("${jatis.sendwa.template.suffix}") private String templateSuffix;
	
	@Autowired private Gson gson;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private MessageDeliveryReportLogic messageDeliveryReportLogic;
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericWhatsAppHalosisLogic.class);
	private static Map<String, HalosisTokenBean> tokens = new HashMap<>();
	
	private static final String SEND_WA_TO_NOTE = "Sending WhatsApp to ";
	private static final String FAIL_SEND_WA_LOG = "Failed to send Halosis WhatsApp: {}";
	
	@Override
	@Async
	public void sendMessage(HalosisSendWhatsAppRequestBean request, AuditContext audit) {
		MsTenant tenant = request.getMsTenant();
		MsMsgTemplate template = request.getTemplate();
		String phone = request.getPhoneNumber();
		List<String> headerTexts = request.getHeaderTexts();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();
				
		HalosisCredentialBean credential = getTenantCredential(tenant);
		boolean isSuccessful = true;
		String vendorTrxNo = null;
		try {
			String token = getTenantAuthorizationToken(tenant, credential);
			HalosisSendWhatsAppResponse waResponse = callSendWhatsApp(tenant, template, phone, token, headerTexts, bodyTexts, buttonText);
			isSuccessful = GlobalVal.WA_STATUS_SUCESS.equals(waResponse.getStatus());
			vendorTrxNo = waResponse.getWamId();
		} catch (Exception e) {
			LOG.error(FAIL_SEND_WA_LOG, e.getLocalizedMessage(), e);
			isSuccessful = false;
		}
		
		String balanceTypeCode = request.isOtp() ? GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP : GlobalVal.CODE_LOV_BALANCE_TYPE_WA;
		String trxTypeCode = request.isOtp() ? GlobalVal.CODE_LOV_TRX_TYPE_UWA_OTP : GlobalVal.CODE_LOV_TRX_TYPE_UWA;
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceTypeCode);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, trxTypeCode);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
		
		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(request.getReservedTrxNo());
		mutation.setVendorTrxNo(vendorTrxNo);
		mutation.setTrxDate(new Date());
		mutation.setQty(isSuccessful ? -1 : 0);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setUsrCrt(audit.getCallerId());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(tenant);
		mutation.setMsVendor(vendor);
		
		if (null != request.getAmMsuser()) {
			mutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			mutation.setTrDocumentH(request.getTrDocumentH());
			mutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			mutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			mutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isBlank(mutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
			mutation.setRefNo(request.getRefNo());
		}
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = SEND_WA_TO_NOTE + phone;
		} else {
			notes = request.getNotes();
		}
		mutation.setNotes(notes);
		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
	}

	@Override
	public boolean sendSynchronousMessage(HalosisSendWhatsAppRequestBean request, AuditContext audit) {
		MsTenant tenant = request.getMsTenant();
		MsMsgTemplate template = request.getTemplate();
		String phone = request.getPhoneNumber();
		List<String> headerTexts = request.getHeaderTexts();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();
				
		HalosisCredentialBean credential = getTenantCredential(tenant);
		boolean isSuccessful = true;
		String vendorTrxNo = null;
		try {
			String token = getTenantAuthorizationToken(tenant, credential);
			HalosisSendWhatsAppResponse waResponse = callSendWhatsApp(tenant, template, phone, token, headerTexts, bodyTexts, buttonText);
			isSuccessful = GlobalVal.WA_STATUS_SUCESS.equals(waResponse.getStatus());
			vendorTrxNo = waResponse.getWamId();
		} catch (Exception e) {
			LOG.error(FAIL_SEND_WA_LOG, e.getLocalizedMessage(), e);
			isSuccessful = false;
		}
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_WA);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UWA);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_SEND_DOCUMENT);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);
		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(request.getReservedTrxNo());
		mutation.setVendorTrxNo(vendorTrxNo);
		mutation.setTrxDate(new Date());
		mutation.setQty(isSuccessful ? -1 : 0);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setUsrCrt(audit.getCallerId());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(tenant);
		mutation.setMsVendor(vendor);
		
		if (null != request.getAmMsuser()) {
			mutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			mutation.setTrDocumentH(request.getTrDocumentH());
			mutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			mutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			mutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isBlank(mutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
			mutation.setRefNo(request.getRefNo());
		}
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = SEND_WA_TO_NOTE + phone;
		} else {
			notes = request.getNotes();
		}
		mutation.setNotes(notes);
		daoFactory.getBalanceMutationDao().insertTrBalanceMutation(mutation);

		if(isSuccessful){
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, request.getReservedTrxNo(), vendorTrxNo,
					phone, NotificationType.WHATSAPP_HALOSIS, messageGateway, sendingPoint, audit);
		}

		return isSuccessful;
	}
	
	
	private HalosisCredentialBean getTenantCredential(MsTenant tenant) {
		MsTenantSettings emailSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "HALOSIS_EMAIL");
		if (emailSettings == null) {
			return getApplicationCredential(tenant);
		}
		
		MsTenantSettings passwordSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "HALOSIS_PASSWORD");
		if (passwordSettings == null) {
			return getApplicationCredential(tenant);
		}
		
		LOG.debug("Tenant {}, uses Halosis credential from tenant settings", tenant.getTenantCode());
		return new HalosisCredentialBean(emailSettings.getSettingValue(), passwordSettings.getSettingValue());
	}
	
	private HalosisCredentialBean getApplicationCredential(MsTenant tenant) {
		LOG.debug("Tenant {}, uses Halosis credential from application", tenant.getTenantCode());
		return new HalosisCredentialBean(halosisEmail, halosisPassword);
	}
	
	private String getTenantAuthorizationToken(MsTenant tenant, HalosisCredentialBean credential) throws IOException {
		HalosisTokenBean tokenBean = tokens.get(tenant.getTenantCode());
		if (null == tokenBean) {
			return generateNewTenantToken(tenant, credential);
		}
		
		boolean isExpired = isTokenExpired(tokenBean);
		if (isExpired) {
			return generateNewTenantToken(tenant, credential);
		}
		
		LOG.debug("Tenant {}, uses token from memory", tenant.getTenantCode());
		return tokenBean.getToken();
	}
	
	private String generateNewTenantToken(MsTenant tenant, HalosisCredentialBean credential) throws IOException {
		LOG.debug("Tenant {}, generating new token", tenant.getTenantCode());
		HalosisLoginResponse loginResponse = loginHalosis(credential.getEmail(), credential.getPassword());
		String refreshToken = loginResponse.getRefreshToken();
		HalosisAccessTokenResponse accessTokenResponse = getAccessToken(refreshToken);
		
		HalosisTokenBean tokenBean = new HalosisTokenBean();
		tokenBean.setToken(accessTokenResponse.getLongLivedToken());
		
		// Expired date in "ddd, dd MMM yyyy HH:mm:ss" format, Convert to "dd MMM yyyy HH:mm:ss" format by removing the first 5 characters
		tokenBean.setExpiredDate(accessTokenResponse.getTokenExpiredAt().substring(5));
		tokens.put(tenant.getTenantCode(), tokenBean);
		
		return accessTokenResponse.getLongLivedToken();
	}
	
	private boolean isTokenExpired(HalosisTokenBean tokenBean) {
		try {
			String expiredDateString = tokenBean.getExpiredDate();
			Date expiredDate = MssTool.formatStringToDate(expiredDateString, "dd MMM yyyy HH:mm:ss");
			
			Date expiredDateThreshold = DateUtils.addDays(expiredDate, -1);
			Date currentDate = new Date();
			
			return currentDate.getTime() >= expiredDateThreshold.getTime();
		} catch (Exception e) {
			LOG.error("Failed to validate Halosis token expiry date", e);
			return true;
		}
		
	}
	
	private HalosisLoginResponse loginHalosis(String email, String password) throws IOException {
		
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put(HttpHeaders.CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		Headers headers = Headers.of(headerMap);
		
		HalosisLoginRequest request = new HalosisLoginRequest();
		request.setEmail(email);
		request.setPassword(password);
		String jsonRequest = gson.toJson(request);
		RequestBody body = RequestBody.create(MediaType.parse(HttpHeaders.APPLICATION_JSON), jsonRequest);
		
		Request okHttpRequest = new Request.Builder()
				.headers(headers)
				.url(baseUrl + loginUrl)
				.post(body)
				.build();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(60L, TimeUnit.SECONDS)
				.readTimeout(60L, TimeUnit.SECONDS)
				.build();
		
		Date startTime = new Date();
		Response response = client.newCall(okHttpRequest).execute();
		Date finishTime = new Date();
		logProcessDuration("Login Halosis", startTime, finishTime);
		
		String jsonResponse = response.body().string();
		return gson.fromJson(jsonResponse, HalosisLoginResponse.class);
	}
	
	private HalosisAccessTokenResponse getAccessToken(String refreshToken) throws IOException {
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put(HttpHeaders.CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		Headers headers = Headers.of(headerMap);
		
		HalosisAccessTokenRequest request = new HalosisAccessTokenRequest();
		request.setRefreshToken(refreshToken);
		String jsonRequest = gson.toJson(request);
		RequestBody body = RequestBody.create(MediaType.parse(HttpHeaders.APPLICATION_JSON), jsonRequest);
		
		Request okHttpRequest = new Request.Builder()
				.headers(headers)
				.url(baseUrl + accessTokenUrl)
				.post(body)
				.build();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(60L, TimeUnit.SECONDS)
				.readTimeout(60L, TimeUnit.SECONDS)
				.build();
		
		Date startTime = new Date();
		Response response = client.newCall(okHttpRequest).execute();
		Date finishTime = new Date();
		logProcessDuration("Get Halosis access token", startTime, finishTime);
		
		String jsonResponse = response.body().string();
		LOG.info("Get halosis access token response: {}", jsonResponse);
		return gson.fromJson(jsonResponse, HalosisAccessTokenResponse.class);
		
	}
	
	private HalosisWhatsAppTemplateComponent prepareHeaderComponent(List<String> headerTexts) {
		List<HalosisWhatsAppTemplateComponentParameter> parameters = new ArrayList<>();
		for (String headerText : headerTexts) {
			HalosisWhatsAppTemplateComponentParameter param = new HalosisWhatsAppTemplateComponentParameter();
			param.setType("text");
			param.setText(headerText);
			parameters.add(param);
		}
		
		HalosisWhatsAppTemplateComponent component = new HalosisWhatsAppTemplateComponent();
		component.setType("header");
		component.setParameters(parameters);
		return component;
	}
	
	private HalosisWhatsAppTemplateComponent prepareBodyComponent(List<String> headerTexts) {
		List<HalosisWhatsAppTemplateComponentParameter> parameters = new ArrayList<>();
		for (String headerText : headerTexts) {
			HalosisWhatsAppTemplateComponentParameter param = new HalosisWhatsAppTemplateComponentParameter();
			param.setType("text");
			param.setText(headerText);
			parameters.add(param);
		}
		
		HalosisWhatsAppTemplateComponent component = new HalosisWhatsAppTemplateComponent();
		component.setType("body");
		component.setParameters(parameters);
		return component;
	}
	
	private HalosisWhatsAppTemplateComponent prepareButtonComponent(String buttonText) {
		HalosisWhatsAppTemplateComponentParameter param = new HalosisWhatsAppTemplateComponentParameter();
		param.setType("text");
		param.setText(buttonText);
		
		List<HalosisWhatsAppTemplateComponentParameter> parameters = new ArrayList<>();
		parameters.add(param);
		
		HalosisWhatsAppTemplateComponent component = new HalosisWhatsAppTemplateComponent();
		component.setType("button");
		component.setSubType("URL");
		component.setIndex("0");
		component.setParameters(parameters);
		return component;
	}
	
	private HalosisSendWhatsAppResponse callSendWhatsApp(MsTenant tenant, MsMsgTemplate template, String phone, String token, List<String> headerTexts, List<String> bodyTexts, String buttonText) throws IOException {
		
		// Prepare header
		Map<String, String> header = new HashMap<>();
		header.put(HttpHeaders.AUTHORIZATION, HttpHeaders.buildBearerToken(token));
		header.put(HttpHeaders.CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		Headers headers = Headers.of(header);
		
		// Prepare body
		List<HalosisWhatsAppTemplateComponent> components = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(headerTexts)) {
			HalosisWhatsAppTemplateComponent headerComponent = prepareHeaderComponent(headerTexts);
			components.add(headerComponent);
		}
		
		if (CollectionUtils.isNotEmpty(bodyTexts)) {
			HalosisWhatsAppTemplateComponent bodyComponent = prepareBodyComponent(bodyTexts);
			components.add(bodyComponent);
		}
		
		if (StringUtils.isNotBlank(buttonText)) {
			HalosisWhatsAppTemplateComponent buttonComponent = prepareButtonComponent(buttonText);
			components.add(buttonComponent);
		}
		
		HalosisWhatsAppTemplateLanguage language = new HalosisWhatsAppTemplateLanguage();
		language.setCode("id");
		
		HalosisWhatsAppTemplate waTemplate = new HalosisWhatsAppTemplate();
		waTemplate.setName(template.getWaHalosisTemplateCode() + templateSuffix);
		waTemplate.setLanguage(language);
		waTemplate.setComponents(components);
		
		HalosisSendWhatsAppRequest request = new HalosisSendWhatsAppRequest();
		request.setMessagingProduct("whatsapp");
		request.setRecipientType("individual");
		request.setTo(MssTool.changePrefixTo62(phone));
		request.setType("template");
		request.setTemplate(waTemplate);
		
		String jsonRequest = gson.toJson(request);
		LOG.info("Tenant {}, send WhatsApp Halosis request: {}", tenant.getTenantCode(), jsonRequest);
		RequestBody body = RequestBody.create(MediaType.parse(HttpHeaders.APPLICATION_JSON), jsonRequest);
		
		Request okHttpRequest = new Request.Builder()
				.headers(headers)
				.url(baseUrl + sendWaUrl)
				.post(body)
				.build();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(60L, TimeUnit.SECONDS)
				.readTimeout(60L, TimeUnit.SECONDS)
				.build();
		
		Date startTime = new Date();
		Response response = client.newCall(okHttpRequest).execute();
		Date finishTime = new Date();
		logProcessDuration("Send WhatsApp Halosis", startTime, finishTime);
		String jsonResponse = response.body().string();
		LOG.info("Tenant {}, send WhatsApp Halosis response: {}", tenant.getTenantCode(), jsonResponse);
		return gson.fromJson(jsonResponse, HalosisSendWhatsAppResponse.class);
	}

	@Override
	@Async
	public void sendMessage(HalosisSendWhatsAppRequestBean request, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		MsTenant tenant = request.getMsTenant();
		MsMsgTemplate template = request.getTemplate();
		String phone = request.getPhoneNumber();
		List<String> headerTexts = request.getHeaderTexts();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();		

		MsLov lovNotificationMedia = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_MESSAGE_MEDIA, GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
		MsLov lovNotificationVendor = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);
		
		HalosisCredentialBean credential = getTenantCredential(tenant);
		boolean isSuccessful = true;
		String vendorTrxNo = null;
		
		String resultStatus = "1";
		
		try {
			String token = getTenantAuthorizationToken(tenant, credential);
			HalosisSendWhatsAppResponse waResponse = callSendWhatsApp(tenant, template, phone, token, headerTexts, bodyTexts, buttonText);
			isSuccessful = GlobalVal.WA_STATUS_SUCESS.equals(waResponse.getStatus());
			vendorTrxNo = waResponse.getWamId();
			resultStatus = GlobalVal.WA_STATUS_SUCESS.equals(waResponse.getStatus()) ? "1" : "0";
			
		} catch (Exception e) {
			LOG.error(FAIL_SEND_WA_LOG, e.getLocalizedMessage(), e);
			resultStatus = "0";
			isSuccessful = false;
		}
		
		String balanceTypeCode = request.isOtp() ? GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP : GlobalVal.CODE_LOV_BALANCE_TYPE_WA;
		String trxTypeCode = request.isOtp() ? GlobalVal.CODE_LOV_TRX_TYPE_UWA_OTP : GlobalVal.CODE_LOV_TRX_TYPE_UWA;
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceTypeCode);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, trxTypeCode);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);

		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(request.getReservedTrxNo());
		mutation.setVendorTrxNo(vendorTrxNo);
		mutation.setTrxDate(new Date());
		mutation.setQty(isSuccessful ? -1 : 0);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setUsrCrt(audit.getCallerId());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(tenant);
		mutation.setMsVendor(vendor);
		
		if (null != request.getAmMsuser()) {
			mutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			mutation.setTrDocumentH(request.getTrDocumentH());
			mutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			mutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			mutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isBlank(mutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
			mutation.setRefNo(request.getRefNo());
		}
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = SEND_WA_TO_NOTE + phone;
		} else {
			notes = request.getNotes();
		}
		mutation.setNotes(notes);
		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);

		if(isSuccessful){
			messageDeliveryReportLogic.insertMessageDeliveryReport(request.getMsTenant(), vendor, request.getReservedTrxNo(), vendorTrxNo, phone,
					NotificationType.WHATSAPP_HALOSIS, lovNotificationVendor, auditTrailBean.getLovSendingPoint(), audit);
		}


		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
		auditTrail.setEmail(StringUtils.upperCase(auditTrailBean.getEmail()));
		auditTrail.setAmMsUser(auditTrailBean.getUser());
		auditTrail.setMsTenant(auditTrailBean.getTenant());
		auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
		auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
		auditTrail.setOtpCode(auditTrailBean.getOtpCode());
		auditTrail.setNotificationMedia(lovNotificationMedia.getDescription());
		auditTrail.setNotificationVendor(lovNotificationVendor.getDescription());
		auditTrail.setNotes(auditTrailBean.getNotes());
		auditTrail.setResultStatus(resultStatus);
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(auditTrailBean.getEmail());
		auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
		
		if (auditTrailBean.getDocumentDs() == null) {
			return;
		}
		
		for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
			TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
			auditTrailDetail.setDtmCrt(new Date());
			auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
			auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
			auditTrailDetail.setTrDocumentD(docD);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);
		}
		
	}
	
	@Override
	public boolean sendSynchronousMessage(HalosisSendWhatsAppRequestBean request, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		MsTenant tenant = request.getMsTenant();
		MsMsgTemplate template = request.getTemplate();
		String phone = request.getPhoneNumber();
		List<String> headerTexts = request.getHeaderTexts();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();
			
		HalosisCredentialBean credential = getTenantCredential(tenant);
		boolean isSuccessful = true;
		String vendorTrxNo = null;
		
		String resultStatus = "1";
		
		try {
			String token = getTenantAuthorizationToken(tenant, credential);
			HalosisSendWhatsAppResponse waResponse = callSendWhatsApp(tenant, template, phone, token, headerTexts, bodyTexts, buttonText);
			isSuccessful = GlobalVal.WA_STATUS_SUCESS.equals(waResponse.getStatus());
			vendorTrxNo = waResponse.getWamId();
			resultStatus = GlobalVal.WA_STATUS_SUCESS.equals(waResponse.getStatus()) ? "1" : "0";
		} catch (Exception e) {
			LOG.error(FAIL_SEND_WA_LOG, e.getLocalizedMessage(), e);
			isSuccessful = false;
			resultStatus = "0";
		}
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_WA);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UWA);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);

		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(request.getReservedTrxNo());
		mutation.setVendorTrxNo(vendorTrxNo);
		mutation.setTrxDate(new Date());
		mutation.setQty(isSuccessful ? -1 : 0);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setUsrCrt(audit.getCallerId());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(tenant);
		mutation.setMsVendor(vendor);
		
		if (null != request.getAmMsuser()) {
			mutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			mutation.setTrDocumentH(request.getTrDocumentH());
			mutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			mutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			mutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isBlank(mutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
			mutation.setRefNo(request.getRefNo());
		}
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = SEND_WA_TO_NOTE + phone;
		} else {
			notes = request.getNotes();
		}
		mutation.setNotes(notes);
		daoFactory.getBalanceMutationDao().insertTrBalanceMutation(mutation);

		if (isSuccessful) {
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, request.getReservedTrxNo(), vendorTrxNo, phone,
					NotificationType.WHATSAPP_HALOSIS, messageGateway, auditTrailBean.getLovSendingPoint(), audit);
		}

		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
		auditTrail.setEmail(StringUtils.upperCase(auditTrailBean.getEmail()));
		auditTrail.setAmMsUser(auditTrailBean.getUser());
		auditTrail.setMsTenant(auditTrailBean.getTenant());
		auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
		auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
		auditTrail.setOtpCode(auditTrailBean.getOtpCode());
		auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
		auditTrail.setNotificationVendor(GlobalVal.NOTIFICATION_VENDOR_NAME_WHATSAPP_HALOSIS);
		auditTrail.setNotes(auditTrailBean.getNotes());
		auditTrail.setResultStatus(resultStatus);
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(auditTrailBean.getEmail());
		auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		
		if (auditTrailBean.getDocumentDs() == null) {
			return isSuccessful;
		}
		
		for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
			TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
			auditTrailDetail.setDtmCrt(new Date());
			auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
			auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
			auditTrailDetail.setTrDocumentD(docD);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetail(auditTrailDetail);
		}
		
		return isSuccessful;
	}

}
