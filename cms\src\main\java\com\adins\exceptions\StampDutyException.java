package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class StampDutyException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonStampDuty {
		USER_CANNOT_CREATE_SDT,
		VENDOR_CANNOT_CREATE_SDT,
		CONVERT_FEE_ERROR,
		CONVERT_QTY_ERROR,
		INVALID_VENDOR_TENANT,
		PARSE_DATE_FILTER_ERROR,
		GENERATE_SDT_REPORT_ERROR,
		RETRY_STAMPING_TENANT_CODE_INVALID,
		RETRY_STAMPING_DOCUMENT_NOT_FOUND,
		RETRY_STAMPING_DECRYPT_FAILED,
		RETRY_STAMPING_FAILED,
		BALANCE_MUTATION_NOT_FOUND,
		REF_NUMBER_INVALID,
		RETRY_STAMPING_PROCESS
	}
	
	private final ReasonStampDuty reason;
	
	public StampDutyException(ReasonStampDuty reason) {
		this.reason = reason;
	}
	
	public StampDutyException(String message, ReasonStampDuty reason) {
		super(message);
		this.reason = reason;
	}

	public StampDutyException(Throwable ex, ReasonStampDuty reason) {
		super(ex);
		this.reason = reason;
	}
	
	public StampDutyException(String message, Throwable ex, ReasonStampDuty reason) {
		super(message, ex);
		this.reason = reason;
	}

	public ReasonStampDuty getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (null != this.reason) {
			switch (reason) {
			case USER_CANNOT_CREATE_SDT:
				return StatusCode.USER_CANNOT_CREATE_SDT;
			case VENDOR_CANNOT_CREATE_SDT:
				return StatusCode.VENDOR_CANNOT_CREATE_SDT;
			case CONVERT_FEE_ERROR:
				return StatusCode.ERROR_PARSING;
			case CONVERT_QTY_ERROR:
				return StatusCode.ERROR_PARSING;
			case INVALID_VENDOR_TENANT:
				return StatusCode.INVALID_VENDOR_TENANT;
			case PARSE_DATE_FILTER_ERROR:
				return StatusCode.ERROR_PARSING;
			case GENERATE_SDT_REPORT_ERROR:
				return StatusCode.GENERATE_SDT_REPORT_ERROR;
			case RETRY_STAMPING_TENANT_CODE_INVALID:
				return StatusCode.RETRY_STAMPING_TENANT_CODE_INVALID;
			case RETRY_STAMPING_DOCUMENT_NOT_FOUND:
				return StatusCode.RETRY_STAMPING_DOCUMENT_NOT_FOUND;
			case RETRY_STAMPING_DECRYPT_FAILED:
				return StatusCode.RETRY_STAMPING_DECRYPT_FAILED;
			case BALANCE_MUTATION_NOT_FOUND:
				return StatusCode.BALANCE_MUTATION_NOT_FOUND;
			case REF_NUMBER_INVALID:
				return StatusCode.REF_NUMBER_INVALID;
			case RETRY_STAMPING_FAILED:
				return StatusCode.RETRY_STAMPING_FAILED;
			case RETRY_STAMPING_PROCESS:
				return StatusCode.RETRY_STAMPING_PROCESS;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}
}
