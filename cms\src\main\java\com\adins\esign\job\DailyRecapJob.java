package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class DailyRecapJob {
	private static final Logger LOG = LoggerFactory.getLogger(DailyRecapJob.class);
	private static final String SCHEDULER = "SCHEDULER";
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void runDailyRecap() {
		try {
			LOG.info("Job Daily Recap Started");
			AuditContext auditContext = new AuditContext(SCHEDULER);
			schedulerLogic.dailyRecap(auditContext);
			LOG.info("Job Daily Recap Finished");
		} catch (Exception e) {
			LOG.error("Error on running Daily Recap job", e);
		}
	}
	
	
}
