package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.ProcessAutosignBmDao;
import com.adins.esign.model.TrProcessAutosignBmH;
import com.adins.esign.model.custom.DetailImportAutosignBmBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.InquiryImportAutosignBmRequest;

@Transactional
@Component
public class ProcessAutosignBmDaoHbn extends BaseDaoHbn implements ProcessAutosignBmDao {

	@Override
	public void insertTrProcessExcelBm(TrProcessAutosignBmH trProcessExcelBmH) {
		trProcessExcelBmH.setUsrCrt(MssTool.maskData(trProcessExcelBmH.getUsrCrt()));
		this.managerDAO.insert(trProcessExcelBmH);
	}

	@Override
	public List<Map<String, Object>> getListImportProcessAutosignBm(InquiryImportAutosignBmRequest request, int min,
			int max, Date startDate, Date endDate) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		params.put(GlobalVal.CONST_TENANT_CODE, request.getTenantCode());
		
		Map<String, String> statusMap = new HashMap<>();
	    statusMap.put("WAITING", "0");
	    statusMap.put("IN PROCESS", "1");
	    statusMap.put("DONE", "2");
		
	    String statusValue = statusMap.get(request.getStatusImport().toUpperCase());
	        
		
		String paramQuery = this.constructParamMessage(params, startDate, endDate, statusValue);
		
		StringBuilder query = new StringBuilder();

		query.append("WITH totalprocessautosign AS ( ")
		       .append("SELECT tpabd.id_process_autosign_bm_h AS id_process_autosign_bm_h, ")
		       .append("COUNT(CASE WHEN tpabd.status = '3' THEN 1 END) || '/' || COUNT(*) AS total ")
		       .append("FROM tr_process_autosign_bm_d tpabd ")
		       .append("JOIN tr_process_autosign_bm_h tpabh ON tpabd.id_process_autosign_bm_h = tpabh.id_process_autosign_bm_h ")
		       .append("GROUP BY tpabd.id_process_autosign_bm_h ")
		       .append("), paginated_data AS ( ")
		       .append("SELECT tpash.id_process_autosign_bm_h AS \"IdProcessAutosignH\",")
		       .append("TO_CHAR(tpash.trx_date, 'YYYY-MM-DD HH24:mi:ss.ms') AS \"RequestImportTime\", ")
		       .append("tpash.execute_type AS \"ExecuteTime\", ")
		       .append("CASE WHEN tpash.status = '0' THEN 'Waiting' ")
		       .append("     WHEN tpash.status = '1' THEN 'In Process' ")
		       .append("     WHEN tpash.status = '2' THEN 'Done' ")
		       .append("     ELSE 'Unknown' END AS \"Status\", ")
		       .append("tpash.file_name AS \"Filename\", ")
		       .append("COALESCE(tps.total, '0/0') AS \"Total\", ")
		       .append("ROW_NUMBER() OVER (ORDER BY tpash.trx_date DESC) AS \"rowNum\" ")
		       .append("FROM tr_process_autosign_bm_h tpash ")
		       .append("LEFT JOIN totalprocessautosign tps ON tps.id_process_autosign_bm_h = tpash.id_process_autosign_bm_h ")
		       .append("JOIN ms_tenant mt ON tpash.id_ms_tenant = mt.id_ms_tenant ")
		       .append("WHERE mt.tenant_code = :tenantCode ")
			   .append(paramQuery)
		       .append(") ")
		       .append("SELECT \"IdProcessAutosignH\", \"RequestImportTime\", \"ExecuteTime\", \"Status\", \"Filename\", \"Total\" ")
		       .append("FROM paginated_data ")
		       .append("WHERE \"rowNum\" BETWEEN :min AND :max");

		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}
	
	private String constructParamMessage(Map<String, Object> params, Date dateStart, 
			Date dateEnd, String importStatus ) {
		
		StringBuilder query = new StringBuilder();
		
		if(StringUtils.isNotBlank(importStatus)) {
			query.append(" and tpash.status = :status ");
			params.put("status", importStatus);
		}
		
		if (dateStart == null && dateEnd == null) {
			query.append("and DATE(tpash.trx_date) >= date_trunc('MONTH', now()) and DATE(tpash.trx_date) <= now() ");
		} else {
		    if (dateStart != null && dateEnd != null) {
		        query.append("and DATE(tpash.trx_date) >= :dateStart and DATE(tpash.trx_date) <= :dateEnd ");
		        params.put("dateStart", dateStart);
		        params.put("dateEnd", dateEnd);
		    }
		}

		
		return query.toString();

	}
	
	@Override
	public Integer countListImportProcessAutosignBm(InquiryImportAutosignBmRequest request, int min,
			int max, Date startDate, Date endDate) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		params.put(GlobalVal.CONST_TENANT_CODE, request.getTenantCode());

		Map<String, String> statusMap = new HashMap<>();
	    statusMap.put("WAITING", "0");
	    statusMap.put("IN PROCESS", "1");
	    statusMap.put("DONE", "2");
		
	    String statusValue = statusMap.get(request.getStatusImport().toUpperCase());
	        
		
		String paramQuery = this.constructParamMessage(params, startDate, endDate, statusValue);
		
		StringBuilder query = new StringBuilder();

		query.append("WITH totalprocessautosign AS ( ")
		       .append("SELECT tpabd.id_process_autosign_bm_h AS id_process_autosign_bm_h, ")
		       .append("COUNT(CASE WHEN tpabd.status = '3' THEN 1 END) || '/' || COUNT(*) AS total ")
		       .append("FROM tr_process_autosign_bm_d tpabd ")
		       .append("JOIN tr_process_autosign_bm_h tpabh ON tpabd.id_process_autosign_bm_h = tpabh.id_process_autosign_bm_h ")
		       .append("GROUP BY tpabd.id_process_autosign_bm_h ")
		       .append("), paginated_data AS ( ")
		       .append("SELECT tps.id_process_autosign_bm_h AS \"IdProcessAutosignH\",")
		       .append("tpash.trx_date AS \"RequestImportTime\", ")
		       .append("tpash.execute_type AS \"ExecuteTime\", ")
		       .append("CASE WHEN tpash.status = '0' THEN 'Waiting' ")
		       .append("     WHEN tpash.status = '1' THEN 'In Process' ")
		       .append("     WHEN tpash.status = '2' THEN 'Done' ")
		       .append("     ELSE 'Unknown' END AS \"Status\", ")
		       .append("tpash.file_name AS \"Filename\", ")
		       .append("tps.total AS \"Total\", ")
		       .append("ROW_NUMBER() OVER (ORDER BY tpash.trx_date DESC) AS \"rowNum\" ")
		       .append("FROM tr_process_autosign_bm_h tpash ")
		       .append("LEFT JOIN totalprocessautosign tps ON tps.id_process_autosign_bm_h = tpash.id_process_autosign_bm_h ")
		       .append("JOIN ms_tenant mt ON tpash.id_ms_tenant = mt.id_ms_tenant ")
		       .append("WHERE mt.tenant_code = :tenantCode ")
		       .append(paramQuery)
		       .append(") ")
		       .append("SELECT count(*) FROM paginated_data ");
		  BigInteger totalData = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);

		return totalData.intValue();
	}
	
	//FIX SONAR
	
	public TrProcessAutosignBmH getProcessAutosignBmHeader(String tenantCode, String requestDate, String fileName) {
		
		Object[][] queryParams = {
				{GlobalVal.CONST_TENANT_CODE, tenantCode},
				{"fileName", fileName},
				{"trxDate", MssTool.formatStringToDate(requestDate, GlobalVal.DATE_TIME_FORMAT_MIL_SEC)}
		};
		
		return this.managerDAO.selectOne(
				"from TrProcessAutosignBmH pab "
				+ "join fetch pab.msTenant t "
				+ "join fetch pab.msVendor v "
				+ "where t.tenantCode = :tenantCode and pab.fileName = :fileName and pab.trxDate = :trxDate "
				, queryParams);
	}

	@Override
	public List<DetailImportAutosignBmBean> getListOfProcessAutosignBmDetail(Long idProcessAutosignBmH, int min, int max) {
		Object[][] params = {
				{"idProcessAutosignBmH", idProcessAutosignBmH},
				{"min", min}, {"max", max}
		};

		StringBuilder query = new StringBuilder();
		query.append("select nik, email, phone, \"keyUser\", cvv, \"poaId\", \"certExp\", status, notes from (")
			.append("select coalesce(nik, '-') as nik, coalesce(email, '-') as email, coalesce(phone, '-') as phone, ")
			.append("       coalesce(key_user, '-') as \"keyUser\", coalesce(cvv, '-') as cvv, ")
			.append("       coalesce(poa_id, '-') as \"poaId\", ")
			.append("       coalesce(to_char(cert_expired_date, 'YYYY-MM-DD'), '-') as \"certExp\", ") // Alias enclosed in quotes
			.append("       coalesce(notes, '-') as notes, ")
			.append("       case when status = '0' then 'Waiting' ")
			.append("            when status = '1' then 'In Process' ")
			.append("            when status = '2' then 'Fail' ")
			.append("            when status = '3' then 'Success' ")
			.append("       end as status, ")
			.append("ROW_NUMBER() OVER (ORDER BY dtm_crt asc) AS \"rowNum\" ")
			.append("from tr_process_autosign_bm_d ")
			.append("where id_process_autosign_bm_h = :idProcessAutosignBmH ")
			.append(") as data where \"rowNum\" between :min and :max ");

		return managerDAO.selectForListString(DetailImportAutosignBmBean.class, query.toString(), params, null);
	}


	@Override
	public Integer countListOfProcessAutosignBmDetail(Long idProcessAutosignBmH) {
		Object[][] params = {{"idProcessAutosignBmH", idProcessAutosignBmH}};
		
		StringBuilder query = new StringBuilder();
		query.append("select count(*) ")
			 .append("from tr_process_autosign_bm_d ")
			 .append("where id_process_autosign_bm_h = :idProcessAutosignBmH ");
		
		BigInteger totalData = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);

		return totalData.intValue();
	}

}