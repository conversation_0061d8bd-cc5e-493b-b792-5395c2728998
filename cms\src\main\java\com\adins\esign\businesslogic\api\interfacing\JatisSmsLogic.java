package com.adins.esign.businesslogic.api.interfacing;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface JatisSmsLogic {
	
	/**
	 * 
	 * Insert tr_balance_mutation with <code>Propagation.REQUIRES_NEW</code> transaction.<br>
	 * If database session error occurred, may be needed to get Ms<PERSON><PERSON>t, TrDocumentH, TrDocumentD, or AmMsuser with <code>Propagation.REQUIRES_NEW</code> transaction also.
	 * 
	 * @param request - Mandatory to set all request attributes (tenant, phoneNumber, smsMessage, trxNo, isOtpSms)
	 * @param documentH - Optional, if not null, will be used to insert tr_balance_mutation
	 * @param document - Optional, if not null, will be used to insert tr_balance_mutation
	 * @param user - Optional, if not null, will be used to insert tr_balance_mutation
	 * @param notes - Optional, if null, will be filled with "Send SMS to {phone}"
	 * 
	 */
	void sendSmsAndCutBalance(JatisSmsRequestBean request, TrDocumentH documentH, TrDocumentD document, AmMsuser user, String notes, AuditContext audit);
	void sendSmsAndCutBalance(JatisSmsRequestBean request, TrDocumentH documentH, TrDocumentD document, AmMsuser user, String notes, AuditContext audit, SigningProcessAuditTrailBean auditTrailBean);
	JatisSmsResponse sendSmsOnly(JatisSmsRequestBean request);
}
