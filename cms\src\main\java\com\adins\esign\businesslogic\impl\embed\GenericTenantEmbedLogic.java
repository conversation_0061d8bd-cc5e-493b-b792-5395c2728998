package com.adins.esign.businesslogic.impl.embed;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.embed.TenantEmbedLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.custom.BalanceVendoroftenantBean;
import com.adins.esign.model.custom.embed.EmbedMsgBeanV2;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.EmbedValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.webservices.model.TenantSettingsEmbedRequest;
import com.adins.esign.webservices.model.TenantSettingsRequest;
import com.adins.esign.webservices.model.TenantSettingsResponse;
import com.adins.esign.webservices.model.embed.CheckLivenessFaceCompareServiceEmbedRequest;
import com.adins.esign.webservices.model.embed.CheckLivenessFaceCompareServiceEmbedResponse;
import com.adins.esign.webservices.model.embed.GetAvailableSendingPointEmbedRequest;
import com.adins.esign.webservices.model.embed.GetAvailableSendingPointEmbedResponse;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericTenantEmbedLogic extends BaseLogic implements TenantEmbedLogic {

	@Autowired private EmbedValidatorLogic embedValidatorLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;

	
	@Override
	public CheckLivenessFaceCompareServiceEmbedResponse checkLivenessFaceCompareServiceEmbed(CheckLivenessFaceCompareServiceEmbedRequest request, AuditContext audit) {
		
		EmbedMsgBeanV2 msg = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(),request.getTenantCode(), true, audit);

		CheckLivenessFaceCompareServiceEmbedResponse response = new CheckLivenessFaceCompareServiceEmbedResponse();
		response.setLivenessFacecompareServicesStatus("1".equals(msg.getMsTenant().getLivenessFaceCompareServices()) ? "1" : "0");
		response.setMustLivenessFaceCompareFirst("1".equals(msg.getMsTenant().getUseLivenessFacecompareFirst()) ? "1" : "0");
		return response;
	}
	
	@Override
	public TenantSettingsResponse getTenantSettingsEmbed(TenantSettingsEmbedRequest request, AuditContext audit) {
		EmbedMsgBeanV2 msg = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(),request.getTenantCode(), false, audit);
		
		TenantSettingsRequest settingRequest = new TenantSettingsRequest();
		settingRequest.setTenantCode(msg.getMsTenant().getTenantCode());
		settingRequest.setParam(request.getParam());
		return tenantLogic.getTenantSettingsWithoutSecurity(settingRequest, audit);
	}
	
	@Override
	public GetAvailableSendingPointEmbedResponse getAvailableSendingPointEmbed(GetAvailableSendingPointEmbedRequest request, AuditContext audit) {
		
		EmbedMsgBeanV2 msg = embedValidatorLogic.validateEmbedMessageV2(request.getMsg(),request.getTenantCode(), false, audit);
		String email = msg.getDecryptedEmail();
		String messageValidation = "";
		
		
		BigInteger signerIdMsUser = daoFactory.getUserDao().getIdMsUserBySignerRegisteredEmail(email);
		
		
		messageValidation = getMessage("businesslogic.userval.emailnotfound", new Object[] { email }, audit);
		commonValidatorLogic.validateNotNull(signerIdMsUser, messageValidation, StatusCode.USER_NOT_FOUND);
		
		
		Long idMsUser = signerIdMsUser.longValue();	
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_TENANT_CODE_EMPTY,
				new Object[] { request.getTenantCode() }, audit);
		commonValidatorLogic.validateNotNull(signerIdMsUser, messageValidation, StatusCode.TENANT_CODE_EMPTY);
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_TENANT_CODE_EMPTY,
				new Object[] { request.getVendorCode() }, audit);
		commonValidatorLogic.validateNotNull(signerIdMsUser, messageValidation, StatusCode.VENDOR_CODE_EMPTY);
		
		
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(idMsUser, request.getVendorCode());
		
		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_OTP_SIGN_BY_EMAIL);
		
		List<BalanceVendoroftenantBean> listBalanceVendoroftenant = daoFactory.getBalanceVendoroftenantDao().getListBalanceVendoroftenant(tenant.getTenantCode(), GlobalVal.VENDOR_CODE_ESG);
		
		List<String> listOtpSendingOption = new ArrayList<>();
		for (int i = 0; i < listBalanceVendoroftenant.size(); i++) {
			if (listBalanceVendoroftenant.get(i).getBalanceTypeCode().equals(GlobalVal.CODE_LOV_BALANCE_TYPE_SMS)) {
				listOtpSendingOption.add(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
			} else if (listBalanceVendoroftenant.get(i).getBalanceTypeCode().equals(GlobalVal.CODE_LOV_BALANCE_TYPE_WA)) {
				listOtpSendingOption.add(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
			}
		}
		

		if (null != tenantSettings && tenantSettings.getSettingValue().equals("1") && vendorUser.getEmailService().equals("0")) {
			listOtpSendingOption.add(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL);
		}
		
		GetAvailableSendingPointEmbedResponse response = new GetAvailableSendingPointEmbedResponse();
		response.setListAvailableOptionSendingPoint(listOtpSendingOption);
		
		String defaultSendingPoint = null;
		if (tenant.getLovDefaultOtpSendingOption() != null) {
			if (tenant.getLovDefaultOtpSendingOption().getCode().equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS) || tenant.getLovDefaultOtpSendingOption().getCode().equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA) || tenant.getLovDefaultOtpSendingOption().getCode().equals(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL)) {
				if (listOtpSendingOption.contains(tenant.getLovDefaultOtpSendingOption().getCode())) {
					defaultSendingPoint = tenant.getLovDefaultOtpSendingOption().getCode();
				} else {
					defaultSendingPoint = GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS;
				}
			} else {
				defaultSendingPoint = GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS;

			}
	
		} else {
			defaultSendingPoint = GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS;
		}
		response.setDefaultAvailableOptionSendingPoint(defaultSendingPoint);
		
		return response;

	}
	

}
