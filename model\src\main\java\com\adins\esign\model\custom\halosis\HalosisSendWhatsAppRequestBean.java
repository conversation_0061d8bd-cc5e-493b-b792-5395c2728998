package com.adins.esign.model.custom.halosis;

import java.util.List;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrDocumentH;

public class HalosisSendWhatsAppRequestBean {
	private MsMsgTemplate template; // mandatory
	private List<String> headerTexts; // belum cek mandatory / optional
	private List<String> bodyTexts; // mandatory
	private String buttonText; // optional, set null if does not need button
	private String reservedTrxNo; // mandatory
	private String phoneNumber; // mandatory, filled with 08xxxxxx format 
	private AmMsuser amMsuser; // optional, set only if needed to be inserted into tr_balance_mutation
	private TrDocumentH trDocumentH; // optional, set only if needed to be inserted into tr_balance_mutation
	private MsBusinessLine msBusinessLine; // optional, set only if needed to be inserted into tr_balance_mutation
	private MsOffice msOffice; // optional, set only if needed to be inserted into tr_balance_mutation
	private MsTenant msTenant; // mandatory
	private String refNo; // Optional, set only if you need to fill tr_balance_mutation.ref_no
	private String notes; // Optional, default message if empty: Sending WhatsApp to {phone_number}
	private boolean isOtp;
	
	public MsMsgTemplate getTemplate() {
		return template;
	}
	public void setTemplate(MsMsgTemplate template) {
		this.template = template;
	}
	public List<String> getHeaderTexts() {
		return headerTexts;
	}
	public void setHeaderTexts(List<String> headerTexts) {
		this.headerTexts = headerTexts;
	}
	public List<String> getBodyTexts() {
		return bodyTexts;
	}
	public void setBodyTexts(List<String> bodyTexts) {
		this.bodyTexts = bodyTexts;
	}
	public String getButtonText() {
		return buttonText;
	}
	public void setButtonText(String buttonText) {
		this.buttonText = buttonText;
	}
	public String getReservedTrxNo() {
		return reservedTrxNo;
	}
	public void setReservedTrxNo(String reservedTrxNo) {
		this.reservedTrxNo = reservedTrxNo;
	}
	public String getPhoneNumber() {
		return phoneNumber;
	}
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	public AmMsuser getAmMsuser() {
		return amMsuser;
	}
	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}
	public TrDocumentH getTrDocumentH() {
		return trDocumentH;
	}
	public void setTrDocumentH(TrDocumentH trDocumentH) {
		this.trDocumentH = trDocumentH;
	}
	public MsBusinessLine getMsBusinessLine() {
		return msBusinessLine;
	}
	public void setMsBusinessLine(MsBusinessLine msBusinessLine) {
		this.msBusinessLine = msBusinessLine;
	}
	public MsOffice getMsOffice() {
		return msOffice;
	}
	public void setMsOffice(MsOffice msOffice) {
		this.msOffice = msOffice;
	}
	public MsTenant getMsTenant() {
		return msTenant;
	}
	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
	public String getRefNo() {
		return refNo;
	}
	public void setRefNo(String refNo) {
		this.refNo = refNo;
	}
	public String getNotes() {
		return notes;
	}
	public void setNotes(String notes) {
		this.notes = notes;
	}
	public boolean isOtp() {
		return isOtp;
	}
	public void setIsOtp(boolean isOtp) {
		this.isOtp = isOtp;
	}
	
}
