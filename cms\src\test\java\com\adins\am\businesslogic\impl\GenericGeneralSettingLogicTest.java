package com.adins.am.businesslogic.impl;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.businesslogic.api.GeneralSettingLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.esign.businesslogic.api.TestSetUpLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class GenericGeneralSettingLogicTest extends BaseLogic{
//	@Autowired private GeneralSettingLogic generalSettingLogic;
//	@Autowired private TestSetUpLogic testSetUpLogic;
//
//	private AuditContext auditContext = new AuditContext();
//	private AmGeneralsetting generalSetting;
//	
//	@BeforeEach
//	@Rollback(true)
//	public void setUp() {
//		auditContext.setCallerId("JUNIT");
//		generalSetting = testSetUpLogic.setUpGenSet("JUNIT");
//	}
//	
//	@Test
//	void listGeneralSettingNullTest() {
//		Object[][] params = { { Restrictions.eq("isDeleted", null)}, 
//				{ Restrictions.eq("isActive", "1")} };
//		Map<String, Object> listGeneralSetting = generalSettingLogic.listGeneralSetting(params, new Object(), 1, 1, auditContext);
//		assertNull(listGeneralSetting);
//	}
//	
//	@Test
//	void getGeneralSettingByUuidNullTest() {
//		AmGeneralsetting generalSetting = generalSettingLogic.getGeneralSetting(this.generalSetting.getIdGeneralSetting(), auditContext);
//		assertNull(generalSetting);
//	}
//	
//	@Test
//	@Rollback(true)
//	void updateGeneralSettingTest() {
//		this.generalSetting.setGsCode("JUNIT UPDATE");
//		generalSettingLogic.updateGeneralSetting(this.generalSetting, auditContext, this.generalSetting.getIdGeneralSetting());
//		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByUuid(this.generalSetting.getIdGeneralSetting());
//		assertEquals(gs.getGsCode(), this.generalSetting.getGsCode());
//	}
//	
//	@Test
//	void getGeneralSettingByCodeNullTest() {
//		AmGeneralsetting generalSetting = generalSettingLogic.getGeneralSetting(this.generalSetting.getGsCode(), auditContext);
//		assertNull(generalSetting);
//	}
}
