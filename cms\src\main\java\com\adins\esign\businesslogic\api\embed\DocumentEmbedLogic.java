package com.adins.esign.businesslogic.api.embed;

import java.io.IOException;
import java.text.ParseException;

import com.adins.esign.webservices.model.BulkSignDocumentEmbedRequest;
import com.adins.esign.webservices.model.BulkSignDocumentResponse;
import com.adins.esign.webservices.model.CheckDocumentSendStatusEmbedRequest;
import com.adins.esign.webservices.model.CheckDocumentSendStatusResponse;
import com.adins.esign.webservices.model.DocumentExcelReportResponse;
import com.adins.esign.webservices.model.ListInquiryDocumentEmbedRequest;
import com.adins.esign.webservices.model.ResendSignNotificationResponse;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.embed.CancelDigitalSignEmbedRequest;
import com.adins.esign.webservices.model.embed.CancelDigitalSignEmbedResponse;
import com.adins.esign.webservices.model.embed.CheckDocumentBeforeSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.CheckDocumentBeforeSigningEmbedResponse;
import com.adins.esign.webservices.model.embed.ResendSignNotificationEmbedRequest;
import com.adins.esign.webservices.model.embed.SignDocumentEmbedV2Request;
import com.adins.esign.webservices.model.embed.SignDocumentEmbedV2Response;
import com.adins.esign.webservices.model.embed.StartStampingMeteraiEmbedRequest;
import com.adins.esign.webservices.model.embed.StartStampingMeteraiEmbedResponse;
import com.adins.esign.webservices.model.embed.ViewSignerEmbedRequest;
import com.adins.esign.webservices.model.embed.ViewSignerEmbedResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface DocumentEmbedLogic {

	CheckDocumentBeforeSigningEmbedResponse checkDocumentBeforeSigningEmbed(CheckDocumentBeforeSigningEmbedRequest request, AuditContext audit);
	ViewDocumentResponse getDocumentFileEmbed(ViewDocumentRequest request, AuditContext audit);
	CancelDigitalSignEmbedResponse cancelDigitalSign(CancelDigitalSignEmbedRequest request, AuditContext audit);
	SignDocumentEmbedV2Response signDocument(SignDocumentEmbedV2Request request, AuditContext audit);
	DocumentExcelReportResponse exportDocumentReportEmbed(ListInquiryDocumentEmbedRequest request, AuditContext audit);
	ResendSignNotificationResponse resendSignNotification(ResendSignNotificationEmbedRequest request, AuditContext audit);
	ViewSignerEmbedResponse viewSignerEmbed(ViewSignerEmbedRequest request, AuditContext audit);
	CheckDocumentSendStatusResponse checkDocumentSendStatusEmbed(CheckDocumentSendStatusEmbedRequest request, AuditContext audit);
	BulkSignDocumentResponse bulkSignDocumentEmbed(BulkSignDocumentEmbedRequest request, AuditContext audit) throws IOException, ParseException;
	StartStampingMeteraiEmbedResponse startStampingMeteraiEmbed(StartStampingMeteraiEmbedRequest request,
			AuditContext audit);
}
