package com.adins.esign.dataaccess.impl;

import static org.junit.jupiter.api.Assertions.assertNotEquals;

import java.util.List;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.dataaccess.api.EmailDao;
import com.adins.esign.model.MsEmailHosting;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class EmailDaoHbnTest{
	@Autowired private EmailDao emailDao;

	
	@Test
	void getListActiveEmailHostingTest() {
		List<MsEmailHosting> emailHosting = emailDao.getListActiveEmailHosting();
		assertNotEquals(0, emailHosting.size());
	}

}
