package com.adins.esign.dataaccess.impl;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.RoleDao;
//import com.adins.esign.model.MsJob;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class JobDaoHbnTest extends BaseLogic{
//	@Autowired private JobDao jobDao;
//	
//	@BeforeEach
//	public void setUp() {
//		
//	}
//	
//	@Order(1)
//	@Test
//	void getJobByCodeTest() {
//		MsJob job = jobDao.getJobByCode(GlobalVal.JOB_ADMIN);
//		assertEquals(GlobalVal.JOB_ADMIN, job.getJobCode());
//	}
//	
//	@Order(2)
//	@Test
//	void getJobByCodeNullTest() {
//		MsJob job = jobDao.getJobByCode("INVALIDCODE");
//		assertNull(job);
//	}
//	
//	@Order(3)
//	@Test
//	void getJobByCodeNullTestWithEmptyCode() {
//		MsJob job = jobDao.getJobByCode("");
//		assertNull(job);
//	}
//	
//	@Order(4)
//	@Test
//	@Rollback(true)
//	void insertJobTest() {
//		AmMssubsystem amMssubsystem = daoFactory.getSubsystemDao().getSubsystemByName(GlobalVal.SUBSYSTEM_ESIGN);
//		
//		MsJob newJob = new MsJob();
//    	newJob.setMsJob(null);
//    	newJob.setAmMssubsystem(amMssubsystem);
//		newJob.setIsActive("1");
//    	newJob.setUsrCrt("JUNIT");
//    	newJob.setDtmCrt(new Date());
//    	newJob.setJobCode("JUNIT");
//    	newJob.setDescription("JUNIT DESC");
//    	newJob.setIsFieldPerson("0");
//    	newJob.setIsBranch("1");
//    	
//    	jobDao.insertJob(newJob);
//    	MsJob job = jobDao.getJobByCode(newJob.getJobCode());
//    	assertEquals(job.getJobCode(), newJob.getJobCode());
//	}
}
