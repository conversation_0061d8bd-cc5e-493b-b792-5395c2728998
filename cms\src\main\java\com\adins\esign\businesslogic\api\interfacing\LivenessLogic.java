package com.adins.esign.businesslogic.api.interfacing;

import java.io.IOException;

import com.adins.esign.webservices.model.LivenessUrlResponse;
import com.adins.esign.webservices.model.RegistrationRequest;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface LivenessLogic {
	void registerLiveness(RegistrationRequest request, AuditContext audit) throws IOException;
	LivenessUrlResponse getLivenessUrl(String xApiKey, AuditContext audit);
}
