package com.adins.esign.webservices.frontend.endpoint;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.job.QueuePublisher;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.SaveSignCallbackCompleteSIgnTekenAjaBean;
import com.adins.esign.model.custom.SaveSigningResultDecryptedBean;
import com.adins.esign.webservices.frontend.api.CallbackService;
import com.adins.esign.webservices.model.TekenAjaCallbackRequest;
import com.adins.esign.webservices.model.TekenAjaCallbackResponse;
import com.adins.exceptions.CallbackException;
import com.adins.exceptions.CallbackException.ReasonCallback;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.google.gson.Gson;

import io.swagger.annotations.Api;

@Component
@Path("/callback")
@Api(value = "CallbackService")
public class GenericCallbackService implements CallbackService {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericCallbackService.class);
	@Autowired private DigisignLogic digisignLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private DocumentLogic documentLogic;
	@Autowired private UserLogic userLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private Gson gson;

	@Override
	@GET
	@Path("/digi/{tenantCode}/sign")
	@Produces("application/json")
	public Response callbackDigisignSigning(
			@PathParam("tenantCode") String tenantCode, @QueryParam("msg") String msg) {

		AuditContext audit = new AuditContext(GlobalVal.VENDOR_CODE_DIGISIGN);
		MsTenant tenant = tenantLogic.getTenantByCode(tenantCode, audit);
		String redirectSignUri = StringUtils.isBlank(tenant.getClientSigningRedirectUrl()) ? 
				this.commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_URI_REDIR_SIGN, audit) : 
					tenant.getClientSigningRedirectUrl();
		if (msg != null) {
			String encodedMsg = "";
			try {
				encodedMsg = URLEncoder.encode(msg, StandardCharsets.UTF_8.toString());
			}
			catch (UnsupportedEncodingException e) {
				LOG.warn("Error encodeUrl: {}", msg);
			}
			
			String decrypted = digisignLogic.decryptMessage(encodedMsg, tenantCode);
			SaveSigningResultDecryptedBean bean = gson.fromJson(decrypted, SaveSigningResultDecryptedBean.class);
			QueuePublisher.queueSaveSigningResult(bean);
			String aesEncryptKey = tenant.getAesEncryptKey();
			if (aesEncryptKey == null || StringUtils.isBlank(aesEncryptKey))
			{
				aesEncryptKey = commonLogic.getAesEncryptionKey(audit);
			}
			String documentId = commonLogic.encryptMessageToString(bean.getDocumentId(),aesEncryptKey, audit);
			LOG.info("Encrypt Key for document Id :{}",aesEncryptKey);
			
			try {
				redirectSignUri = redirectSignUri + "?documentId=" + URLEncoder.encode(documentId, StandardCharsets.UTF_8.toString()) + "&tenantCode=" + tenantCode;
			} catch (UnsupportedEncodingException e) {
				LOG.error("Error encodingRedirectUri: {}", redirectSignUri, e);
			}
		}

		URI uri = null;
		
		try {
			uri = new URI(redirectSignUri);
		}
		catch (URISyntaxException eUri) {
			LOG.error("Error createRedirectUri: {}", redirectSignUri, eUri);
		}
		
		return Response.temporaryRedirect(uri).build();
	}
	
	@Override
	@POST
	@Path("/djelas/sign")
	@Produces("application/json")
	public TekenAjaCallbackResponse tekenAjaCallback(TekenAjaCallbackRequest request) {
		
		AuditContext audit = new AuditContext(GlobalVal.VENDOR_CODE_TEKENAJA);
		
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String token = httpRequest.getHeader("Token");
		String tokenGeneralSetting = this.commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_GSCODE_TOKEN_CALLBACK_TEKENAJA, audit);
		
		if (!token.equals(tokenGeneralSetting)) {
			throw new CallbackException("Token Not Match",  ReasonCallback.TOKEN_NOT_MATCH);
		}
		
		if (GlobalVal.CALLBACK_REGISTRASI_CODE.equals(request.getCode())) {
			
			userLogic.callbackRegister(request, audit);
			
		} else if (GlobalVal.CALLBACK_DOCUMENT_SIGN_CODE.equals(request.getCode())) {
			
			SaveSignCallbackCompleteSIgnTekenAjaBean bean = new SaveSignCallbackCompleteSIgnTekenAjaBean();
			bean.setSignerEmail(request.getData().getSignerEmail());
			bean.setCode(request.getCode());
			bean.setDocumentId(request.getData().getDocumentId());
			QueuePublisher.queueSaveSigningResult(bean);
			
		} else if (GlobalVal.CALLBACK_DOCUMENT_SIGN_COMPLETE_CODE.equals(request.getCode())) {
			
			MsLov lovSignStatus = documentLogic.getLovSignStatusByPsreId(request.getData().getDocumentId());
			if(!GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED.equals(lovSignStatus.getCode())) {
				for(int i = 0; i<request.getData().getSigners().size(); i++) {
					SaveSignCallbackCompleteSIgnTekenAjaBean bean = new SaveSignCallbackCompleteSIgnTekenAjaBean();
					bean.setSignerEmail(request.getData().getSigners().get(i).getEmail());
					bean.setCode(request.getCode());
					bean.setDocumentId(request.getData().getDocumentId());
					QueuePublisher.queueSaveSigningResult(bean);
				}
		    }
			
	    }
		return new TekenAjaCallbackResponse();
	}
}
