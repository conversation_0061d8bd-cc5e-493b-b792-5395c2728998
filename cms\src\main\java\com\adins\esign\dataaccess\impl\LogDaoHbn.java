package com.adins.esign.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmAuditlog;
import com.adins.esign.dataaccess.api.LogDao;

@Transactional
@Component
public class LogDaoHbn extends BaseDaoHbn implements LogDao{

	@Override
	public AmAuditlog getAuditLogByActivity(String activity) {
		return this.managerDAO.selectOne(
				"from AmAuditlog al "
				+ "where al.activity = :activity",
				new Object [][] {{AmAuditlog.ACTIVITY_HBM, activity}});
	}

}
