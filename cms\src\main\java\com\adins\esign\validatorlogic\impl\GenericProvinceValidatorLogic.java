package com.adins.esign.validatorlogic.impl;

import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsprovince;
import com.adins.esign.validatorlogic.api.ProvinceValidatorLogic;
import com.adins.exceptions.LocationException;
import com.adins.exceptions.LocationException.ReasonLocation;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericProvinceValidatorLogic extends BaseLogic implements ProvinceValidatorLogic {

	private String getMessages(String code, Object[] params, AuditContext audit) {
		return messageSource.getMessage(code, params, retrieveLocaleAudit(audit));
	}
	
	@Override
	public AmMsprovince validateGetProvince(Long idMsprovince, boolean checkProvinceExistance, AuditContext audit) {
		if (null == idMsprovince) {
			return null;
		}
		
		AmMsprovince province = daoFactory.getProvinceDao().getProvince(idMsprovince);
		if (checkProvinceExistance && null == province) {
			throw new LocationException(getMessages("businesslogic.province.notfound",
					new Object[] {idMsprovince}, audit), ReasonLocation.PROVINCE_NOT_FOUND);
		}
		
		return province;
	}

}
