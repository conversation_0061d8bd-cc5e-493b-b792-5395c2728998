package com.adins.esign.dataaccess.impl;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.businesslogic.api.TestSetUpLogic;
import com.adins.esign.dataaccess.api.LovDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.custom.LovBean;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class LovDaoHbnTest {
//	@Autowired private TestSetUpLogic testSetUpLogic;
//	@Autowired private LovDao lovDao;
//	
//	private MsLov lov;
//	
//	@BeforeEach
//	@Rollback(true)
//	public void setUp() {
//		lov = testSetUpLogic.setUpLovByGroupAndCode("JUNIT_LOVGROUP", "JUNIT_CODE");
//	}
//	
//	@Order(1)
//	@Test
//	void getMsLovByCodeTest() {
//		MsLov lov = lovDao.getMsLovByCode(this.lov.getCode());
//		assertEquals(lov.getCode(), this.lov.getCode());
//	}
//	
//	@Order(2)
//	@Test
//	void getMsLovByCodeNullTest() {
//		MsLov lov = lovDao.getMsLovByCode("INVALIDCODE");
//		assertNull(lov);
//	}
//	
//	@Order(3)
//	@Test
//	void getMsLovByCodeNullWithEmptyCode() {
//		MsLov lov = lovDao.getMsLovByCode("");
//		assertNull(lov);
//	}
//	
//	@Order(4)
//	@Test
//	void getMsLovByGroupAndCodeTest() {
//		MsLov lov = lovDao.getMsLovByGroupAndCode(this.lov.getLovGroup(), this.lov.getCode());
//		assertEquals(lov.getCode(), this.lov.getCode());
//	}
//	
//	@Order(5)
//	@Test
//	void getMsLovByGroupAndCodeNullTest() {
//		MsLov lov = lovDao.getMsLovByGroupAndCode("INVALIDCODE", "INVALIDCODE");
//		assertNull(lov);
//	}
//	
//	@Order(6)
//	@Test
//	void getMsLovByGroupAndCodeNullTestWithEmptyGroup() {
//		MsLov lov = lovDao.getMsLovByGroupAndCode("", "INVALIDCODE");
//		assertNull(lov);
//	}
//	
//	@Order(7)
//	@Test
//	void getMsLovByGroupAndCodeNullTestWithEmptyCode() {
//		MsLov lov = lovDao.getMsLovByGroupAndCode("INVALIDCODE", "");
//		assertNull(lov);
//	}
//
//	@Order(8)
//	@Test
//	void getMsLovListByGroupTest() {
//		List<LovBean> listLov = lovDao.getMsLovListByGroup(this.lov.getLovGroup());
//		assertTrue(!listLov.isEmpty());
//	}
//	
//	@Order(9)
//	@Test
//	void getMsLovListByGroupNullTest() {
//		List<LovBean> listLov = lovDao.getMsLovListByGroup("INVALIDCODE");
//		assertTrue(listLov.isEmpty());
//	}
//	
//	@Order(10)
//	@Test
//	void getMsLovListByGroupNullTestWithEmptyGroup() {
//		List<LovBean> listLov = lovDao.getMsLovListByGroup("");
//		assertTrue(listLov.isEmpty());
//	}
//	
//	@Order(11)
//	@Test
//	@Rollback(true)
//	void insertLovTest() {
//		MsLov insertLov = new MsLov();
//		insertLov.setIsActive("1");
//		insertLov.setUsrCrt("JUNIT");
//		insertLov.setDtmCrt(new Date());
//		insertLov.setLovGroup("JUNIT_LOVGROUP2");
//		insertLov.setCode("JUNIT_CODE2");
//		insertLov.setDescription("DESCJUNIT");
//		insertLov.setSequence(10);
//		
//		lovDao.insertLov(insertLov);
//		MsLov lov = lovDao.getMsLovByCode(insertLov.getCode());
//		assertEquals(lov.getCode(), insertLov.getCode());
//	}
//	
//	@Order(12)
//	@Test
//	@Rollback(true)
//	void updateLovTest() {
//		this.lov.setCode("JUNIT_CODE2");
//		lovDao.updateLov(this.lov);
//		
//		MsLov lov = lovDao.getMsLovByCode(this.lov.getCode());
//		assertEquals(lov.getCode(), this.lov.getCode());
//	}
//	
//	@Order(13)
//	@Test
//	void checkLovExistTest() {
//		MsLov lov = lovDao.checkLovExist(this.lov);
//		assertEquals(lov.getCode(), this.lov.getCode());
//	}
//	
//	@Order(14)
//	@Test
//	void checkLovExistNullTest() {
//		MsLov checkLov = new MsLov();
//		checkLov.setIsActive("1");
//		checkLov.setUsrCrt("JUNIT");
//		checkLov.setDtmCrt(new Date());
//		checkLov.setLovGroup("JUNIT_LOVGROUP2");
//		checkLov.setCode("JUNIT_CODE2");
//		checkLov.setDescription("DESCJUNIT");
//		checkLov.setSequence(10);
//		
//		MsLov lov = lovDao.checkLovExist(checkLov);
//		assertNull(lov);
//	}
//	
//	@Order(15)
//	@Test
//	void getLastSequenceTest() {
//		int seq = lovDao.getLastSequence(this.lov.getLovGroup());
//		assertEquals(seq, this.lov.getSequence());
//	}
	
}
