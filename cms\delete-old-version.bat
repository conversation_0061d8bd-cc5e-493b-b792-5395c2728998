@echo off
set url=https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent
FOR /f "delims=" %%R in ('svn info --show-item=revision %url%') do @set svnRevision=%%R
echo SVN revision: %svnRevision%
set /a deletedVersion=%svnRevision% - 2
echo %deletedVersion%
if exist com.adins.esign.cms-0.0.1-SNAPSHOT-r%deletedVersion% (
	rd /s /q com.adins.esign.cms-0.0.1-SNAPSHOT-r%deletedVersion%
	ECHO com.adins.esign.cms-0.0.1-SNAPSHOT-r%deletedVersion% DELETED
)
if exist com.adins.esign.cms-0.0.1-SNAPSHOT-r%deletedVersion%.war (
	del /f /q com.adins.esign.cms-0.0.1-SNAPSHOT-r%deletedVersion%.war
	ECHO com.adins.esign.cms-0.0.1-SNAPSHOT-r%deletedVersion%.war DELETED
)
if exist com.adins.esign.cms-0.0.1-SNAPSHOT-r%deletedVersion%.war.original (
	del /f /q com.adins.esign.cms-0.0.1-SNAPSHOT-r%deletedVersion%.war.original
	ECHO com.adins.esign.cms-0.0.1-SNAPSHOT-r%deletedVersion%.war.original DELETED
)
