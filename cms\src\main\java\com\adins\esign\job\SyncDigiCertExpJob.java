package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class SyncDigiCertExpJob {
	private static final Logger LOG = LoggerFactory.getLogger(SyncDigiCertExpJob.class);
	private static final String SCHEDULER = "SCHEDULER";
	
	@Autowired private SchedulerLogic schedulerLogic;
	
	public void syncDigiCertExp() {
		try {
			LOG.info("Job Sync Digi Certificate Expired Date Started");
			AuditContext auditContext = new AuditContext(SCHEDULER);
			schedulerLogic.digiCertExpDateSync(auditContext);
			LOG.info("Job Sync Digi Certificate Expired Date Finished");
		} catch (Exception e) {
			LOG.error("Error on running Sync Digi Certificate Expired Date job", e);
		}
	}
}
