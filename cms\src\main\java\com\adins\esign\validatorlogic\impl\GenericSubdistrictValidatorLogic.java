package com.adins.esign.validatorlogic.impl;

import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsdistrict;
import com.adins.am.model.AmMssubdistrict;
import com.adins.esign.validatorlogic.api.SubdistrictValidatorLogic;
import com.adins.exceptions.LocationException;
import com.adins.exceptions.LocationException.ReasonLocation;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericSubdistrictValidatorLogic extends BaseLogic implements SubdistrictValidatorLogic {

	
	private String getMessages(String code, Object[] params, AuditContext audit) {
		return messageSource.getMessage(code, params, retrieveLocaleAudit(audit));
	}
	
	@Override
	public AmMssubdistrict validateGetSubdistrict(Long idMssubdistrict, AmMsdistrict district, boolean checkSubdistrictExistence, AuditContext audit) {
		AmMssubdistrict subdistrict = daoFactory.getSubDistrictDao().getSubdistrict(idMssubdistrict);
		if (checkSubdistrictExistence) {
			if (null == subdistrict) {
				throw new LocationException(getMessages("businesslogic.subdistrict.notfound",
						new Object[] {idMssubdistrict}, audit), ReasonLocation.SUBDISTRICT_NOT_FOUND);
			}
			if (!district.getDistrictName().equals(subdistrict.getAmMsdistrict().getDistrictName())) {
				String message = getMessages("businesslogic.subdistrict.invaliddistrict", new String[] {subdistrict.getSubdistrictName(), district.getDistrictName()}, audit);
				throw new LocationException(message, ReasonLocation.INVALID_DISTRICT);
			}
		}
		return subdistrict;
	}

}
