package com.adins.esign.businesslogic.impl.interfacing;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.mail.MessagingException;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.businesslogic.api.interfacing.EmateraiLogic;
import com.adins.esign.confins.model.DocumentToUploadBean;
import com.adins.esign.confins.model.UploadToCoreBean;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.ActivationDocumentBean;
import com.adins.esign.model.custom.BalanceBean;
import com.adins.esign.model.custom.DataReturnBean;
import com.adins.esign.model.custom.DownloadStampedDocRequest;
import com.adins.esign.model.custom.DownloadStampedDocResponse;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.EmeteraiBean;
import com.adins.esign.model.custom.GenerateTokenEmeteraiRequest;
import com.adins.esign.model.custom.GenerateTokenEmeteraiResponseBean;
import com.adins.esign.model.custom.SignLocationBean;
import com.adins.esign.model.custom.UploadDocumentEmeteraiResponseBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.BalanceRequest;
import com.adins.esign.webservices.model.GenerateEmeteraiRequest;
import com.adins.esign.webservices.model.GenerateEmeteraiResponse;
import com.adins.esign.webservices.model.StampingEmeteraiRequest;
import com.adins.esign.webservices.model.StampingEmeteraiResponse;
import com.adins.esign.webservices.model.UpdateStampDutyStatusResponse;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.exceptions.EmailException;
import com.adins.exceptions.EmailException.ReasonEmail;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.google.gson.Gson;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

@Transactional
@Component
public class GenericEmateraiLogic extends BaseLogic implements EmateraiLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericEmateraiLogic.class);
	
	private String headerXApiKey = "x-api-key";
    private String headerAuth = javax.ws.rs.core.HttpHeaders.AUTHORIZATION;
    private String headerContentType = javax.ws.rs.core.HttpHeaders.CONTENT_TYPE;
    private String headerJson = MediaType.APPLICATION_JSON;
    private String headerPdf = org.springframework.http.MediaType.APPLICATION_PDF_VALUE;
    
	@Value("${emeterai.clientid}") private String clientId;
	@Value("${emeterai.clientemail}") private String clientEmail;
	@Value("${emeterai.clientpassword}") private String clientPassword;
    @Value("${emeterai.auth.xapikey}") private String authXapikey;
	@Value("${emeterai.uri}") private String urlEmeterai;
	@Value("${emeterai.generatetoken.uri}") private String urlGenerateToken;
	@Value("${emeterai.uploaddocument.uri}") private String urlUploadDocument;
	@Value("${emeterai.generateemeterai.uri}") private String urlGenerateEmeterai;
	@Value("${emeterai.stampingemeterai.uri}") private String urlStampingEmeterai;
	@Value("${emeterai.downloaddocument.uri}") private String urlDownloadDocument;
	@Value("${spring.mail.username}") private String fromEmailAddr;
	
    @Autowired private Gson gson;
    @Autowired private MessageTemplateLogic messageTemplateLogic;
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private DocumentLogic documentLogic;
	@Autowired private DigisignLogic digisignLogic;
	
	@Override
	public UpdateStampDutyStatusResponse attachMeterai(TrDocumentH documentH, AuditContext audit) throws IOException {
		this.updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_PROCESS, null, audit);
		
		List<TrDocumentD> listDocumentD = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderId(documentH.getIdDocumentH());
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(listDocumentD.get(0).getMsTenant(), listDocumentD.get(0).getMsVendor());
		
		boolean isAllDocumentFullyStamped = true;
		
		GenerateTokenEmeteraiResponseBean generateTokenBean = this.generateTokenEmeterai(documentH, audit);
		if (!GlobalVal.STATUS_MCP_SUCCESS.equals(generateTokenBean.getCode())) {
			return new UpdateStampDutyStatusResponse();
		}
		
		String token = generateTokenBean.getData().getMcpToken().getToken().getJwt();
		LOG.info("Token E-Meterai {}", token);
		
		for (TrDocumentD documentD: listDocumentD) {
			if (null == documentD.getTotalMaterai() || null == documentD.getTotalStamping()) {
				continue;
			}
			
			if (0 == documentD.getTotalMaterai()) {
				this.updateStepAttachMeterai(documentD, GlobalVal.STEP_ATTACH_METERAI_NOT_SDT, audit);
				continue;
			}
			
			if (documentD.getTotalStamping() < documentD.getTotalMaterai()) {
				isAllDocumentFullyStamped = false;
				int count = documentD.getTotalStamping();
				boolean isError = false;
				
				while(count < documentD.getTotalMaterai() && !isError) {
					String stepMeterai = documentD.getSdtProcess();
				
					try {
						switch (stepMeterai) {
							case GlobalVal.STEP_ATTACH_METERAI_NOT_STR:
							case GlobalVal.STEP_ATTACH_METERAI_SDT_STR:
							case GlobalVal.STEP_ATTACH_METERAI_UPL_DOC:
								UploadDocumentEmeteraiResponseBean uploadDocbean = this.uploadDocumentEmeterai(documentD, documentH, vot, token, 
										"<EMAIL>", "documentID_eSignHub_1", "2021-12-20", "<EMAIL>", "Andy", "327101", "Jakarta", 
										"Test API", "Surat Berharga", audit);
								if (!GlobalVal.STATUS_MCP_SUCCESS.equals(uploadDocbean.getCode())) {
									isError = true;
									break;
								}
							case GlobalVal.STEP_ATTACH_METERAI_GEN_SDT:
								String statusGenEmeterai = this.generateEmeterai(documentH, documentD, count, audit);
								if (!GlobalVal.STATUS_MCP_SUCCESS.equals(statusGenEmeterai)) {
									isError = true;
									break;
								}
							case GlobalVal.STEP_ATTACH_METERAI_STM_SDT:
								String statusStamping = this.stampEmeterai(documentH, documentD, count, audit);
								if (!GlobalVal.STATUS_MCP_SUCCESS.equals(statusStamping)) {
									isError = true;
									break;
								}
							default:
								break;
						}
						
						if (isError) {
							this.updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_ERROR, null, audit);
							return new UpdateStampDutyStatusResponse();
						}
 					} catch (Exception e) {
						this.updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_ERROR, e, audit);
						return new UpdateStampDutyStatusResponse();
					}
					
					count++;
				}
				
			}
			
		}
		
		if (isAllDocumentFullyStamped) {
			this.uploadFullySignedDocument(documentH, audit);
			return new UpdateStampDutyStatusResponse();
		}
		
		return this.attachMeterai(documentH, audit);
	}
	
	private GenerateTokenEmeteraiResponseBean generateTokenEmeterai(TrDocumentH documentH, AuditContext audit) throws IOException {
		GenerateTokenEmeteraiResponseBean responseResult = new GenerateTokenEmeteraiResponseBean();
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerContentType, headerJson);
		mapHeader.add(headerXApiKey, authXapikey);
		WebClient client = WebClient.create(urlEmeterai + urlGenerateToken).headers(mapHeader);
		
		GenerateTokenEmeteraiRequest request = new GenerateTokenEmeteraiRequest();
		request.setClientId(clientId);
		request.setClientEmail(clientEmail);
		request.setClientPassword(clientPassword);
		
		String jsonRequest = gson.toJson(request);
		LOG.info("JSON request generate token e-meterai : {}", jsonRequest);
		Response response = null;
		try {
			response = client.post(jsonRequest);
		} catch (Exception e) {
			responseResult.setCode("200");
			return responseResult;
		}
		
		String emeteraiResult = StringUtils.EMPTY;
		try {
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			emeteraiResult = IOUtils.toString(isReader);
		} catch (IOException e) {
			responseResult.setCode("200");
			return responseResult;
		}
		LOG.info("JSON result generate token e-meterai : {}", emeteraiResult);
		
		try {
			responseResult = gson.fromJson(emeteraiResult, GenerateTokenEmeteraiResponseBean.class);
		} catch (Exception e) {
			responseResult.setCode("200");
			return responseResult;
		}
		
		if (!GlobalVal.STATUS_MCP_SUCCESS.equals(responseResult.getCode())) {
			responseResult.setMessage(responseResult.getMessage());
			responseResult.setCode(responseResult.getCode());
			return responseResult;
		}
		
		return responseResult;
	}
	
	private UploadDocumentEmeteraiResponseBean uploadDocumentEmeterai(TrDocumentD documentD, 
			TrDocumentH documentH, MsVendoroftenant vot, String token, String emailUploader, String docNumber, 
			String docDate, String emailStamper, String nameStamper, String ktpStamper, String docMeteraiLocation, 
			String docMeteraiReason, String jenisDocMeterai, AuditContext audit) throws IOException {
		UploadDocumentEmeteraiResponseBean responseResult = new UploadDocumentEmeteraiResponseBean();
		this.updateStepAttachMeterai(documentD, GlobalVal.STEP_ATTACH_METERAI_UPL_DOC, audit);
		String pdfBase64 = "";
		String transactionId = documentD.getTransactionId();
		String[] transactionIds = {};
		if (StringUtils.isNotBlank(transactionId)) {
			transactionIds = transactionId.split(";");
		}
		
		if(transactionIds.length >= documentD.getTotalMaterai()) {
			responseResult.setCode(GlobalVal.STATUS_MCP_SUCCESS);
			return responseResult;
		}
		
		if (transactionIds.length > 0) {
			DownloadStampedDocResponse downloadDocBean = this.downloadStampedDocument("62c26252-e49c-4c0b-9506-2189fe18eafa", 
					documentH, audit);
			
			// Original logic for transactionId
//			DownloadStampedDocResponse downloadDocBean = this.downloadStampedDocument(documentH, 
//					transactionIds[transactionIds.length - 1], audit);
			
			if (!GlobalVal.STATUS_MCP_SUCCESS.equals(downloadDocBean.getResponseCode())) {
				responseResult.setMessage(downloadDocBean.getMessage());
				responseResult.setCode(downloadDocBean.getResponseCode());
				return responseResult;
			}
						
			pdfBase64 = downloadDocBean.getDataReturn().getPdfFile();
		} else {
			ViewDocumentRequest viewDocRequest = new ViewDocumentRequest();
			viewDocRequest.setDocumentId(documentD.getDocumentId());
			ViewDocumentResponse viewDocResponse = new ViewDocumentResponse();
			try {
				viewDocResponse = digisignLogic.getDocumentFileDigisign(documentD, vot.getToken(), audit);
			} catch (Exception e) {
				responseResult.setCode("200");
				return responseResult;
			}
			pdfBase64 = viewDocResponse.getPdfBase64();
		}
		
		if (StringUtils.isNotBlank(pdfBase64)) {
			byte[] dataDocFile = Base64.getDecoder().decode(pdfBase64);
			
			OkHttpClient client = new OkHttpClient.Builder().connectTimeout(15L, TimeUnit.SECONDS)
					.writeTimeout(15L, TimeUnit.SECONDS)
					.readTimeout(15L, TimeUnit.SECONDS).build();
			
			RequestBody body = new okhttp3.MultipartBody.Builder().setType(okhttp3.MultipartBody.FORM)
					.addFormDataPart("client_id", clientId)
					.addFormDataPart("email_uploader", emailUploader)
					.addFormDataPart("doc_number", docNumber)
					.addFormDataPart("doc_date", docDate)
					.addFormDataPart("doc_upload", "TestDocument.pdf", RequestBody.create(okhttp3.MediaType.parse("application/pdf"), dataDocFile))
					.addFormDataPart("email_stamper", emailStamper)
					.addFormDataPart("name_stamper", nameStamper)
					.addFormDataPart("ktp_stamper", ktpStamper)
					.addFormDataPart("doc_meterai_location", docMeteraiLocation)
					.addFormDataPart("doc_meterai_reason", docMeteraiReason)
					.addFormDataPart("jenis_doc_meterai", jenisDocMeterai)
					.build();
			
			Request request = new Request.Builder()
					.url(urlEmeterai + urlUploadDocument)
					.addHeader(headerAuth, token)
					.addHeader(headerXApiKey, authXapikey)
					.post(body).build();
			
			okhttp3.Response response = null;
			try {
				 response = client.newCall(request).execute();
			} catch (Exception e) {
				responseResult.setCode("200");
				return responseResult;
			}
			String jsonResponse = response.body().string();
			LOG.info("JSON result upload doc emeterai : {}", jsonResponse);
			
			try {
				responseResult = gson.fromJson(jsonResponse, UploadDocumentEmeteraiResponseBean.class);
			} catch (Exception e) {
				responseResult.setCode("200");
				return responseResult;
			}
			
			if ((GlobalVal.STATUS_MCP_SUCCESS).equals(responseResult.getCode()) && StringUtils.isNotBlank(responseResult.getData().getTransactionId())) {
				String updatedtransactionId;
				if (StringUtils.isNotBlank(transactionId)) {
					updatedtransactionId = transactionId + ";" + responseResult.getData().getTransactionId();
				} else {
					updatedtransactionId = responseResult.getData().getTransactionId();
				}
				LOG.info("Updated Transaction Id JSON {}", updatedtransactionId);
				
				documentD.setUsrUpd(audit.getCallerId());
				documentD.setDtmUpd(new Date());
				documentD.setTransactionId(updatedtransactionId);
				documentD.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_GEN_SDT);
				daoFactory.getDocumentDao().updateDocumentDetail(documentD);
			}
			
		}
		
		return responseResult;
	}
	
	private GenerateEmeteraiResponse generateEmeteraiMcp(String transactionId, String meteraiPrice) throws IOException {
		GenerateEmeteraiResponse generateResponse = new GenerateEmeteraiResponse();
		String signatureKey = MssTool.getHashedString(clientId+transactionId);
		
		GenerateEmeteraiRequest request = new GenerateEmeteraiRequest();
		request.setClientId(clientId);
		request.setTransactionId(transactionId);
		request.setMeteraiPrice(meteraiPrice);
		request.setSignatureKey(signatureKey);
		String jsonRequest = gson.toJson(request);
		LOG.info("Generate emeterai json request: {}", jsonRequest);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerContentType, headerJson);
		mapHeader.add(headerXApiKey, authXapikey);
//		WebClient client = WebClient.create(urlEmeterai + urlGenerateEmeterai).headers(mapHeader);
//		Response response = client.post(request);
//		
//		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
//		String result = StringUtils.EMPTY;
//		try {
//			result = IOUtils.toString(isReader);
//		} catch (IOException e) {
//			throw new IOException(e);
//		}
//		LOG.info("JSON result generate emeterai : {}", result);
//		try {
//			generateResponse = gson.fromJson(result, GenerateEmeteraiResponse.class);
//		} catch (Exception e) {
//			LOG.error(e.getLocalizedMessage());
//		}
		
		generateResponse.setResponseCode(GlobalVal.STATUS_MCP_SUCCESS);
		generateResponse.setMessage("Generate meterai Success");
		generateResponse.setHttpResponse("201");
		EmeteraiBean bean = new EmeteraiBean();
		bean.setSnEmeterai("GH2LH7PRIP0G9I700000I9");
		bean.setCreatedAt("2021-12-23T10:29:13.738Z");
		generateResponse.setData(bean);
		
		return generateResponse;
	}
	
	private void sendInsufficientSdtBalanceEmail(String action, String refNo, String documentName, String balanceType, Integer saldo, String[] emailDest) {
		Map<String, Object> reminder = new HashMap<>();
		reminder.put("title", action);
		reminder.put("action", action);
		reminder.put("refNo", refNo);
		reminder.put("documentName", documentName);
		reminder.put("balanceType", balanceType);
		reminder.put("saldo", saldo);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("reminder", reminder);

		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_INSUFFICIENT_BAL, templateParameters);
		String[] recipient = emailDest;

		EmailInformationBean emailBean = new EmailInformationBean();
		emailBean.setFrom(fromEmailAddr);
		emailBean.setTo(recipient);
		emailBean.setBodyMessage(template.getBody());
		emailBean.setSubject(template.getSubject());

		try {
			emailSenderLogic.sendEmail(emailBean, null);
		} catch (MessagingException e) {
			throw new EmailException(ReasonEmail.SEND_EMAIL_ERROR);
		}
	}
	
	private String generateEmeterai(TrDocumentH docH, TrDocumentD document, int currentLoop, AuditContext audit) {
		
		String[] trxIds = document.getTransactionId().split(";");
		int availableSdt = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
		int sdtNeeded = trxIds.length - availableSdt;
		
		if (!GlobalVal.STEP_ATTACH_METERAI_GEN_SDT.equals(document.getSdtProcess())) {
			LOG.info("Document {} SDT process is not {} .", document.getDocumentId(), GlobalVal.STEP_ATTACH_METERAI_GEN_SDT);
			return GlobalVal.STATUS_MCP_SUCCESS;
		}
		
		if (availableSdt == trxIds.length) {
			LOG.info("E-Meterai is already available for document {}", document.getDocumentId());
			return GlobalVal.STATUS_MCP_SUCCESS;
		}
		
		MsTenant tenant = document.getMsTenant();
		MsVendor vendor = document.getMsVendor();
		MsLov balanceType = daoFactory.getLovDao()
				.getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		
		BalanceRequest balanceRequest = new BalanceRequest();
		balanceRequest.setTenantCode(tenant.getTenantCode());
		balanceRequest.setVendorCode(vendor.getVendorCode());
		balanceRequest.setBalanceType(GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		
		int sdtBalance = this.getSdtBalance(balanceRequest, audit);
		if (sdtBalance < sdtNeeded) {
			LOG.warn("Insufficient balance to generate E-Meterai");
			
			String[] recipient = tenant.getEmailReminderDest().split(",");
			
			this.sendInsufficientSdtBalanceEmail("generate e-Meterai", docH.getRefNumber(), document.getMsDocTemplate().getDocTemplateName(),
					balanceType.getDescription(), sdtBalance, recipient);
			
			docH.setProsesMaterai((short) 2);
			docH.setUsrUpd(audit.getCallerId());
			docH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentH(docH);
			return GlobalVal.STATUS_ATTACH_METERAI_ERROR;
		}
		
		GenerateEmeteraiResponse response = new GenerateEmeteraiResponse();
		try {
			response = this.generateEmeteraiMcp(trxIds[currentLoop], "10000");
		} catch (IOException e) {
			e.printStackTrace();
		}
		
		if (!GlobalVal.STATUS_MCP_SUCCESS.equals(response.getResponseCode())) {
			LOG.warn("Unsuccessfull generate e-Meterai");
			return GlobalVal.STATUS_ATTACH_METERAI_ERROR;
		}
		
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		MsLov sdtStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_AVAILABLE);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT);
		
		TrStampDuty stampDuty = new TrStampDuty();
		stampDuty.setTrxNo(trxNo);
		stampDuty.setStampDutyNo(response.getData().getSnEmeterai());
		stampDuty.setMsLov(sdtStatus);
		stampDuty.setUsrCrt(audit.getCallerId());
		stampDuty.setDtmCrt(new Date());
		stampDuty.setMsTenant(tenant);
		stampDuty.setMsVendor(vendor);
		stampDuty.setStampDutyFee(10000);
		daoFactory.getStampDutyDao().insertTrStampDuty(stampDuty);
		
		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setQty(0);
		mutation.setTrxDate(new Date());
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setMsTenant(tenant);
		mutation.setMsVendor(vendor);
		mutation.setTrDocumentD(document);
		mutation.setTrDocumentH(docH);
		mutation.setNotes(response.getData().getSnEmeterai());
		mutation.setTrStampDuty(stampDuty);
		mutation.setUsrCrt(audit.getCallerId());
		mutation.setDtmCrt(new Date());
		daoFactory.getBalanceMutationDao().insertTrBalanceMutation(mutation);
		

		this.updateStepAttachMeterai(document, GlobalVal.STEP_ATTACH_METERAI_STM_SDT, audit);
		return GlobalVal.STATUS_MCP_SUCCESS;
	}
	
	private int getSdtBalance(BalanceRequest request, AuditContext audit) {
		List<BalanceBean> result = saldoLogic.getBalanceNotSecure(request, audit).getListBalance();
		return result.get(0).getCurrentBalance().intValue();
	}
	
	private String stampEmeterai(TrDocumentH documentH, TrDocumentD document, int currentLoop, AuditContext audit) {
		if (!GlobalVal.STEP_ATTACH_METERAI_STM_SDT.equals(document.getSdtProcess())) {
			LOG.info("Document {} SDT process is not {} .", document.getDocumentId(), GlobalVal.STEP_ATTACH_METERAI_STM_SDT);
			return GlobalVal.STATUS_MCP_SUCCESS;
		}
		
		int sdtAvailable = daoFactory.getStampDutyDao().countAvailableStampDutyForDocument(document.getIdDocumentD()).intValue();
		if (sdtAvailable == 0) {
			LOG.info("No available stamp duty, move to the next process");
			return GlobalVal.STATUS_MCP_SUCCESS;
		}
		
		String[] trxIds = document.getTransactionId().split(";");
		int totalStamped = document.getTotalStamping();
		int sdtNeeded = trxIds.length - totalStamped;		
		
		List<Map<String, Object>> stampDutyIds = daoFactory.getStampDutyDao().getIdStampDutyForDocument(document.getIdDocumentD());
		List<MsDocTemplateSignLoc> sdtLocs = daoFactory.getDocumentDao()
				.getListSignLocationByTemplateCodeAndLovSignType(document.getMsDocTemplate().getDocTemplateCode(), GlobalVal.CODE_LOV_SIGN_TYPE_SDT);
		
		if (sdtNeeded != sdtAvailable) {
			LOG.info("Different amount of stamp duty needed and stamp duty available. Needed: {}, Available{}", sdtNeeded, sdtAvailable);
			return GlobalVal.STATUS_MCP_SUCCESS;
		}
		
		BigInteger idStampDuty = (BigInteger) stampDutyIds.get(currentLoop).get("d0");
		TrStampDuty stampDuty = daoFactory.getStampDutyDao().getStampDutyById(idStampDuty.longValue());
		if (!GlobalVal.CODE_LOV_SDT_AVAILABLE.equals(stampDuty.getMsLov().getCode())) {
			LOG.info("SDT with number {} is not available.", stampDuty.getStampDutyNo());
			return GlobalVal.STATUS_ATTACH_METERAI_ERROR;
		}
		
		MsDocTemplateSignLoc sdtLoc = sdtLocs.get(currentLoop);
		SignLocationBean coordinates = gson.fromJson(sdtLoc.getSignLocation(), SignLocationBean.class);
		
		StampingEmeteraiRequest request = new StampingEmeteraiRequest();
		request.setClientId(clientId);
		request.setSnEmeterai(stampDuty.getStampDutyNo());
		request.setTransactionId(trxIds[currentLoop]);
		request.setPage(sdtLoc.getSignPage());
		request.setLowerLeftX(Double.valueOf(coordinates.getLlx()));
		request.setLowerLeftY(Double.valueOf(coordinates.getLly()));
		request.setUpperRightX(Double.valueOf(coordinates.getUrx()));
		request.setUpperRightY(Double.valueOf(coordinates.getUry()));
		
		StampingEmeteraiResponse response = new StampingEmeteraiResponse();
		
		try {
			response = this.stampEmeteraiMcp(request);
		} catch (IOException e) {
			e.printStackTrace();
		}
		
		if (!GlobalVal.STATUS_MCP_SUCCESS.equals(response.getResponseCode())) {
			LOG.info("Stamping e-Meterai unsuccessfull with message: {}", response.getMessage());
			return GlobalVal.STATUS_ATTACH_METERAI_ERROR;
		}
		
		 // Update total stamp
		Integer totalStamping = totalStamped+1;
		document.setTotalStamping(totalStamping.shortValue());
		String step = (totalStamping == document.getTotalMaterai().intValue() ? 
				GlobalVal.STEP_ATTACH_METERAI_UPL_CON : GlobalVal.STEP_ATTACH_METERAI_UPL_DOC);
		this.updateStepAttachMeterai(document, step, audit);
		
		// Update SDT
		MsLov lovGoLive = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, GlobalVal.CODE_LOV_SDT_GO_LIVE);
		stampDuty.setMsLov(lovGoLive);
		stampDuty.setUsrUpd(audit.getCallerId());
		stampDuty.setDtmUpd(new Date());
		daoFactory.getStampDutyDao().updateNativeStringTrStampDuty(stampDuty);
		
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT);
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		
		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(trxNo);
		mutation.setTrxDate(new Date());
		mutation.setRefNo(documentH.getRefNumber());
		mutation.setQty(-1);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setMsTenant(document.getMsTenant());
		mutation.setMsVendor(document.getMsVendor());
		mutation.setTrDocumentD(document);
		mutation.setTrDocumentH(documentH);
		mutation.setNotes(stampDuty.getStampDutyNo());
		mutation.setTrStampDuty(stampDuty);
		mutation.setUsrCrt(audit.getCallerId());
		mutation.setDtmCrt(new Date());
		daoFactory.getBalanceMutationDao().insertTrBalanceMutation(mutation);
		
		return GlobalVal.STATUS_MCP_SUCCESS;
	}
	
	private StampingEmeteraiResponse stampEmeteraiMcp(StampingEmeteraiRequest request) throws IOException {
		StampingEmeteraiResponse stampingResponse = new StampingEmeteraiResponse();
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerContentType, headerJson);
		mapHeader.add(headerXApiKey, authXapikey);
//		WebClient client = WebClient.create(urlEmeterai + urlStampingEmeterai).headers(mapHeader);
//		
//		String jsonReq = gson.toJson(request);
//		LOG.info("Stamping Emeterai json request: {}", jsonReq);
//		
//		Response response = client.post(jsonReq);
//		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
//		String result = StringUtils.EMPTY;
//		try {
//			result = IOUtils.toString(isReader);
//		} catch (IOException e) {
//			throw new IOException(e);
//		}
//		LOG.info("Stamping Emeterai response: {}", result);
//		try {
//			stampingResponse = gson.fromJson(result, StampingEmeteraiResponse.class);
//		} catch(Exception e) {
//			LOG.error(e.getLocalizedMessage());
//		}
		
		stampingResponse.setResponseCode(GlobalVal.STATUS_MCP_SUCCESS);
		stampingResponse.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		return stampingResponse;
	}

	private Status uploadFullySignedDocument(TrDocumentH docH, AuditContext audit) throws IOException {
		UploadToCoreBean bean = this.prepareUploadToCoreData(docH, audit);
		return documentLogic.callUrlUpload(docH.getUrlUpload(), bean);
	}
	
	private DownloadStampedDocResponse downloadStampedDocument(String rawTrxIds, TrDocumentH docH, AuditContext audit) throws IOException {
		DownloadStampedDocResponse responseResult = new DownloadStampedDocResponse();
		String[] trxIds = rawTrxIds.split(";");
		
		DownloadStampedDocRequest mcpRequest = new DownloadStampedDocRequest();
		mcpRequest.setTransactionId(trxIds[trxIds.length - 1]);
		mcpRequest.setClientId(clientId);
		String mcpRequestString = gson.toJson(mcpRequest);
		LOG.info("JSON request download document e-meterai : {}", mcpRequestString);
		
		String url = urlEmeterai + urlDownloadDocument;
		GenerateTokenEmeteraiResponseBean token = this.generateTokenEmeterai(docH, audit);
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerContentType, headerJson);
		mapHeader.add(headerXApiKey, authXapikey);
		mapHeader.add(headerAuth, token.getData().getMcpToken().getToken().getJwt());
		WebClient client = WebClient.create(url).headers(mapHeader);
		
		Response response = null;
		try {
			response = client.post(mcpRequestString);
		} catch (Exception e) {
			responseResult.setResponseCode("200");
			return responseResult;
		}
		
		MultivaluedMap<String, Object> responseHeader = response.getHeaders();
		String responseContentType = responseHeader.get(headerContentType).toString();
		responseContentType = responseContentType.substring(1, responseContentType.length() - 1);
		
		if (headerPdf.equalsIgnoreCase(responseContentType)) {
			InputStream is = (InputStream) response.getEntity();
			String base64String;
			try {
				byte[] pdfBytes = org.apache.commons.io.IOUtils.toByteArray(is);
				base64String = Base64.getEncoder().encodeToString(pdfBytes);
				responseResult.setResponseCode(GlobalVal.STATUS_MCP_SUCCESS);
				DataReturnBean bean = new DataReturnBean();
				bean.setPdfFile(base64String);
				responseResult.setDataReturn(bean);
			} catch (Exception e) {
				responseResult.setResponseCode("200");
				return responseResult;
			}
		} else {
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			
			String result = StringUtils.EMPTY;
			try {
				result = IOUtils.toString(isReader);
			} catch (IOException e) {
				responseResult.setMessage(responseResult.getMessage());
				responseResult.setResponseCode("200");
				return responseResult;
			}
			LOG.info("JSON result download document e-meterai : {}", result);
			
			try {
				responseResult = gson.fromJson(result, DownloadStampedDocResponse.class);
			} catch (Exception e) {
				responseResult.setMessage(responseResult.getMessage());
				responseResult.setResponseCode("200");
				return responseResult;
			}
		}
		
		if (!GlobalVal.STATUS_MCP_SUCCESS.equals(responseResult.getResponseCode())) {
			responseResult.setMessage(responseResult.getMessage());
			responseResult.setResponseCode(responseResult.getResponseCode());
			return responseResult;
		}
		
		return responseResult;
	}
	
	private UploadToCoreBean prepareUploadToCoreData(TrDocumentH docH, AuditContext audit) throws IOException {
		List<DocumentToUploadBean> listUpload = new ArrayList<>();
		List<ActivationDocumentBean> listDocD = daoFactory.getDocumentDao().getActivationDocumentByDocHAndUser(docH, null);
		for (ActivationDocumentBean bean : listDocD) {
			DocumentToUploadBean uploadBean = new DocumentToUploadBean();
			uploadBean.setDocTypeTc(bean.getDocumentTemplateCode());
			uploadBean.setDisplayName(bean.getDocTemplateName());
			
			if (bean.getTotalMeterai() > 0) {
				DownloadStampedDocResponse response = this.downloadStampedDocument(bean.getTransactionIds(), docH, audit);
				uploadBean.setContent(response.getDataReturn().getPdfFile());
			} else {
				TrDocumentD document = daoFactory.getDocumentDao().getDocumentDetailByDocId(bean.getDocumentId());
				ViewDocumentResponse response = digisignLogic.getDocumentFile(document, audit);
				uploadBean.setContent(response.getPdfBase64());
			}
			
			String filename = GlobalVal.PREFIX_DOCUMENT_FILE_NAME + bean.getDocumentId() + ".pdf";
			uploadBean.setFileName(filename);
			
			listUpload.add(uploadBean);
		}
		
		UploadToCoreBean bean = new UploadToCoreBean();
		bean.setDocumentObjs(listUpload);
		bean.setRefNo(docH.getRefNumber());
		
		return bean;
	}
	
	@Override
	public String updateStatusProcessMeterai(TrDocumentH documentH, String statusAttachMeterai, Exception e, AuditContext audit) {
		if (GlobalVal.STATUS_ATTACH_METERAI_ERROR.equals(statusAttachMeterai)) {
			LOG.error("Error on attach meterai");
		}
		if (null != e) {
			LOG.error(e.getLocalizedMessage());
			e.printStackTrace();
		}

		documentH.setProsesMaterai(new Short(statusAttachMeterai));
		documentH.setDtmUpd(new Date());
		documentH.setUsrUpd(audit.getCallerId());
		daoFactory.getDocumentDao().updateDocumentH(documentH);

		return statusAttachMeterai;
	}
	
	private String updateStepAttachMeterai(TrDocumentD documentD, String step, AuditContext audit) {
		documentD.setDtmUpd(new Date());
		documentD.setUsrUpd(audit.getCallerId());
		documentD.setSdtProcess(step);
		daoFactory.getDocumentDao().updateDocumentDetail(documentD);
		
		return step;
	}
}
