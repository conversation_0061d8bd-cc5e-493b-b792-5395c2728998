package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class SubmitTaskException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum Reason {
        DELETED,
        ERROR_SUBMIT,
        RV_USED,
        SAVE_DRAFT_INVALID_DATA
    }

	private final Reason reason;	
	
	public Reason getReason() {
		return reason;
	}	
    
	public SubmitTaskException(Reason reason) {
		super();
		this.reason = reason;
	}

	public SubmitTaskException(String message, Throwable cause, Reason reason) {
		super(message, cause);
		this.reason = reason;
	}

	public SubmitTaskException(String message, Reason reason) {
		super(message);
		this.reason = reason;
	}

	public SubmitTaskException(Throwable cause, Reason reason) {
		super(cause);
		this.reason = reason;
	}

	@Override
	public int getErrorCode() {
		switch (reason) {
			case DELETED:
				return StatusCode.SUBMIT_DELETED;
			case ERROR_SUBMIT:
				return StatusCode.ERROR_SUBMIT;
			case RV_USED:
				return StatusCode.ERROR_RV_ALREADY_USED;
			case SAVE_DRAFT_INVALID_DATA:
				return StatusCode.SUBMIT_INVALID_DATA;
		}
		return StatusCode.SUBMIT_ERROR;
	}

}
