package com.adins.am.model;
// Generated 09-Sep-2021 22:49:32 by Hibernate Tools 5.2.12.Final

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.am.model.custom.ActiveDeleteAndUpdateableEntity;
import com.adins.esign.model.MsEmailHosting;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrClientCallbackRequest;
import com.adins.esign.model.TrDocumentDSign;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrDocumentSigningRequest;
import com.adins.esign.model.TrFaceVerify;
import com.adins.esign.model.TrFeedback;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.TrJobResult;
import com.adins.esign.model.TrJobUpdatePsreId;
import com.adins.esign.model.TrSignLinkRequest;
import com.adins.esign.model.TrUrlForwarder;

/**
 * AmMsuser generated by hbm2java
 */
@Entity
@Table(name = "am_msuser")
public class AmMsuser extends ActiveDeleteAndUpdateableEntity  implements java.io.Serializable {
	private static final long serialVersionUID = 1L;

	public static final String ID_MS_USER_HBM = "idMsUser";
	public static final String LOGIN_ID_HBM = "loginId";
	public static final String FULLNAME_HBM = "fullName";
	public static final String HASHED_PHONE_HBM = "hashedPhone";
	public static final String OTP_CODE_HBM = "otpCode";
	public static final String HASHED_IDNO_HBM = "hashedIdNo";
	public static final String PASSWORD_HBM = "password";
	public static final String AM_MSUSER_HBM = "amMsuser";

	private long idMsUser;
	private MsOffice msOffice;
	private MsEmailHosting msEmailHosting;
	private String loginId;
	private String fullName;
	private String initialName;
	private String loginProvider;
	private String password;
	private Integer failCount;
	private String isLoggedIn;
	private String isLocked;
	private String isDormant;
	private Date lastLoggedIn;
	private Date lastLocked;
	private Date lastExpired;
	private Date lastDormant;
	private Date lastLoggedFail;
	private Date lastRequestOut;
	private Date prevLoggedIn;
	private Date prevLoggedFail;
	private String changePwdLogin;
	private String resetCode;
	private String otpCode;
	private String emailService;
	private Date resetCodeRequestDate;
	private Short resetCodeRequestNum;
	private String hashedPhone;
	private String hashedIdNo;
	private String vendorResetPassLink;
	private String dataChangeRequest;
	private String activationLink;
	private String reregistrationLink;
	private Date livenessFacecompareRequestDate;
	private Short livenessFacecompareRequestNum;
	private Date livenessFacecompareValidationDate;
	private Short livenessFacecompareValidationNum;
	private Set<TrDocumentDSign> trDocumentDSigns = new HashSet<>(0);
	private Set<TrDocumentH> trDocumentHsForIdMsuserCustomer = new HashSet<>(0);


	private Set<AmUserpwdhistory> amUserpwdhistories = new HashSet<>(0);
	private Set<MsVendorRegisteredUser> msVendorRegisteredUsers = new HashSet<>(0);
	private Set<MsUseroftenant> msUseroftenants = new HashSet<>(0);
	private Set<TrFeedback> trFeedbacks = new HashSet<>(0);
	private Set<AmMemberofrole> amMemberofroles = new HashSet<>(0);
	private Set<AmAuditlog> amAuditlogs = new HashSet<>(0);
	private Set<AmUserPersonalData> amUserPersonalDatas = new HashSet<>(0);
	private Set<TrDocumentH> trDocumentHsForIdMsuserRequestBy = new HashSet<>(0);
	private Set<TrBalanceMutation> trBalanceMutations = new HashSet<>(0);
	private Set<TrFaceVerify> trFaceVerifies = new HashSet<>(0);
	private Set<TrInvitationLink> trInvitationLinks = new HashSet<>(0);
	private Set<TrJobResult> trJobResults = new HashSet<>(0);
	private Set<TrDocumentSigningRequest> trDocumentSigningRequests = new HashSet<>(0);
	private Set<TrSignLinkRequest> trSignLinkRequests = new HashSet<>(0);
	private Set<TrJobUpdatePsreId> trJobUpdatePsreIds = new HashSet<>(0);
	private Set<TrClientCallbackRequest> trClientCallbackRequests = new HashSet<>(0);
	private Set<TrUrlForwarder> trUrlForwarders = new HashSet<>(0);

	
	public AmMsuser(AmMsuser clone) {
        this.idMsUser = clone.idMsUser;
        this.msOffice = clone.msOffice;
        this.msEmailHosting = clone.msEmailHosting;
        this.loginId = clone.loginId;
        this.fullName = clone.fullName;
        this.initialName = clone.initialName;
        this.loginProvider = clone.loginProvider;
        this.password = clone.password;
        this.failCount = clone.failCount;
        this.isLoggedIn = clone.isLoggedIn;
        this.isLocked = clone.isLocked;
        this.lastLoggedIn = clone.lastLoggedIn;
        this.lastLocked = clone.lastLocked;
        this.lastExpired = clone.lastExpired;
        this.lastDormant = clone.lastDormant;
        this.lastLoggedFail = clone.lastLoggedFail;
        this.lastRequestOut = clone.lastRequestOut;
        this.prevLoggedIn = clone.prevLoggedIn;
        this.prevLoggedFail = clone.prevLoggedFail;
        this.changePwdLogin = clone.changePwdLogin;
        this.resetCode = clone.resetCode;
        this.otpCode = clone.otpCode;
        this.emailService = clone.emailService;
        this.resetCodeRequestDate = clone.resetCodeRequestDate;
        this.resetCodeRequestNum = clone.resetCodeRequestNum;
        this.hashedPhone = clone.hashedPhone;
        this.hashedIdNo = clone.hashedIdNo;
        this.vendorResetPassLink = clone.vendorResetPassLink;
        this.dataChangeRequest = clone.dataChangeRequest;
        this.activationLink = clone.activationLink;
        this.reregistrationLink = clone.reregistrationLink;
        this.livenessFacecompareRequestDate = clone.livenessFacecompareRequestDate;
        this.livenessFacecompareRequestNum = clone.livenessFacecompareRequestNum;
        this.livenessFacecompareValidationDate = clone.livenessFacecompareValidationDate;
        this.livenessFacecompareValidationNum = clone.livenessFacecompareValidationNum;
        this.isActive = clone.isActive;
        this.isDeleted = clone.isDeleted;
        this.isDormant = clone.isDormant;
        this.usrCrt = clone.usrCrt;
		this.dtmCrt = clone.dtmCrt;
		this.usrUpd = clone.usrUpd;
		this.dtmUpd = clone.dtmUpd;
    }
	
	public AmMsuser() {
	}

	public AmMsuser(long idMsUser, String fullName, String password, String usrCrt, Date dtmCrt) {
		this.idMsUser = idMsUser;
		this.fullName = fullName;
		this.password = password;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
	}

	public AmMsuser(long idMsUser, MsOffice msOffice, MsEmailHosting msEmailHosting, String loginId, String fullName,
			String initialName, String loginProvider, String password, Integer failCount, String isLoggedIn,
			String isLocked, String isDormant, Date lastLoggedIn, Date lastLocked, Date lastExpired, Date lastDormant,
			Date lastLoggedFail, Date lastRequestOut, Date prevLoggedIn, Date prevLoggedFail, String changePwdLogin,
			String resetCode, String otpCode, String emailService, Date resetCodeRequestDate, Short resetCodeRequestNum,
			String hashedPhone, String hashedIdNo, String vendorResetPassLink, String dataChangeRequest,
			String activationLink, String reregistrationLink, Date livenessFacecompareRequestDate,
			Short livenessFacecompareRequestNum, Set<TrDocumentDSign> trDocumentDSigns,
			Set<TrDocumentH> trDocumentHsForIdMsuserCustomer, Set<AmUserpwdhistory> amUserpwdhistories,
			Set<MsVendorRegisteredUser> msVendorRegisteredUsers, Set<MsUseroftenant> msUseroftenants,
			Set<TrFeedback> trFeedbacks, Set<AmMemberofrole> amMemberofroles, Set<AmAuditlog> amAuditlogs,
			Set<AmUserPersonalData> amUserPersonalDatas, Set<TrDocumentH> trDocumentHsForIdMsuserRequestBy,
			Set<TrBalanceMutation> trBalanceMutations, Set<TrFaceVerify> trFaceVerifies,
			Set<TrInvitationLink> trInvitationLinks, Set<TrJobResult> trJobResults) {
		super();
		this.idMsUser = idMsUser;
		this.msOffice = msOffice;
		this.msEmailHosting = msEmailHosting;
		this.loginId = loginId;
		this.fullName = fullName;
		this.initialName = initialName;
		this.loginProvider = loginProvider;
		this.password = password;
		this.failCount = failCount;
		this.isLoggedIn = isLoggedIn;
		this.isLocked = isLocked;
		this.isDormant = isDormant;
		this.lastLoggedIn = lastLoggedIn;
		this.lastLocked = lastLocked;
		this.lastExpired = lastExpired;
		this.lastDormant = lastDormant;
		this.lastLoggedFail = lastLoggedFail;
		this.lastRequestOut = lastRequestOut;
		this.prevLoggedIn = prevLoggedIn;
		this.prevLoggedFail = prevLoggedFail;
		this.changePwdLogin = changePwdLogin;
		this.resetCode = resetCode;
		this.otpCode = otpCode;
		this.emailService = emailService;
		this.resetCodeRequestDate = resetCodeRequestDate;
		this.resetCodeRequestNum = resetCodeRequestNum;
		this.hashedPhone = hashedPhone;
		this.hashedIdNo = hashedIdNo;
		this.vendorResetPassLink = vendorResetPassLink;
		this.dataChangeRequest = dataChangeRequest;
		this.activationLink = activationLink;
		this.reregistrationLink = reregistrationLink;
		this.livenessFacecompareRequestDate = livenessFacecompareRequestDate;
		this.livenessFacecompareRequestNum = livenessFacecompareRequestNum;
		this.trDocumentDSigns = trDocumentDSigns;
		this.trDocumentHsForIdMsuserCustomer = trDocumentHsForIdMsuserCustomer;
		this.amUserpwdhistories = amUserpwdhistories;
		this.msVendorRegisteredUsers = msVendorRegisteredUsers;
		this.msUseroftenants = msUseroftenants;
		this.trFeedbacks = trFeedbacks;
		this.amMemberofroles = amMemberofroles;
		this.amAuditlogs = amAuditlogs;
		this.amUserPersonalDatas = amUserPersonalDatas;
		this.trDocumentHsForIdMsuserRequestBy = trDocumentHsForIdMsuserRequestBy;
		this.trBalanceMutations = trBalanceMutations;
		this.trFaceVerifies = trFaceVerifies;
		this.trInvitationLinks = trInvitationLinks;
		this.trJobResults = trJobResults;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_user", unique = true, nullable = false)
	public long getIdMsUser() {
		return this.idMsUser;
	}

	public void setIdMsUser(long idMsUser) {
		this.idMsUser = idMsUser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_office")
	public MsOffice getMsOffice() {
		return this.msOffice;
	}

	public void setMsOffice(MsOffice msOffice) {
		this.msOffice = msOffice;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_email_hosting")
	public MsEmailHosting getMsEmailHosting() {
		return msEmailHosting;
	}

	public void setMsEmailHosting(MsEmailHosting msEmailHosting) {
		this.msEmailHosting = msEmailHosting;
	}

	@Column(name = "login_id", length = 80)
	public String getLoginId() {
		return this.loginId;
	}

	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}

	@Column(name = "full_name", nullable = false, length = 80)
	public String getFullName() {
		return this.fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	@Column(name = "initial_name", length = 15)
	public String getInitialName() {
		return this.initialName;
	}

	public void setInitialName(String initialName) {
		this.initialName = initialName;
	}

	@Column(name = "login_provider", length = 6)
	public String getLoginProvider() {
		return this.loginProvider;
	}

	public void setLoginProvider(String loginProvider) {
		this.loginProvider = loginProvider;
	}

	@Column(name = "password", nullable = false, length = 200)
	public String getPassword() {
		return this.password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	@Column(name = "fail_count")
	public Integer getFailCount() {
		return this.failCount;
	}

	public void setFailCount(Integer failCount) {
		this.failCount = failCount;
	}

	@Column(name = "is_logged_in", length = 1)
	public String getIsLoggedIn() {
		return this.isLoggedIn;
	}

	public void setIsLoggedIn(String isLoggedIn) {
		this.isLoggedIn = isLoggedIn;
	}

	@Column(name = "is_locked", length = 1)
	public String getIsLocked() {
		return this.isLocked;
	}

	public void setIsLocked(String isLocked) {
		this.isLocked = isLocked;
	}

	@Column(name = "is_dormant", length = 1)
	public String getIsDormant() {
		return this.isDormant;
	}

	public void setIsDormant(String isDormant) {
		this.isDormant = isDormant;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_logged_in", length = 29)
	public Date getLastLoggedIn() {
		return this.lastLoggedIn;
	}

	public void setLastLoggedIn(Date lastLoggedIn) {
		this.lastLoggedIn = lastLoggedIn;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_locked", length = 29)
	public Date getLastLocked() {
		return this.lastLocked;
	}

	public void setLastLocked(Date lastLocked) {
		this.lastLocked = lastLocked;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_expired", length = 29)
	public Date getLastExpired() {
		return this.lastExpired;
	}

	public void setLastExpired(Date lastExpired) {
		this.lastExpired = lastExpired;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_dormant", length = 29)
	public Date getLastDormant() {
		return this.lastDormant;
	}

	public void setLastDormant(Date lastDormant) {
		this.lastDormant = lastDormant;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_logged_fail", length = 29)
	public Date getLastLoggedFail() {
		return this.lastLoggedFail;
	}

	public void setLastLoggedFail(Date lastLoggedFail) {
		this.lastLoggedFail = lastLoggedFail;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_request_out", length = 29)
	public Date getLastRequestOut() {
		return this.lastRequestOut;
	}

	public void setLastRequestOut(Date lastRequestOut) {
		this.lastRequestOut = lastRequestOut;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "prev_logged_in", length = 29)
	public Date getPrevLoggedIn() {
		return this.prevLoggedIn;
	}

	public void setPrevLoggedIn(Date prevLoggedIn) {
		this.prevLoggedIn = prevLoggedIn;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "prev_logged_fail", length = 29)
	public Date getPrevLoggedFail() {
		return this.prevLoggedFail;
	}

	public void setPrevLoggedFail(Date prevLoggedFail) {
		this.prevLoggedFail = prevLoggedFail;
	}

	@Column(name = "change_pwd_login", length = 1)
	public String getChangePwdLogin() {
		return this.changePwdLogin;
	}

	public void setChangePwdLogin(String changePwdLogin) {
		this.changePwdLogin = changePwdLogin;
	}

	@Column(name = "reset_code", length = 40)
	public String getResetCode() {
		return this.resetCode;
	}

	public void setResetCode(String resetCode) {
		this.resetCode = resetCode;
	}
	
	@Column(name = "otp_code", length = 10)
	public String getOtpCode() {
		return otpCode;
	}

	public void setOtpCode(String otpCode) {
		this.otpCode = otpCode;
	}


	@Column(name = "email_service", length = 20)
	public String getEmailService() {
		return this.emailService;
	}

	public void setEmailService(String emailService) {
		this.emailService = emailService;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "reset_code_request_date", length = 29)
	public Date getResetCodeRequestDate() {
		return this.resetCodeRequestDate;
	}

	public void setResetCodeRequestDate(Date resetCodeRequestDate) {
		this.resetCodeRequestDate = resetCodeRequestDate;
	}

	@Column(name = "reset_code_request_num")
	public Short getResetCodeRequestNum() {
		return this.resetCodeRequestNum;
	}

	public void setResetCodeRequestNum(Short resetCodeRequestNum) {
		this.resetCodeRequestNum = resetCodeRequestNum;
	}

	@Column(name = "hashed_phone", length = 200)
	public String getHashedPhone() {
		return this.hashedPhone;
	}

	public void setHashedPhone(String hashedPhone) {
		this.hashedPhone = hashedPhone;
	}

	@Column(name = "hashed_id_no", length = 200)
	public String getHashedIdNo() {
		return this.hashedIdNo;
	}

	public void setHashedIdNo(String hashedIdNo) {
		this.hashedIdNo = hashedIdNo;
	}

	@Column(name = "vendor_reset_pass_link", length = 200)
	public String getVendorResetPassLink() {
		return vendorResetPassLink;
	}

	public void setVendorResetPassLink(String vendorResetPassLink) {
		this.vendorResetPassLink = vendorResetPassLink;
	}
	
	@Column(name = "data_change_request", length = 20)
	public String getDataChangeRequest() {
		return this.dataChangeRequest;
	}

	public void setDataChangeRequest(String dataChangeRequest) {
		this.dataChangeRequest = dataChangeRequest;
	}
	
	@Column(name = "activation_link", length = 300)
	public String getActivationLink() {
		return activationLink;
	}

	public void setActivationLink(String activationLink) {
		this.activationLink = activationLink;
	}
	
	@Column(name = "reregistration_link", length = 300)
	public String getReregistrationLink() {
		return reregistrationLink;
	}

	public void setReregistrationLink(String reregistrationLink) {
		this.reregistrationLink = reregistrationLink;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "liveness_facecompare_request_date", length = 29)
	public Date getLivenessFacecompareRequestDate() {
		return livenessFacecompareRequestDate;
	}

	public void setLivenessFacecompareRequestDate(Date livenessFacecompareRequestDate) {
		this.livenessFacecompareRequestDate = livenessFacecompareRequestDate;
	}

	@Column(name = "liveness_facecompare_request_num")
	public Short getLivenessFacecompareRequestNum() {
		return livenessFacecompareRequestNum;
	}

	public void setLivenessFacecompareRequestNum(Short livenessFacecompareRequestNum) {
		this.livenessFacecompareRequestNum = livenessFacecompareRequestNum;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "liveness_facecompare_validation_date", length = 29)
	public Date getLivenessFacecompareValidationDate() {
		return livenessFacecompareValidationDate;
	}

	public void setLivenessFacecompareValidationDate(Date livenessFacecompareValidationDate) {
		this.livenessFacecompareValidationDate = livenessFacecompareValidationDate;
	}

	@Column(name = "liveness_facecompare_validation_num")
	public Short getLivenessFacecompareValidationNum() {
		return livenessFacecompareValidationNum;
	}

	public void setLivenessFacecompareValidationNum(Short livenessFacecompareValidationNum) {
		this.livenessFacecompareValidationNum = livenessFacecompareValidationNum;
	}
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrDocumentDSign> getTrDocumentDSigns() {
		return this.trDocumentDSigns;
	}

	public void setTrDocumentDSigns(Set<TrDocumentDSign> trDocumentDSigns) {
		this.trDocumentDSigns = trDocumentDSigns;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuserByIdMsuserCustomer")
	public Set<TrDocumentH> getTrDocumentHsForIdMsuserCustomer() {
		return this.trDocumentHsForIdMsuserCustomer;
	}

	public void setTrDocumentHsForIdMsuserCustomer(Set<TrDocumentH> trDocumentHsForIdMsuserCustomer) {
		this.trDocumentHsForIdMsuserCustomer = trDocumentHsForIdMsuserCustomer;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<AmUserpwdhistory> getAmUserpwdhistories() {
		return this.amUserpwdhistories;
	}

	public void setAmUserpwdhistories(Set<AmUserpwdhistory> amUserpwdhistories) {
		this.amUserpwdhistories = amUserpwdhistories;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<MsVendorRegisteredUser> getMsVendorRegisteredUsers() {
		return this.msVendorRegisteredUsers;
	}

	public void setMsVendorRegisteredUsers(Set<MsVendorRegisteredUser> msVendorRegisteredUsers) {
		this.msVendorRegisteredUsers = msVendorRegisteredUsers;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<MsUseroftenant> getMsUseroftenants() {
		return this.msUseroftenants;
	}

	public void setMsUseroftenants(Set<MsUseroftenant> msUseroftenants) {
		this.msUseroftenants = msUseroftenants;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrFeedback> getTrFeedbacks() {
		return this.trFeedbacks;
	}

	public void setTrFeedbacks(Set<TrFeedback> trFeedbacks) {
		this.trFeedbacks = trFeedbacks;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<AmMemberofrole> getAmMemberofroles() {
		return this.amMemberofroles;
	}

	public void setAmMemberofroles(Set<AmMemberofrole> amMemberofroles) {
		this.amMemberofroles = amMemberofroles;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<AmAuditlog> getAmAuditlogs() {
		return this.amAuditlogs;
	}

	public void setAmAuditlogs(Set<AmAuditlog> amAuditlogs) {
		this.amAuditlogs = amAuditlogs;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<AmUserPersonalData> getAmUserPersonalDatas() {
		return this.amUserPersonalDatas;
	}

	public void setAmUserPersonalDatas(Set<AmUserPersonalData> amUserPersonalDatas) {
		this.amUserPersonalDatas = amUserPersonalDatas;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuserByIdMsuserRequestBy")
	public Set<TrDocumentH> getTrDocumentHsForIdMsuserRequestBy() {
		return this.trDocumentHsForIdMsuserRequestBy;
	}

	public void setTrDocumentHsForIdMsuserRequestBy(Set<TrDocumentH> trDocumentHsForIdMsuserRequestBy) {
		this.trDocumentHsForIdMsuserRequestBy = trDocumentHsForIdMsuserRequestBy;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrBalanceMutation> getTrBalanceMutations() {
		return this.trBalanceMutations;
	}

	public void setTrBalanceMutations(Set<TrBalanceMutation> trBalanceMutations) {
		this.trBalanceMutations = trBalanceMutations;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrFaceVerify> getTrFaceVerifies() {
		return this.trFaceVerifies;
	}

	public void setTrFaceVerifies(Set<TrFaceVerify> trFaceVerifies) {
		this.trFaceVerifies = trFaceVerifies;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrInvitationLink> getTrInvitationLinks() {
		return this.trInvitationLinks;
	}

	public void setTrInvitationLinks(Set<TrInvitationLink> trInvitationLinks) {
		this.trInvitationLinks = trInvitationLinks;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrJobResult> getTrJobResults() {
		return trJobResults;
	}

	public void setTrJobResults(Set<TrJobResult> trJobResults) {
		this.trJobResults = trJobResults;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrDocumentSigningRequest> getTrDocumentSigningRequests() {
		return trDocumentSigningRequests;
	}

	public void setTrDocumentSigningRequests(Set<TrDocumentSigningRequest> trDocumentSigningRequests) {
		this.trDocumentSigningRequests = trDocumentSigningRequests;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrSignLinkRequest> getTrSignLinkRequests() {
		return trSignLinkRequests;
	}

	public void setTrSignLinkRequests(Set<TrSignLinkRequest> trSignLinkRequests) {
		this.trSignLinkRequests = trSignLinkRequests;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrJobUpdatePsreId> getTrJobUpdatePsreIds() {
		return trJobUpdatePsreIds;
	}

	public void setTrJobUpdatePsreIds(Set<TrJobUpdatePsreId> trJobUpdatePsreIds) {
		this.trJobUpdatePsreIds = trJobUpdatePsreIds;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrClientCallbackRequest> getTrClientCallbackRequests() {
		return trClientCallbackRequests;
	}

	public void setTrClientCallbackRequests(Set<TrClientCallbackRequest> trClientCallbackRequests) {
		this.trClientCallbackRequests = trClientCallbackRequests;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsuser")
	public Set<TrUrlForwarder> getTrUrlForwarders() {
		return trUrlForwarders;
	}

	public void setTrUrlForwarders(Set<TrUrlForwarder> trUrlForwarders) {
		this.trUrlForwarders = trUrlForwarders;
	}
	
	
}
