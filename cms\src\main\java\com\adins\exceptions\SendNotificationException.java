package com.adins.exceptions;

import com.adins.exceptions.UserException.ReasonUser;
import com.adins.framework.exception.AdInsException;

public class SendNotificationException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonSendNotif {
        NOTIF_MEDIA_INVALID,
		NOTIF_GATEWAY_AND_MESSAGE_MEDIA_NOT_VALID,
		NOTIF_DAILY_LIMIT_REACHED,
		NOTIF_PERIOD_LIMIT_REACHED,
		NOTIF_LIMIT_NOT_SET
    }

    private final ReasonSendNotif reason;

    public SendNotificationException(ReasonSendNotif reason) {
		this.reason = reason;
	}

    public SendNotificationException(String message, ReasonSendNotif reason) {
		super(message);
		this.reason = reason;
	}

	public SendNotificationException(Throwable ex, ReasonSendNotif reason) {
		super(ex);
		this.reason = reason;
	}

	public SendNotificationException(String message, Throwable ex, ReasonSendNotif reason) {
		super(message, ex);
		this.reason = reason;
	}

	public ReasonSendNotif getReason() {
		return reason;
	}

    @Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
			case NOTIF_MEDIA_INVALID:
				return StatusCode.SEND_NOTIFICATION_MEDIA_NOT_VALID;
			case NOTIF_GATEWAY_AND_MESSAGE_MEDIA_NOT_VALID:
				return StatusCode.NOTIF_GATEWAY_AND_MESSAGE_MEDIA_NOT_VALID;
			case NOTIF_DAILY_LIMIT_REACHED:
				return StatusCode.NOTIF_DAILY_LIMIT_REACHED;
			case NOTIF_PERIOD_LIMIT_REACHED:
				return StatusCode.NOTIF_PERIOD_LIMIT_REACHED;
			case NOTIF_LIMIT_NOT_SET:
				return StatusCode.NOTIF_LIMIT_NOT_SET;
			default:
				return StatusCode.UNKNOWN;
			}
			
		}
		return StatusCode.UNKNOWN;
	}



}
