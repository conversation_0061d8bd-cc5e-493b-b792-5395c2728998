package com.adins.esign.dataaccess.impl;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.DailyRecapDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceDailyRecap;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.AuditDataType;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class DailyRecapDaoHbnTest {
	@Autowired private DailyRecapDao dailyRecapDao;
	@Autowired private CommonLogic commonLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private VendorLogic vendorLogic;
	
	private AuditDataType auditData;
	private AuditContext auditContext;

	private MsLov balanceType;
	private MsTenant tenantJunit;
	private MsVendor vendorJunit;
	
	@BeforeEach
	public void setUp() {
		auditData = new AuditDataType();
		auditData.setCallerId("INITIAL");
		
		auditContext = new AuditContext(auditData.getCallerId());
		
		balanceType = this.commonLogic.getLovByGroupAndCode(
				GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT,auditContext);
		
		tenantJunit = tenantLogic.getTenantByCode("WOMF", auditContext);
		vendorJunit = vendorLogic.getVendorByCode(GlobalVal.VENDOR_CODE_ESG, auditContext);
	}
	
	@Order(1)
	@Test
	void countQtyDailyRecapTest() {
		
		int countDailyRecap = dailyRecapDao.countQtyDailyRecap("2021-09-17", balanceType, tenantJunit, vendorJunit);
		assertNotEquals(0, countDailyRecap);
	}
	
	@Order(1)
	@Test
	void getDailyRecapTest() {
		TrBalanceDailyRecap dailyRecap = dailyRecapDao.getDailyRecap("2022-02-05", balanceType, tenantJunit, vendorJunit);
		assertNotNull(dailyRecap);
	}
}
