package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class ValidationException extends AdInsException {
	
	private static final long serialVersionUID = 1L;
	
	private int errorCode;
	
	public ValidationException(String message, int errorCode) {
		super(message);
		this.errorCode = errorCode;
	}

	@Override
	public int getErrorCode() {
		if (errorCode <= 0) {
			return StatusCode.UNKNOWN;
		}
		
		return errorCode;
	}

}
