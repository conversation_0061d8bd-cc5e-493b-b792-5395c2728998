package com.adins.esign.model.custom;

import java.util.Objects;

public class ManualStampValidationBean {

    private String refNo;
    private String tenantCode;


    public String getRefNo() {
        return this.refNo;
    }

    public void setRefNo(String refNo) {
        this.refNo = refNo;
    }

    public String getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    @Override
    public boolean equals(Object object) {
        if (this == object) {
            return true;
        }

        if (null == object) {
            return false;
        }

        if (!(object instanceof ManualStampValidationBean)) {
            return false;
        }

        ManualStampValidationBean bean = (ManualStampValidationBean) object;
        
        if (!Objects.equals(refNo, bean.getRefNo())) {
            return false;
        }

        return Objects.equals(tenantCode, bean.getTenantCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.refNo, this.tenantCode);
    }


    @Override
    public String toString() {
        return "{" +
            " refNo='" + getRefNo() + "'" +
            ", tenantCode='" + getTenantCode() + "'" +
            "}";
    }

}
