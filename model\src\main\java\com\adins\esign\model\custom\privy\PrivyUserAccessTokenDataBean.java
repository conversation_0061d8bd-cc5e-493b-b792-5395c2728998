package com.adins.esign.model.custom.privy;

import com.google.gson.annotations.SerializedName;

public class PrivyUserAccessTokenDataBean {
	private String token;
	@SerializedName("expired_at") private String expiredAt;
	@SerializedName("created_at") private String createdAt;

	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	public String getExpiredAt() {
		return expiredAt;
	}
	public void setExpiredAt(String expiredAt) {
		this.expiredAt = expiredAt;
	}
	public String getCreatedAt() {
		return createdAt;
	}
	public void setCreatedAt(String createdAt) {
		this.createdAt = createdAt;
	}
	
	
}
