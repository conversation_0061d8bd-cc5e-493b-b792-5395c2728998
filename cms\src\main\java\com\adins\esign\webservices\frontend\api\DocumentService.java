package com.adins.esign.webservices.frontend.api;

import java.io.IOException;
import java.text.ParseException;

import com.adins.esign.webservices.model.ActivationStatusByDocumentIdRequest;
import com.adins.esign.webservices.model.ActivationStatusByDocumentIdResponse;
import com.adins.esign.webservices.model.BulkSignDocumentEmbedRequest;
import com.adins.esign.webservices.model.BulkSignDocumentRequest;
import com.adins.esign.webservices.model.BulkSignDocumentResponse;
import com.adins.esign.webservices.model.CancelDigitalSignRequest;
import com.adins.esign.webservices.model.CancelDigitalSignResponse;
import com.adins.esign.webservices.model.CancelSignNormalRequest;
import com.adins.esign.webservices.model.CheckDocTemplateRequest;
import com.adins.esign.webservices.model.CheckDocTemplateResponse;
import com.adins.esign.webservices.model.CheckDocumentBeforeSigningRequest;
import com.adins.esign.webservices.model.CheckDocumentBeforeSigningResponse;
import com.adins.esign.webservices.model.CheckDocumentSendStatusEmbedRequest;
import com.adins.esign.webservices.model.CheckDocumentSendStatusRequest;
import com.adins.esign.webservices.model.CheckDocumentSendStatusResponse;
import com.adins.esign.webservices.model.DocumentExcelReportResponse;
import com.adins.esign.webservices.model.DocumentSignDetailsRequest;
import com.adins.esign.webservices.model.DocumentSignDetailsResponse;
import com.adins.esign.webservices.model.DocumentTemplateAddUpdateRequest;
import com.adins.esign.webservices.model.DocumentTemplateAddUpdateResponse;
import com.adins.esign.webservices.model.DocumentTemplateGetOneRequest;
import com.adins.esign.webservices.model.DocumentTemplateGetOneResponse;
import com.adins.esign.webservices.model.DocumentTemplateListEmbedRequest;
import com.adins.esign.webservices.model.DocumentTemplateListEmbedResponse;
import com.adins.esign.webservices.model.DocumentTemplateListRequest;
import com.adins.esign.webservices.model.DocumentTemplateListResponse;
import com.adins.esign.webservices.model.DocumentTemplateSignLocListEmbedRequest;
import com.adins.esign.webservices.model.DocumentTemplateSignLocListEmbedResponse;
import com.adins.esign.webservices.model.DownloadManualReportRequest;
import com.adins.esign.webservices.model.DownloadManualReportResponse;
import com.adins.esign.webservices.model.DownloadStampedFileFromDmsRequest;
import com.adins.esign.webservices.model.DownloadStampedFileFromDmsResponse;
import com.adins.esign.webservices.model.DummyClientURLUploadRequest;
import com.adins.esign.webservices.model.DummyClientURLUploadResponse;
import com.adins.esign.webservices.model.GetDocumentIdRequest;
import com.adins.esign.webservices.model.GetDocumentIdResponse;
import com.adins.esign.webservices.model.GetListReportRequest;
import com.adins.esign.webservices.model.GetListReportResponse;
import com.adins.esign.webservices.model.InsertDocumentManualSignRequest;
import com.adins.esign.webservices.model.InsertDocumentManualSignResponse;
import com.adins.esign.webservices.model.InsertDocumentStampingRequest;
import com.adins.esign.webservices.model.InsertDocumentStampingResponse;
import com.adins.esign.webservices.model.ListInquiryDocumentEmbedRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentNormalRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentResponse;
import com.adins.esign.webservices.model.ResendNotifSignRequest;
import com.adins.esign.webservices.model.ResendNotifSignResponse;
import com.adins.esign.webservices.model.ResendSignNotificationRequest;
import com.adins.esign.webservices.model.ResendSignNotificationResponse;
import com.adins.esign.webservices.model.ResumeSendDocumentRequest;
import com.adins.esign.webservices.model.ResumeSendDocumentResponse;
import com.adins.esign.webservices.model.RetryLatestStampFromUploadRequest;
import com.adins.esign.webservices.model.RetryLatestStampFromUploadResponse;
import com.adins.esign.webservices.model.RetryStampingEmbedRequest;
import com.adins.esign.webservices.model.RetryStampingEmbedResponse;
import com.adins.esign.webservices.model.RetryStampingMeteraiDocumentResponse;
import com.adins.esign.webservices.model.RetryStampingNormalRequest;
import com.adins.esign.webservices.model.RetryStampingNormalResponse;
import com.adins.esign.webservices.model.RetryStampingRequest;
import com.adins.esign.webservices.model.RetryStampingResponse;
import com.adins.esign.webservices.model.SaveManualStampRequest;
import com.adins.esign.webservices.model.SaveSignResultRequest;
import com.adins.esign.webservices.model.SaveSignResultResponse;
import com.adins.esign.webservices.model.SignConfirmDocumentRequest;
import com.adins.esign.webservices.model.SignConfirmDocumentResponse;
import com.adins.esign.webservices.model.SignDocumentEmbedRequest;
import com.adins.esign.webservices.model.SignDocumentRequest;
import com.adins.esign.webservices.model.SignDocumentResponse;
import com.adins.esign.webservices.model.SignLinkRequest;
import com.adins.esign.webservices.model.SignLinkResponse;
import com.adins.esign.webservices.model.SignerListEmbedRequest;
import com.adins.esign.webservices.model.SignerListRequest;
import com.adins.esign.webservices.model.SignerListResponse;
import com.adins.esign.webservices.model.StampDocumentRequest;
import com.adins.esign.webservices.model.StampDocumentResponse;
import com.adins.esign.webservices.model.StartStampingMeteraiRequest;
import com.adins.esign.webservices.model.StartStampingMeteraiResponse;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.embed.RetryLatestStampFromUploadEmbedRequest;
import com.adins.framework.service.base.model.MssResponseType;

public interface DocumentService {
	DocumentTemplateListResponse listDocumentTemplate(DocumentTemplateListRequest docRequest);
	DocumentTemplateListEmbedResponse listDocumentTemplateEmbed(DocumentTemplateListEmbedRequest request);
	DocumentTemplateAddUpdateResponse insertDocumentTemplate(DocumentTemplateAddUpdateRequest docRequest);
	DocumentTemplateGetOneResponse getDocumentTemplate(DocumentTemplateGetOneRequest docRequest);
	DocumentTemplateAddUpdateResponse updateDocumentTemplate(DocumentTemplateAddUpdateRequest docRequest);
	
	SignDocumentResponse signDocument(SignDocumentRequest signDocRequest) throws IOException, ParseException;
	SignDocumentResponse signDocumentEmbed(SignDocumentEmbedRequest request) throws IOException, ParseException;
	BulkSignDocumentResponse bulkSignDocument(BulkSignDocumentRequest request) throws IOException, ParseException;
	BulkSignDocumentResponse bulkSignDocumentEmbed(BulkSignDocumentEmbedRequest request) throws IOException, ParseException;
	
	SaveSignResultResponse saveSignResult(SaveSignResultRequest saveSignRequest) throws IOException;
	ListInquiryDocumentResponse getListInquiryDocument(ListInquiryDocumentRequest request);
	ListInquiryDocumentResponse getListInquiryDocumentEmbed(ListInquiryDocumentEmbedRequest request);
	ListInquiryDocumentResponse getListInquiryDocumentNormal(ListInquiryDocumentNormalRequest request);
	ViewDocumentResponse getDocumentFile(ViewDocumentRequest request) throws IOException;
	ViewDocumentResponse getDocumentFileEmbed(ViewDocumentRequest request) throws IOException;
	SignerListResponse getSignerList(SignerListRequest request);
	SignerListResponse getSignerListEmbed(SignerListEmbedRequest request);
	CheckDocTemplateResponse checkDocTemplateExist(CheckDocTemplateRequest request);
	
	// Cancel sign
	CancelDigitalSignResponse cancelDigitalSign(CancelDigitalSignRequest request); // embed
	MssResponseType cancelSignNormal(CancelSignNormalRequest request); // Normal (from FE with token auth)
	
	ResendSignNotificationResponse resendSignNotification(ResendSignNotificationRequest request);
	ResendNotifSignResponse resendNotifSign(ResendNotifSignRequest request);
	
	// Download Excel List Document
	DocumentExcelReportResponse exportDocumentReportEmbed(ListInquiryDocumentEmbedRequest request);
	
	SignLinkResponse getUserSignLink(SignLinkRequest request);
	CheckDocumentSendStatusResponse checkDocumentSendStatus(CheckDocumentSendStatusRequest request);
	CheckDocumentSendStatusResponse checkDocSendStatusEmbed(CheckDocumentSendStatusEmbedRequest request);
	
	GetDocumentIdResponse getDocumentId(GetDocumentIdRequest request);
	
	ActivationStatusByDocumentIdResponse activationStatusByDocumentId(ActivationStatusByDocumentIdRequest request) throws IOException, ParseException;
	ActivationStatusByDocumentIdResponse activationStatusByDocumentIdEmbed(ActivationStatusByDocumentIdRequest request) throws IOException, ParseException;
	
	ResumeSendDocumentResponse resumeSendDocument(ResumeSendDocumentRequest request) throws IOException, ParseException;
	
	// Document stamping
	RetryStampingResponse retryStamping(RetryStampingRequest request) throws IOException, ParseException;
	RetryStampingEmbedResponse retryStampingEmbed(RetryStampingEmbedRequest request) throws IOException, ParseException;
	RetryLatestStampFromUploadResponse retryLatestStampFromUpload(RetryLatestStampFromUploadRequest request);
	RetryLatestStampFromUploadResponse retryLatestStampFromUploadEmbed(RetryLatestStampFromUploadEmbedRequest request);
	RetryStampingNormalResponse retryStampingNormal(RetryStampingNormalRequest request);
	
	DocumentTemplateSignLocListEmbedResponse listDocumentTemplateSignLocEmbed(DocumentTemplateSignLocListEmbedRequest request);
	RetryStampingMeteraiDocumentResponse retryStampingMeterai (RetryStampingRequest request);
	
	// API insert document stamping emeterai untuk menu embed
	InsertDocumentStampingResponse insertDocumentStamping(InsertDocumentStampingRequest request);
	InsertDocumentManualSignResponse insertDocumentManualSign(InsertDocumentManualSignRequest request) throws ParseException, IOException;
	// API insert document stamping emeterai untuk eksternal 
	StampDocumentResponse stampDocument(StampDocumentRequest request);
	StartStampingMeteraiResponse startStampingMeterai(StartStampingMeteraiRequest request);
	
	//dummy url client
	DummyClientURLUploadResponse dummyClientUrlUpload(DummyClientURLUploadRequest request);
	
	DocumentTemplateListEmbedResponse getFilterListDocumentTemplate(DocumentTemplateListRequest request);
	DownloadStampedFileFromDmsResponse downloadStampedDocumentFromDms(DownloadStampedFileFromDmsRequest request) throws IOException;
	DownloadStampedFileFromDmsResponse downloadStampedDocumentFromDmsEmbed(DownloadStampedFileFromDmsRequest request) throws IOException;
	
	CheckDocumentBeforeSigningResponse checkDocumentBeforeSigning(CheckDocumentBeforeSigningRequest request);
	
	SignConfirmDocumentResponse insertSignConfirmDocumentResponse(SignConfirmDocumentRequest request);
	
	DocumentSignDetailsResponse getDocumentSignDetails(DocumentSignDetailsRequest request);
	
	InsertDocumentStampingResponse saveManualStampRequest(SaveManualStampRequest request);
}
