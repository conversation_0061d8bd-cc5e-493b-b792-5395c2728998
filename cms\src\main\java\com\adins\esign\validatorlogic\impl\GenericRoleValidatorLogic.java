package com.adins.esign.validatorlogic.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsrole;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.validatorlogic.api.RoleValidatorLogic;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericRoleValidatorLogic extends BaseLogic implements RoleValidatorLogic {

	@Override
	public AmMsrole validateGetRole(String tenantCode, String roleCode, boolean checkRoleExistence, boolean checkAllRole, AuditContext audit  ) {
		
		if (StringUtils.isBlank(roleCode)) {
			String[] errParams = {"roleCode"};
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, errParams, audit), ReasonParam.MANDATORY_PARAM);
		}
		AmMsrole role = new AmMsrole();
		
		if (checkAllRole) {
			role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeV2(roleCode, tenantCode);
		} else {
			role = daoFactory.getRoleDao().getRoleByCodeAndTenantCode(roleCode, tenantCode);
		}
		
		if (checkRoleExistence && null == role) {
			String[] errParams = {"Role", StringUtils.upperCase(roleCode)};
			throw new CommonException(getMessage("businesslogic.global.datanotexist", errParams, audit), ReasonCommon.DATA_NOT_EXISTED);
		}
		
		return role;
	}

	@Override
	public void validateRoleByNameForAddRole(String tenantCode, String roleName, AuditContext audit) {
		if (StringUtils.isBlank(roleName)) {
			String[] errParams = {getMessage("businesslogic.role.rolename", null, audit)};
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, errParams, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		AmMsrole role = daoFactory.getRoleDao().getRoleByName(roleName, tenantCode);
		if (null != role) {
			String[] errParams = {roleName};
			throw new CommonException(getMessage("businesslogic.role.rolewithnamealreadyexist", errParams, audit), ReasonCommon.DATA_ALREADY_EXISTED);
		}
	}

	@Override
	public void validateRoleByNameForEditRole(String tenantCode, String roleName, AmMsrole existingRole,
			AuditContext audit) {
		if (StringUtils.isBlank(roleName)) {
			String[] errParams = {getMessage("businesslogic.role.rolename", null, audit)};
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, errParams, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		AmMsrole role = daoFactory.getRoleDao().getRoleByName(roleName, tenantCode);
		if (null != role && !existingRole.getRoleCode().equals(role.getRoleCode())) {
			String[] errParams = {roleName};
			throw new CommonException(getMessage("businesslogic.role.rolewithnamealreadyexist", errParams, audit), ReasonCommon.DATA_ALREADY_EXISTED);
		}
	}

}
