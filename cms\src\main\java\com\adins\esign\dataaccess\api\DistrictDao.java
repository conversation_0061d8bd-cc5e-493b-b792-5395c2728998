package com.adins.esign.dataaccess.api;

import java.util.List;
import com.adins.am.model.AmMsdistrict;
import com.adins.esign.model.custom.DistrictBean;
import com.adins.esign.model.custom.DistrictExternalBean;

public interface DistrictDao {	
	List<DistrictBean> getListDistrict(String districtName, Long provinceId);

	AmMsdistrict getDistrict(Long idMsdistrict);
	AmMsdistrict getDistrictById(Long idDistrict, Long idMsprovince);	
	AmMsdistrict getDistrictByName(String districtName);		

	void insertAmMsdistrict(AmMsdistrict district);
	void updateAmMsdistrict(AmMsdistrict district);
	
	List<DistrictExternalBean> getDistrictExternalList(Long idMsprovince);

}