package com.adins.esign.businesslogic.impl;

import java.io.IOException;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.adins.esign.businesslogic.api.SmsLogic;
import com.adins.esign.webservices.model.SendSmsValueFirstRequestBean;

@Disabled
@ExtendWith(SpringExtension.class)
class GenericSmsLogicTest {
	@Autowired private SmsLogic smsLogic;
	
	@Test
	void sendSmsTest() throws IOException {
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean("085691160989", "hellow can you hear me http://www.ad-ins.com/sign?docId=1234", null);
		smsLogic.sendSms(sendSmsValueFirstRequestBean);
	}
}
