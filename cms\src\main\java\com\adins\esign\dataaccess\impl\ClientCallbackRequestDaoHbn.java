package com.adins.esign.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.ClientCallbackRequestDao;
import com.adins.esign.model.TrClientCallbackRequest;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class ClientCallbackRequestDaoHbn extends BaseDaoHbn implements ClientCallbackRequestDao {

	@Override
	public void insertClientCallbackRequest(TrClientCallbackRequest request) {
		request.setUsrCrt(MssTool.maskData(request.getUsrCrt()));
		managerDAO.insert(request);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertClientCallbackRequestNewTrx(TrClientCallbackRequest request) {
		request.setUsrCrt(MssTool.maskData(request.getUsrCrt()));
		managerDAO.insert(request);
	}

}
