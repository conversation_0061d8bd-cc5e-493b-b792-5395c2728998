package com.adins.esign.webservices.frontend.api;

import java.io.IOException;
import java.text.ParseException;

import com.adins.esign.webservices.model.DetailImportAutosignBmRequest;
import com.adins.esign.webservices.model.DetailImportAutosignBmResponse;
import com.adins.esign.webservices.model.DownloadTemplateExcelImportAutosignBmRequest;
import com.adins.esign.webservices.model.DownloadTemplateExcelImportAutosignBmResponse;
import com.adins.esign.webservices.model.ImportAutosignBmDataRequest;
import com.adins.esign.webservices.model.ImportAutosignBmDataResponse;
import com.adins.esign.webservices.model.InquiryImportAutosignBmRequest;
import com.adins.esign.webservices.model.InquiryImportAutosignBmResponse;

public interface AutosignService {
	ImportAutosignBmDataResponse importAutosignBmData(ImportAutosignBmDataRequest request) throws IOException, ParseException;
	InquiryImportAutosignBmResponse listImportAutosignBmDataResponse(InquiryImportAutosignBmRequest request) throws IOException, ParseException;
	DetailImportAutosignBmResponse detailImportAutosignBm(DetailImportAutosignBmRequest request);
	DownloadTemplateExcelImportAutosignBmResponse downloadTemplateExcelImportAutosignBm(
			DownloadTemplateExcelImportAutosignBmRequest request);
}
