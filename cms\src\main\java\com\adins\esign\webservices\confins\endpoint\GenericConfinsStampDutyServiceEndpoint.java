package com.adins.esign.webservices.confins.endpoint;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.StampDutyLogic;
import com.adins.esign.businesslogic.api.interfacing.PaymentReceiptOnPremStampingLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.webservices.confins.api.ConfinsStampDutyService;
import com.adins.esign.webservices.model.UpdateStampDutyStatusRequest;
import com.adins.esign.webservices.model.UpdateStampDutyStatusResponse;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptRequest;
import com.adins.esign.webservices.model.confins.InsertStampingPaymentReceiptResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/confins/stampduty")
@Api(value = "ConfinsStampDutyService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericConfinsStampDutyServiceEndpoint implements ConfinsStampDutyService {

	@Autowired private StampDutyLogic stampDutyLogic;
	@Autowired private PaymentReceiptOnPremStampingLogic prOnPremStampingLogic;

	@Override
	@POST
	@Path("/attachMeteraiPajakku")
	public UpdateStampDutyStatusResponse attachMeteraiPajakku(UpdateStampDutyStatusRequest request) throws IOException {
		AuditContext audit = request.getAudit().toAuditContext();
		audit.setCallerId(GlobalVal.CONST_CONFINS);
		
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader("x-api-key");
		
		return stampDutyLogic.attachMeteraiPajakku(request, apiKey, audit);
	}

	@Override
	@POST
	@Path("/insertStampingPaymentReceipt")
	public InsertStampingPaymentReceiptResponse insertStampingPaymentReceipt(
			InsertStampingPaymentReceiptRequest request) throws ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String apiKey = httpRequest.getHeader("x-api-key");
		String[] arr = apiKey.split("@");
		request.setTenantCode(arr[1]);
 		return prOnPremStampingLogic.insertStampingPaymentReceipt(request, audit);
	}

	
	@Override
	@POST
	@Path("/insertStampingPaymentReceiptDummy")
	public InsertStampingPaymentReceiptResponse insertStampingPaymentReceiptDummy(
			InsertStampingPaymentReceiptRequest request) throws ParseException {
		InsertStampingPaymentReceiptResponse response = new InsertStampingPaymentReceiptResponse();
		if ("1".equals(request.getReturnStampResult())) {
			response.setStampedDocument("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");
		}
 		return response;
	}
}
