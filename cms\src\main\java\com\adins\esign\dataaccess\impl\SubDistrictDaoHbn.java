package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMssubdistrict;
import com.adins.esign.dataaccess.api.SubDistrictDao;
import com.adins.esign.model.custom.SubDistrictBean;
import com.adins.esign.model.custom.SubDistrictExternalBean;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class SubDistrictDaoHbn  extends BaseDaoHbn implements SubDistrictDao{

	
	private String constructParamsSubDistrict(Map<String, Object> params, String subDistrictName, Long districtId) {
		StringBuilder query = new StringBuilder();
		
		if (StringUtils.isNotBlank(subDistrictName) && districtId != null) {
			query.append(" WHERE subdistrict_name LIKE :subDistrictName AND id_msdistrict = :districtId ");
			params.put("subDistrictName", subDistrictName);
			params.put("districtId", districtId);
		}
		else if(!StringUtils.isNotBlank(subDistrictName) && districtId != null) {
			query.append(" WHERE id_msdistrict = :districtId ");
			params.put("districtId", districtId);
		}
		
		return query.toString();
	}
	
	@Override
	public List<SubDistrictBean> getListSubDistrict(String subDistrictName, Long districtId){
		Map<String, Object> params = new HashMap<>();

		String paramsQuerySubDistrict = this.constructParamsSubDistrict(params, subDistrictName, districtId);
		StringBuilder query = new StringBuilder();
		
		query.append(" SELECT subdistrict_name AS \"subDistrictName\", ")
		.append(" CAST(id_mssubdistrict AS VARCHAR) AS \"idMsSubDistrict\" ")
		.append(" FROM am_mssubdistrict ")
		.append(paramsQuerySubDistrict);
		
	
		return this.managerDAO.selectForListString(SubDistrictBean.class, query.toString(), params, null);
	}


	@Override
	public AmMssubdistrict getSubdistrictById(Long subdistrictId, Long idMsDistrict) {
		if (subdistrictId == null && idMsDistrict == null) {
			return null;
		}
	
		Object[][] queryParams = {
				{AmMssubdistrict.SUBDISTRICT_ID_HBM, subdistrictId},
				{"idMsdistrict", idMsDistrict}
		};
		
		return this.managerDAO.selectOne(
				"from AmMssubdistrict asd "
				+ "join fetch asd.amMsdistrict ad "
				+ "where asd.subdistrictId = :subdistrictId and ad.idMsdistrict = :idMsdistrict",
				queryParams);
	}


	@Override
	public void insertAmMssubdistrict(AmMssubdistrict subdistrict) {
		subdistrict.setUsrCrt(MssTool.maskData(subdistrict.getUsrCrt()));
		this.managerDAO.insert(subdistrict);
		
	}


	@Override
	public void updateAmMssubdistrict(AmMssubdistrict subdistrict) {
		subdistrict.setUsrUpd(MssTool.maskData(subdistrict.getUsrUpd()));
		this.managerDAO.update(subdistrict);
	}

	@Override
	public AmMssubdistrict getSubdistrict(Long idMssubdistrict) {
		return this.managerDAO.selectOne(
				"from AmMssubdistrict ms "
				+ "join fetch ms.amMsdistrict "
				+ "where ms.idMssubdistrict = :idMssubdistrict ",
				new Object[][] {{AmMssubdistrict.ID_MSSUBDISTRICT_HBM, idMssubdistrict}});
	}

	@Override
	public List<SubDistrictExternalBean> getSubDistrictExternalList(Long idMsDistrict) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put("idMsDistrict", idMsDistrict);
		
		query.append(" select subdistrict_name AS \"subDistrictName\" from am_mssubdistrict " + 
				"where id_msdistrict = :idMsDistrict ORDER by subdistrict_name asc ");
		
		return this.managerDAO.selectForListString(SubDistrictExternalBean.class, query.toString(), params, null);
	}

	
}
