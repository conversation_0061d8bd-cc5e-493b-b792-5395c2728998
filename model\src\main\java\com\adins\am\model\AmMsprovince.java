package com.adins.am.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.adins.am.model.custom.UpdateableEntity;

@Entity
@Table(name = "am_msprovince")
public class AmMsprovince extends UpdateableEntity {

	public static final String ID_MSPROVINCE_HBM = "idMsprovince";
	public static final String PROVINCE_ID_HBM = "provinceId";
	public static final String PROVINCE_NAME_HBM = "provinceName";
	
	private Long idMsprovince;
	private String provinceName;
	private Long provinceId;

	public AmMsprovince() {
		
	}
	
	public AmMsprovince(String provinceName, Long provinceId,
			String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd) {
		super();
		this.provinceName = provinceName;
		this.provinceId = provinceId;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
	}
	
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id_msprovince", unique = true, nullable = false)
	public Long getIdMsprovince() {
		return idMsprovince;
	}

	public void setIdMsprovince(Long idMsprovince) {
		this.idMsprovince = idMsprovince;
	}
	
	@Column(name = "province_name", length = 70)
	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}
	
	@Column(name = "province_id")
	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}
}
