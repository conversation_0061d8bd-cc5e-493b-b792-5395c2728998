package com.adins.esign.businesslogic.api;

import java.util.Map;

import com.adins.esign.model.MsMsgTemplate;

public interface MessageTemplateLogic {

	/**
	 * 
	 * @param msgTemplateCode kode template yang terdaftar di table MS_MSG_TEMPLATE
	 * @param templateParameters parameter yang digunakan di dalam template
	 * @return
	 * 		Record table dengan subject dan body yang sudah di-parse dengan parameters. 		
	 */
	public MsMsgTemplate getAndParseContent(String msgTemplateCode, Map<String, Object> templateParameters);
}
