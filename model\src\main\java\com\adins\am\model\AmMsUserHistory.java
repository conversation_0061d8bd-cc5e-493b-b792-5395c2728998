package com.adins.am.model;

import java.util.Date;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.am.model.custom.ActiveDeleteAndUpdateableEntity;
import com.adins.esign.model.MsEmailHosting;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.TrUserDataAccessLog;

@Entity
@Table(name = "am_msuser_history")
public class AmMsUserHistory extends ActiveDeleteAndUpdateableEntity  implements java.io.Serializable{
	private static final long serialVersionUID = 1L;
	private long idMsUserHistory;
	private TrUserDataAccessLog trUserDataAccessLog;
	private MsOffice msOffice;
	private MsEmailHosting msEmailHosting;
	private String loginId;
	private String fullName;
	private String initialName;
	private String loginProvider;
	private String password;
	private Integer failCount;
	private String isLoggedIn;
	private String isLocked;
	private String isDormant;
	private Date lastLoggedIn;
	private Date lastLocked;
	private Date lastExpired;
	private Date lastDormant;
	private Date lastLoggedFail;
	private Date lastRequestOut;
	private Date prevLoggedIn;
	private Date prevLoggedFail;
	private String changePwdLogin;
	private String resetCode;
	private String otpCode;
	private String emailService;
	private Date resetCodeRequestDate;
	private Short resetCodeRequestNum;
	private String hashedPhone;
	private String hashedIdNo;
	private String vendorResetPassLink;
	private String dataChangeRequest;
	private String activationLink;
	private String reregistrationLink;
	private Date livenessFacecompareRequestDate;
	private Short livenessFacecompareRequestNum;
	private Date livenessFacecompareValidationDate;
	private Short livenessFacecompareValidationNum;
	

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_user_history", unique = true, nullable = false)
	public long getIdMsUserHistory() {
		return idMsUserHistory;
	}

	
	public void setIdMsUserHistory(long idMsUserHistory) {
		this.idMsUserHistory = idMsUserHistory;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_user_access_data_log")
	public TrUserDataAccessLog getTrUserDataAccessLog() {
		return trUserDataAccessLog;
	}

	public void setTrUserDataAccessLog(TrUserDataAccessLog trUserDataAccessLog) {
		this.trUserDataAccessLog = trUserDataAccessLog;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_office")
	public MsOffice getMsOffice() {
		return this.msOffice;
	}

	public void setMsOffice(MsOffice msOffice) {
		this.msOffice = msOffice;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_email_hosting")
	public MsEmailHosting getMsEmailHosting() {
		return msEmailHosting;
	}

	public void setMsEmailHosting(MsEmailHosting msEmailHosting) {
		this.msEmailHosting = msEmailHosting;
	}

	@Column(name = "login_id", length = 80)
	public String getLoginId() {
		return this.loginId;
	}

	public void setLoginId(String loginId) {
		this.loginId = loginId;
	}

	@Column(name = "full_name", nullable = false, length = 80)
	public String getFullName() {
		return this.fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	@Column(name = "initial_name", length = 15)
	public String getInitialName() {
		return this.initialName;
	}

	public void setInitialName(String initialName) {
		this.initialName = initialName;
	}

	@Column(name = "login_provider", length = 6)
	public String getLoginProvider() {
		return this.loginProvider;
	}

	public void setLoginProvider(String loginProvider) {
		this.loginProvider = loginProvider;
	}

	@Column(name = "password", nullable = false, length = 200)
	public String getPassword() {
		return this.password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	@Column(name = "fail_count")
	public Integer getFailCount() {
		return this.failCount;
	}

	public void setFailCount(Integer failCount) {
		this.failCount = failCount;
	}

	@Column(name = "is_logged_in", length = 1)
	public String getIsLoggedIn() {
		return this.isLoggedIn;
	}

	public void setIsLoggedIn(String isLoggedIn) {
		this.isLoggedIn = isLoggedIn;
	}

	@Column(name = "is_locked", length = 1)
	public String getIsLocked() {
		return this.isLocked;
	}

	public void setIsLocked(String isLocked) {
		this.isLocked = isLocked;
	}

	@Column(name = "is_dormant", length = 1)
	public String getIsDormant() {
		return this.isDormant;
	}

	public void setIsDormant(String isDormant) {
		this.isDormant = isDormant;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_logged_in", length = 29)
	public Date getLastLoggedIn() {
		return this.lastLoggedIn;
	}

	public void setLastLoggedIn(Date lastLoggedIn) {
		this.lastLoggedIn = lastLoggedIn;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_locked", length = 29)
	public Date getLastLocked() {
		return this.lastLocked;
	}

	public void setLastLocked(Date lastLocked) {
		this.lastLocked = lastLocked;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_expired", length = 29)
	public Date getLastExpired() {
		return this.lastExpired;
	}

	public void setLastExpired(Date lastExpired) {
		this.lastExpired = lastExpired;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_dormant", length = 29)
	public Date getLastDormant() {
		return this.lastDormant;
	}

	public void setLastDormant(Date lastDormant) {
		this.lastDormant = lastDormant;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_logged_fail", length = 29)
	public Date getLastLoggedFail() {
		return this.lastLoggedFail;
	}

	public void setLastLoggedFail(Date lastLoggedFail) {
		this.lastLoggedFail = lastLoggedFail;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "last_request_out", length = 29)
	public Date getLastRequestOut() {
		return this.lastRequestOut;
	}

	public void setLastRequestOut(Date lastRequestOut) {
		this.lastRequestOut = lastRequestOut;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "prev_logged_in", length = 29)
	public Date getPrevLoggedIn() {
		return this.prevLoggedIn;
	}

	public void setPrevLoggedIn(Date prevLoggedIn) {
		this.prevLoggedIn = prevLoggedIn;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "prev_logged_fail", length = 29)
	public Date getPrevLoggedFail() {
		return this.prevLoggedFail;
	}

	public void setPrevLoggedFail(Date prevLoggedFail) {
		this.prevLoggedFail = prevLoggedFail;
	}

	@Column(name = "change_pwd_login", length = 1)
	public String getChangePwdLogin() {
		return this.changePwdLogin;
	}

	public void setChangePwdLogin(String changePwdLogin) {
		this.changePwdLogin = changePwdLogin;
	}

	@Column(name = "reset_code", length = 40)
	public String getResetCode() {
		return this.resetCode;
	}

	public void setResetCode(String resetCode) {
		this.resetCode = resetCode;
	}
	
	@Column(name = "otp_code", length = 10)
	public String getOtpCode() {
		return otpCode;
	}

	public void setOtpCode(String otpCode) {
		this.otpCode = otpCode;
	}


	@Column(name = "email_service", length = 20)
	public String getEmailService() {
		return this.emailService;
	}

	public void setEmailService(String emailService) {
		this.emailService = emailService;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "reset_code_request_date", length = 29)
	public Date getResetCodeRequestDate() {
		return this.resetCodeRequestDate;
	}

	public void setResetCodeRequestDate(Date resetCodeRequestDate) {
		this.resetCodeRequestDate = resetCodeRequestDate;
	}

	@Column(name = "reset_code_request_num")
	public Short getResetCodeRequestNum() {
		return this.resetCodeRequestNum;
	}

	public void setResetCodeRequestNum(Short resetCodeRequestNum) {
		this.resetCodeRequestNum = resetCodeRequestNum;
	}

	@Column(name = "hashed_phone", length = 200)
	public String getHashedPhone() {
		return this.hashedPhone;
	}

	public void setHashedPhone(String hashedPhone) {
		this.hashedPhone = hashedPhone;
	}

	@Column(name = "hashed_id_no", length = 200)
	public String getHashedIdNo() {
		return this.hashedIdNo;
	}

	public void setHashedIdNo(String hashedIdNo) {
		this.hashedIdNo = hashedIdNo;
	}

	@Column(name = "vendor_reset_pass_link", length = 200)
	public String getVendorResetPassLink() {
		return vendorResetPassLink;
	}

	public void setVendorResetPassLink(String vendorResetPassLink) {
		this.vendorResetPassLink = vendorResetPassLink;
	}
	
	@Column(name = "data_change_request", length = 20)
	public String getDataChangeRequest() {
		return this.dataChangeRequest;
	}

	public void setDataChangeRequest(String dataChangeRequest) {
		this.dataChangeRequest = dataChangeRequest;
	}
	
	@Column(name = "activation_link", length = 300)
	public String getActivationLink() {
		return activationLink;
	}

	public void setActivationLink(String activationLink) {
		this.activationLink = activationLink;
	}
	
	@Column(name = "reregistration_link", length = 300)
	public String getReregistrationLink() {
		return reregistrationLink;
	}

	public void setReregistrationLink(String reregistrationLink) {
		this.reregistrationLink = reregistrationLink;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "liveness_facecompare_request_date", length = 29)
	public Date getLivenessFacecompareRequestDate() {
		return livenessFacecompareRequestDate;
	}

	public void setLivenessFacecompareRequestDate(Date livenessFacecompareRequestDate) {
		this.livenessFacecompareRequestDate = livenessFacecompareRequestDate;
	}

	@Column(name = "liveness_facecompare_request_num")
	public Short getLivenessFacecompareRequestNum() {
		return livenessFacecompareRequestNum;
	}

	public void setLivenessFacecompareRequestNum(Short livenessFacecompareRequestNum) {
		this.livenessFacecompareRequestNum = livenessFacecompareRequestNum;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "liveness_facecompare_validation_date", length = 29)
	public Date getLivenessFacecompareValidationDate() {
		return livenessFacecompareValidationDate;
	}

	public void setLivenessFacecompareValidationDate(Date livenessFacecompareValidationDate) {
		this.livenessFacecompareValidationDate = livenessFacecompareValidationDate;
	}

	@Column(name = "liveness_facecompare_validation_num")
	public Short getLivenessFacecompareValidationNum() {
		return livenessFacecompareValidationNum;
	}

	public void setLivenessFacecompareValidationNum(Short livenessFacecompareValidationNum) {
		this.livenessFacecompareValidationNum = livenessFacecompareValidationNum;
	}
	
}
