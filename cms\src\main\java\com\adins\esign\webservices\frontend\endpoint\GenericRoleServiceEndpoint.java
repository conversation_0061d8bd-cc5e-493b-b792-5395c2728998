package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.api.RoleLogic;
import com.adins.esign.webservices.frontend.api.RoleService;
import com.adins.esign.webservices.model.AddMenuOfRoleRequest;
import com.adins.esign.webservices.model.AddRoleManagementRequest;
import com.adins.esign.webservices.model.AddRoleRequest;
import com.adins.esign.webservices.model.EditRoleManagementRequest;
import com.adins.esign.webservices.model.GetListRoleManagementRequest;
import com.adins.esign.webservices.model.GetListRoleManagementResponse;
import com.adins.esign.webservices.model.GetListRoleRequest;
import com.adins.esign.webservices.model.GetListRoleResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/role")
@Api(value = "RoleService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericRoleServiceEndpoint implements RoleService {
	
	@Autowired private RoleLogic roleLogic;

	@Override
	@POST
	@Path("/s/addRole")
	public MssResponseType addRole(AddRoleRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return roleLogic.addRole(request, audit);
	}

	@Override
	@POST
	@Path("/s/addMenuofrole")
	public MssResponseType addMenuOfRole(AddMenuOfRoleRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return roleLogic.addMenuOfRole(request, audit);
	}
	
	@POST
	@Path("/s/getrolelist")
	public GetListRoleResponse getListRole(GetListRoleRequest request) {
			AuditContext audit = request.getAudit().toAuditContext();
			return roleLogic.getListRole(request, audit);
	}
	
	@Override
	@POST
	@Path("/s/getRoleListManagement")
	public GetListRoleManagementResponse getListRoleManagement(GetListRoleManagementRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return roleLogic.getListRoleManagement(request, audit);
	}

	@Override
	@POST
	@Path("/s/addRoleManagement")
	public MssResponseType addRoleManagement(AddRoleManagementRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return roleLogic.addRoleManagement(request, audit);
	}

	@Override
	@POST
	@Path("/s/editRoleManagement")
	public MssResponseType editRoleManagement(EditRoleManagementRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return roleLogic.editRoleManagement(request, audit);
	}

}
