package com.adins.esign.businesslogic.impl.interfacing;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.text.NumberFormat;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.lang.FormatterUtils;

@Disabled("temp disabled")
@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class WomfDukcapilLogicTest {
	
	private static final Double PERFECT_SCORE = Double.valueOf(100d);
	private static final Double SCORE_MULTIPLIER = Double.valueOf(9.5d);
	
	@Autowired private CommonLogic commonLogic;
	
	@Test
	void biometricScorePostProcessingTest() {
		/*
		 * Expected result-nya adalah 95 untuk input score biometrik 10.
		 * Dengan asumsi nilai FR_DUKCAPIL_MULTIPLIER di am_generalsetting masih 9.5
		 */
		Double result = this.biometricScorePostProcessing(Double.valueOf(10));
		assertEquals(Double.valueOf(95), result);
	}
	
	/**
	 * Method dipindah langsung ke JUNIT karena logic-nya private di WomfDukcapilLogic.
	 * Kalau di-test dengan panggil verifyDataAndBiometric() perlu lempar parameter gambar.
	 */
	private Double biometricScorePostProcessing(Double biometricScore) {
		if (null == biometricScore) {
			return null;
		}
		
		double finalScore = biometricScore.doubleValue() * this.getBiometricMultiplier();
		if (finalScore > 100) {
			return PERFECT_SCORE;
		}
		
		NumberFormat nf = FormatterUtils.getNumberFormat(FormatterUtils.NFORMAT_US_2);
		String finalScoreStr = nf.format(finalScore);
		return Double.valueOf(finalScoreStr);
	}
	
	private Double getBiometricMultiplier() {
		
		String gsMultiplier = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_FR_DUKCAPIL_MULTIPLIER, new AuditContext());
		if (StringUtils.isBlank(gsMultiplier)) {
			return SCORE_MULTIPLIER; 
		}
		
		try {
			return Double.valueOf(gsMultiplier);
		} catch (NumberFormatException e) {
			return SCORE_MULTIPLIER;
		}
	}
}
