package com.adins.esign.dataaccess.impl;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

import java.util.List;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.esign.dataaccess.api.RoleDao;
import com.adins.esign.dataaccess.api.UserDao;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class RoleDaoHbnTest {
@Autowired UserDao userDao;
@Autowired RoleDao roleDao;
	
	@Test
	void getMemberofroleByLoginIdAndTenantCodeTest() {
		AmMemberofrole memberRoleTest = roleDao.getMemberofroleByLoginIdRoleTenantCode("<EMAIL>", "WOM");
		assertNotNull(memberRoleTest);
	}
	
	@Test
	void getListRoleByIdUserTest() {
		AmMsuser user = userDao.getUserByLoginId("<EMAIL>");
		List<AmMsrole> listRoleTest = roleDao.getListRoleByIdUser(user.getIdMsUser());
		assertNotEquals(0, listRoleTest.size());
	}
	
	@Test
	void getRoleByCodeAndTenantCodeTest() {
		AmMsrole roleJunit = roleDao.getRoleByCodeAndTenantCode("ADMCLIENT","WOMF");
		assertNotNull(roleJunit);
	}
	
	@Test
	void getRoleByIdTest() {
		AmMsrole roleJunit = roleDao.getRoleById(2);
		assertNotNull(roleJunit);
	}
	
	@Test
	void getMemberofroleByLoginIdRoleTenantCodeTest() {
		AmMemberofrole memberRole = roleDao.getMemberofroleByLoginIdRoleTenantCode("<EMAIL>", "WOMF");
		assertNotNull(memberRole);

	}
	
	@Test
	void getListRoleByIdUserTenantCodeTest() {
		AmMsuser user = userDao.getUserByLoginId("<EMAIL>");
		List<AmMsrole> roleList = roleDao.getListRoleByIdUserTenantCode(user.getIdMsUser(), "WOMF");
		assertNotEquals(0, roleList.size());
	}
	
}
