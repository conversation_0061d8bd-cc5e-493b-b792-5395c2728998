package com.adins.esign.webservices.embed.endpoint;

import java.io.IOException;
import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.PhaseInterceptorChain;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.SignConfirmationLogic;
import com.adins.esign.businesslogic.api.embed.DocumentEmbedLogic;
import com.adins.esign.webservices.embed.api.DocumentEmbedService;
import com.adins.esign.webservices.model.BulkSignDocumentEmbedRequest;
import com.adins.esign.webservices.model.BulkSignDocumentResponse;
import com.adins.esign.webservices.model.CheckDocumentSendStatusEmbedRequest;
import com.adins.esign.webservices.model.CheckDocumentSendStatusResponse;
import com.adins.esign.webservices.model.DocumentExcelReportResponse;
import com.adins.esign.webservices.model.ListInquiryDocumentEmbedRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentResponse;
import com.adins.esign.webservices.model.ResendSignNotificationResponse;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.embed.CancelDigitalSignEmbedRequest;
import com.adins.esign.webservices.model.embed.CancelDigitalSignEmbedResponse;
import com.adins.esign.webservices.model.embed.CheckDocumentBeforeSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.CheckDocumentBeforeSigningEmbedResponse;
import com.adins.esign.webservices.model.embed.ConfirmSignEmbedRequest;
import com.adins.esign.webservices.model.embed.ConfirmSignEmbedResponse;
import com.adins.esign.webservices.model.embed.ResendSignNotificationEmbedRequest;
import com.adins.esign.webservices.model.embed.SignDocumentEmbedV2Request;
import com.adins.esign.webservices.model.embed.SignDocumentEmbedV2Response;
import com.adins.esign.webservices.model.embed.StartStampingMeteraiEmbedRequest;
import com.adins.esign.webservices.model.embed.StartStampingMeteraiEmbedResponse;
import com.adins.esign.webservices.model.embed.ViewSignerEmbedRequest;
import com.adins.esign.webservices.model.embed.ViewSignerEmbedResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/embed/document")
@Api(value = "DocumentEmbedService")
@Produces({MediaType.APPLICATION_JSON})
public class GenericDocumentEmbedServiceEndpoint implements DocumentEmbedService {
	
	@Autowired private DocumentLogic documentLogic;
	@Autowired private DocumentEmbedLogic documentEmbedLogic;
	@Autowired private SignConfirmationLogic signConfirmationLogic;

	@Override
	@POST
	@Path("/inquiryEmbed")
	public ListInquiryDocumentResponse getListInquiryDocumentEmbed(ListInquiryDocumentEmbedRequest request) {
		AuditContext auditContext = request.getAudit().toAuditContext();
		return documentLogic.getListInquiryDocumentEmbedV2(request, auditContext);
	}

	@Override
	@POST
	@Path("/checkDocumentBeforeSigningEmbed")
	public CheckDocumentBeforeSigningEmbedResponse checkDocumentBeforeSigningEmbed(CheckDocumentBeforeSigningEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentEmbedLogic.checkDocumentBeforeSigningEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/viewDocument")
	public ViewDocumentResponse getDocumentFileEmbed(ViewDocumentRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentEmbedLogic.getDocumentFileEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/cancelDigitalSign")
	public CancelDigitalSignEmbedResponse cancelDigitanSign(CancelDigitalSignEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentEmbedLogic.cancelDigitalSign(request, audit);
	}

	@Override
	@POST
	@Path("/signConfirmDocument")
	public ConfirmSignEmbedResponse confirmSign(ConfirmSignEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		
		// Set IP address from header x-real-ip
		Message message = PhaseInterceptorChain.getCurrentMessage();
		HttpServletRequest httpRequest = (HttpServletRequest) message.get(AbstractHTTPDestination.HTTP_REQUEST);
		String xRealIp = httpRequest.getHeader("x-real-ip");
		if (StringUtils.isNotBlank(xRealIp)) {
			request.setIpAddress(xRealIp);
		}
		
		return signConfirmationLogic.signConfirmationDocumentEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/signDocument")
	public SignDocumentEmbedV2Response signDocument(SignDocumentEmbedV2Request request) throws IOException, ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentEmbedLogic.signDocument(request, audit);
	}

	@Override
	@POST
	@Path("/downloadDocumentReport")
	public DocumentExcelReportResponse exportDocumentReportEmbed(ListInquiryDocumentEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentEmbedLogic.exportDocumentReportEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/resendSignNotif")
	public ResendSignNotificationResponse resendSignNotification(ResendSignNotificationEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentEmbedLogic.resendSignNotification(request, audit);
	}

	@Override
	@POST
	@Path("/viewSignerEmbed")
	public ViewSignerEmbedResponse viewSignerEmbed(ViewSignerEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentEmbedLogic.viewSignerEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/checkDocumentSendStatusEmbed")
	public CheckDocumentSendStatusResponse checkDocumentSendStatusEmbed(CheckDocumentSendStatusEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentEmbedLogic.checkDocumentSendStatusEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/bulkSignDocumentEmbed")
	public BulkSignDocumentResponse bulkSignDocumentEmbed(BulkSignDocumentEmbedRequest request) throws IOException, ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentEmbedLogic.bulkSignDocumentEmbed(request, audit);
	}
	
	@Override
	@POST
	@Path("/startStampingMateraiEmbed")
	public StartStampingMeteraiEmbedResponse startStampingMeteraiEmbed (StartStampingMeteraiEmbedRequest request) throws IOException, ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return documentEmbedLogic.startStampingMeteraiEmbed(request, audit);
	}
	
	

}