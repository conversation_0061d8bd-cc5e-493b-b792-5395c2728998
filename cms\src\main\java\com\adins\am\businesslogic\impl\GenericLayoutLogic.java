package com.adins.am.businesslogic.impl;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.businesslogic.api.LayoutLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericLayoutLogic extends BaseLogic implements LayoutLogic {
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<Map<String, Object>> retrieveMenuTree(AuditContext callerId) {
		return null;

	}
}
