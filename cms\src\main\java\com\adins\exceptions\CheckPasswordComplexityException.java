package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class CheckPasswordComplexityException extends AdInsException{

	private static final long serialVersionUID = 1L;
	
	public enum ReasonCheckPasswordComplexity {
		INVALID_PASSWORD_LENGTH,
		INVALID_PASSWORD_COMPLEXITY
	}
	
	private final ReasonCheckPasswordComplexity reason;
	
	public ReasonCheckPasswordComplexity getReason() {
		return reason;
	}

	public CheckPasswordComplexityException(String message, ReasonCheckPasswordComplexity reason) {
		super(message);
		this.reason = reason;
	}

	@Override
	public int getErrorCode() {
		switch (reason) {
		case INVALID_PASSWORD_LENGTH:
			return StatusCode.INVALID_PASSWORD_LENGTH;
		case INVALID_PASSWORD_COMPLEXITY:
			return StatusCode.INVALID_PASSWORD_COMPLEXITY;
		default:
			return StatusCode.UNKNOWN;
		}
	}

	

}
