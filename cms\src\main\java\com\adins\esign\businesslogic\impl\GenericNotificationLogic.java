package com.adins.esign.businesslogic.impl;

import java.util.HashMap;
import java.util.Date;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.ArrayList;
import java.util.Calendar;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.NotificationLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SmsLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.interfacing.JatisSmsLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.esign.webservices.model.SendNotificationTestRequest;
import com.adins.esign.webservices.model.SendNotificationTestResponse;
import com.adins.esign.webservices.model.SendSmsResponse;
import com.adins.esign.webservices.model.SendSmsValueFirstRequestBean;
import com.adins.esign.webservices.model.SendWhatsAppRequest;
import com.adins.exceptions.InvitationLinkException;
import com.adins.exceptions.SendNotificationException;
import com.adins.exceptions.InvitationLinkException.ReasonInvitationLink;
import com.adins.exceptions.SendNotificationException.ReasonSendNotif;
import com.adins.exceptions.StatusCode;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericNotificationLogic extends BaseLogic implements NotificationLogic {

    private static final Logger LOG = LoggerFactory.getLogger(NotificationLogic.class);

    @Autowired CommonValidatorLogic commonValidatorLogic;
    @Autowired VendorValidatorLogic vendorValidatorLogic;
    @Autowired BalanceValidatorLogic balanceValidatorLogic;
    @Autowired WhatsAppLogic whatsAppLogic;
    @Autowired WhatsAppHalosisLogic whatsAppHalosisLogic;
    @Autowired MessageTemplateLogic messageTemplateLogic;
    @Autowired JatisSmsLogic jatisSmsLogic;
    @Autowired SmsLogic smsLogic;
    @Autowired SaldoLogic saldoLogic;
	@Autowired TenantSettingsLogic tenantSettingsLogic;

    private static final String VFIRST_ERR28681 = "28681";
	private static final String VFIRST_ERR28682 = "28682";
	private static final String VFIRST_ERR408 = "408";

    @Override
    public SendNotificationTestResponse sendNotificationTesting(SendNotificationTestRequest request, AuditContext audit) {
        
        String messageValidation = "";

        commonValidatorLogic.validateNotNull(request.getPhoneNo(), "phoneNo", audit);
        commonValidatorLogic.validateNotNull(request.getNotifGateway(), "notifGateway", audit);
        commonValidatorLogic.validateNotNull(request.getMessageMedia(), "messageMedia", audit);

        String generalSetting = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_REGEX_PHONE_FORMAT);

        if (!request.getPhoneNo().matches(generalSetting)) {
            throw new InvitationLinkException(
							messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_PHONE,
									new Object[] { request.getPhoneNo() }, this.retrieveLocaleAudit(audit)),
							ReasonInvitationLink.INVALID_PHONE_NO);
        }

        commonValidatorLogic.validateNotNull(request.getMessage(), "Message", audit);

        MsLov notifGateway = daoFactory.getLovDao().getMsLovByCode(request.getNotifGateway().toUpperCase());

        messageValidation = getMessage("businesslogic.vendor.invalidnotificationgateway", null, audit);
        commonValidatorLogic.validateNotNull(notifGateway, messageValidation, StatusCode.NOTIF_GATEWAY_AND_MESSAGE_MEDIA_NOT_VALID);

        MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);

        MsTenant tenant = daoFactory.getTenantDao().getTenantByCode("ADINS");

        AmMsuser user = daoFactory.getUserDao().getUserByLoginId(audit.getCallerId());

        messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND, null, audit);
        commonValidatorLogic.validateNotNull(user, messageValidation, StatusCode.USER_NOT_FOUND);

        String sendingPointOption = request.getMessageMedia().toUpperCase();

        

        if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA.equals(sendingPointOption)) {
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_WA, tenant, vendor, audit);

            if (notifGateway.getCode().equals(GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP)) {
                return sendNotifTestingWhatsappJatis(request.getPhoneNo(), request.getMessage(), user, tenant, audit);
            } else if (notifGateway.getCode().equals(GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS)) {
                return sendNotifTestingWhatsappHalosis(request.getPhoneNo(), request.getMessage(), user, tenant, audit);
            } else {
                throw new SendNotificationException(getMessage("businesslogic.user.notifsendingmediaandnotifgatewaynotvalid", null, audit), ReasonSendNotif.NOTIF_GATEWAY_AND_MESSAGE_MEDIA_NOT_VALID);
            }
		} else if (GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS.equals(sendingPointOption)) {
			balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_SMS, tenant, vendor, audit);
            if (notifGateway.getCode().equals(GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS)) {
                return sendNotifTestingSMSJatis(request.getPhoneNo(), request.getMessage(), user, tenant, audit);
            } else if (notifGateway.getCode().equals(GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST)) {
                return sendNotifTestingSMSVFirst(request.getPhoneNo(), request.getMessage(), user, tenant, audit);
            } else {
                throw new SendNotificationException(getMessage("businesslogic.user.notifsendingmediaandnotifgatewaynotvalid", null, audit), ReasonSendNotif.NOTIF_GATEWAY_AND_MESSAGE_MEDIA_NOT_VALID);

            }

		} else {
		    throw new SendNotificationException(getMessage("businesslogic.user.notifsendingmedianotvalid", null, audit), ReasonSendNotif.NOTIF_MEDIA_INVALID);
        }
    }

    private SendNotificationTestResponse sendNotifTestingWhatsappJatis(String phoneNo, String message, AmMsuser user, MsTenant tenant, AuditContext audit) {
		String templateCode = GlobalVal.TEMPLATE_SEND_NOTIFICATION_TESTING; 
		
		MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateCode);
		
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(message);
		Long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);
		
		String notes = phoneNo + GlobalVal.SEND_WA_NOTIF_TESTING;
		
		SendWhatsAppRequest request = new SendWhatsAppRequest();
		request.setReservedTrxNo(reservedTrxNo);
		request.setTemplate(template);
		request.setBodyTexts(bodyTexts);
		request.setMsTenant(tenant);
		request.setPhoneNumber(phoneNo);
		request.setAmMsuser(user);
		request.setRemoveHeader(true);
		request.setNotes(notes);
		
        whatsAppLogic.sendMessage(request, audit);
				
		SendNotificationTestResponse response = new SendNotificationTestResponse();
		
		response.setRecipient(phoneNo);
		return response;
	}

    private SendNotificationTestResponse sendNotifTestingWhatsappHalosis(String phoneNo, String message, AmMsuser user, MsTenant tenant, AuditContext audit) {
		
		String templateCode = GlobalVal.TEMPLATE_SEND_NOTIFICATION_TESTING; 
		
		MsMsgTemplate template = daoFactory.getMsgTemplateDao().getTemplateByTypeAndCode(GlobalVal.TEMPLATE_TYPE_WA, templateCode);
		
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(message);
		Long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		String reservedTrxNo = String.valueOf(trxNo);

		String notes = phoneNo + GlobalVal.SEND_WA_NOTIF_TESTING;
		
		HalosisSendWhatsAppRequestBean request = new HalosisSendWhatsAppRequestBean();
		request.setReservedTrxNo(reservedTrxNo);
		request.setTemplate(template);
		request.setBodyTexts(bodyTexts);
		request.setMsTenant(tenant);
		request.setPhoneNumber(phoneNo);
		request.setAmMsuser(user);
		request.setNotes(notes);
		
        whatsAppHalosisLogic.sendMessage(request, audit);
		
		SendNotificationTestResponse response = new SendNotificationTestResponse();

		response.setRecipient(phoneNo);
		return response;
	}

    private SendNotificationTestResponse sendNotifTestingSMSJatis(String phoneNo, String message, AmMsuser user, MsTenant tenant, AuditContext audit) {	
		
        Map<String, Object> userMap = new HashMap<>();
		userMap.put("msg", message);
		
		Map<String, Object> param = new HashMap<>();
		param.put("message", userMap);

		String templateCode = GlobalVal.TEMPLATE_SEND_NOTIF_TESTING;
		MsMsgTemplate msgTemplate = messageTemplateLogic.getAndParseContent(templateCode, param);

		long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		
        JatisSmsRequestBean request = new JatisSmsRequestBean(tenant, phoneNo, msgTemplate.getBody(), String.valueOf(nextTrxNo), false);

        String notes = phoneNo + GlobalVal.SEND_SMS_NOTIF_TESTING;

        jatisSmsLogic.sendSmsAndCutBalance(request, null, null, user, notes, audit);

        SendNotificationTestResponse response = new SendNotificationTestResponse();

        response.setRecipient(phoneNo);
        return response;
    }
	
	private SendNotificationTestResponse sendNotifTestingSMSVFirst(String phoneNo, String message, AmMsuser user, MsTenant tenant, AuditContext audit) {
        Map<String, Object> userMap = new HashMap<>();
		userMap.put("msg", message);
		
		Map<String, Object> param = new HashMap<>();
		param.put("message", userMap);

		String templateCode = GlobalVal.TEMPLATE_SEND_NOTIF_TESTING;
		MsMsgTemplate msgTemplate = messageTemplateLogic.getAndParseContent(templateCode, param);

		SendSmsResponse response = new SendSmsResponse();
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNo, msgTemplate.getBody(), tenant);
				
	    response = smsLogic.sendSms(sendSmsValueFirstRequestBean);

		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USMS);
        String notes = phoneNo + GlobalVal.SEND_SMS_NOTIF_TESTING;
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		if (response.getErrorCode() == null || (!response.getErrorCode().equals(VFIRST_ERR28682)
				&& !response.getErrorCode().equals(VFIRST_ERR28681)
				&& !response.getErrorCode().equals(VFIRST_ERR408))) {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), null,
					-1, String.valueOf(response.getTrxNo()), user, notes, response.getGuid(), null, null, audit);
		} else {
			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), null, 0, String.valueOf(response.getTrxNo()), user, notes + GlobalVal.BALMUT_ERROR + response.getErrorCode(), response.getGuid(), audit);
		}

        SendNotificationTestResponse res = new SendNotificationTestResponse();
        res.setRecipient(phoneNo);

        return res;
	}
}