package com.adins.esign.model.custom;

import com.adins.framework.service.base.model.MssRequestType;

public class GenerateInvLinkRequest extends MssRequestType{

	private static final long serialVersionUID = 1L;

	private GenerateInvLinkUserBean[] users;
	private String tenantCode;
	private String referenceNo;
	private String officeCode;
	private String officeName;
	private String regionCode;
	private String regionName;
	private String businessLineCode;
	private String businessLineName;
	private String ipAddress;
	private String psreCode;
	private String roleType;
	
	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public GenerateInvLinkUserBean[] getUsers() {
		return users;
	}

	public void setUsers(GenerateInvLinkUserBean[] users) {
		this.users = users;
	}

	public String getReferenceNo() {
		return referenceNo;
	}

	public void setReferenceNo(String referenceNo) {
		this.referenceNo = referenceNo;
	}

	public String getOfficeCode() {
		return officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getOfficeName() {
		return officeName;
	}

	public void setOfficeName(String officeName) {
		this.officeName = officeName;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public String getBusinessLineCode() {
		return businessLineCode;
	}

	public void setBusinessLineCode(String businessLineCode) {
		this.businessLineCode = businessLineCode;
	}

	public String getBusinessLineName() {
		return businessLineName;
	}

	public void setBusinessLineName(String businessLineName) {
		this.businessLineName = businessLineName;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public String getPsreCode() {
		return psreCode;
	}

	public void setPsreCode(String psreCode) {
		this.psreCode = psreCode;
	}


	public String getRoleType() {
		return this.roleType;
	}

	public void setRoleType(String roleType) {
		this.roleType = roleType;
	}
	
}
