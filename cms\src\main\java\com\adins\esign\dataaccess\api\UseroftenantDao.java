package com.adins.esign.dataaccess.api;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;

public interface UseroftenantDao {
	void insertUseroftenantNewTran(MsUseroftenant useroftenant);
	
	MsUseroftenant getUserTenantByLoginIdAndTenantCode(String loginId, String tenantCode);
	MsUseroftenant getUserTenantByIdMsUserAndTenantCode(Long idMsUser, String tenantCode);
	MsUseroftenant getUserTenantByIdMsUserAndTenantCodeNewTran(Long idMsUser, String tenantCode);
	
	MsUseroftenant getLatestUserTenant(String loginId);
	MsUseroftenant getLatestUseroftenant(AmMsuser user, MsVendor vendor);
}
