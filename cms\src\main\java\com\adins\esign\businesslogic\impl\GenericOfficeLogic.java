package com.adins.esign.businesslogic.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.OfficeLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.OfficeBean;
import com.adins.esign.webservices.model.OfficeListEmbedRequest;
import com.adins.esign.webservices.model.OfficeListRequest;
import com.adins.esign.webservices.model.OfficeListResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericOfficeLogic extends BaseLogic implements OfficeLogic {

	@Autowired CommonLogic commonLogic;
	
	@Override
	public OfficeListResponse getOfficeList(OfficeListRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getTenantCode())) {
			throw new TenantException(getMessage("businesslogic.paymentsigntype.emptytenantcode", null, audit), ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		
		if (null == tenant) {
			throw new TenantException(getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {"Tenant code", request.getTenantCode()}, audit), ReasonTenant.TENANT_NOT_FOUND);
		}
		
		if (StringUtils.isNotBlank(request.getRegionCode()) ) {
			MsRegion region = daoFactory.getRegionDao().getRegionByCodeAndTenant(request.getRegionCode(), request.getTenantCode());
			
			if (null ==  region)
			{
				throw new TenantException(getMessage("businessLogic.tenant.invalidregion", new String[] {request.getRegionCode()}, audit), ReasonTenant.TENANT_CODE_EMPTY);
			}
		}
		
		
		OfficeListResponse response = new OfficeListResponse();
		List<OfficeBean> officeBeanList = new ArrayList<>();
		
		List<MsOffice> msOfficeList = daoFactory.getOfficeDao().getOfficeListByTenantCodeAndRegionCode(request.getTenantCode(),request.getRegionCode());
		for (MsOffice office : msOfficeList) {
			OfficeBean bean = new OfficeBean();
			bean.setOfficeCode(office.getOfficeCode());
			bean.setOfficeName(office.getOfficeName());
			officeBeanList.add(bean);
		}
		response.setOfficeList(officeBeanList);
		return response;
	}

	@Override
	public OfficeListResponse getOfficeListEmbed(OfficeListEmbedRequest request, AuditContext audit) {
		EmbedMsgBean msgBean =  commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		audit.setCallerId(msgBean.getEmail());
		
		if (StringUtils.isBlank(msgBean.getTenantCode())) {
			throw new TenantException(messageSource.getMessage("businesslogic.paymentsigntype.emptytenantcode", null
					, this.retrieveLocaleAudit(audit))
					, ReasonTenant.TENANT_CODE_EMPTY);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		if (null == tenant) {
			throw new TenantException(getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {"Tenant code", msgBean.getTenantCode()}, audit), ReasonTenant.TENANT_NOT_FOUND);
		}
		
	
		OfficeListResponse response = new OfficeListResponse();
		List<OfficeBean> officeBeanList = new ArrayList<>();
		
		List<MsOffice> officeList = daoFactory.getOfficeDao().getOfficeList(tenant.getTenantCode());
		for (MsOffice office : officeList) {
			OfficeBean bean = new OfficeBean();
			bean.setOfficeCode(commonLogic.encryptMessageToString(office.getOfficeCode(), audit));
			bean.setOfficeName(office.getOfficeName());
			officeBeanList.add(bean);
		}
		response.setOfficeList(officeBeanList);
		return response;
	}

	@Override
	public MsOffice insertOffice(String officeName, String officeCode, MsTenant tenant, AuditContext audit) {
		MsOffice office = new MsOffice();
		office.setOfficeCode(StringUtils.upperCase(officeCode));
		office.setIsActive("1");
		office.setOfficeName(officeName);
		office.setMsTenant(tenant);
		office.setUsrCrt(audit.getCallerId());
		office.setDtmCrt(new Date());
		
		daoFactory.getOfficeDao().insertOffice(office);
		return office;
	}

	@Override
	public MsOffice insertUnregisteredOffice(String officeName, String officeCode, MsRegion region, MsTenant tenant, AuditContext audit) {
		if (StringUtils.isBlank(officeCode)) {
			return null;
		}
		
		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(officeCode, tenant.getTenantCode());
		if (null == office && StringUtils.isNotBlank(officeName)) {
			office = new MsOffice();
			office.setUsrCrt(audit.getCallerId());
			office.setIsActive("1");
			office.setDtmCrt(new Date());
			office.setOfficeCode(StringUtils.upperCase(officeCode));
			office.setOfficeName(officeName);
			office.setMsTenant(tenant);
			office.setMsRegion(region);
			daoFactory.getOfficeDao().insertOffice(office);
		} else if (null == office && StringUtils.isBlank(officeName)) {
			String var = messageSource.getMessage("businesslogic.insertstamping.var.officeName", null, this.retrieveLocaleAudit(audit));
			throw new ParameterException(this.messageSource.getMessage("businesslogic.document.mandatorycannotbeempty", new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonParam.MANDATORY_PARAM);
		}
		
		return office;
	}

	@Override
	public MsOffice getOfficeByCodeAndTenant(String officeCode, boolean checkOfficeExistence, MsTenant tenant, AuditContext audit) {
		MsOffice office = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(officeCode, tenant.getTenantCode());
		if (checkOfficeExistence && null == office) {
			throw new CommonException(getMessage("businesslogic.global.datanotfound", new String[] {"Office", officeCode}, audit), ReasonCommon.DATA_NOT_EXISTED);
		}
		
		return office;
	}

}
