package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.esign.businesslogic.api.SchedulerLogic;

@DisallowConcurrentExecution
@Component
public class LocationJob extends BaseLogic {
	private static final Logger LOG = LoggerFactory.getLogger(LocationJob.class);
	private static final String SCHEDULER = "SCHEDULER";

	@Autowired SchedulerLogic schedulerLogic;
	
	public void runLocation() {
		try {
			LOG.info("Job Location Started");
			AuditContext auditContext = new AuditContext(SCHEDULER);
			schedulerLogic.syncLocation(auditContext);
			LOG.info("Job Location Finished");
		} catch (Exception e) {
			LOG.error("Error on running Job Location", e);
		}
	}
}
