package com.adins.esign.dataaccess.api;

import com.adins.am.model.AmGeneralsetting;
import com.adins.esign.model.MsPeruriDocType;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.DocumentEMateraiTypeBean;
import java.util.List;

public interface CommonDao {
	String getUuidDao();
	long nextSequenceTrBalanceMutationTrxNo();
	AmGeneralsetting getGeneralSetting(String code);
	AmGeneralsetting getGeneralSettingByTenant(String code, MsTenant tenant);
	void updateGeneralSetting(AmGeneralsetting generalSetting);
	
	// FIXME: Buat interface baru PeruriDocTypeDao, dan pindah ke situ
	MsPeruriDocType getMsPeruriDocTypeByDocId(String peruriDocId);
	void insertMsPeruriDocType(MsPeruriDocType msPeruriDocType);
	void updateMsPeruriDocType(MsPeruriDocType msPeruriDocType);

	List<DocumentEMateraiTypeBean> getListDocumentEMateraiType(String isActive);
	void deactiveDocumentNotInDocId(List<String> allperuriDocId, String usrUpd);
}
