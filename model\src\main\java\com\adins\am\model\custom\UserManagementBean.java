package com.adins.am.model.custom;

import java.util.Date;

import com.adins.am.model.AmMsuser;


@SuppressWarnings("serial")
public class UserManagementBean implements java.io.Serializable  {
	private AmMsuser bean = new AmMsuser();
	private String zipCodeBranch;
	private String zipCode;
	private String areaCode;
	private String errorText;
	private Date emergencyDate;
	private String stateEmergency;
	
	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public AmMsuser getBean() {
		return bean;
	}

	public void setBean(AmMsuser bean) {
		this.bean = bean;
	}

	public String getZipCode() {
		return zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	public String getZipCodeBranch() {
		return zipCodeBranch;
	}

	public void setZipCodeBranch(String zipCodeBranch) {
		this.zipCodeBranch = zipCodeBranch;
	}

	public String getErrorText() {
		return errorText;
	}

	public void setErrorText(String errorText) {
		this.errorText = errorText;
	}

	public Date getEmergencyDate() {
		return emergencyDate;
	}

	public void setEmergencyDate(Date emergencyDate) {
		this.emergencyDate = emergencyDate;
	}

	public String getStateEmergency() {
		return stateEmergency;
	}

	public void setStateEmergency(String stateEmergency) {
		this.stateEmergency = stateEmergency;
	}
}
