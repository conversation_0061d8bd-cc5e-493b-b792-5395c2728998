package com.adins.am.model;
// Generated 09-Sep-2021 22:49:32 by Hibernate Tools 5.2.12.Final

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import com.adins.am.model.custom.ActiveDeleteAndUpdateableEntity;
import com.adins.esign.model.MsTenant;

/**
 * AmMsrole generated by hbm2java
 */
@SuppressWarnings("squid:S107")
@Entity
@Table(name = "am_msrole")
public class AmMsrole extends ActiveDeleteAndUpdateableEntity implements java.io.Serializable {
	private static final long serialVersionUID = 1L;
	public static final String ROLE_CODE_HBM = "roleCode";
	public static final String AM_MSROLE_HBM = "amMsrole";
	public static final String ROLE_NAME_HBM = "roleName";

	
	private long idMsRole;
	private String roleName;
	private String roleCode;
	private MsTenant msTenant;
	private String isUserManagement;
	private Set<AmMemberofrole> amMemberofroles = new HashSet<>(0);
	private Set<AmMenuofrole> amMenuofroles = new HashSet<>(0);

	public AmMsrole() {
	}

	public AmMsrole(long idMsRole, String usrCrt, Date dtmCrt, String roleName, String roleCode, String isUserManagement, MsTenant msTenant) {
		this.idMsRole = idMsRole;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.roleName = roleName;
		this.roleCode = roleCode;
		this.msTenant = msTenant;
	}

	public AmMsrole(long idMsRole, String usrCrt, Date dtmCrt, String usrUpd, Date dtmUpd, String isActive,
			String roleName, String roleCode, String isDeleted, String isUserManagement, MsTenant msTenant, Set<AmMemberofrole> amMemberofroles,
			Set<AmMenuofrole> amMenuofroles) {
		this.idMsRole = idMsRole;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
		this.isActive = isActive;
		this.roleName = roleName;
		this.roleCode = roleCode;
		this.isDeleted = isDeleted;
		this.msTenant = msTenant;
		this.amMemberofroles = amMemberofroles;
		this.amMenuofroles = amMenuofroles;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_ms_role", unique = true, nullable = false)
	public long getIdMsRole() {
		return this.idMsRole;
	}

	public void setIdMsRole(long idMsRole) {
		this.idMsRole = idMsRole;
	}

	@Column(name = "role_name", nullable = false, length = 100)
	public String getRoleName() {
		return this.roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	@Column(name = "role_code", nullable = false, length = 20)
	public String getRoleCode() {
		return this.roleCode;
	}

	public void setRoleCode(String roleCode) {
		this.roleCode = roleCode;
	}


	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant", nullable = false)
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsrole")
	public Set<AmMemberofrole> getAmMemberofroles() {
		return this.amMemberofroles;
	}

	public void setAmMemberofroles(Set<AmMemberofrole> amMemberofroles) {
		this.amMemberofroles = amMemberofroles;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "amMsrole")
	public Set<AmMenuofrole> getAmMenuofroles() {
		return this.amMenuofroles;
	}

	public void setAmMenuofroles(Set<AmMenuofrole> amMenuofroles) {
		this.amMenuofroles = amMenuofroles;
	}
	
	@Column(name= "is_usermanagement", length = 1)
	public String getIsUserManagement() {
		return isUserManagement;
	}

	public void setIsUserManagement(String isUserManagement) {
		this.isUserManagement = isUserManagement;
	}

}
