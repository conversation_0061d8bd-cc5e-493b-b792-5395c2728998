package com.adins.esign.job;

import java.text.ParseException;
import java.util.Set;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.esign.model.custom.BalanceCheckQueueBean;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class EmailReminderJob {
	private static final Logger LOG = LoggerFactory.getLogger(EmailReminderJob.class);
	private static final String SCHEDULER = "SCHEDULER";
	
	@Autowired private SchedulerLogic schedulerLogic;
	@Autowired private SaldoLogic saldoLogic;
	
	public void runEmailReminder() {
		try {
			LOG.info("Job Email Reminder Started");
			AuditContext auditContext = new AuditContext(SCHEDULER);
			schedulerLogic.emailReminderTopUp(auditContext);
			LOG.info("Job Email Reminder Finished");
		} catch (Exception e) {
			LOG.error("Error on running Email Reminder job", e);
		}
	}
	
	public void takeQueueBalanceCheck() {
		Set<BalanceCheckQueueBean> set = QueuePublisher.getQueueBalanceCheck();

		set.forEach(item -> {
			LOG.debug( "Consumed: {}", item);
			try {
				saldoLogic.sendReminderEmail(item.getTenantCode(), item.getVendorCode(), item.getBalanceTypeCode());
			} catch (ParseException e) {
				LOG.error("Error on running take queue balance check", e);
			}
		});
		
		set.clear(); //Penting! supaya tidak diproses > 1x
		LOG.debug( "Queue balance check Empty! Consumer Finished!" );
	}
}
