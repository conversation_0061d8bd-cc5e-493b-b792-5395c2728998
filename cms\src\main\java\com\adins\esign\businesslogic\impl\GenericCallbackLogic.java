package com.adins.esign.businesslogic.impl;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.CallbackLogic;
import com.adins.esign.businesslogic.api.FunctionComputeLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrClientCallbackRequest;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.custom.TenantCallbackBean;
import com.adins.esign.model.custom.TenantCallbackRequestBean;
import com.adins.esign.util.MssTool;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.google.gson.Gson;

@Component
public class GenericCallbackLogic extends BaseLogic implements CallbackLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericCallbackLogic.class);
	
	@Autowired private PersonalDataEncryptionLogic encryptionLogic;
	@Autowired private FunctionComputeLogic functionComputeLogic;
	@Autowired private Gson gson;

	@Override
	@Async
	public void executeCallbackToClient(MsTenant tenant, MsLov lovCallbackType, MsVendorRegisteredUser vendorUser, TrDocumentD document, String callbackMessage, AuditContext audit) {
		
		if (StringUtils.isBlank(tenant.getClientCallbackUrl())) {
			return;
		}
		
		if (!needToExecuteCallback(tenant, lovCallbackType)) {
			LOG.debug("Skipping callback to {} for callback type {}", tenant.getTenantCode(), lovCallbackType.getCode());
			return;
		}
		
		TenantCallbackBean data = prepareCallbackData(lovCallbackType, vendorUser, document);
		
		TenantCallbackRequestBean request = new TenantCallbackRequestBean();
		request.setCallbackType(lovCallbackType.getCode());
		request.setTimeStamp(MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_TIME_FORMAT_SEC));
		request.setData(data);
		request.setMessage(callbackMessage);
		
		String jsonRequest = gson.toJson(request);
		
		TrClientCallbackRequest callbackRequest = new TrClientCallbackRequest();
		callbackRequest.setRequestStatus((short) 1);
		callbackRequest.setRequestDate(new Date());
		callbackRequest.setMsTenant(tenant);
		callbackRequest.setLovCallbackType(lovCallbackType);
		
		if (null != vendorUser) {
			callbackRequest.setAmMsuser(vendorUser.getAmMsuser());
		}
		
		if (null != document) {
			callbackRequest.setTrDocumentD(document);
		}
		
		callbackRequest.setCallbackRequest(jsonRequest);
		callbackRequest.setUsrCrt(audit.getCallerId());
		callbackRequest.setDtmCrt(new Date());
		daoFactory.getClientCallbackRequestDao().insertClientCallbackRequestNewTrx(callbackRequest);
		
		functionComputeLogic.invokeClientCallback(callbackRequest.getIdClientCallbackRequest());
	}
	
	private TenantCallbackBean prepareCallbackData(MsLov lovCallbackType, MsVendorRegisteredUser vendorUser, TrDocumentD document) {
		TenantCallbackBean data = new TenantCallbackBean();
		
		if (GlobalVal.CODE_LOV_CALLBACK_TYPE_ACTIVATION_COMPLETE.equals(lovCallbackType.getCode())) {
			String phoneNumber = encryptionLogic.decryptToString(vendorUser.getPhoneBytea());
			data.setPhone(phoneNumber);
			data.setEmail(vendorUser.getSignerRegisteredEmail());
		}
		
		if (GlobalVal.CODE_LOV_CALLBACK_TYPE_SIGNING_COMPLETE.equals(lovCallbackType.getCode())) {
			data.setEmail(vendorUser.getSignerRegisteredEmail());
			data.setDocumentId(document.getDocumentId());
		}
		
		if (GlobalVal.CODE_LOV_CALLBACK_TYPE_DOCUMENT_SIGN_COMPLETE.equals(lovCallbackType.getCode())) {
			data.setDocumentId(document.getDocumentId());
		}
		
		if (GlobalVal.CODE_LOV_CALLBACK_TYPE_ALL_DOCUMENT_SIGN_COMPLETE.equals(lovCallbackType.getCode())) {
			data.setRefNo(document.getTrDocumentH().getRefNumber());
		}
		return data;
	}
	
	private boolean needToExecuteCallback(MsTenant tenant, MsLov lovCallbackType) {
		if (GlobalVal.CODE_LOV_CALLBACK_TYPE_ACTIVATION_COMPLETE.equals(lovCallbackType.getCode())) {
			MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ACTIVATION_CALLBACK);
			return settings != null && "1".equals(settings.getSettingValue());
		}
		
		if (GlobalVal.CODE_LOV_CALLBACK_TYPE_SIGNING_COMPLETE.equals(lovCallbackType.getCode())) {
			MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_SIGNER_COMPLETE_CALLBACK);
			return settings != null && "1".equals(settings.getSettingValue());
		}
		
		if (GlobalVal.CODE_LOV_CALLBACK_TYPE_DOCUMENT_SIGN_COMPLETE.equals(lovCallbackType.getCode())) {
			MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_DOCUMENT_COMPLETE_CALLBACK);
			return settings != null && "1".equals(settings.getSettingValue());
		}
		
		if (GlobalVal.CODE_LOV_CALLBACK_TYPE_ALL_DOCUMENT_SIGN_COMPLETE.equals(lovCallbackType.getCode())) {
			MsTenantSettings settings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ALL_DOCUMENT_COMPLETE_CALLBACK);
			return settings != null && "1".equals(settings.getSettingValue());
		}
		
		return false;
	}

}
