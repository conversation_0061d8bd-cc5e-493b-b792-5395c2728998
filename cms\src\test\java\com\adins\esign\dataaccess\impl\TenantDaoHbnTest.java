package com.adins.esign.dataaccess.impl;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.TenantDao;
import com.adins.esign.dataaccess.api.VendorDao;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.AuditDataType;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class TenantDaoHbnTest {
	@Autowired private TenantDao tenantDao;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private UserLogic userLogic;
	
	private AuditDataType auditData;
	private AuditContext auditContext;

	private MsTenant tenantJunit;
	private AmMsuser userJunit;
	
	@BeforeEach
	public void setUp() {
		auditData = new AuditDataType();
		auditData.setCallerId("INITIAL");
		
		auditContext = new AuditContext(auditData.getCallerId());
		
		tenantJunit = tenantLogic.getTenantById((long) 1, auditContext);
		userJunit = userLogic.getUserByLoginId("<EMAIL>");
	}
	
	@Order(1)
	@Test
	void getUseroftenantByUserTenantTest() {
		MsUseroftenant userTenant = tenantDao.getUseroftenantByUserTenant(userJunit, tenantJunit);
		assertNotNull(userTenant);
	}
	
	@Order(2)
	@Test
	void getUseroftenantByLoginIdTenantCode() {
		MsUseroftenant userTenant = tenantDao.getUseroftenantByLoginIdTenantCode(userJunit.getLoginId(), tenantJunit.getTenantCode());
		assertNotNull(userTenant);
	}
}
