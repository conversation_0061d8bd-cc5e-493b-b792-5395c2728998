package com.adins.esign.businesslogic.impl;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.mail.MessagingException;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsdistrict;
import com.adins.am.model.AmMsprovince;
import com.adins.am.model.AmMssubdistrict;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.EmailLogic;
import com.adins.esign.businesslogic.api.EmailPatternLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.FileAccessLogic;
import com.adins.esign.businesslogic.api.FunctionComputeLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SchedulerLogic;
import com.adins.esign.businesslogic.api.SmsLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.businesslogic.api.interfacing.EmeteraiPajakkuLogic;
import com.adins.esign.businesslogic.api.interfacing.JatisSmsLogic;
import com.adins.esign.businesslogic.api.interfacing.TekenAjaLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppLogic;
import com.adins.esign.confins.model.UploadToCoreBean;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.MediaType;
import com.adins.esign.constants.enums.NotificationSendingPoint;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsBalancevendoroftenant;
import com.adins.esign.model.MsEmailHosting;
import com.adins.esign.model.MsEmailPattern;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsPeruriDocType;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrBalanceDailyRecap;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.custom.BalanceBean;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrDocumentSigningRequest;
import com.adins.esign.model.TrErrorHistory;
import com.adins.esign.model.TrJobCheckRegisterStatus;
import com.adins.esign.model.TrSchedulerJob;
import com.adins.esign.model.custom.BalanceDigiBean;
import com.adins.esign.model.custom.BalanceDigiResponseBean;
import com.adins.esign.model.custom.CheckDigiCertExpDateResponse;
import com.adins.esign.model.custom.DeleteEmailParam;
import com.adins.esign.model.custom.DeleteOnPremResultBean;
import com.adins.esign.model.custom.DocHBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.ExpungeEmailResultBean;
import com.adins.esign.model.custom.PajakkuDocumentTypeBean;
import com.adins.esign.model.custom.PajakkuDocumentTypeResultBean;
import com.adins.esign.model.custom.ReadEmailBean;
import com.adins.esign.model.custom.ResumeWorkflowReturnBean;
import com.adins.esign.model.custom.StampDutyBean;
import com.adins.esign.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsResponse;
import com.adins.esign.model.custom.tekenaja.TekenAjaLocationBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.BalanceRequest;
import com.adins.esign.webservices.model.BalanceResponse;
import com.adins.esign.webservices.model.DummyClientURLUploadRequest;
import com.adins.esign.webservices.model.SendSmsResponse;
import com.adins.esign.webservices.model.SendSmsValueFirstRequestBean;
import com.adins.esign.webservices.model.SendWhatsAppRequest;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.EmailException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.EmailException.ReasonEmail;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.AuditDataType;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.util.Tool;
import com.google.gson.Gson;

import okhttp3.Headers;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

@Transactional
@Component
public class GenericSchedulerLogic extends BaseLogic implements SchedulerLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericSchedulerLogic.class);
	
	@Autowired private Gson gson;
	
	@Autowired private CommonLogic commonLogic;
	@Autowired private DocumentLogic documentLogic;
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private MessageTemplateLogic messageTemplateLogic;
	@Autowired private EmailLogic emailLogic;
	@Autowired private SmsLogic smsLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private DigisignLogic digisignLogic;
	@Autowired private TekenAjaLogic tekenAjaLogic;
	@Autowired private EmeteraiPajakkuLogic emateraiPajakkuLogic;
	@Autowired private FileAccessLogic fileAccessLogic;
	@Autowired private FunctionComputeLogic functionComputeLogic;
	@Autowired private JatisSmsLogic jatisSmsLogic;
	@Autowired private WhatsAppLogic whatsAppLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncryptionLogic;
	@Autowired private EmailPatternLogic emailPatterLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	private static final String[] DEFAULT_EMAIL_ESIGN_PIC = {"<EMAIL>", "<EMAIL>"};
	
	@Value("${spring.mail.username}") private String fromEmailAddr;

	@Override
	public void dailyRecap(AuditContext audit) {
		LocalDate localDate = LocalDate.now().minusDays(1);
		Date recapDate = java.sql.Date.valueOf(localDate); // scheduler on 00:00:01, jadi local date -1 untuk rekap

		List<MsBalancevendoroftenant> listBalance = daoFactory.getVendorDao().getListMsBalancevendoroftenant();
		for (MsBalancevendoroftenant bvot : listBalance) {
			if ("1".equalsIgnoreCase(bvot.getMsTenant().getIsActive())) {
				this.balanceDailyRecap(recapDate, bvot.getMsLov(), bvot.getMsTenant(), bvot.getMsVendor(), audit);
			}
		}

		String notes = "";
		this.insertTrSchedulerJob(recapDate, GlobalVal.CODE_LOV_SCHED_TYPE_DAILY, GlobalVal.CODE_LOV_JOB_TYPE_BALRECAP,
				0, notes, audit);
	}

	private void balanceDailyRecap(Date recapDate, MsLov balanceTypeLov, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		String recapDateString = MssTool.formatDateToStringIn(recapDate, GlobalVal.DATE_FORMAT); // yyyy-MM-dd 00:00:00

		if (!checkDailyRecapExisted(recapDateString, balanceTypeLov, tenant, vendor)) {
			int qtyBalance = daoFactory.getDailyRecapDao().countQtyDailyRecap(recapDateString, balanceTypeLov, tenant,
					vendor);
			LOG.info("Tenant {}, Vendor {}, Balance type {}, Balance {}", tenant.getTenantCode(),
					vendor.getVendorCode(), balanceTypeLov.getCode(), qtyBalance);
			this.insertDailyRecap(recapDate, qtyBalance, balanceTypeLov, tenant, vendor, audit);
		}
	}

	private void insertDailyRecap(Date recapDate, int qtyBalanceRecap, MsLov balanceType, MsTenant tenant,
			MsVendor vendor, AuditContext audit) {

		TrBalanceDailyRecap dailyRecap = new TrBalanceDailyRecap();
		dailyRecap.setUsrCrt(audit.getCallerId());
		dailyRecap.setDtmCrt(new Date());
		dailyRecap.setMsLov(balanceType);
		dailyRecap.setMsTenant(tenant);
		dailyRecap.setMsVendor(vendor);
		dailyRecap.setRecapDate(MssTool.changeDateFormat(recapDate, GlobalVal.DATE_FORMAT));
		dailyRecap.setRecapTotalBalance(qtyBalanceRecap);
		this.daoFactory.getDailyRecapDao().insertTrBalanceDailyRecap(dailyRecap);
	}

	
	
	
	private boolean checkDailyRecapExisted(String dateRecap, MsLov balanceType, MsTenant tenant, MsVendor vendor) {
		TrBalanceDailyRecap existingDailyRecap = this.daoFactory.getDailyRecapDao().getDailyRecap(dateRecap,
				balanceType, tenant, vendor);
		return (0 != existingDailyRecap.getIdBalanceDailyRecap()); // empty object model has id = 0
	}

	@Override
	public void emailReminderTopUp(AuditContext audit) throws ParseException {
		TrSchedulerJob schedJob = new TrSchedulerJob();

		List<MsBalancevendoroftenant> listBalance = daoFactory.getVendorDao().getListMsBalancevendoroftenant();
		for (MsBalancevendoroftenant bvot : listBalance) {
			MsTenant tenant = bvot.getMsTenant();
			if ("1".equalsIgnoreCase(tenant.getIsActive())) {
				if (StringUtils.isBlank(tenant.getEmailReminderDest())) {
					LOG.info("Tenant {}, Vendor {}, Balance {}, email reminder recipient empty", tenant.getTenantName(), bvot.getMsVendor().getVendorName(), bvot.getMsLov().getDescription());
					continue;
				}
				schedJob = daoFactory.getSchedulerJobDao().getSchedulerJobByTenant(
						MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_FORMAT), GlobalVal.CODE_LOV_JOB_TYPE_BALREM,
						bvot.getMsLov().getCode(),tenant.getTenantCode());
				// split string by comma and trim
				if (null == schedJob) {
					String[] emailReminderDestinations = tenant.getEmailReminderDest().split("\\s*,\\s*"); 
					this.reminderBalance(bvot.getMsLov().getCode(), emailReminderDestinations, tenant, bvot.getMsVendor(),
							audit);
					
					
				} else {
					LOG.info("Tenant {}, Vendor {}, Balance {}, Sudah pernah kirim email reminder hari ini, tidak mengirim email reminder lagi", tenant.getTenantName(),bvot.getMsVendor().getVendorName(), bvot.getMsLov().getDescription());
					
				}
				
			}
		}

		

	}

	@Override
	public void reminderBalance(String balanceType, String[] emailReminderDestinations, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		Date startDate = new Date();
		MsLov balanceTypeDoc = this.commonLogic.getLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceType, audit);
		if (null == balanceTypeDoc) {
			LOG.info("Tenant {}, Vendor {}, balance {} is not active or not found", tenant.getTenantName(), vendor.getVendorName(), balanceType);
			return;
		}
		
		BalanceBean balanceBeanBelowThreshold = saldoLogic.checkSaldoBelowThreshold(tenant, vendor, balanceTypeDoc);
		if (null == balanceBeanBelowThreshold) {
			LOG.info("Does not need to send balance reminder for tenant {}, vendor {}, balance {}",
					tenant.getTenantName(), vendor.getVendorName(), balanceTypeDoc.getDescription());
			return;
		}
		TrSchedulerJob schedJob = new TrSchedulerJob();
		MsLov jobTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_JOB_TYPE
				, GlobalVal.CODE_LOV_JOB_TYPE_BALREM);
		MsLov schedTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SCHEDULER_TYPE
				, GlobalVal.CODE_LOV_SCHED_TYPE_DAILY);

		schedJob = new TrSchedulerJob();
		schedJob.setUsrCrt("SCHEDULER EMAIL REMINDER");
		schedJob.setDtmCrt(new Date());
		schedJob.setMsLovByBalanceType(balanceTypeDoc);
		schedJob.setMsLovByJobType(jobTypeLov);
		schedJob.setMsLovBySchedulerType(schedTypeLov);
		schedJob.setSchedulerStart(startDate);
		schedJob.setSchedulerEnd(new Date());
		schedJob.setMsTenant(tenant);
		schedJob.setMailReminderCount(new Short("1"));

		daoFactory.getSchedulerJobDao().insertSchedulerJob(schedJob);
		sendEmailReminder(emailReminderDestinations, tenant.getTenantName(), vendor.getVendorName(), balanceTypeDoc.getDescription(), balanceBeanBelowThreshold.getCurrentBalance());
	}

	@Override
	public void sendEmailReminder(String[] emailReminderDestinations, String tenantName, String vendorName, String balanceType, BigInteger saldo) {
		
		LOG.info("Send email reminder to {}, tenant {}, vendor {}, desc {}, balance {}", emailReminderDestinations, tenantName, vendorName, balanceType, saldo.longValue());

		Map<String, Object> reminder = new HashMap<>();
		reminder.put("vendorName", vendorName);
		reminder.put("balanceType", balanceType);
		reminder.put("saldo", saldo);
		reminder.put("tenantName", tenantName);
		reminder.put("fullname", tenantName);

		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("reminder", reminder);

		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_REMINDER_TOPUP, templateParameters);
		String[] recipient = emailReminderDestinations;

		EmailInformationBean emailBean = new EmailInformationBean();
		emailBean.setFrom(fromEmailAddr);
		emailBean.setTo(recipient);
		emailBean.setBodyMessage(template.getBody());
		emailBean.setSubject(template.getSubject());

		try {
			emailSenderLogic.sendEmail(emailBean, null);
		} catch (MessagingException e) {
			throw new DocumentException(ReasonDocument.UNKNOWN);
		}
	}

	@Override
	public void readEmail(AuditContext audit) {
		Date startTime = new Date();
		long totalData = 0;

		List<MsEmailHosting> emailHostingList = daoFactory.getEmailDao().getListActiveEmailHosting();
		for (MsEmailHosting msEmailHosting : emailHostingList) {
			
			String email = msEmailHosting.getDefaultEmailInbox();
			String password = msEmailHosting.getDefaultEmailPassword();
			
			// Get emails content
			List<ReadEmailBean> emailList = emailLogic.readEmail(email, password);
			totalData += emailList.size();
			
			// Forward content to SMS / WA
			this.forwardEmails(emailList, audit);

			// Delete email
//			DeleteEmailParam param = new DeleteEmailParam();
//			param.setDeleteType(GlobalVal.DELETE_EMAIL_TYPE_READ);
//			try {
//				emailLogic.deleteEmail(email, password, param);
//			} catch (Exception e) {
//				LOG.error("Error when deleting email from {}", email, e);
//			}
		}
		if (totalData > 0) {
			String notes = "";
			this.insertTrSchedulerJob(startTime, GlobalVal.CODE_LOV_SCHED_TYPE_CONTINOUS,
					GlobalVal.CODE_LOV_JOB_TYPE_REMAIL, totalData, notes, audit);
		}
	}

	private void forwardEmails(List<ReadEmailBean> emailList, AuditContext audit) {
		for (ReadEmailBean readEmailBean : emailList) {
			this.forwardEmail(readEmailBean, audit);
		}
	}

	private void forwardEmail(ReadEmailBean emailBean, AuditContext audit) {
		
		// Split untuk handling email dari TekenAja karena subject emailnya diakhiri dengan '- {nama_dokumen}'
		String[] subject = emailBean.getSubject().split(" - ");
		if (StringUtils.isNotBlank(subject[0])) {
			emailBean.setSubject(subject[0]);
		}
		
		String[] actions = {GlobalVal.EMAIL_PATTERN_FORWARD, GlobalVal.EMAIL_PATTERN_SHORTEN_FORWARD};
		MsEmailPattern emailPattern = daoFactory.getEmailDao().getEmailPattern(emailBean.getSubject(), emailBean.getSender(), actions);
		if (null == emailPattern) {
			LOG.warn("Email with subject: {} and sender: {} is not found in ms_email_pattern. Forwarding email to Esign PIC.", emailBean.getSubject(), emailBean.getSender());
			this.sendEmailToEsignPic(emailBean);
			return;
		}
		
		if (!isEmailNeededToForward(emailBean)) {
			return;
		}
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(emailBean.getRecipient(), false, audit);
		if (null == user) {
			LOG.warn("User with email {} is not found. Forwarding email to eSignHub PIC.", emailBean.getRecipient());
			this.sendEmailToEsignPic(emailBean);
			return;
		}
		
		MsVendor vendor = emailPattern.getMsVendor();
		if (null == vendor) {
			LOG.warn("Vendor is not found. Forwarding email to eSignHub PIC.");
			this.sendEmailToEsignPic(emailBean);
			return;
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendor.getVendorCode());
		if (null == vendorUser) {
			LOG.warn("Vendor registered user is not found. Forwarding email to eSignHub PIC.");
			this.sendEmailToEsignPic(emailBean);
			return;
		}

		TrDocumentD docDetail = daoFactory.getDocumentDao().getLatestDocumentDetailBySigner(vendorUser.getAmMsuser());
		MsTenant tenant;
		if (null != docDetail) {
			tenant = docDetail.getMsTenant();
		} else {
			tenant = daoFactory.getTenantDao().getTenantByUser(user.getLoginId());
		}

		if (null == tenant) {
			LOG.warn("Tenant or vendor is not found. Forwarding email to eSignHub PIC.");
			this.sendEmailToEsignPic(emailBean);
			return;
		}
		
		NotificationSendingPoint sendingPoint = NotificationSendingPoint.getSendingPointByCode(emailPattern.getLovSendingPoint().getCode());
		NotificationType notifType = tenantLogic.getNotificationType(tenant, sendingPoint, vendorUser.getEmailService());
		
		String forwardedContent = null;
		if (GlobalVal.EMAIL_PATTERN_SHORTEN_FORWARD.equals(emailPattern.getAction())) {
			String parsedContent = emailLogic.parseEmailContent(emailBean, emailPattern);
			forwardedContent = emailPatterLogic.shortenParsedEmailContent(emailPattern, user, notifType, parsedContent, audit);
		} else {
			forwardedContent = emailLogic.parseEmailContent(emailBean, emailPattern);
		}
		
		Map<String, Object> userMap = new HashMap<>();
		userMap.put("parsed", forwardedContent);

		Map<String, Object> templateParameteres = new HashMap<>();
		templateParameteres.put("user", userMap);
		
		String templateCode = emailPattern.getMsMsgTemplate().getTemplateCode();
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(templateCode, templateParameteres);
		
		String phoneNo = personalDataEncryptionLogic.decryptToString(vendorUser.getPhoneBytea());
		
		
		if (NotificationType.WHATSAPP == notifType) {
			forwardWhatsApp(emailPattern.getMsMsgTemplateWa(), forwardedContent, phoneNo, user, tenant, emailPattern.getLovBalanceType(), audit);
		} else if (NotificationType.SMS_VFIRST == notifType) {
			forwardSmsVfirst(phoneNo, template.getBody(), tenant, user, emailPattern.getLovBalanceType(), audit);
		} else if (NotificationType.SMS_JATIS == notifType) {
			forwardSmsJatis(phoneNo, template.getBody(), tenant, user, emailPattern.getLovBalanceType(), audit);
		}
		
	}
	
	private void forwardSmsVfirst(String phoneNumber, String smsBody, MsTenant tenant, AmMsuser user, MsLov lovBalanceType, AuditContext audit) {
		
		MsLov balanceType = null;
		MsLov trxType = null;
		if (null == lovBalanceType) {
			balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
			trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		} else {
			balanceType = lovBalanceType;
			trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, "U" + balanceType.getCode());
		}
		
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(phoneNumber, smsBody, tenant);
		SendSmsResponse response = smsLogic.sendSms(sendSmsValueFirstRequestBean);
		String notes = phoneNumber + " : Forward Email to SMS ";
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		
		if (response.getErrorCode() == null || 
				(!response.getErrorCode().equals("28682") 
				&& !response.getErrorCode().equals("28681")
				&& !response.getErrorCode().equals("408"))) {
			
			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(String.valueOf(response.getTrxNo()));
			mutation.setTrxDate(new Date());
			mutation.setQty(-1);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			
			if (GlobalVal.CODE_LOV_BALANCE_TYPE_OTP.equals(balanceType.getCode()) && null != user) {
				mutation.setUsrCrt(user.getLoginId());
			} else {
				mutation.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
			}
			
			mutation.setDtmCrt(new Date());
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setAmMsuser(user);
			mutation.setNotes(notes);
			mutation.setVendorTrxNo(response.getGuid());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
			
		} else {
			
			TrBalanceMutation mutation = new TrBalanceMutation();
			mutation.setTrxNo(String.valueOf(response.getTrxNo()));
			mutation.setTrxDate(new Date());
			mutation.setQty(0);
			mutation.setMsLovByLovBalanceType(balanceType);
			mutation.setMsLovByLovTrxType(trxType);
			
			if (GlobalVal.CODE_LOV_BALANCE_TYPE_OTP.equals(balanceType.getCode()) && null != user) {
				mutation.setUsrCrt(user.getLoginId());
			} else {
				mutation.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
			}
			
			mutation.setDtmCrt(new Date());
			mutation.setMsTenant(tenant);
			mutation.setMsVendor(vendor);
			mutation.setAmMsuser(user);
			mutation.setNotes(notes + " error " + response.getErrorCode());
			mutation.setVendorTrxNo(response.getGuid());
			daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
			
		}
	}
	
	private void forwardSmsJatis(String phoneNumber, String smsBody, MsTenant tenant, AmMsuser user, MsLov lovBalanceType, AuditContext audit) {
		long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		JatisSmsRequestBean request = new JatisSmsRequestBean(tenant, phoneNumber, smsBody, String.valueOf(trxNo), true);
		JatisSmsResponse smsResponse = jatisSmsLogic.sendSmsOnly(request);
		
		if (!"1".equals(smsResponse.getStatus())) {
			return;
		}
		
		MsLov balanceType = null;
		MsLov trxType = null;
		if (null == lovBalanceType) {
			balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
			trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		} else {
			balanceType = lovBalanceType;
			trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, "U" + balanceType.getCode());
		}
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
		String notes = phoneNumber + " : Forward Email to SMS ";
		
		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(request.getTrxNo());
		mutation.setTrxDate(new Date());
		mutation.setQty(-1);
		mutation.setMsLovByLovTrxType(trxType);
		mutation.setMsLovByLovBalanceType(balanceType);
		mutation.setUsrCrt(audit.getCallerId());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(request.getTenant());
		mutation.setMsVendor(vendor);
		mutation.setAmMsuser(user);
		mutation.setVendorTrxNo(smsResponse.getMessageId());
		mutation.setNotes(notes);
		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);
	}
	
	private void forwardWhatsApp(MsMsgTemplate waTemplate, String parsedContent, String phoneNumber, AmMsuser user, MsTenant tenant, MsLov balanceType, AuditContext audit) {
		long trxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();
		
		List<String> bodyTexts = new ArrayList<>();
		bodyTexts.add(parsedContent);
		
		SendWhatsAppRequest request = new SendWhatsAppRequest();
		request.setTemplate(waTemplate);
		request.setBodyTexts(bodyTexts);
		request.setButtonText(parsedContent);
		
		if (null != balanceType && GlobalVal.CODE_LOV_BALANCE_TYPE_OTP.equals(balanceType.getCode())) {
			request.setRemoveHeader(true);
		}
		
		request.setReservedTrxNo(String.valueOf(trxNo));
		request.setPhoneNumber(phoneNumber);
		request.setAmMsuser(user);
		request.setMsTenant(tenant);
		
		whatsAppLogic.sendMessage(request, audit);
	}
	
	private boolean isEmailNeededToForward(ReadEmailBean emailBean) {
		String[] actions = {GlobalVal.EMAIL_PATTERN_GET_DATA};
		MsEmailPattern emailPattern = daoFactory.getEmailDao().getEmailPattern(emailBean.getSubject(), emailBean.getSender(), actions);
		
		if (null == emailPattern) {
			LOG.info("Forwarder checking: Email with subject {} from {} will be forwarded", emailBean.getSubject(), emailBean.getSender());
			return true;
		}
		
		// Handling case Permintaan Tandatangan TekenAja (Jika 1 kontrak 1 dokumen baru forward ke SMS)
		// Perlu perubahan code jika ada case baru untuk cek apakah suatu email perlu diforward ke SMS
		String psreDocumentId = emailLogic.parseEmailContent(emailBean, emailPattern);
		TrDocumentD document = daoFactory.getDocumentDao().getDocumentByPsreDocumentId(psreDocumentId);
		if (null == document) {
			LOG.warn("Forwarder checking: Document with PSRE document ID {} is not found", psreDocumentId);
			sendEmailToEsignPic(emailBean);
			return false;
		}
		
		BigInteger totalDoc = daoFactory.getDocumentDao().countDocumentInAgreement(document.getTrDocumentH().getIdDocumentH());
		if (totalDoc.intValue() > 1) {
			LOG.info("Forwarder checking: Document with PSRE document ID {} has other documents in agreement. Skip sending SMS.", psreDocumentId);
			return false;
		}
		
		LOG.info("Forwarder checking: Document with PSRE document ID {} has no other document in agreement. Sending SMS", psreDocumentId);
		return true;
	}
	
	private void sendEmailToEsignPic(ReadEmailBean emailBean) {
		String picEmails = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_EMAIL_PIC_ESIGN);
		String[] emails = picEmails.split(";");
		if (StringUtils.isBlank(emails[0])) {
			emails = DEFAULT_EMAIL_ESIGN_PIC;
		}
		
		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setTo(emails);
		emailInfo.setFrom(fromEmailAddr);
		emailInfo.setSubject(emailBean.getSubject() + " | " + emailBean.getRecipient());
		emailInfo.setBodyMessage(emailBean.getContent());

		try {
			emailSenderLogic.sendEmail(emailInfo, null);
		} catch (Exception e) {
			LOG.error("Error when sending email to eSignHub PIC", e);
		}
	}

	@Override
	public void deleteEmail(AuditContext audit) {
		Date startTime = new Date();
		long dataProcessed = 0;
		Set<String> uniqueErrorMessages = new HashSet<>();

		List<MsEmailHosting> emailHostingList = daoFactory.getEmailDao().getListActiveEmailHosting();
		String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_MAX_EMAIL_KEEP_ALIVE, audit);
		Integer dayDeleteParam = Integer.parseInt(gsValue);

		DeleteEmailParam param = new DeleteEmailParam();
		param.setDay(dayDeleteParam);
		param.setDeleteType(GlobalVal.DELETE_EMAIL_TYPE_DATE);

		LOG.info("Delete Email Job day param: {} day(s)", param.getDay());

		for (MsEmailHosting msEmailHosting : emailHostingList) {
			String password = msEmailHosting.getDefaultEmailPassword();

			List<AmMsuser> userEmailList = daoFactory.getEmailDao()
					.getAllUserActiveEmailByHostingDomain(msEmailHosting);
			for (AmMsuser user : userEmailList) {
				String email = user.getLoginId();
				try {
					ExpungeEmailResultBean expungeResult = emailLogic.deleteEmail(email, password, param);
					dataProcessed += expungeResult.getTotalData();

					if (StringUtils.isNotBlank(expungeResult.getErrorMessage())) {
						uniqueErrorMessages.add(expungeResult.getErrorMessage());
					}

				} catch (Exception e) {
					LOG.error("Error when deleting email: {}", email);
				}
			}
		}

		String notes = StringUtils.join(uniqueErrorMessages, ",");
		notes = StringUtils.left(notes, 200);
		this.insertTrSchedulerJob(startTime, GlobalVal.CODE_LOV_SCHED_TYPE_DAILY, GlobalVal.CODE_LOV_JOB_TYPE_DEMAIL,
				dataProcessed, notes, audit);
	}

	@Override
	public void digiBalanceSync(AuditContext audit) throws IOException {
		Date startTime = new Date();
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_DIGISIGN);
		List<MsTenant> listTenant = daoFactory.getTenantDao().getListTenantByVendor(vendor);
		BalanceRequest request = new BalanceRequest();
		request.setVendorCode(GlobalVal.VENDOR_CODE_DIGISIGN);

		int totalData = 0;
		for (MsTenant tenant : listTenant) {
			request.setTenantCode(tenant.getTenantCode());

			BalanceResponse response = saldoLogic.getBalanceNotSecure(request, audit);
			BalanceDigiResponseBean digiResponse = digisignLogic.getBalanceDigi(tenant, vendor, audit);

			int docEsign = 0;
			int otpEsign = 0;
			int verifEsign = 0;

			for (BalanceBean bean : response.getListBalance()) {
				if (GlobalVal.CODE_LOV_BALANCE_TYPE_DOC.equals(bean.getCode())) {
					docEsign = bean.getCurrentBalance().intValue();
				} else if (GlobalVal.CODE_LOV_BALANCE_TYPE_OTP.equals(bean.getCode())) {
					otpEsign = bean.getCurrentBalance().intValue();
				} else if (GlobalVal.CODE_LOV_BALANCE_TYPE_VRF.equals(bean.getCode())) {
					verifEsign = bean.getCurrentBalance().intValue();
				}
			}

			if (digiResponse.getResult() != null) {
				for (BalanceDigiBean bean : digiResponse.getBalance()) {
					if (bean.getType().equals(GlobalVal.DIGI_BALANCE_TYPE_DOC)) {
						syncBalanceDoc(bean.getAmount(), docEsign, tenant, vendor, audit);
						totalData++;
					} else if (bean.getType().equals(GlobalVal.DIGI_BALANCE_TYPE_SMS)) {
						syncBalanceOtp(bean.getAmount(), otpEsign, tenant, vendor, audit);
						totalData++;
					} else if (bean.getType().equals(GlobalVal.DIGI_BALANCE_TYPE_VRF)) {
						syncBalanceVrf(bean.getAmount(), verifEsign, tenant, vendor, audit);
						totalData++;
					}
				}
			}
		}
		String notes = "";
		this.insertTrSchedulerJob(startTime, GlobalVal.CODE_LOV_SCHED_TYPE_DAILY, GlobalVal.CODE_LOV_JOB_TYPE_SYNCBAL,
				totalData, notes, audit);

	}

	private void syncBalanceDoc(int balanceDigi, int balanceEsign, MsTenant tenant, MsVendor vendor,
			AuditContext audit) {
		int diff = balanceDigi - balanceEsign;
		if (diff != 0) {
			MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
					GlobalVal.CODE_LOV_BALANCE_TYPE_DOC);
			String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			MsLov trxType;
			if (diff > 0) {
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
						GlobalVal.CODE_LOV_TRX_TYPE_TDOC);
			} else {
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
						GlobalVal.CODE_LOV_TRX_TYPE_UDOC);
			}

			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), null,
					diff, trxNo, null, "Doc Balance Sync With Digisign", null, audit);
		}
	}

	private void syncBalanceOtp(int balanceDigi, int balanceEsign, MsTenant tenant, MsVendor vendor,
			AuditContext audit) {
		int diff = balanceDigi - balanceEsign;
		if (diff != 0) {
			MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
					GlobalVal.CODE_LOV_BALANCE_TYPE_OTP);
			String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			MsLov trxType;
			if (diff > 0) {
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
						GlobalVal.CODE_LOV_TRX_TYPE_TOTP);
			} else {
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
						GlobalVal.CODE_LOV_TRX_TYPE_UOTP);
			}

			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), null,
					diff, trxNo, null, "OTP Balance Sync With Digisign", null, audit);
		}
	}

	private void syncBalanceVrf(int balanceDigi, int balanceEsign, MsTenant tenant, MsVendor vendor,
			AuditContext audit) {
		int diff = balanceDigi - balanceEsign;
		if (diff != 0) {
			MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
					GlobalVal.CODE_LOV_BALANCE_TYPE_VRF);
			String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			MsLov trxType;
			if (diff > 0) {
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
						GlobalVal.CODE_LOV_TRX_TYPE_TVRF);
			} else {
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
						GlobalVal.CODE_LOV_TRX_TYPE_UVRF);
			}

			saldoLogic.insertBalanceMutation(null, null, null, balanceType, trxType, tenant, vendor, new Date(), null,
					diff, trxNo, null, "Verif Balance Sync With Digisign", null, audit);
		}
	}

	@Override
	public void digiCertExpDateSync(AuditContext audit) throws IOException, ParseException {
		Date start = new Date();
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_DIGISIGN);
		List<MsTenant> listTenant = daoFactory.getTenantDao().getListTenantByVendor(vendor);

		Long data = (long) 0;
		for (MsTenant tenant : listTenant) {
			MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			List<AmMsuser> listUser = daoFactory.getUserDao().listUserWithCertExpDateLessThan30Days(tenant, vendor);
			for (AmMsuser user : listUser) {
				MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao()
						.getVendorRegisteredUserByLoginIdAndVendorCode(user.getLoginId(), vendor.getVendorCode());
				CheckDigiCertExpDateResponse response = digisignLogic.checkCertExpDate(vru.getSignerRegisteredEmail(),
						vot, audit);
				if (response.getResult() != null && response.getResult().equals("00")) {
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
					Date digiDate = sdf.parse(response.getExpiredTime());
					if (!digiDate.equals(vru.getCertExpiredDate())) {
						vru.setCertExpiredDate(digiDate);
						daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vru);
					}
				}
				data++;
			}
		}
		String notes = "";
		this.insertTrSchedulerJob(start, GlobalVal.CODE_LOV_SCHED_TYPE_DAILY,
				GlobalVal.CODE_LOV_JOB_TYPE_SYNCCERTEXPDATE, data, notes, audit);
	}

	private void insertTrSchedulerJob(Date startTime, String lovSchedulerTypeCode, String lovJobTypeCode,
			long dataProcessed, String notes, AuditContext audit) {
		MsLov lovSchedulerType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SCHEDULER_TYPE,
				lovSchedulerTypeCode);
		MsLov lovJobType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_JOB_TYPE, lovJobTypeCode);

		TrSchedulerJob schedulerJob = new TrSchedulerJob();
		schedulerJob.setSchedulerStart(startTime);
		schedulerJob.setSchedulerEnd(new Date());
		schedulerJob.setMsLovBySchedulerType(lovSchedulerType);
		schedulerJob.setMsLovByJobType(lovJobType);
		schedulerJob.setDataProcessed(dataProcessed);
		schedulerJob.setNotes(notes);
		schedulerJob.setUsrCrt(audit.getCallerId());
		schedulerJob.setDtmCrt(new Date());
		daoFactory.getSchedulerJobDao().insertSchedulerJob(schedulerJob);
	}

	@Override
	public void syncLocation(AuditContext audit) throws IOException {
		Date startTime = new Date();
		Long data = (long) 0;

		String vendorCode = "TKNAJ";
		
		TekenAjaLocationBean jsonProvince = tekenAjaLogic.getProvinceList(vendorCode);
		
		for(Map.Entry<String, Object> province : jsonProvince.getData().entrySet()) {
			LOG.info("Id: {}, Province name: {}", province.getKey(), province.getValue());
			Long idProvince = Long.parseLong(province.getKey());
			String provinceName = (String) province.getValue();
			AmMsprovince prov = daoFactory.getProvinceDao().getProvinceById(idProvince);


			if (prov == null) {
				prov = new AmMsprovince();
				prov.setProvinceId(idProvince);
				prov.setProvinceName(provinceName);
				prov.setUsrCrt(audit.getCallerId());
				prov.setDtmCrt(new Date());
				daoFactory.getProvinceDao().insertAmMsprovince(prov);
				data++;
			} else if(!prov.getProvinceName().equals(province.getValue())){
				prov.setProvinceName(provinceName);
				prov.setUsrUpd(audit.getCallerId());
				prov.setDtmUpd(new Date());
				daoFactory.getProvinceDao().updateAmMsprovince(prov);
				data++;
			}
			
			
			//district
			TekenAjaLocationBean jsonDistrict = tekenAjaLogic.getDistrictList(province.getKey(), vendorCode);
			for(Map.Entry<String, Object> district : jsonDistrict.getData().entrySet()) {
				LOG.info("Id: {}, District name: {}", district.getKey(), district.getValue());

				Long idDistrict = Long.parseLong(district.getKey());
				String districtName = (String) district.getValue();
				Long idMsprovince = prov.getIdMsprovince();
				AmMsdistrict dist = daoFactory.getDistrictDao().getDistrictById(idDistrict, idMsprovince);

				if(dist == null) {
					dist = new AmMsdistrict();
					dist.setDistrictId(idDistrict);
					dist.setDistrictName(districtName);
					dist.setAmMsprovince(prov);
					dist.setUsrCrt(audit.getCallerId());
					dist.setDtmCrt(new Date());
					daoFactory.getDistrictDao().insertAmMsdistrict(dist);
					data++;
				}else {
					dist.setDistrictName(districtName);
					dist.setDtmUpd(new Date());
					dist.setUsrUpd(audit.getCallerId());
					daoFactory.getDistrictDao().updateAmMsdistrict(dist);
					data++;
				}
				//subdistrict
				TekenAjaLocationBean jsonSubdistrict = tekenAjaLogic.getSubDistrictList(province.getKey(), district.getKey(), vendorCode);
				for(Map.Entry<String, Object> subdistrict : jsonSubdistrict.getData().entrySet()) {
					LOG.info("Id: {}, Subdistrict name: {}", subdistrict.getKey(), subdistrict.getValue());

					Long idSubdistrict = Long.parseLong(subdistrict.getKey());
					String subdistrictName = (String) subdistrict.getValue();
					Long idMsdistrict = dist.getIdMsdistrict();
					AmMssubdistrict subdist = daoFactory.getSubDistrictDao().getSubdistrictById(idSubdistrict, idMsdistrict);

					if(subdist == null) {
						subdist = new AmMssubdistrict();
						subdist.setSubdistrictId(idSubdistrict);
						subdist.setSubdistrictName(subdistrictName);
						subdist.setAmMsdistrict(dist);
						subdist.setUsrCrt(audit.getCallerId());
						subdist.setDtmCrt(new Date());
						daoFactory.getSubDistrictDao().insertAmMssubdistrict(subdist);
						data++;
					}else {
						subdist.setSubdistrictName(subdistrictName);
						subdist.setDtmUpd(new Date());
						subdist.setUsrUpd(audit.getCallerId());
						daoFactory.getSubDistrictDao().updateAmMssubdistrict(subdist);
						data++;
					}
				}
			}
		}

		String notes = "";
		this.insertTrSchedulerJob(startTime, GlobalVal.CODE_LOV_SCHED_TYPE_DAILY, GlobalVal.CODE_LOV_JOB_TYPE_SYNCREGTEKEN,
				data, notes, audit);
	}

	@Override
	public void errorHistoryRerunSendDoc(AuditContext audit) {
		Date startTime = new Date();
		List<TrErrorHistory> errorHistories = daoFactory.getErrorHistoryDao()
				.getErrorHistoryListByRerunProcess(GenericErrorReportLogic.ERR_HIST_RERUN_ERROR);
		
		LOG.info("About to rerun {} error history ref number(s)", errorHistories.size());
		
		for (TrErrorHistory errorHistory : errorHistories) {
			String rerunSendDocUrl = errorHistory.getMsTenant().getRerunUrl();
			Status status = documentLogic.callUrlRerunSendDocument(rerunSendDocUrl, errorHistory.getRefNumber());
			if ((200 == status.getCode() && "Success Resume Workflow".equalsIgnoreCase(status.getMessage()))
					|| (500 == status.getCode() && "Workflow Task not found".equalsIgnoreCase(status.getMessage()))
					|| (501 == status.getCode() && "Agreement not found".equalsIgnoreCase(status.getMessage()))) {
				
				errorHistory.setRerunProcess(GenericErrorReportLogic.ERR_HIST_RERUN_FINISHED);
				errorHistory.setUsrUpd(audit.getCallerId());
				errorHistory.setDtmUpd(new Date());
				daoFactory.getErrorHistoryDao().updateErrorHistory(errorHistory);
			}
			
		}
		
		if (!errorHistories.isEmpty()) {
			String notes = "Error History: Rerun send document";			
			insertTrSchedulerJob(startTime, GlobalVal.CODE_LOV_SCHED_TYPE_CONTINOUS, GlobalVal.CODE_LOV_JOB_TYPE_ERRHISTRERUN, errorHistories.size(), notes, audit);
		}	
	}
	
	@Override
	public void documentType(AuditContext audit) throws IOException {
		Date startTime = new Date();
		Long data = (long) 0;
		PajakkuDocumentTypeBean jsonDocType = emateraiPajakkuLogic.getDocumentType();
		
		List<String> allperuriDocId = new ArrayList<>();

		for(PajakkuDocumentTypeResultBean documentTypeResult : jsonDocType.getResult()) {
			LOG.info("Id: {}, kode: {}, name: {}", documentTypeResult.getId(), documentTypeResult.getKode(), documentTypeResult.getNama());
			String peruriDocId = documentTypeResult.getId();
			MsPeruriDocType docType = daoFactory.getCommonDao().getMsPeruriDocTypeByDocId(peruriDocId);
			
			allperuriDocId.add(peruriDocId);
			
			if (docType == null) {
				docType = new MsPeruriDocType();
				docType.setPeruriDocId(documentTypeResult.getId());
				docType.setDocCode(documentTypeResult.getKode());
				docType.setDocName(documentTypeResult.getNama());
				docType.setIsActive("1");
				docType.setUsrCrt(audit.getCallerId());
				docType.setDtmCrt(new Date());
				daoFactory.getCommonDao().insertMsPeruriDocType(docType);
				data++;
			} else if( (!docType.getDocName().equals(documentTypeResult.getNama())) || (!docType.getDocCode().equals(documentTypeResult.getKode())) || (!docType.getIsActive().equals("1"))){
				docType.setDocCode(documentTypeResult.getKode());
				docType.setDocName(documentTypeResult.getNama());
				docType.setIsActive("1");
				docType.setUsrUpd(audit.getCallerId());
				docType.setDtmUpd(new Date());
				daoFactory.getCommonDao().updateMsPeruriDocType(docType);
				data++;
			}
		}
		
		daoFactory.getCommonDao().deactiveDocumentNotInDocId(allperuriDocId, audit.getCallerId());
		
		String notes = "";
		this.insertTrSchedulerJob(startTime, GlobalVal.CODE_LOV_SCHED_TYPE_DAILY, GlobalVal.CODE_LOV_JOB_TYPE_SYNCREGTEKEN,
				data, notes, audit);
	}

	@Override
	public void deleteUnusedComplementaryStampingDocumentJob(AuditContext audit) throws IOException {
		
	    String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATA_FOREACH_DELETE_UNUSED_DOC);
	    Integer max = Integer.parseInt(gsValue);
	    
		List<DocHBean> listTrDocumentH = daoFactory.getDocumentDao().getListDocumentBasedByCallbackValue(max);
		
		for(DocHBean dhb : listTrDocumentH) {
			TrDocumentH dh = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCodeNewTran(dhb.getRefNumber(), dhb.getTenantCode());
			List<TrDocumentD> listTrDocumentD = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(dh.getIdDocumentH());

			if(dh.getCallbackProcess().equals((short)901)) {
					for(TrDocumentD dd : listTrDocumentD) {
						fileAccessLogic.deleteBaseStampDocument(dd);
					}
				dh.setCallbackProcess((short)902);
				dh.setDtmUpd(new Date());
				dh.setUsrUpd(audit.getCallerId());
			}
			
			if(dh.getCallbackProcess().equals((short)902)) {
					for(TrDocumentD dd : listTrDocumentD) {
						fileAccessLogic.deleteStampedDocumentbckp(dd);
					}
				dh.setCallbackProcess((short)903);
				dh.setDtmUpd(new Date());
				dh.setUsrUpd(audit.getCallerId());
			}
			
			if(dh.getCallbackProcess().equals((short)903)) {
				List<StampDutyBean> listStampDuty = daoFactory.getStampDutyDao().getListStampDutyByIdDocumentHNewTran(dh.getIdDocumentH());
					for(StampDutyBean sd : listStampDuty) {
						fileAccessLogic.deleteStampQr(sd.getStampDutyNo());
					}
					dh.setCallbackProcess((short)904);
					dh.setDtmUpd(new Date());
					dh.setUsrUpd(audit.getCallerId());	
			}
			daoFactory.getDocumentDao().updateDocumentHNewTran(dh);
		}
	}
	
	private void uploadDocumentToDms(TrDocumentH documentH, AuditContext audit) {
		Status status = new Status();
		try {
			UploadToCoreBean uploadBean = documentLogic.prepareUploadToCoreData(documentH, audit);
			if (uploadBean != null) {
				status = documentLogic.callUrlUpload(documentH.getUrlUpload(), uploadBean);
			}
			
			if (200 == status.getCode() || (500 == status.getCode() && status.getMessage().contains("Error in the application."))) {
				documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_SUCCESS_UPLOAD_CONFINS, audit);
			} else {
				LOG.error("Error di upload confins code={}, message={}", status.getCode(), status.getMessage());
				documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_FAIL_UPLOAD_CONFINS, audit);
			}
		} catch (Exception e) {
			LOG.error("Error di upload DMS: {}", e.getLocalizedMessage(), e);
			documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_FAIL_UPLOAD_CONFINS, audit);
		}
	}
	
	private void callResultUrl(TrDocumentH documentH, AuditContext audit) {
		if (!Tool.compareShort(documentH.getProsesMaterai(), (short) 0)) {
			LOG.info("Kontrak {} sedang / sudah diproses untuk stamping. Lewati resume workflow.", documentH.getRefNumber());
		
			documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_SUCCESS_UPLOAD_CONFINS, audit); //native
			return;
		}
		
		if (StringUtils.isBlank(documentH.getResultUrl())) {
			documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_FAIL_UPLOAD_CONFINS, audit); // native
			return;
		}
		
		ResumeWorkflowReturnBean returned = callUrlSuccess(documentH, audit);
		Status status = returned.getStatus();
		if (200 == status.getCode()) {
			documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_FAIL_UPLOAD_CONFINS, audit); // native
				
			if (StringUtils.isBlank(documentH.getUrlUpload())) {
				documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_SUCCESS_UPLOAD_CONFINS, audit); // native
			}		
		} else {
			LOG.error("Error di result url code={}, message={}", status.getCode(), status.getMessage());
			processErrorDocument(documentH, returned, audit);
		}
	}
	
	private void processErrorDocument(TrDocumentH documentH, ResumeWorkflowReturnBean returned, AuditContext audit) {
		
		int maxAttempt = tenantSettingsLogic.getSettingValue(documentH.getMsTenant(), "MAX_RETRY_RESUME_WF_ATTEMPT", 0);
		if (0 == maxAttempt) {
			documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_FAIL_UPLOAD_CONFINS, audit); // native
		} else {
			Short currAttempt = documentH.getRetryResumeAttemptNum();
			
			if (null == currAttempt) {
				documentLogic.updateCallbackProcessAndRetryResumeAttemptNumNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_FAIL_CALL_RESULT_URL, (short) 1, audit); //nativewithtretry 
			} else {
				short retryResumeAttempNum = (short) (currAttempt +  1);
				if (currAttempt + 1 < maxAttempt) {
					documentLogic.updateCallbackProcessAndRetryResumeAttemptNumNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_FAIL_CALL_RESULT_URL, retryResumeAttempNum, audit); //nativewithtretry
				} else {
					documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_FAIL_UPLOAD_CONFINS, audit); //native
					sendEmailMaxRetryResume(documentH, returned, audit);
				}
			}
		}
	}
	
	private void sendEmailMaxRetryResume(TrDocumentH docH, ResumeWorkflowReturnBean returned, AuditContext audit) {
		Map<String, Object> templateParameters = new HashMap<>();
		Map<String, Object> kontrakMap = new HashMap<>();
		kontrakMap.put("refNumber", docH.getRefNumber());
		kontrakMap.put("tenantName", docH.getMsTenant().getTenantName());
		kontrakMap.put("retryResumeWorkFlowAttemptNum", docH.getRetryResumeAttemptNum());
		Map<String, Object> errorMap = new HashMap<>();
		errorMap.put("response", gson.toJson(returned.getStatus()));
		errorMap.put("stackTrace", StringUtils.isBlank(returned.getStackTrace()) ? "Tidak Ada" : returned.getStackTrace());
		
		templateParameters.put("kontrak", kontrakMap);
		templateParameters.put("error", errorMap);

		String gsVal = daoFactory.getGeneralSettingDao().getGsValueByCode("EMAIL_PIC_ESIGN");
		String[] email = gsVal.split(";");
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_RETRY_RESUME_WORKFLOW_FAIL, templateParameters);
		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setFrom(fromEmailAddr);
		emailInfo.setTo(email);
		emailInfo.setSubject(template.getSubject());
		emailInfo.setBodyMessage(template.getBody());
		try {
			emailSenderLogic.sendEmail(emailInfo, null);
			LOG.info("Retry Resume workflow maxed email Sent.");
		} catch (MessagingException e) {
			throw new EmailException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMAIL_SENDER, null,
					this.retrieveLocaleAudit(audit)), ReasonEmail.SEND_EMAIL_ERROR);
		}
	}
	
	private ResumeWorkflowReturnBean callUrlSuccess(TrDocumentH documentH, AuditContext audit) {
		ResumeWorkflowReturnBean toReturn = new ResumeWorkflowReturnBean();
		Status status = new Status();

		try {
			WebClient client = WebClient.create(documentH.getResultUrl());
			MssTool.trustAllSslCertificate(client);
			LOG.info("Calling resume workflow CONFINS with URL: {}", documentH.getResultUrl());
			Response response = client.post(null);
//			MssTool.setWebClientConnReadTimeout(client, 1, 1);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

			String result = IOUtils.toString(isReader);
			result = result.replace("\\", "");
			result = result.substring(1, result.length() - 1);

			LOG.info("Calling resume workflow {} response: {}", documentH.getRefNumber(), result);
			status = gson.fromJson(result, Status.class);
		} catch (Exception e) {
			toReturn.setStackTrace(Arrays.toString(e.getStackTrace()));
			LOG.error("Exception occured when calling resume workflow for ref number: {}", documentH.getRefNumber(), e);
			documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_FAIL_CALL_RESULT_URL, audit); // native
		}
		
		toReturn.setStatus(status);
		return toReturn;
	}
	
	private void callAllResultUrl(AuditContext audit) {
		List<TrDocumentH> listDocumentHFailResultUrl = daoFactory.getDocumentDao().getListDocumentHeaderByCallbackProcessNewTrx(GlobalVal.CALLBACK_PROCESS_FAIL_CALL_RESULT_URL);
		for (TrDocumentH documentH : listDocumentHFailResultUrl) {
			callResultUrl(documentH, audit);
		}
	}
	
	/**
	 * Currently only support 1 tr_document_h with only 1 tr_document_d<br>
	 * If there is 1 tr_document_h with > 1 tr_document_d, will only upload the first document
	 */
	private void uploadDocumentWithStandardRequest(TrDocumentH documentH, AuditContext audit) {
		
		List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
		TrDocumentD document = documents.get(0);
		String url = documentH.getUrlUpload();
		
		try {
			OkHttpClient okHClient = MssTool.getUnsafeOkHttpClient();
			String token = daoFactory.getTenantSettingsDao().getTenantSettings(document.getMsTenant(), GlobalVal.CODE_LOV_TENANT_SETTING_TOKEN_CLIENT_URL_UPLOAD).getSettingValue();
			commonValidatorLogic.validateNotNull(token, getMessage("businesslogic.tenantsettings.tenantsettingsnotfoundfortenant", new Object[] {GlobalVal.CODE_LOV_TENANT_SETTING_TOKEN_CLIENT_URL_UPLOAD, document.getMsTenant().getTenantName()}, audit), StatusCode.TENANT_SETTINGS_NOT_FOUND);
			
			// Prepare header
			Map<String, String> header = new HashMap<>();
			header.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
			header.put(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
			header.put("token", token);
			Headers headers = Headers.of(header);
			
			LOG.info("Kontrak {}, Dokumen {}, Upload document to client request header: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), header);
			LOG.info("Kontrak {}, Dokumen {}, Upload document to client with URL: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), url);
						
			// Prepare json request body
			AuditDataType auditDataType = new AuditDataType();
			auditDataType.setCallerId(audit.getCallerId());
			
			ViewDocumentRequest viewRequest = new ViewDocumentRequest();
			viewRequest.setDocumentId(document.getDocumentId());
			ViewDocumentResponse viewResponse = documentLogic.viewDocumentWithoutSecurity(viewRequest, audit);
			
			String base64Document = viewResponse.getPdfBase64();
			DummyClientURLUploadRequest request = new DummyClientURLUploadRequest();
			request.setDocFile(base64Document);
			request.setDocNumber(documentH.getRefNumber());
			request.setTenantCode(document.getMsTenant().getTenantCode());
			request.setAudit(auditDataType);
			request.setDocId(document.getDocumentId());
			String jsonBody = gson.toJson(request);
			
			// Logging json request
			DummyClientURLUploadRequest loggingRequest = DummyClientURLUploadRequest.newInstance(request);
			loggingRequest.setDocFile("base64doc");
			String logJson = gson.toJson(loggingRequest);
			LOG.info("Kontrak {}, Dokumen {}, Upload document to client request body: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), logJson);
			
			RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_JSON), jsonBody);
			
			Request okHRequest = new Request.Builder()
					.headers(headers)
					.url(url)
					.post(body).build();
			
			okhttp3.Response okHResponse = okHClient.newCall(okHRequest).execute();
			String result = okHResponse.body().string();
			LOG.info("Kontrak {}, Dokumen {}, Upload document to client response: {}", document.getTrDocumentH().getRefNumber(), document.getDocumentId(), result);
			
			MssResponseType uploadResponse = gson.fromJson(result, MssResponseType.class);
			
			if (null != uploadResponse.getStatus() && 0 == uploadResponse.getStatus().getCode()) {
				documentLogic.updateCallbackProcessNewTranNative(documentH, ((short) 4), audit); // native
			}
			
		} catch (Exception e) {
			LOG.error("Kontrak {}, Dokumen {}, Upload document to client exception: {}", documentH.getRefNumber(), document.getDocumentId(), e.getLocalizedMessage(), e);
		}
		
		
	}
	
	private void uploadAllDocumentToDms(AuditContext audit) {
		List<TrDocumentH> listDocumentHFailUploadConfins = daoFactory.getDocumentDao().getListDocumentHeaderByCallbackProcessNewTrx(GlobalVal.CALLBACK_PROCESS_FAIL_UPLOAD_CONFINS);
		for (TrDocumentH documentH : listDocumentHFailUploadConfins) {
			if (!Tool.compareShort(documentH.getProsesMaterai(), (short) 0)) {
				
				LOG.info("Kontrak {} sedang / sudah diproses untuk stamping. Lewati upload DMS.", documentH.getRefNumber());
				documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_SUCCESS_UPLOAD_CONFINS, audit); // native
				continue;
			}
			
			if (StringUtils.isNotBlank(documentH.getUrlUpload())) {
				if ("1".equals(documentH.getIsStandardUploadUrl())) {
					uploadDocumentWithStandardRequest(documentH, audit);
				} else {
					uploadDocumentToDms(documentH, audit);
				}
			} else {
				documentLogic.updateCallbackProcessNewTranNative(documentH, GlobalVal.CALLBACK_PROCESS_SUCCESS_UPLOAD_CONFINS, audit); // native
			}
			
		}
	}
	
	@Override
	public void resumeWorkflow(AuditContext audit) {
		callAllResultUrl(audit);
	}

	@Override
	public void deleteOnPremDocResultJob(AuditContext audit) {
		List<AmGeneralsetting> maxDateGss = daoFactory.getGeneralSettingDao().getListGsObjByCode(AmGlobalKey.GENERALSETTING_DELETE_DOC_ON_PREM_DATETOKEEP);
		Date now = new Date();
		Date processStartTime = now;
		int docAmount = 0;
		
		for (AmGeneralsetting maxDateGs : maxDateGss) {
			AmGeneralsetting limitGs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_DELETE_DOC_ON_PREM_LIMIT);
			
			Date maxDate = DateUtils.addDays(now, -(Integer.parseInt(maxDateGs.getGsValue())));
			
			int toDelete = daoFactory.getDocumentDao().countDocToDeleteOnPremResult(maxDate);
			docAmount += toDelete;
			int iter = (int) Math.ceil((double) docAmount / Integer.parseInt(limitGs.getGsValue()));
			LOG.info("Total stamped docs to delete : {}", docAmount);
			LOG.info("Total Delete Stamped Doc iteration : {}", iter);
			
			if (0 == toDelete) {
				LOG.info("No Stamped Doc to delete");
				continue;
	        }
			
			for (int i = 0; i < iter; i++) {
				List<DeleteOnPremResultBean> toDeletes = daoFactory.getDocumentDao().getDocToDeleteOnPremResult(maxDate, limitGs.getGsValue());
				Long dataProcessed = (long) toDeletes.size();
				
				LOG.info("Delete stamped doc iteration : {}", (i + 1));
				LOG.info("Total Stamped contract to delete in iteration : {}", dataProcessed);
				for (DeleteOnPremResultBean bean : toDeletes) {
					TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCodeNewTran(bean.getRefNum(), bean.getTenantCode());
					List<TrDocumentD> docs = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(docH.getIdDocumentH());
					LOG.info("Deleting stamped doc of Ref Number {}", docH.getRefNumber());
					for (TrDocumentD doc : docs) {
						fileAccessLogic.deleteStampedDocument(doc);
					}
					
					documentLogic.updateCallbackProcessNewTranNative(docH, (short) 905, audit); // native
				}
			}
		}
		
		Date dateEnd = new Date();
		Short zero = 0;
		MsLov lovSchedulerType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_SCHEDULER_TYPE, GlobalVal.CODE_LOV_SCHED_TYPE_DAILY);
		MsLov lovJobType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_JOB_TYPE, GlobalVal.CODE_LOV_JOB_DEL_RESULTSTAMP_ONPREM);
		
		TrSchedulerJob insertJob = new TrSchedulerJob();
		insertJob.setDtmCrt(new Date());
		insertJob.setUsrCrt("DELETE STAMPED DOCUMENT ON PREM");
		insertJob.setDataProcessed((long) docAmount);
		insertJob.setSchedulerStart(processStartTime);
		insertJob.setSchedulerEnd(dateEnd);
		insertJob.setMsLovBySchedulerType(lovSchedulerType);
		insertJob.setMsLovByJobType(lovJobType);
		insertJob.setMailReminderCount(zero);
		
		daoFactory.getSchedulerJobDao().insertSchedulerJobNewTrx(insertJob);
	}

	@Override
	public void uploadDmsWF(AuditContext audit) {
		uploadAllDocumentToDms(audit);
	}
	
	private void doSignVida(AuditContext audit) {
		
		List<TrDocumentSigningRequest> documentSigningRequests = daoFactory.getDocumentSigningRequestDao().getDocumentSigningRequestsByRequestStatusVendorNewTran((short) 0, "VIDA");
		
		for (TrDocumentSigningRequest documentSignRequest : documentSigningRequests) {
			
			TrDocumentD document = documentSignRequest.getTrDocumentD();
			TrDocumentH documentH = documentSignRequest.getTrDocumentH();
			AmMsuser signer = documentSignRequest.getAmMsuser();
			
			if ("1".equals(document.getSigningProcess())) {
				LOG.info("Sign VIDA Job: Skip invoking FC to sign document {} with signer {} (document is being procesed)", document.getDocumentId(), signer.getLoginId());
				continue;
			}
			
			document.setSigningProcess("1");
			document.setUsrUpd(audit.getCallerId());
			document.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
			
			documentH.setSigningProcess("1");
			documentH.setUsrUpd(audit.getCallerId());
			documentH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
			
			documentSignRequest.setRequestStatus((short) 1);
			documentSignRequest.setRequestStart(new Date());
			documentSignRequest.setDtmUpd(new Date());
			documentSignRequest.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentSigningRequestDao().updateDocumentSigningRequestNewTran(documentSignRequest);
			
			LOG.info("Sign VIDA Job: Invoking FC to sign for document {} with signer {}", document.getDocumentId(), signer.getLoginId());
			functionComputeLogic.invokeSignVidaDocument(documentSignRequest.getIdDocumentSigningRequest());
			
		}
	}
	
	@Override
	public void signVida(AuditContext audit) {
		try {
			doSignVida(audit);
		} catch (Exception e) {
			LOG.error("Sign Vida Job error: {}", e.getLocalizedMessage(), e);
		}
	}
	
	private void sendSignCompleteEmail(TrDocumentH documentH) {
		MsTenant tenant = documentH.getMsTenant();
		
		MsTenantSettings tenantSettings = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "ALL_DOCUMENTS_DONE_SIGNING_NOTIFICATION_RECEIVER");
		if (null == tenantSettings || StringUtils.isBlank(tenantSettings.getSettingValue())) {
			return;
		}
		
		Map<String, Object> docParams = new HashMap<>();
		docParams.put("refNumberLabel", tenant.getRefNumberLabel());
		docParams.put("refNumber", documentH.getRefNumber());
		
		Map<String, Object> templateParams = new HashMap<>();
		templateParams.put("doc", docParams);
		
		int stampDutyNeeded = daoFactory.getDocumentDao().countStampDutyNeeded(documentH);
		String templateCode = null;
		if (stampDutyNeeded > 0) {
			templateCode = GlobalVal.TEMPLATE_DOCH_CAN_STAMP;
		} else {
			templateCode = GlobalVal.TEMPLATE_DOCH_SIGN_COMPLETE;
		}
		
		MsMsgTemplate template = messageTemplateLogic.getAndParseContent(templateCode, templateParams);
		String[] recipients = StringUtils.split(tenantSettings.getSettingValue(), ",");
		
		EmailInformationBean emailInfo = new EmailInformationBean();
		emailInfo.setFrom(fromEmailAddr);
		emailInfo.setTo(recipients);
		emailInfo.setBodyMessage(template.getBody());
		emailInfo.setSubject(template.getSubject());
		
		try {
			emailSenderLogic.sendEmail(emailInfo, null);
		} catch (Exception e) {
			LOG.error("Failed to send sign complete notification email: {}", e.getLocalizedMessage(), e);
		}
		
	}

	@Override
	public void processAutomaticStamp(AuditContext audit) {
		List<TrDocumentH> documentHs = daoFactory.getDocumentDao().getListDocumentHeaderByCallbackProcessAndIsManualUploadNewTrx((short) 4, "1");
		LOG.info("Processing {} document header(s) for automatic stamp", documentHs.size());

		for (TrDocumentH documentH : documentHs) {
			
			if ("1".equals(documentH.getAutomaticStampingAfterSign())) {
				MsLov lovStamping = documentH.getMsTenant().getLovVendorStamping();
				if (lovStamping != null && GlobalVal.VENDOR_CODE_PRIVY_ID.equals(lovStamping.getCode())) {
					documentH.setProsesMaterai(new Short(GlobalVal.ON_PREM_STAMP_IN_QUEUE_PRIVY));
				} else if (lovStamping != null && GlobalVal.VENDOR_CODE_VIDA.contentEquals(lovStamping.getCode())) {
					documentH.setProsesMaterai(new Short(GlobalVal.VIDA_STAMP_IN_QUEUE));
				} else {
					documentH.setProsesMaterai(new Short(GlobalVal.ON_PREM_STAMP_IN_QUEUE));
				}
				LOG.info("Kontrak {} {}, flagged for stamping", documentH.getMsTenant().getTenantCode(), documentH.getRefNumber());
			} else {
				sendSignCompleteEmail(documentH);
			}

			documentLogic.updateCallbackProcessNewTran(documentH, (short) 6, audit);
		}
	}

	@Override
	public void signPrivy(AuditContext audit) {
		List<TrDocumentSigningRequest> documentSigningRequests = daoFactory.getDocumentSigningRequestDao().getDocumentSigningRequestsByRequestStatusVendorNewTran((short) 0, "PRIVY");
		
		for (TrDocumentSigningRequest documentSignRequest : documentSigningRequests) {
			
			TrDocumentD document = documentSignRequest.getTrDocumentD();
			TrDocumentH documentH = documentSignRequest.getTrDocumentH();
			AmMsuser signer = documentSignRequest.getAmMsuser();
			
			if ((null != document && "1".equals(document.getSigningProcess())) || "1".equals(documentH.getSigningProcess())) {
				String documentName = null != document ? document.getDocumentId() : documentH.getRefNumber();
				LOG.info("Sign PRIVY Job: Skip invoking FC to sign document {} with signer {} (document is being procesed)", documentName, signer.getLoginId());
				continue;
			}
			
			if (null != document) {
				document.setSigningProcess("1");
				document.setUsrUpd(audit.getCallerId());
				document.setDtmUpd(new Date());
				daoFactory.getDocumentDao().updateDocumentDetailNewTran(document);
			} else {
				List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderIdNewTran(documentH.getIdDocumentH());
				for (TrDocumentD documentD : documents) {
					documentD.setSigningProcess("1");
					documentD.setUsrUpd(audit.getCallerId());
					documentD.setDtmUpd(new Date());
					daoFactory.getDocumentDao().updateDocumentDetailNewTran(documentD);
				}
			}
			
			documentH.setSigningProcess("1");
			documentH.setUsrUpd(audit.getCallerId());
			documentH.setDtmUpd(new Date());
			daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
			
			documentSignRequest.setRequestStatus((short) 1);
			documentSignRequest.setRequestStart(new Date());
			documentSignRequest.setDtmUpd(new Date());
			documentSignRequest.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentSigningRequestDao().updateDocumentSigningRequestNewTran(documentSignRequest);
			
			String documentName = null != document ? document.getDocumentId() : documentH.getRefNumber();
			LOG.info("Sign PRIVY Job: Invoking FC to sign for document {} with signer {}", documentName, signer.getLoginId());
			functionComputeLogic.invokeSignPrivyDocument(documentSignRequest.getIdDocumentSigningRequest());
			
		}
	}

	@Override
	public void registerPrivy(AuditContext audit) {
		List<TrJobCheckRegisterStatus> requests = daoFactory.getJobCheckRegisterStatusDao().getUnprocessedJobCheckRegStatusNewTrx();
		LOG.info("Check Register Status: Request to process : {}", requests.size());
		
		for (TrJobCheckRegisterStatus request : requests) {
			TrBalanceMutation bm = request.getTrBalanceMutation();
			MsVendor vendor = daoFactory.getVendorDao().getVendorByIdMsVendor(bm.getMsVendor().getIdMsVendor());
			
			if (!GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {
				request.setRequestStatus((short) 2);
				request.setDtmUpd(new Date());
				request.setUsrCrt(audit.getCallerId());
				daoFactory.getJobCheckRegisterStatusDao().updateJobCheckRegStatusNewTrx(request);
				continue;
			}
			
			request.setRequestStatus((short) 1);
			request.setDtmUpd(new Date());
			request.setUsrCrt(audit.getCallerId());
			daoFactory.getJobCheckRegisterStatusDao().updateJobCheckRegStatusNewTrx(request);
			
			LOG.info("Check Register Status: Invoking FC to check register status for Request ID {}", request.getIdJobCheckRegisterStatus());
			functionComputeLogic.invokeCheckRegisterStatus(request.getIdJobCheckRegisterStatus());
		}
	}

	@Override
	public void attachMeteraiPrivy(AuditContext audit) {
		Short process = 62;
		List<TrDocumentH> listDocumentH = daoFactory.getDocumentDao().getListDocumentHeaderByProsesMeteraiNewTran(process);
		
		for(TrDocumentH docH : listDocumentH) {
			docH.setProsesMaterai((short) 64);
			docH.setDtmUpd(new Date());
			docH.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentDao().updateDocumentHNewTran(docH);
			
			LOG.info("Attach Meterai Privy: Invoking FC to attach meterai with Privy for Reference Number {}", docH.getRefNumber());
			functionComputeLogic.invokeAttachMeteraiPrivy(docH.getIdDocumentH());
		}
	}

	@Override
	public void attachMeteraiVida(AuditContext audit) {
		Short process = 72;
		List<TrDocumentH> documentHs = daoFactory.getDocumentDao().getListDocumentHeaderByProsesMeteraiNewTran(process);
		
		for (TrDocumentH documentH : documentHs) {
			documentH.setProsesMaterai((short) 74);
			documentH.setDtmUpd(new Date());
			documentH.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
			
			LOG.info("Attach meterai VIDA job: Invoking FC to attach meterai VIDA for tenant: {}, ref number: {}", documentH.getMsTenant().getTenantCode(), documentH.getRefNumber());
			functionComputeLogic.invokeAttachMeteraiVida(documentH.getIdDocumentH());
		}
		
	}

	@Override
	public void attachMeteraiPajakku(AuditContext audit) {
		Short process = 52;
		List<TrDocumentH> documentHs = daoFactory.getDocumentDao().getListDocumentHeaderByProsesMeteraiNewTran(process);
		
		for (TrDocumentH documentH : documentHs) {
			documentH.setProsesMaterai((short) 54);
			documentH.setDtmUpd(new Date());
			documentH.setUsrUpd(audit.getCallerId());
			daoFactory.getDocumentDao().updateDocumentHNewTran(documentH);
			
			LOG.info("Attach meterai Pajakku job: Invoking FC to attach meterai Pajakku for tenant: {}, ref number: {}", documentH.getMsTenant().getTenantCode(), documentH.getRefNumber());
			functionComputeLogic.invokeAttachMeteraiPajakku(documentH.getIdDocumentH());
		}
	}
	
}
