package com.adins.esign.businesslogic.impl;

import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.ImportResource;
import org.springframework.stereotype.Component;
import org.springframework.test.annotation.Commit;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.TestSetUpLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.dataaccess.api.UserDao;
import com.adins.esign.model.custom.GenerateInvLinkRequest;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.webservices.model.ActivationLinkRequest;
import com.adins.esign.webservices.model.RegistrationRequest;
import com.adins.esign.webservices.model.UserAutoFillDataRequest;
import com.adins.esign.webservices.model.UserAutoFillDataResponse;
import com.adins.exceptions.InvitationLinkException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.InvitationLinkException.ReasonInvitationLink;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.framework.persistence.dao.model.AuditContext;
import okhttp3.mockwebserver.MockWebServer;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
@TestPropertySource(locations = "classpath:application.properties")
@Component
class GenericUserLogicTest extends BaseLogic {
    @Value("${digisign.preregister.uri}") private String urlPreregist;
    @Value("${digisign.activation.uri}") private String urlActivation;
    @Value("${esign.regex.phone}") private String regexPhone;
    @Value("${esign.regex.email}") private String regexEmail;
    
	@Autowired private TestSetUpLogic testSetUpLogic;
	@Autowired private UserLogic userLogic;
	@Autowired private UserDao userDao;
	@Autowired private CommonLogic commonLogic;

	private AuditContext auditContext = new AuditContext();
	private AmMsuser user;
//	private MsBranch branch;
	
	private UserBean userBean;
	private RegistrationRequest regisReq = new RegistrationRequest();
	private ActivationLinkRequest actLinkReq = new ActivationLinkRequest();
	
	private static String LOGIN_ID = "<EMAIL>";
	private MockWebServer server;
	
	@BeforeEach
	public void setUp() {
		auditContext.setCallerId(LOGIN_ID);
//		job = daoFactory.getJobDao().getJobByCode(GlobalVal.JOB_ADMIN);
//		branch = daoFactory.getBranchDao().getActiveBranchByCode(GlobalVal.BRANCH_CODE_HO);
//		user = testSetUpLogic.setUpUser("<EMAIL>", "JUNIT", job, branch, null, subsystem);
//		userBean = testSetUpLogic.setUpUserBean(user);
//		regisReq.setUserData(userBean);
//		actLinkReq.setEmail(user.getEmail());
//		server = new MockWebServer();
	}

	@Test
	void insertNewUserPersonalDataTest() {
		user = userLogic.getUserByLoginId(LOGIN_ID);
		SignerBean sb = new SignerBean();
		sb.setProvinsi("JAWA TIMUR");
		sb.setKota("MALANG");
		sb.setKecamatan("KLOJEN");
		sb.setKelurahan("KASIN");
		sb.setZipcode("65118");
		sb.setUserAddress("JALAN KEMANGGISAN NO 12");
		sb.setUserGender("M");
		sb.setUserPob("BLITAR");
		sb.setUserDob("1989-05-21");
		sb.setEmail("<EMAIL>");
		sb.setIdPhoto("base64FotoJunit");
		
		sb.setIdNo("357302020599007");
		sb.setUserPhone("081234567890");
		userLogic.insertNewUserPersonalData(user, sb, auditContext);
	}
	
	@Test
	void updateUserPersonalDataTest() {
		user = userLogic.getUserByLoginId(LOGIN_ID);
		UserBean userBean = new UserBean();
		userBean.setEmail(LOGIN_ID);
		userBean.setProvinsi("JAWA TIMUR UPD");
		userBean.setKota("MALANG");
		userBean.setKecamatan("KLOJEN");
		userBean.setKelurahan("KASIN");
		userBean.setZipcode("65118");
		userBean.setUserAddress("JALAN KEMANGGISAN NO 12");
		userBean.setUserGender("M");
		userBean.setUserPob("BLITAR");
		userBean.setUserDob("1989-05-21");
		userBean.setIdPhoto("base64FotoJunitUPD");
		
		userBean.setIdNo("357302020599007UPD");
		userBean.setUserPhone("081234567890UPD");
		userLogic.updateUserPersonalData(userBean, auditContext);
		
		PersonalDataBean personalData = userDao.getUserDataByIdMsUser(user.getIdMsUser(), true);
		assertEquals("081234567890UPD", personalData.getPhoneRaw());
	}
	
	@Test
	@Rollback(true)
	void getUserDataRegistrationByLoginIdTest() throws IOException, ParseException {
		UserAutoFillDataRequest request = new UserAutoFillDataRequest();
		UserBean userData = new UserBean();
		userData.setLoginId(LOGIN_ID);
		request.setUserData(userData);
		
		UserAutoFillDataResponse response = userLogic.getUserDataRegistrationByLoginId(request, auditContext);
		Assert.assertNotNull(response.getUserData());
	}
	
	@Test
	@Rollback(true)
	void generateRandomPasswordTest() {
		String randomPass = userLogic.generateRandomPassword();
		Assert.assertNotNull(randomPass);
	}

	@Test
	@Rollback(true)
	void regextest() {		
		String email ="<EMAIL>";
		
		if(StringUtils.isNotBlank(email)) {
			AmGeneralsetting gsEmail = commonLogic.getGeneralSettingByCode(AmGlobalKey.GENERALSETTING_REGEX_EMAIL_FORMAT, null);
			String emailRegex = gsEmail != null && StringUtils.isNotBlank(gsEmail.getGsValue()) ? gsEmail.getGsValue() : "regexEmail";
			
			System.out.println(email.matches(emailRegex));
		}
	}
	
	@Test
	void checkPhoneAndEmailRegexTest() {
		String email ="<EMAIL>";
		String phone ="";
		
		AmGeneralsetting gsPhone = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_PHONE_FORMAT);
		String phoneNoRegex = gsPhone != null && StringUtils.isNotBlank(gsPhone.getGsValue()) ? gsPhone.getGsValue() : regexPhone;
		AmGeneralsetting gsEmail = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_REGEX_EMAIL_FORMAT);
		String emailRegex = gsEmail != null && StringUtils.isNotBlank(gsEmail.getGsValue()) ? gsEmail.getGsValue() : regexEmail;
		
		assertTrue(email.matches(regexEmail));
//		assertTrue(phone.matches(phoneNoRegex));

	}
}
