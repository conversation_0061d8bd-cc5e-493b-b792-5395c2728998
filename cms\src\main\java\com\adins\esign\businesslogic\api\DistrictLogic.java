package com.adins.esign.businesslogic.api;

import com.adins.esign.webservices.model.GetDistrictByInvitationRequest;
import com.adins.esign.webservices.model.GetDistrictEmbedRequest;
import com.adins.esign.webservices.model.GetDistrictRequest;
import com.adins.esign.webservices.model.GetDistrictResponse;
import com.adins.esign.webservices.model.external.GetDistrictListExternalRequest;
import com.adins.esign.webservices.model.external.GetDistrictListExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface DistrictLogic {
	GetDistrictResponse getDistrictList(GetDistrictRequest request, AuditContext audit);

	GetDistrictResponse getDistrictListEmbed(GetDistrictEmbedRequest request, AuditContext audit);

	GetDistrictResponse getDistrictListByInvitation(GetDistrictByInvitationRequest request, AuditContext audit);
	
	GetDistrictListExternalResponse getDistrictListExternal(GetDistrictListExternalRequest request, String apiKey, AuditContext audit);
	
}
