package com.adins.esign.model.custom.embed;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;

public class EmbedMsgBeanV2 {
	private MsTenant msTenant;
	private MsOffice msOffice;
	private AmMsuser amMsuser;
	private String decryptedEmail;
	
	public MsTenant getMsTenant() {
		return msTenant;
	}
	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}
	public MsOffice getMsOffice() {
		return msOffice;
	}
	public void setMsOffice(MsOffice msOffice) {
		this.msOffice = msOffice;
	}
	public AmMsuser getAmMsuser() {
		return amMsuser;
	}
	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}
	public String getDecryptedEmail() {
		return decryptedEmail;
	}
	public void setDecryptedEmail(String decryptedEmail) {
		this.decryptedEmail = decryptedEmail;
	}
	
	
}
