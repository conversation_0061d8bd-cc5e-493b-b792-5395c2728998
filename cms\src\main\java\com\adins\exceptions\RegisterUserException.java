package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class RegisterUserException extends AdInsException{
	private static final long serialVersionUID = 1L;
	public enum Reason{
		GEN_SIGN_SPECIMEN_FAILED,
		ALREADY_ACTIVATED
	} 
	private final Reason reason;
	public RegisterUserException(Reason reason) {
		this.reason = reason;
	}

	public RegisterUserException(String message, Reason reason) {
		super(message);
		this.reason = reason;
	}

	public RegisterUserException(Throwable ex, Reason reason) {
		super(ex);
		this.reason = reason;
	}
	
	public RegisterUserException(String message, Throwable ex, Reason reason) {
		super(message, ex);
		this.reason = reason;
	}
	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
				case GEN_SIGN_SPECIMEN_FAILED:
					return StatusCode.GEN_SIGN_SPECIMEN_FAILED;
				case ALREADY_ACTIVATED:
					return StatusCode.ALREADY_ACTIVATED;
				default:
					return StatusCode.UNKNOWN;
			}
		} 
		return StatusCode.UNKNOWN;
	}

}
