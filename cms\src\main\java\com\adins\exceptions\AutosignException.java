package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class AutosignException extends AdInsException{
	private static final long serialVersionUID = 1L;
	
	public enum ReasonAutosign {
		BASE64NOTVALID,
		EXECUTIONDATENOTVALID,
		INVALID_IMPORT_AUTOSIGN_DATE_RANGE,
		HEADER_NOT_FOUND,
		INVALID_STATUS_IMPORT,
		EMPTY_TEMPLATE_FILE
	}
	
	private final ReasonAutosign reason;

	public AutosignException(ReasonAutosign reason) {
		this.reason = reason;
	}

	public AutosignException(String message, ReasonAutosign reason) {
		super(message);
		this.reason = reason;
	}

	public AutosignException(Throwable ex, ReasonAutosign reason) {
		super(ex);
		this.reason = reason;
	}

	public AutosignException(String message, Throwable ex, ReasonAutosign reason) {
		super(message, ex);
		this.reason = reason;
	}

	public ReasonAutosign getReason() {
		return reason;
	}

	@Override
	public int getErrorCode() {
		if (this.reason != null) {
		switch (reason) {
		case BASE64NOTVALID:
			return StatusCode.BASE64NOTVALID;	
		case EXECUTIONDATENOTVALID:
			return StatusCode.EXECUTIONDATENOTVALID;
		case INVALID_IMPORT_AUTOSIGN_DATE_RANGE:
			return StatusCode.INVALID_IMPORT_AUTOSIGN_DATE_RANGE;
		case HEADER_NOT_FOUND:
			return StatusCode.HEADER_NOT_FOUND;
		case INVALID_STATUS_IMPORT:
			return StatusCode.INVALID_STATUS_IMPORT;
		case EMPTY_TEMPLATE_FILE:
			return StatusCode.EMPTY_TEMPLATE_FILE;
		default:
			return StatusCode.UNKNOWN;
		}
		
	}
	return StatusCode.UNKNOWN;
	}
}
