package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserPersonalData;
import com.adins.am.model.AmUserPersonalDataHistory;
import com.adins.am.model.AmUserpwdhistory;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.UserDao;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.PersonalDataHistoryBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.GetListDataPenggunaRequest;
import com.adins.esign.webservices.model.GetListUserManagementRequest;
import com.adins.esign.webservices.model.ListInquiryUserRequest;

@Transactional
@Component
public class UserDaoHbn extends BaseDaoHbn implements UserDao {
	private static final String CONST_PHONE = "phone";
	private static String CONST_ROLE_CODE = "roleCode";
	@Autowired
	private CloudStorageLogic cloudStorageLogic;
	
	@Override
	public void insertUser(AmMsuser newUser) {
		newUser.setUsrCrt(MssTool.maskData(newUser.getUsrCrt()));
		this.managerDAO.insert(newUser);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertUserNewTran(AmMsuser newUser) {
		newUser.setUsrCrt(MssTool.maskData(newUser.getUsrCrt()));
		this.managerDAO.insert(newUser);
	}
	

	
	@Override
	public void updateUser(AmMsuser updUser) {
		updUser.setUsrUpd(MssTool.maskData(updUser.getUsrUpd()));
		this.managerDAO.update(updUser);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateUserNewTran(AmMsuser updUser) {
		updUser.setUsrUpd(MssTool.maskData(updUser.getUsrUpd()));
		this.managerDAO.update(updUser);
	}

	@Override
	public AmMsuser getUserByLoginId(String loginId) {
		if (StringUtils.isBlank(loginId)){
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "join fetch u.msOffice mo "
				+ "join fetch mo.msTenant mt "
				+ "where u.loginId = :loginId and u.isActive ='1' and u.isDeleted ='0' ", 
						new Object[][] {{AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId)}});
	}

	@Override
	public AmMsuser getUserByIdMsUser(long id) {
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "join fetch u.msOffice mo "
				+ "where u.idMsUser = :idMsUser and u.isDeleted ='0' and u.isActive = '1' ", 
				new Object[][] {{AmMsuser.ID_MS_USER_HBM, id}});

	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public AmMsuser getUserByIdMsUserNewTrx(long id) {
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "join fetch u.msOffice mo "
				+ "where u.idMsUser = :idMsUser and u.isDeleted ='0' ", 
				new Object[][] {{AmMsuser.ID_MS_USER_HBM, id}});

	}
	
	@Override
	public int getDateDiffPassHistByIdMsUser(long id) {
		Object[][] params = { {AmMsuser.ID_MS_USER_HBM, id} };
		BigInteger diff = (BigInteger) this.managerDAO.selectOneNativeString(
			"SELECT IFNULL(DATEDIFF(MAX(DTM_CRT), CURRENT_TIMESTAMP),0) " +
			"from AM_USERPWDHISTORY " +
			"WHERE ID_MS_USER = :idMsUser", params);
		return diff.intValue(); 
	}
	
	@Override
	public AmUserpwdhistory getUserPwdhistory(long idUser) {
		return this.managerDAO.selectOne(
				"from AmUserpwdhistory uph "
				+ "join fetch uph.amMsuser u "
				+ "where u.idMsUser = :idMsUser",
				new Object [][] {{AmMsuser.ID_MS_USER_HBM, idUser}});
	}

	@Override
	public void insertUserPwdhistory(AmUserpwdhistory newUserPwdhistory) {
		newUserPwdhistory.setUsrCrt(MssTool.maskData(newUserPwdhistory.getUsrCrt()));
		this.managerDAO.insert(newUserPwdhistory);
	}
	
	private StringBuilder buildConditionalParam(Map<String, Object> paramsQuery, ListInquiryUserRequest request, String requestType) {
		StringBuilder conditionalParam = new StringBuilder();
		
		// Customer filter
		if (GlobalVal.INQUIRY_USER_REQUEST_TYPE_CUSTOMER.equalsIgnoreCase(requestType)) {
			if (StringUtils.isNumeric(request.getNik())) {
				String hashedIdentifier = MssTool.getHashedString(request.getNik());
				if (request.getNik().length() == 16) {
					conditionalParam.append(" and u.hashed_id_no = :hashedIdentifier ");
				} else {
					conditionalParam.append(" and vru.hashed_signer_registered_phone = :hashedIdentifier ");	
				}
				paramsQuery.put("hashedIdentifier", hashedIdentifier);

			} else {
				conditionalParam.append(" and vru.signer_registered_email = :signerRegisteredEmail AND vru.email_service = '0' ");
				paramsQuery.put(MsVendorRegisteredUser.SIGNER_REGISTERED_EMAIL_HBM, StringUtils.upperCase(request.getNik()));
			}
		}
		
		// Karyawan filter
		if (GlobalVal.INQUIRY_USER_REQUEST_TYPE_EMPLOYEE.equalsIgnoreCase(requestType)) {
			if (StringUtils.isNotBlank(request.getIsActive())) {
				conditionalParam.append(" and vru.is_active = :isActive ");
				paramsQuery.put("isActive", request.getIsActive());
			}
			if (StringUtils.isNotBlank(request.getFullName())) {
				conditionalParam.append(" and u.full_name like :fullName ");
				paramsQuery.put(AmMsuser.FULLNAME_HBM, "%" + StringUtils.upperCase(request.getFullName()) + "%");
			}
			
			if (StringUtils.isNotBlank(request.getLoginId())) {
				conditionalParam.append(" and vru.signer_registered_email like :signerRegisteredEmail ");
				paramsQuery.put(MsVendorRegisteredUser.SIGNER_REGISTERED_EMAIL_HBM, "%" + StringUtils.upperCase(request.getLoginId()) + "%");
			}
			
			if(StringUtils.isNotBlank(request.getActivatedDateStart())) {
				conditionalParam.append(" and cast(vru.activated_date as date) >= cast( :activatedDateStart as date) ");
				paramsQuery.put("activatedDateStart", request.getActivatedDateStart());
			}
			
			if (StringUtils.isNotBlank(request.getActivatedDateEnd())) {
				conditionalParam.append(" and cast(vru.activated_date as date) <= cast( :activatedDateEnd as date) ");
				paramsQuery.put("activatedDateEnd", request.getActivatedDateEnd());
			}
		}
		return conditionalParam;
	}

	@Override
	public List<Map<String, Object>> getListInquiryUserId(ListInquiryUserRequest request, String requestType, long idMsTenant, int start, int end) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder conditionalParam = this.buildConditionalParam(params, request, requestType); 
		
		params.put("start", start);
		params.put("end", end);
		params.put(MsTenant.ID_TENANT_HBM, idMsTenant);
		
		query
		.append(" select * from ( ")
			.append("select vru.id_ms_vendor_registered_user, u.id_ms_user, row_number() over(order by u.id_ms_user) as row ")
			.append("from am_msuser u ")
			.append("join ms_vendor_registered_user vru on vru.id_ms_user = u.id_ms_user ")
			.append("join ms_useroftenant uot on uot.id_ms_user = u.id_ms_user and id_ms_tenant = :idMsTenant ");
			
		
		if (GlobalVal.INQUIRY_USER_REQUEST_TYPE_EMPLOYEE.equalsIgnoreCase(requestType)) {
			query.append("join lateral ( ")
						.append("select amr.id_ms_role from am_memberofrole amr ")
						.append("join am_msrole mr on mr.id_ms_role = amr.id_ms_role and role_code <>'CUST' ")
						.append("where amr.id_ms_user = u.id_ms_user and mr.id_ms_tenant = uot.id_ms_tenant ")
						.append("limit 1 ")
				.append(") amr on true ");
		}
		
		query
			.append(" where 1=1 and u.is_deleted = '0' and u.is_active = '1' ")
			.append(conditionalParam)
			.append(" order by u.id_ms_user ")
			.append(") as b ")
			.append("where  b.row between :start and :end ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public BigInteger countListInquiryUser(ListInquiryUserRequest request, String requestType, long idMsTenant) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		StringBuilder conditionalParam = this.buildConditionalParam(params, request, requestType); 

		params.put(MsTenant.ID_TENANT_HBM, idMsTenant);
		
		query
		.append(" select count(1) from ( ")
			.append("select vru.id_ms_vendor_registered_user, u.id_ms_user ")
			.append("from am_msuser u ")
			.append("join ms_vendor_registered_user vru on vru.id_ms_user = u.id_ms_user ")
			.append("join ms_useroftenant uot on uot.id_ms_user = u.id_ms_user and id_ms_tenant = :idMsTenant ");
			
		
		if (GlobalVal.INQUIRY_USER_REQUEST_TYPE_EMPLOYEE.equalsIgnoreCase(requestType)) {
			query.append("join lateral ( ")
						.append("select amr.id_ms_role from am_memberofrole amr ")
						.append("join am_msrole mr on mr.id_ms_role = amr.id_ms_role and role_code <> 'CUST' ")
						.append("where amr.id_ms_user = u.id_ms_user and mr.id_ms_tenant = uot.id_ms_tenant ")
						.append("limit 1 ")
				.append(") amr on true ");
		}
		
		query
			.append(" where 1=1 and u.is_deleted = '0' and u.is_active = '1' ")
			.append(conditionalParam)
			.append(" order by u.id_ms_user ")
			.append(") as b ");
		
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getUserPasswordHistoryList(long idMsUser) {
		String lastPasswordCount = this.managerDAO.selectOne(AmGeneralsetting.class,
				new Object[][] {{ Restrictions.eq(AmGeneralsetting.GS_CODE_HBM, AmGlobalKey.GENERALSETTING_LAST_PASSWORD) }})
				.getGsValue();
		int pwCount = Integer.parseInt(lastPasswordCount);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select uph.password ")
			.append("from am_userpwdhistory uph ")
			.append("where uph.id_ms_user = :idMsUser ")
			.append("order by uph.dtm_crt desc ")
			.append("limit :limit ");
		
		Object[][] params = new Object[][] {	{ AmMsuser.ID_MS_USER_HBM, idMsUser },
												{ "limit", pwCount } };
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public void insertUseroftenant(MsUseroftenant useroftenant) {
		useroftenant.setUsrCrt(MssTool.maskData(useroftenant.getUsrCrt()));
		this.managerDAO.insert(useroftenant);
	}
	
	@Override
	public PersonalDataBean getUserDataByIdMsUser(long idMsUser, boolean isPhoto) {
		AmUserPersonalData personalData = this.managerDAO.selectOne(
				"from AmUserPersonalData ud "
				+ "join fetch ud.amMsuser u "
				+ "where u.idMsUser = :idMsUser and u.isDeleted ='0' and u.isActive ='1' ", 
				new Object[][] {{AmMsuser.ID_MS_USER_HBM, idMsUser}});

		PersonalDataBean personalDataBean = new PersonalDataBean();
		if (null != personalData) {
			personalDataBean.setUserPersonalData(personalData);
			if(null != personalData.getPhoneBytea()) {
				personalDataBean.setPhoneRaw(personalDataEncLogic.decryptToString(personalData.getPhoneBytea()));
			}
			if(null != personalData.getIdNoBytea()) {
				personalDataBean.setIdNoRaw(personalDataEncLogic.decryptToString(personalData.getIdNoBytea()));
			}
			if(null != personalData.getAddressBytea()) {
				personalDataBean.setAddressRaw(personalDataEncLogic.decryptToString(personalData.getAddressBytea()));
			}
			if(isPhoto) {
				if(null != personalData.getPhotoIdBytea()) {
					String key = new String(personalData.getPhotoIdBytea());
					byte[] bytePhoto = cloudStorageLogic.getContentKtp(key);
					personalDataBean.setPhotoIdRaw(bytePhoto);
				}
				if(null != personalData.getPhotoSelf()) {
					String key = new String(personalData.getPhotoSelf());
					byte[] bytePhoto = cloudStorageLogic.getContentKtp(key);
					personalDataBean.setSelfPhotoRaw(bytePhoto);
				}
			}
		}
		return personalDataBean;
	}
	
	@Override
	public PersonalDataBean getUserDataByIdMsUserOptional(long idMsUser, boolean getNIK, boolean getPhone,
			boolean getAddress, boolean getPhotoKTP, boolean getPhotoSelfie) {
		AmUserPersonalData personalData = this.managerDAO.selectOne(
				"from AmUserPersonalData ud "
				+ "join fetch ud.amMsuser u "
				+ "where u.idMsUser = :idMsUser and u.isDeleted ='0' and u.isActive ='1' ", 
				new Object[][] {{AmMsuser.ID_MS_USER_HBM, idMsUser}});

		PersonalDataBean personalDataBean = new PersonalDataBean();
		if (null == personalData) {
			return personalDataBean;
		}
		
		personalDataBean.setUserPersonalData(personalData);
		if(null != personalData.getPhoneBytea() && getPhone) {
			personalDataBean.setPhoneRaw(personalDataEncLogic.decryptToString(personalData.getPhoneBytea()));
		}
		if(null != personalData.getIdNoBytea() && getNIK) {
			personalDataBean.setIdNoRaw(personalDataEncLogic.decryptToString(personalData.getIdNoBytea()));
		}
		if(null != personalData.getAddressBytea() && getAddress) {
			personalDataBean.setAddressRaw(personalDataEncLogic.decryptToString(personalData.getAddressBytea()));
		}
		if(null != personalData.getPhotoIdBytea() && getPhotoKTP) {
			String key = new String(personalData.getPhotoIdBytea());
			byte[] bytePhoto = cloudStorageLogic.getContentKtp(key);
			personalDataBean.setPhotoIdRaw(bytePhoto);
		}
		if(null != personalData.getPhotoSelf() && getPhotoSelfie) {
			String key = new String(personalData.getPhotoSelf());
			byte[] bytePhoto = cloudStorageLogic.getContentKtp(key);
			personalDataBean.setSelfPhotoRaw(bytePhoto);
		}
		return personalDataBean;
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public PersonalDataBean getUserDataByIdMsUserNewTrx(long idMsUser, boolean getPhoto) {
		AmUserPersonalData personalData = this.managerDAO.selectOne(
				"from AmUserPersonalData ud "
				+ "join fetch ud.amMsuser u "
				+ "where u.idMsUser = :idMsUser and u.isDeleted ='0' and u.isActive ='1' ", 
				new Object[][] {{AmMsuser.ID_MS_USER_HBM, idMsUser}});
		
		if (null == personalData) {
			return null;
		}
		
		PersonalDataBean personalDataBean = new PersonalDataBean();
		personalDataBean.setUserPersonalData(personalData);
		if(null != personalData.getPhoneBytea()) {
			personalDataBean.setPhoneRaw(personalDataEncLogic.decryptToString(personalData.getPhoneBytea()));
		}
		if(null != personalData.getIdNoBytea()) {
			personalDataBean.setIdNoRaw(personalDataEncLogic.decryptToString(personalData.getIdNoBytea()));
		}
		if(null != personalData.getAddressBytea()) {
			personalDataBean.setAddressRaw(personalDataEncLogic.decryptToString(personalData.getAddressBytea()));
		}
		if (getPhoto) {
			if(null != personalData.getPhotoIdBytea()) {
				String key = new String(personalData.getPhotoIdBytea());
				byte[] bytePhoto = cloudStorageLogic.getContentKtp(key);
				personalDataBean.setPhotoIdRaw(bytePhoto);
			}
			if(null != personalData.getPhotoSelf()) {
				String key = new String(personalData.getPhotoSelf());
				byte[] bytePhoto = cloudStorageLogic.getContentKtp(key);
				personalDataBean.setSelfPhotoRaw(bytePhoto);
			}
		}
		
		return personalDataBean;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertUserPersonalDataHistoryNewTrx(PersonalDataHistoryBean personalDataHistoryBean) {
		AmUserPersonalDataHistory amUserPersonalDataHistory = personalDataHistoryBean.getUserPersonalDataHistory();
		
		if(StringUtils.isNotBlank(personalDataHistoryBean.getIdNoRaw())) {
			amUserPersonalDataHistory.setIdNoBytea(personalDataEncLogic.encryptFromString(personalDataHistoryBean.getIdNoRaw()));
		}
		if(StringUtils.isNotBlank(personalDataHistoryBean.getPhoneRaw())) {
			amUserPersonalDataHistory.setPhoneBytea(personalDataEncLogic.encryptFromString(personalDataHistoryBean.getPhoneRaw()));
		}
		if(StringUtils.isNotBlank(personalDataHistoryBean.getAddressRaw())) {
			amUserPersonalDataHistory.setAddressBytea(personalDataEncLogic.encryptFromString(personalDataHistoryBean.getAddressRaw()));
		}
		if(null != personalDataHistoryBean.getPhotoIdRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationKtp(personalDataHistoryBean.getIdNoRaw(), personalDataHistoryBean.getPhotoIdRaw());
			amUserPersonalDataHistory.setPhotoIdBytea(key.getBytes());
		}
		if(null != personalDataHistoryBean.getSelfPhotoRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationSelfie(personalDataHistoryBean.getIdNoRaw(), personalDataHistoryBean.getSelfPhotoRaw());
			amUserPersonalDataHistory.setPhotoSelf(key.getBytes());
		}
		
		amUserPersonalDataHistory.setUsrCrt(MssTool.maskData(amUserPersonalDataHistory.getUsrCrt()));
		this.managerDAO.insert(amUserPersonalDataHistory);
	}
	
	@Override
	public void insertUserPersonalData(PersonalDataBean personalDataBean) {
		AmUserPersonalData amUserPersonalData = personalDataBean.getUserPersonalData();
		
		if(StringUtils.isNotBlank(personalDataBean.getIdNoRaw())) {
			amUserPersonalData.setIdNoBytea(personalDataEncLogic.encryptFromString(personalDataBean.getIdNoRaw()));
		}
		if(StringUtils.isNotBlank(personalDataBean.getPhoneRaw())) {
			amUserPersonalData.setPhoneBytea(personalDataEncLogic.encryptFromString(personalDataBean.getPhoneRaw()));
		}
		if(StringUtils.isNotBlank(personalDataBean.getAddressRaw())) {
			amUserPersonalData.setAddressBytea(personalDataEncLogic.encryptFromString(personalDataBean.getAddressRaw()));
		}
		if(null != personalDataBean.getPhotoIdRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationKtp(personalDataBean.getIdNoRaw(), personalDataBean.getPhotoIdRaw());
			amUserPersonalData.setPhotoIdBytea(key.getBytes());
		}
		if(null != personalDataBean.getSelfPhotoRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationSelfie(personalDataBean.getIdNoRaw(), personalDataBean.getSelfPhotoRaw());
			amUserPersonalData.setPhotoSelf(key.getBytes());
		}
		
		amUserPersonalData.setUsrCrt(MssTool.maskData(amUserPersonalData.getUsrCrt()));
		this.managerDAO.insert(amUserPersonalData);
	}
	
	@Override
	public void updateUserPersonalData(PersonalDataBean personalDataBean) {
		AmUserPersonalData amUserPersonalData = personalDataBean.getUserPersonalData();
		
		if(StringUtils.isNotBlank(personalDataBean.getIdNoRaw())) {
			amUserPersonalData.setIdNoBytea(personalDataEncLogic.encryptFromString(personalDataBean.getIdNoRaw()));
		}
		else {
			amUserPersonalData.setIdNoBytea(null);
		}
		if(StringUtils.isNotBlank(personalDataBean.getPhoneRaw())) {
			amUserPersonalData.setPhoneBytea(personalDataEncLogic.encryptFromString(personalDataBean.getPhoneRaw()));
		}
		else {
			amUserPersonalData.setPhoneBytea(null);
		}
		if(StringUtils.isNotBlank(personalDataBean.getAddressRaw())) {
			amUserPersonalData.setAddressBytea(personalDataEncLogic.encryptFromString(personalDataBean.getAddressRaw()));
		}
		else {
			amUserPersonalData.setAddressBytea(null);
		}
		if(null != personalDataBean.getPhotoIdRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationKtp(personalDataBean.getIdNoRaw(), personalDataBean.getPhotoIdRaw());
			amUserPersonalData.setPhotoIdBytea(key.getBytes());
		}else {
			amUserPersonalData.setPhotoIdBytea(null);
		}
		if(null != personalDataBean.getSelfPhotoRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationSelfie(personalDataBean.getIdNoRaw(), personalDataBean.getSelfPhotoRaw());
			amUserPersonalData.setPhotoSelf(key.getBytes());
		}
		else {
			amUserPersonalData.setPhotoSelf(null);
		}
		
		amUserPersonalData.setUsrUpd(MssTool.maskData(amUserPersonalData.getUsrUpd()));
		this.managerDAO.update(amUserPersonalData);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateUserPersonalDataNewTrans(PersonalDataBean personalDataBean) {
		AmUserPersonalData amUserPersonalData = personalDataBean.getUserPersonalData();
		
		if(StringUtils.isNotBlank(personalDataBean.getIdNoRaw())) {
			amUserPersonalData.setIdNoBytea(personalDataEncLogic.encryptFromString(personalDataBean.getIdNoRaw()));
		}
		else {
			amUserPersonalData.setIdNoBytea(null);
		}
		if(StringUtils.isNotBlank(personalDataBean.getPhoneRaw())) {
			amUserPersonalData.setPhoneBytea(personalDataEncLogic.encryptFromString(personalDataBean.getPhoneRaw()));
		}
		else {
			amUserPersonalData.setPhoneBytea(null);
		}
		if(StringUtils.isNotBlank(personalDataBean.getAddressRaw())) {
			amUserPersonalData.setAddressBytea(personalDataEncLogic.encryptFromString(personalDataBean.getAddressRaw()));
		}
		else {
			amUserPersonalData.setAddressBytea(null);
		}
		if(null != personalDataBean.getPhotoIdRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationKtp(personalDataBean.getIdNoRaw(), personalDataBean.getPhotoIdRaw());
			amUserPersonalData.setPhotoIdBytea(key.getBytes());
		}else {
			amUserPersonalData.setPhotoIdBytea(null);
		}
		if(null != personalDataBean.getSelfPhotoRaw()) {
			String key = this.cloudStorageLogic.storeRegistrationSelfie(personalDataBean.getIdNoRaw(), personalDataBean.getSelfPhotoRaw());
			amUserPersonalData.setPhotoSelf(key.getBytes());
		}
		else {
			amUserPersonalData.setPhotoSelf(null);
		}
		
		amUserPersonalData.setUsrUpd(MssTool.maskData(amUserPersonalData.getUsrUpd()));
		this.managerDAO.update(amUserPersonalData);
	}
	@Override
	public AmUserPersonalData getUserDataByPhone(String phone) {
		Object[][] params = new Object[][] {{AmMsuser.HASHED_PHONE_HBM, MssTool.getHashedString(phone)}};
		return this.managerDAO.selectOne(
				"from AmUserPersonalData ud "
				+ "join fetch ud.amMsuser u "
				+ "where u.hashedPhone = :hashedPhone and u.isDeleted ='0' ", 
				params);
	}

	@Override
	public AmMsuser getActiveUserByPhone(String phone) {
		if (StringUtils.isBlank(phone)) {
			return null;
		}
		
		Object[][] params = new Object[][] {{AmMsuser.HASHED_PHONE_HBM, MssTool.getHashedString(phone)}};
		
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "where u.hashedPhone = :hashedPhone and u.isActive ='1'  and u.isDeleted ='0' ", 
				params);
	}
	
	@Override
	public AmMsuser getUserByIdNo(String idNo) {
		if (StringUtils.isBlank(idNo)) {
			return null;
		}
		
		Object[][] params = new Object[][] {{AmMsuser.HASHED_IDNO_HBM, MssTool.getHashedString(idNo)}};
		
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "where u.hashedIdNo = :hashedIdNo and u.isDeleted ='0' ", 
				params);
	}
	
	@Override
	public AmMsuser getUserByPhone(String phone) {
		if (StringUtils.isBlank(phone)) {
			return null;
		}
		
		Object[][] params = new Object[][] {{AmMsuser.HASHED_PHONE_HBM, MssTool.getHashedString(phone)}};
		
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "where u.hashedPhone = :hashedPhone and u.isDeleted ='0' ", 
				params);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public AmMsuser getUserByIdNoNewTran(String idNo) {
		if (StringUtils.isBlank(idNo)) {
			return null;
		}
		
		Object[][] params = new Object[][] {{AmMsuser.HASHED_IDNO_HBM, MssTool.getHashedString(idNo)}};
		
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "where u.hashedIdNo = :hashedIdNo and u.isDeleted ='0' ", 
				params);
	}

	@Override
	public AmMsuser getUserByOtpCode(String otpCode) {
		Object[][] params = new Object[][] {{AmMsuser.OTP_CODE_HBM, StringUtils.upperCase(otpCode)}};
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "where u.otpCode = :otpCode and u.isDeleted ='0' ", 
				params);
	}

	@Override
	public List<AmMsuser> listUserWithCertExpDateLessThan30Days(MsTenant tenant, MsVendor vendor) {
		if (null == tenant || null == vendor) {
			return Collections.emptyList();
		}
		
		StringBuilder query = new StringBuilder();
		query.append("select u.id_ms_user, u.login_id from am_msuser u ")
			 .append("join ms_vendor_registered_user vru on vru.id_ms_user = u.id_ms_user ")
			 .append("join ms_vendoroftenant vot on vot.id_ms_vendor = vru.id_ms_vendor ")
			 .append("join ms_tenant t on t.id_ms_tenant = vot.id_ms_tenant ")
			 .append("join ms_vendor v on v.id_ms_vendor = vot.id_ms_vendor ")
			 .append("where t.tenant_code = :tenantCode AND v.vendor_code = :vendorCode and vru.is_active = '1' ")
			 .append("and vru.cert_expired_date < current_date + interval '30' day ");
		
		List<Map<String, Object>> listResult = this.managerDAO.selectAllNativeString(query.toString(), 
				new Object[][] {{"tenantCode", tenant.getTenantCode()}, {"vendorCode", vendor.getVendorCode()}});
		
		Iterator<Map<String, Object>> itr = listResult.iterator();
		List<AmMsuser> listUser = new ArrayList<>();
		while(itr.hasNext()) {
			Map<String, Object> map = itr.next();
			AmMsuser user = new AmMsuser();
			BigInteger id = (BigInteger) map.get("d0");
			user.setIdMsUser(id.longValue());
			user.setLoginId((String) map.get("d1"));
			
			listUser.add(user);
		}
		
		return listUser;
	}

	@Override
	public List<Map<String, Object>> getListInquiryEditUserId(String userIdentifier, String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		params.put(CONST_ROLE_CODE, GlobalVal.ROLE_CUSTOMER);
		if (StringUtils.isNumeric(userIdentifier)) {
			// NIK or Phone
			params.put("userIdentifier", MssTool.getHashedString(userIdentifier));
		} else {
			// Email
			params.put("userIdentifier", StringUtils.upperCase(userIdentifier));
		}

		String additionalQueryCondition = null;
		if (StringUtils.isNumeric(userIdentifier)) {
			if (userIdentifier.length() == 16) {
				additionalQueryCondition = "mu.hashed_id_no = :userIdentifier ";
			} else {
				additionalQueryCondition = "mvu.hashed_signer_registered_phone = :userIdentifier ";
			}
		} else {
			additionalQueryCondition = "mvu.signer_registered_email = :userIdentifier ";	
		}
		
		StringBuilder query = new StringBuilder();
		query
			.append(" select distinct (mvu.id_ms_vendor_registered_user) ")
			.append(" from am_msuser mu ")
			.append(" join am_memberofrole mor on mu.id_ms_user = mor.id_ms_user ")
			.append(" join ms_vendor_registered_user mvu on mu.id_ms_user = mvu.id_ms_user ")
			.append(" where 1=1 ")
			.append(" and " + additionalQueryCondition)
			.append(" and mor.id_ms_role = ")
				.append( " (select id_ms_role from am_msrole mr ")
				.append(" join ms_tenant mt on mt.id_ms_tenant = mr.id_ms_tenant ")
				.append(" where mr.role_code = :roleCode and mt.tenant_code = :tenantCode) ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}
	
	@Override
	public List<Map<String, Object>> getListUserViewOtpWithId(String userIdentifier) {
		Map<String, Object> params = new HashMap<>();
		if (StringUtils.isNumeric(userIdentifier)) {
			// NIK or Phone
			params.put("userIdentifier", MssTool.getHashedString(userIdentifier));
		} else {
			// Email
			params.put("userIdentifier", StringUtils.upperCase(userIdentifier));
		}

		String additionalQueryCondition = null;
		if (StringUtils.isNumeric(userIdentifier)) {
			if (userIdentifier.length() == 16) {
				additionalQueryCondition = "mu.hashed_id_no = :userIdentifier ";
			} else {
				additionalQueryCondition = "mvu.hashed_signer_registered_phone = :userIdentifier ";
			}
		} else {
			additionalQueryCondition = "mvu.signer_registered_email = :userIdentifier ";	
		}
		StringBuilder query = new StringBuilder();
		query
			.append(" select distinct (mvu.id_ms_vendor_registered_user) ")
			.append(" from am_msuser mu ")
			.append(" join am_memberofrole mor on mu.id_ms_user = mor.id_ms_user ")
			.append(" join am_msrole mr on mor.id_ms_role = mr.id_ms_role ")
			.append(" join ms_tenant mt on mr.id_ms_tenant = mt.id_ms_tenant ")
			.append(" join ms_vendor_registered_user mvu on mu.id_ms_user = mvu.id_ms_user ")
			.append(" and " + additionalQueryCondition);
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getDistinctIdMsusersByEmail(String email) {
		Map<String, Object> params = new HashMap<>();
		params.put(GlobalVal.CONST_EMAIL, StringUtils.isBlank(email) ? StringUtils.EMPTY : StringUtils.upperCase(email));
		
		StringBuilder query = new StringBuilder()
				.append("select distinct id_ms_user from ms_vendor_registered_user ")
				.append("where signer_registered_email = :email ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getDistinctIdMsusersByPhone(String phone) {
		String hashedPhone = StringUtils.isBlank(phone) ? StringUtils.EMPTY : MssTool.getHashedString(phone);
		
		Map<String, Object> params = new HashMap<>();
		params.put(CONST_PHONE, hashedPhone);
		
		StringBuilder query = new StringBuilder()
				.append("select distinct id_ms_user from ms_vendor_registered_user ")
				.append("where hashed_signer_registered_phone = :phone ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getDistinctIdMsusersByPhoneAndVendor(String phone, String vendorCode) {
		String hashedPhone = StringUtils.isBlank(phone) ? StringUtils.EMPTY : MssTool.getHashedString(phone);
		
		Map<String, Object> params = new HashMap<>();
		params.put(CONST_PHONE, hashedPhone);
		params.put("vendor", StringUtils.isBlank(vendorCode) ? StringUtils.EMPTY : StringUtils.upperCase(vendorCode));
		
		StringBuilder query = new StringBuilder()
				.append("select distinct id_ms_user from ms_vendor_registered_user vru ")
				.append("join ms_vendor v on v.id_ms_vendor = vru.id_ms_vendor ")
				.append("where hashed_signer_registered_phone = :phone and v.vendor_code = :vendor ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getDistinctIdMsusersByEmailAndVendor(String email, String vendorCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(GlobalVal.CONST_EMAIL, StringUtils.isBlank(email) ? StringUtils.EMPTY : StringUtils.upperCase(email));
		params.put("vendor", StringUtils.isBlank(vendorCode) ? StringUtils.EMPTY : StringUtils.upperCase(vendorCode));
		
		StringBuilder query = new StringBuilder()
				.append("select distinct id_ms_user from ms_vendor_registered_user vru ")
				.append("join ms_vendor v on v.id_ms_vendor = vru.id_ms_vendor ")
				.append("where signer_registered_email = :email and v.vendor_code = :vendor ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<AmUserpwdhistory> getListUserpwdhistory(AmMsuser user) {
		Object[][] params = new Object[][] {{ "amMsuser", user }};
		return (List<AmUserpwdhistory>) managerDAO.list(
				"from AmUserpwdhistory uph "
				+ "join fetch uph.amMsuser "
				+ "where uph.amMsuser = :amMsuser ", params).get(AmGlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public List<Map<String, Object>> getListUserManagement(GetListUserManagementRequest request, long idMsTenant, int min, int max) {
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		params.put("min", min);
		params.put("max", max);
		params.put(MsTenant.ID_TENANT_HBM, idMsTenant);
		
		
		query.append("WITH RankedUsers AS (");
        query.append("  SELECT ");
        query.append("    am_msuser.full_name, ");
        query.append("    am_msuser.login_id, ");
        query.append("    am_msrole.role_code, ");
        query.append("    am_msuser.is_active, ");
        query.append("    am_msrole.is_usermanagement, ");
        query.append("    ROW_NUMBER() OVER (ORDER BY am_msuser.login_id) AS rowNum ");
        query.append("  FROM ");
        query.append("    am_msuser ");
        query.append("  JOIN ");
        query.append("    ms_useroftenant ON ms_useroftenant.id_ms_user = am_msuser.id_ms_user ");
        query.append("  JOIN ");
        query.append("    am_msrole ON am_msrole.id_ms_tenant = ms_useroftenant.id_ms_tenant ");
        query.append("  JOIN ");
        query.append("    am_memberofrole ON am_msuser.id_ms_user = am_memberofrole.id_ms_user ");
        query.append("      AND am_msrole.id_ms_role = am_memberofrole.id_ms_role ");
        query.append("  WHERE ");
        query.append("    am_msrole.is_usermanagement = '1' ");
        query.append("    AND ms_useroftenant.id_ms_tenant = :idMsTenant");
        
    	if (StringUtils.isNotBlank(request.getLoginId())) {
			query.append(" and am_msuser.login_id LIKE :loginId ");
			params.put(AmMsuser.LOGIN_ID_HBM, StringUtils.isBlank(request.getLoginId()) ? StringUtils.EMPTY : "%" + StringUtils.upperCase(request.getLoginId() + "%"));
		}
		
		if (StringUtils.isNotBlank(request.getRoleCode())) {
			query.append(" and am_msrole.role_code = :roleCode ");
			params.put(CONST_ROLE_CODE, StringUtils.isBlank(request.getRoleCode()) ? StringUtils.EMPTY : StringUtils.upperCase(request.getRoleCode()));
		}
		
		if (StringUtils.isNotBlank(request.getStatus())) {  
		    query.append(" and am_msuser.is_active = :isActive ");
		    params.put(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, request.getStatus());
		}
		
        query.append(") ");
        query.append("SELECT ");
        query.append("  full_name, ");
        query.append("  login_id, ");
        query.append("  role_code, ");
        query.append("  is_active, ");
        query.append("  is_usermanagement ");
        query.append("FROM RankedUsers ");
		query.append("where RankedUsers.rowNum between :min and :max");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getAllUser() {
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder()
				.append("select distinct id_ms_user, login_id, full_name, hashed_phone, hashed_id_no from am_msuser usr ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public int countListUserManagement(GetListUserManagementRequest request, long idMsTenant) {
Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		params.put(MsTenant.ID_TENANT_HBM, idMsTenant);
		
		query.append("WITH RankedUsers AS (");
        query.append("  SELECT ");
        query.append("    am_msuser.full_name, ");
        query.append("    am_msuser.login_id, ");
        query.append("    am_msrole.role_code, ");
        query.append("    am_msuser.is_active, ");
        query.append("    am_msrole.is_usermanagement, ");
        query.append("    ROW_NUMBER() OVER (ORDER BY am_msuser.login_id) AS rowNum ");
        query.append("  FROM ");
        query.append("    am_msuser ");
        query.append("  JOIN ");
        query.append("    ms_useroftenant ON ms_useroftenant.id_ms_user = am_msuser.id_ms_user ");
        query.append("  JOIN ");
        query.append("    am_msrole ON am_msrole.id_ms_tenant = ms_useroftenant.id_ms_tenant ");
        query.append("  JOIN ");
        query.append("    am_memberofrole ON am_msuser.id_ms_user = am_memberofrole.id_ms_user ");
        query.append("      AND am_msrole.id_ms_role = am_memberofrole.id_ms_role ");
        query.append("  WHERE ");
        query.append("    am_msrole.is_usermanagement = '1' ");
        query.append("    AND ms_useroftenant.id_ms_tenant = :idMsTenant");
        
    	if (StringUtils.isNotBlank(request.getLoginId())) {
			query.append(" and am_msuser.login_id LIKE :loginId ");
			params.put(AmMsuser.LOGIN_ID_HBM, StringUtils.isBlank(request.getLoginId()) ? StringUtils.EMPTY : "%" + StringUtils.upperCase(request.getLoginId() + "%"));
		}
		
		if (StringUtils.isNotBlank(request.getRoleCode())) {
			query.append(" and am_msrole.role_code = :roleCode ");
			params.put(CONST_ROLE_CODE, StringUtils.isBlank(request.getRoleCode()) ? StringUtils.EMPTY : StringUtils.upperCase(request.getRoleCode()));
		}
		
		if (StringUtils.isNotBlank(request.getStatus())) {  
		    query.append(" and am_msuser.is_active = :isActive ");
		    params.put(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, request.getStatus());
		}
		
        query.append(") ");
        query.append("SELECT count(*)");
        query.append("FROM RankedUsers ");
		
		List<Map<String, Object>> total = this.getManagerDAO().selectAllNativeString(query.toString(), params);
		
		String totalString = total.get(0).toString();
		int start = total.get(0).toString().indexOf("=");
		int end = total.get(0).toString().indexOf("}");
		String subString = totalString.substring(start + 1, end);

		return Integer.parseInt(subString);
	}
	
	
	@Override
	public List<Map<String, Object>> getListDataPengguna(GetListDataPenggunaRequest request, long idMsTenant) {
	    
	    Map<String, Object> params = new HashMap<>();
	    
	    String userIdentifier = request.getLoginId();

		params.put("idMsTenant", idMsTenant);
	
	    if (StringUtils.isNumeric(userIdentifier)) {
			params.put("userIdentifier", MssTool.getHashedString(userIdentifier));
		} else {
			params.put("userIdentifier", StringUtils.upperCase(userIdentifier));
		}



		String additionalQueryCondition = null;
		if (StringUtils.isNumeric(userIdentifier)) {
			if (userIdentifier.length() == 16) {
				additionalQueryCondition = "mu.hashed_id_no = :userIdentifier ";
			} else {
				additionalQueryCondition = "mvu.hashed_signer_registered_phone = :userIdentifier ";
			}
		} else {
			additionalQueryCondition = "mvu.signer_registered_email = :userIdentifier ";	
		}
		
	    StringBuilder query = new StringBuilder();
	    query.append("SELECT mvu.signer_registered_email AS \"loginId\", ");
	    query.append("full_name AS \"fullName\", ");
	    query.append("ms_vendor.vendor_name AS \"vendorName\", ");
	    query.append("ms_vendor.vendor_code AS \"vendorCode\", ");
	    query.append("mvu.is_active AS \"isActivated\", ");
	    query.append("mvu.is_registered AS \"isRegistered\", ");
	    query.append("CASE ");
	    query.append("  WHEN mvu.is_active = '1' AND mvu.is_registered = '1' THEN 'Sudah Aktivasi' ");
	    query.append("  WHEN mvu.is_active = '0' AND mvu.is_registered = '1' THEN 'Belum Aktivasi' ");
	    query.append("  ELSE 'Belum registrasi' ");
	    query.append("END AS \"isActive\", ");
	    query.append("mvu.id_ms_vendor_registered_user AS idMsVendorRegisteredUser ");
	    query.append("FROM am_msuser mu ");
	    query.append("JOIN ms_vendor_registered_user mvu ON mu.id_ms_user = mvu.id_ms_user ");
	    query.append("JOIN ms_vendor ON mvu.id_ms_vendor = ms_vendor.id_ms_vendor ");
		query.append("JOIN ms_useroftenant mut ON mu.id_ms_user = mut.id_ms_user ");
	    query.append("WHERE " + additionalQueryCondition);
		query.append("AND mut.id_ms_tenant = :idMsTenant ");

	    return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public AmUserPersonalData getUserPersonalDataByIdMsUser(AmMsuser idMsUser) {
		Object[][] params = new Object[][]{{"idMsuser", idMsUser}};
 		return this.managerDAO.selectOne(
 				"from AmUserPersonalData amp "
 				+ "where amp.amMsuser = :idMsuser "
 				, params);
	}

	@Override
	public int countRoleInTenant(String roleCode, String tenantCode) {
		
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		params.put(GlobalVal.CONST_TENANT, tenantCode);
		params.put("role", roleCode);
		
		query
		.append("select count (am_msrole.id_ms_role) from am_msuser join ms_useroftenant on ms_useroftenant.id_ms_user = am_msuser.id_ms_user\r\n" + 
				"join ms_tenant on ms_tenant.id_ms_tenant = ms_useroftenant.id_ms_tenant\r\n" + 
				"join am_msrole on am_msrole.id_ms_tenant = ms_tenant.id_ms_tenant\r\n" + 
				"join am_memberofrole on am_msuser.id_ms_user = am_memberofrole.id_ms_user and am_msrole.id_ms_role = am_memberofrole.id_ms_role " + 
				"where am_msrole.is_usermanagement = '1' AND ms_tenant.tenant_code = :tenant \r\n");
		
		if (StringUtils.isNotBlank(roleCode)) {
			query.append("and am_msrole.role_code = :role");
		}
		
		List<Map<String, Object>> total = this.getManagerDAO().selectAllNativeString(query.toString(), params);
		
		String totalString = total.get(0).toString();
		int start = total.get(0).toString().indexOf("=");
		int end = total.get(0).toString().indexOf("}");
		String subString = totalString.substring(start + 1, end);

		return Integer.parseInt(subString);
	}

	@Override
	public String getLoginIdByTenantAndCallerId(String tenantCode, String callerId) {
		
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		
		params.put(GlobalVal.CONST_TENANT, tenantCode);
		params.put("callerId", callerId);
		
		query
			.append("select distinct(am_msuser.login_id) \r\n" + 
					"from am_msuser join ms_useroftenant on ms_useroftenant.id_ms_user = am_msuser.id_ms_user\r\n" + 
					"join ms_tenant on ms_tenant.id_ms_tenant = ms_useroftenant.id_ms_tenant\r\n" + 
					"join am_msrole on am_msrole.id_ms_tenant = ms_tenant.id_ms_tenant\r\n" + 
					"join am_memberofrole on am_msuser.id_ms_user = am_memberofrole.id_ms_user and am_msrole.id_ms_role = am_memberofrole.id_ms_role " + 
					"where am_msrole.is_usermanagement = '1' and ms_tenant.tenant_code = :tenant \r\n" + 
					"and am_msuser.login_id = :callerId ");
		
		return (String) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override	
	public AmMsuser getDataUserByLoginId(String loginId) {
		if (StringUtils.isBlank(loginId)){
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "join fetch u.msOffice mo "
				+ "join fetch mo.msTenant mt "
				+ "where u.loginId = :loginId and u.isDeleted ='0' ", 
						new Object[][] {{AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId)}});
	}
	

	@Override
	public AmMsuser getNotActiveUser(String loginId) {
		Object[][] params = new Object[][]{{"loginid", StringUtils.upperCase(loginId)}};
		return this.managerDAO.selectOne(
				"from AmMsuser us "
				+ " where us.loginId = :loginid "
				, params);
	}

	@Override
	public BigInteger getIdMsUserBySignerRegisteredEmail(String signerRegisteredEmail) {
		
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		
		params.put("signerRegisteredEmail", StringUtils.upperCase(signerRegisteredEmail));
		
		query
			.append("select distinct id_ms_user from ms_vendor_registered_user where signer_registered_email = :signerRegisteredEmail ");
		
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public MsVendorRegisteredUser getSignerDetail(long idMsUser, String vendorCode) {
		
		Object[][] params = new Object[][] {
			{AmMsuser.ID_MS_USER_HBM, idMsUser},
			{MsVendor.VENDOR_CODE_HBM, vendorCode}
		};
		
		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser mvru "
				+ "join fetch mvru.msVendor mv " 
				+ "join fetch mvru.amMsuser u "
				+ "where u.idMsUser = :idMsUser and mv.vendorCode = :vendorCode", 
				params);
	}

	@Override
	public BigInteger getIdMsUserByLoginId(String loginId) {
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		
		params.put(AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId));
		
		query
			.append("select distinct id_ms_user from ms_vendor_registered_user where signer_registered_email = :loginId ");
		
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public BigInteger getIdMsUserByPhoneNo(String phoneNo) {
		
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		
		params.put("phoneNo", MssTool.getHashedString(phoneNo));
		
		query
			.append("select distinct id_ms_user from ms_vendor_registered_user where hashed_signer_registered_phone = :phoneNo ");
		
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public String getPassDefault(String isActive) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder query = new StringBuilder();
		params.put("isActive", isActive);
		
		query
			.append("select default_email_password from ms_email_hosting where is_active = :isActive LIMIT 1");
		
		return (String) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getDistinctIdMsusersByPhoneAndEmail(String phone, String email) {
		
		String hashedPhone = StringUtils.isBlank(phone) ? StringUtils.EMPTY : MssTool.getHashedString(phone);
		
		Map<String, Object> params = new HashMap<>();
		params.put(CONST_PHONE, hashedPhone);
		params.put(GlobalVal.CONST_EMAIL, StringUtils.isBlank(email) ? StringUtils.EMPTY : StringUtils.upperCase(email));
		
		StringBuilder query = new StringBuilder()
				.append("select distinct id_ms_user from ms_vendor_registered_user ")
				.append("where hashed_signer_registered_phone = :phone and signer_registered_email = :email ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	

	@Override
	public MsVendorRegisteredUser getVendorRegisteredUser(long idMsUser, String vendorCode) {
		Object[][] params = {{AmMsuser.ID_MS_USER_HBM, idMsUser},
			{MsVendor.VENDOR_CODE_HBM, vendorCode}
		};
		
		return this.managerDAO.selectOne(
				"from MsVendorRegisteredUser mvru "
				+ "join fetch mvru.msVendor mv " 
				+ "join fetch mvru.amMsuser u "
				+ "where u.idMsUser = :idMsUser and mv.vendorCode = :vendorCode", 
				params);
	}

	@Override
	public Long getIdMsUserRegisteredInOtherVendorByEmail(String email, MsVendor vendor) {
		Map<String, Object> params = new HashMap<>();
		params.put(GlobalVal.CONST_EMAIL, StringUtils.upperCase(email));
		params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select distinct mu.id_ms_user ")
			.append("from am_msuser mu ")
			.append("join ms_vendor_registered_user vru on mu.id_ms_user = vru.id_ms_user ")
			.append("where vru.signer_registered_email = :email ")
			.append("and vru.id_ms_vendor != :idMsVendor ");
		
		BigInteger idMsUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsUser) {
			return null;
		}
		
		return idMsUser.longValue();
	}

	@Override
	public Long getIdMsUserRegisteredInOtherVendorByPhone(String phone, MsVendor vendor) {
		Map<String, Object> params = new HashMap<>();
		params.put("hashedPhone", MssTool.getHashedString(phone));
		params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select distinct mu.id_ms_user ")
			.append("from am_msuser mu ")
			.append("join ms_vendor_registered_user vru on mu.id_ms_user = vru.id_ms_user ")
			.append("where vru.hashed_signer_registered_phone = :hashedPhone ")
			.append("and vru.id_ms_vendor != :idMsVendor ");
		
		BigInteger idMsUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsUser) {
			return null;
		}
		
		return idMsUser.longValue();
	}

	@Override
	public Long getIdMsUserRegisteredInOtherVendorByNik(String nik, MsVendor vendor) {
		
		Map<String, Object> params = new HashMap<>();
		params.put("hashedIdNo", MssTool.getHashedString(nik));
		params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select distinct mu.id_ms_user ")
			.append("from am_msuser mu ")
			.append("join ms_vendor_registered_user vru on mu.id_ms_user = vru.id_ms_user ")
			.append("where mu.hashed_id_no = :hashedIdNo ")
			.append("and vru.id_ms_vendor != :idMsVendor ");
		
		BigInteger idMsUser = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idMsUser) {
			return null;
		}
		
		return idMsUser.longValue();
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<Map<String, Object>> getDistinctIdMsusersByEmailNewTrx(String email) {
		Map<String, Object> params = new HashMap<>();
		params.put(GlobalVal.CONST_EMAIL, StringUtils.isBlank(email) ? StringUtils.EMPTY : StringUtils.upperCase(email));
		
		StringBuilder query = new StringBuilder()
				.append("select distinct id_ms_user from ms_vendor_registered_user ")
				.append("where signer_registered_email = :email ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}
	
	@Override
	public AmMsuser getDeletedUserManagementByLoginId(String loginId) {
		if (StringUtils.isBlank(loginId)){
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from AmMsuser u "
				+ "join fetch u.msOffice mo "
				+ "join fetch mo.msTenant mt "
				+ "where u.loginId = :loginId", 
						new Object[][] {{AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId)}});
	}
	
}
