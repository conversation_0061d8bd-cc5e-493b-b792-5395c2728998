package com.adins.esign.webservices.embed.api;

import java.io.IOException;
import java.text.ParseException;

import com.adins.esign.webservices.model.BulkSignDocumentEmbedRequest;
import com.adins.esign.webservices.model.BulkSignDocumentResponse;
import com.adins.esign.webservices.model.CheckDocumentSendStatusEmbedRequest;
import com.adins.esign.webservices.model.CheckDocumentSendStatusResponse;
import com.adins.esign.webservices.model.DocumentExcelReportResponse;
import com.adins.esign.webservices.model.ListInquiryDocumentEmbedRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentResponse;
import com.adins.esign.webservices.model.ResendSignNotificationResponse;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.embed.CancelDigitalSignEmbedRequest;
import com.adins.esign.webservices.model.embed.CancelDigitalSignEmbedResponse;
import com.adins.esign.webservices.model.embed.CheckDocumentBeforeSigningEmbedRequest;
import com.adins.esign.webservices.model.embed.CheckDocumentBeforeSigningEmbedResponse;
import com.adins.esign.webservices.model.embed.ConfirmSignEmbedRequest;
import com.adins.esign.webservices.model.embed.ConfirmSignEmbedResponse;
import com.adins.esign.webservices.model.embed.ResendSignNotificationEmbedRequest;
import com.adins.esign.webservices.model.embed.SignDocumentEmbedV2Request;
import com.adins.esign.webservices.model.embed.SignDocumentEmbedV2Response;
import com.adins.esign.webservices.model.embed.StartStampingMeteraiEmbedRequest;
import com.adins.esign.webservices.model.embed.StartStampingMeteraiEmbedResponse;
import com.adins.esign.webservices.model.embed.ViewSignerEmbedRequest;
import com.adins.esign.webservices.model.embed.ViewSignerEmbedResponse;

public interface DocumentEmbedService {
	ListInquiryDocumentResponse getListInquiryDocumentEmbed(ListInquiryDocumentEmbedRequest request);
	CheckDocumentBeforeSigningEmbedResponse checkDocumentBeforeSigningEmbed(CheckDocumentBeforeSigningEmbedRequest request);
	ViewDocumentResponse getDocumentFileEmbed(ViewDocumentRequest request);
	CancelDigitalSignEmbedResponse cancelDigitanSign(CancelDigitalSignEmbedRequest request);
	ConfirmSignEmbedResponse confirmSign(ConfirmSignEmbedRequest request);
	SignDocumentEmbedV2Response signDocument(SignDocumentEmbedV2Request request) throws IOException, ParseException;
	DocumentExcelReportResponse exportDocumentReportEmbed(ListInquiryDocumentEmbedRequest request);
	ResendSignNotificationResponse resendSignNotification(ResendSignNotificationEmbedRequest request);
	ViewSignerEmbedResponse viewSignerEmbed(ViewSignerEmbedRequest request);
	CheckDocumentSendStatusResponse checkDocumentSendStatusEmbed(CheckDocumentSendStatusEmbedRequest request);
	BulkSignDocumentResponse bulkSignDocumentEmbed(BulkSignDocumentEmbedRequest request) throws IOException, ParseException;
	StartStampingMeteraiEmbedResponse startStampingMeteraiEmbed(StartStampingMeteraiEmbedRequest request)
			throws IOException, ParseException;
}
