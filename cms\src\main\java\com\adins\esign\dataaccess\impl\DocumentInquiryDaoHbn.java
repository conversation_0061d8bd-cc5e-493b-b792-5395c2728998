package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.DocumentInquiryDao;
import com.adins.esign.dataaccess.factory.api.DaoFactory;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class DocumentInquiryDaoHbn extends BaseDaoHbn implements DocumentInquiryDao {

	private static String start= "start";
	private static String end = "end";
    @Autowired UserValidatorLogic userValidatorLogic;
    @Autowired DaoFactory daofactory;

    @Override
	@Transactional(readOnly = true)
    public List<Map<String, Object>> getListInquiryDocument(String[] paramsInquiry, String callerId, String queryType) {
        Map<String, Object> paramsQuery = new HashMap<>();
		
		// Params 9: start row, Params 10: end row
		if (!"0".equals(paramsInquiry[9]) && !"0".equals(paramsInquiry[10])) {
			paramsQuery.put(start, Integer.valueOf(paramsInquiry[9]));
			paramsQuery.put(end, Integer.valueOf(paramsInquiry[10]));
		}
		
		/*
			Query inquiry document untuk non-customer membutuhkan waktu > 3 menit untuk selesai. 
			Query inquiry document untuk inbox aman
			Link untuk contoh:
			/embed/inquiry?msg=uCJVQ%2FPggDtKKd%2Beo20mBAiTYhlhURGxbHk3uDX9mYbe1t4a7v2Iitgk2PzUt2y4loFd1VhjX2gbs0u4sZ6URdvDRILCmBTQjw%2B%2Fm9C%2Bum0%3D&isMonitoring=1&isHO=1
			
			Sementara join ke ms_doc_template di-comment, nama dokumen di-hardcode.
		*/
		StringBuilder conditionalParam = this.buildConditionalParam(paramsInquiry, paramsQuery, callerId, queryType);
		StringBuilder query = new StringBuilder();
		query		
			.append(" with ddsCTE as ")
			.append(" ( ")
			.append("    SELECT  distinct on (dd.id_document_d) dd.id_document_d, dds.id_document_d_sign, dds.id_ms_user,  dds.sign_date, ")
			.append("           dt.doc_template_name, lovdocType.description AS doc_type_name , usercust.full_name, ")
			.append("           dh.ref_number, lovDocType.description,  coalesce(dt.doc_template_name, dd.document_name) AS doc_name, ")
			.append("           usercust.full_name as customer_name, dd.request_date, dd.completed_date, dd.document_id, ")
			.append("           office.office_name,  lovSignStat.description  AS sign_stat, ")
			.append("           dd.total_materai, dd.total_stamping, dh.automatic_stamping_after_sign AS \"status_otomatis_stamping\", ")
			.append("           dh.proses_materai AS \"status_proses_materai\" , ms_vendor.vendor_code AS \"vendor_code\", dd.signing_process AS \"signing_process\", ")
			.append("           dh.is_active AS \"is_active\", ")
			.append("           case when dh.automatic_stamping_after_sign != '1' and dh.proses_materai = 0 and dd.total_sign = dd.total_signed then '1' ")
			.append("           else '0' end AS \"can_start_stamp\", ")
			.append("           dd.archive_document_status AS \"archive_document_status\", ")
			.append("           case when dd.priority_sequence is null then 32767 ")
			.append("               when dd.priority_sequence = 0 then 32767 ")
			.append("               else dd.priority_sequence end as priority_sequence ")
			.append("    FROM tr_document_d dd ")
			.append("    JOIN tr_document_d_sign dds on dds.id_document_d = dd.id_document_d AND dds.id_ms_user = :idMsUser ")
			.append("    JOIN   ms_vendor  ON dd.id_ms_vendor = ms_vendor.id_ms_vendor")
			.append("    JOIN   tr_document_h dh   ON   dh.id_document_h = dd.id_document_h AND dh.is_active = '1' ")
			.append("    JOIN   ms_tenant mt       ON   dh.id_ms_tenant = mt.id_ms_tenant AND mt.tenant_code = '" + paramsInquiry[0] + "' ")
			.append("    LEFT JOIN   am_msuser usercust ON   dh.id_msuser_customer = usercust.id_ms_user ")
			.append("    LEFT JOIN   ms_doc_template dt ON   dd.id_ms_doc_template = dt.id_doc_template ")
			.append("    JOIN   ms_lov lovDocType  ON   dh.lov_doc_type = lovDocType.id_lov AND lovDocType.lov_group = 'DOC_TYPE' ")
			.append("    JOIN   ms_lov lovSignStat ON   dd.lov_sign_status = lovSignStat.id_lov AND lovSignStat.lov_group = 'LOV_SIGN_STATUS' ")
			.append("    JOIN   ms_office office   ON   office.id_ms_office = dh.id_ms_office ");

		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			query.append("JOIN ms_lov at on at.id_lov = dds.lov_autosign ");
		}

		query.append(" WHERE 1 = 1 ");
		
		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			query.
				append(" and dds.sign_date is null ")
				.append(" and at.code = '" + GlobalVal.CODE_LOV_MANUALSIGN + "' ");
		}
		
		query
			.append(conditionalParam)
			.append(" ) ")
				.append(" SELECT * FROM ( ")
					.append(" SELECT DISTINCT ON (id_document_d) ROW_NUMBER() OVER(ORDER BY date_trunc('minute', request_date), ref_number, priority_sequence) AS row, ")
					.append(" ref_number, doc_type_name, doc_name, customer_name, ")
					.append(" TO_CHAR(request_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') AS request_date, ")
					.append(" TO_CHAR(completed_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') AS completed_date, document_id, ")
					.append(" CONCAT(countSigner - COALESCE(countSignNotDone,'0'), ' / ', COALESCE(countSigner,'0')) AS total_signed, ")
					.append(" ddsCTE.id_document_d,  office_name, sign_stat, ")
					.append(" concat(COALESCE(total_stamping, '0'), '/', COALESCE(total_materai, '0')), ")
					.append(" status_otomatis_stamping, ")
					.append(" status_proses_materai, vendor_code, signing_process, is_active, can_start_stamp, archive_document_status ")
					.append(" FROM ddsCTE ")
					.append(" JOIN LATERAL ( ")
						.append(" SELECT dds.id_document_d, COUNT (DISTINCT dds.id_ms_user) AS countSigner, ")
							.append(" COUNT (DISTINCT dds.id_ms_user) FILTER (WHERE dds.sign_date IS NULL) AS countSignNotDone ")
						.append(" FROM tr_document_d_sign dds ")
						.append(" WHERE dds.id_document_d = ddsCTE.id_document_d ")
						.append(" GROUP BY dds.id_document_d ")
					.append(" ) countSign ON TRUE ")
				.append(" ) as a ");
			
			// Params 9: start row, Params 10: end row
			if (!"0".equals(paramsInquiry[9]) && !"0".equals(paramsInquiry[10])) {
				query.append(" where a.row between :start and :end ")
					.append("order by row ");
			}
		
		return this.managerDAO.selectAllNativeString(query.toString(), paramsQuery);
    }

    @Override
	@Transactional(readOnly = true)
    public long countListInquiryDocument(String[] paramsInquiry, String callerId, String queryType) {

        Map<String, Object> paramsQuery = new HashMap<>();
		
		StringBuilder conditionalParam = this.buildConditionalParam(paramsInquiry, paramsQuery, callerId, queryType);
		StringBuilder query = new StringBuilder();
		query		
			.append(" with ddsCTE as ")
			.append(" ( ")
			.append("    SELECT  distinct on (dd.id_document_d) dd.id_document_d,  dds.id_document_d_sign, dds.id_ms_user,  dds.sign_date, ")
			.append("           dt.doc_template_name, lovdocType.description AS doc_type_name , usercust.full_name, ")
			.append("           dh.ref_number, lovDocType.description,  dt.doc_template_name AS doc_name, ")
			.append("           usercust.full_name as customer_name, dd.request_date, dd.completed_date, dd.document_id, ")
			.append("           office.office_name,  lovSignStat.description  AS sign_stat, ")
			.append("           dd.archive_document_status ")
			.append("    FROM   tr_document_d dd ")
			.append("    JOIN tr_document_d_sign dds on dds.id_document_d = dd.id_document_d AND dds.id_ms_user = :idMsUser ")
			.append("    JOIN   ms_vendor  ON dd.id_ms_vendor = ms_vendor.id_ms_vendor")
			.append("    JOIN   tr_document_h dh   ON   dh.id_document_h = dd.id_document_h AND dh.is_active = '1' ")
			.append("    JOIN   ms_tenant mt       ON   dh.id_ms_tenant = mt.id_ms_tenant AND mt.tenant_code = '" + paramsInquiry[0] + "' ")
			.append("    LEFT JOIN   am_msuser usercust ON   dh.id_msuser_customer = usercust.id_ms_user ")
			.append("    LEFT JOIN   ms_doc_template dt ON   dd.id_ms_doc_template = dt.id_doc_template ")
			.append("    JOIN   ms_lov lovDocType  ON   dh.lov_doc_type = lovDocType.id_lov AND lovDocType.lov_group = 'DOC_TYPE' ")
			.append("    JOIN   ms_lov lovSignStat ON   dd.lov_sign_status = lovSignStat.id_lov AND lovSignStat.lov_group = 'LOV_SIGN_STATUS' ")
			.append("    JOIN   ms_office office   ON   office.id_ms_office = dh.id_ms_office ");

		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			query.append("JOIN ms_lov at on at.id_lov = dds.lov_autosign ");
		}

		query.append(" WHERE 1 = 1 ");
		
		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			query.
				append(" and dds.sign_date is null ")
				.append(" and at.code = '" + GlobalVal.CODE_LOV_MANUALSIGN + "' ");
		}
		
		query
			.append(conditionalParam)
			.append(" ) ")
			.append(" SELECT COUNT (DISTINCT (ddsCTE.id_document_d))")
			.append(" FROM ddsCTE ");
		
		return ((BigInteger) this.managerDAO.selectOneNativeString(query.toString(), paramsQuery)).longValue();
    }

    private StringBuilder buildConditionalParam(String[] paramsInquiry, Map<String, Object> paramsQuery, String callerId, String queryType) {
		StringBuilder conditionalParam = new StringBuilder();
		AuditContext audit = new AuditContext (callerId);
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(callerId, true, audit);
		
		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			// Status need Sign
			conditionalParam.append(" and dd.lov_sign_status = :idLov ");
			paramsQuery.put(MsLov.ID_LOV_HBM, daofactory.getLovDao().getMsLovByCode(GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN).getIdLov());
				
			// id user
			paramsQuery.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
			
		} else if (GlobalVal.INQUIRY_TYPE_LIST_CUST.equals(queryType)) {
			// Status
			if (StringUtils.isNotBlank(paramsInquiry[3])) {
				conditionalParam.append(" and dd.lov_sign_status = :idLov ");
				paramsQuery.put(MsLov.ID_LOV_HBM, Long.valueOf(paramsInquiry[3]));
			}
			
			// id user
			paramsQuery.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
			
		} else if (GlobalVal.INQUIRY_TYPE_LIST_BM_MF.equals(queryType)) {
			// id user
			paramsQuery.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
						
			// Customer name
			if (StringUtils.isNotBlank(paramsInquiry[2])) {
				conditionalParam.append(" and usercust.full_name like :fullName ");
				paramsQuery.put(AmMsuser.FULLNAME_HBM, "%" + StringUtils.upperCase(paramsInquiry[2]) + "%");
			}
							
			// Status
			if (StringUtils.isNotBlank(paramsInquiry[3])) {
				conditionalParam.append(" and dd.lov_sign_status = :idLov ");
				paramsQuery.put(MsLov.ID_LOV_HBM, Long.valueOf(paramsInquiry[3]));
			}
						
			// Ref number
			if (StringUtils.isNotBlank(paramsInquiry[4])) {
				conditionalParam.append(" and upper(dh.ref_number) like :refNumber ");
				paramsQuery.put(TrDocumentH.REF_NUMBER_HBM, "%" + StringUtils.upperCase(paramsInquiry[4]) + "%");
			} else {
				// Request start date
				if (StringUtils.isNotBlank(paramsInquiry[5])) {
					conditionalParam.append(" and dd.request_date >= :requestDateStart ");
					paramsQuery.put("requestDateStart", MssTool.formatStringToDate(paramsInquiry[5] + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				} else {
					conditionalParam.append(" and dd.request_date >= date_trunc('month', now()) ");
				}
							
				// Request end date 
				if (StringUtils.isNotBlank(paramsInquiry[6])) {
					conditionalParam.append(" and dd.request_date <= :requestDateEnd ");
					paramsQuery.put("requestDateEnd", MssTool.formatStringToDate(paramsInquiry[6] + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				} else {
					conditionalParam.append("and dd.request_date <= now() ");
				}
							
				// Complete start date
				if (StringUtils.isNotBlank(paramsInquiry[7])) {
					conditionalParam.append(" and dd.completed_date >= :completeDateStart ");
					paramsQuery.put("completeDateStart", MssTool.formatStringToDate(paramsInquiry[7] + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				}
							
				// Complete end date
				if (StringUtils.isNotBlank(paramsInquiry[8])) {
					conditionalParam.append(" and dd.completed_date <= :completeDateEnd ");
					paramsQuery.put("completeDateEnd", MssTool.formatStringToDate(paramsInquiry[8] + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				}
			}
						
			// Doc type
			if (StringUtils.isNotBlank(paramsInquiry[11])) {
				conditionalParam.append(" and dh.lov_doc_type = :docType ");
				paramsQuery.put("docType", Long.valueOf(paramsInquiry[11]));
			}
			
		}

		return conditionalParam;
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> getListInquiryDocumentMonitoring(String[] paramsInquiry, boolean filterActiveStatus) {
		Map<String, Object> paramsQuery = new HashMap<>();
		
		// Params 9: start row, Params 10: end row
		if (!"0".equals(paramsInquiry[9]) && !"0".equals(paramsInquiry[10])) {
			paramsQuery.put(start, Integer.valueOf(paramsInquiry[9]));
			paramsQuery.put(end, Integer.valueOf(paramsInquiry[10]));
		}

		StringBuilder conditionalParam = this.buildConditionalParamMonitoring(paramsInquiry, paramsQuery, filterActiveStatus);
		StringBuilder query = new StringBuilder();
		query		
			.append(" with ddsCTE as ")
			.append(" ( ")
			.append("    SELECT distinct(dd.id_document_d), ")
			.append("           dt.doc_template_name, lovdocType.description AS doc_type_name , usercust.full_name, ")
			.append("           dh.ref_number, lovDocType.description,  case when dd.id_ms_doc_template is not null then dt.doc_template_name else dd.document_name end AS doc_name, ")
			.append("           usercust.full_name as customer_name, dd.request_date, dd.completed_date, dd.document_id, ")
			.append("           office.office_name, region.region_name,  lovSignStat.description  AS sign_stat, ")
			.append("           dd.total_materai, dd.total_stamping, dh.automatic_stamping_after_sign AS \"status_otomatis_stamping\", ")
			.append("           dh.proses_materai AS \"status_proses_materai\" , ms_vendor.vendor_code AS \"vendor_code\", dd.signing_process AS \"signing_process\", ")
			.append("           dh.is_active AS \"is_active\", ")
			.append("           case when dh.automatic_stamping_after_sign != '1' and dh.proses_materai = 0 and dd.total_sign = dd.total_signed then '1' ")
			.append("           else '0' end AS \"can_start_stamp\", ")
			.append("           dd.archive_document_status AS \"archive_document_status\", ")
			.append("           case when dd.priority_sequence is null then 32767 ")
			.append("               when dd.priority_sequence = 0 then 32767 ")
			.append("               else dd.priority_sequence end as priority_sequence ")
			.append("    FROM tr_document_d dd ")
			.append("    JOIN tr_document_d_sign dds on dds.id_document_d = dd.id_document_d ")
			.append("    JOIN   ms_vendor  ON dd.id_ms_vendor = ms_vendor.id_ms_vendor")
			.append("    JOIN   tr_document_h dh   ON   dh.id_document_h = dd.id_document_h ")
			.append("    JOIN   ms_tenant mt       ON   dh.id_ms_tenant = mt.id_ms_tenant AND mt.tenant_code = '" + paramsInquiry[0] + "' ")
			.append("    LEFT JOIN   am_msuser usercust ON   dh.id_msuser_customer = usercust.id_ms_user ")
			.append("    LEFT JOIN   ms_doc_template dt ON   dd.id_ms_doc_template = dt.id_doc_template ")
			.append("    JOIN   ms_lov lovDocType  ON   dh.lov_doc_type = lovDocType.id_lov ")
			.append("    JOIN   ms_lov lovSignStat ON   dd.lov_sign_status = lovSignStat.id_lov ")
			.append("    JOIN   ms_office office   ON   office.id_ms_office = dh.id_ms_office ")
			.append("    LEFT JOIN ms_region region ON  region.id_ms_region = office.id_ms_region ")
			.append(" WHERE 1 = 1 ")
			.append(conditionalParam)
			.append(" ) ")
				.append(" SELECT * FROM ( ")
					.append(" SELECT DISTINCT ON (id_document_d) ROW_NUMBER() OVER(ORDER BY date_trunc('minute', request_date), ref_number, priority_sequence) AS row, ")
					.append(" ref_number, doc_type_name, doc_name, customer_name, ")
					.append(" TO_CHAR(request_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') AS request_date, ")
					.append(" TO_CHAR(completed_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') AS completed_date, document_id, ")
					.append(" CONCAT(countSigner - COALESCE(countSignNotDone,'0'), ' / ', COALESCE(countSigner,'0')) AS total_signed, ")
					.append(" ddsCTE.id_document_d,  office_name, region_name, sign_stat, ")
					.append(" concat(COALESCE(total_stamping, '0'), '/', COALESCE(total_materai, '0')), ")
					.append(" status_otomatis_stamping, ")
					.append(" status_proses_materai, vendor_code, signing_process, is_active, can_start_stamp, archive_document_status ")
					.append(" FROM ddsCTE ")
					.append(" JOIN LATERAL ( ")
						.append(" SELECT dds.id_document_d, COUNT (DISTINCT dds.id_ms_user) AS countSigner, ")
							.append(" COUNT (DISTINCT dds.id_ms_user) FILTER (WHERE dds.sign_date IS NULL) AS countSignNotDone ")
						.append(" FROM tr_document_d_sign dds ")
						.append(" WHERE dds.id_document_d = ddsCTE.id_document_d ")
						.append(" GROUP BY dds.id_document_d ")
					.append(" ) countSign ON TRUE ")
				.append(" ) as a ");
			
			// Params 9: start row, Params 10: end row
			if (!"0".equals(paramsInquiry[9]) && !"0".equals(paramsInquiry[10])) {
				query.append(" where a.row between :start and :end ");
			}
			query.append("order by row ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), paramsQuery);
	}

	@Override
	@Transactional(readOnly = true)
	public long countListInquiryDocumentMonitoring(String[] paramsInquiry, boolean filterActiveStatus) {
		Map<String, Object> paramsQuery = new HashMap<>();
		
		StringBuilder conditionalParam = this.buildConditionalParamMonitoring(paramsInquiry, paramsQuery, filterActiveStatus);
		StringBuilder query = new StringBuilder();
		query		
			.append(" with ddsCTE as ")
			.append(" ( ")
			.append("    SELECT distinct(dd.id_document_d), ")
			.append("           dt.doc_template_name, lovdocType.description AS doc_type_name , usercust.full_name, ")
			.append("           dh.ref_number, lovDocType.description,  dt.doc_template_name AS doc_name, ")
			.append("           usercust.full_name as customer_name, dd.request_date, dd.completed_date, dd.document_id, ")
			.append("           office.office_name, region.region_name,  lovSignStat.description  AS sign_stat, ")
			.append("           dd.archive_document_status ")
			.append("    FROM   tr_document_d dd ")
			.append("    JOIN tr_document_d_sign dds on dds.id_document_d = dd.id_document_d ")
			.append("    JOIN   tr_document_h dh   ON   dh.id_document_h = dd.id_document_h ")
			.append("    JOIN   ms_tenant mt       ON   dh.id_ms_tenant = mt.id_ms_tenant AND mt.tenant_code = '" + paramsInquiry[0] + "' ")
			.append("    LEFT JOIN   am_msuser usercust ON   dh.id_msuser_customer = usercust.id_ms_user ")
			.append("    LEFT JOIN   ms_doc_template dt ON   dd.id_ms_doc_template = dt.id_doc_template ")
			.append("    JOIN   ms_lov lovDocType  ON   dh.lov_doc_type = lovDocType.id_lov ")
			.append("    JOIN   ms_lov lovSignStat ON   dd.lov_sign_status = lovSignStat.id_lov ")
			.append("    JOIN   ms_office office   ON   office.id_ms_office = dh.id_ms_office ")
			.append("    LEFT JOIN ms_region region ON  region.id_ms_region = office.id_ms_region ")
			.append(" WHERE 1 = 1 ")
			.append(conditionalParam)
			.append(" ) ")
			.append(" SELECT COUNT (DISTINCT (ddsCTE.id_document_d))")
			.append(" FROM ddsCTE ");
		
		return ((BigInteger) this.managerDAO.selectOneNativeString(query.toString(), paramsQuery)).longValue();
	}

	private StringBuilder buildConditionalParamMonitoring(String[] paramsInquiry, Map<String, Object> paramsQuery, boolean filterActiveStatus) {
		
		StringBuilder conditionalParam = new StringBuilder();

		// Office code
		if (StringUtils.isNotBlank(paramsInquiry[1])) {
			conditionalParam.append(" and office.office_code = :officeCode ");
			paramsQuery.put(MsOffice.OFFICE_CODE_HBM,  StringUtils.upperCase(paramsInquiry[1]));
		}
		
		// Customer name
		if (StringUtils.isNotBlank(paramsInquiry[2])) {
			conditionalParam.append(" and usercust.full_name like :fullName ");
			paramsQuery.put(AmMsuser.FULLNAME_HBM, "%" + StringUtils.upperCase(paramsInquiry[2]) + "%");
		}
		
		// Status
		if (StringUtils.isNotBlank(paramsInquiry[3])) {
			conditionalParam.append(" and dd.lov_sign_status = :idLov ");
			paramsQuery.put(MsLov.ID_LOV_HBM, Long.valueOf(paramsInquiry[3]));
		}
		
		// Ref number
		if (StringUtils.isNotBlank(paramsInquiry[4])) {
			conditionalParam.append(" and upper(dh.ref_number) like :refNumber ");
			paramsQuery.put(TrDocumentH.REF_NUMBER_HBM, "%" + StringUtils.upperCase(paramsInquiry[4]) + "%");
		} else {
			// Request start date
			// Filter request date hanya dijalankan jika filter ref number kosong
			if (StringUtils.isNotBlank(paramsInquiry[5])) {
				conditionalParam.append(" and dd.request_date >= :requestDateStart ");
				paramsQuery.put("requestDateStart", MssTool.formatStringToDate(paramsInquiry[5] + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
			} else {
				conditionalParam.append(" and dd.request_date >= date_trunc('month', now()) ");
			}
			
			// Request end date 
			if (StringUtils.isNotBlank(paramsInquiry[6])) {
				conditionalParam.append(" and dd.request_date <= :requestDateEnd ");
				paramsQuery.put("requestDateEnd", MssTool.formatStringToDate(paramsInquiry[6] + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
			} else {
				conditionalParam.append("and dd.request_date <= now() ");
			}
			
			// Complete start date
			if (StringUtils.isNotBlank(paramsInquiry[7])) {
				conditionalParam.append(" and dd.completed_date >= :completeDateStart ");
				paramsQuery.put("completeDateStart",  MssTool.formatStringToDate(paramsInquiry[7] + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
			}
			
			// Complete end date
			if (StringUtils.isNotBlank(paramsInquiry[8])) {
				conditionalParam.append(" and dd.completed_date <= :completeDateEnd ");
				paramsQuery.put("completeDateEnd", MssTool.formatStringToDate(paramsInquiry[8] + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
			}
		}
		
		// Doc type
		if (StringUtils.isNotBlank(paramsInquiry[11])) {
			conditionalParam.append(" and dh.lov_doc_type = :docType ");
			paramsQuery.put("docType", Long.valueOf(paramsInquiry[11]));
		}
		
		// Region code
		if (StringUtils.isNotBlank(paramsInquiry[12])) {
			conditionalParam.append(" and region.region_code = :regionCode ");
			paramsQuery.put("regionCode", StringUtils.upperCase(paramsInquiry[12]));
		}
		
		// Proses Materai
		if (StringUtils.isNotBlank(paramsInquiry[13])) {
			if (paramsInquiry[13].equals(GlobalVal.HASIL_STAMPING_NOT_STARTED)) {
				conditionalParam.append(" and dh.proses_materai = 0 ");
			} else if (paramsInquiry[13].equals(GlobalVal.HASIL_STAMPING_FAILED)) {
				conditionalParam.append(" and dh.proses_materai in (1,51,321,521,61,71)");
			} else if (paramsInquiry[13].equals(GlobalVal.HASIL_STAMPING_SUCCESS)) {
				conditionalParam.append(" and dh.proses_materai in (3,53,323,523,63,73)");
			} else if (paramsInquiry[13].equals(GlobalVal.HASIL_STAMPING_IN_PROGRESS)) {
				conditionalParam.append(" and dh.proses_materai in (2,52,322,522,5,55,325,525,64,65,62,72,74,75)");
			}
		}

		// Is Active
		if (filterActiveStatus) {
			if (StringUtils.isNotBlank(paramsInquiry[14])) {
				conditionalParam.append(" and dh.is_active = :isActive ");
				paramsQuery.put("isActive", paramsInquiry[14]);
			}
		} else {
			conditionalParam.append(" and dh.is_active = '1' ");
		}

		return conditionalParam;
	}

	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> getListInquiryDocumentInboxEmbed(String[] paramsInquiry, String callerId, boolean isReadOffice) {
		Map<String, Object> paramsQuery = new HashMap<>();
		
		// Params 9: start row, Params 10: end row
		if (!"0".equals(paramsInquiry[9]) && !"0".equals(paramsInquiry[10])) {
			paramsQuery.put(start, Integer.valueOf(paramsInquiry[9]));
			paramsQuery.put(end, Integer.valueOf(paramsInquiry[10]));
		}
		
		StringBuilder conditionalParam = this.buildConditionalParamInboxEmbed(paramsInquiry, paramsQuery, callerId, isReadOffice);
		StringBuilder query = new StringBuilder();
		query		
			.append(" with ddsCTE as ")
			.append(" ( ")
			.append("    SELECT  distinct on ( dd.id_document_d)  dd.id_document_d, dds.id_document_d_sign, dds.id_ms_user, dds.sign_date, ")
			.append("           dt.doc_template_name, lovdocType.description AS doc_type_name , usercust.full_name, ")
			.append("           dh.ref_number, lovDocType.description,  case when dd.id_ms_doc_template is not null then dt.doc_template_name else dd.document_name end AS doc_name, ")
			.append("           usercust.full_name as customer_name, dd.request_date, dd.completed_date, dd.document_id, ")
			.append("           office.office_name,  lovSignStat.description  AS sign_stat, ")
			.append("           dd.total_materai, dd.total_stamping, dh.automatic_stamping_after_sign AS \"status_otomatis_stamping\", ")
			.append("           dh.proses_materai AS \"status_proses_materai\" , ms_vendor.vendor_code AS \"vendor_code\", dd.signing_process AS \"signing_process\", ")
			.append("           dh.is_active AS \"is_active\", ")
			.append("           case when dh.automatic_stamping_after_sign != '1' and dh.proses_materai = 0 and dd.total_sign = dd.total_signed then '1' ")
			.append("           else '0' end AS \"can_start_stamp\", ")
			.append("           dd.archive_document_status AS \"archive_document_status\", ")
			.append("           case when dd.priority_sequence is null then 32767 ") 
			.append("               when dd.priority_sequence = 0 then 32767 ")
			.append("               else dd.priority_sequence end as priority_sequence ")
			.append("    FROM tr_document_d dd ")
			.append("    JOIN tr_document_d_sign dds on dds.id_document_d = dd.id_document_d AND dds.id_ms_user = :idMsUser ")
			.append("    JOIN   ms_vendor  ON dd.id_ms_vendor = ms_vendor.id_ms_vendor")
			.append("    JOIN   tr_document_h dh   ON   dh.id_document_h = dd.id_document_h ")
			.append("    JOIN   ms_tenant mt       ON   dh.id_ms_tenant = mt.id_ms_tenant AND mt.tenant_code = '" + paramsInquiry[0] + "' ")
			.append("    LEFT JOIN   am_msuser usercust ON   dh.id_msuser_customer = usercust.id_ms_user ")
			.append("    LEFT JOIN   ms_doc_template dt ON   dd.id_ms_doc_template = dt.id_doc_template ")
			.append("    JOIN   ms_lov lovDocType  ON   dh.lov_doc_type = lovDocType.id_lov ")
			.append("    JOIN   ms_lov lovSignStat ON   dd.lov_sign_status = lovSignStat.id_lov ")
			.append("    JOIN   ms_office office   ON   office.id_ms_office = dh.id_ms_office ")
			.append("JOIN ms_lov at on at.id_lov = dds.lov_autosign ")
			.append(" WHERE 1 = 1 ")
			.append(" and dds.sign_date is null ")
			.append(" and at.code = '" + GlobalVal.CODE_LOV_MANUALSIGN + "' ")
			.append(conditionalParam)
			.append(" ) ")
				.append(" SELECT * FROM ( ")
					.append(" SELECT DISTINCT ON (id_document_d) ROW_NUMBER() OVER(ORDER BY date_trunc('minute', request_date), ref_number, priority_sequence) AS row, ")
					.append(" ref_number, doc_type_name, doc_name, customer_name, ")
					.append(" TO_CHAR(request_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') AS request_date, ")
					.append(" TO_CHAR(completed_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') AS completed_date, document_id, ")
					.append(" CONCAT(countSigner - COALESCE(countSignNotDone,'0'), ' / ', COALESCE(countSigner,'0')) AS total_signed, ")
					.append(" ddsCTE.id_document_d,  office_name, sign_stat, ")
					.append(" concat(COALESCE(total_stamping, '0'), '/', COALESCE(total_materai, '0')), ")
					.append(" status_otomatis_stamping, ")
					.append(" status_proses_materai, vendor_code, signing_process, is_active, can_start_stamp, archive_document_status ")
					.append(" FROM ddsCTE ")
					.append(" JOIN LATERAL ( ")
						.append(" SELECT dds.id_document_d, COUNT (DISTINCT dds.id_ms_user) AS countSigner, ")
							.append(" COUNT (DISTINCT dds.id_ms_user) FILTER (WHERE dds.sign_date IS NULL) AS countSignNotDone ")
						.append(" FROM tr_document_d_sign dds ")
						.append(" WHERE dds.id_document_d = ddsCTE.id_document_d ")
						.append(" GROUP BY dds.id_document_d ")
					.append(" ) countSign ON TRUE ")
				.append(" ) as a ");
			
			// Params 9: start row, Params 10: end row
			if (!"0".equals(paramsInquiry[9]) && !"0".equals(paramsInquiry[10])) {
				query.append(" where a.row between :start and :end ")
					.append("order by row ");
			}
		
		return this.managerDAO.selectAllNativeString(query.toString(), paramsQuery);
	}

	@Override
	@Transactional(readOnly = true)
	public long countListInquiryDocumentMonitoringInboxEmbed(String[] paramsInquiry, String callerId, boolean isReadOffice) {
		Map<String, Object> paramsQuery = new HashMap<>();
		
		StringBuilder conditionalParam = this.buildConditionalParamInboxEmbed(paramsInquiry, paramsQuery, callerId, isReadOffice);
		StringBuilder query = new StringBuilder();
		query		
			.append(" with ddsCTE as ")
			.append(" ( ")
			.append("    SELECT dds.id_document_d_sign, dds.id_ms_user, dd.id_document_d , dds.sign_date, ")
			.append("           dt.doc_template_name, lovdocType.description AS doc_type_name , usercust.full_name, ")
			.append("           dh.ref_number, lovDocType.description,  dt.doc_template_name AS doc_name, ")
			.append("           usercust.full_name as customer_name, dd.request_date, dd.completed_date, dd.document_id, ")
			.append("           office.office_name, region.region_name,  lovSignStat.description  AS sign_stat, ")
			.append("           dd.archive_document_status ")
			.append("    FROM   tr_document_d dd ")
			.append("    JOIN tr_document_d_sign dds on dds.id_document_d = dd.id_document_d AND dds.id_ms_user = :idMsUser ")
			.append("    JOIN   tr_document_h dh   ON   dh.id_document_h = dd.id_document_h ")
			.append("    JOIN   ms_tenant mt       ON   dh.id_ms_tenant = mt.id_ms_tenant AND mt.tenant_code = '" + paramsInquiry[0] + "' ")
			.append("    LEFT JOIN   am_msuser usercust ON   dh.id_msuser_customer = usercust.id_ms_user ")
			.append("    LEFT JOIN   ms_doc_template dt ON   dd.id_ms_doc_template = dt.id_doc_template ")
			.append("    JOIN   ms_lov lovDocType  ON   dh.lov_doc_type = lovDocType.id_lov ")
			.append("    JOIN   ms_lov lovSignStat ON   dd.lov_sign_status = lovSignStat.id_lov ")
			.append("    JOIN   ms_office office   ON   office.id_ms_office = dh.id_ms_office ")
			.append("    LEFT JOIN ms_region region ON  region.id_ms_region = office.id_ms_region ")
			.append("JOIN ms_lov at on at.id_lov = dds.lov_autosign ")
			.append(" WHERE 1 = 1 ")
			.append(" and dds.sign_date is null ")
			.append(" and at.code = '" + GlobalVal.CODE_LOV_MANUALSIGN + "' ")
			.append(conditionalParam)
			.append(" ) ")
			.append(" SELECT COUNT (DISTINCT (ddsCTE.id_document_d))")
			.append(" FROM ddsCTE ");
		
		return ((BigInteger) this.managerDAO.selectOneNativeString(query.toString(), paramsQuery)).longValue();
	}

	private StringBuilder buildConditionalParamInboxEmbed(String[] paramsInquiry, Map<String, Object> paramsQuery, String callerId, boolean isReadOffice) {
		StringBuilder conditionalParam = new StringBuilder();
		AuditContext audit = new AuditContext (callerId);
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(callerId, true, audit);
		
		// Tenant code
		if (StringUtils.isNotBlank(paramsInquiry[0])) {
			conditionalParam.append(" and mt.tenant_code = :tenantCode ");
			paramsQuery.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(paramsInquiry[0]));
		}
		
		// Status need Sign
		conditionalParam.append(" and dd.lov_sign_status = :idLov ");
		paramsQuery.put(MsLov.ID_LOV_HBM, daofactory.getLovDao().getMsLovByCode(GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN).getIdLov());
		
		// id user
		paramsQuery.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		
		// Office code
		if (StringUtils.isNotBlank(paramsInquiry[1]) && isReadOffice) {
			conditionalParam.append(" and office.office_code = :officeCode ");
			paramsQuery.put(MsOffice.OFFICE_CODE_HBM,  StringUtils.upperCase(paramsInquiry[1]));
		}
		
		// Is Active
		conditionalParam.append(" and dh.is_active = '1' ");
		
		return conditionalParam;
	}
    
}
