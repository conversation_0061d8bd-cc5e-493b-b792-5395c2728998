package com.adins.am.businesslogic.impl;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.service.security.ApiKeyAuthenticator;
import com.adins.framework.service.security.ClientDetails;
import com.adins.framework.service.security.InvalidApiKeyException;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class GenericApiKeyAuthenticatorTest {
	@Autowired private ApiKeyAuthenticator apiKeyAuthenticator;
	
	private String apiKey;
	private String remoteAddress;
	
	@BeforeEach
	public void setUp() {
		apiKey = "JUNIT@12345";
		remoteAddress = "JUNIT Remote Address";
	}
	
	@SuppressWarnings("deprecation")
	@Test
	void authenticateTest() {
		ClientDetails clientDetails = apiKeyAuthenticator.authenticate(apiKey);
		assertNotNull(clientDetails);
	}
	
	@SuppressWarnings("deprecation")
	@Test
	void authenticateInvalidTest() {
		assertThrows(InvalidApiKeyException.class, () -> apiKeyAuthenticator.authenticate("JUNIT_NULL"));
	}
	
	@Test
	void authenticateWithRemoteAddressTest() {
		ClientDetails clientDetails = apiKeyAuthenticator.authenticate(apiKey, remoteAddress);
		assertNotNull(clientDetails);
	}
	
	@Test
	void authenticateWithRemoteAddressInvalidTest() {
		assertThrows(InvalidApiKeyException.class, () -> apiKeyAuthenticator.authenticate("JUNIT_NULL", remoteAddress));
	}
}
