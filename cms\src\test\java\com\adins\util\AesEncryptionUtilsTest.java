package com.adins.util;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

import org.bouncycastle.crypto.InvalidCipherTextException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

@Disabled("temp disabled")
class AesEncryptionUtilsTest {
	
	/*
		msg dari digisign url encoded, perlu didecode dengan UrlDecoder
		msg dari digisign mengandung line separator jadi perlu manual di hapus
	*/
	private String encryptedHtml = "0pbE1wAmozAohhkoojVVpzFxPDk5BH9qnMGO3uqTGJNJ0umvrhiETGF%2FQH2EtKy0QIcL9j3DTj%2By%0AyCQbqej1zQwbLrLneG56B04Vc1jbqtzSh6R3FW4894%2FmFIMibWMzZCoAm%2FiPPNZ2b7vH6qDhOMK1%0AHlzvrZACIRjrmPPyuo2sj5cKtyLxKii%2F6jeTcWDr4NGgGdW0PmvYxAKb1xjOw4fucLAEEsZm4cnp%0A412tjx8%3D";
	private String encrypted = "0pbE1wAmozAohhkoojVVpzFxPDk5BH9qnMGO3uqTGJNJ0umvrhiETGF/QH2EtKy0QIcL9j3DTj+yyCQbqej1zQwbLrLneG56B04Vc1jbqtzSh6R3FW4894/mFIMibWMzZCoAm/iPPNZ2b7vH6qDhOMK1HlzvrZACIRjrmPPyuo2sj5cKtyLxKii/6jeTcWDr4NGgGdW0PmvYxAKb1xjOw4fucLAEEsZm4cnp412tjx8=";
	private String key = "K9bu6w89raldAWTj";
	
	private String activationMsg = "B9zrGI4dF0PAPWDsaTBgrbxWNX%2Fi6qnJhfi%2BrVl9DXuPtMkchM6WIS3b4HRIdWWiFtZAhJqdntS0%0AOAp4L8s7lcH5ER2gVls%2BdYmLnyRDIC3acfsW8ka2MBeBcXb0JpvvC6o8Z%2Fs2%2BCkicCG%2BTPpYBdp%2B%0A6ON36F0b7CE4EfTDXsw%3D";
	private String activationMsgPlain = "{\"result\":\"00\",\"notif\":\"Proses Aktivasi Berhasil\",\"email_user\":\"<EMAIL>\",\"nik\":\"3275094801950033\"}";
	
	@Test
	void decryptTest() throws InvalidCipherTextException {
		String key="K9bu6w89raldAWTj";
		String cipher = "ef16/h5YQyIKVb1amaIm6g==";
		String plain = AesEncryptionUtils.decrypt(cipher, key);
		
		Assertions.assertEquals("12345", plain);
	}
	
	@Test
	void encryptTest() throws Exception {
		String msg="12345";
		String key="K9bu6w89raldAWTj";
		String cipherText = AesEncryptionUtils.encrypt(msg, key);
		
		Assertions.assertEquals("ef16/h5YQyIKVb1amaIm6g==", cipherText);
	}
	
	@Test
	void testDigiMsgCallbackSign() throws UnsupportedEncodingException {
		String decoded = URLDecoder.decode(encryptedHtml, StandardCharsets.UTF_8.toString());		
		decoded = decoded.replace("\n", "").replace("\r", "");
		Assertions.assertEquals(encrypted, decoded);
		
		String plain = AesEncryptionUtils.decrypt(decoded, key);		
	}
	
	@Test
	void testDigiMsgCallbackAct() throws UnsupportedEncodingException {
		String decoded = URLDecoder.decode(activationMsg, StandardCharsets.UTF_8.toString());		
		decoded = decoded.replace("\n", "").replace("\r", "");		
		
		String plain = AesEncryptionUtils.decrypt(decoded, "RBazsYSDTuShYbUG");
		Assertions.assertEquals(activationMsgPlain, plain);
	}
}
