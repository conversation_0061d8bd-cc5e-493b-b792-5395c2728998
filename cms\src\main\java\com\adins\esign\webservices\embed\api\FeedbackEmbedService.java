package com.adins.esign.webservices.embed.api;

import org.dom4j.DocumentException;
import org.springframework.context.NoSuchMessageException;

import com.adins.esign.webservices.model.FeedbackResponse;
import com.adins.esign.webservices.model.embed.FeedbackHybridEmbedRequest;

public interface FeedbackEmbedService {
	FeedbackResponse saveFeedback(FeedbackHybridEmbedRequest request) throws NoSuchMessageException, DocumentException;
}
