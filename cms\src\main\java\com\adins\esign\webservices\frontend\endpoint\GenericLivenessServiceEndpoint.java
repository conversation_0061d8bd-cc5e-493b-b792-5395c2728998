package com.adins.esign.webservices.frontend.endpoint;

import java.io.IOException;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.model.custom.FaceMatchLivenessRequest;
import com.adins.esign.webservices.frontend.api.LivenessService;
import com.adins.esign.webservices.model.FaceVerifyRequest;
import com.adins.esign.webservices.model.FaceVerifyResponse;
import com.adins.esign.webservices.model.LivenessEmbedRequest;
import com.adins.esign.webservices.model.LivenessRequest;
import com.adins.esign.webservices.model.LivenessResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import io.swagger.annotations.Api;


@Component
@Path("/liveness")
@Api(value = "LivenessServices")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericLivenessServiceEndpoint implements LivenessService {
	
	@Autowired private TenantLogic tenantLogic;
	
	@Override
	@POST
	@Path("/s/tenantLiveness")
	public LivenessResponse getTenantLiveness(LivenessRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getTenantLiveness(request, audit);
	}
	
	@Override
	@POST
	@Path("/tenantLivenessEmbed")
	public LivenessResponse getTenantLivenessEmbed(LivenessEmbedRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getTenantLivenessEmbed(request, audit);
	}

	@Override
	@POST
	@Path("/livenessCheck")
	public FaceVerifyResponse getLivenessCheck(FaceVerifyRequest request) throws IOException {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getLivenessCheck(request, audit);
	}

	
	@Override
	@POST
	@Path("/livenessCheckNodeflux")
	public FaceVerifyResponse getLivenessCheckNodeflux(FaceMatchLivenessRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return tenantLogic.getLivenessCheckNodeflux(request, audit);
	}
}
