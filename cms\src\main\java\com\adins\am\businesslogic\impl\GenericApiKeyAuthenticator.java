package com.adins.am.businesslogic.impl;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.model.MsTenant;
import com.adins.framework.service.security.ApiKeyAuthenticator;
import com.adins.framework.service.security.ClientDetails;
import com.adins.framework.service.security.InvalidApiKeyException;

@Component
@Transactional(readOnly=true)
public class GenericApiKeyAuthenticator extends BaseLogic implements ApiKeyAuthenticator {

	private static final Logger LOG = LoggerFactory.getLogger(GenericApiKeyAuthenticator.class);
    
	@Autowired TenantLogic tenantLogic;    
	
	@Override
    public ClientDetails authenticate(String apiKey) {
		return this.authenticate(api<PERSON><PERSON>, null);
    }

    /**
     * @throws InvalidApiKeyException Client akan mendapat Http error 500:
     * <ol>
     *     <li>. Tidak ada client code dalam X-Api-Key (apikey@clientCode)
     *     <li>. Invalid client code
     *     <li>. Invalid (notmatch, case sensitive) apikey
     * </ol>
     */
    @Override
    public ClientDetails authenticate(String apiKey, String remoteAddress) {
        if (!StringUtils.contains(apiKey, '@')) {
                throw new InvalidApiKeyException("Api Key does not contains tenant code. example: apikey@tenantCode");
        }
        
        String[] apiKeyStripped = StringUtils.splitPreserveAllTokens(apiKey, '@');
        String key = apiKeyStripped[0];
        String tenantCode = StringUtils.upperCase(apiKeyStripped[1]);
        
        MsTenant tenant = tenantLogic.getTenantByCode(tenantCode, null);
        
        if (null == tenant ) {
	        LOG.error("Tenant code '{}' not found", tenantCode);
	        throw new InvalidApiKeyException(messageSource.getMessage("businesslogic.vendor.invalidtenantcode", null, this.retrieveDefaultLocale()));
        }
            
        if (!StringUtils.equals(key, tenant.getApiKey())) {
                throw new InvalidApiKeyException(messageSource.getMessage("businesslogic.tenant.incorrectapikey", null, this.retrieveDefaultLocale()));
        }
        
        ClientDetails client = new ClientDetails();
        client.setClientId(tenant.getTenantCode());
        
        return client;
    }
}