package com.adins.esign.businesslogic.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsprovince;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.DistrictLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.model.custom.DistrictBean;
import com.adins.esign.model.custom.DistrictExternalBean;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.validatorlogic.api.InvitationLinkValidatorLogic;
import com.adins.esign.webservices.model.GetDistrictByInvitationRequest;
import com.adins.esign.webservices.model.GetDistrictEmbedRequest;
import com.adins.esign.webservices.model.GetDistrictRequest;
import com.adins.esign.webservices.model.GetDistrictResponse;
import com.adins.esign.webservices.model.external.GetDistrictListExternalRequest;
import com.adins.esign.webservices.model.external.GetDistrictListExternalResponse;
import com.adins.exceptions.LocationException;
import com.adins.exceptions.LocationException.ReasonLocation;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericDistrictLogic extends BaseLogic implements DistrictLogic {
	
	@Autowired private CommonLogic commonLogic;
	
	@Autowired private InvitationLinkValidatorLogic invLinkValidatorLogic;
	@Autowired private TenantLogic tenantLogic;

	@Override
	public GetDistrictResponse getDistrictList(GetDistrictRequest request, AuditContext audit) {
		GetDistrictResponse response = new GetDistrictResponse();
		
		String trim = StringUtils.trim(request.getDistrictName());
		String toUpper = StringUtils.upperCase(trim);
		List<DistrictBean> listDistrict = daoFactory.getDistrictDao().
				getListDistrict(toUpper,request.getProvinceId());

		
		response.setListDistrict(listDistrict);
		
		if(listDistrict.isEmpty()) {
			List<DistrictBean> listDistrictByProvince = daoFactory.getDistrictDao().getListDistrict(null ,request.getProvinceId());
			response.setListDistrict(listDistrictByProvince);
			return response;
		}
		
		return response;
	}

	@Override
	public GetDistrictResponse getDistrictListEmbed(GetDistrictEmbedRequest request, AuditContext audit) {
		commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);

		GetDistrictRequest districtListRequest = new GetDistrictRequest();
		districtListRequest.setDistrictName(request.getDistrictName());
		districtListRequest.setProvinceId(request.getProvinceId());
		
		return this.getDistrictList(districtListRequest, audit);
	}

	@Override
	public GetDistrictResponse getDistrictListByInvitation(GetDistrictByInvitationRequest request, AuditContext audit) {

		String invCode = invLinkValidatorLogic.decryptInvitationCode(request.getMsg(), audit);
		invLinkValidatorLogic.validateGetInvitationLink(invCode, audit);
		
		GetDistrictRequest districtListRequest = new GetDistrictRequest();
		districtListRequest.setDistrictName(request.getDistrictName());
		districtListRequest.setProvinceId(request.getProvinceId());
		
		
		return this.getDistrictList(districtListRequest, audit);
	}

	@Override
	public GetDistrictListExternalResponse getDistrictListExternal(GetDistrictListExternalRequest request,
			String apiKey, AuditContext audit) {
		
		tenantLogic.getTenantFromXApiKey(apiKey, audit);
		
		if (StringUtils.isBlank(request.getProvinceName())) {
			throw new LocationException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new Object[] { "Province Name" }, this.retrieveLocaleAudit(audit)), ReasonLocation.PROVINCE_NAME_EMPTY);
		}
		
		AmMsprovince province = daoFactory.getProvinceDao().getProvinceByName(request.getProvinceName());
		
		if (province == null) {
			throw new LocationException(getMessage("businesslogic.province.provincewithnamenotfound",
					new Object[] { request.getProvinceName() }, audit), ReasonLocation.PROVINCE_NOT_FOUND);
		}
		
		List<DistrictExternalBean> districtList = daoFactory.getDistrictDao().getDistrictExternalList(province.getIdMsprovince());
		
		GetDistrictListExternalResponse response = new GetDistrictListExternalResponse();
		response.setDistrictList(districtList);
		return response;
	}

}
