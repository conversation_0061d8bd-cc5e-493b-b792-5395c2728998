package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.esign.model.TrSignLinkRequest;

public interface SignLinkRequestDao {
	void insertSignLinkRequest(TrSignLinkRequest signLinkRequest);
	void updateSignLinkRequest(TrSignLinkRequest signLinkRequest);
	void deleteSignLinkRequest(TrSignLinkRequest signLinkRequest);
	
	List<TrSignLinkRequest> getSignLinkRequestsBySignLinkCode(String code);
}
