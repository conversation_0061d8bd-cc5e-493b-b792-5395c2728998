package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class VendorException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonVendor {
		VENDOR_TYPE_CODE_EMPTY,
		VENDOR_TYPE_CODE_INVALID,
		TENANT_CODE_EMPTY,
		TENANT_NOT_FOUND,
		USER_NOT_FOUND,
		USER_TENANT_NOT_FOUND,
		VENDOR_CODE_INVALID,
		VENDOR_NOT_FOUND,
		VENDOR_CAN_NOT_RESEND_ACT_LINK,
		VENDOR_CODE_EMPTY,
		VENDOR_NAME_EMPTY,
		BALANCE_VENDOR_NOT_FOUND,
		VENDOR_STATUS_INVALID,
		VENDOR_PSRE_PAYMENT_SIGN_TYPE,
		INVALID_VENDOR_OFTENANT,
		VENDOR_NOT_SUPPORT_HASH_SIGN,
		VENDOR_NOT_SUPPORT_DOWNLOAD_CERT,
		VENDOR_NOT_EXIST_OR_ACTIVE_IN_TENANT,
		PSRE_LIST_EMPTY,
		VENDOR_NOT_OPERATING
	}

	private final ReasonVendor reason;
	
	public VendorException(ReasonVendor reason) {
		this.reason = reason;
	}
	
	public VendorException(String message, ReasonVendor reason) {
		super(message);
		this.reason = reason;
	}
	
	public VendorException(Throwable ex, ReasonVendor reason) {
		super(ex);
		this.reason = reason;
	}
	
	public VendorException(String message, Throwable ex, ReasonVendor reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	public ReasonVendor getReason() {
		return reason;
	}
	
	@Override
	public int getErrorCode() {
		if (null != this.reason) {
			switch (reason) {
			case VENDOR_TYPE_CODE_INVALID:
				return StatusCode.VENDOR_TYPE_CODE_INVALID;
			case VENDOR_TYPE_CODE_EMPTY:
				return StatusCode.VENDOR_TYPE_CODE_EMPTY;
			case TENANT_CODE_EMPTY:
				return StatusCode.VENDOR_TENANT_CODE_EMPTY;
			case TENANT_NOT_FOUND:
				return StatusCode.VENDOR_TENANT_NOT_EXIST;
			case USER_NOT_FOUND:
				return StatusCode.VENDOR_USER_NOT_FOUND;
			case USER_TENANT_NOT_FOUND:
				return StatusCode.VENDOR_USE_TENANT_NOT_FOUND;
			case VENDOR_CODE_INVALID:
				return StatusCode.VENDOR_CODE_INVALID;
			case VENDOR_NOT_FOUND:
				return StatusCode.VENDOR_NOT_FOUND;
			case VENDOR_CAN_NOT_RESEND_ACT_LINK:
				return StatusCode.VENDOR_CAN_NOT_RESEND_ACT_LINK;
			case VENDOR_CODE_EMPTY:
				return StatusCode.VENDOR_TYPE_CODE_EMPTY;
			case VENDOR_NAME_EMPTY:
				return StatusCode.VENDOR_NAME_EMPTY;
			case VENDOR_STATUS_INVALID:
				return StatusCode.VENDOR_STATUS_INVALID;
			case BALANCE_VENDOR_NOT_FOUND:
				return StatusCode.VENDOR_BALANCE_VENDOR_NOT_FOUND;
			case VENDOR_PSRE_PAYMENT_SIGN_TYPE : 
				return StatusCode.VENDOR_PSRE_PAYEMENT_SIGN_TYPE_INVALID;
			case INVALID_VENDOR_OFTENANT : 
				return StatusCode.INVALID_VENDOR_OFTENANT;
			case VENDOR_NOT_SUPPORT_HASH_SIGN:
				return StatusCode.VENDOR_NOT_SUPPORT_HASH_SIGN;
			case VENDOR_NOT_SUPPORT_DOWNLOAD_CERT:
				return StatusCode.VENDOR_NOT_SUPPORT_DOWNLOAD_CERT;
			case VENDOR_NOT_EXIST_OR_ACTIVE_IN_TENANT:
				return StatusCode.VENDOR_NOT_EXIST_OR_ACTIVE_IN_TENANT;
			case PSRE_LIST_EMPTY:
				return StatusCode.PSRE_LIST_EMPTY;
			case VENDOR_NOT_OPERATING:
				return StatusCode.VENDOR_NOT_OPERATING;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}
	
	
}
