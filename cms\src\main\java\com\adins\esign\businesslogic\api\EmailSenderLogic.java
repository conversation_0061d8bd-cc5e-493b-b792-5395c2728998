package com.adins.esign.businesslogic.api;

import javax.mail.MessagingException;

import com.adins.esign.model.custom.EmailAttachmentBean;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;

public interface EmailSenderLogic {
	/**
	 * 
	 * emailInfo.from  nullable
	 * emailInfo.replyTo nullable
	 * emailInfo.to nullable
	 * emailInfo.cc nullable
	 * emailInfo.bcc nullable
	 * emailInfo.subject
	 * emailInfo.bodyMessage body message always set to text/html content type.
	 * @param attachments nullable
	 * 
	 * @throws IllegalArgumentException if destination (to, cc and bcc) is empty 
	 * @throws EmailingException on
	 * 	<li>cannot createMimeMultiparts message
	 * 	<li>wrong formatted adddress
	 */
	void sendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments) throws MessagingException;
	
	void sendEmailWithoutAsync(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments) throws MessagingException;
	
	void sendEmail(EmailInformationBean emailInfo, EmailAttachmentBean[] attachments, SigningProcessAuditTrailBean auditTrail) throws MessagingException;
}
