package com.adins.esign.validatorlogic.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.ValidationException;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericCommonValidatorLogic extends BaseLogic implements CommonValidatorLogic {

		 
	@Override
	public void validateNotNull(Object object, String objectName, AuditContext audit) {
		String message = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {objectName}, audit);
		validateNotNull(object, message, StatusCode.MANDATORY_PARAMETER);
	}

	@Override
	public void validateNotNull(Object object, String statusMessage, int errorCode) {
		if (object == null) {
			throw new ValidationException(statusMessage, errorCode);
		}
		
		if (object instanceof String && StringUtils.isBlank((String) object)) {
			throw new ValidationException(statusMessage, errorCode);
		}
	}

	@Override
	public void validateMustNull(Object object, String statusMessage, int errorCode) {
		if (object != null) {
			throw new ValidationException(statusMessage, errorCode);
		}
		
	}
	
	@Override
	public void validateMustEquals(Object object , Object objectCompare, String statusMessage, int errorCode ) {
		if (!objectCompare.equals(object)) {
			throw new ValidationException(statusMessage, errorCode);
		}
		
	}
	
	@Override
	public void validateMustNotEquals(Object object , Object objectCompare, String statusMessage, int errorCode ) {
		if (objectCompare.equals(object)) {
			throw new ValidationException(statusMessage, errorCode);
		}
		
	}

	@Override
	public void validateListNotEmpty(List list, String statusMessage, int errorCode) {
		if (list.isEmpty()) {
			throw new ValidationException(statusMessage, errorCode);
		}
	}
}
