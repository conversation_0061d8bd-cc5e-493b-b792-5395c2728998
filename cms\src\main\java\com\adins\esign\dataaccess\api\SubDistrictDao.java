package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.am.model.AmMssubdistrict;
import com.adins.esign.model.custom.SubDistrictBean;
import com.adins.esign.model.custom.SubDistrictExternalBean;

public interface SubDistrictDao {

	List<SubDistrictBean> getListSubDistrict(String subDistrictName, Long districtId);	
	AmMssubdistrict getSubdistrictById(Long idSubdistrict, Long idMsDistrict);	
	AmMssubdistrict getSubdistrict(Long idMssubdistrict);

	void insertAmMssubdistrict(AmMssubdistrict subdistrict);
	void updateAmMssubdistrict(AmMssubdistrict subdistrict);
	List<SubDistrictExternalBean> getSubDistrictExternalList(Long idMsDistrict);
}
