package com.adins.esign.dataaccess.impl;

import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.PeruriDocTypeDao;
import com.adins.esign.model.MsPeruriDocType;

@Transactional
@Component
public class PeruriDocTypeDoaHbn extends BaseDaoHbn implements PeruriDocTypeDao {

	@Override
	public MsPeruriDocType getPeruriDocTypeByCode(String code) {
		return managerDAO.selectOne(MsPeruriDocType.class, new Object[][] {{MsPeruriDocType.DOC_CODE_HBM, code}});
	}

	@Override
	public MsPeruriDocType getPeruriDocTypeByDocId(String id) {
		return managerDAO.selectOne(MsPeruriDocType.class, new Object[][] {{MsPeruriDocType.PERURI_DOC_ID_HBM, id}});
	}

	@Override
	public MsPeruriDocType getPeruriDocTypeByIdMsPeruriDocType(Long id) {
		return managerDAO.selectOne(MsPeruriDocType.class, new Object[][] {{Restrictions.eq(MsPeruriDocType.ID_PERURI_DOC_TYPE, id)}});
	}

}
