package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class LocationException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonLocation {
		PROVINCE_NOT_FOUND,
		DISTRICT_NOT_FOUND,
		SUBDISTRICT_NOT_FOUND,
		INVALID_PROVINCE,
		INVALID_DISTRICT,
		PROVINCE_NAME_EMPTY,
		DISTRICT_NAME_EMPTY
	}
	
	private final ReasonLocation reason;
	
	public LocationException(String message, ReasonLocation reason) {
		super(message);
		this.reason = reason;
	}

	@Override
	public int getErrorCode() {
		if (null != this.reason) {
			switch (reason) {
			case PROVINCE_NOT_FOUND:
				return StatusCode.PROVINCE_NOT_FOUND;
			case DISTRICT_NOT_FOUND:
				return StatusCode.DISTRICT_NOT_FOUND;
			case SUBDISTRICT_NOT_FOUND:
				return StatusCode.SUBDISTRICT_NOT_FOUND;
			case PROVINCE_NAME_EMPTY:
				return StatusCode.PROVINCE_NAME_EMPTY;
			case DISTRICT_NAME_EMPTY:
				return StatusCode.DISTRICT_NAME_EMPTY;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}
}
