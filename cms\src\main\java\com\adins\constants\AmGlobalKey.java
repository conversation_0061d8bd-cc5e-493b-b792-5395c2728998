package com.adins.constants;

public class AmGlobal<PERSON>ey {

	protected AmGlobalKey() {
		throw new IllegalStateException("AmGlobalKey class shall not be instantiated! Class=" + this.getClass().getName());
	}

	/**
	 * Key for for storing exception error code, specified in as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_EXCEPTION = "excp";

	/**
	 * Key for for storing last action result, specified in as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_LAST_RESULT = "lastResult";

	/**
	 * Key for for storing default previous parameter for redirection, as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_REDIR_PARAM_MAP = "redir.params";

	/**
	 * Key for for storing default target for redirection, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_REDIR_TARGET = "redir.target";

	/**
	 *  Key for for storing state, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_STATE = "state";

	/**
	 *  Key for for storing mode, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_ACTION_NAME = "actionName";

	/**
	 *  Key for for storing mode, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_MODE = "mode";

	/**
	 * Property key for <i>other</i> context factory.
	 */
	public static final String CONTEXT_FACTORY = "context.factory";

	/**
	 * Property key for <i>other</i> Context URL provider.
	 */
	public static final String CONTEXT_URL = "context.url";

	/**
	 * Property key for initializing group of Context Class locations.
	 */
	public static final String CONTEXT_INITIALIZER_CLASS = "initializerClassLocation";
	
	/**
	 * Property key for database (datasource) JNDI name.
	 */
	public static final String JNDI_DATASOURCE = "jndi.datasource";

	/**
	 * Key for for storing result list object, used in map for storing query result.
	 */
	public static final String MAP_RESULT_LIST = "resultList";

	/**
	 * Key for for storing size of result list object, used in map for storing query result.
	 */
	public static final String MAP_RESULT_SIZE = "resultSize";

	/**
	 * Key for for storing result list object with that version, used in map for storing query result.
	 */
	public static final String MAP_RESULT_VERSION = "resultVersion";

	/**
	 * String that is used for storing action message.
	 */
	public static final String SESSION_ACTION_MESSAGE = "SESSION_ACTION_MESSAGE";

	/**
	 *Key for storing current parameter information in application session.
	 */
	public static final String SESSION_CURR_PARAMETER = "SESSION_CURR_PARAMETER";

	/**
	 *Key for storing login information in application session.
	 */
	public static final String SESSION_LOGIN = "SESSION_LOGIN";

	/**
	 * String that is used for storing model version for concurrency checking.
	 */
	public static final String SESSION_MODEL_VERSION = "SESSION_MODEL_VERSION";

	/**
	 *Key for storing push parameter information in application session.
	 */
	public static final String SESSION_PUSH_PARAMETER = "SESSION_PUSH_PARAMETER";

	/**
	 *Key for storing temporary parameter information in application session.
	 */
	public static final String SESSION_TEMP_PARAMETER = "SESSION_TEMP_PARAMETER";

	/**
	 *Key for specifying location of file application properties when using in system environment.
	 */
	public static final String SYSTEM_ENVIRONMENT = "application.test.properties";

	public static final String AUDIT_KEY_LOCALE = "locale";
	
	public static final String GENERALSETTING_MAX_ROW 			= "MAX_ROW_SIZE";
    public static final String GENERALSETTING_DEFAULT_LOCALE 	= "AM_DEFAULT_LOCALE";
	public static final String GENERALSETTING_MAX_FAIL_COUNT 	= "AM_MAX_FAIL_COUNT";
	public static final String GENERALSETTING_SYSTEM_TIMEOUT 	= "AM_SYSTEM_TIMEOUT";
    public static final String GENERALSETTING_PASSWORD_EXPIRED 	= "AM_PWD_EXPIRED";
	public static final String GENERALSETTING_LAST_PASSWORD 	= "LAST_PASSWORD";
	public static final String GENERALSETTING_INTERFACE_TYPE 	= "INTERFACE_TYPE"; 
	public static final String GENERALSETTING_AES_KEY 			= "AES_KEY";
	public static final String GENERALSETTING_MAX_DATE_RANGE	= "RPT_MAX_DATE_RANGE";
	public static final String GENERALSETTING_PASSWORD_FORMAT	= "AM_PASSWORD_FORMAT";
	public static final String GENERALSETTING_URI_REDIR_SIGN	= "URI_REDIR_SIGN";
	public static final String GENERALSETTING_REMINDER_TOPUP_FQ = "REMINDER_TOPUP_FREQ";
	public static final String GENERALSETTING_MAX_EMAIL_KEEP_ALIVE	= "MAX_EMAIL_KEEP_ALIVE";
	public static final String GENERALSETTING_EMAIL_PIC_ESIGN	=  "EMAIL_PIC_ESIGN";
	public static final String GENERALSETTING_URL_LIVENESS_KTP	= "URL_LIVENESS_KTP";
	public static final String GENERALSETTING_URL_LIVENESS_SLF	= "URL_LIVENESS_SLF";
	public static final String GENERALSETTING_URL_LIVENESS_FACE_COMPARE	= "URL_LIVENESS_FACE_COMPARE";
	public static final String GENERALSETTING_URL_RESET_PASS_DIGI	= "URL_RESET_PASS_DIGI";
	public static final String GENERALSETTING_URL_VERIF_AKUN_TKNAJ	= "URL_VERIF_AKUN_TKNAJ";
	public static final String GENERALSETTING_STATUS_SERVICE_LIVENESS = "STATUS_SERVICE_LIVENESS";
	public static final String GENERALSETTING_OTP_RESET_PWD_DAILY 		= "OTP_RESET_PWD_DAILY";
	public static final String GENERALSETTING_NILAI_METERAI_LUNAS 		= "NILAI_METERAI_LUNAS";
	public static final String GENERALSETTING_FR_DUKCAPIL_MULTIPLIER 	= "FR_DUKCAPIL_MULTIPLIER";
	public static final String GENERALSETTING_REGEX_PHONE_FORMAT 	= "AM_PHONE_FORMAT";
	public static final String GENERALSETTING_REGEX_EMAIL_FORMAT	= "AM_EMAIL_FORMAT";
	public static final String GENERALSETTING_USER_UPDATE_CODE 		= "USER_UPDATE_CODE";
	public static final String GENERALSETTING_PERURI_CONN_TIMEOUT 	= "PERURI_CONN_TIMEOUT";
	public static final String GENERALSETTING_PERURI_READ_TIMEOUT 	= "PERURI_READ_TIMEOUT";
	public static final String GENERALSETTING_PAYMENT_RECEIPT_PERURI_CONN_TIMEOUT 	= "PAYMENT_RECEIPT_PERURI_CONN_TIMEOUT";
	public static final String GENERALSETTING_PAYMENT_RECEIPT_PERURI_READ_TIMEOUT 	= "PAYMENT_RECEIPT_PERURI_READ_TIMEOUT";
	public static final String GENERALSETTING_ATTACH_SDT_ERROR_EMAIL_RECEIVER 	= "ATTACH_SDT_ERROR_EMAIL_RECEIVER";
	public static final String GENERALSETTING_ATTACH_SDT_MAX_REPEATED_ERROR_COUNT 	= "ATTACH_SDT_MAX_REPEATED_ERROR_COUNT";
	public static final String GENERALSETTING_GSCODE_TOKEN_CALLBACK_TEKENAJA 		= "TOKEN_CALLBACK_TEKENAJA";
	public static final String GENERALSETTING_REGEX_NAME_CLEANSING 					= "REGEX_NAME_CLEANSING";
	public static final String GENERALSETTING_TEKENAJA_SKIP_PHONE_VAL				= "TKNAJ_SKIP_PHONE_VAL";
	public static final String GENERALSETTING_CERTIFICATE_CHECK_SKIP_VAL			= "CERTIFICATE_CHECK_SKIP_VAL";
	public static final String GENERALSETTING_DIGI_SEND_DOC_CONN_TIMEOUT = "DIGI_SEND_DOC_CONN_TIMEOUT";
	public static final String GENERALSETTING_DIGI_SEND_DOC_READ_TIMEOUT = "DIGI_SEND_DOC_READ_TIMEOUT";
	public static final String GENERALSETTING_SEND_SMS_GENINV 	= "SEND_SMS_GENINV";
	public static final String GENERALSETTING_SEND_WA_GENINV 	= "SEND_WA_GENINV";
	public static final String GENERALSETTING_SEND_SMS_SENDDOC 	= "SEND_SMS_SENDDOC";
	public static final String GENERALSETTING_SEND_WA_SENDDOC 	= "SEND_WA_SENDDOC";
	public static final String GENERALSETTING_SEND_SMS_FORPASS 	= "SEND_SMS_FORPASS";
	public static final String GENERALSETTING_SEND_WA_FORPASS   = "SEND_WA_FORPASS";
	public static final String GENERALSETTING_PAJAKKU_USERNAME	= "PERURI_ACCOUNT_NAME";
	public static final String GENERALSETTING_PAJAKKU_PASSWORD	= "PERURI_ACCOUNT_PASSWORD";
	public static final String GENERALSETTING_IMAP_CONN_TIMEOUT		= "IMAP_CONN_TIMEOUT";
	public static final String GENERALSETTING_IMAP_READ_TIMEOUT		= "IMAP_READ_TIMEOUT";
	public static final String GENERALSETTING_IMAP_WRITE_TIMEOUT	= "IMAP_WRITE_TIMEOUT";
	public static final String GENERALSETTING_NUMBER_OF_STM_SDT_ATTEMPTS	= "NUMBER_OF_STM_SDT_ATTEMPTS";
	public static final String GENERALSETTING_TOKEN_DATE_DIFF_SMS	= "TOKEN_DATE_DIFF_SMS";
	public static final String GENERALSETTING_MAX_DATA_FOREACH_DELETE_UNUSED_DOC	= "MAX_DATA_FOREACH_DELETE_UNUSED_DOC";
	public static final String GENERALSETTING_DMS_USERNAME		= "DMS_USERNAME";
	public static final String GENERALSETTING_SUBMIT_RECON_MAXDATE	= "SUBMIT_REKON_DATE_LIMIT";
	public static final String GENERALSETTING_STAMPED_DOC_CHECK_ATTEMPTS = "STAMPED_DOC_CHECK_ATTEMPTS";
	public static final String GENERALSETTING_STAMPED_DOC_CHECK_DELAY = "STAMPED_DOC_CHECK_DELAY";
	public static final String GENERALSETTING_DELETE_DOC_ON_PREM_LIMIT = "DELETE_DOC_ON_PREM_LIMIT"; //100
	public static final String GENERALSETTING_DELETE_DOC_ON_PREM_DATETOKEEP = "DELETE_DOC_ON_PREM_DATETOKEEP"; //10
	public static final String GENERALSETTING_DEFAULT_VENDOR_INSERT_USER_MANAGEMENT = "VENDOR_DEFAULT_INSERT_USER_MANAGEMENT";
	public static final String GENERALSETTING_OTP_ACTIVATION_USER_DAILY = "OTP_ACTIVATION_USER_DAILY";
	public static final String GENERALSETTING_API_KEY_VIDA_CVV = "API_KEY_VIDA_CVV";
	public static final String GENERALSETTING_VIDA_POA_ITERATION = "VIDA_POA_ITERATION";
	public static final String GENERALSETTING_API_VIDA_POA_WAIT_TIME = "VIDA_POA_WAIT_TIME";
	public static final String GENERALSETTING_LIVENESS_FACECOMPARE_USER_DAILY_LIMIT = "LIVENESS_FACECOMPARE_USER_DAILY_LIMIT";
	public static final String GENERALSETTING_LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT = "LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT";
	public static final String GENERALSETTING_LIVENESS_FACECOMPARE_API_KEY = "LIVENESS_FACECOMPARE_API_KEY";
	public static final String GENERALSETTING_SEND_SMS_OTP_USER = "SEND_SMS_OTP_USER";
	public static final String GENERALSETTING_SEND_WA_OTP_USER = "SEND_WA_OTP_USER";
	public static final String GENERALSETTING_LIVENESS_FACECOMPARE_TENANT_CODE = "LIVENESS_FACECOMPARE_TENANT_CODE";
	public static final String GENERALSETTING_VIDA_REGISTER_MAX_ATTEMPTS = "VIDA_REGISTER_MAX_ATTEMPTS";
	public static final String GENERALSETTING_DEFAULT_VIDA_REGISTER_MAX_ATTEMPTS = "DEFAULT_VIDA_REGISTER_MAX_ATTEMPTS";
	public static final String GENERALSETTING_DEFAULT_REDIRECT_AFTER_SIGNING_COUNTDOWN_TIME = "TIME_TO_REDIRECT_AFTER_SIGNING";
	public static final String GENERALSETTING_DEFAULT_DURATION_RESEND_OTP = "DURATION_RESEND_OTP";
	public static final String GENERALSETTING_TIME_TO_REDIRECT_AFTER_ACTIVATION = "TIME_TO_REDIRECT_AFTER_ACTIVATION";
	public static final String GENERALSETTING_VIDA_CERTIFICATE_EXPIRE_TIME = "VIDA_CERTIFICATE_EXPIRE_TIME";
	public static final String GENERALSETTING_PRIVY_CERTIFICATE_EXPIRE_TIME = "PRIVY_CERTIFICATE_EXPIRE_TIME";
	public static final String GENERALSETTING_BYPASS_LIVENESS_FACE_COMPARE_CHECK = "BYPASS_LIVENESS_FACE_COMPARE_CHECK";
	public static final String GENERALSETTING_ALLOW_CONCURENT_SESSION = "ALLOW_CONCURENT_SESSION";
	public static final String GS_DEFAULT_TIME_ENCRYPT_DOCUMENT = "DEFAULT_TIME_ENCRYPT_DOCUMENT";

}
