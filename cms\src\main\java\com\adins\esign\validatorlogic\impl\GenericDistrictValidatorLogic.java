package com.adins.esign.validatorlogic.impl;

import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsdistrict;
import com.adins.am.model.AmMsprovince;
import com.adins.esign.validatorlogic.api.DistrictValidatorLogic;
import com.adins.exceptions.LocationException;
import com.adins.exceptions.LocationException.ReasonLocation;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericDistrictValidatorLogic extends BaseLogic implements DistrictValidatorLogic {
	
	
	private String getMessages(String code, Object[] params, AuditContext audit) {
		return messageSource.getMessage(code, params, retrieveLocaleAudit(audit));
	}
	
	@Override
	public AmMsdistrict validateGetDistrict(Long idMsdistrict, AmMsprovince province, boolean checkDistrictExistence, AuditContext audit) {
		AmMsdistrict district = daoFactory.getDistrictDao().getDistrict(idMsdistrict);
		if (checkDistrictExistence) {
			if (null == district) {
				throw new LocationException(getMessages("businesslogic.district.notfound",
						new Object[] {idMsdistrict}, audit), ReasonLocation.DISTRICT_NOT_FOUND);
			}
			if (!province.getProvinceName().equals(district.getAmMsprovince().getProvinceName())) {
				String message = getMessages("businesslogic.district.invalidprovince", new String[] {district.getDistrictName(), province.getProvinceName()}, audit);
				throw new LocationException(message, ReasonLocation.INVALID_PROVINCE);
			}
		}
		return district;
	}

}
