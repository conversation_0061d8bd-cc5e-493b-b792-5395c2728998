package com.adins.esign.dataaccess.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.dataaccess.api.BusinessLineDao;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsTenant;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class BusinessLineDaoHbn extends BaseDaoHbn implements BusinessLineDao {

	@SuppressWarnings("unchecked")
	@Override
	public List<MsBusinessLine> getBusinessLineByTenant(String tenantCode) {
		MsTenant tenant = this.managerDAO.selectOne(MsTenant.class,
				new Object[][] {{ Restrictions.eq(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)) }});
		
		Object[][] params = new Object[][] {{ Restrictions.eq(MsBusinessLine.TENANT_HBM, tenant) }};
		Map<String, Object> mapResult = this.managerDAO.selectAll(MsBusinessLine.class, params, null);
		return (List<MsBusinessLine>) mapResult.get(AmGlobalKey.MAP_RESULT_LIST);
	}
	
	@Override
	public MsBusinessLine getBusinessLineByCodeAndTenant(String businessLineCode, String tenantCode) {
		if (StringUtils.isBlank(businessLineCode))
			return null;
	
		Object[][] queryParams = {
				{MsBusinessLine.BUSINESSLINE_CODE_HBM, StringUtils.upperCase(businessLineCode)},
				{"tenantCode", StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
				"from MsBusinessLine mbl "
				+ "join fetch mbl.msTenant mt "
				+ "where mbl.businessLineCode = :businessLineCode and mt.tenantCode = :tenantCode ",
				queryParams);
	}

	@Override
	public void insertBusinessLine(MsBusinessLine businessLine) {
		businessLine.setUsrCrt(MssTool.maskData(businessLine.getUsrCrt()));
		this.managerDAO.insert(businessLine);
	}

}
