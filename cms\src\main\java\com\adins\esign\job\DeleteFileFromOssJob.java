package com.adins.esign.job;

import java.util.concurrent.BlockingQueue;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.model.custom.QueueDeleteBean;

@DisallowConcurrentExecution
@Component
public class DeleteFileFromOssJob {
	private static final Logger LOG = LoggerFactory.getLogger(DeleteFileFromOssJob.class);
	@Autowired private CloudStorageLogic cloudStorageLogic;
	
	public void takeQueueSaveSigningResult() {
		BlockingQueue<QueueDeleteBean> queueDelete = QueuePublisher.getQueueDeleteFileFromOss();
		LOG.info("Deleting OSS files in folder /pdfdoc started");
		while (!queueDelete.isEmpty()) {
			try {
				QueueDeleteBean bean = queueDelete.take();
				cloudStorageLogic.deleteFileFromOss(bean.getRefNumber());
			} catch (InterruptedException e) {
				LOG.info("Error on running take queue delete OSS files from /pdfdoc. Interrupting current thread.");
				Thread.currentThread().interrupt();
			} catch (Exception e) {
				LOG.error("Error on running take queue delete OSS files from /pdfdoc", e);
			}
		}
		
		LOG.info("Deleting OSS files in folder /pdfdoc finished");
	}
}
