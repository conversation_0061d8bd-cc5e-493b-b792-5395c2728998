package com.adins.esign.businesslogic.api;

import com.adins.esign.model.<PERSON><PERSON>ov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrDocumentD;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface CallbackLogic {
	/**
	 * <AUTHOR>
	 * 
	 * @param tenant Mandatory, will be used to insert tr_client_callback_request
	 * @param lovCallbackType Mandatory, will be used to insert tr_client_callback_request
	 * @param vendorUser Mandatory, will be used to insert tr_client_callback_request
	 * @param document Optional, will be used to insert tr_client_callback_request if filled
	 * @param callbackMessage Callback message that will be sent to client (Example: "Success", "Verification failed", etc.)
	 * <br><br>
	 * Insert tr_client_callback_request with <code>Propagation.REQUIRES_NEW</code> transaction.<br>
	 * If database session error occurred, may be needed to get <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>endorRegisteredU<PERSON>, or <PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON> with <code>Propagation.REQUIRES_NEW</code> transaction.
	 */
	void executeCallbackToClient(MsTenant tenant, MsLov lovCallbackType, MsVendorRegisteredUser vendorUser, TrDocumentD document, String callbackMessage, AuditContext audit);
}
