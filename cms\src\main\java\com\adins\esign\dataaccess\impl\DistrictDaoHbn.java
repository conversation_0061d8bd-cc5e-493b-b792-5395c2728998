package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsdistrict;
import com.adins.esign.dataaccess.api.DistrictDao;
import com.adins.esign.model.custom.DistrictBean;
import com.adins.esign.model.custom.DistrictExternalBean;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class DistrictDaoHbn  extends BaseDaoHbn implements DistrictDao{
	
	private String constructParamsDistrict(Map<String, Object> params, String districtName, Long provinceId) {
		StringBuilder query = new StringBuilder();
		
		if (StringUtils.isNotBlank(districtName) && provinceId != null) {
			query.append(" WHERE district_name LIKE :districtName  AND id_msprovince = :provinceId ");
			params.put("districtName", districtName);
			params.put("provinceId", provinceId);
		}
		else if (!StringUtils.isNotBlank(districtName) && provinceId!=null) {
			query.append(" WHERE id_msprovince = :provinceId ");
			params.put("provinceId", provinceId);
		}
		
		return query.toString();
	}
	
	@Override
	public List<DistrictBean> getListDistrict(String districtName, Long provinceId){
		Map<String, Object> params = new HashMap<>();

		String paramsQueryDistrict = this.constructParamsDistrict(params, districtName, provinceId);
		StringBuilder query = new StringBuilder();
		
		query.append(" SELECT district_name AS \"districtName\", ")
		.append(" CAST(id_msdistrict AS VARCHAR) AS \"idMsDistrict\" ")
		.append(" FROM am_msdistrict ")
		.append(paramsQueryDistrict);
		
	
		return this.managerDAO.selectForListString(DistrictBean.class, query.toString(), params, null);
	}


	@Override
	public AmMsdistrict getDistrictById(Long districtId, Long idMsprovince) {
		if (districtId == null && idMsprovince == null) {
			return null;
		}
	
		Object[][] queryParams = {
				{AmMsdistrict.DISTRICT_ID_HBM, districtId},
				{"idMsprovince", idMsprovince}
		};
		
		return this.managerDAO.selectOne(
				"from AmMsdistrict ad "
				+ "join fetch ad.amMsprovince ap "
				+ "where ad.districtId = :districtId and ap.idMsprovince = :idMsprovince",
				queryParams);
	}


	@Override
	public void insertAmMsdistrict(AmMsdistrict district) {
		district.setUsrCrt(MssTool.maskData(district.getUsrCrt()));
		this.managerDAO.insert(district);
	}

	@Override
	public void updateAmMsdistrict(AmMsdistrict district) {
		district.setUsrUpd(MssTool.maskData(district.getUsrUpd()));
		this.managerDAO.update(district);		
	}

	@Override
	public AmMsdistrict getDistrictByName(String districtName) {
		if (districtName == null) {
			return null;
		}
		return this.managerDAO.selectOne(AmMsdistrict.class,
				new Object[][] {{ Restrictions.eq(AmMsdistrict.DISTRICT_NAME_HBM, StringUtils.upperCase(districtName)) }});
	}

	@Override
	public AmMsdistrict getDistrict(Long idMsdistrict) {
		return this.managerDAO.selectOne(
				"from AmMsdistrict md "
				+ "join fetch md.amMsprovince "
				+ "where md.idMsdistrict = :idMsdistrict ",
				new Object[][] {{AmMsdistrict.ID_MSDISTRICT_HBM, idMsdistrict}});
	}

	@Override
	public List<DistrictExternalBean> getDistrictExternalList(Long idMsprovince) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put("idMsProvince", idMsprovince);
		
		query.append(" select district_name AS \"districtName\" from am_msdistrict " + 
				"where id_msprovince = :idMsProvince ORDER by district_name asc ");
		
		return this.managerDAO.selectForListString(DistrictExternalBean.class, query.toString(), params, null);
	}

}
