package com.adins.esign.businesslogic.api;

import com.adins.esign.webservices.model.GetSubDistrictByInvitationRequest;
import com.adins.esign.webservices.model.GetSubDistrictEmbedRequest;
import com.adins.esign.webservices.model.GetSubDistrictRequest;
import com.adins.esign.webservices.model.GetSubDistrictResponse;
import com.adins.esign.webservices.model.external.GetSubDistrictListExternalRequest;
import com.adins.esign.webservices.model.external.GetSubDistrictListExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface SubDistrictLogic {
	
	GetSubDistrictResponse getSubDistrictList(GetSubDistrictRequest request, AuditContext audit);
	
	GetSubDistrictResponse getSubDistrictListEmbed(GetSubDistrictEmbedRequest request, AuditContext audit);
	
	GetSubDistrictResponse getSubDistrictListByInvitation(GetSubDistrictByInvitationRequest request, AuditContext audit);
	
	GetSubDistrictListExternalResponse getSubDistrictListExternal(GetSubDistrictListExternalRequest request, String apiKey, AuditContext audit);
	
}
