package com.adins.esign.businesslogic.api;

import java.util.Date;
import java.util.Map;

import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrJobCheckRegisterStatus;
import com.adins.esign.model.TrManualReport;
import com.adins.esign.model.TrSigningProcessAuditTrail;

public interface CloudStorageLogic {
	
	// Deprecated TekenAja Methods - START
	String storeDocument(String refNumber, String documentId, byte[] document);
	String storeDocumentSignedTknaj(String year, String month, String refNumber, String documentId, byte[] document);
	byte[] getDocumentTknAj(TrDocumentH docH,TrDocumentD document);
	String storeCertificateTknAj(String tekenId,byte[] certificate);
	byte [] getCertificateTknAj(String tekenId);
	// Deprecated TekenAja Methods - END
	
	String storeRegistrationSelfie(String nik, byte[] photo);
	String storeRegistrationKtp(String nik, byte[] photo);
	byte[] getContentKtp(String key); //Foto ktp dienkrip
	byte[] getContentNonKtp(String key); //Foto non-ktp tidak dienkrip
	String storeTransactionSelfie(String tenantCode, String nik, String refNumber, byte[] photo);
	
	String deleteFileFromOss(String refNumber);

	// Stamping Document, directory: /stamping_eMaterai/
	String storeStampingDocument(String tenantCode, String refNumber, String documentId, byte[] document);
	void deleteStampingDocument(String tenantCode, String refNumber, String documentId);
	byte[] getStampingDocument(String tenantCode, String refNumber, String documentId, String documentArchiveStatus);
	
	// Stamping payment receipt, directory: /stamping_document/base/
	String storeStampingPaymentReceipt(TrDocumentD document, byte[] documentByteArray);
	void deleteStampingPaymentReceipt(TrDocumentD document);
	byte[] getStampingPaymentReceipt(TrDocumentD document);
	
	// Stamped Document, directory: /stamping_result/
	String storeStampedDocument(TrDocumentD document, byte[] documentByteArray);
	void deleteStampedDocument(TrDocumentD document);
	byte[] getStampedDocument(TrDocumentD document);
	
	// VIDA base sign document, directory: /signing_document/
	String storeBaseSignDocument(TrDocumentD document, byte[] documentByteArray);
	void deleteBaseSignDocument(TrDocumentD document);
	byte[] getBaseSignDocument(TrDocumentD document);
	
	// VIDA signed document, directory: /sign_complete/
	String storeSignedDocument(TrDocumentD document, byte[] documentByteArray);
	void deleteSignedDocument(TrDocumentD document);
	byte[] getSignedDocument(TrDocumentD document);
	
	//upload dummy
	String storeDocumentUrlDummy(String tenantCode, String refNumber, byte[] document);
	
	//downloadManualReport
	byte [] getManualReport(TrManualReport document);
	
	// User selfie, directory: selfie_user/{year}/{month}/{trx no}.jpeg
	String storeUserSelfie(String trxNo, Date trxDate, byte[] selfiePhoto);
	
	// User liveness face compare selfie audit trail, directory: audit_log/liveness_face_compare/{id_signing_process_audit_trail}.jpeg
	String storeLivenessFaceComparePhoto(long idTrSigningProcessAuditLog, byte[] selfiePhoto);
		
	
	// Register request, directory: register_request/{year}/{month}/{id_job_check_register_status}.txt
	String storeRegisterRequest(TrJobCheckRegisterStatus jobCheckRegisterStatus, String jsonRequest);
	
	//Save Manual Stamp , directory: stamping_ematerai\{year}\{month}\{document_id}.pdf
	String storeSaveManualStamp(TrDocumentD docD, byte[] document);
	byte[] getManualStamp(TrDocumentD document);
	
	// Audit trail API log, directory: audit_log/{process_name}/{id_signing_process_audit_trail}.zip
	String storeZippedApiLogAuditTrail(String subfolderName, TrSigningProcessAuditTrail trail, byte[] zippedTextFile);
	byte[] getZippedApiLogAuditTrail(String subfolderName, TrSigningProcessAuditTrail trail);
	
	String storeAutosignExcel(Date trxDate, String tenantCode, String vendorCode, String filename, long idProcessExcelBmH, byte[] base64Excel);
	byte[] getTemplateExcelAutosignBm();
	
	String storeManualReportUpload(String tenantCode,  String filename, byte[] base64Excel);
	String deleteManualReport(String tenantCode,  String filename);
	byte[] getCustomSign(String tenantCode, String idNo);

	void restoreObject(String key, Integer days);
	Map<String, String> getObjectRestoreStatus(String key);
}
