package com.adins.esign.businesslogic.api;

public interface FunctionComputeLogic {
	void invokeAsynchronousTest(String payload);
	void invokeReconcileOtpDigisign(long idJobResult);
	void invokeSignVidaDocument(long idSignRequest);
	void invokeSignPrivyDocument(long idSignRequest);
	void invokeUpdatePsreId(long idJobUpdatePsreId);
	void invokeRegistrationLiveness(String trxNo);
	void invokeCheckRegisterStatus(long idJobCheckRegisterStatus);
	void invokeClientCallback(long idClientCallbackRequest);
	void invokeAttachMeteraiPrivy(long idDocumentH);
	void invokeAttachMeteraiVida(long idDocumentH);
	void invokeProcessAutosignData(long idProcessAutosignBmH);
	void invokeAttachMeteraiPajakku(long idDocumentH);
}
