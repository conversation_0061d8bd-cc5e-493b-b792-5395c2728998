package com.adins.am.model;
// Generated 09-Sep-2021 22:49:32 by Hibernate Tools 5.2.12.Final

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;
import com.adins.esign.model.MsTenant;

/**
 * AmGeneralsetting generated by hbm2java
 */
@SuppressWarnings("squid:S107")
@Entity
@Table(name = "am_generalsetting")
public class AmGeneralsetting extends ActiveAndUpdateableEntity implements java.io.Serializable {
	private static final long serialVersionUID = 1L;

	public static final String GS_CODE_HBM = "gsCode";
	
	private long idGeneralSetting;
	private MsTenant msTenant;
	private String gsCode;
	private String gsPrompt;
	private String gsType;
	private String gsValue;
	private String editable;
	
	public AmGeneralsetting() {
	}

	public AmGeneralsetting(long idGeneralSetting, String usrCrt, Date dtmCrt, String editable) {
		this.idGeneralSetting = idGeneralSetting;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.editable = editable;
	}

	public AmGeneralsetting(long idGeneralSetting, MsTenant msTenant, String isActive, String usrCrt, Date dtmCrt,
			String usrUpd, Date dtmUpd, String gsCode, String gsPrompt, String gsType, String gsValue,
			String editable) {
		this.idGeneralSetting = idGeneralSetting;
		this.msTenant = msTenant;
		this.isActive = isActive;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.usrUpd = usrUpd;
		this.dtmUpd = dtmUpd;
		this.gsCode = gsCode;
		this.gsPrompt = gsPrompt;
		this.gsType = gsType;
		this.gsValue = gsValue;
		this.editable = editable;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_general_setting", unique = true, nullable = false)
	public long getIdGeneralSetting() {
		return this.idGeneralSetting;
	}

	public void setIdGeneralSetting(long idGeneralSetting) {
		this.idGeneralSetting = idGeneralSetting;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_tenant")
	public MsTenant getMsTenant() {
		return this.msTenant;
	}

	public void setMsTenant(MsTenant msTenant) {
		this.msTenant = msTenant;
	}

	@Column(name = "gs_code", length = 80)
	public String getGsCode() {
		return this.gsCode;
	}

	public void setGsCode(String gsCode) {
		this.gsCode = gsCode;
	}

	@Column(name = "gs_prompt", length = 200)
	public String getGsPrompt() {
		return this.gsPrompt;
	}

	public void setGsPrompt(String gsPrompt) {
		this.gsPrompt = gsPrompt;
	}

	@Column(name = "gs_type", length = 80)
	public String getGsType() {
		return this.gsType;
	}

	public void setGsType(String gsType) {
		this.gsType = gsType;
	}

	@Column(name = "gs_value", length = 2048)
	public String getGsValue() {
		return this.gsValue;
	}

	public void setGsValue(String gsValue) {
		this.gsValue = gsValue;
	}

	@Column(name = "editable", nullable = false, length = 1)
	public String getEditable() {
		return this.editable;
	}

	public void setEditable(String editable) {
		this.editable = editable;
	}

}
