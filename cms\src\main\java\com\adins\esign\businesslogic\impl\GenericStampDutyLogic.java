package com.adins.esign.businesslogic.impl;

import java.io.IOException;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.ExcelLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.StampDutyLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrDocumentHStampdutyError;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.EmbedMsgBean;
import com.adins.esign.model.custom.MonitoringBean;
import com.adins.esign.model.custom.StampDutyBean;
import com.adins.esign.model.custom.StampDutyHistoryBean;
import com.adins.esign.model.custom.StampDutyReportBean;
import com.adins.esign.model.custom.TaxReportBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.webservices.model.CreateMateraiRequest;
import com.adins.esign.webservices.model.CreateMateraiResponse;
import com.adins.esign.webservices.model.TaxExcelReportRequest;
import com.adins.esign.webservices.model.TaxExcelReportResponse;
import com.adins.esign.webservices.model.InquiryStampDutyDetailRequest;
import com.adins.esign.webservices.model.InquiryStampDutyDetailResponse;
import com.adins.esign.webservices.model.InquiryStampDutyRequest;
import com.adins.esign.webservices.model.InquiryStampDutyResponse;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiEmbedRequest;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiRequest;
import com.adins.esign.webservices.model.ListMonitoringEmeteraiResponse;
import com.adins.esign.webservices.model.ReverseTopupRequest;
import com.adins.esign.webservices.model.ReverseTopupResponse;
import com.adins.esign.webservices.model.StampDutyExcelReportRequest;
import com.adins.esign.webservices.model.StampDutyExcelReportResponse;
import com.adins.esign.webservices.model.UpdateStampDutyStatusRequest;
import com.adins.esign.webservices.model.UpdateStampDutyStatusResponse;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.EmbedMsgException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.EmbedMsgException.ReasonEmbedMsg;
import com.adins.exceptions.StampDutyException;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.StampDutyException.ReasonStampDuty;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.UserException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Transactional
@Component
public class GenericStampDutyLogic extends BaseLogic implements StampDutyLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericStampDutyLogic.class);
	private static final String CONST_LIST_INVITATION = "listInvitation";
	
	@Autowired private ExcelLogic excelLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private VendorLogic vendorLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	
	@Override
	public TrStampDuty getStampDutyById(long idStampDuty) {
		return daoFactory.getStampDutyDao().getStampDutyById(idStampDuty);
	}
	
	@Override
	public Map<String, Object> getStampDutyByIdTenant(long idMsTenant) {
		return daoFactory.getStampDutyDao().getAvailableStampDutyByIdTenant(idMsTenant);
	}
	
	@Override
	public long countAvailableStampDutyByIdTenant(long idMsTenant) {
		BigInteger availableStamp = daoFactory.getStampDutyDao().countAvailableStampDutyByIdTenant(idMsTenant);		
		return availableStamp.longValue();
	}

	@Override
	public void updateStampDutyStatus(TrStampDuty stampDuty, String codeLovStampDutyStatus, AuditContext audit) {
		MsLov stampDutyStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS, codeLovStampDutyStatus);
		stampDuty.setMsLov(stampDutyStatus);
		stampDuty.setUsrUpd(audit.getCallerId());
		stampDuty.setDtmUpd(new Date());
		daoFactory.getStampDutyDao().updateNativeStringTrStampDuty(stampDuty);
	}

	@Override
	public CreateMateraiResponse createMaterai(CreateMateraiRequest request, AuditContext audit) {
		
		// Check if user tenant is the same with tenant from request
		if (!isUserValid(request.getLoginId(), request.getTenantCode(), audit)) {
			throw new StampDutyException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SDT_USER_CANNOT_CREATE,
					null, this.retrieveLocaleAudit(audit)), ReasonStampDuty.USER_CANNOT_CREATE_SDT);
		}
		
		// Check if vendor can create stamp duty
		if (!isVendorAbleToCreateStamp(request.getVendorCode())) {
			throw new StampDutyException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SDT_VENDOR_CANNOT_CREATE,
					new Object[] {request.getVendorCode()}, this.retrieveLocaleAudit(audit)), ReasonStampDuty.VENDOR_CANNOT_CREATE_SDT);
		}
		
		// Check if vendor is valid for tenant to create sdt
		if (!isVendorValidForTenant(request.getTenantCode(), request.getVendorCode(), audit)) {
			throw new StampDutyException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SDT_VENDOR_TENANT_INVALID,
					new Object[] {request.getTenantCode(), request.getVendorCode()}, this.retrieveLocaleAudit(audit)),
					ReasonStampDuty.INVALID_VENDOR_TENANT);
		}
		
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		MsLov trxTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE,
				GlobalVal.CODE_LOV_TRX_TYPE_TSDT);
		MsLov balanceTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		MsLov stampDutyStatusLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SDT_STATUS,
				GlobalVal.CODE_LOV_SDT_AVAILABLE);
		
		// Insert to tr_balance_mutation
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(request.getLoginId());
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		
		Integer qty = 0;
		Integer fee = 0;
		try {
			qty = Integer.valueOf(request.getStampDutyQty());
		} catch (NumberFormatException e) {
			throw new StampDutyException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SDT_PARSE_QTY,
					null, this.retrieveLocaleAudit(audit)), ReasonStampDuty.CONVERT_QTY_ERROR);
		}
		try {
			fee = Integer.valueOf(request.getStampDutyFee());
		} catch (NumberFormatException e) {
			throw new StampDutyException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SDT_PARSE_FEE,
					null, this.retrieveLocaleAudit(audit)), ReasonStampDuty.CONVERT_FEE_ERROR);
		}
				
		Date trxDate = new Date();
		String refNo = StringUtils.upperCase(request.getStampDutyInvNo());
		String notes = request.getNotes();
		
		saldoLogic.insertBalanceMutation(null, null, null, balanceTypeLov, trxTypeLov, tenant, vendor, trxDate, 
				refNo, qty, trxNo, user, notes, null, audit);
		
		daoFactory.getStampDutyDao().insertTrStampDuty(request.getLoginId(),
				stampDutyStatusLov.getIdLov(), tenant.getIdMsTenant(), vendor.getIdMsVendor(), fee, trxNo, qty);
		
		CreateMateraiResponse response = new CreateMateraiResponse();
		Status status = new Status();
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}

	private boolean isUserValid(String loginId, String tenantCode, AuditContext audit) {
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(loginId);
		if (null == user) {
			throw new UserException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_USER_NOT_FOUND,
					null, this.retrieveLocaleAudit(audit)), ReasonUser.LOGIN_ID_NOT_EXISTS);
		}
		List<AmMsrole> roles = daoFactory.getRoleDao().getListRoleByIdUser(user.getIdMsUser());
		for (AmMsrole amMsrole : roles) {
			if (GlobalVal.ROLE_CUSTOMER.equalsIgnoreCase(amMsrole.getRoleCode())
					|| GlobalVal.ROLE_BM_MF.equalsIgnoreCase(amMsrole.getRoleCode())) {
				return false;
			}
		}
		return null != tenantLogic.getUseroftenant(loginId, tenantCode, audit);
	}
	
	private boolean isVendorValidForTenant(String tenantCode, String vendorCode, AuditContext audit) {
		return null != vendorLogic.getVendorTenantByCode(tenantCode, vendorCode, audit);
	}
	
	private boolean isVendorAbleToCreateStamp(String vendorCode) {
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);
		return GlobalVal.CODE_LOV_VENDOR_TYPE_E_MATERAI.equalsIgnoreCase(vendor.getMsLovVendorType().getCode());
	}

	@Override
	public InquiryStampDutyResponse getListStampDuty(InquiryStampDutyRequest request, AuditContext audit) {
		InquiryStampDutyResponse response = new InquiryStampDutyResponse();
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		
		Date dateStart = null;
		Date dateEnd = null;
		if(StringUtils.isNotBlank(request.getStampDutyUsedDtStart())) {
			dateStart = MssTool.formatStringToDate(request.getStampDutyUsedDtStart() + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		if(StringUtils.isNotBlank(request.getStampDutyUsedDtEnd())) {
			dateEnd = MssTool.formatStringToDate(request.getStampDutyUsedDtEnd() + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		
		int totalResult = daoFactory.getStampDutyDao().countListStampDuty(
				request.getTenantCode(), request.getInvoiceNo(), request.getStampDutyStatus(),
				request.getStampDutyNo(), request.getBusinessLineCode(), request.getOfficeCode(), 
				request.getRegionCode(), dateStart, dateEnd, balanceType.getIdLov());
		
		double totalPage = Math.ceil((double) totalResult / maxRow);
		
		List<StampDutyBean> result = daoFactory.getStampDutyDao().getListStampDuty(
				request.getTenantCode(), request.getInvoiceNo(), request.getStampDutyStatus(),
				request.getStampDutyNo(), request.getBusinessLineCode(), request.getOfficeCode(),
				request.getRegionCode(), dateStart, dateEnd, min, max, balanceType.getIdLov());
		
		response.setListStampDuty(result);
		response.setPage(request.getPage());
		response.setTotalPage((int) totalPage);
		response.setTotalResult(totalResult);
		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}

	@Override
	public InquiryStampDutyDetailResponse getStampDutyDetail(InquiryStampDutyDetailRequest request, AuditContext audit) {
		InquiryStampDutyDetailResponse response = new InquiryStampDutyDetailResponse();
		List<StampDutyHistoryBean> result = daoFactory.getStampDutyDao().getListStampDutyDetail(request.getTenantCode(), request.getIdStampDuty());
		
		response.setStampDutyHist(result);
		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		
		return response;
	}

	@Override
	public InquiryStampDutyResponse getListReverseTopup(InquiryStampDutyRequest request, AuditContext audit)
			throws ParseException {
		InquiryStampDutyResponse response = new InquiryStampDutyResponse();
		// Sudah divalidasi RBAC
		// this.userValidation(audit.getCallerId(), request.getTenantCode(), audit); //callerId = loginId
		
		List<StampDutyBean> result = daoFactory.getStampDutyDao().getListReverseTopup(request.getTenantCode(), request.getInvoiceNo(), 
				GlobalVal.CODE_LOV_SDT_AVAILABLE, request.isReversalSearchFilter());
		response.setListStampDuty(result);
		Status status = new Status();
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}

	@Override
	public ReverseTopupResponse reverseTopupMaterai(ReverseTopupRequest request, AuditContext audit) {
		ReverseTopupResponse response = new ReverseTopupResponse();
		Status status = new Status();
		if (StringUtils.isBlank(request.getInvoiceNo())) {
			status.setCode(200);
			status.setMessage(this.messageSource.getMessage("businesslogic.stampduty.emptyinvoiceno", 
					new Object[] {}, this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
			return response;
		}
		
		if (StringUtils.isBlank(request.getTenantCode())) {
			status.setCode(200);
			status.setMessage(this.messageSource.getMessage("businesslogic.vendor.emptytenantcode", 
					new Object[] {}, this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
			return response;
		}
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			status.setCode(200);
			status.setMessage(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_TENANT_NOT_EXIST, 
					new Object[] {request.getTenantCode()}, this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
			return response;
		}
		
		if (StringUtils.isBlank(request.getVendorCode())) {
			status.setCode(200);
			status.setMessage(this.messageSource.getMessage("businesslogic.vendor.emptytenantcode", 
					new Object[] {}, this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
			return response;
		}
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(request.getVendorCode());
		if (null == vendor) {
			status.setCode(200);
			status.setMessage(this.messageSource.getMessage("businesslogic.saldo.vendornotexist", 
					new Object[] {request.getVendorCode()}, this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
			return response;
		}
		
		StampDutyBean bean = daoFactory.getStampDutyDao().getStampDutyByInvoiceNo(request.getInvoiceNo());
		if (!bean.getVendorCode().equalsIgnoreCase(request.getVendorCode())) {
			status.setCode(200);
			status.setMessage(this.messageSource.getMessage("businesslogic.stampduty.vendorcannotdelete", 
					new Object[] {request.getVendorCode()}, this.retrieveLocaleAudit(audit)));
			response.setStatus(status);
			return response;
		}
		
		daoFactory.getStampDutyDao().deleteListStampDutyByTenantCodeAndInvoiceNoAndStampDutyStatus(request.getTenantCode(), request.getVendorCode(), request.getInvoiceNo(), GlobalVal.CODE_LOV_SDT_AVAILABLE);

		MsLov balanceTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SDT);
		AmMsuser user = daoFactory.getUserDao().getUserByLoginId(audit.getCallerId());
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		MsLov trxTypeLov = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USDT);
		saldoLogic.insertBalanceMutation(null, null, null, balanceTypeLov, trxTypeLov, tenant, vendor, new Date(), request.getInvoiceNo(), -request.getQty(), trxNo, user, request.getNotes(), null, audit);
		
		status.setCode(GlobalVal.STATUS_CODE_SUCCESS);
		status.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setStatus(status);
		return response;
	}

	@Override
	public StampDutyExcelReportResponse exportStampDutyReport(StampDutyExcelReportRequest request, AuditContext audit) {
		Date dateStart = null;
		Date dateEnd = null;
		
		if(StringUtils.isNotBlank(request.getStampDutyUsedDtStart())) {
			dateStart = MssTool.formatStringToDate(request.getStampDutyUsedDtStart() + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		if(StringUtils.isNotBlank(request.getStampDutyUsedDtEnd())) {
			dateEnd = MssTool.formatStringToDate(request.getStampDutyUsedDtEnd() + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		List<StampDutyReportBean> result = daoFactory.getStampDutyDao().getListStampDutyReport(request.getTenantCode(), request.getInvoiceNo(),
				request.getStampDutyStatus(), request.getStampDutyNo(),
				request.getBusinessLineCode(), request.getOfficeCode(), request.getRegionCode(), dateStart, dateEnd);
				
		StampDutyExcelReportResponse response = new StampDutyExcelReportResponse();
		response.setBase64ExcelReport(this.generateStampDutyExcelFile(result));
		response.setFilename(this.generateStampDutyExcelFilename(request));
		
		return response;
	}
	
	private String generateStampDutyExcelFilename(StampDutyExcelReportRequest request) {
		StringBuilder filename = new StringBuilder();
		filename.append("SDT_REPORT");
		if (StringUtils.isNotBlank(request.getInvoiceNo())) {
			filename.append("_").append(request.getInvoiceNo());
		}
		if (StringUtils.isNotBlank(request.getStampDutyStatus())) {
			filename.append("_").append(request.getStampDutyStatus());
		}
		if (StringUtils.isNotBlank(request.getStampDutyNo())) {
			filename.append("_").append(request.getStampDutyNo());
		}
		if (StringUtils.isNotBlank(request.getBusinessLineCode())) {
			filename.append("_").append(request.getBusinessLineCode());
		}
		if (StringUtils.isNotBlank(request.getRegionCode())) {
			filename.append("_").append(request.getRegionCode());
		}
		if (StringUtils.isNotBlank(request.getOfficeCode())) {
			filename.append("_").append(request.getOfficeCode());
		}
		filename.append(" ").append(MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_FORMAT_SDT_REPORT));
		filename.append(".xlsx");
		return filename.toString();
	}
	
	private String generateStampDutyExcelFile(List<StampDutyReportBean> listStampDuty) {
		byte[] excelByteArray = null;
		try {
			excelByteArray = excelLogic.generate((workbook, styleBoldText) ->
				this.createStampDutyExcelSheet(workbook, styleBoldText, listStampDuty));
		} catch (IOException e) {
			throw new StampDutyException("", ReasonStampDuty.GENERATE_SDT_REPORT_ERROR);
		}
		
		return Base64.getEncoder().encodeToString(excelByteArray);
	}
	
	private void createStampDutyExcelSheet(XSSFWorkbook workbook, XSSFCellStyle styleBoldText,
			List<StampDutyReportBean> listStampDuty) {
		XSSFSheet mainSheet = workbook.createSheet("Laporan Meterai");
		
		String[] column = new String[] {"No Meterai", "No Kontrak", "Tanggal Pakai", "Harga Meterai", "Status",
				"Nama Dokumen", "Nama Pelanggan", "Cabang", "Area", "Lini Bisnis"};
		
		for (int i = 0; i < column.length; i++) {
			switch (i) {
				case 0: // No Meterai
					mainSheet.setColumnWidth(i, 7000);
					break;
				case 1: // No Kontrak
					mainSheet.setColumnWidth(i, 5000);
					break;
				case 2: // Tanggal Pakai
					mainSheet.setColumnWidth(i, 4000);
					break;
				case 3: // Harga Meterai
					mainSheet.setColumnWidth(i, 4000);
					break;
				case 4: // Status
					mainSheet.setColumnWidth(i, 5000);
					break;
				case 5: // Nama Dokumen
					mainSheet.setColumnWidth(i, 11000);
					break;
				case 6: // Nama Pelanggan
					mainSheet.setColumnWidth(i, 5000);
					break;
				case 7: // Cabang
					mainSheet.setColumnWidth(i, 5000);
					break;
				case 8: // Area
					mainSheet.setColumnWidth(i, 5000);
					break;
				case 9: // Lini Bisnis
					mainSheet.setColumnWidth(i, 11000);
					break;
				default:
					mainSheet.setColumnWidth(i, 5000);
					break;
			}
		}
		
		XSSFRow rowOne = mainSheet.createRow(0);
		for (int i = 0; i < column.length; i++) {
			XSSFCell cell = rowOne.createCell(i);
			cell.setCellValue(column[i]);
			cell.setCellStyle(styleBoldText);
		}
		
		for (int i = 0 ; i < listStampDuty.size(); i++) {
			XSSFRow row = mainSheet.createRow(i+1);
			XSSFCell cellOne = row.createCell(0);
			XSSFCell cellTwo = row.createCell(1);
			XSSFCell cellThree = row.createCell(2);
			XSSFCell cellFour = row.createCell(3);
			XSSFCell cellFive = row.createCell(4);
			XSSFCell cellSix = row.createCell(5);
			XSSFCell cellSeven = row.createCell(6);
			XSSFCell cellEight = row.createCell(7);
			XSSFCell cellNine = row.createCell(8);
			XSSFCell cellTen = row.createCell(9);
			
			StampDutyReportBean bean = listStampDuty.get(i);
			cellOne.setCellValue(bean.getStampDutyNo());
			cellTwo.setCellValue(bean.getNoKontrak());
			cellThree.setCellValue(bean.getStampDutyUsedDt());
			cellFour.setCellValue(bean.getStampDutyFee());
			cellFive.setCellValue(bean.getStampDutyStatusDesc());
			cellSix.setCellValue(bean.getDocName());
			cellSeven.setCellValue(bean.getCustName());
			cellEight.setCellValue(bean.getOfficeName());
			cellNine.setCellValue(bean.getRegionName());
			cellTen.setCellValue(bean.getBusinessLineName());
		}
	}

	@Override
	public UpdateStampDutyStatusResponse updateStampDutyStatus(UpdateStampDutyStatusRequest request,
			AuditContext audit) throws IOException {
		TrDocumentH documentH = daoFactory.getDocumentDao().getDocumentHeaderByRefNo(request.getAgreementNo());
		if (null == documentH) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {"DocumentH", request.getAgreementNo()}, this.retrieveLocaleAudit(audit)),
					ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}
		
		if (!Objects.equals(documentH.getTotalDocument(), documentH.getTotalSigned()) ) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_YET_SIGNED_ALL,
					new Object[] {}, this.retrieveLocaleAudit(audit)),
					ReasonDocument.DOCUMENT_NOT_YET_SIGNED_ALL);
		}
		
		if (GlobalVal.STATUS_ATTACH_METERAI_FINISHED.equalsIgnoreCase(documentH.getProsesMaterai().toString())) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_ALRDY_SIGNED_ALL,
					new Object[] {}, this.retrieveLocaleAudit(audit)),
					ReasonDocument.DOCUMENT_ALREADY_SIGNED_ALL);
		}
		
		this.updateStatusProcessMeterai(documentH, GlobalVal.STATUS_ATTACH_METERAI_ERROR, null, audit);
		
		return new UpdateStampDutyStatusResponse();
	}

	@Override
	public UpdateStampDutyStatusResponse attachMeteraiRKG(UpdateStampDutyStatusRequest request, AuditContext audit) throws IOException {
		return updateStampDutyStatus(request, audit);
	}
	
	@Override
	public boolean isAgreementStamped(TrDocumentH documentH) {
		String currentProcess = String.valueOf(documentH.getProsesMaterai());
		return GlobalVal.STATUS_ATTACH_METERAI_FINISHED.equals(currentProcess)
				|| GlobalVal.ON_PREM_STAMP_SUCCESS.equals(currentProcess)
				|| GlobalVal.PRIVY_STAMP_SUCCESS.equals(currentProcess)
				|| GlobalVal.VIDA_STAMP_SUCCESS.equals(currentProcess)
				|| GlobalVal.ON_PREM_PR_STAMP_SUCCESS.equals(currentProcess);
	}

	@Override
	public UpdateStampDutyStatusResponse attachMeteraiPajakku(UpdateStampDutyStatusRequest request, String apiKey, AuditContext audit) throws IOException {
		
		MsTenant tenant = tenantLogic.getTenantFromXApiKey(apiKey, audit);
		
		TrDocumentH documentH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(request.getAgreementNo(), tenant.getTenantCode());
		if (null == documentH) {
			throw new DocumentException(getMessage("businesslogic.document.agreementnotfoundintenant",
					new String[] {request.getAgreementNo(), tenant.getTenantCode()}, audit), ReasonDocument.REFERENCE_NO_NOT_EXISTS);
		}
		
		if (!Objects.equals(documentH.getTotalDocument(), documentH.getTotalSigned()) ) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_NOT_YET_SIGNED_ALL,
					new Object[] {}, audit), ReasonDocument.DOCUMENT_NOT_YET_SIGNED_ALL);
		}
		
		if ("0".equals(documentH.getIsActive())) {
			String message = getMessage("businesslogic.document.inactiveagreement", new String[] {documentH.getRefNumber()}, audit);
			Status status = new Status();
			status.setCode(0);
			status.setMessage(message);
			
			UpdateStampDutyStatusResponse response = new UpdateStampDutyStatusResponse();
			response.setStatus(status);
			return response;
		}
		
		if (isAgreementStamped(documentH)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_ALRDY_SIGNED_ALL,
					new Object[] {}, audit), ReasonDocument.DOCUMENT_ALREADY_SIGNED_ALL);
		}
		
		if (documentH.getProsesMaterai() != null && documentH.getProsesMaterai() != 0) {
			throw new DocumentException(getMessage("businesslogic.document.documentisnotvalidatetostamp",
					new Object[] {tenant.getRefNumberLabel(), documentH.getRefNumber() }, audit), ReasonDocument.CONTRACT_IS_IN_STAMPING_PROCESS);
		}
		
		MsLov lovVendorStamping = tenant.getLovVendorStamping();
		String prosesMaterai = null;
		if (null != lovVendorStamping && GlobalVal.VENDOR_CODE_PRIVY_ID.equals(lovVendorStamping.getCode())) {
			prosesMaterai = GlobalVal.ON_PREM_STAMP_IN_QUEUE_PRIVY;
		} else if (null != lovVendorStamping && GlobalVal.VENDOR_CODE_VIDA.equals(lovVendorStamping.getCode())) {
			prosesMaterai = GlobalVal.VIDA_STAMP_IN_QUEUE;
		} else {
			prosesMaterai = GlobalVal.ON_PREM_STAMP_IN_QUEUE;
		}
		
		this.updateStatusProcessMeterai(documentH, prosesMaterai, null, audit);
		
		return new UpdateStampDutyStatusResponse();
	}
	
	private String updateStatusProcessMeterai(TrDocumentH documentH, String statusAttachMeterai, Exception e, AuditContext audit) {
		if (GlobalVal.STATUS_ATTACH_METERAI_ERROR.equals(statusAttachMeterai) || GlobalVal.ON_PREM_STAMP_IN_QUEUE.equals(statusAttachMeterai))
			LOG.error("Flagging Document with Ref No. {} Error attach Meterai", documentH.getRefNumber());
		if (null != e) {
			LOG.error(e.getLocalizedMessage(), e);
		}

		documentH.setProsesMaterai(new Short(statusAttachMeterai));
		documentH.setDtmUpd(new Date());
		documentH.setUsrUpd(audit.getCallerId());
		daoFactory.getDocumentDao().updateDocumentH(documentH);

		return statusAttachMeterai;
	}
	
	private boolean isDateRangeValid(String startDate, String endDate, AuditContext audit) {
		SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
		String maxRangeDate = daoFactory.getGeneralSettingDao().getGsValueByCode(AmGlobalKey.GENERALSETTING_MAX_DATE_RANGE);
		
		if (StringUtils.isBlank(startDate) && StringUtils.isBlank(endDate)) {
			return true;
		}
		if (StringUtils.isNotBlank(startDate) && StringUtils.isBlank(endDate)) {
			try {
				sdf.parse(startDate);
			} catch (ParseException e) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		if (StringUtils.isBlank(startDate) && StringUtils.isNotBlank(endDate)) {
			try {
				sdf.parse(endDate);
			} catch (ParseException e) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
			}
			return true;
		}
		
		long dayCount = 0;
		try {
			Date start = sdf.parse(startDate);
			Date end = sdf.parse(endDate);
			dayCount = (end.getTime() - start.getTime()) / (1000*60*60*24);
		} catch (ParseException e) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.READ_WRITE_ERROR);
		}
		LOG.info("Date range: {} hari", dayCount);
		return dayCount <= Long.valueOf(maxRangeDate);
	}


	@Override
	public ListMonitoringEmeteraiResponse getListMonitoringEmeterai(ListMonitoringEmeteraiRequest request, AuditContext audit) throws ParseException {
				
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
			
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);

		Date dateStart = null;
		Date dateEnd = null;

		if (!isDateRangeValid(request.getTanggalDokumenMulai(), request.getTanggalDokumenSampai(), audit)) {
			throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
					new Object[] { CONST_LIST_INVITATION }, audit), ReasonDocument.INVALID_DATE_RANGE);
		}

		if (StringUtils.isNotBlank(request.getTanggalDokumenMulai())) {
			dateStart = MssTool.formatStringToDate(request.getTanggalDokumenMulai() + GlobalVal.SOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);

		}

		if (StringUtils.isNotBlank(request.getTanggalDokumenSampai())) {
			dateEnd = MssTool.formatStringToDate(request.getTanggalDokumenSampai() + GlobalVal.EOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		Integer totalData = daoFactory.getDocumentDao().countListMonitoring(min, max, request.getNomorDokumen(), request.getTipeDokumen(), 
				request.getHasilStamping(), request.getJenisDokumen(), request.getTemplateDokumen(), request.getNoSN(), tenant.getTenantCode(),
				request.getTaxType(), request.getCabang(), dateStart, dateEnd);
		
		double totalPage = Math.ceil((double) totalData / maxRow);
		
		List<MonitoringBean> listMonitoringbean = new ArrayList<>();
		List<Map<String, Object>> listMonitoring = daoFactory.getDocumentDao().getListMonitoring(min, max, request.getNomorDokumen(), request.getTipeDokumen(), 
				request.getHasilStamping(), request.getJenisDokumen(), request.getTemplateDokumen(), request.getNoSN(), tenant.getTenantCode(),
				request.getTaxType(), request.getCabang(), dateStart, dateEnd);
		Iterator<Map<String, Object>> itr = listMonitoring.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			
			BigInteger idDocumentD = (BigInteger) map.get("d14");
			BigInteger idDocumentH = (BigInteger) map.get("d15");
			
			TrDocumentHStampdutyError trDocumentHStampdutyError = daoFactory.getDocumentDao().getDocumentHStampdutyErrorNewTran(idDocumentH.longValue(), idDocumentD.longValue());
			if(trDocumentHStampdutyError == null) {
				trDocumentHStampdutyError = daoFactory.getDocumentDao().getDocumentHStampdutyErrorNewTran(idDocumentH.longValue(), null);
			}
			
			MonitoringBean bean = new MonitoringBean();
			bean.setNomorDokumen((String) map.get("d0"));
			bean.setTanggalDokumen((String) map.get("d1"));
			bean.setNamaDokumen((String) map.get("d2"));
			bean.setJenisDokumen((String) map.get("d3"));
			bean.setTipeDokumen((String) map.get("d4"));
			bean.setNominalDokumen((String) map.get("d5"));
			bean.setTemplateDokumen((String) map.get("d6"));
			bean.setHasilStamping((String) map.get("d7"));
			bean.setNoSN((String) map.get("d8"));
			bean.setProsesMaterai((String) map.get("d9"));
			bean.setNoIdentitas((String) map.get("d10"));
			bean.setNamaIdentitas((String) map.get("d11"));
			bean.setTaxType((String) map.get("d12"));
			bean.setCabang((String) map.get("d13"));
			
			//error message
			  if(trDocumentHStampdutyError == null || (GlobalVal.HASIL_STAMPING_SUCCESS.equals(map.get("d7"))) || (GlobalVal.HASIL_STAMPING_IN_PROGRESS.equals(map.get("d7")))) {
				  bean.setErrorMessage("");
			  } else {
				  bean.setErrorMessage(trDocumentHStampdutyError.getErrorMessage());
			  }
			  
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailById(idDocumentD.longValue());
			bean.setDocumentId(docD.getDocumentId());

			// Set document archive status
			String archiveStatus = (String) map.get("d16");
			if ("1".equals(archiveStatus)) {
				bean.setDocumentArchiveStatus(GlobalVal.CONST_ARCHIVE);
			} else if ("2".equals(archiveStatus)) {
				bean.setDocumentArchiveStatus(GlobalVal.CONST_RESTORE);
			} else {
				bean.setDocumentArchiveStatus(GlobalVal.CONST_ACTIVE);
			}

			listMonitoringbean.add(bean);
		}
		
		ListMonitoringEmeteraiResponse response = new ListMonitoringEmeteraiResponse();
		response.setListMonitoring(listMonitoringbean);
		response.setPage(request.getPage());
		response.setTotalResult(totalData);
		response.setTotalPage((int) totalPage);
		return response;
	}
	
	@Override
	public TaxExcelReportResponse exportTaxReport(TaxExcelReportRequest request, AuditContext audit) throws ParseException{
		
		if (StringUtils.isBlank(request.getMsg())) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		if (null == msgBean) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_INVALID, null,
					this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_INVALID);
		}
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		if (null == tenant) {
			if (StringUtils.isBlank(msgBean.getTenantCode())) {
				throw new TenantException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_TENANT_CODE_EMPTY
						, null, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
			} else {
				throw new TenantException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_TENANT_NOT_EXIST
						, new Object[] {msgBean.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
			}
		}
		request.setTenantCode(tenant.getTenantCode());
		
		Date dateStart = null;
		Date dateEnd = null;

		if (!isDateRangeValid(request.getTanggalDokumenMulai(), request.getTanggalDokumenSampai(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { CONST_LIST_INVITATION }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}

		if (StringUtils.isNotBlank(request.getTanggalDokumenMulai())) {
			dateStart = MssTool.formatStringToDate(request.getTanggalDokumenMulai() + GlobalVal.SOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);

		}

		if (StringUtils.isNotBlank(request.getTanggalDokumenSampai())) {
			dateEnd = MssTool.formatStringToDate(request.getTanggalDokumenSampai() + GlobalVal.EOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		List<TaxReportBean> listStampDuty = daoFactory.getDocumentDao().getListMonitoringReportEmbed(request.getNomorDokumen(), request.getTipeDokumen(), 
				request.getHasilStamping(), request.getJenisDokumen(), request.getTemplateDokumen(), request.getNoSN(), request.getTenantCode(),dateStart, dateEnd);
				
		TaxExcelReportResponse response = new TaxExcelReportResponse();
		response.setBase64ExcelReport(this.generateTaxExcelFile(listStampDuty));
		response.setFilename(this.generateTaxExcelFilename(request));
		
		return response;
	}
	
	@Override
	public ListMonitoringEmeteraiResponse getListMonitoringEmeteraiEmbed(ListMonitoringEmeteraiEmbedRequest request, AuditContext audit)
			throws ParseException {
		ListMonitoringEmeteraiResponse response = new ListMonitoringEmeteraiResponse();
		commonValidatorLogic.validateNotNull(request.getMsg(), getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY, null, audit), StatusCode.EMBED_MSG_EMPTY);
		
		EmbedMsgBean msgBean = commonLogic.decryptMessageToClass(request.getMsg(), audit, EmbedMsgBean.class);
		commonValidatorLogic.validateNotNull(msgBean, getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_INVALID, null, audit), StatusCode.EMBED_MSG_INVALID);

		commonValidatorLogic.validateNotNull(msgBean.getTenantCode(), getMessage(GlobalKey.MESSAGE_ERROR_TENANT_CODE_EMPTY, null, audit), StatusCode.TENANT_NOT_FOUND);
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(msgBean.getTenantCode());
		commonValidatorLogic.validateNotNull(tenant, getMessage(GlobalKey.MESSAGE_ERROR_SALDO_TENANT_NOT_EXIST, new Object[] {msgBean.getTenantCode()}, audit), StatusCode.TENANT_NOT_FOUND);
		
		request.setTenantCode(tenant.getTenantCode());
		audit.setCallerId(msgBean.getEmail());
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);

		Date dateStart = null;
		Date dateEnd = null;

		if (!isDateRangeValid(request.getTanggalDokumenMulai(), request.getTanggalDokumenSampai(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { CONST_LIST_INVITATION }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}

		if (StringUtils.isNotBlank(request.getTanggalDokumenMulai())) {
			dateStart = MssTool.formatStringToDate(request.getTanggalDokumenMulai() + GlobalVal.SOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);

		}

		if (StringUtils.isNotBlank(request.getTanggalDokumenSampai())) {
			dateEnd = MssTool.formatStringToDate(request.getTanggalDokumenSampai() + GlobalVal.EOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		Integer totalData = daoFactory.getDocumentDao().countListMonitoringEmbed(min, max, request.getNomorDokumen(), request.getTipeDokumen(), 
				request.getHasilStamping(), request.getJenisDokumen(), request.getTemplateDokumen(), request.getNoSN(), request.getTenantCode(),dateStart, dateEnd);
		
		double totalPage = Math.ceil((double) totalData / maxRow);
		
		List<MonitoringBean> listMonitoringbean = new ArrayList<>();
		List<Map<String, Object>> listMonitoring = daoFactory.getDocumentDao().getListMonitoringEmbed(min, max, request.getNomorDokumen(), request.getTipeDokumen(), 
				request.getHasilStamping(), request.getJenisDokumen(), request.getTemplateDokumen(), request.getNoSN(), request.getTenantCode(),dateStart, dateEnd);
		Iterator<Map<String, Object>> itr = listMonitoring.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			
			BigInteger idDocumentD = (BigInteger) map.get("d12");
			BigInteger idDocumentH = (BigInteger) map.get("d13");
			
			TrDocumentHStampdutyError trDocumentHStampdutyError = daoFactory.getDocumentDao().getDocumentHStampdutyErrorNewTran(idDocumentH.longValue(), idDocumentD.longValue());
			if(trDocumentHStampdutyError == null) {
				trDocumentHStampdutyError = daoFactory.getDocumentDao().getDocumentHStampdutyErrorNewTran(idDocumentH.longValue(), null);
			}
			
			MonitoringBean bean = new MonitoringBean();
			bean.setNomorDokumen((String) map.get("d0"));
			bean.setTanggalDokumen((String) map.get("d1"));
			bean.setNamaDokumen((String) map.get("d2"));
			bean.setJenisDokumen((String) map.get("d3"));
			bean.setTipeDokumen((String) map.get("d4"));
			bean.setNominalDokumen((String) map.get("d5"));
			bean.setTemplateDokumen((String) map.get("d6"));
			bean.setHasilStamping((String) map.get("d7"));
			bean.setNoSN((String) map.get("d8"));
			bean.setProsesMaterai((String) map.get("d9"));
			bean.setNoIdentitas((String) map.get("d10"));
			bean.setNamaIdentitas((String) map.get("d11"));
			
			//error message
			boolean doNotSetErrorMessage = trDocumentHStampdutyError == null || GlobalVal.HASIL_STAMPING_SUCCESS.equals(map.get("d7")) || GlobalVal.HASIL_STAMPING_IN_PROGRESS.equals(map.get("d7"));
			bean.setErrorMessage(doNotSetErrorMessage ? "" : trDocumentHStampdutyError.getErrorMessage());
			
			// is success
			bean.setIsSuccess(GlobalVal.HASIL_STAMPING_SUCCESS.equals(map.get("d7")) ? "1" : "0");
			
			//nomorDoc and nomorSNencrypt
			String nomorDoc = (String) map.get("d0");
			String nomorDokumenEcrypt = StringUtils.isNotBlank(nomorDoc) ? commonLogic.encryptMessageToString((String) map.get("d0"), audit) : "";
			
			String nomorSN = (String) map.get("d8");
			String nomorSNEcrypt = StringUtils.isNotBlank(nomorSN) ? commonLogic.encryptMessageToString((String) map.get("d8"), audit) : "";
			
			TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailById(idDocumentD.longValue());
			String docIdEncrypt = commonLogic.encryptMessageToString(docD.getDocumentId(), audit);
			bean.setEncryptedDocId(docIdEncrypt);
			
			bean.setNomorDokumenEncrypt(nomorDokumenEcrypt);
			bean.setNoSNEncrypt(commonLogic.encryptMessageToString(nomorSNEcrypt, audit));
			listMonitoringbean.add(bean);
		}
		
		
		response.setListMonitoring(listMonitoringbean);
		response.setPage(request.getPage());
		response.setTotalResult(totalData);
		response.setTotalPage((int) totalPage);
		return response;
	}

	@Override
	public TaxExcelReportResponse exportTaxReportNormal(TaxExcelReportRequest request, AuditContext audit) throws ParseException{
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(request.getTenantCode());
		if (null == tenant) {
			if (StringUtils.isBlank(request.getTenantCode())) {
				throw new TenantException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_TENANT_CODE_EMPTY
						, null, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
			} else {
				throw new TenantException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_TENANT_NOT_EXIST
						, new Object[] {request.getTenantCode()}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_NOT_FOUND);
			}
		}
		request.setTenantCode(tenant.getTenantCode());
		
		Date dateStart = null;
		Date dateEnd = null;
		
		if (!isDateRangeValid(request.getTanggalDokumenMulai(), request.getTanggalDokumenSampai(), audit)) {
			throw new DocumentException(
					this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_DATE_RANGE,
							new Object[] { "dokumen" }, this.retrieveLocaleAudit(audit)),
					ReasonDocument.INVALID_DATE_RANGE);
		}

		if (StringUtils.isNotBlank(request.getTanggalDokumenMulai())) {
			dateStart = MssTool.formatStringToDate(request.getTanggalDokumenMulai() + GlobalVal.SOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);

		}

		if (StringUtils.isNotBlank(request.getTanggalDokumenSampai())) {
			dateEnd = MssTool.formatStringToDate(request.getTanggalDokumenSampai() + GlobalVal.EOD_TIME,
					GlobalVal.DATE_TIME_FORMAT_SEC);
		}
		
		List<TaxReportBean> listStampDuty = daoFactory.getDocumentDao().getListMonitoringReport(request.getNomorDokumen(), request.getTipeDokumen(), 
				request.getHasilStamping(), request.getJenisDokumen(), request.getTemplateDokumen(), request.getNoSN(), request.getTenantCode(), request.getTaxType(), request.getCabang(), dateStart, dateEnd);
				
		TaxExcelReportResponse response = new TaxExcelReportResponse();
		response.setBase64ExcelReport(this.generateTaxExcelFile(listStampDuty));
		response.setFilename(this.generateTaxExcelFilename(request));
		
		return response;
	}
	
	private String generateTaxExcelFile(List<TaxReportBean> listTax) {
		byte[] excelByteArray = null;
		try {
			excelByteArray = excelLogic.generateEmbed((workbook, styleBoldText) ->
				this.createTaxExcelSheet(workbook, styleBoldText, listTax));
		} catch (IOException e) {
			// perlu ganti errornya
			throw new StampDutyException("", ReasonStampDuty.GENERATE_SDT_REPORT_ERROR);
		}
		
		return Base64.getEncoder().encodeToString(excelByteArray);
	}
	
	private void createTaxExcelSheet(XSSFWorkbook workbook, XSSFCellStyle styleBoldText,
			List<TaxReportBean> listTax) {
		XSSFSheet mainSheet = workbook.createSheet("Laporan Pajak");
		
		String[] column = new String[] {"No", "Nomor Dokumen", "Jenis Dokumen", "Nilai Dokumen", "Tgl Dokumen", "Indentitas",
				"Nomor Identitas", "Nama Terutang", "Bea Meterai Dipungut", "Authorized Distributor"};
		
		for (int i = 0; i < column.length; i++) {
			switch (i) {
				case 0: // No
					mainSheet.setColumnWidth(i, 2000);
					break;
				case 1: // No Dokumen
					mainSheet.setColumnWidth(i, 5000);
					break;
				case 2: // Jenis Dokumen
					mainSheet.setColumnWidth(i, 4000);
					break;
				case 3: // Nilai Dokumen
					mainSheet.setColumnWidth(i, 4000);
					break;
				case 4: // Tgl Dokumen
					mainSheet.setColumnWidth(i, 4000);
					break;
				case 5: // Identitas
					mainSheet.setColumnWidth(i, 4000);
					break;
				case 6: // Nomor Identitas
					mainSheet.setColumnWidth(i, 5000);
					break;
				case 7: // Nama Terutang
					mainSheet.setColumnWidth(i, 7000);
					break;
				case 8: // Bea Meterai Dipungut
					mainSheet.setColumnWidth(i, 6000);
					break;
				case 9: // Authorized Distributor
					mainSheet.setColumnWidth(i, 6000);
					break;
				default:
					mainSheet.setColumnWidth(i, 5000);
					break;
			}
		}
		
		XSSFRow rowOne = mainSheet.createRow(0);
		for (int i = 0; i < column.length; i++) {
			XSSFCell cell = rowOne.createCell(i);
			cell.setCellValue(column[i]);
			cell.setCellStyle(styleBoldText);
		}
		
		for (int i = 0 ; i < listTax.size(); i++) {
			XSSFRow row = mainSheet.createRow(i+1);
			XSSFCell cellOne = row.createCell(0);
			XSSFCell cellTwo = row.createCell(1);
			XSSFCell cellThree = row.createCell(2);
			XSSFCell cellFour = row.createCell(3);
			XSSFCell cellFive = row.createCell(4);
			XSSFCell cellSix = row.createCell(5);
			XSSFCell cellSeven = row.createCell(6);
			XSSFCell cellEight = row.createCell(7);
			XSSFCell cellNine = row.createCell(8);
			XSSFCell cellTen = row.createCell(9);

			TaxReportBean bean = listTax.get(i);
			cellOne.setCellValue((double) i + 1);
			cellTwo.setCellValue(bean.getNomorDokumen());
			cellThree.setCellValue(bean.getJenisDokumen());
			cellFour.setCellValue(bean.getNominalDokumen().doubleValue());
			cellFive.setCellValue(bean.getTanggalDokumen());
			cellSix.setCellValue(bean.getIdentitas());
			cellSeven.setCellValue(bean.getNoIdentitas());
			cellEight.setCellValue(bean.getNamaIdentitas());
			cellNine.setCellValue(bean.getStampDutyFee());
			cellTen.setCellValue("PAJAKKU");
		}
	}
	
	private String generateTaxExcelFilename(TaxExcelReportRequest request) {
		StringBuilder filename = new StringBuilder();
		filename.append("TAX_REPORT");
		if (StringUtils.isNotBlank(request.getTenantCode())) {
			filename.append("_").append(request.getTenantCode());
		}
		filename.append(" ").append(MssTool.formatDateToStringIn(new Date(), GlobalVal.DATE_FORMAT_SDT_REPORT));
		filename.append(".xlsx");
		return filename.toString();
	}
}
