package com.adins.esign;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.impl.provider.EsignAuthenticationToken;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.framework.persistence.dao.model.AuditContext;

@SuppressWarnings("deprecation")
@Component("esignSecurityServices")
public class EsignSecurityServices extends BaseLogic {
	
	@Autowired private UserValidatorLogic userValidatorLogic;
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean isValidTenant(String tenantCode, Authentication authentication) {
		//check wheter tenant from request is identical with auth object
		OAuth2Authentication auth = (OAuth2Authentication)authentication;
		EsignAuthenticationToken esignToken = (EsignAuthenticationToken) auth.getUserAuthentication();
		
		Map<String, List<String>> mapTenantRole = esignToken.getMapTenantRole();
		return mapTenantRole.containsKey(tenantCode);
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean isRoleTenant(String roles, String tenantCode, Authentication authentication) {
		//check whether role needed exist within role of tenant setting
		OAuth2Authentication auth = (OAuth2Authentication)authentication;
		EsignAuthenticationToken esignToken = (EsignAuthenticationToken) auth.getUserAuthentication();
		
		Map<String, List<String>> mapTenantRole = esignToken.getMapTenantRole();
		List<String> roleListByTenant = mapTenantRole.get(tenantCode);
		return roleListByTenant.contains(roles);
	}
	
	// Username can be either email or phone
	public boolean isValidUser(String username, Authentication authentication) {
		String authenticatedLoginId = (String) authentication.getPrincipal();
		AuditContext audit = new AuditContext(username);
		
		AmMsuser authenticatedUser = null;
		if (StringUtils.isNumeric(authenticatedLoginId)) {
			authenticatedUser = userValidatorLogic.validateGetUserByPhone(authenticatedLoginId, false, audit);
		} else {
			authenticatedUser = userValidatorLogic.validateGetUserByEmailv2(authenticatedLoginId, false, audit);
		}
		
		AmMsuser requestingUser = null;
		if (StringUtils.isNumeric(username)) {
			requestingUser = userValidatorLogic.validateGetUserByPhone(username, false, audit);
		} else {
			requestingUser = userValidatorLogic.validateGetUserByEmailv2(username, false, audit);
		}
				
		if (null == requestingUser) {
			return false;
		}
		
		return requestingUser.getIdMsUser() == authenticatedUser.getIdMsUser();
	}
	
}
