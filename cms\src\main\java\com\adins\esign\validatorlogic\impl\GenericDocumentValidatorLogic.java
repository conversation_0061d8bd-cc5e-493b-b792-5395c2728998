package com.adins.esign.validatorlogic.impl;

import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsPeruriDocType;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDSign;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.custom.SignLocationBean;
import com.adins.esign.model.custom.StampingLocationBean;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.validatorlogic.api.DocumentValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.StampDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentRequest;
import com.adins.esign.webservices.model.external.InsertStampingMateraiExternalRequest;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.EmbedMsgException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.EmbedMsgException.ReasonEmbedMsg;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.exceptions.ParameterException;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericDocumentValidatorLogic extends BaseLogic implements DocumentValidatorLogic {
	
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private BalanceValidatorLogic balanceValidatorLogic;
	
	private static final String MSG_VAR_CANNOTBEEMPTY = "businesslogic.document.mandatorycannotbeempty";
	
	@Override
	public void validateAgreementForRetryStamp(TrDocumentH documentH, AuditContext audit) {
		if (null == documentH) {
			throw new DocumentException(getMessage("businesslogic.document.documentnotfound", null, audit), ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
		}
		short prosesMaterai = documentH.getProsesMaterai();
		
		if ((1 != prosesMaterai) && (51 != prosesMaterai) && (321 != prosesMaterai) && (521 != prosesMaterai)) {
			String status = StringUtils.EMPTY;
			if (0 == prosesMaterai) {
				status = "Not started";
			} else if (2 == prosesMaterai || 52 == prosesMaterai || 322 == prosesMaterai || 522 == prosesMaterai
					|| 5 == prosesMaterai || 55 == prosesMaterai || 325 == prosesMaterai || 525 == prosesMaterai) {
				status = "In progress";
			} else if (3 == prosesMaterai || 53 == prosesMaterai || 323 == prosesMaterai || 523 == prosesMaterai) {
				status = "Success";
			}
			
			String[] params = {status};
			throw new DocumentException(getMessage("businesslogic.emeterai.cannotretry", params, audit), ReasonDocument.DOCUMENT_FILE_NOT_EXISTS);
		}
	}

	@Override
	public void validateStampDocumentRequest(StampDocumentRequest request, AuditContext audit) {
		// Validasi documentFile
		if (StringUtils.isBlank(request.getDocumentFile())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"documentFile"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		try {
			Base64.getDecoder().decode(request.getDocumentFile());
		} catch (Exception e) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.mustbase64", null,
					retrieveLocaleAudit(audit)), ReasonDocument.DOCUMENT_FILE_INACCESSIBLE);
		}
		
		if (StringUtils.isBlank(request.getDocumentTemplateCode())) {
			List<StampingLocationBean> stampingLocations = request.getStampingLocations();
			if (CollectionUtils.isEmpty(stampingLocations)) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"stampingLocations"}, audit), ReasonParam.MANDATORY_PARAM);
			}
			
			for (StampingLocationBean stampingLocationBean : stampingLocations) {
				// Validasi stampPage
				if (StringUtils.isBlank(stampingLocationBean.getStampPage())) {
					throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"stampPage"}, audit), ReasonParam.MANDATORY_PARAM);
				}
				try {
					Integer.parseInt(stampingLocationBean.getStampPage());
				} catch (Exception e) {
					throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_NUMERIC, new String[] {"stampPage"}, audit), ReasonParam.MUST_BE_NUMERIC);
				}
				
				if (null == stampingLocationBean.getStampLocation()) {
					throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"stampingLocation"}, audit), ReasonParam.MANDATORY_PARAM);
				}
				
				SignLocationBean loc = stampingLocationBean.getStampLocation();
				
				// Validasi llx
				if (StringUtils.isBlank(loc.getLlx())) {
					throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"llx"}, audit), ReasonParam.MANDATORY_PARAM);
				}
				try {
					Double.parseDouble(loc.getLlx());
				} catch (Exception e) {
					throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_NUMERIC, new String[] {"llx"}, audit), ReasonParam.MUST_BE_NUMERIC);
				}
				
				// Validasi lly
				if (StringUtils.isBlank(loc.getLly())) {
					throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"lly"}, audit), ReasonParam.MANDATORY_PARAM);
				}
				try {
					Double.parseDouble(loc.getLly());
				} catch (Exception e) {
					throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_NUMERIC, new String[] {"lly"}, audit), ReasonParam.MUST_BE_NUMERIC);
				}
				
				// Validasi urx
				if (StringUtils.isBlank(loc.getUrx())) {
					throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"urx"}, audit), ReasonParam.MANDATORY_PARAM);
				}
				try {
					Double.parseDouble(loc.getUrx());
				} catch (Exception e) {
					throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_NUMERIC, new String[] {"urx"}, audit), ReasonParam.MUST_BE_NUMERIC);
				}
				
				// Validasi ury
				if (StringUtils.isBlank(loc.getUry())) {
					throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"ury"}, audit), ReasonParam.MANDATORY_PARAM);
				}
				try {
					Double.parseDouble(loc.getUry());
				} catch (Exception e) {
					throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_NUMERIC, new String[] {"ury"}, audit), ReasonParam.MUST_BE_NUMERIC);
				}
			}
		}
		
		if (StringUtils.isBlank(request.getDocumentNumber())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"documentNumber"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		if (StringUtils.isBlank(request.getOfficeCode())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"officeCode"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		if (StringUtils.isBlank(request.getOfficeName())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"officeName"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		if (StringUtils.isNotBlank(request.getRegionCode()) && StringUtils.isBlank(request.getRegionName())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"regionName"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		if (StringUtils.isNotBlank(request.getBusinessLineCode()) && StringUtils.isBlank(request.getBusinessLineName())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"businessLineName"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		if (StringUtils.isBlank(request.getDocName())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"docName"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		// Validasi docDate
		if (StringUtils.isBlank(request.getDocDate())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"docDate"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
			sdf.setLenient(false);
			sdf.parse(request.getDocDate());
		} catch (Exception e) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_INCORRENT_DATE_FORMAT,
					new String[] {"docDate", GlobalVal.DATE_FORMAT}, audit), ReasonParam.INCORRENT_DATE_FORMAT);
		}
		
		if (StringUtils.isBlank(request.getDocTypeCode())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"docTypeCode"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		if (StringUtils.isBlank(request.getPeruriDocTypeId())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"peruriDocTypeId"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		
		// Validasi nomimal
		if (StringUtils.isBlank(request.getDocNominal())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"docNominal"}, audit), ReasonParam.MANDATORY_PARAM);
		}
		try {
			Integer.parseInt(request.getDocNominal());
		} catch (Exception e) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_NUMERIC, new String[] {"docNominal"}, audit), ReasonParam.MUST_BE_NUMERIC);
		}
		
		if (!GlobalVal.TAX_TYPE_PEMUNGUT.equals(request.getTaxType()) && !GlobalVal.TAX_TYPE_NON_PEMUNGUT.equals(request.getTaxType())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ACTION,
					new String[] {"taxType", GlobalVal.TAX_TYPE_PEMUNGUT + " / " + GlobalVal.TAX_TYPE_NON_PEMUNGUT},  audit), ReasonParam.MANDATORY_PARAM);
		}
		
		
		if (GlobalVal.TAX_TYPE_PEMUNGUT.equals(request.getTaxType())) {
			// Validasi idType
			if (StringUtils.isBlank(request.getIdType())) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {GlobalVal.CONST_VALIDATE_STAMP_ID_TYPE_PARAM}, audit), ReasonParam.MANDATORY_PARAM);
			}
			if (!"KTP".equals(request.getIdType()) && !"NPWP".equals(request.getIdType())) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ACTION,
						new String[] {GlobalVal.CONST_VALIDATE_STAMP_ID_TYPE_PARAM, "KTP / NPWP"},  audit), ReasonParam.MANDATORY_PARAM);
			}
			
			// Validasi idNo
			if (StringUtils.isBlank(request.getIdNo())) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"idNo"}, audit), ReasonParam.MANDATORY_PARAM);
			}
			if ("KTP".equals(request.getIdType()) && request.getIdNo().length() != 16) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ACTION, new String[] {"KTP", "16 digit"}, audit), ReasonParam.MANDATORY_PARAM);
			}
			if ("NPWP".equals(request.getIdType())
					&& (request.getIdNo().length() < 15 || request.getIdNo().length() > 16)) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ACTION, new String[] {"NPWN", "15-16 digit"}, audit), ReasonParam.MANDATORY_PARAM);
			}
			try {
				Long.parseLong(request.getIdNo());
			} catch (Exception e) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_NUMERIC, new String[] {"idNo"}, audit), ReasonParam.MUST_BE_NUMERIC);
			}
			
			// Validasi taxOwedsName
			if (StringUtils.isBlank(request.getTaxOwedsName())) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"taxOwedsName"}, audit), ReasonParam.MANDATORY_PARAM);
			}
		}
	}
	
	private boolean isDocumentInOfficeOrUserInSigner(AmMsuser user, String documentId, String officeCode) {
		TrDocumentD doc = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
		
		if (StringUtils.isNotBlank(officeCode)) {
			return doc.getTrDocumentH().getMsOffice().getOfficeCode().equalsIgnoreCase(officeCode);
		}
		
		List<TrDocumentDSign> listDocSign = daoFactory.getDocumentDao().getDocumentDSignByIdDocumentDAndIdUser(doc.getIdDocumentD(), user.getIdMsUser());
		return !listDocSign.isEmpty();
	}

	@Override
	public void validateViewDocumentRequest(ViewDocumentRequest request, AuditContext audit) {
		if (GlobalVal.SYSTEM_LOGINID.equals(audit.getCallerId())
				|| GlobalVal.DIGI_LOGINID.equals(audit.getCallerId())
				|| GlobalVal.SCHEDULER_WORKFLOW.equals(audit.getCallerId())
				|| GlobalVal.CALLER_ID_MONITORING.equals(audit.getCallerId())
				|| GlobalVal.SCHEDULER.equals(audit.getCallerId())) {
			return;
		}
		
		AmMsuser user;
		if (StringUtils.isNumeric(audit.getCallerId())) {
			user = userValidatorLogic.validateGetUserByPhone(audit.getCallerId(), true, audit);
		} else {
			user = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), true, audit);
		}
		
		// FIXME: Validasi role perlu diubah untuk case user multitenant
		List<AmMsrole> roles = daoFactory.getRoleDao().getListRoleByIdUser(user.getIdMsUser());
		boolean isRoleValid = false;
		for (AmMsrole amMsrole : roles) {
			// If customer / user, check if document is in its office  or user is one of the signer
			if (GlobalVal.ROLE_CUSTOMER.equalsIgnoreCase(amMsrole.getRoleCode())
					&& !isDocumentInOfficeOrUserInSigner(user, request.getDocumentId(), request.getOfficeCode())) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_CUST_INACCESSIBLE, 
						null, this.retrieveLocaleAudit(audit)) , ReasonDocument.DOCUMENT_FILE_INACCESSIBLE);
			}
			// If BM, check if document is in its office  or user is one of the signer
			if (GlobalVal.ROLE_BM_MF.equalsIgnoreCase(amMsrole.getRoleCode())
					&& !isDocumentInOfficeOrUserInSigner(user, request.getDocumentId(), request.getOfficeCode())) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_BM_INACCESSIBLE,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.DOCUMENT_FILE_INACCESSIBLE);
			}
			// If Admin Credit, check if document is in its office  or user is one of the signer
			if (GlobalVal.ROLE_ADMIN_CREDIT.equalsIgnoreCase(amMsrole.getRoleCode())
					&& !isDocumentInOfficeOrUserInSigner(user, request.getDocumentId(), request.getOfficeCode())) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_USER_CANNOT_ACCESS_DOC_1,
						null, this.retrieveLocaleAudit(audit)), ReasonDocument.DOCUMENT_FILE_INACCESSIBLE);
			}
			if (GlobalVal.ROLE_CUSTOMER.equalsIgnoreCase(amMsrole.getRoleCode())
					|| GlobalVal.ROLE_BM_MF.equalsIgnoreCase(amMsrole.getRoleCode())
					|| GlobalVal.ROLE_ADMIN_CREDIT.equalsIgnoreCase(amMsrole.getRoleCode())) {
				isRoleValid = true;
				break;
			}
		}
		if (!isRoleValid) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_USER_CANNOT_ACCESS_DOC_1,
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.DOCUMENT_FILE_INACCESSIBLE);
		}
	}

	@Override
	public void validateViewDocumentEmbedRequest(ViewDocumentRequest request, AuditContext audit) {
		if (StringUtils.isBlank(request.getMsg())) {
			throw new EmbedMsgException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_EMBED_MSG_EMPTY,
					null, this.retrieveLocaleAudit(audit)), ReasonEmbedMsg.ENCRYPTED_MSG_EMPTY);
		}
		if (StringUtils.isBlank(request.getDocumentId())) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {"Document Id"}, this.retrieveLocaleAudit(audit)), ReasonDocument.DOCUMENT_ID_EMPTY);
		}
	}
	
	private boolean isAgreementNeedStamp(TrDocumentH documentH) {
		List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderId(documentH.getIdDocumentH());
		for (TrDocumentD document : documents) {
			if  (document.getTotalMaterai() > 0) {
				return true;
			}
		}
		return false;
	}

	@Override
	public void validateAgreementForStartStamp(TrDocumentH documentH, MsTenant tenant, AuditContext audit) {
		
		if (null != documentH.getProsesMaterai() && documentH.getProsesMaterai() != 0) {
			// Dokumen sudah dalam proses stamping!
			throw new ParameterException(getMessage("businesslogic.stampduty.onstampingprocess", null, audit), ReasonParam.INVALID_CONDITION);
		}
		
		boolean needStamp = isAgreementNeedStamp(documentH);
		if (!needStamp) {
			// Dokumen tidak perlu dilakukan stamping
			throw new ParameterException(getMessage("businesslogic.stampduty.doesnotneedstamp", null, audit), ReasonParam.INVALID_CONDITION);
		}
		
		if ("1".equals(documentH.getAutomaticStampingAfterSign())) {
			// Tidak bisa mulai stamping. Nanti akan otomatis dimulai.
			throw new ParameterException(getMessage("businesslogic.stampduty.flaggedforautomaticstamp", null, audit), ReasonParam.INVALID_CONDITION);
		}
		
		if (!documentH.getTotalDocument().equals(documentH.getTotalSigned())) {
			// Dokumen perlu ditandatangan sebelum di stamping!
			throw new ParameterException(getMessage("businesslogic.stampduty.needsignbeforestamp", null, audit), ReasonParam.INVALID_CONDITION);
		}
		
		MsVendor stampVendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_SDT, tenant, stampVendor, audit);
	}
	
	@Override
	public void validateAgreementForStartStampEmbed(TrDocumentH documentH, MsTenant tenant, AuditContext audit) {
		
		if (null != documentH.getProsesMaterai() && documentH.getProsesMaterai() != 0) {
			// Dokumen sudah dalam proses stamping!
			throw new ParameterException(getMessage("businesslogic.stampduty.onstampingprocess", null, audit), ReasonParam.INVALID_CONDITION);
		}
		
		boolean needStamp = isAgreementNeedStamp(documentH);
		if (!needStamp) {
			// Dokumen tidak perlu dilakukan stamping
			throw new ParameterException(getMessage("businesslogic.stampduty.doesnotneedstamp", null, audit), ReasonParam.INVALID_CONDITION);
		}
		
		if (!documentH.getTotalDocument().equals(documentH.getTotalSigned())) {
			// Dokumen perlu ditandatangan sebelum di stamping!
			throw new ParameterException(getMessage("businesslogic.stampduty.needsignbeforestamp", null, audit), ReasonParam.INVALID_CONDITION);
		}
		
		MsVendor stampVendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		balanceValidatorLogic.validateBalanceAvailability(GlobalVal.CODE_LOV_BALANCE_TYPE_SDT, tenant, stampVendor, audit);
	}


	@Override
	public boolean isSignSequenceValid(TrDocumentD document, AmMsuser user, AuditContext audit) {
		if (!"1".equals(document.getIsSequence())) {
			return true;
		}
		
		// Ambil penanda tangan dokumen yang paling pertama perlu ttd
		TrDocumentDSign documentDSign = daoFactory.getDocumentDao().getUnsignedDocumentDSign(document);
		if (null == documentDSign) {
			return true;
		}
		
		return documentDSign.getAmMsuser().getIdMsUser() == user.getIdMsUser();
		
	}

	@Override
	public void validateSignSequence(TrDocumentD document, AmMsuser user, AuditContext audit) {
		if (!"1".equals(document.getIsSequence())) {
			return;
		}
		
		// Ambil penanda tangan dokumen yang paling pertama perlu ttd
		TrDocumentDSign documentDSign = daoFactory.getDocumentDao().getUnsignedDocumentDSign(document);
		if (null == documentDSign) {
			return;
		}
		
		if (documentDSign.getAmMsuser().getIdMsUser() != user.getIdMsUser()) {
			throw new ParameterException(getMessage("businesslogic.document.mustfirstlysignbyother",
					new String[] {documentDSign.getAmMsuser().getFullName()}, audit), ReasonParam.INVALID_CONDITION);
			
		}
	}

	@Override
	public boolean isDocumentHSignCompleted(TrDocumentH documentH) {
		List<TrDocumentD> documents = daoFactory.getDocumentDao().getListDocumentDetailByDocumentHeaderId(documentH.getIdDocumentH());
		for (TrDocumentD document : documents) {
			if (!document.getTotalSign().equals(document.getTotalSigned())) {
				return false;
			}
		}
		
		return true;
	}

	@Override
	public void validateRefNumber(String refNumber, AuditContext audit) {
		    if (StringUtils.isBlank(refNumber)) {
		        throw new DocumentException(getMessage(GlobalKey.MESSAGE_ERROR_DOC_REFNOEMPTY, null, audit),
		                ReasonDocument.REFERENCE_NO_EMPTY);
		    }		
	}
	
	@Override
	public void validateRequestForExternalMateraiStamping(InsertStampingMateraiExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		MsDocTemplate dt = daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(request.getDocumentTemplateCode(), tenant.getTenantCode());
		if (StringUtils.isNotBlank(request.getDocumentTemplateCode()) && null == dt) {
			String[] errParams = { "Document Template Code " + request.getDocumentTemplateCode() };
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, errParams, retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}
		
		if (StringUtils.isBlank(request.getDocumentTemplateCode()) && (request.getStampingLocations() == null || request.getStampingLocations().isEmpty())) {
			throw new DocumentException(getMessage("businesslogic.insertstamping.doctemplateorloc", null, audit), ReasonDocument.PARAM_INVALID);
		}
		
		int totalStamp = 0;
		if (null != dt) {
			List<MsDocTemplateSignLoc> sls = daoFactory.getDocumentDao().getListSignLocation(dt.getDocTemplateCode(), GlobalVal.CODE_LOV_SIGN_TYPE_SDT, tenant.getTenantCode());
			totalStamp = sls.size();
		} else {
			totalStamp = request.getStampingLocations().size();
		}
		
		String var;
		if (StringUtils.isBlank(request.getDocumentFile())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.file", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		if (StringUtils.isBlank(request.getDocDate())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docdate", null, this.retrieveLocaleAudit(audit));
			throw new DocumentException(this.messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonDocument.VAR_EMPTY);
		}
		
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(GlobalVal.DATE_FORMAT);
			sdf.setLenient(false);
			sdf.parse(request.getDocDate());
		} catch (Exception e) {
			throw new ParameterException(getMessage("businesslogic.error.parsedateformat", null, audit), ReasonParam.INVALID_FORMAT);
		}
		
		if (StringUtils.isBlank(request.getOfficeCode())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.officeCode", null, this.retrieveLocaleAudit(audit));
			throw new ParameterException(this.messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonParam.MANDATORY_PARAM);
		}
		
		if (StringUtils.isBlank(request.getDocumentNumber())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docnumber", null, this.retrieveLocaleAudit(audit));
			throw new ParameterException(this.messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonParam.MANDATORY_PARAM);
		}
		
		if (StringUtils.isBlank(request.getDocName())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docname", null, this.retrieveLocaleAudit(audit));
			throw new ParameterException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var }, this.retrieveLocaleAudit(audit)), ReasonParam.MANDATORY_PARAM);
		}
		
		if (StringUtils.isBlank(request.getPeruriDocTypeId())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.doctypeperuri", null, this.retrieveLocaleAudit(audit));
			throw new ParameterException(this.messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonParam.MANDATORY_PARAM);
		}
		
		MsPeruriDocType peruriDocType = daoFactory.getPeruriDocTypeDao().getPeruriDocTypeByDocId(request.getPeruriDocTypeId());
		if (null == peruriDocType) {
			String[] errParams = { "Peruri Doc Type " + request.getPeruriDocTypeId() };
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, errParams, retrieveLocaleAudit(audit)), ReasonDocument.DOC_TYPE_NOT_EXIST);
		}
		
		if (StringUtils.isBlank(request.getDocTypeCode())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.doctype", null, this.retrieveLocaleAudit(audit));
			throw new ParameterException(messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] { var }, this.retrieveLocaleAudit(audit)), ReasonParam.MANDATORY_PARAM);
		}
		
		MsLov lovDocType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_DOC_TYPE, request.getDocTypeCode());
		if (null == lovDocType) {
			String[] errParams = { "Doc Type " + request.getDocTypeCode() };
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND, errParams, retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}
		
		if (StringUtils.isBlank(request.getDocNominal())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docnominal", null, this.retrieveLocaleAudit(audit));
			throw new ParameterException(this.messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonParam.MANDATORY_PARAM);
		}
		
		try {
			Integer.parseInt(request.getDocNominal());
		} catch (Exception e) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.docnominal", null, this.retrieveLocaleAudit(audit));
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_NUMERIC, new String[] {var}, audit), ReasonParam.MUST_BE_NUMERIC);
		}
		
		if (StringUtils.isBlank(request.getTaxType())) {
			var = messageSource.getMessage("businesslogic.insertstamping.var.taxtype", null, this.retrieveLocaleAudit(audit));
			throw new ParameterException(this.messageSource.getMessage(MSG_VAR_CANNOTBEEMPTY, new Object[] {var}, this.retrieveLocaleAudit(audit)), ReasonParam.MANDATORY_PARAM);
		}
		
		if (!GlobalVal.TAX_TYPE_NON_PEMUNGUT.equals(request.getTaxType()) && !GlobalVal.TAX_TYPE_PEMUNGUT.equals(request.getTaxType())) {
			throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ACTION,
					new String[] {"taxType", "Pemungut / Non Pemungut"},  audit), ReasonParam.MANDATORY_PARAM);
		}
		
		if (GlobalVal.TAX_TYPE_PEMUNGUT.equals(request.getTaxType())) {
			AmGeneralsetting gsUname = daoFactory.getGeneralSettingDao().getGsObjByCodeAndTenant(AmGlobalKey.GENERALSETTING_PAJAKKU_USERNAME, tenant);
			if (null == gsUname) {
				throw new ParameterException(getMessage("businesslogic.external.pemungutaccnotavailable", null, audit), ReasonParam.UNKNOWN);
			}
			
			AmGeneralsetting gsPass = daoFactory.getGeneralSettingDao().getGsObjByCodeAndTenant(AmGlobalKey.GENERALSETTING_PAJAKKU_PASSWORD, tenant);
			if (null == gsPass) {
				throw new ParameterException(getMessage("businesslogic.external.pemungutaccnotavailable", null, audit), ReasonParam.UNKNOWN);
			}
			
			// Validasi idType
			if (StringUtils.isBlank(request.getIdType())) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {GlobalVal.CONST_VALIDATE_STAMP_ID_TYPE_PARAM}, audit), ReasonParam.MANDATORY_PARAM);
			}
			if (!"KTP".equals(request.getIdType()) && !"NPWP".equals(request.getIdType())) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ACTION,
						new String[] {GlobalVal.CONST_VALIDATE_STAMP_ID_TYPE_PARAM, "KTP / NPWP"},  audit), ReasonParam.MANDATORY_PARAM);
			}
			
			// Validasi idNo
			if (StringUtils.isBlank(request.getIdNo())) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"idNo"}, audit), ReasonParam.MANDATORY_PARAM);
			}
			if ("KTP".equals(request.getIdType()) && request.getIdNo().length() != 16) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ACTION, new String[] {"KTP", "16 digit"}, audit), ReasonParam.MANDATORY_PARAM);
			}
			if ("NPWP".equals(request.getIdType())
					&& (request.getIdNo().length() < 15 || request.getIdNo().length() > 16)) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ACTION, new String[] {"NPWN", "15-16 digit"}, audit), ReasonParam.MANDATORY_PARAM);
			}
			try {
				Long.parseLong(request.getIdNo());
			} catch (Exception e) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_NUMERIC, new String[] {"idNo"}, audit), ReasonParam.MUST_BE_NUMERIC);
			}
			
			// Validasi taxOwedsName
			if (StringUtils.isBlank(request.getTaxOwedsName())) {
				throw new ParameterException(getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] {"taxOwedsName"}, audit), ReasonParam.MANDATORY_PARAM);
			}
			
			balanceValidatorLogic.validateBalanceAvailabilityWithAmount(GlobalVal.CODE_LOV_BALANCE_TYPE_SDT_POSTPAID, tenant, vendor, totalStamp, audit);
		} else {
			balanceValidatorLogic.validateBalanceAvailabilityWithAmount(GlobalVal.CODE_LOV_BALANCE_TYPE_SDT, tenant, vendor, totalStamp, audit);
		}
	}

	@Override
	public void validateDocumentsPrioritySequence(List<TrDocumentD> documents, AmMsuser user, AuditContext audit) {
		
		for (TrDocumentD document : documents) {
			if (!isDocumentTopPriorityForSigner(document, user)) {
				throw new DocumentException(getMessage("businesslogic.document.highestpriortyunsigned", null, audit), ReasonDocument.NOT_HIGHEST_PRIORITY);
			}
		}
	}

	@Override
	public boolean isDocumentTopPriorityForSigner(TrDocumentD document, AmMsuser user) {
		TrDocumentH documentH = document.getTrDocumentH();
		Short currentDocPriority = null != document.getPrioritySequence() ? document.getPrioritySequence() : 0;
		Short highestPriority = daoFactory.getDocumentDao().getHighestPriorityUnsignedDocument(documentH.getIdDocumentH(), user.getIdMsUser());
		
		Short zero = 0;
		if (currentDocPriority.equals(zero)) {
			currentDocPriority = Short.MAX_VALUE;
		}
		if (highestPriority.equals(zero)) {
			highestPriority = Short.MAX_VALUE;
		}
		
		// Priority with the lowest value is the top priority
		return currentDocPriority <= highestPriority;
	}
}
