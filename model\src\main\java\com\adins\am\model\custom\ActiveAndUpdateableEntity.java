package com.adins.am.model.custom;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
public abstract class ActiveAndUpdateableEntity extends UpdateableEntity {
	protected String isActive;
	public static final String IS_ACTIVE_HBM = "isActive";

	@Column(name = "is_active", length = 1)
	public String getIsActive() {
		return this.isActive;
	}

	public void setIsActive(String isActive) {
		this.isActive = isActive;
	}
}
