package com.adins.esign.dataaccess.impl;

import static org.junit.jupiter.api.Assertions.assertNotEquals;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import com.adins.esign.businesslogic.api.TestSetUpLogic;
import com.adins.esign.dataaccess.api.DocumentDao;
import com.adins.esign.model.custom.DocumentTemplateBean;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnableTransactionManagement
@Transactional
class DocumentDaoHbnTest {
	@Autowired private TestSetUpLogic testSetUpLogic;
	@Autowired private DocumentDao docDao;
//	
//	private MsLov lov;
//	
//	@BeforeEach
//	@Rollback(true)
//	public void setUp() {
//		lov = testSetUpLogic.setUpLovByGroupAndCode("JUNIT_LOVGROUP", "JUNIT_CODE");
//	}
//	
	@Order(1)
	@Test
	void getMsLovByCodeTest() {
		List<DocumentTemplateBean>  listDocTemp = docDao.getListDocumentTemplate("", "", "", "", 1, 25);
		assertNotEquals(0, listDocTemp.size());
	}
	
	@Test
	void getDocumentSignerListTest() {
		List<Map<String, Object>> signerList  = docDao.getDocumentSignerList("00155D0B-7502-9703-11EC-3C88377F9650");
		assertNotEquals(0, signerList.size());

	}
}
