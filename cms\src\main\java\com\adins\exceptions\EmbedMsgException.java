package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class EmbedMsgException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public enum ReasonEmbedMsg {
		ENCRYPTED_MSG_EMPTY,
		ENCRYPTED_MSG_INVALID,
		EMBED_SESSION_EXPIRED
	}
	
	private final ReasonEmbedMsg reason;
	
	public EmbedMsgException(ReasonEmbedMsg reason) {
		this.reason = reason;
	}
	
	public EmbedMsgException(String message, ReasonEmbedMsg reason) {
		super(message);
		this.reason = reason;
	}
	
	public EmbedMsgException(Throwable ex, ReasonEmbedMsg reason) {
		super(ex);
		this.reason = reason;
	}
	
	public EmbedMsgException(String message, Throwable ex, ReasonEmbedMsg reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	public ReasonEmbedMsg getReason() {
		return reason;
	}
	
	@Override
	public int getErrorCode() {
		if (this.reason != null) {
			switch (reason) {
			case ENCRYPTED_MSG_EMPTY:
				return StatusCode.EMBED_MSG_EMPTY;
			case ENCRYPTED_MSG_INVALID:
				return StatusCode.EMBED_MSG_INVALID;
			case EMBED_SESSION_EXPIRED:
				return StatusCode.EMBED_SESSION_EXPIRED;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}
}
