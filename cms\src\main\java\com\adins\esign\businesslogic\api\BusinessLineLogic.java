package com.adins.esign.businesslogic.api;

import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsTenant;
import com.adins.esign.webservices.model.BusinessLineListRequest;
import com.adins.esign.webservices.model.BusinessLineListResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface BusinessLineLogic {
	BusinessLineListResponse getBusinessLineList(BusinessLineListRequest request, AuditContext audit);
	MsBusinessLine insertUnregisteredBusinessLine(String businessLineCode, String businessLineName, MsTenant tenant, AuditContext audit);
}
