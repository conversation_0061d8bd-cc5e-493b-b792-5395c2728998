package com.adins.esign.webservices.frontend.endpoint;

import java.text.ParseException;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.ErrorReportLogic;
import com.adins.esign.webservices.frontend.api.ErrorReportService;
import com.adins.esign.webservices.model.ErrorHistoryActivationStatusRequest;
import com.adins.esign.webservices.model.ErrorHistoryActivationStatusResponse;
import com.adins.esign.webservices.model.ErrorHistoryRequest;
import com.adins.esign.webservices.model.ErrorHistoryResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

import io.swagger.annotations.Api;

@Component
@Path("/errorReport")
@Api(value = "ErrorReportService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericErrorReportServiceEndPoint implements ErrorReportService {
	
	@Autowired private ErrorReportLogic errorreportLogic;
	
	@Override
	@POST
	@Path("/s/errorHistory")
	public ErrorHistoryResponse getErrorHistory(ErrorHistoryRequest request) throws ParseException {
		AuditContext audit = request.getAudit().toAuditContext();
		return errorreportLogic.getErrorHistory(request, audit);
	}

	@Override
	@POST
	@Path("/s/errHistActStatus")
	public ErrorHistoryActivationStatusResponse getErrorHistActivationStatus(ErrorHistoryActivationStatusRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return errorreportLogic.getErrorHistActivationStatus(request, audit);
	}

}
