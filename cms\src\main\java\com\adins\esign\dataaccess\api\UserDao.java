package com.adins.esign.dataaccess.api;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

import com.adins.am.model.AmMsUserHistory;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserpwdhistory;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrUserDataAccessLog;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.PersonalDataHistoryBean;
import com.adins.esign.webservices.model.GetListDataPenggunaRequest;
import com.adins.esign.webservices.model.GetListUserManagementRequest;
import com.adins.esign.webservices.model.ListInquiryUserRequest;
import com.adins.am.model.AmUserPersonalData;

public interface UserDao {
	
	// Get id_ms_user that is registered in other vendor
	Long getIdMsUserRegisteredInOtherVendorByEmail(String email, MsVendor vendor);
	Long getIdMsUserRegisteredInOtherVendorByPhone(String phone, MsVendor vendor);
	Long getIdMsUserRegisteredInOtherVendorByNik(String nik, MsVendor vendor);
	
	// AmMsuser
	void insertUser(AmMsuser newUser);
	void insertUserNewTran(AmMsuser user);
	void updateUser(AmMsuser updUser);
	void updateUserNewTran(AmMsuser updUser);
	
	AmMsuser getUserByLoginId(String loginId);
	AmMsuser getUserByIdMsUser(long idMsUser);
	AmMsuser getUserByIdMsUserNewTrx(long idMsUser);
	AmMsuser getActiveUserByPhone(String phone);
	
	AmMsuser getUserByIdNo(String idNo);
	AmMsuser getUserByIdNoNewTran(String idNo);
	
	AmMsuser getUserByOtpCode(String otpCode);
	AmMsuser getNotActiveUser(String loginid);
	List<Map<String, Object>> getAllUser();
	
	List<Map<String, Object>> getDistinctIdMsusersByEmail(String email);
	List<Map<String, Object>> getDistinctIdMsusersByPhone(String phone);
	List<Map<String, Object>> getDistinctIdMsusersByPhoneAndVendor(String phone, String vendorCode);
	List<Map<String, Object>> getDistinctIdMsusersByEmailAndVendor(String email, String vendorCode);
	List<Map<String, Object>> getDistinctIdMsusersByPhoneAndEmail(String phone, String email);
	int getDateDiffPassHistByIdMsUser(long idMsUser);
	
	// AmUserPersonalData
	PersonalDataBean getUserDataByIdMsUser(long idMsUser, boolean getPhoto);
	PersonalDataBean getUserDataByIdMsUserNewTrx(long idMsUser, boolean getPhoto);
	
	PersonalDataBean getUserDataByIdMsUserOptional(long idMsUser, boolean getNIK,
			boolean getPhone, boolean getAddress, boolean getPhotoKTP, boolean getPhotoSelfie);

	AmUserPersonalData getUserDataByPhone(String phone);
	AmUserPersonalData getUserPersonalDataByIdMsUser(AmMsuser idMsUser);
	void insertUserPersonalData(PersonalDataBean amUserPersonalData);
	void updateUserPersonalData(PersonalDataBean amUserPersonalData);
	void updateUserPersonalDataNewTrans(PersonalDataBean personalDataBean);
	void insertUserPersonalDataHistoryNewTrx(PersonalDataHistoryBean personalDataHistoryBean);
	AmUserpwdhistory getUserPwdhistory(long uuidUser);
	List<AmUserpwdhistory> getListUserpwdhistory(AmMsuser user);
	
	void insertUserPwdhistory(AmUserpwdhistory newUserPwdhistory);
	List<Map<String, Object>> getListInquiryUserId(ListInquiryUserRequest request, String requestType, long idMsTenant, int start, int end);
	BigInteger countListInquiryUser(ListInquiryUserRequest request, String requestType, long idMsTenant);
	List<Map<String, Object>> getUserPasswordHistoryList(long idMsUser);
	void insertUseroftenant(MsUseroftenant useroftenant);
	List<AmMsuser> listUserWithCertExpDateLessThan30Days(MsTenant tenant, MsVendor vendor);
	List<Map<String, Object>> getListInquiryEditUserId(String userIdentifier, String tenantCode);
	List<Map<String, Object>> getListUserViewOtpWithId(String userIdentifier);
	
	// UserManagement
	List<Map<String, Object>> getListUserManagement(GetListUserManagementRequest request, long idMsTenant, int min, int max);
	int countListUserManagement(GetListUserManagementRequest request, long idMsTenant);
	int countRoleInTenant(String roleCode, String tenantCode);
	String getLoginIdByTenantAndCallerId(String tenantCode, String callerId);
	AmMsuser getDataUserByLoginId(String loginId);
	
	//dataPengguna
	List<Map<String, Object>> getListDataPengguna(GetListDataPenggunaRequest request, long idMsTenant);
	
	BigInteger getIdMsUserBySignerRegisteredEmail(String signerRegisteredEmail);
	MsVendorRegisteredUser getSignerDetail(long idMsUser, String vendorCode);
	
	BigInteger getIdMsUserByLoginId(String loginId);
	BigInteger getIdMsUserByPhoneNo(String phoneNo);
	
	//getpassdomain
	String getPassDefault(String isActive);
	
	MsVendorRegisteredUser getVendorRegisteredUser(long idMsUser, String vendorCode);
	
	List<Map<String, Object>> getDistinctIdMsusersByEmailNewTrx(String email);
	AmMsuser getUserByPhone(String phone);
	AmMsuser getDeletedUserManagementByLoginId(String loginId);

}
