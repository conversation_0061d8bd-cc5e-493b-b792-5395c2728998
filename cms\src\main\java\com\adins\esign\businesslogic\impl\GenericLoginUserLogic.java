package com.adins.esign.businesslogic.impl;

import java.util.Date;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.LdapException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.framework.tool.ldap.LdapLogic;
import com.adins.framework.tool.password.PasswordHash;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.LoginUserLogic;
import com.adins.esign.businesslogic.api.interfacing.IntFormLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.model.MsVendor;
import com.adins.esign.util.PropertiesHelper;
import com.adins.exceptions.LoginException;
import com.adins.exceptions.LoginException.Reason;
import com.adins.exceptions.ServicesUserException;

@Component
public class GenericLoginUserLogic extends BaseLogic implements LoginUserLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericLoginUserLogic.class);

	private AuditInfo auditInfo;
    
	@Autowired private LdapLogic ldapLogic;
	
	@Autowired private CommonLogic commonLogic;
	
	@Autowired 
	private Environment env;
	
	@Autowired
	@Qualifier("intFormLogicFactoryBean")
	private IntFormLogic intFormLogic;

	public GenericLoginUserLogic() {
		String[] pkCols = { "uuidMsUser" };
		String[] pkDbCols = { "UUID_MS_USER" };
		String[] cols = { "uuidMsUser", "imei", "androidId", "deviceInfo" };
		String[] dbCols = { "UUID_MS_USER", "IMEI", "ANDROID_ID", "DEVICE_INFO" };
		this.auditInfo = new AuditInfo("AM_MSUSER", pkCols, pkDbCols, cols, dbCols);
	}
	
	@Transactional(noRollbackFor={LoginException.class, ServicesUserException.class},
	        isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public AmMsuser doLogin(String loginId, String password, AuditContext callerId){

		AmMsuser beanUser = daoFactory.getUserDao().getUserByLoginId(loginId);
		
        //cek Login ID
        if (null == beanUser) {
        	throw new LoginException(this.messageSource.getMessage("businesslogic.login.invalididpassword", 
				null, this.retrieveLocaleAudit(callerId)), Reason.LOGIN_INVALID);
        }
		
		String uuid = String.valueOf(beanUser.getIdMsUser());
		boolean canLogin = false;
		Integer maxFailCount = Integer.parseInt(commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_MAX_FAIL_COUNT,  callerId));
			               
		Integer failCount = beanUser.getFailCount();
		String isLocked = beanUser.getIsLocked();
		if (null == failCount) {
			failCount = 0;
		}
		if (StringUtils.isBlank(isLocked)) {
			isLocked = "0";
		}
		
		if (PropertiesHelper.isBypassLogin()) {
			canLogin = true;
		}
		else {
			if (GlobalVal.FLAG_LOGIN_PROVIDER_NC.equals(beanUser.getLoginProvider())){
				canLogin = this.intFormLogic.authenticateUser(beanUser, password);
			}
			else if (GlobalVal.FLAG_LOGIN_PROVIDER_AD.equals(beanUser.getLoginProvider())) {
				canLogin = this.ldapAuth(loginId, password);
			}
			else {
				//cek username, password, dan imei, jika sama maka bisa login
				canLogin = PasswordHash.validatePassword(password, beanUser.getPassword());
			}
		}
		
		if (canLogin) {
            //user is locked or not
            if ("1".equals(isLocked)) {
            	 throw new LoginException(this.messageSource.getMessage("businesslogic.login.locked", 
 		        		new Object[]{beanUser.getLoginId()}, this.retrieveLocaleAudit(callerId)),
 		                Reason.LOGIN_LOCKED);
            }
                    
            //cek user aktif atau ga
            if (!"1".equals(beanUser.getIsActive())) {
                throw new LoginException(
                		this.messageSource.getMessage("businesslogic.login.inactive", new Object[]{loginId}, this.retrieveLocaleAudit(callerId)),
                		Reason.LOGIN_INACTIVE);
            }
                       
            if(GlobalVal.FLAG_LOGIN_PROVIDER_DB.equals(beanUser.getLoginProvider())) {
            	boolean isExpiredPass = this.isPwdExpired(beanUser.getIdMsUser(), callerId);
            	if(isExpiredPass) {
					beanUser.setChangePwdLogin("1");
					beanUser.setLastExpired(new Date());
					beanUser.setDtmUpd(new Date());
					beanUser.setUsrUpd(uuid);
            	}
            }
			
            beanUser.setDtmUpd(new Date());
            beanUser.setUsrUpd(uuid);		
				
			//sukses login - update status login
            beanUser.setPrevLoggedIn(beanUser.getLastLoggedIn());
			beanUser.setLastLoggedIn(new Date());
			beanUser.setFailCount(0);
			beanUser.setIsLoggedIn("1");
			
			this.auditManager.auditEdit(beanUser, auditInfo, callerId.getCallerId(), "");

			daoFactory.getUserDao().updateUser(beanUser);
		} 
		else {
			if ("0".equals(isLocked)) {
				failCount += 1;
				beanUser.setFailCount(failCount);
				beanUser.setPrevLoggedFail(beanUser.getLastLoggedFail());
				beanUser.setLastLoggedFail(new Date());
				if (failCount >= maxFailCount) {
					beanUser.setIsLocked("1");
					beanUser.setLastLocked(new Date());
		            
		            throw new LoginException(this.messageSource.getMessage("businesslogic.login.locked", 
		        		new Object[]{beanUser.getLoginId()}, this.retrieveLocaleAudit(callerId)),
		                Reason.LOGIN_LOCKED);
				}
				//update failCount
				beanUser.setDtmUpd(new Date());
				beanUser.setUsrUpd(uuid);
				daoFactory.getUserDao().updateUser(beanUser);

			}

			throw new LoginException(this.messageSource.getMessage("businesslogic.login.invalididpassword", 
            		new Object[]{beanUser.getLoginId()}, this.retrieveLocaleAudit(callerId)), Reason.LOGIN_INVALID);
		}
		
		return beanUser;
	}
	
//	@Async
//	@Override
//	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
//	public void sendMessageToJms(AmMsuser user, AuditContext auditContext){
////		user.setHashUuidMsUser(hashids.encode(user.getIdMsUser()));
//		//SSE message		
//		Gson gson = new Gson();
//		MessageEventBean msgBean = new MessageEventBean();
//		msgBean.setEvent(GlobalVal.EVENT_NEWS);
//		msgBean.setUuidUser(user.getHashUuidMsUser());
//		msgBean.setUuidMessageBean(daoFactory.getCommonDao().getUuidDao());
//		Object[] msgArgs = {user.getLoginId(), user.getFullName()};
//		msgBean.setNotes(this.messageSource.getMessage("businesslogic.jms.login", msgArgs, this.retrieveLocaleAudit(auditContext)));
//		
//		msgBean.setDate(DateFormatUtils.format(new Date(), "HH:mm:ss"));
//		msgBean.setIsDetected("1");
//		
//		try{
//			sendMessage(gson.toJson(msgBean));
//			LOG.info("Send message to JMS Success");
//		}catch(Exception e){
//			LOG.error("Send message to JMS Failed", e);
//		}
//	}
//	
	
	private boolean isPwdExpired (long idMsUser, AuditContext audit) {
		int days = daoFactory.getUserDao().getDateDiffPassHistByIdMsUser(idMsUser);
		
		String generalSettingPwdExpired = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_PASSWORD_EXPIRED, audit);
		
		return (Integer.parseInt(generalSettingPwdExpired) < days);
	}
	
    public boolean ldapAuth(String user, String password) throws LdapException {
        boolean ldapEnabled = BooleanUtils.toBoolean(env.getProperty(GlobalKey.LDAP_ENABLED));
        if (!ldapEnabled) {
            LOG.debug("LDAP Authentication disabled! Return true");
            return true;
        }
        
        return this.ldapLogic.authenticate(
        		env.getProperty(GlobalKey.LDAP_DOMAIN) + "\\" + user, password, false);
    }

//	@Override
//	@Transactional
//	public String updateUserAvatar(String uuidUser, String imageAvatar, AuditContext callerId) {
//		AmMsuser user = daoFactory.getUserDao().getUserByUuid(Long.valueOf(uuidUser));
//		if(user == null) {
//			return this.messageSource.getMessage("businesslogic.user.usernotfound",
//					new Object[] {""}, this.retrieveLocaleAudit(callerId));
//		}
//		AmUseravatar userAvatar = daoFactory.getUserDao().getAvatarByUuid(user.getIdMsUser());
//		
//		if(userAvatar == null) {
//			userAvatar = new AmUseravatar();
//			userAvatar.setAmMsuser(user);
//			userAvatar.setAvatar(Base64.getDecoder().decode(imageAvatar));
//			userAvatar.setDtmCrt(new Date());
//			userAvatar.setUsrCrt(callerId.getCallerId());
//			daoFactory.getUserDao().insertUserAvatar(userAvatar);
//		}
//		else {
//			userAvatar.setAvatar(Base64.getDecoder().decode(imageAvatar));
//			userAvatar.setDtmUpd(new Date());
//			userAvatar.setUsrUpd(callerId.getCallerId());
//			daoFactory.getUserDao().updateUserAvatar(userAvatar);
//		}
//		return this.messageSource.getMessage("businesslogic.user.updateavatar",
//				new Object[] {""}, this.retrieveLocaleAudit(callerId));
//	}

	@Override
	public String checkPreRegister(long idMsUser, String vendorCode, AuditContext auditContext) {
		if(StringUtils.isBlank(vendorCode)) {
			return "0"; //flow login tidak via email
		}
		
		MsVendor vendorData = daoFactory.getVendorDao().getVendorDataByIdMsUser(idMsUser, vendorCode);
		if(null == vendorData) {
			return "1"; //belum pernah melakukan register
		}
		else {
			return "0"; //sudah pernah melakukan register
		}
		
	}
    
}
