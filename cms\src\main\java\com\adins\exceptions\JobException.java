package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class JobException extends AdInsException {
	private static final long serialVersionUID = 1L;
	
	public enum ReasonJob {
		ID_JOB_RESULT_EMPTY,
		JOB_TYPE_CODE_EMPTY,
		JOB_RESULT_NOT_FOUND
	}

    private final ReasonJob reason;
	
	public JobException(ReasonJob reason) {
		this.reason = reason;
	}
	
	public JobException(String message, ReasonJob reason) {
		super(message);
		this.reason = reason;
	}
	
	public JobException(Throwable ex, ReasonJob reason) {
		super(ex);
		this.reason = reason;
	}
	
	public JobException(String message, Throwable ex, ReasonJob reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	public ReasonJob getReason() {
		return reason;
	}
	
	@Override
	public int getErrorCode() {
		if (null != this.reason) {
			switch (reason) {
			case JOB_TYPE_CODE_EMPTY	:
				return StatusCode.JOB_TYPE_CODE_EMPTY;
			case JOB_RESULT_NOT_FOUND	:
				return StatusCode.JOB_RESULT_NOT_FOUND;
			case ID_JOB_RESULT_EMPTY	:
				return StatusCode.ID_JOB_RESULT_EMPTY;
			default:
				return StatusCode.UNKNOWN;
			}
		}
		return StatusCode.UNKNOWN;
	}

}
