package com.adins.esign.businesslogic.impl.interfacing;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.NumberFormat;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.interfacing.DukcapilLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.DukcapilRequestBean;
import com.adins.esign.model.custom.DukcapilResponseBean;
import com.adins.esign.model.custom.WomfBiometricResponseBean;
import com.adins.esign.model.custom.WomfDukcapilResponseWrapperBean;
import com.adins.esign.util.MssTool;
import com.adins.exceptions.EkycException;
import com.adins.exceptions.EkycException.ReasonEkyc;
import com.adins.exceptions.RemoteException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.lang.FormatterUtils;
import com.google.gson.Gson;

@Component("WomfDukcapil")
public class WomfDukcapilLogic implements DukcapilLogic {

	public static final String CODE_SUCCESS 					= "00";
	public static final String CODE_NIK_TIDAK_SESUAI_KETENTUAN	= "01";
	public static final String CODE_NAMA_LENGKAP_TIDAK_SESUAI_KETENTUAN = "02";
	public static final String CODE_TEMPAT_LAHIR_TIDAK_SESUAI_KETENTUAN = "03";
	public static final String CODE_TGL_LAHIR_TIDAK_SESUAI_KETENTUAN	= "04";
	public static final String CODE_FOTO_TIDAK_SESUAI_KETENTUAN			= "05";
	public static final String CODE_CUSTOMER_ID_TIDAK_SESUAI_KETENTUAN	= "06";
	public static final String CODE_TRX_ID_TIDAK_SESUAI_KETENTUAN		= "07";	
	public static final String CODE_TRX_SOURCE_TIDAK_SESUAI_KETENTUAN	= "08";
	public static final String CODE_REQUEST_TIDAK_DAPAT_DITERUSKAN		= "30";
	public static final String CODE_KESALAHAN_PROSES_DUKCAPIL			= "31";
	public static final String CODE_KESALAHAN_PROSES_FACE_RECOGNITION	= "32";
	
	public static final String DUKCAPIL_MATCH = "Match";
	public static final String DUKCAPIL_NOT_MATCH = "Not Match";
	
	public static final String FORMAT_DATE_REQUEST 	= "dd/MM/yyyy";
	public static final String TRX_SOURCE_ESIGNHUB	= "ESIGNHUB";
	
	private static final Logger LOG = LoggerFactory.getLogger(WomfDukcapilLogic.class);
	private static final Double PERFECT_SCORE = Double.valueOf(100d);
	private static final Double SCORE_MULTIPLIER = Double.valueOf(9.5d);
	
	@Autowired private Gson gson;
	@Autowired private CommonLogic commonLogic;
	
	@Override
	public DukcapilResponseBean verifyDataAndBiometric(MsTenant tenant, DukcapilRequestBean request) {		
		String apiUrl = tenant.getEkycUrl();
		
		WebClient client = WebClient.create(apiUrl)
				.type(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON);
		MssTool.trustAllSslCertificate(client);
		Map<String, String> payloadMap = this.prepareRequestPayload(request);
		String json = gson.toJson(payloadMap);
		LOG.info("Json request EKYC API {}: {}", tenant.getEkycUrl(), json);
		
		Response apiResponse = client.post(json);
		// API Dukcapil WOM kirim 500 Internal Server Error dengan message body
		if (apiResponse.getStatus() != Response.Status.OK.getStatusCode() &&
				apiResponse.getStatus() != Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()) {
			LOG.warn("HTTP Error while check e-KYC to {} : NIK: {}", client.getCurrentURI(), request.getNik());
			throw new RemoteException("Connection to check e-KYC failed");	
		}
		
		InputStreamReader isReader = new InputStreamReader((InputStream) apiResponse.getEntity());
		String result = null;
		try {
			result = IOUtils.toString(isReader);
		} catch (IOException e) {
			LOG.error("Error on getting WOMF e-KYC API Response Body", e);
			throw new RemoteException("Error on getting WOMF e-KYC API Response Body");
		}
		
		if (StringUtils.isBlank(result)) {
			LOG.error("WOMF e-KYC API Response Body is empty");
			throw new RemoteException("WOMF e-KYC API Response Body is empty");
		}
				
		LOG.info("NIK : {}, Success get e-KYC with return : {}", request.getNik(), result);
		WomfDukcapilResponseWrapperBean checkDukcapilResponse = gson.fromJson(result, WomfDukcapilResponseWrapperBean.class);
				
		String responseCode = checkDukcapilResponse.getResponseCode();
		if (!CODE_SUCCESS.equals(responseCode)) {
			LOG.warn("HTTP Error while check e-KYC to {} : NIK: {}", client.getCurrentURI(), request.getNik());
			LOG.warn("e-KYC error with code {} and message: {}", responseCode, checkDukcapilResponse.getResponseMessage());
			this.throwVerifyDataAndBiometricError(responseCode, checkDukcapilResponse.getResponseMessage());
		}
				
		return this.processResponse(checkDukcapilResponse);
	}
	
	private void throwVerifyDataAndBiometricError(String responseCode, String responseMessage) {
		switch (responseCode) {
		case CODE_NIK_TIDAK_SESUAI_KETENTUAN:
			throw new EkycException(responseMessage, ReasonEkyc.NIK_TIDAK_SESUAI_KETENTUAN);
		case CODE_NAMA_LENGKAP_TIDAK_SESUAI_KETENTUAN:
			throw new EkycException(responseMessage, ReasonEkyc.NAMA_LENGKAP_TIDAK_SESUAI_KETENTUAN);
		case CODE_TEMPAT_LAHIR_TIDAK_SESUAI_KETENTUAN:
			throw new EkycException(responseMessage, ReasonEkyc.TEMPAT_LAHIR_TIDAK_SESUAI_KETENTUAN);
		case CODE_TGL_LAHIR_TIDAK_SESUAI_KETENTUAN:
			throw new EkycException(responseMessage, ReasonEkyc.TGL_LAHIR_TIDAK_SESUAI_KETENTUAN);
		case CODE_FOTO_TIDAK_SESUAI_KETENTUAN:
			throw new EkycException(responseMessage, ReasonEkyc.FOTO_TIDAK_SESUAI_KETENTUAN);
		case CODE_CUSTOMER_ID_TIDAK_SESUAI_KETENTUAN:
			throw new EkycException(responseMessage, ReasonEkyc.CUSTOMER_ID_TIDAK_SESUAI_KETENTUAN);
		case CODE_TRX_ID_TIDAK_SESUAI_KETENTUAN:
			throw new EkycException(responseMessage, ReasonEkyc.TRX_ID_TIDAK_SESUAI_KETENTUAN);
		case CODE_TRX_SOURCE_TIDAK_SESUAI_KETENTUAN:
			throw new EkycException(responseMessage, ReasonEkyc.TRX_SOURCE_TIDAK_SESUAI_KETENTUAN);
		case CODE_REQUEST_TIDAK_DAPAT_DITERUSKAN:
			throw new EkycException(responseMessage, ReasonEkyc.REQUEST_TIDAK_DAPAT_DITERUSKAN);
		case CODE_KESALAHAN_PROSES_DUKCAPIL:
			throw new EkycException(responseMessage, ReasonEkyc.KESALAHAN_PROSES_DUKCAPIL);
		case CODE_KESALAHAN_PROSES_FACE_RECOGNITION:
			throw new EkycException(responseMessage, ReasonEkyc.KESALAHAN_PROSES_FACE_RECOGNITION);
		default:
			throw new RemoteException("Checking e-KYC failed with code: " + responseCode);
		}
	}
	
	private Map<String, String> prepareRequestPayload(DukcapilRequestBean request) {
		Map<String, String> payloadMap = new HashMap<>();
		payloadMap.put("NIK", request.getNik());
		payloadMap.put("namaLengkap", request.getNama());
		payloadMap.put("tempatLahir", request.getTempatLahir());
		String formattedDob = FormatterUtils.formatDate(request.getTglLahir(), FORMAT_DATE_REQUEST);		
		payloadMap.put("tanggalLahir", formattedDob);
		payloadMap.put("foto", Base64.getEncoder().encodeToString(request.getSelfie()));
		payloadMap.put("customerId", request.getNik()); //Confirm dengan Pak Liudy, custId boleh diisi NIK
		payloadMap.put("transactionSource", TRX_SOURCE_ESIGNHUB);
		String formattedTrxId = FormatterUtils.formatDate(new Date(), "yyyyMMddHHmmssSSS");
		payloadMap.put("transactionId", formattedTrxId);
		
		return payloadMap;
	}
	
	/**
	 * example response:
	 * {"responseCode":"00","responseMessage":"Sukses","result":{"NIK":"Match","namaLengkap":"Match","tanggalLahir":"Match","tempatLahir":"Match","skorBiometrik":"9.96530532836914"}}
	 * Jika sumberBioMetrik = Asli RI, nilai biometrik tidak perlu dikali.
	 */
	private DukcapilResponseBean processResponse(WomfDukcapilResponseWrapperBean womfApiResponse) {
		WomfBiometricResponseBean result = womfApiResponse.getResult();
		boolean isNikMatch = DUKCAPIL_MATCH.equals(result.getNIK());
		boolean isNamaMatch = DUKCAPIL_MATCH.equals(result.getNamaLengkap());
		boolean isTempatLahirMatch = DUKCAPIL_MATCH.equals(result.getTempatLahir());
		boolean isTglLahirMatch = DUKCAPIL_MATCH.equals(result.getTanggalLahir());				
		Double biometricScore = NumberUtils.createDouble(result.getSkorBiometrik());
		if (!GlobalVal.SUMBER_BIOMETRIK_ASLIRI.equals(result.getSumberBioMetrik())) {
			biometricScore = this.biometricScorePostProcessing(biometricScore);
		}
		return new DukcapilResponseBean(
				isNikMatch, isNamaMatch, isTempatLahirMatch, isTglLahirMatch, biometricScore);		 	
	}
	
	/**
	 * Dikarenakan Digisign menggunakan skor 0-100% dan Dukcapil 0-20.
	 * Dimana minimum skor dinyatakan match selfie: Digisign 75%  dan Dukcapil di skor 6
	 * Dukcapil 6/20 = 30% masih jauh dari skor digisign 75%.
	 * Jadi skor biometrik dukcapil di-kalikan 13, 6x13=78.
	 * Jika setelah dikalikan 13 > 100, dikembalikan nilai 100
	 * 
	 * Update 9-Feb-22, pengali berubah dari 13 menjadi 9.5, karena WOMF menggunakan standard skor 8 dari hasil dukcapil
	 * 8x9.5 = 7.6
	 */
	private Double biometricScorePostProcessing(Double biometricScore) {
		if (null == biometricScore) {
			return null;
		}
		
		double finalScore = biometricScore.doubleValue() * this.getBiometricMultiplier();
		if (finalScore > 100) {
			return PERFECT_SCORE;
		}
		
		NumberFormat nf = FormatterUtils.getNumberFormat(FormatterUtils.NFORMAT_US_2);
		String finalScoreStr =nf.format(finalScore);
		return Double.valueOf(finalScoreStr);
	}
	
	/**
	 * Return nilai multiplier berdasarkan nilai di am_generalsetting.
	 * Jika nilai FR_DUKCAPIL_MULTIPLIER blank, pakai hardcode multiplier 9.5
	 * Jika parse FR_DUKCAPIL_MULTIPLIER gagal, pakai hardcode multiplier 9.5
	 */
	private Double getBiometricMultiplier() {
		
		String gsMultiplier = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_FR_DUKCAPIL_MULTIPLIER, new AuditContext());
		if (StringUtils.isBlank(gsMultiplier)) {
			return SCORE_MULTIPLIER; 
		}
		
		try {
			return Double.valueOf(gsMultiplier);
		} catch (NumberFormatException e) {
			return SCORE_MULTIPLIER;
		}
	}
}
