package com.adins.esign.businesslogic.api;

import java.math.BigInteger;
import java.text.ParseException;
import java.io.IOException;

import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface SchedulerLogic {	
	void dailyRecap(AuditContext auditContext);
	void emailReminderTopUp(AuditContext auditContext) throws ParseException;
	
	void reminderBalance(String balanceType, String[] emailReminderDestinations, MsTenant tenant, MsVendor vendor,
			AuditContext audit);
	
	void sendEmailReminder(String[] emailDestinations, String tenantName, String vendorName, String balanceType, BigInteger saldo);
	void readEmail(AuditContext audit);
	void digiBalanceSync(AuditContext audit) throws IOException;
	void deleteEmail(AuditContext audit);
	void digiCertExpDateSync(AuditContext audit) throws IOException, ParseException;
	void syncLocation(AuditContext audit) throws IOException;
	void errorHistoryRerunSendDoc(AuditContext audit);
	void documentType(AuditContext audit) throws IOException;
	void deleteUnusedComplementaryStampingDocumentJob (AuditContext audit) throws IOException;
	void resumeWorkflow(AuditContext audit);
	void deleteOnPremDocResultJob(AuditContext audit);
	void uploadDmsWF(AuditContext audit);
	void signVida(AuditContext audit);
	void processAutomaticStamp(AuditContext audit);
	void signPrivy(AuditContext audit);
	void registerPrivy(AuditContext audit);
	void attachMeteraiPrivy(AuditContext audit);
	void attachMeteraiVida(AuditContext audit);
	void attachMeteraiPajakku(AuditContext audit);
}
