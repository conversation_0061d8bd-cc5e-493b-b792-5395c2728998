package com.adins.esign.job;

import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.LinkedBlockingQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.adins.esign.model.custom.BalanceCheckQueueBean;
import com.adins.esign.model.custom.QueueDeleteBean;
import com.adins.esign.model.custom.SaveSignCallbackCompleteSIgnTekenAjaBean;
import com.adins.esign.model.custom.SaveSigningResultDecryptedBean;
import com.adins.esign.model.queuebean.UpdErrHistRerunProcessBean;

public class QueuePublisher {
	private static final Logger LOG = LoggerFactory.getLogger(QueuePublisher.class);
	
	private static Set<BalanceCheckQueueBean> balanceCheckQ;
	private static Set<SaveSigningResultDecryptedBean> saveSigningResultQ;
	private static BlockingQueue<QueueDeleteBean> queueDelete;
	private static BlockingQueue<UpdErrHistRerunProcessBean> updErrHistRerunProcessQ;
	private static BlockingQueue<SaveSignCallbackCompleteSIgnTekenAjaBean> saveSigningResultCompleteTekenAja;
	
	static {
		balanceCheckQ = new ConcurrentSkipListSet<>();
		saveSigningResultQ = new ConcurrentSkipListSet<>();
		queueDelete = new LinkedBlockingQueue<>();
		updErrHistRerunProcessQ = new LinkedBlockingQueue<>();
		saveSigningResultCompleteTekenAja = new LinkedBlockingQueue<>();
	}
	
	private QueuePublisher() {	
	}
	
	public static void queueBalanceCheck(BalanceCheckQueueBean bcqBean) {
		balanceCheckQ.add(bcqBean);
		LOG.debug("Queuing check balance: {}", bcqBean);
	}
	
	public static Set<BalanceCheckQueueBean> getQueueBalanceCheck() {
		return balanceCheckQ;
	}
	
	public static void queueSaveSigningResult(SaveSigningResultDecryptedBean ssrdBean) {
		saveSigningResultQ.add(ssrdBean); 
		LOG.debug("Queuing save signing result Digisign: {}", ssrdBean);
	}
	
	public static Set<SaveSigningResultDecryptedBean> getQueueSaveSigningResult() {
		return saveSigningResultQ;
	}
	
	public static void queueDeleteFileFromOss(QueueDeleteBean ssrdBean) {
		queueDelete.add(ssrdBean);
		LOG.debug("Queuing delete from OSS: {}", ssrdBean.getRefNumber());
	}
	
	public static BlockingQueue<QueueDeleteBean> getQueueDeleteFileFromOss() {
		return queueDelete;
	}
	
	public static void queueUpdErrHistRerunProcess(UpdErrHistRerunProcessBean bean) {
		updErrHistRerunProcessQ.add(bean);
		LOG.debug("Queuing update error history rerun process for NIK: {}", bean.getNik());
	}
	
	public static BlockingQueue<UpdErrHistRerunProcessBean> getQueueUpdErrHistRerunProcess() {
		return updErrHistRerunProcessQ;
	}
	
	public static void queueSaveSigningResult(SaveSignCallbackCompleteSIgnTekenAjaBean ssrdBean) {
		saveSigningResultCompleteTekenAja.add(ssrdBean);
		LOG.debug("Queuing save signing result TekenAja: {}", ssrdBean);
	}
	
	public static BlockingQueue<SaveSignCallbackCompleteSIgnTekenAjaBean> getSaveSigningResultCompleteTekenAja() {
		return saveSigningResultCompleteTekenAja;
	}
}
