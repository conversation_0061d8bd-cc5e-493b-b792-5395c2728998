package com.adins.esign.validatorlogic.impl;

import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.model.MsVendor;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.framework.persistence.dao.model.AuditContext;

@Component
public class GenericVendorValidatorLogic extends BaseLogic implements VendorValidatorLogic {

	
	private String getMessages(String code, Object[] params, AuditContext audit) {
		return messageSource.getMessage(code, params, retrieveLocaleAudit(audit));
	}

	@Override
	public MsVendor validateGetOperatingDefaultVendor(String tenantCode, boolean checkDefaultVendorExistence, AuditContext audit) {
		MsVendor vendor = daoFactory.getVendorDao().getDefaultOperatingPsreVendorOfTenant(tenantCode);
		if (checkDefaultVendorExistence && null == vendor) {
			throw new VendorException(getMessages(GlobalKey.MESSAGE_ERROR_VENDOR_DEFAULT_NOT_FOUND,
					new String[] {tenantCode}, audit), ReasonVendor.VENDOR_NOT_FOUND);
		}
		return vendor;
	}
	@Override
	public MsVendor validateGetVendor(String vendorCode, boolean checkVendorExistence, AuditContext audit) {
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);
		if (checkVendorExistence && null == vendor) {
			throw new VendorException(getMessages(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new String[] {"Vendor", vendorCode}, audit), ReasonVendor.VENDOR_NOT_FOUND);
		}
		return vendor;
	}

	@Override
	public MsVendor validateGetMainDefaultVendor(String tenantCode, boolean checkDefaultVendorExistence,
			AuditContext audit) {
		MsVendor vendor = daoFactory.getVendorDao().getMainDefaultPsreVendorOfTenant(tenantCode);
		if (checkDefaultVendorExistence && null == vendor) {
			throw new VendorException(getMessages(GlobalKey.MESSAGE_ERROR_VENDOR_DEFAULT_NOT_FOUND,
					new String[] {tenantCode}, audit), ReasonVendor.VENDOR_NOT_FOUND);
		}
		return vendor;
	}

	@Override
	public MsVendor validateVendorOfTenant(String vendorCode, String tenantCode, boolean checkVendorExistence,
			AuditContext audit) {
		MsVendor vendor = daoFactory.getVendorDao().getVendorByTenantCodeAndVendorCode(vendorCode, tenantCode);
		if (checkVendorExistence && null == vendor) {
			throw new VendorException(getMessages(GlobalKey.MESSAGE_ERROR_VENDOR_NOT_EXIST_OR_ACTIVE_IN_TENANT,
					new String[] {vendorCode, tenantCode}, audit), ReasonVendor.VENDOR_NOT_EXIST_OR_ACTIVE_IN_TENANT);
		}
		
		return vendor;
	}

	@Override
	public MsVendor validateGetVendorByStatusAndTenant(String vendorCode, String tenantCode, AuditContext audit) {
		MsVendor vendor = validateVendorOfTenant(vendorCode, tenantCode, true, audit);
		if(!"1".equals(vendor.getIsActive())) {
			throw new VendorException(getMessages(GlobalKey.MESSAGE_ERROR_VENDOR_NOT_ACTIVE,
					new String[] {vendorCode}, audit), ReasonVendor.VENDOR_STATUS_INVALID);
		}
		if(!"1".equals(vendor.getIsOperating())) {
			throw new VendorException(getMessages(GlobalKey.MESSAGE_ERROR_VENDOR_NOT_OPERATING,
					new String[] {vendorCode}, audit), ReasonVendor.VENDOR_NOT_OPERATING);
		}

		return vendor;
	}

}
