package com.adins.exceptions;

import com.adins.exceptions.RegisterException.ReasonRegister;
import com.adins.framework.exception.AdInsException;

public class <PERSON>Exception extends AdInsException {

	
	public enum ReasonRole {
		ADMESIGN_NOT_ALLOWED
	}
	
	private final ReasonRole reason;
	
	public RoleException(String message, ReasonRole reason) {
		super(message);
		this.reason = reason;
	}
	
	public RoleException(String message, Throwable ex, ReasonRole reason) {
		super(message, ex);
		this.reason = reason;
	}
	
	@Override
	public int getErrorCode() {
		switch (reason) {
		case ADMESIGN_NOT_ALLOWED:
			return StatusCode.ADMESIGN_NOT_ALLOWED;
		default:
			return StatusCode.UNKNOWN;
		}
	}

}
