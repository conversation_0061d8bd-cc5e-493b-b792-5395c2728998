package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class UserManagementException extends AdInsException{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	public enum ReasonUserManagement {
		USER_ID_ALREADY_EXIST,
		ROLE_NOT_EXIST,
		ROLE_NOT_USER_MANAGEMENT,
		NO_PHONE_NOT_VALID,
		NO_KTP_IS_NOT_VALID,
		NO_KTP_IS_ALREADY_REGISTERED,
		EMAIL_IS_NOT_VALID,
		LOGIN_ID_IS_NOT_FOUND,
		OFFICE_IS_NOT_AVAIABLE,
		DATE_FORMAT_NOT_VALID,
		ROLE_IS_NOT_FOUND,
		SAME_NO_KTP,
		DATA_TYPE_INVALID,
		USER_NOT_FOUND_WITH_THAT_ID_NO
	}
	
	private final ReasonUserManagement reason;
	
	public UserManagementException(String message, ReasonUserManagement reason) {
		super(message);
		this.reason = reason;
	}

	@Override
	public int getErrorCode() {
		switch (reason) {
		case USER_ID_ALREADY_EXIST: 
			return StatusCode.USER_ALREADY_REGISTERED;
		case ROLE_NOT_EXIST:
			return StatusCode.TENANT_ROLE_NOT_EXIST;
		case ROLE_NOT_USER_MANAGEMENT:
			return StatusCode.ROLE_IS_NOT_USER_MANAGEMENT;
		case NO_PHONE_NOT_VALID:
			return StatusCode.NO_PHONE_IS_NOT_VALID;
		case NO_KTP_IS_NOT_VALID: 
			return StatusCode.KTP_DIGIT_IS_NOT_VALID;
		case EMAIL_IS_NOT_VALID:
			return StatusCode.EMAIL_IS_NOT_VALID;
		case LOGIN_ID_IS_NOT_FOUND:
			return StatusCode.LOGIN_ID_NOT_EXISTS;
		case OFFICE_IS_NOT_AVAIABLE:
			return StatusCode.OFFICE_NOT_EXISTS;
		case NO_KTP_IS_ALREADY_REGISTERED: 
			return StatusCode.ID_NO_ALREADY_REGISTERED;
		case DATE_FORMAT_NOT_VALID: 
			return StatusCode.INVALID_DATE_FORMAT;
		case ROLE_IS_NOT_FOUND:
			return StatusCode.ROLE_CODE_NOT_FOUND;
		case SAME_NO_KTP:
			return StatusCode.SAME_NO_KTP;
		case DATA_TYPE_INVALID:
			return StatusCode.TYPE_DATA_INVALID;
		case USER_NOT_FOUND_WITH_THAT_ID_NO:
			return StatusCode.USER_NOT_FOUND_WITH_THAT_ID_NO;
		default:
			return StatusCode.UNKNOWN;
		}
	}
	
	
}
