package com.adins.esign.businesslogic.impl.interfacing;

import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.interfacing.DukcapilLogic;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.DukcapilRequestBean;
import com.adins.esign.model.custom.DukcapilResponseBean;

@Component("AsliRiDukcapil")
public class AsliRiDukcapilLogic implements DukcapilLogic {

	@Override
	public DukcapilResponseBean verifyDataAndBiometric(MsTenant tenant, DukcapilRequestBean request) {
		return new DukcapilResponseBean();
	}

}
