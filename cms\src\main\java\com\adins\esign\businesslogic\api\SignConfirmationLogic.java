package com.adins.esign.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.webservices.model.SignConfirmDocumentRequest;
import com.adins.esign.webservices.model.SignConfirmDocumentResponse;
import com.adins.esign.webservices.model.embed.ConfirmSignEmbedRequest;
import com.adins.esign.webservices.model.embed.ConfirmSignEmbedResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface SignConfirmationLogic {
	@RolesAllowed({"ROLE_DASHBOARD"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#request.email, authentication)")
	SignConfirmDocumentResponse signConfirmationDocument(SignConfirmDocumentRequest request, AuditContext audit);
	
	ConfirmSignEmbedResponse signConfirmationDocumentEmbed(ConfirmSignEmbedRequest request, AuditContext audit);
}
