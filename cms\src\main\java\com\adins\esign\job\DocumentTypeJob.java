package com.adins.esign.job;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.esign.businesslogic.api.SchedulerLogic;

@DisallowConcurrentExecution
@Component
public class DocumentTypeJob extends BaseLogic {
	private static final Logger LOG = LoggerFactory.getLogger(DocumentTypeJob.class);
	private static final String SCHEDULER = "SCHEDULER";

	@Autowired SchedulerLogic schedulerLogic;
	
	public void runDocumentType() {
		try {
			LOG.info("Job DocumentType Started");
			AuditContext auditContext = new AuditContext(SCHEDULER);
			schedulerLogic.documentType(auditContext);
			LOG.info("Job DocumentType Finished");
		} catch (Exception e) {
			LOG.error("Error on running DocumentType Job", e);
		}
	}
}
