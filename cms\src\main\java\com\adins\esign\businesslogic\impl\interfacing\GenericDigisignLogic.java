package com.adins.esign.businesslogic.impl.interfacing;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.persistence.EntityNotFoundException;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.ContentDisposition;
import org.apache.cxf.jaxrs.ext.multipart.MultipartBody;
import org.apache.cxf.transport.http.HTTPConduit;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.AmUserPersonalData;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.businesslogic.api.interfacing.DukcapilLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsEmailHosting;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.custom.ActivationDigisignRequestBean;
import com.adins.esign.model.custom.ActivationDigisignResponseBean;
import com.adins.esign.model.custom.BalanceDigiRequest;
import com.adins.esign.model.custom.BalanceDigiResponseBean;
import com.adins.esign.model.custom.BulkSignDocumentDigisignResponseBean;
import com.adins.esign.model.custom.ChangeEmailPhoneDigiRequest;
import com.adins.esign.model.custom.ChangeEmailPhoneDigiResponse;
import com.adins.esign.model.custom.CheckDigiCertExpDateResponse;
import com.adins.esign.model.custom.CheckDigiCertExpRequest;
import com.adins.esign.model.custom.DownloadDocumentDigisignRequestBean;
import com.adins.esign.model.custom.DukcapilRequestBean;
import com.adins.esign.model.custom.DukcapilResponseBean;
import com.adins.esign.model.custom.ManualSignBean;
import com.adins.esign.model.custom.ManualSignerBean;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.RegisterDigisignRequestBean;
import com.adins.esign.model.custom.RegisterDigisignResponseBean;
import com.adins.esign.model.custom.RegisterDigisignResponseDataBean;
import com.adins.esign.model.custom.RegisterVerificationStatusBean;
import com.adins.esign.model.custom.SendDocumentRequestBean;
import com.adins.esign.model.custom.SendDocumentResponseBean;
import com.adins.esign.model.custom.SignDocumentDigisignRequestBean;
import com.adins.esign.model.custom.SignDocumentDigisignResponseBean;
import com.adins.esign.model.custom.SignLocationBean;
import com.adins.esign.model.custom.SignLocationDigisignBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.ZipcodeCityBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.ActivationDigisignRequest;
import com.adins.esign.webservices.model.ActivationDigisignResponse;
import com.adins.esign.webservices.model.BulkSignDocumentDigisignRequest;
import com.adins.esign.webservices.model.BulkSignDocumentDigisignRequestBean;
import com.adins.esign.webservices.model.BulkSignDocumentDigisignResponse;
import com.adins.esign.webservices.model.BulkSignDocumentRequest;
import com.adins.esign.webservices.model.DocumentConfinsRequestBean;
import com.adins.esign.webservices.model.DownloadDocumentDigisignRequest;
import com.adins.esign.webservices.model.DownloadDocumentDigisignResponse;
import com.adins.esign.webservices.model.InsertDocumentManualSignRequest;
import com.adins.esign.webservices.model.RegisterDigisignRequest;
import com.adins.esign.webservices.model.RegisterDigisignResponse;
import com.adins.esign.webservices.model.RegisterResponse;
import com.adins.esign.webservices.model.RegistrationRequest;
import com.adins.esign.webservices.model.SendDocumentRequest;
import com.adins.esign.webservices.model.SendDocumentResponse;
import com.adins.esign.webservices.model.SignDocumentDigisignRequest;
import com.adins.esign.webservices.model.SignDocumentDigisignResponse;
import com.adins.esign.webservices.model.SignDocumentRequest;
import com.adins.esign.webservices.model.ViewDocumentResponse;
import com.adins.esign.webservices.model.digisign.DigisignRegisterRequest;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.exceptions.DigisignException;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.StatusCode;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.tool.lang.FormatterUtils;
import com.adins.framework.tool.password.PasswordHash;
import com.adins.util.AesEncryptionUtils;
import com.adins.util.GenerateSignPicture;
import com.google.gson.Gson;

@Transactional
@Component
public class GenericDigisignLogic extends BaseLogic implements DigisignLogic, ApplicationContextAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericDigisignLogic.class);
    @Autowired private Gson gson;
	@Autowired private CommonLogic commonLogic;
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	
	public static final String DIGI_ERR_MSG_NIK_USED = "NIK sudah terdaftar dengan email lain, silahkan login dengan email yang sesuai NIK atau gunakan NIK lain.";
	public static final String DIGI_REGISTERED_MSG = "Anda sudah terdaftar sebelumnya, silahkan gunakan layanan Digisign";
	public static final String DIGI_ERR_MSG_NO_FACE_SELFIE = "No face found on Selfie";
	public static final String DIGI_ERR_MSG_NO_FACE_KTP = "No face found on KTP";
	public static final String DIGI_ERR_MSG_LIGHTING_PROBLEM = "Verifikasi gagal. Pastikan cahaya cukup dan foto ktp sesuai dengan foto wajah.";
	public static final String DIGI_ERR_MSG_FACE_VERIF_FAILED = "Verifikasi wajah gagal";
	public static final String DIGI_ERR_MSG_ERROR_REGISTER	= "Digisign Register Error: ";
	public static final String DIGI_ERR_MSG_ERRORPROCESSINGKTP = "businesslogic.digisign.errorprocessingktp";
	public static final String DIGI_ERR_MSG_ERRORPROCESSINGSELFIE = "businesslogic.digisign.errorprocessingselfie";
	
	private static final String JSON_FIELD = "jsonfield";
	private static final String CDVALUE_JSON_FIELD = "form-data; name=\"jsonfield\"";
	private static final String BEARER = "Bearer ";
	private static final String CONST_LOG_DIGI_REGISTER_REQUEST = "Digisign register request: {}";
	private static final String CONST_DIGI_CONTENT_DISPOSITION_KTP = "form-data; name=\"fotoktp\"; filename=\"";
	private static final String CONST_DIGI_CONTENT_DISPOSITION_SELFIE = "form-data; name=\"fotodiri\"; filename=\"";
	private static final String CONST_DIGI_CONTENT_DISPOSITION_TTD = "form-data; name=\"ttd\"; filename=\"";
	private static final String CONST_KTPPHOTO = "fotoktp";
	private static final String CONST_SELFPHOTO = "fotodiri";
	private static final String CONST_PNG_EXTENSION = ".png\"";
	private static final String CONST_CONNECT_TIMED_OUT = "connect timed out"; 
	private static final String CONST_READ_TIMED_OUT = "read timed out";
	private static final String CONST_READ_TIMED_OUT_2 = "Read timed out";
	private static final String CONST_LOG_DIGI_REGISTER_RESPONSE  ="Digisign register response: {}";
	
	// Default send document timeout if not found in general setting
	private static final Long SEND_DOC_CONN_TIMEOUT = 120_000L;
	private static final Long SEND_DOC_READ_TIMEOUT = 120_000L;
	
	private String headerAuth = javax.ws.rs.core.HttpHeaders.AUTHORIZATION;
	private String headerContentType = javax.ws.rs.core.HttpHeaders.CONTENT_TYPE;
	private String headerMultipart = javax.ws.rs.core.MediaType.MULTIPART_FORM_DATA;
	private String headerJson = MediaType.APPLICATION_JSON;
	
	@Value("${digisign.uri}") private String urlDigisign;
	@Value("${digisign.senddocument.uri}") private String urlSendDocument;
	@Value("${digisign.bulksigndocument.uri}") private String urlBulkSignDocument;
	@Value("${digisign.signdocument.uri}") private String urlSignDocument;
	@Value("${digisign.downloaddocument.uri}") private String urlDownloadDocument;
	@Value("${digisign.preregister.uri}") private String urlPreregist;
	@Value("${digisign.activation.uri}") private String urlActivation;
	@Value("${digisign.checkbalance.uri}") private String urlCheckBalance;
	@Value("${digisign.checkcertexp.uri}") private String urlCheckCertExp;
	@Value("${digisign.changephoneemail.uri}") private String urlChangePhoneEmail;
	
	@Autowired private VendorLogic vendorLogic; 
	@Autowired private ApplicationContext applicationContext; 
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	
	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}

    private void logDuration(Date start, Date end) {
    	long duration = end.getTime() - start.getTime();
    	long miliSecDiff = TimeUnit.MILLISECONDS.toMillis(duration);
    	long secDiff = TimeUnit.MILLISECONDS.toSeconds(duration);
    	long minDiff = TimeUnit.MILLISECONDS.toMinutes(duration);
    	long hrsDiff = TimeUnit.MILLISECONDS.toHours(duration);
    	
    	String stringDuration = hrsDiff + ":" + minDiff + ":" + secDiff + ":" + miliSecDiff;
    	LOG.info("Duration hit Digisign : {} " ,stringDuration);
    }
    
    private Long getConnectionTimeout(AuditContext audit) {
    	String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_DIGI_SEND_DOC_CONN_TIMEOUT, audit);
    	if (StringUtils.isNotBlank(gsValue)) {
    		try {
    			return Long.valueOf(gsValue);
    		} catch (Exception e) {
				return SEND_DOC_CONN_TIMEOUT;
			}
    	}
    	return SEND_DOC_CONN_TIMEOUT;
    }
    
    private Long getReadTimeout(AuditContext audit) {
    	String gsValue = commonLogic.getGeneralSettingValueByCode(AmGlobalKey.GENERALSETTING_DIGI_SEND_DOC_READ_TIMEOUT, audit);
    	if (StringUtils.isNotBlank(gsValue)) {
    		try {
    			return Long.valueOf(gsValue);
    		} catch (Exception e) {
				return SEND_DOC_READ_TIMEOUT;
			}
    	}
    	return SEND_DOC_READ_TIMEOUT;
    }
    
    /**
	 * Based on API Docs Digisign API-EXTAT3.0.1
	 * AES/ECB/PKCS5Padding library
	 * @param msg			parameter msg di querystring URL redirect dari digisign
	 * 						per 15-Sep-2021, msg berisikan karakter % yg artinya string adalah url encoded.
	 * 						Method ini akan mendecode, dan kemudian menghilangkan karakter newline.
	 * @param tenantCode  	kode tenant, digunakan untuk ambil key decrypt yg tersimpan di ms_vendoroftenant.encryption_key
	 * @return	String plaintext hasil decrypt msg
	 */
	@Override
	public String decryptMessage(String msg, String tenantCode) throws DigisignException {
		String decypted = "";
		if (null != tenantCode) {
			String aesKey = this.daoFactory.getTenantDao().getVendorOfTenantEncryptionKey(
					tenantCode, GlobalVal.VENDOR_CODE_DIGISIGN);
			
			if (StringUtils.contains(msg, "%")) {
				try {
					msg = URLDecoder.decode(msg, StandardCharsets.UTF_8.toString());
				}
				catch (UnsupportedEncodingException e) {
					LOG.warn("Exception when url-decoding message: {}", msg);
				}
				msg = msg.replace("\n", "").replace("\r", "");
			}
			decypted = AesEncryptionUtils.decrypt(msg, aesKey);
		} else {
			decypted = decryptLoop(msg);
		}
		
		LOG.info("Digisign decrypt result: {}", decypted);
		return decypted;
	}
	
	private String decryptLoop(String msg) throws DigisignException {
		List<MsVendoroftenant> lVot = daoFactory.getVendorDao().getListVendoroftenantByVendorCode(GlobalVal.VENDOR_CODE_DIGISIGN);
		String decrypted = null;
		for(MsVendoroftenant vot : lVot) {
			String aesKey = vot.getEncryptionKey();
			if (null == aesKey) {
				continue;
			}
			
			if (StringUtils.contains(msg, "%")) {
				try {
					msg = URLDecoder.decode(msg, StandardCharsets.UTF_8.toString());
				}
				catch (UnsupportedEncodingException e) {
					LOG.warn("Exception when url-decoding message: {}", msg);
				}
				msg = msg.replace("\n", "").replace("\r", "");
			}
			
			try {
				decrypted = AesEncryptionUtils.decrypt(msg, aesKey);
			} catch (Exception e) {
				LOG.warn(String.format("Error %1$s when Decrypting with %2$s\'s Key",e.getMessage(), vot.getMsTenant().getTenantCode()));
			}
		}
		
		if (null != decrypted) {
			return decrypted;
		} else {
			throw new DigisignException("Invalid msg");
		}
		
	}
	
	@Override
	public SendDocumentResponseBean sendDocumentDigisign(DocumentConfinsRequestBean docRequest, InsertDocumentManualSignRequest manualSignReq, String documentId,
			MsTenant tenant, MsVendor vendor, AuditContext audit) throws IOException {

		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();

		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		if(null == vot) {
			throw new EntityNotFoundException("Vendor of tenant mapping not found with Vendor : "+vendor.getVendorCode() 
												+" and Tenant : "+tenant.getTenantCode());
		}
		if(StringUtils.isBlank(vot.getToken())) {
			this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_DATANOTEXIST,
					new Object[] { "Token Vendor of tenant", vendor.getVendorCode() +" - "+ tenant.getTenantCode() }, this.retrieveLocaleAudit(audit));
		}
		
		mapHeader.add(headerAuth, vot.getToken());
		mapHeader.add(headerContentType, headerMultipart);

		WebClient client = WebClient.create(urlDigisign + urlSendDocument).headers(mapHeader);
		
		// Set connection and read timeout for send document
		HTTPConduit httpConduit = WebClient.getConfig(client).getHttpConduit();
		httpConduit.getClient().setConnectionTimeout(getConnectionTimeout(audit));
		httpConduit.getClient().setReceiveTimeout(getReadTimeout(audit));
		
		List<Attachment> atts = new LinkedList<>();

		SendDocumentRequest sendDocReq = new SendDocumentRequest();
		sendDocReq.setSendDocReqBean(prepareSendDocumentBean(docRequest, manualSignReq, documentId, tenant, vendor, audit));
		
		String jsonRequestSendDoc = gson.toJson(sendDocReq);
		LOG.info("JSON request send Document : {}", jsonRequestSendDoc);
		
		ContentDisposition cdJson = new ContentDisposition(CDVALUE_JSON_FIELD);
		atts.add(new Attachment(JSON_FIELD, new ByteArrayInputStream(jsonRequestSendDoc.getBytes()), cdJson));

		String documentName = GlobalVal.PREFIX_DOCUMENT_FILE_NAME + documentId;
		ContentDisposition cdDocument = new ContentDisposition("form-data; name=\"file\"; filename=\""+documentName+".pdf\"");

		String docFile = null != docRequest ? docRequest.getDocumentFile() : manualSignReq.getDocumentFile();
		byte[] dataPdfDocument = Base64.getDecoder().decode(docFile);
		atts.add(new Attachment("file*", new ByteArrayInputStream(dataPdfDocument), cdDocument));
		
		MultipartBody body = new MultipartBody(atts);
		Date start = new Date();
		Response response = client.post(body);
		Date end = new Date();
		logDuration(start, end);

		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String digisignResult = IOUtils.toString(isReader);
		LOG.info("JSON result send Document : {}", digisignResult);
		
		SendDocumentResponse sendDocResp = gson.fromJson(digisignResult, SendDocumentResponse.class);
		
		if (!GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(sendDocResp.getSendDocRespBean().getResult())) {
			throw new DigisignException(this.messageSource.getMessage("businesslogic.document.errorsend",
					new Object[] {"Digisign", sendDocResp.getSendDocRespBean().getNotif()}, this.retrieveLocaleAudit(audit)));
		}
		return sendDocResp.getSendDocRespBean();
	}

	private SendDocumentRequestBean prepareSendDocumentBean(DocumentConfinsRequestBean docConfinsReq, InsertDocumentManualSignRequest manualSignReq, String documentId,
			MsTenant tenant, MsVendor vendor, AuditContext audit) {

		SendDocumentRequestBean sendDocRequest = new SendDocumentRequestBean();
		
		//EMAIL ADMIN
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		String emailAdmin = vot.getEmailPartner();
		
		sendDocRequest.setUserid(emailAdmin);
		sendDocRequest.setDocumentId(documentId);
		sendDocRequest.setRedirect(true);
		sendDocRequest.setSequenceOption(false);
		
		String tenantCode = tenant.getTenantCode();
		
		if (null != docConfinsReq) {
			MsDocTemplate docTemp = daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(docConfinsReq.getDocumentTemplateCode(), tenantCode);
			String payment = "";
			if (GlobalVal.PAY_SIGN_TYPE_DOC.equalsIgnoreCase(docTemp.getMsLovPaymentSignType().getCode())) {
				payment = GlobalVal.CODE_DIGISIGN_PAY_PER_DOC;
			} else {
				payment = GlobalVal.CODE_DIGISIGN_PAY_PER_SIGN;
			}
			sendDocRequest.setPayment(payment);
			
			MsDocTemplate docTemplate = daoFactory.getDocumentDao().
					getDocumentTemplateByCodeAndTenantCode(docConfinsReq.getDocumentTemplateCode(), tenant.getTenantCode());
			List<MsDocTemplateSignLoc> signLocList = daoFactory.getDocumentDao().
					getListSignLocationByTemplateCodeAndIdTenant(docTemplate.getDocTemplateCode(), tenant.getIdMsTenant());
			
			List<SignLocationDigisignBean> signLocDigiBeanList = new ArrayList<>();
			for (MsDocTemplateSignLoc signLoc : signLocList) {
				if (!signLoc.getMsLovByLovSignType().getCode().equalsIgnoreCase(GlobalVal.CODE_LOV_SIGN_TYPE_SDT)) {
					SignLocationDigisignBean signLocDigiBean = new SignLocationDigisignBean();
					MsLov signerType = signLoc.getMsLovByLovSignerType();
					
					boolean signerTypeProvided = false; // flag untuk cek apakah ada data signerType yg tidak dikirim confins
		
					for(SignerBean signerBeanRequest : docConfinsReq.getSigner()) {
						AmMsuser user = daoFactory.getUserDao().getUserByIdNo(signerBeanRequest.getIdNo());
						MsVendorRegisteredUser vru = getVru(user);
						// Data user yang dikirim ke digi dijadikan uppercase semua
						signerBeanRequest.setLoginId(signerBeanRequest.getLoginId().toUpperCase());
						signerBeanRequest.setUserName(signerBeanRequest.getUserName().toUpperCase());
						signerBeanRequest.setUserAddress(signerBeanRequest.getUserAddress().toUpperCase());
						signerBeanRequest.setUserPob(signerBeanRequest.getUserPob().toUpperCase());
						signerBeanRequest.setEmail(signerBeanRequest.getEmail().toUpperCase());
						signerBeanRequest.setProvinsi(signerBeanRequest.getProvinsi().toUpperCase());
						signerBeanRequest.setKota(signerBeanRequest.getKota().toUpperCase());
						signerBeanRequest.setKecamatan(signerBeanRequest.getKecamatan().toUpperCase());
						signerBeanRequest.setKelurahan(signerBeanRequest.getKelurahan().toUpperCase());
						
						if(signerType.getCode().equalsIgnoreCase(signerBeanRequest.getSignerType())) {
							signLocDigiBean.setUserName(signerBeanRequest.getUserName());
							signLocDigiBean.setEmail(vru.getSignerRegisteredEmail());
							signLocDigiBean.setAksiTtd(signerBeanRequest.getSignAction());
							
							signerTypeProvided = true; 
							if(GlobalVal.CODE_DIGISIGN_AUTOSIGN.equalsIgnoreCase(signerBeanRequest.getSignAction())) {
								MsVendorRegisteredUser regUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(signerBeanRequest.getLoginId(), GlobalVal.VENDOR_CODE_DIGISIGN);
								signLocDigiBean.setKuser(regUser.getVendorUserAutosignKey());
							}
							signLocDigiBean.setUser(signLoc.getMsLovByLovSignType().getCode());
						}
					}
					if(!signerTypeProvided) { //ada signer type yg tidak dikirim confins
						throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
								new Object[] {"Signer Type : " + signerType.getCode()}, 
								this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
		
					}
						
					signLocDigiBean.setPage(String.valueOf(signLoc.getSignPage()));
		
					SignLocationBean coordinateBean = gson.fromJson(signLoc.getSignLocation(), SignLocationBean.class);
					signLocDigiBean.setUrx(coordinateBean.getUrx());
					signLocDigiBean.setUry(coordinateBean.getUry());
					signLocDigiBean.setLlx(coordinateBean.getLlx());
					signLocDigiBean.setLly(coordinateBean.getLly());
					
					signLocDigiBeanList.add(signLocDigiBean);
				}
			}
			SignLocationDigisignBean[] signLocBeanArray = new SignLocationDigisignBean[signLocDigiBeanList.size()];
			signLocDigiBeanList.toArray(signLocBeanArray);
			
			// ESH-1029: Parameter sendTo tidak perlu diisi karena hanya untuk menambahkan pengguna yang bisa view document
			sendDocRequest.setSendTo(Collections.emptyList());
			sendDocRequest.setReqSign(Arrays.asList(signLocBeanArray));
		} else {
			this.prepSendManualSign(manualSignReq, sendDocRequest);
		}

		return sendDocRequest;	
	}
	
	private void prepSendManualSign(InsertDocumentManualSignRequest manualSignReq, SendDocumentRequestBean sendDocRequest) {
		String payment = GlobalVal.PAY_SIGN_TYPE_DOC.equals(manualSignReq.getPaymentType()) ? GlobalVal.CODE_DIGISIGN_PAY_PER_DOC : GlobalVal.CODE_DIGISIGN_PAY_PER_SIGN;
		sendDocRequest.setPayment(payment);
		List<SignLocationDigisignBean> signLocDigiBeanList = new ArrayList<>();
		
		for (ManualSignerBean signer : manualSignReq.getSigners()) {
			AmMsuser user = daoFactory.getUserDao().getUserByIdNo(signer.getIdNo());
			MsVendorRegisteredUser vru = getVru(user);
			for (ManualSignBean sign : signer.getSignLocations()) {
				SignLocationDigisignBean signLocDigiBean = new SignLocationDigisignBean();
				signLocDigiBean.setUserName(signer.getName().toUpperCase());
				signLocDigiBean.setEmail(vru.getSignerRegisteredEmail());
				signLocDigiBean.setAksiTtd("mt");
				signLocDigiBean.setUser("ttd");
				signLocDigiBean.setPage(String.valueOf(sign.getSignPage()));
				
				signLocDigiBean.setUrx(sign.getSignLocation().getUrx());
				signLocDigiBean.setUry(sign.getSignLocation().getUry());
				signLocDigiBean.setLlx(sign.getSignLocation().getLlx());
				signLocDigiBean.setLly(sign.getSignLocation().getLly());
				
				signLocDigiBeanList.add(signLocDigiBean);
			}
		}
		
		SignLocationDigisignBean[] signLocBeanArray = new SignLocationDigisignBean[signLocDigiBeanList.size()];
		signLocDigiBeanList.toArray(signLocBeanArray);
		
		// ESH-1029: Parameter sendTo tidak perlu diisi karena hanya untuk menambahkan pengguna yang bisa view document
		sendDocRequest.setSendTo(Collections.emptyList());
		sendDocRequest.setReqSign(Arrays.asList(signLocBeanArray));
	}
	
	private MsVendorRegisteredUser getVru(AmMsuser user) {
		return daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), GlobalVal.VENDOR_CODE_DIGISIGN);
	}

	@Override
	public BulkSignDocumentDigisignResponseBean bulkSignDocument(BulkSignDocumentRequest request, AuditContext audit) throws IOException {
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		TrDocumentD docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(request.getDocumentIds()[0]);
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(docD.getMsTenant(), docD.getMsVendor());
		mapHeader.add(headerAuth, vot.getToken());
		mapHeader.add(headerContentType, headerMultipart);

		WebClient client = WebClient.create(urlDigisign + urlBulkSignDocument).headers(mapHeader);
		
		List<Attachment> atts = new LinkedList<>();
		
		BulkSignDocumentDigisignRequest bulkSignDigisignRequest = new BulkSignDocumentDigisignRequest();
		bulkSignDigisignRequest.setBulkSignDocRequestBean(this.prepareBulkSignRequset(request, audit));
		
		String jsonRequest = gson.toJson(bulkSignDigisignRequest);
		LOG.info("JSON request bulk sign : {}", jsonRequest);

		ContentDisposition cdJson = new ContentDisposition(CDVALUE_JSON_FIELD);
		atts.add(new Attachment(JSON_FIELD, new ByteArrayInputStream(jsonRequest.getBytes()), cdJson));
		
		MultipartBody body = new MultipartBody(atts);
		Date start = new Date();
		Response response = client.post(body);
		Date end = new Date();
		logDuration(start, end);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String digisignResult = StringUtils.EMPTY;
		try {
			digisignResult = IOUtils.toString(isReader);
		} catch (IOException e) {
			throw new IOException(e);
		}
		LOG.info("JSON result bulk sign : {}", digisignResult);
		
		BulkSignDocumentDigisignResponse bulkDigisignResponse = gson.fromJson(digisignResult, BulkSignDocumentDigisignResponse.class);
		return bulkDigisignResponse.getBulkSignDigisignResponseBean();
	}
	
	private BulkSignDocumentDigisignRequestBean prepareBulkSignRequset(BulkSignDocumentRequest request, AuditContext audit) {
		BulkSignDocumentDigisignRequestBean bean = new BulkSignDocumentDigisignRequestBean();
		bean.setEmailUser(request.getLoginId().toUpperCase());
		
		//set email parter by vendor of tenant from first doc D
		MsVendoroftenant  vendorTenant = vendorLogic.getVendorTenantByDocumentId(request.getDocumentIds()[0], audit);
		bean.setUserid(vendorTenant.getEmailPartner());
		
		bean.setDocumentId(request.getDocumentIds());
		bean.setmMustRead(false);
		return bean;
	}
	
	@Override
	public SignDocumentDigisignResponseBean signDocument(SignDocumentRequest signDocReq, TrDocumentD docD, AuditContext audit) throws IOException {
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();

		if (null == docD) {
			docD = daoFactory.getDocumentDao().getDocumentDetailByDocId(signDocReq.getDocumentId());
		}
		
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(docD.getMsTenant(), docD.getMsVendor());
		mapHeader.add(headerAuth, vot.getToken());
		mapHeader.add(headerContentType, headerMultipart);

		WebClient client = WebClient.create(urlDigisign + urlSignDocument).headers(mapHeader);
		
		List<Attachment> atts = new LinkedList<>();
		
		SignDocumentDigisignRequest signDocDigisignRequest = new SignDocumentDigisignRequest();
		signDocDigisignRequest.setSignDocRequestBean(prepareSignDocRequestBean(signDocReq, audit));
		
		String jsonRequestSignDoc = gson.toJson(signDocDigisignRequest);
		LOG.info("JSON request single sign : {}", jsonRequestSignDoc);

		ContentDisposition cdJson = new ContentDisposition(CDVALUE_JSON_FIELD);
		atts.add(new Attachment(JSON_FIELD, new ByteArrayInputStream(jsonRequestSignDoc.getBytes()), cdJson));

		MultipartBody body = new MultipartBody(atts);
		Date start = new Date();
		Response response = client.post(body);
		Date end = new Date();
		logDuration(start, end);

		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String digisignResult = StringUtils.EMPTY;
		try {
			digisignResult = IOUtils.toString(isReader);
		} catch (IOException e) {
			throw new IOException(e);
		}
		LOG.info("JSON result single sign : {}", digisignResult);
		
		SignDocumentDigisignResponse signDocDigisignResponse = gson.fromJson(digisignResult, SignDocumentDigisignResponse.class);
		return signDocDigisignResponse.getSignDocResponseBean();
	}
	
	private SignDocumentDigisignRequestBean prepareSignDocRequestBean(SignDocumentRequest signDocReq, AuditContext audit) {
		SignDocumentDigisignRequestBean signDocRequestBean = new SignDocumentDigisignRequestBean();
		
		//set email parter by vendor of tenant from doc D
		MsVendoroftenant  vendorTenant = vendorLogic.getVendorTenantByDocumentId(signDocReq.getDocumentId(), audit);
		signDocRequestBean.setUserid(vendorTenant.getEmailPartner());
		
		signDocRequestBean.setDocumentId(signDocReq.getDocumentId());
		signDocRequestBean.setEmailUser(signDocReq.getEmail().toUpperCase());
		signDocRequestBean.setViewOnly(false);
		
		return signDocRequestBean;
	}
	
	@Override
	public ViewDocumentResponse getDocumentFile(TrDocumentD document, AuditContext audit) {
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(document.getMsTenant(), document.getMsVendor());	
		return getDocumentFileDigisign(document, vot.getToken(), audit);
	}
	
	@Override
	public ViewDocumentResponse getDocumentFileDigisign(TrDocumentD document, String token, AuditContext audit) {
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerAuth, token);
		mapHeader.add(headerContentType, headerMultipart);
		LOG.info("Get Digisign document file header: {}", mapHeader);
		
		WebClient client = WebClient.create(urlDigisign + urlDownloadDocument).headers(mapHeader);
		
		List<Attachment> atts = new LinkedList<>();
		
		DownloadDocumentDigisignRequest digisignRequest = new DownloadDocumentDigisignRequest();
		digisignRequest.setBean(prepareDownloadDocumentRequest(document.getDocumentId(), audit));
		
		String jsonRequest = gson.toJson(digisignRequest);
		LOG.info("Get Digisign document file request: {}", jsonRequest);

		ContentDisposition cdJson = new ContentDisposition(CDVALUE_JSON_FIELD);
		atts.add(new Attachment(JSON_FIELD, new ByteArrayInputStream(jsonRequest.getBytes()), cdJson));
		
		MultipartBody body = new MultipartBody(atts);
		Date start = new Date();
		Response response = client.post(body);
		Date end = new Date();
		logDuration(start, end);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String digisignResult = StringUtils.EMPTY;
		try {
			digisignResult = IOUtils.toString(isReader);
		} catch (Exception e) {
			throw new DigisignException(e.getLocalizedMessage());
		}
		
		DownloadDocumentDigisignResponse digisignResponse;
		try {
			digisignResponse = gson.fromJson(digisignResult, DownloadDocumentDigisignResponse.class);
		} catch (Exception e) {
			LOG.error("Get Digisign document file failed with response: {}", digisignResult);
			String errorMessage = getHtmlErrorMessage(digisignResult);
			throw new DigisignException(errorMessage);
		}
		
		if (GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(digisignResponse.getBean().getResult())) {
			LOG.info("Get Digisign document file success for {}", document.getDocumentId());
		} else {
			LOG.info("Get Digisign document file failed for {} with notif: {}", document.getDocumentId(), digisignResponse.getBean().getNotif());
		}
		
		Status status = new Status();
		Integer code = 0;
		try {
			code = Integer.valueOf(digisignResponse.getBean().getResult());
		} catch (Exception e) {
			code = StatusCode.DIGISIGN_FAILED;
		}
		status.setCode(code);
		status.setMessage(digisignResponse.getBean().getNotif());
		
		ViewDocumentResponse viewResponse = new ViewDocumentResponse();
		viewResponse.setPdfBase64(digisignResponse.getBean().getFile());
		viewResponse.setStatus(status);
		return viewResponse;
	}
	
	private String getHtmlErrorMessage(String htmlResponse) {
		if (StringUtils.isBlank(htmlResponse)) {
			return htmlResponse;
		}
		
		try {
			Document doc = Jsoup.parse(htmlResponse).body().ownerDocument();
			return doc.text();
		} catch (Exception e) {
			return htmlResponse;
		}
	}
	
	private DownloadDocumentDigisignRequestBean prepareDownloadDocumentRequest(String documentId, AuditContext audit) {
		
		MsVendoroftenant  vendorTenant = vendorLogic.getVendorTenantByDocumentId(documentId, audit);
		
		DownloadDocumentDigisignRequestBean bean = new DownloadDocumentDigisignRequestBean();
		bean.setUserId(vendorTenant.getEmailPartner());
		bean.setDocumentId(documentId);
		
		if (!audit.getCallerId().contains(GlobalVal.SCHEDULER) && !GlobalVal.CALLER_ID_MONITORING.equals(audit.getCallerId())) {
			AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(audit.getCallerId(), true, audit);
			MsVendorRegisteredUser vuser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), GlobalVal.VENDOR_CODE_DIGISIGN);
			bean.setEmailUser(StringUtils.upperCase(vuser.getSignerRegisteredEmail()));
		}
		
		return bean;
	}
	
	@Override
	public RegisterDigisignResponseBean sendDataRegister(RegistrationRequest regisRequest, AuditContext audit) {

		UserBean userBean = regisRequest.getUserData();
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(regisRequest.getTenantCode());
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(regisRequest.getVendorCode());
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		mapHeader.add(headerAuth, vot.getToken());
		mapHeader.add(headerContentType, headerMultipart);
		
		String statusEkyc = vendorLogic.getVendorEKYCStatus(regisRequest.getTenantCode(), regisRequest.getVendorCode(), audit);
		
		WebClient client = WebClient.create(urlDigisign + urlPreregist).headers(mapHeader);	
		
		RegisterDigisignRequest requestPrereg = new RegisterDigisignRequest();
		requestPrereg.setRegRequestBean(preparePreRegisterBean(userBean));
		
		if (!"1".equals(statusEkyc)) { // statusEkyc 0 = Validasi Dukcapil menggunakan ekyc client
			this.putNonEkycFields(tenant, regisRequest, requestPrereg.getRegRequestBean());
		}
		
		String jsonRequestPrereg = gson.toJson(requestPrereg);
		LOG.info(CONST_LOG_DIGI_REGISTER_REQUEST, jsonRequestPrereg);

		ContentDisposition cdJson = new ContentDisposition(CDVALUE_JSON_FIELD);
		
		List<Attachment> atts = new LinkedList<>();
		atts.add(new Attachment(JSON_FIELD, new ByteArrayInputStream(jsonRequestPrereg.getBytes()), cdJson));

		try {
			String imageKtpFileName = GlobalVal.PREFIX_PHOTO_ID_FILE_NAME + userBean.getLoginId();
			ContentDisposition cdKtp = new ContentDisposition(CONST_DIGI_CONTENT_DISPOSITION_KTP+imageKtpFileName+GlobalVal.FILE_DIGI_FORMAT_JPEG);
			byte[] dataImageIdPhoto = MssTool.imageStringToByteArray(userBean.getIdPhoto());
			atts.add(new Attachment(CONST_KTPPHOTO, new ByteArrayInputStream(dataImageIdPhoto), cdKtp));
		} catch (Exception e) {
			throw new DigisignException(messageSource.getMessage(DIGI_ERR_MSG_ERRORPROCESSINGKTP,
					null, retrieveLocaleAudit(audit)));
		}
		
		try {
			String imageSelfFileName = GlobalVal.PREFIX_PHOTO_SELF_FILE_NAME + userBean.getLoginId() ;
			ContentDisposition cdDiri = new ContentDisposition(CONST_DIGI_CONTENT_DISPOSITION_SELFIE+imageSelfFileName+GlobalVal.FILE_DIGI_FORMAT_JPEG);
			byte[] dataImageSelfPhoto = MssTool.imageStringToByteArray(userBean.getSelfPhoto());
			atts.add(new Attachment(CONST_SELFPHOTO, new ByteArrayInputStream(dataImageSelfPhoto), cdDiri));
		} catch (Exception e) {
			throw new DigisignException(messageSource.getMessage(DIGI_ERR_MSG_ERRORPROCESSINGSELFIE,
					null, retrieveLocaleAudit(audit)));
		}
		
		if (null != userBean.getNpwpPhoto()) {
			String imageNpwpFileName = GlobalVal.PREFIX_PHOTO_NPWP_FILE_NAME + userBean.getLoginId() ;
			ContentDisposition cdNpwp = new ContentDisposition("form-data; name=\"fotonpwp\"; filename=\""+imageNpwpFileName+".jpg\"");
			byte[] dataImageNpwpPhoto = MssTool.imageStringToByteArray(userBean.getNpwpPhoto());
			atts.add(new Attachment("fotonpwp", new ByteArrayInputStream(dataImageNpwpPhoto), cdNpwp));
		}
		
		if (null != userBean.getTtd()) {
			String imageTtdFileName = GlobalVal.PREFIX_TTD_FILE_NAME + userBean.getLoginId() ;
			ContentDisposition cdTtd = new ContentDisposition(CONST_DIGI_CONTENT_DISPOSITION_TTD+imageTtdFileName+CONST_PNG_EXTENSION);
			byte[] dataImageTtd = MssTool.imageStringToByteArray(userBean.getTtd());
			atts.add(new Attachment("ttd", new ByteArrayInputStream(dataImageTtd), cdTtd));
		}

		LOG.info("DIGI register request attachments size: {}", atts.size());
		
		MultipartBody body = new MultipartBody(atts);
		Date start = new Date();
		Response response = null;
		try {
			response = client.post(body);
		} 
		catch (Exception e) {
			//throw error khusus timed out 
			//udah coba pake catch SocketTimeoutException tapi error, jadi pake if
			if (e.getLocalizedMessage().contains(CONST_CONNECT_TIMED_OUT)) {
				throw new DigisignException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DIGISIGN_CONNECT_TIMED_OUT, null , retrieveLocaleAudit(audit)));
			}
			if (e.getLocalizedMessage().contains(CONST_READ_TIMED_OUT) || e.getLocalizedMessage().contains(CONST_READ_TIMED_OUT_2)) {
				throw new DigisignException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DIGISIGN_CONNECT_READ_TIMED_OUT, null , retrieveLocaleAudit(audit)));
			}
			throw new DigisignException(DIGI_ERR_MSG_ERROR_REGISTER + e.getLocalizedMessage());
		}
		
		Date end = new Date();
		logDuration(start, end);

		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String digisignResult = StringUtils.EMPTY;
		try {
			digisignResult = IOUtils.toString(isReader);
		} catch (IOException e) {
			throw new DigisignException(DIGI_ERR_MSG_ERROR_REGISTER + e.getLocalizedMessage());
		}
		LOG.info(CONST_LOG_DIGI_REGISTER_RESPONSE, digisignResult);
		
		RegisterDigisignResponse preRegResp = gson.fromJson(digisignResult, RegisterDigisignResponse.class);
		return preRegResp.getRegResponseBean();
	}
	
	private RegisterDigisignRequestBean preparePreRegisterBean(UserBean userBean) {
		RegisterDigisignRequestBean preRegBean = new RegisterDigisignRequestBean();
		preRegBean.setUserid(userBean.getAdminEmail());
		preRegBean.setNama(userBean.getUserName().toUpperCase());
		preRegBean.setJenisKelamin(userBean.getUserGender());
		preRegBean.setTmpLahir(userBean.getUserPob().toUpperCase());
		preRegBean.setTglLahir(userBean.getUserDob());
		preRegBean.setAlamat(userBean.getUserAddress().toUpperCase());
		preRegBean.setProvinci(userBean.getProvinsi().toUpperCase());
		preRegBean.setKota(userBean.getKota().toUpperCase());
		preRegBean.setKecamatan(userBean.getKecamatan().toUpperCase());
		preRegBean.setKelurahan(userBean.getKelurahan().toUpperCase());
		preRegBean.setKodepos(userBean.getZipcode());
		preRegBean.setTlp(userBean.getUserPhone());
		preRegBean.setIdktp(userBean.getIdNo());
		preRegBean.setNpwp(null == userBean.getNpwpNo()? StringUtils.EMPTY : userBean.getNpwpNo()); //ttp harus kirim walau null
		preRegBean.setEmail(userBean.getEmail().toUpperCase());
		preRegBean.setRedirect(true);
		
		return preRegBean;	
	}
    
	@Override
	public ActivationDigisignResponseBean getActivationLink(UserBean userBean, AuditContext audit) {
		try {
			MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
			mapHeader.add(headerAuth, userBean.getToken());
			mapHeader.add(headerContentType, headerMultipart);

			WebClient client = WebClient.create(urlDigisign + urlActivation).headers(mapHeader);
			
			ActivationDigisignRequest requestActivation = new ActivationDigisignRequest();
			requestActivation.setActivationRequestBean(prepareActivationBean(userBean));
			
			String jsonRequestActivation = gson.toJson(requestActivation);
			LOG.info("Get activation link request: {}", jsonRequestActivation);
			
			List<Attachment> atts = new LinkedList<>();
			ContentDisposition cdJson = new ContentDisposition(CDVALUE_JSON_FIELD);
			atts.add(new Attachment(JSON_FIELD, new ByteArrayInputStream(jsonRequestActivation.getBytes()), cdJson));

			MultipartBody body = new MultipartBody(atts);
			Date start = new Date();
			Response response = client.post(body);
			Date end = new Date();
			logDuration(start, end);

			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String digisignResult = IOUtils.toString(isReader);
			LOG.info("Get activation link response: {}", digisignResult);
							
			if (digisignResult.contains("{")) {
				ActivationDigisignResponse activationResp = gson.fromJson(digisignResult, ActivationDigisignResponse.class);
				LOG.info("Result activation link: {}", activationResp.getActivationRequestBean().getLink());
				return activationResp.getActivationRequestBean();
			} else {
				ActivationDigisignResponseBean respBean = new ActivationDigisignResponseBean();
				String result = digisignResult.replaceAll("\\<[a-zA-Z0-9]*\\>", "").replaceAll("<\\/[a-z0-9]\\>", "").replace("\\[a-z]", "");
				LOG.info("Result activation link: {}", result);
				respBean.setNotif(result);
				return respBean;
			}
		} catch (Exception e) {
			throw new DigisignException("Get activation link error: " + e.getLocalizedMessage());
		}
		
	}

	private ActivationDigisignRequestBean prepareActivationBean(UserBean userBean) {
		ActivationDigisignRequestBean activationBean = new ActivationDigisignRequestBean();
		activationBean.setUserid(userBean.getAdminEmail());
		activationBean.setEmailUser(userBean.getEmail().toUpperCase());
		
		return activationBean;	
	}

	private DukcapilResponseBean callDukcapilEkyc(MsTenant tenant, RegistrationRequest regisRequest) {
		DukcapilLogic dukcapilLogic = this.applicationContext.getBean(tenant.getEkycImplClass(), DukcapilLogic.class);
		
		DukcapilRequestBean dukcapilRequest = new DukcapilRequestBean();
		dukcapilRequest.setNik(regisRequest.getUserData().getIdNo());
		dukcapilRequest.setNama(regisRequest.getUserData().getUserName());
		dukcapilRequest.setTempatLahir(regisRequest.getUserData().getUserPob());
		try {
			Date dob = FormatterUtils.parseDate(regisRequest.getUserData().getUserDob(), GlobalVal.DATE_FORMAT_DASH_IN);
			dukcapilRequest.setTglLahir(dob); //Form
		}
		catch (ParseException e) {
			LOG.error("Cannot parse string {} to date with format {}", regisRequest.getUserData().getUserDob(),
					GlobalVal.DATE_FORMAT_DASH_IN);
		}
		byte[] selfie = MssTool.imageStringToByteArray(regisRequest.getUserData().getSelfPhoto());
		dukcapilRequest.setSelfie(selfie);
		
		return dukcapilLogic.verifyDataAndBiometric(tenant, dukcapilRequest);
	}
	
	private DukcapilResponseBean callDukcapilEkycV2(MsTenant tenant, DigisignRegisterRequest regisRequest) {
		DukcapilLogic dukcapilLogic = this.applicationContext.getBean(tenant.getEkycImplClass(), DukcapilLogic.class);
		
		DukcapilRequestBean dukcapilRequest = new DukcapilRequestBean();
		dukcapilRequest.setNik(regisRequest.getUserData().getIdNo());
		dukcapilRequest.setNama(regisRequest.getUserData().getUserName());
		dukcapilRequest.setTempatLahir(regisRequest.getUserData().getUserPob());
		try {
			Date dob = FormatterUtils.parseDate(regisRequest.getUserData().getUserDob(), GlobalVal.DATE_FORMAT_DASH_IN);
			dukcapilRequest.setTglLahir(dob); //Form
		}
		catch (ParseException e) {
			LOG.error("Cannot parse string {} to date with format {}", regisRequest.getUserData().getUserDob(),
					GlobalVal.DATE_FORMAT_DASH_IN);
		}
		byte[] selfie = MssTool.imageStringToByteArray(regisRequest.getUserData().getSelfPhoto());
		dukcapilRequest.setSelfie(selfie);
		
		return dukcapilLogic.verifyDataAndBiometric(tenant, dukcapilRequest);
	}
	
	private String maskingAddress(String alamat, String replaceChar) {
		return RegExUtils.replacePattern(alamat, "[AIUEOaiueo]", replaceChar);
	}
	
	private String jsonDataVerifikasi(DukcapilResponseBean response, String alamat) {
		Map<String, Object> jsonDataVerif = new HashMap<>();
		jsonDataVerif.put("name", response.isNamaMatch());
		jsonDataVerif.put("birthplace", response.isTempatLahirMatch());
		jsonDataVerif.put("birthdate", response.isTglLahirMatch());
		jsonDataVerif.put("address", this.maskingAddress(alamat, "*"));
		
		return gson.toJson(jsonDataVerif);
	}
	
	private void putNonEkycFields(MsTenant tenant, RegistrationRequest regisRequest, RegisterDigisignRequestBean regRequestBean) {
		DukcapilResponseBean dukcapilResponse = this.callDukcapilEkyc(tenant, regisRequest);
		String jsonDataVerif = this.jsonDataVerifikasi(dukcapilResponse, regisRequest.getUserData().getUserAddress());
		
		regRequestBean.setRefVerifikasi(String.valueOf(System.currentTimeMillis()));
		regRequestBean.setDataVerifikasi(jsonDataVerif);
		regRequestBean.setScoreSelfie(String.valueOf(dukcapilResponse.getBiometricScore()));
		regRequestBean.setVnik(this.convertBooleanToNonEkycFieldsValue(dukcapilResponse.isNikMatch()));
		regRequestBean.setVnama(this.convertBooleanToNonEkycFieldsValue(dukcapilResponse.isNamaMatch()));
		regRequestBean.setVtglLahir(this.convertBooleanToNonEkycFieldsValue(dukcapilResponse.isTglLahirMatch()));
		regRequestBean.setvTmpLahir(this.convertBooleanToNonEkycFieldsValue(dukcapilResponse.isTempatLahirMatch()));
	}
	
	private void putNonEkycFieldsV2(MsTenant tenant, DigisignRegisterRequest regisRequest, RegisterDigisignRequestBean regRequestBean) {
		DukcapilResponseBean dukcapilResponse = this.callDukcapilEkycV2(tenant, regisRequest);
		String jsonDataVerif = this.jsonDataVerifikasi(dukcapilResponse, regisRequest.getUserData().getUserAddress());
		
		regRequestBean.setRefVerifikasi(String.valueOf(System.currentTimeMillis()));
		regRequestBean.setDataVerifikasi(jsonDataVerif);
		regRequestBean.setScoreSelfie(String.valueOf(dukcapilResponse.getBiometricScore()));
		regRequestBean.setVnik(this.convertBooleanToNonEkycFieldsValue(dukcapilResponse.isNikMatch()));
		regRequestBean.setVnama(this.convertBooleanToNonEkycFieldsValue(dukcapilResponse.isNamaMatch()));
		regRequestBean.setVtglLahir(this.convertBooleanToNonEkycFieldsValue(dukcapilResponse.isTglLahirMatch()));
		regRequestBean.setvTmpLahir(this.convertBooleanToNonEkycFieldsValue(dukcapilResponse.isTempatLahirMatch()));
	}
	
	
	
	private String convertBooleanToNonEkycFieldsValue(boolean boo) {
		if (boo) {
			return "1";
		}
		else {
			return "0";
		}
	}

	@Override
	public BalanceDigiResponseBean getBalanceDigi(MsTenant tenant, MsVendor vendor, AuditContext audit) throws IOException {
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		
		mapHeader.add(headerAuth, BEARER + vot.getToken());
		mapHeader.add(headerContentType, headerJson);
		
		WebClient client = WebClient.create(urlDigisign + urlCheckBalance).headers(mapHeader);
		
		BalanceDigiRequest request = new BalanceDigiRequest();
		request.setUserid(vot.getEmailPartner());
		
		String jsonReq = gson.toJson(request);
		
		LOG.info("JSON REQUEST CHECK BALANCE DIGI: \n {}", jsonReq);
		Response response = client.post(jsonReq);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		
		String digisignResult = StringUtils.EMPTY;
		try {
			digisignResult = IOUtils.toString(isReader);
		} catch (IOException e) {
			throw new IOException(e);
		}
		LOG.info("JSON result get Balance Digisign link : {}", digisignResult);
		
		BalanceDigiResponseBean responseReturn = new BalanceDigiResponseBean();
		try {
			responseReturn = gson.fromJson(digisignResult, BalanceDigiResponseBean.class);
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage());
		}
		
		return responseReturn;
	}

	@Override
	public CheckDigiCertExpDateResponse checkCertExpDate(String emailUser, 
			MsVendoroftenant vot, AuditContext audit) throws IOException {
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		
		mapHeader.add(headerAuth, BEARER + vot.getToken());
		mapHeader.add(headerContentType, headerJson);
		
		WebClient client = WebClient.create(urlCheckCertExp).headers(mapHeader);
		
		CheckDigiCertExpRequest request = new CheckDigiCertExpRequest();
		request.setEmailUser(StringUtils.lowerCase(emailUser));
		request.setUserid(vot.getEmailPartner());
		
		String jsonReq = gson.toJson(request);
		
		LOG.info("JSON REQUEST CHECK CERTIFICATE EXP DATE DIGI: \n {}", jsonReq);
		Response response = client.post(jsonReq);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		
		String digisignResult = StringUtils.EMPTY;
		try {
			digisignResult = IOUtils.toString(isReader);
		} catch (IOException e) {
			throw new IOException(e);
		}
		LOG.info("JSON result get Certificate Exp Date : {}", digisignResult);
		
		CheckDigiCertExpDateResponse responseReturn = new CheckDigiCertExpDateResponse();
		
		try {
			responseReturn = gson.fromJson(digisignResult, CheckDigiCertExpDateResponse.class);
		} catch (Exception e) {
			LOG.error(e.getLocalizedMessage());
		}
		
		return responseReturn;
	}

	@Override
	public ChangeEmailPhoneDigiResponse changeEmailPhoneDigi(ChangeEmailPhoneDigiRequest request,  MsTenant tenant, AuditContext audit) throws IOException {
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_DIGISIGN);
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		
		mapHeader.add(headerAuth, BEARER + vot.getToken());
		mapHeader.add(headerContentType, headerMultipart);
		
		List<Attachment> atts = new LinkedList<>();
		
		WebClient client = WebClient.create(urlDigisign + urlChangePhoneEmail).headers(mapHeader);
		request.getBean().setUserid(vot.getEmailPartner());
		String jsonReq = gson.toJson(request);
		LOG.info("Change phone email Digisign request: {}", jsonReq);
		
		ContentDisposition cdJson = new ContentDisposition(CDVALUE_JSON_FIELD);
		atts.add(new Attachment(JSON_FIELD, new ByteArrayInputStream(jsonReq.getBytes()), cdJson));

		MultipartBody body = new MultipartBody(atts);
		Date start = new Date();
		Response response = client.post(body);
		Date end = new Date();
		logDuration(start, end);
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		
		String digisignResult = IOUtils.toString(isReader);
		LOG.info("Change phone email Digisign response: {}", digisignResult);
		
		try {
			return gson.fromJson(digisignResult, ChangeEmailPhoneDigiResponse.class);
		} catch (Exception e) {
			throw new DigisignException(getMessage("businesslogic.user.errorchangedigidata",
					new Object[] {"Digisign"}, audit));
		}
	}
	
	private void appendSeparator(StringBuilder messageBuilder, Integer currentFailCount, int totalFail) {
		if (currentFailCount < totalFail && totalFail != 2) {
			messageBuilder.append(",");
		}
		if (currentFailCount < totalFail) {
			messageBuilder.append(" ");
		}
		if (currentFailCount == totalFail - 1) {
			messageBuilder.append("dan ");
		}
	}

	@Override
	public String buildRegisterVerifMessage(RegisterDigisignResponseDataBean verifResult) {
		if (null == verifResult) {
			return StringUtils.EMPTY;
		}
		
		int totalFail = verifResult.failCount();
		if (0 == totalFail) {
			return StringUtils.EMPTY;
		}
		
		Integer currentFailCount = 0;
		StringBuilder message = new StringBuilder();
		message.append("Verifikasi user gagal. ");
		
		if (Boolean.FALSE.equals(verifResult.getNik())) {
			message.append("NIK");
			currentFailCount ++;
			appendSeparator(message, currentFailCount, totalFail);
		}
		if (Boolean.FALSE.equals(verifResult.getName())) {
			message.append("Nama");
			currentFailCount ++;
			appendSeparator(message, currentFailCount, totalFail);
		}
		if (Boolean.FALSE.equals(verifResult.getBirthplace())) {
			message.append("Tempat Lahir");
			currentFailCount ++;
			appendSeparator(message, currentFailCount, totalFail);
		}
		if (Boolean.FALSE.equals(verifResult.getBirthdate())) {
			message.append("Tanggal Lahir");
			currentFailCount ++;
			appendSeparator(message, currentFailCount, totalFail);
		}
		if (Boolean.FALSE.equals(verifResult.getSelfie())) {
			message.append("Selfie");
			currentFailCount ++;
			appendSeparator(message, currentFailCount, totalFail);
		}
		message.append(" tidak sesuai.");
		return message.toString();
	}

	@Override
	public RegisterResponse buildRegisterResponse(RegisterDigisignResponseBean registerResponse, DigisignRegisterRequest request, AuditContext audit) {
		Status status = new Status();
		
		int statusCode = 0;
		try {
			statusCode = Integer.parseInt(registerResponse.getResult());
		} catch (Exception e) {
			statusCode = StatusCode.DIGISIGN_FAILED;
		}
		
		
//		if (statusCode == 0) {
//			registerResponse.setNotif("Harap untuk tetap membuka halaman ini dan melanjutkan aktivasi akun.");
//		}
		
		String messageFormat = StringUtils.isEmpty(registerResponse.getInfo()) ? "%1$s" : "%1$s (%2$s)";
		String message = String.format(messageFormat, registerResponse.getNotif(), registerResponse.getInfo());

		if (14 == statusCode && DIGI_ERR_MSG_NIK_USED.equals(registerResponse.getNotif())) {
			message = this.messageSource.getMessage("businesslogic.user.nikalreadyregistered",
					new String[] { request.getUserData().getIdNo(), registerResponse.getEmailRegistered() },
					this.retrieveLocaleAudit(audit));
		}
		status.setCode(statusCode);
		status.setMessage(message);
		
		RegisterResponse response = new RegisterResponse();
		response.setStatus(status);
		return response;
	}
	
	@Override
	public RegisterDigisignResponseBean sendDataRegisterV2(DigisignRegisterRequest regisRequest, AuditContext audit) {
		UserBean userBean = regisRequest.getUserData();
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		
		MsTenant tenant = daoFactory.getTenantDao().getTenantByCode(regisRequest.getTenantCode());
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(regisRequest.getVendorCode());
		MsVendoroftenant vot = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		mapHeader.add(headerAuth, vot.getToken());
		mapHeader.add(headerContentType, headerMultipart);
		
		String statusEkyc = vendorLogic.getVendorEKYCStatus(regisRequest.getTenantCode(), regisRequest.getVendorCode(), audit);
		
		WebClient client = WebClient.create(urlDigisign + urlPreregist).headers(mapHeader);	
		
		RegisterDigisignRequest requestPrereg = new RegisterDigisignRequest();
		requestPrereg.setRegRequestBean(preparePreRegisterBean(userBean));
		
		if (!"1".equals(statusEkyc)) { // statusEkyc 0 = Validasi Dukcapil menggunakan ekyc client
			this.putNonEkycFieldsV2(tenant, regisRequest, requestPrereg.getRegRequestBean());
		}
		
		String jsonRequestPrereg = gson.toJson(requestPrereg);
		LOG.info(CONST_LOG_DIGI_REGISTER_REQUEST, jsonRequestPrereg);

		ContentDisposition cdJson = new ContentDisposition(CDVALUE_JSON_FIELD);
		
		List<Attachment> atts = new LinkedList<>();
		atts.add(new Attachment(JSON_FIELD, new ByteArrayInputStream(jsonRequestPrereg.getBytes()), cdJson));

		try {
			String imageKtpFileName = GlobalVal.PREFIX_PHOTO_ID_FILE_NAME + userBean.getLoginId();
			ContentDisposition cdKtp = new ContentDisposition(CONST_DIGI_CONTENT_DISPOSITION_KTP+imageKtpFileName+GlobalVal.FILE_DIGI_FORMAT_JPEG);
			byte[] dataImageIdPhoto = MssTool.imageStringToByteArray(userBean.getIdPhoto());
			atts.add(new Attachment(CONST_KTPPHOTO, new ByteArrayInputStream(dataImageIdPhoto), cdKtp));
		} catch (Exception e) {
			throw new DigisignException(messageSource.getMessage(DIGI_ERR_MSG_ERRORPROCESSINGKTP,
					null, retrieveLocaleAudit(audit)));
		}
		
		try {
			String imageSelfFileName = GlobalVal.PREFIX_PHOTO_SELF_FILE_NAME + userBean.getLoginId() ;
			ContentDisposition cdDiri = new ContentDisposition(CONST_DIGI_CONTENT_DISPOSITION_SELFIE+imageSelfFileName+GlobalVal.FILE_DIGI_FORMAT_JPEG);
			byte[] dataImageSelfPhoto = MssTool.imageStringToByteArray(userBean.getSelfPhoto());
			atts.add(new Attachment(CONST_SELFPHOTO, new ByteArrayInputStream(dataImageSelfPhoto), cdDiri));
		} catch (Exception e) {
			throw new DigisignException(messageSource.getMessage(DIGI_ERR_MSG_ERRORPROCESSINGSELFIE,
					null, retrieveLocaleAudit(audit)));
		}
		
		if (null != userBean.getNpwpPhoto()) {
			String imageNpwpFileName = GlobalVal.PREFIX_PHOTO_NPWP_FILE_NAME + userBean.getLoginId() ;
			ContentDisposition cdNpwp = new ContentDisposition("form-data; name=\"fotonpwp\"; filename=\""+imageNpwpFileName+".jpg\"");
			byte[] dataImageNpwpPhoto = MssTool.imageStringToByteArray(userBean.getNpwpPhoto());
			atts.add(new Attachment("fotonpwp", new ByteArrayInputStream(dataImageNpwpPhoto), cdNpwp));
		}
		
		if (null != userBean.getTtd()) {
			String imageTtdFileName = GlobalVal.PREFIX_TTD_FILE_NAME + userBean.getLoginId() ;
			ContentDisposition cdTtd = new ContentDisposition(CONST_DIGI_CONTENT_DISPOSITION_TTD+imageTtdFileName+CONST_PNG_EXTENSION);
			byte[] dataImageTtd = MssTool.imageStringToByteArray(userBean.getTtd());
			atts.add(new Attachment("ttd", new ByteArrayInputStream(dataImageTtd), cdTtd));
		}

		LOG.info("DIGI register request attachments size: {}", atts.size());
		
		MultipartBody body = new MultipartBody(atts);
		Date start = new Date();
		Response response = null;
		try {
			response = client.post(body); 
	    }  
		catch (Exception e) {
			//throw error khusus timed out 
			//udah coba pake catch SocketTimeoutException tapi error, jadi pake if
			if (e.getLocalizedMessage().contains(CONST_CONNECT_TIMED_OUT)) {
				throw new DigisignException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DIGISIGN_CONNECT_TIMED_OUT, null , retrieveLocaleAudit(audit)));
			} else if(e.getLocalizedMessage().contains(CONST_READ_TIMED_OUT) || e.getLocalizedMessage().contains(CONST_READ_TIMED_OUT_2)){
				throw new DigisignException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DIGISIGN_CONNECT_READ_TIMED_OUT, null , retrieveLocaleAudit(audit)));
			}
			else {
			   throw new DigisignException(DIGI_ERR_MSG_ERROR_REGISTER + e.getLocalizedMessage());
			}
		}
		
		Date end = new Date();
		logDuration(start, end);

		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());

		String digisignResult = StringUtils.EMPTY;
		try {
			digisignResult = IOUtils.toString(isReader);
		} catch (IOException e) {
			throw new DigisignException(DIGI_ERR_MSG_ERROR_REGISTER + e.getLocalizedMessage());
		}
		LOG.info(CONST_LOG_DIGI_REGISTER_RESPONSE, digisignResult);
		
		RegisterDigisignResponse preRegResp = gson.fromJson(digisignResult, RegisterDigisignResponse.class);
		return preRegResp.getRegResponseBean();
	}
	
	private MultipartBody prepareRegisterRequestForExternalRequest(RegisterExternalRequest request, MsVendoroftenant vendoroftenant, AuditContext audit) {
		// DATE OF BIRTH: yyyy-MM-dd to dd-MM-yyyy
		Date dob = MssTool.formatStringToDate(request.getTglLahir(), GlobalVal.DATE_FORMAT);
		
		RegisterDigisignRequestBean requestBean = new RegisterDigisignRequestBean();
		requestBean.setUserid(vendoroftenant.getEmailPartner());
		requestBean.setNama(StringUtils.upperCase(request.getNama()));
		requestBean.setJenisKelamin(GlobalVal.CODE_LOV_MALE.equals(request.getJenisKelamin()) ? GlobalVal.CODE_DIGISIGN_MALE : GlobalVal.CODE_DIGISIGN_FEMALE);
		requestBean.setTmpLahir(StringUtils.upperCase(request.getTmpLahir()));
		requestBean.setTglLahir(MssTool.formatDateToStringIn(dob, GlobalVal.DATE_FORMAT_DASH_IN));
		requestBean.setAlamat(StringUtils.upperCase(request.getAlamat()));
		requestBean.setProvinci(StringUtils.upperCase(request.getProvinsi()));
		requestBean.setKota(StringUtils.upperCase(request.getKota()));
		requestBean.setKecamatan(StringUtils.upperCase(request.getKecamatan()));
		requestBean.setKelurahan(StringUtils.upperCase(request.getKelurahan()));
		requestBean.setKodepos(StringUtils.upperCase(request.getKodePos()));
		requestBean.setTlp(request.getTlp());
		requestBean.setIdktp(request.getIdKtp());
		requestBean.setNpwp(StringUtils.EMPTY);
		requestBean.setEmail(StringUtils.upperCase(request.getEmail()));
		requestBean.setRedirect(true);
		
		RegisterDigisignRequest registerRequest = new RegisterDigisignRequest();
		registerRequest.setRegRequestBean(requestBean);
		
		String jsonRequest = gson.toJson(registerRequest);
		LOG.info(CONST_LOG_DIGI_REGISTER_REQUEST, jsonRequest);
		
		List<Attachment> atts = new LinkedList<>();
		ContentDisposition cdJson = new ContentDisposition(CDVALUE_JSON_FIELD);
		atts.add(new Attachment(JSON_FIELD, new ByteArrayInputStream(jsonRequest.getBytes()), cdJson));
		
		try {
			String imageKtpFileName = GlobalVal.PREFIX_PHOTO_ID_FILE_NAME + request.getEmail();
			ContentDisposition cdKtp = new ContentDisposition(CONST_DIGI_CONTENT_DISPOSITION_KTP+imageKtpFileName+GlobalVal.FILE_DIGI_FORMAT_JPEG);
			byte[] dataImageIdPhoto = MssTool.imageStringToByteArray(request.getIdPhoto());
			atts.add(new Attachment(CONST_KTPPHOTO, new ByteArrayInputStream(dataImageIdPhoto), cdKtp));
		} catch (Exception e) {
			throw new DigisignException(messageSource.getMessage(DIGI_ERR_MSG_ERRORPROCESSINGKTP,
					null, retrieveLocaleAudit(audit)));
		}
		
		try {
			String imageSelfFileName = GlobalVal.PREFIX_PHOTO_SELF_FILE_NAME + request.getEmail();
			ContentDisposition cdDiri = new ContentDisposition(CONST_DIGI_CONTENT_DISPOSITION_SELFIE+imageSelfFileName+GlobalVal.FILE_DIGI_FORMAT_JPEG);
			byte[] dataImageSelfPhoto = MssTool.imageStringToByteArray(request.getSelfPhoto());
			atts.add(new Attachment(CONST_SELFPHOTO, new ByteArrayInputStream(dataImageSelfPhoto), cdDiri));
		} catch (Exception e) {
			throw new DigisignException(messageSource.getMessage(DIGI_ERR_MSG_ERRORPROCESSINGSELFIE,
					null, retrieveLocaleAudit(audit)));
		}
		
		// Prepare ttd image
		GenerateSignPicture generateSignPic = new GenerateSignPicture();
		byte[] ttdPic = generateSignPic.generateSignatureSpecimen(request.getNama());
		String ttdBase64 = Base64.getEncoder().encodeToString(ttdPic);
		
		String imageTtdFileName = GlobalVal.PREFIX_TTD_FILE_NAME + request.getEmail();
		ContentDisposition cdTtd = new ContentDisposition(CONST_DIGI_CONTENT_DISPOSITION_TTD+imageTtdFileName+CONST_PNG_EXTENSION);
		byte[] dataImageTtd = MssTool.imageStringToByteArray(ttdBase64);
		atts.add(new Attachment("ttd", new ByteArrayInputStream(dataImageTtd), cdTtd));
		
		return new MultipartBody(atts);
	}

	@Override
	public RegisterDigisignResponseBean registerForExternalRequest(RegisterExternalRequest request, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		
		MsVendoroftenant vendoroftenant = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(headerAuth, vendoroftenant.getToken());
		mapHeader.add(headerContentType, headerMultipart);
		
		MultipartBody body = prepareRegisterRequestForExternalRequest(request, vendoroftenant, audit);
		WebClient client = WebClient.create(urlDigisign + urlPreregist).headers(mapHeader);
		
		HTTPConduit httpConduit = WebClient.getConfig(client).getHttpConduit();
		httpConduit.getClient().setConnectionTimeout(10_000); // 10 seconds
		httpConduit.getClient().setReceiveTimeout(75_000); // 75 seconds
		
		Response response = null;
		try {
			response = client.post(body);
		} catch (Exception e) {
			
			if (e.getLocalizedMessage().contains(CONST_CONNECT_TIMED_OUT)) {
				throw new DigisignException(getMessage(GlobalKey.MESSAGE_ERROR_DIGISIGN_CONNECT_TIMED_OUT, null , audit));
			}
			
			if(e.getLocalizedMessage().contains(CONST_READ_TIMED_OUT) || e.getLocalizedMessage().contains(CONST_READ_TIMED_OUT_2)){
				throw new DigisignException(getMessage(GlobalKey.MESSAGE_ERROR_DIGISIGN_CONNECT_READ_TIMED_OUT, null , audit));
			}
			
			throw new DigisignException("Digisign register error: " + e.getLocalizedMessage());
		}
		
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String jsonResponse = null;
		try {
			jsonResponse = IOUtils.toString(isReader);
			LOG.info(CONST_LOG_DIGI_REGISTER_RESPONSE, jsonResponse);
		} catch (IOException e) {
			throw new DigisignException("Digisign register error: " + e.getLocalizedMessage());
		}
		
		try {
			RegisterDigisignResponse preRegResp = gson.fromJson(jsonResponse, RegisterDigisignResponse.class);
			return preRegResp.getRegResponseBean();
		} catch (Exception e) {
			String errorMessage = getHtmlErrorMessage(jsonResponse);
			throw new DigisignException(errorMessage);
		}
		
	}

	@Override
	public AmMsuser insertRegisteredUserExternal(RegisterExternalRequest request, boolean isActivated, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		
		userValidatorLogic.removeUnnecessaryRegisterExternalParam(request, tenant);
		boolean doesNotNeedPassword = tenantSettingsLogic.getSettingValue(tenant, GlobalVal.CODE_LOV_TENANT_SETTING_ALLOW_NO_PASSWORD_FOR_ACTIVATION);
		
		String hashedPhone = MssTool.getHashedString(request.getTlp());
		String hashedIdNo = MssTool.getHashedString(request.getIdKtp());
		String emailService = (request.getIdEmailHosting() != null && request.getIdEmailHosting() != 0) ? "1" :"0";
		
		MsEmailHosting emailHosting = daoFactory.getEmailDao().getEmailHostingById(request.getIdEmailHosting());
		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCodeNewTran(GlobalVal.ROLE_CUSTOMER, tenant.getTenantCode());
		
		AmMsuser user = daoFactory.getUserDao().getUserByIdNoNewTran(request.getIdKtp());
		if (null == user) {
			// insert user
			MsOffice office = daoFactory.getOfficeDao().getFirstOfficeByTenantCode(tenant.getTenantCode());
			String[] separatedName = request.getNama().split(" ");
			String hashedPassword = null;
			if (doesNotNeedPassword) {
				hashedPassword = "noPassword";
			} else {
				hashedPassword = PasswordHash.createHash(request.getPassword());
			}
			
			user = new AmMsuser();
			user.setIsActive("1");
			user.setIsDeleted("0");
			user.setLoginId(StringUtils.upperCase(request.getEmail()));
			user.setFullName(StringUtils.upperCase(request.getNama()));
			user.setInitialName(StringUtils.upperCase(separatedName[0]));
			user.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
			user.setPassword(hashedPassword);
			user.setFailCount(0);
			user.setIsLoggedIn("0");
			user.setIsLocked("0");
			user.setIsDormant("0");
			user.setMsOffice(office);
			user.setChangePwdLogin("0");
			user.setUsrCrt(audit.getCallerId());
			user.setDtmCrt(new Date());
			user.setEmailService(emailService);
			user.setMsEmailHosting(emailHosting);
			user.setHashedPhone(hashedPhone);
			user.setHashedIdNo(hashedIdNo);
			daoFactory.getUserDao().insertUserNewTran(user);
		} else {
			user.setIsDormant("0");
			user.setUsrUpd(audit.getCallerId());
			user.setDtmUpd(new Date());
			daoFactory.getUserDao().updateUserNewTran(user);
		}
		
		MsUseroftenant useroftenant = daoFactory.getUseroftenantDao().getUserTenantByIdMsUserAndTenantCodeNewTran(user.getIdMsUser(), tenant.getTenantCode());
		if (null == useroftenant) {
			useroftenant = new MsUseroftenant();
			useroftenant.setAmMsuser(user);
			useroftenant.setMsTenant(tenant);
			useroftenant.setUsrCrt(audit.getCallerId());
			useroftenant.setDtmCrt(new Date());
			daoFactory.getUseroftenantDao().insertUseroftenantNewTran(useroftenant);
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCodeNewTran(user.getIdMsUser(), GlobalVal.VENDOR_CODE_VIDA);
		if (null == vendorUser) {
			byte[] phoneBytea = personalDataEncLogic.encryptFromString(request.getTlp());
			Date activatedDate = new Date();
			Date expiredDate = DateUtils.addYears(activatedDate, 1);
			
			vendorUser = new MsVendorRegisteredUser();
			vendorUser.setSignerRegisteredEmail(StringUtils.upperCase(request.getEmail()));
			vendorUser.setIsActive(isActivated ? "1" : "0");
			vendorUser.setUsrCrt(audit.getCallerId());
			vendorUser.setDtmCrt(new Date());
			vendorUser.setAmMsuser(user);
			vendorUser.setMsVendor(vendor);
			vendorUser.setIsRegistered("1");
			vendorUser.setActivatedDate(activatedDate);
			vendorUser.setCertExpiredDate(expiredDate);
			vendorUser.setHashedSignerRegisteredPhone(hashedPhone);
			vendorUser.setPhoneBytea(phoneBytea);
			vendorUser.setEmailService(emailService);
			vendorUser.setMsEmailHosting(emailHosting);
			daoFactory.getVendorRegisteredUserDao().insertVendorRegisteredUserNewTran(vendorUser);
		}
		
		PersonalDataBean personalData = daoFactory.getUserDao().getUserDataByIdMsUserNewTrx(user.getIdMsUser(), false);
		if (null == personalData || null == personalData.getUserPersonalData()) {
			
			Date dob = MssTool.formatStringToDate(request.getTglLahir(), GlobalVal.DATE_FORMAT);
			byte[] selfiePhoto = StringUtils.isBlank(request.getSelfPhoto()) ? null : MssTool.imageStringToByteArray(request.getSelfPhoto());
			byte[] idPhoto = StringUtils.isBlank(request.getIdPhoto()) ? null : MssTool.imageStringToByteArray(request.getIdPhoto());
			
			ZipcodeCityBean zipcodeCityBean = new ZipcodeCityBean();
			zipcodeCityBean.setProvinsi(request.getProvinsi());
			zipcodeCityBean.setKota(request.getKota());
			zipcodeCityBean.setKecamatan(request.getKecamatan());
			zipcodeCityBean.setKelurahan(request.getKelurahan());
			zipcodeCityBean.setZipcode(request.getKodePos());
			
			PersonalDataBean personalDataBean = new PersonalDataBean();
			AmUserPersonalData userPersonalData = new AmUserPersonalData();
			userPersonalData.setUsrCrt(audit.getCallerId());
			userPersonalData.setDtmCrt(new Date());
			userPersonalData.setGender(StringUtils.upperCase(request.getJenisKelamin()));
			userPersonalData.setDateOfBirth(dob);
			userPersonalData.setAmMsuser(user);
			userPersonalData.setPlaceOfBirth(StringUtils.upperCase(request.getTmpLahir()));
			userPersonalData.setEmail(StringUtils.upperCase(user.getLoginId()));
			userPersonalData.setZipcodeBean(zipcodeCityBean);
			personalDataBean.setUserPersonalData(userPersonalData);
			personalDataBean.setSelfPhotoRaw(selfiePhoto);
			personalDataBean.setPhotoIdRaw(idPhoto);
			personalDataBean.setIdNoRaw(request.getIdKtp());
			personalDataBean.setPhoneRaw(request.getTlp());
			personalDataBean.setAddressRaw(StringUtils.upperCase(request.getAlamat()));
			daoFactory.getUserDao().insertUserPersonalData(personalDataBean);
		}
		
		AmMemberofrole userRole = daoFactory.getRoleDao().getMemberofroleNewTran(user, role);
		if (null == userRole) {
			userRole = new AmMemberofrole();
			userRole.setAmMsrole(role);
			userRole.setAmMsuser(user);
			userRole.setUsrCrt(audit.getCallerId());
			userRole.setDtmCrt(new Date());
			daoFactory.getRoleDao().insertMemberOfRoleNewTran(userRole);
		}
		
		return user;
	}

	@Override
	public RegisterVerificationStatusBean buildVerificationStatusBean(RegisterDigisignResponseBean registerResponse, AuditContext audit) {
		if (GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(registerResponse.getResult()) && "Email sudah terdaftar, namun belum melakukan aktivasi. Silahkan untuk melakukan aktivasi sebelum data dihapus dari daftar aktivasi.".equals(registerResponse.getNotif())) {
			return new RegisterVerificationStatusBean();
		}
		
		if (GenericDigisignLogic.DIGI_ERR_MSG_NIK_USED.equals(registerResponse.getNotif())) {
			return new RegisterVerificationStatusBean();
		}
		
		if (GenericDigisignLogic.DIGI_REGISTERED_MSG.equals(registerResponse.getNotif())) {
			return new RegisterVerificationStatusBean();
		}
		
		if (GlobalVal.STATUS_DIGISIGN_SUCCESS.equals(registerResponse.getResult())) {
			RegisterVerificationStatusBean bean = new RegisterVerificationStatusBean();
			bean.setNik(GlobalVal.NIK_REGISTERED);
			bean.setName(GlobalVal.CONST_TRUE);
			bean.setBirthDate(GlobalVal.CONST_TRUE);
			bean.setSelfieCheck(GlobalVal.CONST_TRUE);
			bean.setLiveness(GlobalVal.CONST_TRUE);
			return bean;
		}
		
		
		if (null == registerResponse.getData()) {
			return new RegisterVerificationStatusBean();
		}
		
		// Handling gagal lainnya
		RegisterDigisignResponseDataBean verifResult = registerResponse.getData();
		RegisterVerificationStatusBean bean = new RegisterVerificationStatusBean();
		
		if (Boolean.FALSE.equals(verifResult.getNik())) {
			bean.setNik(GlobalVal.CONST_FALSE);
		}
		if (Boolean.TRUE.equals(verifResult.getNik())) {
			bean.setNik(GlobalVal.CONST_TRUE);
		}
		
		if (Boolean.FALSE.equals(verifResult.getName())) {
			bean.setName(GlobalVal.CONST_FALSE);
		}
		if (Boolean.TRUE.equals(verifResult.getName())) {
			bean.setName(GlobalVal.CONST_TRUE);
		}
		
		if (Boolean.FALSE.equals(verifResult.getBirthdate())) {
			bean.setBirthDate(GlobalVal.CONST_FALSE);
		}
		if (Boolean.TRUE.equals(verifResult.getBirthdate())) {
			bean.setBirthDate(GlobalVal.CONST_TRUE);
		}
		
		if (Boolean.FALSE.equals(verifResult.getSelfie())) {
			bean.setSelfieCheck(GlobalVal.CONST_FALSE);
		}
		if (Boolean.TRUE.equals(verifResult.getSelfie())) {
			bean.setSelfieCheck(GlobalVal.CONST_TRUE);
		}
		
		return bean;
	}

	@Override
	public boolean cutVerifAndTextVerif(RegisterDigisignResponseBean registerResponse, AuditContext audit) {
		return DIGI_ERR_MSG_NO_FACE_SELFIE.equals(registerResponse.getInfo())
				|| DIGI_ERR_MSG_NO_FACE_KTP.equals(registerResponse.getInfo())
				|| DIGI_ERR_MSG_LIGHTING_PROBLEM.equals(registerResponse.getInfo())
				|| DIGI_ERR_MSG_FACE_VERIF_FAILED.equals(registerResponse.getInfo());
	}

	@Override
	public String getRegisterErrorMessage(RegisterDigisignResponseBean registerResponse, AuditContext audit) {
		switch (registerResponse.getInfo()) {
			case DIGI_ERR_MSG_NO_FACE_SELFIE:
				return "Verifikasi user gagal. Tidak ditemukan foto wajah pada foto selfie.";
			case DIGI_ERR_MSG_NO_FACE_KTP:
				return "Verifikasi user gagal. Tidak ditemukan foto wajah pada KTP.";
			case DIGI_ERR_MSG_LIGHTING_PROBLEM:
				return "Verifikasi user gagal. Tidak ditemukan foto wajah pada foto selfie.";
			case DIGI_ERR_MSG_FACE_VERIF_FAILED:
				return "Verifikasi user gagal. Verifikasi wajah gagal.";
			default:
				return registerResponse.getInfo();
		}
	}
}
