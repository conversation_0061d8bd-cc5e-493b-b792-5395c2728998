package com.adins.exceptions;

import java.util.Collections;

import com.adins.framework.exception.AdInsException;

@SuppressWarnings("serial")
public class ChangeException extends AdInsException {
	private static final String ENTITY_CODE = "ENTITY_CODE";	

	public ChangeException(String message) {
		super(message);
	}
	
	public ChangeException(String message, String code) {
		super(message, Collections.singletonMap(ENTITY_CODE, code));
	}
	
	@Override
	public int getErrorCode() {
		return StatusCode.ERROR_CHANGE;
	}
}
