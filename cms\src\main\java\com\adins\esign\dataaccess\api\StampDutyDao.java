package com.adins.esign.dataaccess.api;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrStampDuty;
import com.adins.esign.model.custom.StampDutyBean;
import com.adins.esign.model.custom.StampDutyHistoryBean;
import com.adins.esign.model.custom.StampDutyReportBean;

public interface StampDutyDao {
	BigInteger countAvailableStampDutyByIdTenant(long idMsTenant);
	BigInteger countUsedStampDutyByTenantCodeAndInvoiceNo(String tenantCode, String invoiceNo);
	Map<String, Object> getAvailableStampDutyByIdTenant(long idMsTenant);
	
	TrStampDuty getStampDutyByStampDutyNo(String stampDutyNo);
	TrStampDuty getStampDutyById(long idStampDuty);
	TrStampDuty getStampDutyByIdNewTran(long idStampDuty);
	TrStampDuty getLatestStampDuty(TrDocumentD document);
	
	List<StampDutyBean> getListStampDutyByIdDocumentHNewTran(long documentHeaderId);
	StampDutyBean getStampDutyByInvoiceNo(String invoiceNo);
	void deleteListStampDutyByTenantCodeAndInvoiceNoAndStampDutyStatus(String tenantCode, String vendorCode, String invoiceNo, String stampDutyStatus); 
	void updateTrStampDuty(TrStampDuty stampDuty);
	void updateTrStampDutyNewTran(TrStampDuty stampDuty);
	
	void updateNativeStringTrStampDuty(TrStampDuty stampDuty);
	void updateNativeStringTrStampDutyNewTran(TrStampDuty stampDuty);
	
	void insertTrStampDuty(TrStampDuty stampDuty);
	void insertTrStampDutyNewTran(TrStampDuty stampDuty);
	
	
	void insertTrStampDuty(String loginId, Long idMsLovSdtStatus, Long idMsTenant, Long idMsVendor, Integer fee, String trxNo, Integer qty);
	void deleteTrStampDuty(List<TrStampDuty> listTrStampDuty);
	List<StampDutyBean> getListStampDuty(String tenantCode, String invoiceNo, String stampDutyStatus,
			String stampDutyNo, String businessLineCode, String officeCode, String regionCode,
			Date usedDateStart, Date usedDateEnd, int min, int max, long idBalanceType);
	List<StampDutyBean> getListReverseTopup(String tenantCode, String invoiceNo, String stampDutyStatus, boolean isReversalSearchFilter);
	int countListStampDuty(String tenantCode, String invoiceNo, String stampDutyStatus, 
			String stampDutyNo, String businessLineCode, String officeCode, String regionCode,
			Date usedDateStart, Date usedDateEnd, long idBalanceType);
	List<StampDutyHistoryBean> getListStampDutyDetail(String tenantCode, Long idStampDuty);
	Object getLatestStampDutyDetailTrxType (Long idStampDuty);
	List<Map<String, Object>> getListStampDutyByIdDocumentH(Long idDocumentH);
	List<StampDutyReportBean> getListStampDutyReport(String tenantCode, String invoiceNo, String stampDutyStatus, String stampDutyNo,
			String businessLineCode, String officeCode, String regionCode, Date usedDateStart, Date usedDateEnd);
	// Available SDT For Stamping
	BigInteger countAvailableStampDutyForDocument(Long idDocumentD);
	List<Map<String, Object>> getIdStampDutyForDocument(Long idDocumentD);
	List<Map<String, Object>> getIdStampDutyForDocumentNewTran(TrDocumentD document);
}
