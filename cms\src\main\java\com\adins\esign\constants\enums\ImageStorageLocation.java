package com.adins.esign.constants.enums;

public enum ImageStorageLocation {
    DATABASE("1"), FILE_SYSTEM("0"), DMS("2");
    
    private String storageFlag;

    private ImageStorageLocation(String flag){
        this.storageFlag = flag;
    }
    
    public String getFlag() {
        return storageFlag;
    }
    
    public static ImageStorageLocation fromString(String flag) {
        switch (flag) {
            case "0": return FILE_SYSTEM;
            case "1": return DATABASE;
            case "2": return DMS;
            default: return DATABASE; //default=DB if not found
        }        
    }
}
