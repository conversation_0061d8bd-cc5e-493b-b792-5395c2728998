package com.adins.esign.businesslogic.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.util.List;

import javax.transaction.Transactional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ImportResource;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.TestSetUpLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.custom.GeneralSettingBean;
import com.adins.esign.model.custom.MenuUserBean;
import com.adins.framework.persistence.dao.model.AuditContext;

@ExtendWith(SpringExtension.class)
@ImportResource("classpath:application-test-context.xml")
@EnableTransactionManagement
@Transactional
class GenericCommonLogicTest extends BaseLogic{
//	@Autowired private CommonLogic commonLogic;
//	@Autowired private TestSetUpLogic testSetUpLogic;
//	
//	private AuditContext auditContext = new AuditContext();
//	private AmGeneralsetting generalSetting;
//	private MsLov lov;
//	private AmMsuser user;
//	
//	@BeforeEach
//	@Rollback(true)
//	public void setUp() {
//		auditContext.setCallerId("1");
//		generalSetting = testSetUpLogic.setUpGenSet("JUNIT");
//		lov = testSetUpLogic.setUpLovByGroupAndCode("LOVGROUPJUNIT","CODEJUNIT");
//		user = daoFactory.getUserDao().getUserByLoginId(GlobalVal.USER_ADMIN_LOGIN_ID);
//	}
//
//	@Test
//	@Rollback(true)
//	void getListGeneralSettingTest( ) {
//		testSetUpLogic.setUpGenSet(GlobalVal.SUBSYSTEM_ESIGN);
//		List<GeneralSettingBean> listGeneralSetting = commonLogic.listGeneralSetting(auditContext);
//		assertFalse(listGeneralSetting.isEmpty());
//	}
//	
//	@Test
//	void getListGeneralSettingTestWithoutData( ) {
//		List<GeneralSettingBean> listGeneralSetting = commonLogic.listGeneralSetting(auditContext);
//		assertTrue(listGeneralSetting.isEmpty());
//	}
//	
//	@Test
//	@Rollback(true)
//	void getListMenuByUserIdTest( ) {
//		testSetUpLogic.setUpMobileMenu("JUNIT");
//		List<MenuUserBean> listMenuUser = commonLogic.listMenuByUserId(user.getUuidMsUser(), auditContext);
//		assertFalse(listMenuUser.isEmpty());
//	}
//	
//	@Test
//	void getListMenuByUserIdTestWithoutData( ) {
//		List<MenuUserBean> listMenuUser = commonLogic.listMenuByUserId(user.getUuidMsUser(), auditContext);
//		assertTrue(listMenuUser.isEmpty());
//	}
//	
//	@Test
//	@Rollback(true)
//	void getGeneralSettingByCodeTest( ) {
//		AmGeneralsetting result = commonLogic.getGeneralSettingByCode(generalSetting.getGsCode(), auditContext);
//		assertEquals(result.getGsValue(), generalSetting.getGsValue());
//	}
//	
//	@Test
//	void getGeneralSettingByCodeTestWithoutData( ) {
//		AmGeneralsetting result = commonLogic.getGeneralSettingByCode("CODEJUNITNULL", auditContext);
//		assertNull(result);
//	}
//	
//	@Test
//	@Rollback(true)
//	void getGeneralSettingValueByCodeTest( ) {
//		String gsValue = commonLogic.getGeneralSettingValueByCode(generalSetting.getGsCode(), auditContext);
//		assertEquals( generalSetting.getGsValue(),gsValue);
//	}
//	
//	@Test
//	void getGeneralSettingValueByCodeTestWithoutData( ) {
//		String gsValue = commonLogic.getGeneralSettingValueByCode("CODEJUNITNULL", auditContext);
//		assertEquals("", gsValue);
//	}
//	
//	@Test
//	@Rollback(true)
//	void getLovByCodeTest() {
//		MsLov result = commonLogic.getLovByCode(lov.getCode());
//		assertEquals(lov.getDescription(), result.getDescription());
//	}
//	
//	@Test
//	void getLovByCodeTestWithoutData() {
//		MsLov result = commonLogic.getLovByCode("CODEJUNITNULL");
//		assertNull(result);
//	}
//	
//	@Test
//	@Rollback(true)
//	void getLovByGroupAndCodeTest() {
//		MsLov result = commonLogic.getLovByGroupAndCode(lov.getLovGroup(), lov.getCode());
//		assertEquals(lov.getDescription(), result.getDescription());
//	}
//	
//	@Test
//	void getLovByGroupAndCodeTestWithoutData() {
//		MsLov result = commonLogic.getLovByGroupAndCode("LOVGROUPJUNITNULL", "CODEJUNITNULL");
//		assertNull(result);
//	}
}
