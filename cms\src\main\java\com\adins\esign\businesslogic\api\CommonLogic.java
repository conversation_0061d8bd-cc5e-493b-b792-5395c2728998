package com.adins.esign.businesslogic.api;

import java.util.List;

import javax.annotation.security.RolesAllowed;

import org.apache.poi.ss.formula.functions.T;

import com.adins.am.model.AmGeneralsetting;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.custom.MenuUserBean;
import com.adins.esign.webservices.model.GetDocumentEMateraiTypeRequest;
import com.adins.esign.webservices.model.GetDocumentEMateraiTypeResponse;
import com.adins.esign.webservices.model.GetDocumentTypeResponse;
import com.adins.esign.webservices.model.GetListPsrePriorityExternalResponse;
import com.adins.esign.webservices.model.GetPeruriDocumentTypeRequest;
import com.adins.esign.webservices.model.LovListRequest;
import com.adins.esign.webservices.model.LovListResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

@SuppressWarnings("hiding")
public interface CommonLogic {	
	AmGeneralsetting getGeneralSettingByCode(String gsCode, AuditContext audit);
	String getGeneralSettingValueByCode(String gsCode, AuditContext audit);

	List<MenuUserBean> listMenuByRoleAndTenant(String roleCode, String tenantCode, AuditContext audit);

	MsLov getLovByGroupAndCode(String lovGroup, String lovCode, AuditContext audit);
	LovListResponse getLovByGroupAndConstraint(LovListRequest request);
	LovListResponse getLovEmbedByGroupAndConstraint(LovListRequest request, AuditContext audit);
	String getAesEncryptionKey(AuditContext audit);
	
	<T> T decryptMessageToClass(String encryptedMessage, AuditContext audit, Class<T> classOfT);
	<T> T decryptMessageToClass(String encryptedMessage, String key, Class<T> classOfT, AuditContext audit);
	
	String decryptMessageToString(String encryptedMessage, AuditContext audit);
	String decryptMessageToString(String encryptedMessage, String key, AuditContext audit);
	
	String encryptMessageToString(String message, AuditContext audit);
	String encryptMessageToString(String message, String key, AuditContext audit);
	
	@RolesAllowed("ROLE_CORESYSTEM")
	GetDocumentEMateraiTypeResponse getListPeruriDocTypeWithApiKey(String apiKey, AuditContext audit);
	GetDocumentEMateraiTypeResponse getListPeruriDocType(GetPeruriDocumentTypeRequest request, AuditContext audit);
	
	GetDocumentEMateraiTypeResponse getListPeruriDocTypeEmbed(GetDocumentEMateraiTypeRequest request, AuditContext audit);
	GetDocumentTypeResponse getListDocumentTypeWithApiKey(String apiKey, AuditContext audit);
	GetListPsrePriorityExternalResponse getListPsrePriority(String apiKey, AuditContext audit);
	
}
