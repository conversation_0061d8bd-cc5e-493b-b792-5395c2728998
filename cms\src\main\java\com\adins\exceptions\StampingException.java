package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

/**
 * Only throw when stamping reached max error count
 */
public class StampingException extends AdInsException {
	private static final long serialVersionUID = 1L;

	public StampingException(String message) {
		super(message);
	}

	@Override
	public int getErrorCode() {
		return StatusCode.EMETERAI_PROCESS_FAILED;
	}
}
