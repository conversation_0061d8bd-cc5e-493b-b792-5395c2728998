package com.adins.esign.webservices.frontend.endpoint;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.api.MenuLogic;
import com.adins.esign.webservices.frontend.api.MenuService;
import com.adins.esign.webservices.model.GetListMenuOfRoleRequest;
import com.adins.esign.webservices.model.GetListMenuResponse;
import com.adins.esign.webservices.model.UpdateMenuOfRoleRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssRequestType;
import com.adins.framework.service.base.model.MssResponseType;

import io.swagger.annotations.Api;

@Component
@Path("/menu")
@Api(value = "MenuService")
@Produces({ MediaType.APPLICATION_JSON })
public class GenericMenuServiceEndpoint implements MenuService {
	
	@Autowired MenuLogic menuLogic;

	@Override
	@POST 
	@Path("/s/updateMenuOfRole")
	public MssResponseType updateMenuOfRole(UpdateMenuOfRoleRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return menuLogic.updateMenuOfRole(request, audit);
	}

	@Override
	@POST 
	@Path("/s/getListManageableMenu")
	public GetListMenuResponse getListManageableMenu(MssRequestType request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return menuLogic.getListManageableMenu(audit);
	}

	@Override
	@POST 
	@Path("/s/getListMenuOfRole")
	public GetListMenuResponse getListMenuOfRole(GetListMenuOfRoleRequest request) {
		AuditContext audit = request.getAudit().toAuditContext();
		return menuLogic.getListMenuOfRole(request, audit);
	}

}
