package com.adins.esign.model.custom.validation;

import java.util.Objects;

public class SendOtpValidationBean {

    String tenantCode;
    String phoneNo;

    public String getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getPhoneNo() {
        return this.phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    @Override
    public boolean equals(Object object) {
        if (this == object) {
            return true;
        }
        
        if (null == object) {
            return false;
        }

        if (!(object instanceof SendOtpValidationBean)) {
            return false;
        }

        SendOtpValidationBean bean = (SendOtpValidationBean) object;

        if (!Objects.equals(phoneNo, bean.getPhoneNo())) {
            return false;
        }

        return Objects.equals(tenantCode, bean.getTenantCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.phoneNo, this.tenantCode);
    }

    @Override
    public String toString() {
        return "{" +
               "tenantCode='" + getTenantCode() + '\'' +
               ", phoneNo='" + getPhoneNo() + '\'' +
               '}';
    }
}