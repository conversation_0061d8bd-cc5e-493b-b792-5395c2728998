package com.adins.esign.webservices.frontend.api;

import com.adins.esign.webservices.model.GetListJobByJobProcessTypeRequest;
import com.adins.esign.webservices.model.GetListJobRequest;
import com.adins.esign.webservices.model.GetListJobResponse;

public interface JobService {
	GetListJobResponse getListJobByJobType(GetListJobRequest request);
	GetListJobResponse getListJobByJobProcessType(GetListJobByJobProcessTypeRequest request);
}
