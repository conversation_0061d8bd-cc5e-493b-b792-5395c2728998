package com.adins.esign.dataaccess.api;

import java.util.List;
import java.util.Map;

import com.adins.esign.model.MsPaymentsigntypeoftenant;

public interface PaymentSignTypeDao {
	List<Map<String, Object>> getListPaymentSignTypeByTenantCode(String tenantCode);
	List<Map<String, Object>> getListPaymentSignTypeByTenantCodeAndVendorCode(String tenantCode, String vendorCode);
	MsPaymentsigntypeoftenant getPaymentSignTypeByPaymentSignTypeVendorCodeAndTenantCode(String lovCode, String vendorCode, String tenantCode);
}