package com.adins.esign.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.webservices.model.ListInquiryDocumentRequest;
import com.adins.esign.webservices.model.ListInquiryDocumentResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface DocumentInquiryLogic {

    @RolesAllowed({"ROLE_INQUIRY"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ListInquiryDocumentResponse getListInquiryDocument(ListInquiryDocumentRequest request, AuditContext audit);

}
