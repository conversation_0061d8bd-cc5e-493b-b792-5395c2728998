package com.adins.am.businesslogic.api;

import java.util.Map;

import javax.annotation.security.RolesAllowed;

import com.adins.am.model.AmGeneralsetting;
import com.adins.esign.webservices.model.GetGeneralSettingRequest;
import com.adins.esign.webservices.model.GetGeneralSettingResponse;
import com.adins.framework.persistence.dao.model.AuditContext;


public interface GeneralSettingLogic {
	@RolesAllowed("ROLE_READ_GENERAL")
	Map<String, Object> listGeneralSetting(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	@RolesAllowed("ROLE_READ_GENERAL")
	AmGeneralsetting getGeneralSetting(long uuid, AuditContext callerId);
	@RolesAllowed("ROLE_UPD_GENERAL")
	void updateGeneralSetting(AmGeneralsetting obj, AuditContext callerId, long uuid);
	
	AmGeneralsetting getGeneralSetting(String code, AuditContext callerId);

	GetGeneralSettingResponse getGeneralSetting(GetGeneralSettingRequest request, AuditContext audit);
	
}
