package com.adins.esign.job;

import java.util.concurrent.BlockingQueue;

import org.quartz.DisallowConcurrentExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.esign.businesslogic.api.ErrorReportLogic;
import com.adins.esign.model.queuebean.UpdErrHistRerunProcessBean;
import com.adins.framework.persistence.dao.model.AuditContext;

@DisallowConcurrentExecution
@Component
public class UpdErrHistRerunProcessJob {
	private static final Logger LOG = LoggerFactory.getLogger(UpdErrHistRerunProcessJob.class);
	private static final String SCHEDULER = "SCHEDULER";
	
	@Autowired private ErrorReportLogic errorReportLogic;
	
	public void takeQueueUpdErrHistRerunProcess() {
		BlockingQueue<UpdErrHistRerunProcessBean> updateQueue = QueuePublisher.getQueueUpdErrHistRerunProcess();
		AuditContext audit = new AuditContext(SCHEDULER);
		
		while (!updateQueue.isEmpty()) {
			try {
				UpdErrHistRerunProcessBean bean = updateQueue.take();
				errorReportLogic.updateErrorHistoryRerunProcess(bean.getNik(), bean.getIdVendor(), audit);
			} catch (Exception e) {
				LOG.error("Error on update error history rerun process", e);
			}
		}
		LOG.info("Take queue (update error history rerun process) finished");
	}
}
