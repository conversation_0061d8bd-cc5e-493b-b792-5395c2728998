package com.adins.esign.businesslogic.api;

import java.io.IOException;
import java.io.UnsupportedEncodingException;

import javax.annotation.security.RolesAllowed;
import javax.mail.MessagingException;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.custom.GenerateInvLinkRequest;
import com.adins.esign.model.custom.GenerateInvLinkResponse;
import com.adins.esign.webservices.model.GenerateInvitationLinkForExpiredCertRequest;
import com.adins.esign.webservices.model.GenerateInvitationLinkForExpiredCertResponse;
import com.adins.esign.webservices.model.RegenerateInvitationRequest;
import com.adins.esign.webservices.model.RegenerateInvitationResponse;
import com.adins.esign.webservices.model.UpdateInvDataRequest;
import com.adins.esign.webservices.model.UpdateInvDataResponse;
import com.adins.esign.webservices.model.embed.GenerateInvitationLinkForExpiredCertEmbedRequest;
import com.adins.esign.webservices.model.external.GeneratInvLinkExternalRequest;
import com.adins.esign.webservices.model.external.GeneratInvLinkExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface InvitationLinkLogic {
	void deactivateInvitationLink(TrInvitationLink invitationLink, AuditContext audit);
	String getDataUserInvitation(String receiverDetail, String vendorCode, AuditContext audit);
	boolean isInvitationLinkRegenerable(TrInvitationLink invLink, AuditContext audit);
	boolean isInvitationLinkEditable(TrInvitationLink invLink, AuditContext audit);
	
	@RolesAllowed({"ROLE_CORESYSTEM", "ROLE_GEN_INV"})
	GenerateInvLinkResponse generateAndSendRegInvLink(GenerateInvLinkRequest request, boolean isMenu, AuditContext audit) throws Exception;

	@RolesAllowed({"ROLE_CORESYSTEM", "ROLE_GEN_INV"})
	GenerateInvLinkResponse generateAndSendRegInvLinkV2(GenerateInvLinkRequest request, boolean isMenu, AuditContext audit) throws Exception;
	
	@RolesAllowed("ROLE_INQUIRY_INV")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	RegenerateInvitationResponse regenerateInvitation(RegenerateInvitationRequest request, AuditContext audit) throws MessagingException, IOException;
	
	@RolesAllowed("ROLE_INQUIRY_INV")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	UpdateInvDataResponse updateDataInvRegis(UpdateInvDataRequest request, AuditContext audit) throws IOException;
	
	GeneratInvLinkExternalResponse generateInvLinkExternal(GeneratInvLinkExternalRequest request, String apiKey, AuditContext audit) throws Exception;
	
	GenerateInvitationLinkForExpiredCertResponse generateInvLinkExpiredCert(GenerateInvitationLinkForExpiredCertRequest request, AuditContext audit) throws UnsupportedEncodingException;
	GenerateInvitationLinkForExpiredCertResponse generateInvLinkExpiredCertEmbed(GenerateInvitationLinkForExpiredCertEmbedRequest request, AuditContext audit) throws UnsupportedEncodingException;
}