package com.adins.exceptions;

import com.adins.framework.exception.AdInsException;

public class EmbedWebViewException extends AdInsException {
	
	private static final long serialVersionUID = 1L;

	public enum ReasonEmbedWebView {
		DECRYPT_ERROR,
		SIGN_LINK_REQUEST_NOT_FOUND
	}
	
	private final ReasonEmbedWebView reason;
	
	public EmbedWebViewException(String message, ReasonEmbedWebView reason) {
		super(message);
		this.reason = reason;
	}

	@Override
	public int getErrorCode() {
		if (null == this.reason) {
			return StatusCode.UNKNOWN;
		}
		
		switch (reason) {
			case DECRYPT_ERROR:
				return StatusCode.WEBVIEW_DECRYPT_ERROR;
			case SIGN_LINK_REQUEST_NOT_FOUND:
				return StatusCode.WEBVIEW_SIGN_LINK_NOT_FOUND;
			default:
				return StatusCode.UNKNOWN;
		}
	}

}
