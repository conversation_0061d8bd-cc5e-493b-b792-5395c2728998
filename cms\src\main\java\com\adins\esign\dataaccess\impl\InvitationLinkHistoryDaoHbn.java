package com.adins.esign.dataaccess.impl;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.esign.dataaccess.api.InvitationLinkHistoryDao;
import com.adins.esign.model.TrInvitationLinkHistory;

@Component
@Transactional
public class InvitationLinkHistoryDaoHbn extends BaseDaoHbn implements InvitationLinkHistoryDao {

	@Override
	public void insertInvitationLinkHistory(TrInvitationLinkHistory invLinkHistory) {
		managerDAO.insert(invLinkHistory);
	}

}
