package com.adins.esign.dataaccess.api;

import java.util.List;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsBalancevendoroftenant;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.custom.BalanceTenantBean;
import com.adins.esign.model.custom.TenantListBean;
import com.adins.esign.model.custom.TenantRekonBean;

public interface TenantDao {
	// ms_tenant
	void insertTenant(MsTenant tenant);
	void updateTenant(MsTenant tenant);
	
	MsTenant getTenantById(long idTenant);
	MsTenant getTenantByCode(String tenantCode);
	MsTenant getTenantByCodeNewTrx(String tenantCode);
	MsTenant getTenantByUser(String loginId);
	MsTenant getTenantByApiKeyAndTenantCode(String apiKey, String tenantCode);
	MsTenant getTenantByApiKeyAndTenantCodeNewTrx(String apiKey, String tenantCode);
	
	List<MsTenant> getListTenantByUser(AmMsuser user);
	List<MsTenant> getListTenantByVendor(MsVendor vendor);
	
	// ms_useroftenant
	void insertUserOfTenant(MsUseroftenant useroftenant);
	
	MsUseroftenant getTenantByloginId(String loginId, String tenantCode);
	MsUseroftenant getUseroftenantByUserTenant(AmMsuser user, MsTenant tenant);
	MsUseroftenant getUseroftenantByLoginIdTenantCode(String loginId, String tenantCode);
	
	List<MsUseroftenant> getUserTenant(long idMsUser);
	
	// others
	String getVendorOfTenantEncryptionKey(String tenantCode, String vendorCode);
	List<MsTenant> getListTenant();
	List<TenantListBean> getListTenant(String tenantName, String status);
	List<TenantListBean> getListTenantPaging(String tenantName, String status, int min, int max);
	int countListTenantPaging(String tenantName, String status);
	
	List<MsLov> getPaymentSignTypeByIdTenant(long idTenant);

	List<BalanceTenantBean> getListBalanceTenant(String tenantCode);
	void deleteBalanceTenant(String tenantCode);
	void insertBalanceTenant(List<MsBalancevendoroftenant> listBalanceTenant);
	List<BalanceTenantBean> getListBalanceTypeTenant(String tenantCode);
	
	
	List<TenantRekonBean> getListTenantRekon(String vendorCode);
	
	String getApiKeyBytenantCode(String tenantCode);
	
}
