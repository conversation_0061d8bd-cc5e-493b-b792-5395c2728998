package com.adins.esign.webservices.frontend.api;

import java.text.ParseException;

import com.adins.esign.webservices.model.ErrorHistoryActivationStatusRequest;
import com.adins.esign.webservices.model.ErrorHistoryActivationStatusResponse;
import com.adins.esign.webservices.model.ErrorHistoryRequest;
import com.adins.esign.webservices.model.ErrorHistoryResponse;

public interface ErrorReportService {
   ErrorHistoryResponse getErrorHistory(ErrorHistoryRequest request) throws ParseException;
   ErrorHistoryActivationStatusResponse getErrorHistActivationStatus(ErrorHistoryActivationStatusRequest request);
}
