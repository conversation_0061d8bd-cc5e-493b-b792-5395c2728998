package com.adins.esign.businesslogic.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.JobLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.custom.JobBean;
import com.adins.esign.webservices.model.GetListJobByJobProcessTypeRequest;
import com.adins.esign.webservices.model.GetListJobRequest;
import com.adins.esign.webservices.model.GetListJobResponse;
import com.adins.exceptions.JobException;
import com.adins.exceptions.JobException.ReasonJob;
import com.adins.framework.persistence.dao.model.AuditContext;

@Transactional
@Component
public class GenericJobLogic extends BaseLogic implements JobLogic {

	@Override
	public GetListJobResponse getListJobByJobType(GetListJobRequest request, AuditContext audit) {
		GetListJobResponse response = new GetListJobResponse();
		if(request.getJobType() == null || StringUtils.isBlank(request.getJobType())) {
			throw new JobException(getMessage(GlobalKey.MESSAGE_ERROR_JOB_TYPE_EMPTY, null, audit), ReasonJob.JOB_TYPE_CODE_EMPTY);
		}
		List<Map<String, Object>> listjob = daoFactory.getJobDao().getListJobByJobType(GlobalVal.JOB_TYPE_JOB_PROCESS);
		List<JobBean> jobbean = new ArrayList<>();
		Iterator<Map<String, Object>> itr = listjob.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			JobBean bean = new JobBean();
			
			bean.setJobId(((BigInteger) map.get("d0")).longValue());
			bean.setJobCode((String) map.get("d1"));
			bean.setJobName((String) map.get("d2"));
			bean.setJobType((String) map.get("d3"));
			bean.setIsActive((String) map.get("d4"));
			bean.setResultUploadLocation((String) map.get("d5"));
			bean.setResultFileFormat((String) map.get("d6"));
			jobbean.add(bean);
		}
		response.setJobList(jobbean);
		return response;
	}

	@Override
	public GetListJobResponse getListJobByJobProcessType(GetListJobByJobProcessTypeRequest request,
			AuditContext audit) {
		GetListJobResponse response = new GetListJobResponse();
		List<Map<String, Object>> listjob = daoFactory.getJobDao().getListJobByJobType(GlobalVal.JOB_TYPE_JOB_PROCESS);
		List<JobBean> jobbean = new ArrayList<>();
		Iterator<Map<String, Object>> itr = listjob.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			JobBean bean = new JobBean();
			
			bean.setJobId(((BigInteger) map.get("d0")).longValue());
			bean.setJobCode((String) map.get("d1"));
			bean.setJobName((String) map.get("d2"));
			bean.setJobType((String) map.get("d3"));
			bean.setIsActive((String) map.get("d4"));
			bean.setResultUploadLocation((String) map.get("d5"));
			bean.setResultFileFormat((String) map.get("d6"));
			jobbean.add(bean);
		}
		response.setJobList(jobbean);
		return response;
	}


}
