package com.adins.esign.dataaccess.api;

import java.util.List;
import java.util.Map;

import com.adins.esign.model.MsBalancevendoroftenant;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.custom.ListPsrePriorityBean;
import com.adins.esign.model.custom.VendorTenantBean;
import com.adins.esign.webservices.model.GetListPSrESettingRequest;

public interface VendorDao {
	
	// MsVendor
	MsVendor getVendorDataByIdMsUser(long uuidMsUser, String vendorCode);
	MsVendor getVendorByCode(String vendorCode);
	MsVendor getVendorByCodeNewTrx(String vendorCode);
	MsVendor getDefaultOperatingPsreVendorOfTenant(String tenantCode);
	MsVendor getMainDefaultPsreVendorOfTenant(String tenantCode);
	MsVendor getVendorByIdMsVendor(long idMsVendor);
	MsVendor getVendorMGByVendorCode (String vendorCode);
	MsVendor getHighestPriorityVendorAvailable(String tenantCode, List<String> excludedVendorCodes);
	MsVendor getVendorPSrEByVendorCode(String vendorCode);
	MsVendor getVendorByTenantCodeAndVendorCode(String vendorCode, String tenantCode);
	
	List<Map<String, Object>> getVendorList(String vendorTypeCode, String tenantCode, String vendorCode, String vendorCodeExclude);
	List<Map<String, Object>> getAllVendorList();
	List<Map<String, Object>> getListVendorByTenantAndBalanceType(String tenantCode, String balanceType);
	String getHighestPriorityVendorCodeAvailable(String tenantCode, List<String> excludedVendors);
	
	String getVendorCodeByIdDocumentH(long idDocumentH);
	
	// MsVendoroftenant
	void insertVendoroftenant(MsVendoroftenant vot);
	void deleteVendoroftenant(MsVendoroftenant vot);
	void updateVendor(MsVendor updVendor);
	void updateVendoroftenant(MsVendoroftenant updVot);
	MsVendoroftenant getVendoroftenantByTenantId(long idTenant);
	MsVendoroftenant getVendoroftenant(MsTenant tenant, MsVendor vendor);
	MsVendoroftenant getVendorTenantByCode(String tenantCode, String vendorCode);
	List<MsVendoroftenant> getListVendoroftenant();
	List<MsVendoroftenant> getListVendoroftenantByTenantCode(String tenantCode);
	List<MsVendoroftenant> getListVendoroftenantByVendorCode(String vendorCode);
	
	// MsBalancevendoroftenant
	List<MsBalancevendoroftenant> getListMsBalancevendoroftenant();
	List<VendorTenantBean> getListVendorTenantFromBalanceVendorOfTenant(String tenantCode);
	List<MsBalancevendoroftenant> getListBalanceByVendorTenant(String tenantCode, String vendorCode);
	MsVendorRegisteredUser getVendorNotActiveRegisteredUserByLoginId(String loginId);
	MsBalancevendoroftenant getBalanceVendorOfTenant(String vendorCode, String tenantCode, String balanceType);
	
	//Register BM multi psre
	List<Map<String, Object>> getVendorByIdNo(String idNo);
	
	List<Map<String,Object>> getListVendorByIdMsUser(long idMsUser);
	
	//GetListPSrESetting
	List<Map<String,Object>> getListPSrESetting(GetListPSrESettingRequest request, int min, int max);
	int countGetListPSrESetting(GetListPSrESettingRequest request);
	
	//GetPsrePriority
	List<ListPsrePriorityBean> getListPsrePriority(String tenantCode);
	
}
