<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.2.13.RELEASE</version>
		<relativePath />
	</parent>
	<artifactId>com.adins.esign.model</artifactId>
	<version>4.16.0</version>
	<packaging>jar</packaging>
	
	<properties>
		<adins-framework.version>2.0.3-SNAPSHOT</adins-framework.version>
		<cxf.version>3.3.4</cxf.version>
		<hashids.version>1.0.3</hashids.version>
		<hibernate-jpa.version>1.0.1.Final</hibernate-jpa.version>
		<swagger-ui.version>2.2.10-1</swagger-ui.version>
		<pojo.version>0.7.6</pojo.version>
	</properties>

	<dependencies>
	  	<dependency>
			<groupId>org.hibernate.javax.persistence</groupId>
			<artifactId>hibernate-jpa-2.0-api</artifactId>
			<version>${hibernate-jpa.version}</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>	
	  	<dependency>
	  		<groupId>com.adins.framework</groupId>
	  		<artifactId>com.adins.framework.service.base</artifactId>
	  		<version>${adins-framework.version}</version>
	  	</dependency>
	  	<dependency>
  			<groupId>com.adins.framework</groupId>
  			<artifactId>com.adins.framework.persistence.dao-hibernate</artifactId>
  			<version>${adins-framework.version}</version>
  		</dependency>
	  	<dependency>
		    <groupId>org.apache.cxf</groupId>
		    <artifactId>cxf-rt-rs-service-description-swagger</artifactId>
		    <version>${cxf.version}</version>
		</dependency>
		<dependency>
		    <groupId>org.webjars</groupId>
		    <artifactId>swagger-ui</artifactId>
		    <version>${swagger-ui.version}</version>
		</dependency>
  		<dependency>
		  <groupId>pl.pojo</groupId>
		  <artifactId>pojo-tester</artifactId>
		  <version>${pojo.version}</version>
          <scope>test</scope>
		</dependency>
		<dependency>
		  	<groupId>org.hashids</groupId>
		  	<artifactId>hashids</artifactId>
		  	<version>${hashids.version}</version>
		</dependency>
		<dependency>
		    <groupId>org.postgresql</groupId>
		    <artifactId>postgresql</artifactId>
		    <version>42.7.1</version>
		</dependency>
	</dependencies>
	

	<scm>
		<connection>scm:svn:https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent/model</connection>
		<developerConnection>scm:svn:https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent/model</developerConnection>
		<url>https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent/model</url>
	</scm>
	
	
    
	<build>
		<plugins>
			<plugin>
			    <groupId>org.jacoco</groupId>
			    <artifactId>jacoco-maven-plugin</artifactId>
			    <version>0.8.5</version>
			    <configuration>
			        <destFile>${sonar.coverage.jacoco.xmlReportPaths}</destFile>
			        <append>true</append>
			    </configuration>
			    <executions>
			        <execution>
			            <id>agent</id>
			            <goals>
			                <goal>prepare-agent</goal>
			            </goals>
			        </execution>
					<execution>
						<id>report</id>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
			    </executions>
			</plugin>
		</plugins>

		<finalName>${project.artifactId}-${project.version}-r${buildNumber}</finalName>
	</build>
</project>