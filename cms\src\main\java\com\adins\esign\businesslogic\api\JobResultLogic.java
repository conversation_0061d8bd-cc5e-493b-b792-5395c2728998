package com.adins.esign.businesslogic.api;

import java.text.ParseException;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.webservices.model.CancelJobRequest;
import com.adins.esign.webservices.model.CancelJobResponse;
import com.adins.esign.webservices.model.GetListJobRekonResultRequest;
import com.adins.esign.webservices.model.GetListJobRekonResultResponse;
import com.adins.esign.webservices.model.SubmitRekonJobResultRequest;
import com.adins.esign.webservices.model.SubmitRekonJobResultResponse;
import com.adins.esign.webservices.model.ViewRequestParamJobResultRequest;
import com.adins.esign.webservices.model.ViewRequestParamJobResultResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface JobResultLogic {
	@RolesAllowed("ROLE_RECONCILE_TOOL")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	SubmitRekonJobResultResponse submitRekonJobResult (SubmitRekonJobResultRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_JOB_RESULT")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	CancelJobResponse cancelJobResult(CancelJobRequest request, AuditContext audit);
	
	@RolesAllowed("ROLE_JOB_RESULT")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	GetListJobRekonResultResponse getListJobRekonResultResponse(GetListJobRekonResultRequest request, AuditContext audit) throws ParseException;
	
	@RolesAllowed("ROLE_JOB_RESULT")
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	ViewRequestParamJobResultResponse getRequestParamJobResult(ViewRequestParamJobResultRequest request, AuditContext audit);
}