package com.adins.esign.dataaccess.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.dataaccess.api.OfficeDao;
import com.adins.esign.dataaccess.factory.api.DaoFactory;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class OfficeDaoHbn extends BaseDaoHbn implements OfficeDao {
	@Autowired DaoFactory daofactory;
	@Override
	public void insertOffice(MsOffice newOffice) {
		newOffice.setUsrCrt(MssTool.maskData(newOffice.getUsrCrt()));
		this.managerDAO.insert(newOffice);
	}

	@Override
	public MsOffice getActiveOfficeByOfficeCodeAndTenantCode(String officeCode, String tenantCode) {
		if (StringUtils.isBlank(officeCode))
			return null;
	
		Object[][] queryParams = {
				{MsOffice.OFFICE_CODE_HBM, StringUtils.upperCase(officeCode)},
				{"tenantCode", StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
				"from MsOffice mo "
				+ "join fetch mo.msTenant mt "
				+ "where mo.officeCode = :officeCode and mt.tenantCode = :tenantCode and mo.isActive ='1' ",
				queryParams);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<MsOffice> getOfficeListByTenantCodeAndRegionCode(String tenantCode,String regionCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, tenantCode);
		if (StringUtils.isNotBlank(regionCode)) {
			params.put(MsRegion.REGION_CODE_HBM, regionCode);
		}
		
		String query = 
		" from MsOffice mo "
		+ " join fetch mo.msTenant mt "
		+ " left join fetch mo.msRegion mr "
		+ " where 1=1 "
		+ " and mt.tenantCode = :tenantCode ";
		if (StringUtils.isNotBlank(regionCode)) {
			query = query +  " and mr.regionCode = :regionCode ";
		}
		
		
		return (List<MsOffice>) this.managerDAO.list(
				query,params).get(GlobalKey.MAP_RESULT_LIST);
		
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<MsOffice> getOfficeList(String tenantCode) {
		MsTenant tenant = this.managerDAO.selectOne(MsTenant.class,
				new Object[][] {{ Restrictions.eq(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)) }});
		
		Object[][] params = new Object[][] {{ Restrictions.eq(MsOffice.TENANT_HBM, tenant) }};
		Map<String, Object> mapResult = this.managerDAO.selectAll(MsOffice.class, params, null);
		return (List<MsOffice>) mapResult.get(AmGlobalKey.MAP_RESULT_LIST);
	}
	
	
	@Override
	public void updateOffice(MsOffice office) {
		office.setUsrUpd(MssTool.maskData(office.getUsrUpd()));
		this.managerDAO.update(office);
	}

	@Override
	public MsOffice getFirstOfficeByTenantCode(String tenantCode) {
		StringBuilder query = new StringBuilder();
		query
			.append(" select office_code from ms_office mo ")
			.append(" join ms_tenant mt on mo.id_ms_tenant = mt.id_ms_tenant ")
			.append(" where tenant_code = :tenantCode order by mo.dtm_crt limit 1");
		
		String  officeCode = (String) this.managerDAO.selectOneNativeString(query.toString(),
				new Object[][] {{ MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode) }});
		
		if (null == officeCode) {
			return null;
		}
		
		return this.getActiveOfficeByOfficeCodeAndTenantCode(officeCode, tenantCode);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertOfficeNewTrx(MsOffice office) {
		office.setUsrCrt(MssTool.maskData(office.getUsrCrt()));
		this.managerDAO.insert(office);
	}
}
