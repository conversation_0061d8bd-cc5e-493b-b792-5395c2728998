package com.adins.esign.businesslogic.api;

import java.util.List;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.custom.UpdatePSrERequest;
import com.adins.esign.webservices.model.GetListPSrESettingRequest;
import com.adins.esign.webservices.model.GetListPSrESettingResponse;
import com.adins.esign.webservices.model.GetPsrePriorityRequest;
import com.adins.esign.webservices.model.GetPsrePriorityResponse;
import com.adins.esign.webservices.model.UpdatePsrePriorityRequest;
import com.adins.esign.webservices.model.UpdatePsrePriorityResponse;
import com.adins.esign.webservices.model.VendorListEmbedRequest;
import com.adins.esign.webservices.model.VendorListInvitationRegisterRequest;
import com.adins.esign.webservices.model.VendorListRequest;
import com.adins.esign.webservices.model.VendorListResponse;
import com.adins.esign.webservices.model.external.UpdatePsrePriorityExternalRequest;
import com.adins.esign.webservices.model.external.UpdatePsrePriorityExternalResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;

public interface VendorLogic {
	// MsVendor
	MsVendor getVendorByCode(String vendorCode, AuditContext audit);
	MsVendor getVendorByEmailSender(String emailSender, AuditContext audit);
	
	// MsVendorRegisteredUser
	void insertVendorRegisteredUser(MsVendorRegisteredUser user);
	MsVendorRegisteredUser getVendorRegisteredUserByLoginId(String loginId);
	String checkNotificationTypeV2(String loginId, String vendorCode, AuditContext audit);
	
	// MsVendoroftenant
	List<MsVendoroftenant>  getListVendorTenant(AuditContext audit);
	MsVendoroftenant getVendorTenant(MsTenant tenant, MsVendor vendor, AuditContext audit);
	MsVendoroftenant getVendorTenantByDocumentId(String documentId, AuditContext audit);
	MsVendoroftenant getVendorTenantByCode(String tenantCode, String vendorCode, AuditContext audit);
	
	// Others
	VendorListResponse getVendorList(VendorListRequest request, String vendorCodeExclude, AuditContext audit);
	VendorListResponse getVendorListEmbed(VendorListEmbedRequest request, String vendorCodeExclude, AuditContext audit);
	VendorListResponse getVendorListInvReg(VendorListInvitationRegisterRequest request, AuditContext audit);
	String getVendorEKYCStatus(String tenantCode, String vendorCode, AuditContext audit);
	VendorListResponse getAllVendorList(AuditContext audit);
	VendorListResponse getVendorListV2(VendorListRequest request, String vendorCodeExclude, AuditContext audit);
	GetListPSrESettingResponse getListPSrESetting(GetListPSrESettingRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_PSRE_PRIORITY"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	GetPsrePriorityResponse getPsrePriority(GetPsrePriorityRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_PSRE_SETTING"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	MssResponseType updateVendorPSrE (UpdatePSrERequest updatePSrERequest, AuditContext audit);

	@RolesAllowed({"ROLE_PSRE_PRIORITY"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	UpdatePsrePriorityResponse updatePSrEPriority(UpdatePsrePriorityRequest request, AuditContext audit);
	UpdatePsrePriorityExternalResponse updatePSrEPriorityExternal(UpdatePsrePriorityExternalRequest request, String xApiKey,
			AuditContext audit);

}
