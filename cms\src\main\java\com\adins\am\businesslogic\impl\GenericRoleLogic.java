package com.adins.am.businesslogic.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.businesslogic.api.RoleLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMenuofrole;
import com.adins.am.model.AmMsmenu;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.custom.ListRole;
import com.adins.esign.model.custom.ListRoleManagamentBean;
import com.adins.esign.validatorlogic.api.RoleValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.webservices.model.AddMenuOfRoleRequest;
import com.adins.esign.webservices.model.AddRoleManagementRequest;
import com.adins.esign.webservices.model.AddRoleRequest;
import com.adins.esign.webservices.model.EditRoleManagementRequest;
import com.adins.esign.webservices.model.GetListRoleManagementRequest;
import com.adins.esign.webservices.model.GetListRoleManagementResponse;
import com.adins.esign.webservices.model.GetListRoleRequest;
import com.adins.esign.webservices.model.GetListRoleResponse;
import com.adins.exceptions.CommonException;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.UserException;
import com.adins.exceptions.CommonException.ReasonCommon;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.RoleException;
import com.adins.exceptions.RoleException.ReasonRole;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;

@Component
public class GenericRoleLogic extends BaseLogic implements RoleLogic {

	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private RoleValidatorLogic roleValidatorLogic;
	
	private static final String ROLE_CODE_ADMESIGN = "ADMESIGN";

	@Override
	public List<AmMsrole> getListRoleByIdUser(long idMsUser, AuditContext callerId) {
		return daoFactory.getRoleDao().getListRoleByIdUser(idMsUser);
	}

	@Override
	public List<AmMsrole> getListRoleByIdUserTenantCode(long idMsUser, String tenantCode, AuditContext callerId) {
		return daoFactory.getRoleDao().getListRoleByIdUserTenantCode(idMsUser, tenantCode);
	}

	@Override
	public AmMsrole insertRole(String roleCode, String roleName, MsTenant tenant, AuditContext audit) {
		AmMsrole role = new AmMsrole();
		role.setRoleCode(StringUtils.upperCase(roleCode));
		role.setRoleName(roleName);
		role.setMsTenant(tenant);
		role.setIsActive("1");
		role.setIsDeleted("0");
		role.setDtmCrt(new Date());
		role.setUsrCrt(audit.getCallerId());
		daoFactory.getRoleDao().insertRole(role);

		return role;
	}

	@Override
	public void insertMemberofrole(AmMsrole role, AmMsuser user, AuditContext audit) {
		AmMemberofrole memberofrole = new AmMemberofrole();
		memberofrole.setAmMsrole(role);
		memberofrole.setAmMsuser(user);
		memberofrole.setUsrCrt(audit.getCallerId());
		memberofrole.setDtmCrt(new Date());
		daoFactory.getRoleDao().insertMemberOfRole(memberofrole);
	}

	@Override
	public MssResponseType addRole(AddRoleRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);

		if (StringUtils.isBlank(request.getRoleCode())) {
			throw new ParameterException(
					getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] { "roleCode" }, audit),
					ReasonParam.MANDATORY_PARAM);
		}
		if (StringUtils.isBlank(request.getRoleName())) {
			throw new ParameterException(
					getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_MANDATORY, new String[] { "roleName" }, audit),
					ReasonParam.MANDATORY_PARAM);
		}

		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCode(request.getRoleCode(), tenant.getTenantCode());
		if (null != role) {
			String[] errParams = {String.format("Role Code %s untuk tenant %s", StringUtils.upperCase(request.getRoleCode()), tenant.getTenantName())};
			throw new CommonException(getMessage("businesslogic.global.dataisexist", errParams, audit), ReasonCommon.DATA_ALREADY_EXISTED);
		}

		role = new AmMsrole();
		role.setUsrCrt(audit.getCallerId());
		role.setDtmCrt(new Date());
		role.setIsActive("1");
		role.setIsDeleted("0");
		role.setRoleCode(StringUtils.upperCase(request.getRoleCode()));
		role.setRoleName(request.getRoleName());
		role.setMsTenant(tenant);
		daoFactory.getRoleDao().insertRole(role);

		return new MssResponseType();
	}

	@Override
	public MssResponseType addMenuOfRole(AddMenuOfRoleRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsrole role = roleValidatorLogic.validateGetRole(tenant.getTenantCode(), request.getRoleCode(), true,true, audit);
		
		// Validasi menu-menu yang di-request
		for (String menuCode : request.getMenuCodes()) {
			AmMsmenu menu = daoFactory.getMenuDao().getMenuByCode(menuCode);
			if (null == menu) {
				String[] errParams = {"Menu", StringUtils.upperCase(menuCode)};
				throw new CommonException(getMessage("businesslogic.global.datanotexist", errParams, audit), ReasonCommon.DATA_NOT_EXISTED);
			}
			
			AmMenuofrole menuofrole = daoFactory.getMenuDao().getMenuofrole(menu, role);
			if (null == menuofrole) {
				menuofrole = new AmMenuofrole();
				menuofrole.setAmMsmenu(menu);
				menuofrole.setAmMsrole(role);
				menuofrole.setDtmCrt(new Date());
				menuofrole.setUsrCrt(audit.getCallerId());
				daoFactory.getMenuDao().insertMenuOfRole(menuofrole);
			}
		}
		
		return new MssResponseType();
	}

	@Override
	public GetListRoleResponse getListRole(GetListRoleRequest request, AuditContext audit) {
		
		MsUseroftenant tenant = daoFactory.getTenantDao().getTenantByloginId(audit.getCallerId(), request.getTenantCode());
		
		if (null == tenant) {
			throw new CommonException(getMessage("businesslogic.user.tenantwithcalleridnotfound", new Object[]{audit.getCallerId(), request.getTenantCode()}, audit), ReasonCommon.CALLER_ID_NOT_HAVE_TENANT);
		}
		
		GetListRoleResponse response = new GetListRoleResponse();
		List<Map<String, Object>> role = daoFactory.getRoleDao().getRoleListByUserManagement(request.getTenantCode());
		List<ListRole> listRole = new ArrayList<>();
		Iterator<Map<String, Object>> itr = role.iterator();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			ListRole roleList = new ListRole();

			roleList.setRoleCode(((String) map.get("d0")));
			roleList.setRoleName(((String) map.get("d1")));
			listRole.add(roleList);
		}

		response.setListRole(listRole);
		return response;
	}

	@Override
	public GetListRoleManagementResponse getListRoleManagement(GetListRoleManagementRequest request,
			AuditContext audit) {
		GetListRoleManagementResponse response = new GetListRoleManagementResponse();
		if (StringUtils.isNotBlank(request.getTenantCode())) {
			tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		}
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_MAX_ROW);
		int maxRow = Integer.parseInt(gs.getGsValue());
		int min = ((request.getPage() - 1) * maxRow) + 1;
		int max = (request.getPage() * maxRow);
		
		BigInteger countList = daoFactory.getRoleDao().countListRoleManagement(request.getRoleName(), request.getTenantCode(), request.getStatus());
		double totalPageD = Math.ceil(countList.doubleValue() / maxRow);
		int totalPage = (int) totalPageD;
		if (request.getPage() <= 0 || totalPage == 0) {
			request.setPage(1);
		} else if (request.getPage() > totalPage) {
			request.setPage(totalPage);
		}
		
		List<ListRoleManagamentBean> roles = daoFactory.getRoleDao().getListRoleManagement(request.getRoleName(), request.getTenantCode(), request.getStatus(), min, max);
		
		response.setTotalResult(countList.intValue());
		response.setPage(request.getPage());
		response.setRoles(roles);
		response.setTotalPage(totalPage);
		return response;
	}

	@Override
	public MssResponseType addRoleManagement(AddRoleManagementRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		roleValidatorLogic.validateRoleByNameForAddRole(request.getTenantCode(), request.getRoleName(), audit);
		String roleCode = getRoleCode(request.getRoleName(), request.getTenantCode(), audit);
		
		if (ROLE_CODE_ADMESIGN.equals(roleCode) || ROLE_CODE_ADMESIGN.equalsIgnoreCase(request.getRoleName()) || "Admin Esign".equalsIgnoreCase(request.getRoleName()) || roleCode.contains(ROLE_CODE_ADMESIGN)) {
			throw new RoleException(getMessage("businesslogic.role.admesignnotallowed", null, audit), ReasonRole.ADMESIGN_NOT_ALLOWED);
		}
		
		AmMsrole role = new AmMsrole();
		role.setDtmCrt(new Date());
		role.setUsrCrt(audit.getCallerId());
		role.setIsActive("1");
		role.setIsDeleted("0");
		role.setIsUserManagement("1");
		role.setMsTenant(tenant);
		role.setRoleCode(roleCode);
		role.setRoleName(request.getRoleName());
		daoFactory.getRoleDao().insertRole(role);
		
		Status status = new Status();
		status.setMessage(getMessage("businesslogic.role.successinput", null, audit));
		MssResponseType response = new MssResponseType();
		response.setStatus(status);
		return response;
	}
	
	private String getRoleCode(String roleName, String tenantCode, AuditContext audit) {
		String[] splitName = roleName.split(" ");
		String code = splitName[0];
		code = code.replaceAll("[^a-zA-Z]", "");
		if (code.length() > 15) {
			code = code.substring(0, 15);
		}
		
		String finalCode = code;
		AmMsrole role = roleValidatorLogic.validateGetRole(tenantCode, finalCode, false,false, audit);
		
		if (null != role) {
			int x = 1;
			while (true) {
				finalCode = code + x;
				role = roleValidatorLogic.validateGetRole(tenantCode, finalCode, false,false, audit);
				if (null == role) {
					return StringUtils.upperCase(finalCode);
				}
				x++;
			}
		}
		
		return StringUtils.upperCase(finalCode);
	}
	
	private String getRoleCodeForEdit(String roleName, String tenantCode, AmMsrole existingRole, AuditContext audit) {
		String[] splitName = roleName.split(" ");
		String code = splitName[0];
		code = code.replaceAll("[^a-zA-Z]", "");
		if (code.length() > 15) {
			code = code.substring(0, 15);
		}
		
		String finalCode = code;
		AmMsrole role = roleValidatorLogic.validateGetRole(tenantCode, finalCode, false,false, audit);
		
		if (null != role) {
			if (role == existingRole) {
				return StringUtils.upperCase(finalCode);
			}
			
			int x = 1;
			while (true) {
				finalCode = code + x;
				role = roleValidatorLogic.validateGetRole(tenantCode, finalCode, false,false, audit);
				if (null == role) {
					return StringUtils.upperCase(finalCode);
				}
				x++;
			}
		}
		
		return StringUtils.upperCase(finalCode);
	}

	@Override
	public MssResponseType editRoleManagement(EditRoleManagementRequest request, AuditContext audit) {
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		AmMsrole roleToEdit = roleValidatorLogic.validateGetRole(request.getTenantCode(), request.getRoleCode(), true,true, audit);
		if (StringUtils.isBlank(request.getIsActive())) {
			throw new TenantException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_EMPTY_PARAM,
					new String[] {"Is Active"}, this.retrieveLocaleAudit(audit)), ReasonTenant.TENANT_API_KEY_EMPTY);
		}
		
		roleValidatorLogic.validateRoleByNameForEditRole(tenant.getTenantCode(), request.getRoleName(), roleToEdit, audit);
		
		
		
		String newRoleCode = getRoleCodeForEdit(request.getRoleName(), tenant.getTenantCode(), roleToEdit, audit);
		
		if (!roleToEdit.getRoleName().equals(request.getRoleName())) {
			roleToEdit.setRoleName(request.getRoleName());
			roleToEdit.setRoleCode(newRoleCode);
		}
		if (request.getIsActive().equals("1") || request.getIsActive().equals("0") ) {
			roleToEdit.setIsActive(request.getIsActive());
		} 
		
		roleToEdit.setIsUserManagement("1");
		roleToEdit.setDtmUpd(new Date());
		roleToEdit.setUsrUpd(audit.getCallerId());
		daoFactory.getRoleDao().updateRole(roleToEdit);
		
		Status status = new Status();
		status.setMessage(getMessage("businesslogic.role.successedit", null, audit));
		MssResponseType response = new MssResponseType();
		response.setStatus(status);
		return response;
	}

}
