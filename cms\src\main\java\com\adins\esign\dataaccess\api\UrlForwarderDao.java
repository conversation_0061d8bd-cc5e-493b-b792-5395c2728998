package com.adins.esign.dataaccess.api;

import com.adins.am.model.AmMsuser;
import com.adins.esign.model.MsEmailPattern;
import com.adins.esign.model.TrUrlForwarder;

public interface UrlForwarderDao {
	
	void insertUrlForwarder(TrUrlForwarder urlForwarder);
	void updateUrlForwarder(TrUrlForwarder urlForwarder);
	
	TrUrlForwarder getUrlForwarderByCode(String urlCode);
	TrUrlForwarder getLatestUrlForwarder(MsEmailPattern emailPattern, AmMsuser user);
}
