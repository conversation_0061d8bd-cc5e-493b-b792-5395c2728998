package com.adins.am.model;
// Generated 09-Sep-2021 22:49:32 by Hibernate Tools 5.2.12.Final

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import com.adins.am.model.custom.CreateableEntity;
import com.adins.esign.model.MsLov;

/**
 * AmUserpwdhistory generated by hbm2java
 */
@Entity
@Table(name = "am_userpwdhistory")
public class AmUserpwdhistory extends CreateableEntity implements java.io.Serializable {
	private static final long serialVersionUID = 1L;

	private long idUserPwdHistory;
	private AmMsuser amMsuser;
	private MsLov msLov;
	private String password;

	public AmUserpwdhistory() {
	}

	public AmUserpwdhistory(long idUserPwdHistory, AmMsuser amMsuser, String usrCrt, Date dtmCrt) {
		this.idUserPwdHistory = idUserPwdHistory;
		this.amMsuser = amMsuser;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
	}

	public AmUserpwdhistory(long idUserPwdHistory, AmMsuser amMsuser, MsLov msLov, String usrCrt, Date dtmCrt,
			String password) {
		this.idUserPwdHistory = idUserPwdHistory;
		this.amMsuser = amMsuser;
		this.msLov = msLov;
		this.usrCrt = usrCrt;
		this.dtmCrt = dtmCrt;
		this.password = password;
	}
	
	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id_user_pwd_history", unique = true, nullable = false)
	public long getIdUserPwdHistory() {
		return this.idUserPwdHistory;
	}

	public void setIdUserPwdHistory(long idUserPwdHistory) {
		this.idUserPwdHistory = idUserPwdHistory;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "id_ms_user", nullable = false)
	public AmMsuser getAmMsuser() {
		return this.amMsuser;
	}

	public void setAmMsuser(AmMsuser amMsuser) {
		this.amMsuser = amMsuser;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "lov_pwd_change_type")
	public MsLov getMsLov() {
		return this.msLov;
	}

	public void setMsLov(MsLov msLov) {
		this.msLov = msLov;
	}

	@Column(name = "password", length = 200)
	public String getPassword() {
		return this.password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

}
