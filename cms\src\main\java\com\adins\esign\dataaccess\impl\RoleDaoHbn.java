package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsmenu;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.dataaccess.api.RoleDao;
import com.adins.esign.dataaccess.factory.api.DaoFactory;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.custom.ListRoleManagamentBean;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class RoleDaoHbn extends BaseDaoHbn implements RoleDao {
	@Autowired DaoFactory daofactory;

	@Override
	public AmMsrole getRoleByCodeAndTenantCode(String roleCode, String tenantCode) {
		if (StringUtils.isBlank(roleCode))
			return null;

		return this.managerDAO.selectOne(
				"from AmMsrole role "
				+" join fetch role.msTenant mt "
				+" where role.isActive='1'and mt.isActive ='1' "
						+" and role.roleCode = :roleCode and mt.tenantCode = :tenantCode",
				new Object[][] {{AmMsrole.ROLE_CODE_HBM, StringUtils.upperCase(roleCode)}, {MsTenant.TENANT_CODE_HBM, tenantCode}});
	}

	@Override
	public AmMsrole getRoleByCodeAndTenantCodeV2(String roleCode, String tenantCode) {
		if (StringUtils.isBlank(roleCode))
			return null;

		return this.managerDAO.selectOne(
				"from AmMsrole role "
				+" join fetch role.msTenant mt "
				+" where  mt.isActive ='1' "
						+" and role.roleCode = :roleCode and mt.tenantCode = :tenantCode",
				new Object[][] {{AmMsrole.ROLE_CODE_HBM, StringUtils.upperCase(roleCode)}, {MsTenant.TENANT_CODE_HBM, tenantCode}});
	}

	@Override
	public AmMsrole getRoleById(long idRole) {
		return this.managerDAO.selectOne(
				"from AmMsrole role "
				+" join fetch role.msTenant mt "
				+" where role.idMsRole = :idRole ",
				new Object[][] {{"idRole", idRole}});
	}
	
	@Override
	public void insertRole(AmMsrole newRole) {
		newRole.setUsrCrt(MssTool.maskData(newRole.getUsrCrt()));
		this.managerDAO.insert(newRole);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<AmMsrole> getListRoleByIdUser(long idMsUser) {
		if (0 == idMsUser)
			return Collections.emptyList() ;
		
		StringBuilder query = new StringBuilder();
		query.append(" from AmMsrole role ")
				.append(" join fetch role.amMemberofroles mr ")
				.append(" join fetch mr.amMsuser amu ")
				.append(" join fetch role.msTenant mt ")
				.append(" WHERE role.isActive ='1' AND amu.isActive ='1' AND amu.idMsUser = :idMsUser ");
		
		Map<String, Object> result = this.managerDAO.list(query.toString(), new Object[][] {{AmMsuser.ID_MS_USER_HBM, idMsUser}});
		return (List<AmMsrole>) result.get(GlobalKey.MAP_RESULT_LIST);
	}


	@SuppressWarnings("unchecked")
	@Override
	public List<AmMsrole> getListRoleByIdUserTenantCode(long idMsUser, String tenantCode) {
		if (0 == idMsUser)
			return Collections.emptyList() ;

		StringBuilder query = new StringBuilder();
		query.append(" from AmMsrole role ")
				.append(" join fetch role.amMemberofroles mr ")
				.append(" join fetch mr.amMsuser amu ")
				.append(" join fetch role.msTenant mt ")
				.append(" WHERE role.isActive ='1' AND amu.isActive ='1' AND amu.idMsUser = :idMsUser AND mt.tenantCode = :tenantCode  ");
		
		Map<String, Object> result = this.managerDAO.list(query.toString(), new Object[][] {{AmMsuser.ID_MS_USER_HBM, idMsUser},{MsTenant.TENANT_CODE_HBM, tenantCode}});
		return (List<AmMsrole>) result.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@Override
	public void insertMemberOfRole(AmMemberofrole memberOfRole) {
		memberOfRole.setUsrCrt(MssTool.maskData(memberOfRole.getUsrCrt()));
		this.managerDAO.insert(memberOfRole);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertMemberOfRoleNewTran(AmMemberofrole memberOfRole) {
		memberOfRole.setUsrUpd(MssTool.maskData(memberOfRole.getUsrUpd()));
		this.managerDAO.insert(memberOfRole);
	}

	@Override
	public AmMemberofrole getMemberofroleByUser(AmMsuser user) {
		Object[][] params = {{Restrictions.eq(AmMsuser.AM_MSUSER_HBM, user)}};
		return this.managerDAO.selectOne(AmMemberofrole.class, params);
	}

	@Override
	public AmMemberofrole getMemberofroleByLoginIdRoleTenantCode(String loginId, String tenantCode) {
		Object[][] params = {
				{AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId)},
				{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		StringBuilder query = new StringBuilder();
		query.append(" SELECT mor.id_am_memberofrole, mor.usr_crt, mor.dtm_crt, mor.usr_upd, mor.dtm_upd, ")
				.append(" mor.id_ms_role, mor.id_ms_user ")
				.append(" FROM am_memberofrole mor ")
				.append(" JOIN am_msrole role ON mor.id_ms_role = role.id_ms_role ")
				.append(" JOIN am_msuser amu ON amu.id_ms_user = mor.id_ms_user ")
				.append(" JOIN ms_useroftenant uot ON uot.id_ms_user = amu.id_ms_user ")
				.append(" JOIN ms_tenant mt ON uot.id_ms_tenant = mt.id_ms_tenant AND role.id_ms_tenant = mt.id_ms_tenant ")
				.append(" WHERE role.is_active ='1' AND amu.is_active ='1' AND mt.is_active ='1' ")
				.append(" AND amu.login_id = :loginId AND mt.tenant_code = :tenantCode ");
		
		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), params);
		
		if(result.isEmpty()) {
			return null;
		}
		else {
			Map<String, Object> map = result.get(0); //hanya ada 1 result
			
			AmMemberofrole memberRole = new AmMemberofrole();
			memberRole.setIdAmMemberofrole(((BigInteger) map.get("d0")).longValue());
			memberRole.setUsrCrt((String) map.get("d1"));
			memberRole.setDtmCrt((Date) map.get("d2"));
			memberRole.setUsrUpd((String) map.get("d3"));
			memberRole.setDtmUpd((Date) map.get("d4"));
			memberRole.setAmMsrole(this.getRoleById(((BigInteger) map.get("d5")).longValue()));
			memberRole.setAmMsuser(daofactory.getUserDao().getUserByIdMsUser(((BigInteger) map.get("d6")).longValue()));
			
			return memberRole;
		}

	}
	
	@Override
	public AmMemberofrole getMemberofroleByAmMsuserAndMsTenant(AmMsuser user, MsTenant tenant) {
		Object[][] params = {
				{AmMsuser.ID_MS_USER_HBM, user.getIdMsUser()},
				{MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant()}
		};

		StringBuilder query = new StringBuilder();
		query
			.append("SELECT mor.id_am_memberofrole, mor.usr_crt, mor.dtm_crt, mor.usr_upd, mor.dtm_upd, mor.id_ms_role, mor.id_ms_user ")
			.append("from am_memberofrole mor ")
			.append("join am_msrole role on role.id_ms_role = mor.id_ms_role ")
			.append("where 1=1 ")
			.append("and mor.id_ms_user = :idMsUser ")
			.append("and role.id_ms_tenant = :idMsTenant and role.is_active = '1' ");
		
		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), params);
		
		if(result.isEmpty()) {
			return null;
		}
		
		Map<String, Object> map = result.get(0); //hanya ada 1 result
		
		AmMemberofrole memberRole = new AmMemberofrole();
		memberRole.setIdAmMemberofrole(((BigInteger) map.get("d0")).longValue());
		memberRole.setUsrCrt((String) map.get("d1"));
		memberRole.setDtmCrt((Date) map.get("d2"));
		memberRole.setUsrUpd((String) map.get("d3"));
		memberRole.setDtmUpd((Date) map.get("d4"));
		memberRole.setAmMsrole(this.getRoleById(((BigInteger) map.get("d5")).longValue()));
		memberRole.setAmMsuser(daofactory.getUserDao().getUserByIdMsUser(((BigInteger) map.get("d6")).longValue()));
		
		return memberRole;
	}

	@Override
	public List<AmMemberofrole> getListMemberofroleByRoleId(long idRole) {
		if (0 == idRole) {
			return Collections.emptyList();
		}
		
		StringBuilder query = new StringBuilder();
		query.append(" SELECT mr.* ")
				.append(" FROM am_memberofrole mr ")
				.append(" JOIN am_msrole r ON mr.id_ms_role = r.id_ms_role ")
				.append(" WHERE r.is_active = '1'AND r.id_ms_role = :idRole ");
		
		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), new Object[][] {{"idRole", idRole}});
		
		Iterator<Map<String, Object>> itr = result.iterator();
		List<AmMemberofrole> memberOfRoleList = new ArrayList<>();
		while (itr.hasNext()) {
			Map<String, Object> map = itr.next();
			AmMemberofrole memberOfRole = new AmMemberofrole();
			memberOfRole.setIdAmMemberofrole(((BigInteger) map.get("d0")).longValue());
			memberOfRole.setUsrCrt((String) map.get("d1"));
			memberOfRole.setDtmCrt((Date) map.get("d2"));
			memberOfRole.setUsrUpd((String) map.get("d3"));
			memberOfRole.setDtmUpd((Date) map.get("d4"));
			memberOfRole.setAmMsrole(daofactory.getRoleDao().getRoleById(((BigInteger) map.get("d5")).longValue()));
			memberOfRole.setAmMsuser(daofactory.getUserDao().getUserByIdMsUser(((BigInteger) map.get("d6")).longValue()));
			memberOfRoleList.add(memberOfRole);
		}
		return memberOfRoleList;
	}


	@Override
	public AmMemberofrole getMemberofrole(String loginId, AmMsrole role) {
		Object[][] params = new Object[][] {
			{AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId)},
			{AmMsrole.AM_MSROLE_HBM, role}
		};
		return this.managerDAO.selectOne(
				"from AmMemberofrole mor "
				+ "join fetch mor.amMsrole "
				+ "join fetch mor.amMsuser mu "
				+ "where mu.loginId = :loginId and mor.amMsrole = :amMsrole ", params);
	}


	@Override
	public boolean isMenuAvailableForTenantUser(String loginId, String tenantCode, String menuCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId));
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		params.put(AmMsmenu.MENU_CODE_HBM, StringUtils.upperCase(menuCode));
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count (mm.id_ms_menu) ")
			.append("from am_msuser mu ")
			.append("join am_memberofrole mor on mu.id_ms_user = mor.id_ms_user ")
			.append("join am_menuofrole mr on mor.id_ms_role = mr.id_ms_role ")
			.append("join am_msrole amr on mr.id_ms_role = amr.id_ms_role ")
			.append("join ms_tenant mt on amr.id_ms_tenant = mt.id_ms_tenant ")
			.append("join am_msmenu mm on mr.id_ms_menu = mm.id_ms_menu ")
			.append("where mu.login_id = :loginId ")
			.append("and mt.tenant_code = :tenantCode ")
			.append("and mm.menu_code = :menuCode ");
		
		BigInteger menuAvailable = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == menuAvailable) {
			return false;
		}
		
		return menuAvailable.intValue() > 0;
	}


	@Override
	public AmMemberofrole getMemberofrole(AmMsuser user, AmMsrole role) {
		Object[][] params = new Object[][] {
			{AmMsuser.AM_MSUSER_HBM, user},
			{AmMsrole.AM_MSROLE_HBM, role}
		};
		return this.managerDAO.selectOne(
				"from AmMemberofrole mor "
				+ "join fetch mor.amMsrole "
				+ "join fetch mor.amMsuser mu "
				+ "where mor.amMsuser = :amMsuser and mor.amMsrole = :amMsrole ", params);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public AmMemberofrole getMemberofroleNewTran(AmMsuser user, AmMsrole role) {
		Object[][] params = new Object[][] {
			{AmMsuser.AM_MSUSER_HBM, user},
			{AmMsrole.AM_MSROLE_HBM, role}
		};
		return this.managerDAO.selectOne(
				"from AmMemberofrole mor "
				+ "join fetch mor.amMsrole "
				+ "join fetch mor.amMsuser mu "
				+ "where mor.amMsuser = :amMsuser and mor.amMsrole = :amMsrole ", params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public AmMsrole getRoleByCodeAndTenantCodeNewTran(String roleCode, String tenantCode) {
		if (StringUtils.isBlank(roleCode))
			return null;

		return this.managerDAO.selectOne(
				"from AmMsrole role "
				+" join fetch role.msTenant mt "
				+" where role.isActive='1'and mt.isActive ='1' "
						+" and role.roleCode = :roleCode and mt.tenantCode = :tenantCode",
				new Object[][] {{AmMsrole.ROLE_CODE_HBM, StringUtils.upperCase(roleCode)}, {MsTenant.TENANT_CODE_HBM, tenantCode}});
	}
	
	@Override
	public List<Map<String, Object>> getRoleListByUserManagement(String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_SMALL_HBM, StringUtils.upperCase(tenantCode));
		
		StringBuilder query = new StringBuilder();
		query
			.append(" select distinct role_code as \"role_code\" ,role_name as \"role name \" ")
			.append("from am_msrole rl ")
			.append("join ms_tenant tn on tn.id_ms_tenant = rl.id_ms_tenant ")
			.append("where tn.tenant_code = :tenantcode ")
			.append("AND rl.is_usermanagement = '1' ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}
	
	@Override
	public AmMsrole getRoleByCode(String roleCode, String tenantCode) {
		Object[][] params= new Object[][]{
			{"rolecode", StringUtils.upperCase(roleCode)},
			{MsTenant.TENANT_CODE_SMALL_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
			"from AmMsrole mor "
			+" join fetch mor.msTenant mt "
			+" where mt.tenantCode = :tenantcode and mor.roleCode = :rolecode "
			, params);
	}


	@Override
	public AmMsrole getRoleUserManagementByTenant(String roleCode, String tenantCode) {
		Object[][] params= new Object[][]{
			{"rolecode", StringUtils.upperCase(roleCode)},
			{MsTenant.TENANT_CODE_SMALL_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
			"from AmMsrole mor "
			+" join fetch mor.msTenant mt "
			+" where mt.tenantCode = :tenantcode and mor.roleCode = :rolecode and mor.isUserManagement = '1'  "
			, params);
	}


	@Override
	public AmMemberofrole getRoleIsAdmin(String callerId) {
		Object[][] params = new Object[][] {{"loginid", StringUtils.upperCase(callerId)}};	
		return this.managerDAO.selectOne(
				"from AmMemberofrole mor "
				+ "join fetch mor.amMsrole ar "
				+ "join fetch mor.amMsuser mu "
				+ " where mu.loginId = :loginid and ar.roleCode = 'ADMCLIENT' "
				, params);
	}
	

	@Override
	public AmMsrole getDataRoleByIdUser(long idMsUser, String roleCode, String tenantCode) {
		if (0 == idMsUser) {
			return null;
		}
		Map<String, Object> params = new HashMap<>();
		params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		params.put(AmMsrole.ROLE_CODE_HBM, StringUtils.upperCase(roleCode));
		params.put(AmMsuser.ID_MS_USER_HBM, idMsUser);
		
		return managerDAO.selectOne(
				" from AmMsrole mr "
				+ " join fetch mr.amMemberofroles mor "
				+ " join fetch mor.amMsuser mu "
				+ " join fetch mr.msTenant mt "
				+ " WHERE mu.idMsUser = :idMsUser AND mr.roleCode = :roleCode AND mt.tenantCode = :tenantCode ", params);
	
	}


	@Override
	public void updateMemberofRole(AmMemberofrole memberofRole) {
		memberofRole.setUsrUpd(MssTool.maskData(memberofRole.getUsrUpd()));
		this.managerDAO.update(memberofRole);
	}


	@Override
	public AmMemberofrole getMemberofrole(MsTenant tenant, AmMsuser user, String roleCode) {
		Map<String, Object> params = new HashMap<>();
		params.put("msTenant", tenant);
		params.put("amMsuser", user);
		params.put(AmMsrole.ROLE_CODE_HBM, StringUtils.upperCase(roleCode));
		params.put("isActive", "1");
		
		return managerDAO.selectOne(
				"from AmMemberofrole mor "
				+ "join fetch mor.amMsrole mr "
				+ "where mor.amMsuser = :amMsuser "
				+ "and mr.msTenant = :msTenant "
				+ "and mr.isActive = :isActive "
				+ "and mr.roleCode = :roleCode ", params);
	}


	@Override
	public List<ListRoleManagamentBean> getListRoleManagement(String roleName, String tenantCode, String status, int min, int max) {

		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		
		StringBuilder query = new StringBuilder();
		query.append("select a.\"roleName\", a.\"tenantName\", a.managable, a.status, a.\"roleCode\", a.\"tenantCode\" from ( ")
			.append("select role_name as \"roleName\", mt.tenant_name as \"tenantName\", role_code as \"roleCode\", tenant_code as \"tenantCode\", ")
			.append("coalesce(is_usermanagement,'0') as \"managable\", am.is_active as \"status\", row_number() over (order by am.role_name, mt.tenant_name asc) as row ")
			.append("from am_msrole am ")
				.append("join ms_tenant mt on mt.id_ms_tenant = am.id_ms_tenant  ")
				.append("where mt.is_active = '1' and 1=1 ");
		
		if (StringUtils.isNotBlank(roleName)) {
			params.put(AmMsrole.ROLE_NAME_HBM, "%" + StringUtils.upperCase(roleName) + "%");
			query.append(" and upper(role_name) like :roleName " );
		}
		
		if (StringUtils.isNotBlank(status)) {
			params.put("status", status);
			query.append(" and am.is_active like :status " );
		}
		
		if (StringUtils.isNotBlank(tenantCode)) {
			params.put(MsTenant.TENANT_CODE_HBM, tenantCode);
			query.append(" and mt.tenant_code like :tenantCode " );
		}
		
		query.append(") as a where row between :min and :max ");
		
		return managerDAO.selectForListString(ListRoleManagamentBean.class, query.toString(), params, null);
	}


	@Override
	public BigInteger countListRoleManagement(String roleName, String tenantCode, String status) {
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		query.append("select count(*)  ")
			 .append("from am_msrole am ")
				.append("join ms_tenant mt on mt.id_ms_tenant = am.id_ms_tenant  ")
				.append("where mt.is_active = '1' and 1=1 ");
		
		if (StringUtils.isNotBlank(roleName)) {
			params.put(AmMsrole.ROLE_NAME_HBM, "%" + StringUtils.upperCase(roleName) + "%");
			query.append(" and upper(role_name) like :roleName " );
		}
		
		if (StringUtils.isNotBlank(status)) {
			params.put("status", status);
			query.append(" and am.is_active like :status " );
		}
		
		if (StringUtils.isNotBlank(tenantCode)) {
			params.put(MsTenant.TENANT_CODE_HBM, tenantCode);
			query.append(" and mt.tenant_code like :tenantCode " );
		}
		
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}


	@Override
	public AmMsrole getRoleByName(String roleName, String tenantCode) {
		Object[][] params= new Object[][]{
			{AmMsrole.ROLE_NAME_HBM, roleName},
			{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
			"from AmMsrole mor "
			+" join fetch mor.msTenant mt "
			+" where mt.tenantCode = :tenantCode and mor.roleName = :roleName "
			, params);
	}


	@Override
	public AmMsrole getRoleByCode(String roleCode) {
		Object[][] params= new Object[][]{
			{"roleCode", StringUtils.upperCase(roleCode)}
		};
		
		return this.managerDAO.selectOne(
			"from AmMsrole mor "
			+" join fetch mor.msTenant mt "
			+" where  mor.roleCode = :roleCode "
			, params);
	}


	@Override
	public void updateRole(AmMsrole role) {
		managerDAO.update(role);
	}
}
