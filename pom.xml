<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.adins.esign</groupId>
	<artifactId>com.adins.esign.parent</artifactId>
	<version>4.16.0</version>
	<packaging>pom</packaging>
	
	<modules>
		<module>model</module>
		<module>cms</module>
		<module>coverage</module>
	</modules>

	<properties>
		<commons-lang3.version>3.9</commons-lang3.version>
		<gson.version>2.8.2</gson.version>
		<hamcrest-all.version>1.3</hamcrest-all.version>
		<java.version>1.8</java.version>
		
		<!-- Sonar -->
		<sonar.projectKey>ESIGN_BACKEND</sonar.projectKey>
	    <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
	    <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
	    <sonar.coverage.jacoco.xmlReportPaths>${project.basedir}/../coverage/target/site/jacoco-aggregate/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>
	    <sonar.language>java</sonar.language>
	</properties>
	
	<dependencies>
        <dependency>
		    <groupId>org.hamcrest</groupId>
		    <artifactId>hamcrest-all</artifactId>
		    <version>${hamcrest-all.version}</version>
		    <scope>test</scope>
		</dependency>
		<dependency>
		    <groupId>org.apache.commons</groupId>
		    <artifactId>commons-lang3</artifactId>
		    <version>${commons-lang3.version}</version>
		</dependency>
	</dependencies>
	<scm>
		<connection>scm:svn:https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent</connection>
		<developerConnection>scm:svn:https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent</developerConnection>
		<url>https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent</url>
	</scm>
</project>