package com.adins.esign.dataaccess.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.dataaccess.api.RegionDao;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.util.MssTool;

@Transactional
@Component
public class RegionDaoHbn extends BaseDaoHbn implements RegionDao {
	
	
	@Override
	public MsRegion getRegionById(long idRegion) {
		if (0 == idRegion) {
			return null;
		}
	
		return this.managerDAO.selectOne(
				"from MsRegion r "
				+ "where r.idMsRegion = :idRegion ", 
						new Object[][] {{"idRegion", idRegion}});
	}
	
	@Override
	public List<MsRegion> getRegionListByTenant(String tenantCode) {
		MsTenant tenant = this.managerDAO.selectOne(MsTenant.class,
				new Object[][] {{ Restrictions.eq(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)) }});
		
		Object[][] params = new Object[][] {{ Restrictions.eq(MsRegion.TENANT_HBM, tenant) }};
		Map<String, Object> mapResult = this.managerDAO.selectAll(MsRegion.class, params, null);
		return (List<MsRegion>) mapResult.get(AmGlobalKey.MAP_RESULT_LIST);
	}
	
	@Override
	public MsRegion getRegionByCodeAndTenant(String regionCode, String tenantCode) {
		if (StringUtils.isBlank(regionCode))
			return null;
	
		Object[][] queryParams = {
				{MsRegion.REGION_CODE_HBM, StringUtils.upperCase(regionCode)},
				{"tenantCode", StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
				"from MsRegion mr "
				+ "join fetch mr.msTenant mt "
				+ "where mr.regionCode = :regionCode and mt.tenantCode = :tenantCode ",
				queryParams);
	}

	@Override
	public void insertRegion(MsRegion region) {
		region.setUsrCrt(MssTool.maskData(region.getUsrCrt()));
		this.managerDAO.insert(region);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MsRegion getRegionByCodeAndTenantNewTrx(String regionCode, String tenantCode) {
		if (StringUtils.isBlank(regionCode))
			return null;
	
		Object[][] queryParams = {
				{MsRegion.REGION_CODE_HBM, StringUtils.upperCase(regionCode)},
				{"tenantCode", StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
				"from MsRegion mr "
				+ "join fetch mr.msTenant mt "
				+ "where mr.regionCode = :regionCode and mt.tenantCode = :tenantCode ",
				queryParams);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertRegionNewTrx(MsRegion region) {
		region.setUsrCrt(MssTool.maskData(region.getUsrCrt()));
		this.managerDAO.insert(region);
	}

}
