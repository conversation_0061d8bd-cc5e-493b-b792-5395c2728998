package com.adins.esign.businesslogic.api;

import javax.annotation.security.RolesAllowed;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.esign.webservices.model.GetDataUserManagementViewRequest;
import com.adins.esign.webservices.model.GetDataUserManagementViewResponse;
import com.adins.esign.webservices.model.GetListDataPenggunaRequest;
import com.adins.esign.webservices.model.GetListDataPenggunaResponse;
import com.adins.esign.webservices.model.GetListUserManagementRequest;
import com.adins.esign.webservices.model.GetListUserManagementResponse;
import com.adins.esign.webservices.model.InsertUserManagementResponse;
import com.adins.esign.webservices.model.InsestUserManagementRequest;
import com.adins.esign.webservices.model.UpdateUserManagementRequest;
import com.adins.esign.webservices.model.UpdateUserManagementResponse;
import com.adins.framework.persistence.dao.model.AuditContext;

public interface UserManagementLogic {
	@RolesAllowed({"ROLE_USER_MANAGEMENT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	GetListUserManagementResponse getListUserManagement (GetListUserManagementRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_USER_MANAGEMENT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	InsertUserManagementResponse insertUserManagement(InsestUserManagementRequest request,  AuditContext audit);
	
	@RolesAllowed({"ROLE_UPDATE_USER"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	GetListDataPenggunaResponse getListDataPengguna (GetListDataPenggunaRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_USER_MANAGEMENT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	GetDataUserManagementViewResponse getDataUserManagementView(GetDataUserManagementViewRequest request, AuditContext audit);
	
	@RolesAllowed({"ROLE_USER_MANAGEMENT"})
	@PreAuthorize("@esignSecurityServices.isValidUser(#audit.callerId, authentication)")
	UpdateUserManagementResponse updateUserManagement(UpdateUserManagementRequest request, AuditContext audit);
}